# Plan d'Implémentation Backend - Système POS Restaurant/Bar

## 🎯 **Objectif**
Développer un système POS complet pour les restaurants et bars permettant la gestion des commandes, tables, menus, paiements et intégration automatique avec l'inventaire.

## 📋 **Analyse de l'Existant**

### ✅ **Tables Disponibles dans le Schéma**
- `ServicesComplexe` - Services Restaurant/Bar configurés
- `PointsDeVente` - Caisses POS par service
- `SessionsCaisse` - Sessions de caisse ouvertes/fermées
- `Tables` - Tables physiques avec statuts et positions
- `Produits` - Produits/plats/boissons avec prix et stock
- `CategoriesProduits` - Catégories de menu (Entrées, Plats, Boissons, etc.)
- `Commandes` - Commandes clients avec statuts
- `DetailsCommandes` - Lignes de commande avec produits
- `TransactionsPOS` - Transactions de paiement
- `LignesTransactionPOS` - Détails des transactions
- `MouvementsStock` - Mouvements automatiques de stock
- `ReservationsTables` - Réservations de tables

### ✅ **Services Backend Existants**
- `POSService` - Gestion des points de vente
- `SessionService` - Gestion des sessions de caisse
- `ServiceComplexeService` - Gestion des services
- `POSStockIntegration` - Intégration POS-Stock automatique
- `RecetteService` - Gestion des recettes et ingrédients

## 🏗️ **Architecture Backend à Implémenter**

### **Phase 1: Services Manquants**

#### **1.1 TableService** (`backend/services/table.service.js`)
```javascript
class TableService extends BaseService {
  // Gestion des tables
  static async getTablesByService(serviceId)
  static async createTable(tableData)
  static async updateTableStatus(tableId, statut)
  static async getTableLayout(serviceId)
  static async updateTablePosition(tableId, position)
  
  // Réservations de tables
  static async reserveTable(reservationData)
  static async cancelTableReservation(reservationId)
  static async getTableReservations(serviceId, date)
}
```

#### **1.2 MenuService** (`backend/services/menu.service.js`)
```javascript
class MenuService extends BaseService {
  // Gestion des menus par service
  static async getMenuByService(serviceId)
  static async getProductsByCategory(serviceId, categorieId)
  static async updateProductPrice(produitId, serviceId, prix)
  static async checkProductAvailability(produitId, quantite)
  static async getMenuWithStock(serviceId)
}
```

#### **1.3 CommandeService** (`backend/services/commande.service.js`)
```javascript
class CommandeService extends BaseService {
  // Gestion des commandes
  static async createCommande(commandeData)
  static async addItemToCommande(commandeId, itemData)
  static async updateCommandeStatus(commandeId, statut)
  static async getCommandesByTable(tableId)
  static async getCommandesByService(serviceId, statut)
  
  // Intégration avec le stock
  static async processCommandeWithStock(commandeData)
  static async cancelCommandeWithStockRestore(commandeId)
}
```

### **Phase 2: Contrôleurs à Créer**

#### **2.1 TableController** (`backend/controllers/table.controller.js`)
```javascript
class TableController {
  // CRUD Tables
  static async getAllTables(request, response)
  static async getTablesByService(request, response)
  static async createTable(request, response)
  static async updateTable(request, response)
  static async deleteTable(request, response)
  
  // Gestion des statuts
  static async updateTableStatus(request, response)
  static async getTableLayout(request, response)
  
  // Réservations
  static async createTableReservation(request, response)
  static async getTableReservations(request, response)
  static async cancelTableReservation(request, response)
}
```

#### **2.2 MenuController** (`backend/controllers/menu.controller.js`)
```javascript
class MenuController {
  // Consultation des menus
  static async getMenuByService(request, response)
  static async getProductsByCategory(request, response)
  static async getMenuWithAvailability(request, response)
  
  // Gestion des prix par service
  static async updateServicePrice(request, response)
  static async bulkUpdatePrices(request, response)
}
```

#### **2.3 CommandeController** (`backend/controllers/commande.controller.js`)
```javascript
class CommandeController {
  // Gestion des commandes
  static async createCommande(request, response)
  static async getCommande(request, response)
  static async updateCommande(request, response)
  static async deleteCommande(request, response)
  
  // Gestion des items
  static async addItemToCommande(request, response)
  static async updateItemQuantity(request, response)
  static async removeItemFromCommande(request, response)
  
  // Statuts et workflow
  static async updateCommandeStatus(request, response)
  static async getCommandesByService(request, response)
  static async getCommandesByTable(request, response)
  
  // Paiement et finalisation
  static async processPayment(request, response)
  static async printReceipt(request, response)
}
```

### **Phase 3: Routes à Implémenter**

#### **3.1 Routes Tables** (`backend/routes/table.routes.js`)
```javascript
// Gestion des tables
router.get('/service/:serviceId', TableController.getTablesByService);
router.post('/', TableController.createTable);
router.put('/:id', TableController.updateTable);
router.put('/:id/status', TableController.updateTableStatus);
router.get('/service/:serviceId/layout', TableController.getTableLayout);

// Réservations de tables
router.post('/reservations', TableController.createTableReservation);
router.get('/service/:serviceId/reservations', TableController.getTableReservations);
router.delete('/reservations/:id', TableController.cancelTableReservation);
```

#### **3.2 Routes Menu** (`backend/routes/menu.routes.js`)
```javascript
// Consultation des menus
router.get('/service/:serviceId', MenuController.getMenuByService);
router.get('/service/:serviceId/category/:categoryId', MenuController.getProductsByCategory);
router.get('/service/:serviceId/availability', MenuController.getMenuWithAvailability);

// Gestion des prix
router.put('/service/:serviceId/product/:productId/price', MenuController.updateServicePrice);
router.put('/service/:serviceId/prices/bulk', MenuController.bulkUpdatePrices);
```

#### **3.3 Routes Commandes** (`backend/routes/commande.routes.js`)
```javascript
// CRUD Commandes
router.post('/', CommandeController.createCommande);
router.get('/:id', CommandeController.getCommande);
router.put('/:id', CommandeController.updateCommande);
router.delete('/:id', CommandeController.deleteCommande);

// Gestion des items
router.post('/:id/items', CommandeController.addItemToCommande);
router.put('/:id/items/:itemId', CommandeController.updateItemQuantity);
router.delete('/:id/items/:itemId', CommandeController.removeItemFromCommande);

// Workflow
router.put('/:id/status', CommandeController.updateCommandeStatus);
router.get('/service/:serviceId', CommandeController.getCommandesByService);
router.get('/table/:tableId', CommandeController.getCommandesByTable);

// Paiement
router.post('/:id/payment', CommandeController.processPayment);
router.post('/:id/receipt', CommandeController.printReceipt);
```

### **Phase 4: Intégration avec l'Inventaire**

#### **4.1 Extension POSStockIntegration**
```javascript
// Nouvelles méthodes à ajouter
static async processCommandeWithStockDeduction(commandeData)
static async checkMenuItemsAvailability(serviceId, items)
static async generateStockAlertsForService(serviceId)
static async getIngredientUsageByCommande(commandeId)
```

#### **4.2 Middleware de Validation Stock**
```javascript
// backend/middleware/stockValidation.middleware.js
const validateStockBeforeCommande = async (req, res, next) => {
  // Vérifier la disponibilité des ingrédients
  // Bloquer la commande si stock insuffisant
}
```

### **Phase 5: Système de Notifications**

#### **5.1 Notifications Cuisine**
```javascript
// Notifications temps réel pour la cuisine
static async notifyKitchen(commandeId, items)
static async updatePreparationStatus(commandeId, itemId, statut)
```

#### **5.2 Notifications Stock**
```javascript
// Alertes stock automatiques
static async checkStockLevelsAfterCommande(serviceId)
static async generateRestockSuggestions(serviceId)
```

## 🔄 **Workflow Complet**

### **Processus de Commande**
1. **Sélection Table** → Vérifier statut et réservations
2. **Consultation Menu** → Afficher produits avec disponibilité stock
3. **Création Commande** → Validation stock + réservation ingrédients
4. **Ajout Items** → Vérification continue du stock
5. **Validation Commande** → Déduction stock automatique
6. **Notification Cuisine** → Envoi en préparation
7. **Suivi Préparation** → Mise à jour statuts items
8. **Service** → Marquage items servis
9. **Paiement** → Intégration session de caisse
10. **Finalisation** → Libération table + ticket de caisse

### **Intégration Stock Automatique**
- **Réservation temporaire** lors de l'ajout d'items
- **Déduction définitive** lors de la validation
- **Restauration stock** en cas d'annulation
- **Alertes automatiques** si stock faible
- **Suggestions de réapprovisionnement**

## 📊 **Métriques et Rapports**

### **Rapports à Implémenter**
- Ventes par service/période
- Produits les plus vendus
- Utilisation des tables
- Temps de préparation moyen
- Rotation des stocks
- Rentabilité par produit

## 🔐 **Sécurité et Permissions**

### **Permissions Spécifiques**
- `operate_restaurant_pos` - Utiliser le POS restaurant
- `operate_bar_pos` - Utiliser le POS bar
- `manage_tables` - Gérer les tables
- `manage_menu_prices` - Modifier les prix
- `view_kitchen_orders` - Voir les commandes cuisine
- `manage_table_reservations` - Gérer réservations tables

## 🚀 **Ordre d'Implémentation**

1. **Services de base** (TableService, MenuService, CommandeService)
2. **Contrôleurs et routes** (CRUD complet)
3. **Intégration stock** (Extension POSStockIntegration)
4. **Système de notifications** (Cuisine + Stock)
5. **Rapports et métriques** (Analytics)
6. **Tests et optimisations** (Performance)

## 🔧 **Détails Techniques d'Implémentation**

### **Intégration avec l'Existant**

#### **Extension des Services Existants**
```javascript
// Extension POSService pour Restaurant/Bar
static async getRestaurantPOS(complexeId) {
  return this.getPOSByServiceType(complexeId, 'Restaurant');
}

static async getBarPOS(complexeId) {
  return this.getPOSByServiceType(complexeId, 'Bar');
}

// Extension ServiceComplexeService
static async getRestaurantServices(complexeId) {
  return this.getServicesByType('Restaurant');
}

static async getBarServices(complexeId) {
  return this.getServicesByType('Bar');
}
```

#### **Nouvelles Tables de Configuration**
```sql
-- Configuration spécifique POS Restaurant/Bar
ALTER TABLE "ServicesComplexe"
ADD COLUMN "pos_configuration" json DEFAULT '{}';

-- Exemple de configuration:
{
  "table_management": true,
  "kitchen_display": true,
  "auto_print_kitchen": true,
  "split_bills": true,
  "table_service": true,
  "takeaway": false,
  "delivery": false
}
```

### **Middleware Spécialisés**

#### **Validation Commande Restaurant**
```javascript
// backend/middleware/restaurantValidation.middleware.js
const validateRestaurantCommande = async (req, res, next) => {
  const { table_id, items } = req.body;

  // Vérifier que la table existe et est disponible
  if (table_id) {
    const table = await TableService.getTableById(table_id);
    if (!table || table.statut !== 'Libre') {
      return res.status(400).json({
        success: false,
        message: 'Table non disponible'
      });
    }
  }

  // Vérifier la disponibilité des ingrédients
  for (const item of items) {
    const available = await POSStockIntegration.checkIngredientAvailability(
      item.produit_id,
      item.quantite
    );

    if (!available.success) {
      return res.status(400).json({
        success: false,
        message: `Ingrédients insuffisants pour ${item.nom}`,
        details: available.missing_ingredients
      });
    }
  }

  next();
};
```

### **Système de Notifications WebSocket**

#### **Configuration WebSocket**
```javascript
// backend/websocket/posNotifications.js
const io = require('socket.io')(server);

io.on('connection', (socket) => {
  socket.on('join_service', (serviceId) => {
    socket.join(`service_${serviceId}`);
  });

  socket.on('join_kitchen', (serviceId) => {
    socket.join(`kitchen_${serviceId}`);
  });
});

// Notifications automatiques
class POSNotificationService {
  static notifyKitchen(serviceId, commande) {
    io.to(`kitchen_${serviceId}`).emit('new_order', {
      commande_id: commande.commande_id,
      table: commande.table_numero,
      items: commande.items,
      priority: commande.priority || 'normal'
    });
  }

  static notifyOrderReady(serviceId, commandeId, itemId) {
    io.to(`service_${serviceId}`).emit('order_ready', {
      commande_id: commandeId,
      item_id: itemId,
      timestamp: new Date()
    });
  }

  static notifyStockAlert(serviceId, ingredient) {
    io.to(`service_${serviceId}`).emit('stock_alert', {
      ingredient_id: ingredient.ingredient_id,
      nom: ingredient.nom,
      stock_actuel: ingredient.stock_actuel,
      stock_minimal: ingredient.stock_minimal
    });
  }
}
```

### **Rapports Avancés**

#### **Analytics Restaurant/Bar**
```javascript
// backend/services/posAnalytics.service.js
class POSAnalyticsService extends BaseService {
  static async getServicePerformance(serviceId, dateDebut, dateFin) {
    const query = `
      SELECT
        DATE(c.date_commande) as date,
        COUNT(c.commande_id) as nb_commandes,
        SUM(c.montant_total) as chiffre_affaires,
        AVG(c.montant_total) as panier_moyen,
        COUNT(DISTINCT c.table_id) as tables_utilisees,
        AVG(EXTRACT(EPOCH FROM (
          SELECT MIN(date_fin) FROM "ReservationsTables" rt
          WHERE rt.table_id = c.table_id
          AND rt.date_debut <= c.date_commande
        ) - c.date_commande) / 60) as duree_moyenne_table
      FROM "Commandes" c
      WHERE c.service_id = $1
      AND c.date_commande BETWEEN $2 AND $3
      AND c.statut = 'Payée'
      GROUP BY DATE(c.date_commande)
      ORDER BY date
    `;

    const result = await db.query(query, [serviceId, dateDebut, dateFin]);
    return this.successResponse(result.rows);
  }

  static async getTopProducts(serviceId, limit = 10) {
    const query = `
      SELECT
        p.nom,
        p.produit_id,
        SUM(dc.quantite) as quantite_vendue,
        SUM(dc.montant_ligne) as chiffre_affaires,
        COUNT(DISTINCT dc.commande_id) as nb_commandes
      FROM "DetailsCommandes" dc
      JOIN "Commandes" c ON dc.commande_id = c.commande_id
      JOIN "Produits" p ON dc.produit_id = p.produit_id
      WHERE c.service_id = $1
      AND c.statut = 'Payée'
      GROUP BY p.produit_id, p.nom
      ORDER BY quantite_vendue DESC
      LIMIT $2
    `;

    const result = await db.query(query, [serviceId, limit]);
    return this.successResponse(result.rows);
  }
}
```

### **Optimisations Performance**

#### **Cache Redis pour Menus**
```javascript
// backend/cache/menuCache.js
const redis = require('redis');
const client = redis.createClient();

class MenuCacheService {
  static async getMenuFromCache(serviceId) {
    const cacheKey = `menu:service:${serviceId}`;
    const cached = await client.get(cacheKey);

    if (cached) {
      return JSON.parse(cached);
    }

    return null;
  }

  static async setMenuCache(serviceId, menuData) {
    const cacheKey = `menu:service:${serviceId}`;
    await client.setex(cacheKey, 3600, JSON.stringify(menuData)); // 1h cache
  }

  static async invalidateMenuCache(serviceId) {
    const cacheKey = `menu:service:${serviceId}`;
    await client.del(cacheKey);
  }
}
```

Cette architecture backend fournira une base solide pour le système POS Restaurant/Bar avec intégration complète de l'inventaire, gestion temps réel des commandes, notifications WebSocket et analytics avancés.
