#!/usr/bin/env python3
"""
pg_to_openapi.py - Convert PostgreSQL schema to OpenAPI specification

This script parses a PostgreSQL SQL file and generates an OpenAPI 3.0 specification document.
It identifies tables, columns, relationships, and creates appropriate REST endpoints.

Usage:
    python pg_to_openapi.py input.sql output.yaml
"""

import re
import sys
import yaml
import json
from datetime import datetime
from collections import defaultdict

# SQL data type to OpenAPI type mapping
TYPE_MAPPING = {
    'integer': {'type': 'integer', 'format': 'int32'},
    'bigint': {'type': 'integer', 'format': 'int64'},
    'smallint': {'type': 'integer', 'format': 'int32'},
    'decimal': {'type': 'number', 'format': 'double'},
    'numeric': {'type': 'number', 'format': 'double'},
    'real': {'type': 'number', 'format': 'float'},
    'double precision': {'type': 'number', 'format': 'double'},
    'float': {'type': 'number', 'format': 'float'},
    'money': {'type': 'number', 'format': 'double'},
    'character varying': {'type': 'string'},
    'varchar': {'type': 'string'},
    'character': {'type': 'string'},
    'char': {'type': 'string'},
    'text': {'type': 'string'},
    'citext': {'type': 'string'},
    'uuid': {'type': 'string', 'format': 'uuid'},
    'bytea': {'type': 'string', 'format': 'binary'},
    'boolean': {'type': 'boolean'},
    'date': {'type': 'string', 'format': 'date'},
    'time': {'type': 'string', 'format': 'time'},
    'timestamp': {'type': 'string', 'format': 'date-time'},
    'timestamptz': {'type': 'string', 'format': 'date-time'},
    'timestamp with time zone': {'type': 'string', 'format': 'date-time'},
    'timestamp without time zone': {'type': 'string', 'format': 'date-time'},
    'time with time zone': {'type': 'string', 'format': 'time'},
    'interval': {'type': 'string'},
    'bit': {'type': 'string', 'format': 'binary'},
    'bit varying': {'type': 'string', 'format': 'binary'},
    'jsonb': {'type': 'object'},
    'json': {'type': 'object'},
    'array': {'type': 'array'},
    'hstore': {'type': 'object'},
    'inet': {'type': 'string', 'format': 'ipv4'},
    'cidr': {'type': 'string'},
    'macaddr': {'type': 'string'},
    'point': {'type': 'string'},
    'line': {'type': 'string'},
    'lseg': {'type': 'string'},
    'box': {'type': 'string'},
    'path': {'type': 'string'},
    'polygon': {'type': 'string'},
    'circle': {'type': 'string'},
    'tsvector': {'type': 'string'},
    'tsquery': {'type': 'string'},
    'enum': {'type': 'string'}
}

def extract_enum_values(sql_content):
    """Extract enum values from COMMENT ON COLUMN statements"""
    enum_values = {}
    comment_pattern = re.compile(r"COMMENT ON COLUMN ([^\s]+)\.([^\s]+) IS '([^']+)'")
    
    for match in comment_pattern.finditer(sql_content):
        table_name = match.group(1).strip('"')
        column_name = match.group(2).strip('"')
        comment = match.group(3)
        
        # Look for enum values in the comment
        if ';' in comment:
            values = [v.strip() for v in comment.split(';')]
            enum_values[f"{table_name}.{column_name}"] = values
    
    return enum_values

def extract_table_info(sql_content):
    """Extract table definitions from SQL content"""
    tables = {}
    primary_keys = {}
    foreign_keys = []
    enum_values = extract_enum_values(sql_content)
    
    # Find table definitions
    table_pattern = re.compile(r'CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?([^\s(]+)\s*\((.*?)\);', re.DOTALL | re.IGNORECASE)
    table_matches = table_pattern.finditer(sql_content)
    
    for match in table_matches:
        table_name = match.group(1).strip('"')
        if '.' in table_name:
            table_name = table_name.split('.')[-1].strip('"')
        
        columns = {}
        column_definitions = match.group(2).split(',')
        i = 0
        while i < len(column_definitions):
            # Handle multi-line definitions and commas within parentheses
            if '(' in column_definitions[i] and ')' not in column_definitions[i]:
                j = i + 1
                while j < len(column_definitions) and ')' not in column_definitions[j]:
                    column_definitions[i] += ',' + column_definitions[j]
                    j += 1
                if j < len(column_definitions):
                    column_definitions[i] += ',' + column_definitions[j]
                i = j
            
            line = column_definitions[i].strip()
            
            # Skip constraints and keys temporarily
            if re.match(r'CONSTRAINT|PRIMARY\s+KEY|FOREIGN\s+KEY|UNIQUE|CHECK', line, re.IGNORECASE):
                # Extract primary key
                pk_match = re.search(r'PRIMARY\s+KEY\s*\(([^)]+)\)', line, re.IGNORECASE)
                if pk_match:
                    keys = [k.strip(' "') for k in pk_match.group(1).split(',')]
                    primary_keys[table_name] = keys
                
                # Extract foreign keys
                fk_match = re.search(r'FOREIGN\s+KEY\s*\(([^)]+)\)\s*REFERENCES\s+([^\s(]+)\s*(?:\(([^)]+)\))?', line, re.IGNORECASE)
                if fk_match:
                    local_cols = [c.strip(' "') for c in fk_match.group(1).split(',')]
                    ref_table = fk_match.group(2).strip('"')
                    if '.' in ref_table:
                        ref_table = ref_table.split('.')[-1].strip('"')
                    
                    ref_cols = []
                    if fk_match.group(3):
                        ref_cols = [c.strip(' "') for c in fk_match.group(3).split(',')]
                    
                    foreign_keys.append({
                        'table': table_name,
                        'columns': local_cols,
                        'references': {'table': ref_table, 'columns': ref_cols}
                    })
                i += 1
                continue
            
            # Extract column definition
            col_match = re.search(r'^\s*"?([^"\s]+)"?\s+([^,(]+)(?:\(([^)]+)\))?(.*)', line, re.IGNORECASE)
            if col_match:
                col_name = col_match.group(1)
                col_type = col_match.group(2).lower()
                col_size = col_match.group(3)
                col_constraints = col_match.group(4) if col_match.group(4) else ''
                
                # Handle array types
                if '[]' in col_type:
                    col_type = 'array'
                
                # Check for enum values
                enum_key = f"{table_name}.{col_name}"
                enum_list = enum_values.get(enum_key, [])
                
                columns[col_name] = {
                    'type': col_type,
                    'size': col_size,
                    'nullable': 'NOT NULL' not in col_constraints.upper(),
                    'default': re.search(r'DEFAULT\s+(.+?)(?:,|$)', col_constraints, re.IGNORECASE),
                    'unique': 'UNIQUE' in col_constraints.upper(),
                    'enum_values': enum_list if enum_list else None
                }
                
                # Handle primary key in column definition
                if 'PRIMARY KEY' in col_constraints.upper():
                    primary_keys[table_name] = [col_name]
            
            i += 1
        
        tables[table_name] = columns
    
    # Extract ALTER TABLE statements for foreign keys
    alter_table_pattern = re.compile(r'ALTER\s+TABLE\s+(?:IF\s+EXISTS\s+)?([^\s]+)\s+ADD\s+(?:CONSTRAINT\s+[^\s]+\s+)?FOREIGN\s+KEY\s*\(([^)]+)\)\s*REFERENCES\s+([^\s(]+)(?:\s*\(([^)]+)\))?', re.DOTALL | re.IGNORECASE)
    alter_matches = alter_table_pattern.finditer(sql_content)
    
    for match in alter_matches:
        table_name = match.group(1).strip('"')
        if '.' in table_name:
            table_name = table_name.split('.')[-1].strip('"')
        
        local_cols = [c.strip(' "') for c in match.group(2).split(',')]
        ref_table = match.group(3).strip('"')
        if '.' in ref_table:
            ref_table = ref_table.split('.')[-1].strip('"')
        
        ref_cols = []
        if match.group(4):
            ref_cols = [c.strip(' "') for c in match.group(4).split(',')]
        
        foreign_keys.append({
            'table': table_name,
            'columns': local_cols,
            'references': {'table': ref_table, 'columns': ref_cols}
        })
    
    return tables, primary_keys, foreign_keys

def generate_openapi_spec(tables, primary_keys, foreign_keys, api_info=None):
    """Generate OpenAPI specification from parsed table information"""
    if not api_info:
        api_info = {
            'title': 'PostgreSQL Generated API',
            'description': 'API automatically generated from PostgreSQL schema',
            'version': '1.0.0'
        }
    
    spec = {
        'openapi': '3.0.0',
        'info': api_info,
        'paths': {},
        'components': {
            'schemas': {},
            'responses': {
                'Error': {
                    'description': 'Error response',
                    'content': {
                        'application/json': {
                            'schema': {
                                'type': 'object',
                                'properties': {
                                    'error': {'type': 'string'},
                                    'details': {'type': 'string'}
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    # Generate components schemas from tables
    for table_name, columns in tables.items():
        resource_name = table_name
        schema = {
            'type': 'object',
            'properties': {}
        }
        
        required_fields = []
        for col_name, col_info in columns.items():
            # Convert PostgreSQL type to OpenAPI type
            type_spec = TYPE_MAPPING.get(col_info['type'], {'type': 'string'})
            
            if col_info['type'] == 'array':
                prop = {'type': 'array', 'items': {'type': 'string'}}
            else:
                prop = type_spec.copy()
            
            # Add enum values if present
            if col_info.get('enum_values'):
                prop['enum'] = col_info['enum_values']
            
            if not col_info['nullable'] and 'PRIMARY KEY' not in col_name.upper():
                required_fields.append(col_name)
                
            # Add description if there's a default value
            if col_info['default']:
                default_val = col_info['default'].group(1).strip()
                prop['description'] = f"Default: {default_val}"
            
            # Add format for specific types
            if col_info['type'] in ['timestamp', 'timestamptz']:
                prop['format'] = 'date-time'
            elif col_info['type'] == 'date':
                prop['format'] = 'date'
            
            schema['properties'][col_name] = prop
        
        # Only add required if there are required fields
        if required_fields:
            schema['required'] = required_fields
        
        # Record schema in components
        spec['components']['schemas'][resource_name] = schema
        
        # Create CRUD paths for each resource
        collection_path = f'/{table_name.lower()}'
        item_path = f'/{table_name.lower()}/{{id}}'
        
        # GET collection
        spec['paths'][collection_path] = {
            'get': {
                'summary': f'List all {table_name}',
                'operationId': f'list{table_name}',
                'parameters': [
                    {
                        'name': 'limit',
                        'in': 'query',
                        'description': 'Maximum number of items to return',
                        'schema': {'type': 'integer', 'default': 20}
                    },
                    {
                        'name': 'offset',
                        'in': 'query',
                        'description': 'Number of items to skip',
                        'schema': {'type': 'integer', 'default': 0}
                    }
                ],
                'responses': {
                    '200': {
                        'description': f'Array of {table_name} items',
                        'content': {
                            'application/json': {
                                'schema': {
                                    'type': 'array',
                                    'items': {'$ref': f'#/components/schemas/{resource_name}'}
                                }
                            }
                        }
                    },
                    '400': {'$ref': '#/components/responses/Error'}
                }
            },
            'post': {
                'summary': f'Create a new {table_name}',
                'operationId': f'create{table_name}',
                'requestBody': {
                    'required': True,
                    'content': {
                        'application/json': {
                            'schema': {'$ref': f'#/components/schemas/{resource_name}'}
                        }
                    }
                },
                'responses': {
                    '201': {
                        'description': f'Created {table_name}',
                        'content': {
                            'application/json': {
                                'schema': {'$ref': f'#/components/schemas/{resource_name}'}
                            }
                        }
                    },
                    '400': {'$ref': '#/components/responses/Error'},
                    '422': {'$ref': '#/components/responses/Error'}
                }
            }
        }
        
        # Parameters for individual item endpoints
        id_parameter = {
            'name': 'id',
            'in': 'path',
            'required': True,
            'schema': {'type': 'string'}
        }
        
        # If we know the primary key and its type, be more specific
        if table_name in primary_keys and len(primary_keys[table_name]) == 1:
            pk_name = primary_keys[table_name][0]
            if pk_name in columns:
                pk_type = columns[pk_name]['type']
                if pk_type in TYPE_MAPPING:
                    id_parameter['schema'] = TYPE_MAPPING[pk_type]
        
        # GET single item
        spec['paths'][item_path] = {
            'get': {
                'summary': f'Get a specific {table_name} by ID',
                'operationId': f'get{table_name}',
                'parameters': [id_parameter],
                'responses': {
                    '200': {
                        'description': f'{table_name} item',
                        'content': {
                            'application/json': {
                                'schema': {'$ref': f'#/components/schemas/{resource_name}'}
                            }
                        }
                    },
                    '404': {'$ref': '#/components/responses/Error'}
                }
            },
            'put': {
                'summary': f'Replace a {table_name}',
                'operationId': f'replace{table_name}',
                'parameters': [id_parameter],
                'requestBody': {
                    'required': True,
                    'content': {
                        'application/json': {
                            'schema': {'$ref': f'#/components/schemas/{resource_name}'}
                        }
                    }
                },
                'responses': {
                    '200': {
                        'description': f'Updated {table_name}',
                        'content': {
                            'application/json': {
                                'schema': {'$ref': f'#/components/schemas/{resource_name}'}
                            }
                        }
                    },
                    '400': {'$ref': '#/components/responses/Error'},
                    '404': {'$ref': '#/components/responses/Error'},
                    '422': {'$ref': '#/components/responses/Error'}
                }
            },
            'patch': {
                'summary': f'Update a {table_name} partially',
                'operationId': f'update{table_name}',
                'parameters': [id_parameter],
                'requestBody': {
                    'required': True,
                    'content': {
                        'application/json': {
                            'schema': {'$ref': f'#/components/schemas/{resource_name}'}
                        }
                    }
                },
                'responses': {
                    '200': {
                        'description': f'Updated {table_name}',
                        'content': {
                            'application/json': {
                                'schema': {'$ref': f'#/components/schemas/{resource_name}'}
                            }
                        }
                    },
                    '400': {'$ref': '#/components/responses/Error'},
                    '404': {'$ref': '#/components/responses/Error'},
                    '422': {'$ref': '#/components/responses/Error'}
                }
            },
            'delete': {
                'summary': f'Delete a {table_name}',
                'operationId': f'delete{table_name}',
                'parameters': [id_parameter],
                'responses': {
                    '204': {
                        'description': 'Deletion successful'
                    },
                    '404': {'$ref': '#/components/responses/Error'}
                }
            }
        }
        
        # Add related resource endpoints based on foreign keys
        for fk in foreign_keys:
            if fk['references']['table'] == table_name:
                # This table is referenced by another table
                related_table = fk['table']
                relation_path = f'/{table_name.lower()}/{{id}}/{related_table.lower()}'
                
                spec['paths'][relation_path] = {
                    'get': {
                        'summary': f'Get {related_table} items related to {table_name}',
                        'operationId': f'get{table_name}{related_table}',
                        'parameters': [
                            id_parameter,
                            {
                                'name': 'limit',
                                'in': 'query',
                                'description': 'Maximum number of items to return',
                                'schema': {'type': 'integer', 'default': 20}
                            },
                            {
                                'name': 'offset',
                                'in': 'query',
                                'description': 'Number of items to skip',
                                'schema': {'type': 'integer', 'default': 0}
                            }
                        ],
                        'responses': {
                            '200': {
                                'description': f'Array of related {related_table} items',
                                'content': {
                                    'application/json': {
                                        'schema': {
                                            'type': 'array',
                                            'items': {'$ref': f'#/components/schemas/{related_table}'}
                                        }
                                    }
                                }
                            },
                            '404': {'$ref': '#/components/responses/Error'}
                        }
                    }
                }
    
    return spec

def main():
    if len(sys.argv) != 3:
        print(f"Usage: {sys.argv[0]} input.sql output.yaml")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # Extract tables, primary keys, and foreign keys
        tables, primary_keys, foreign_keys = extract_table_info(sql_content)
        
        # Generate OpenAPI specification
        api_info = {
            'title': f'API Generated from {input_file}',
            'description': f'REST API automatically generated from PostgreSQL schema in {input_file}',
            'version': '1.0.0',
            'contact': {
                'name': 'API Generator'
            },
            'license': {
                'name': 'MIT'
            }
        }
        
        spec = generate_openapi_spec(tables, primary_keys, foreign_keys, api_info)
        
        # Write YAML output
        with open(output_file, 'w', encoding='utf-8') as f:
            yaml.dump(spec, f, sort_keys=False, allow_unicode=True)
        
        print(f"Successfully generated OpenAPI specification in {output_file}")
        
        # Summary of what was found
        print(f"\nFound {len(tables)} tables:")
        for table in sorted(tables.keys()):
            col_count = len(tables[table])
            pk = primary_keys.get(table, [])
            print(f"  - {table}: {col_count} columns" + (f", Primary key: {', '.join(pk)}" if pk else ""))
        
        print(f"\nFound {len(foreign_keys)} foreign key relationships")
        
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()