# Prompt pour générer la page de gestion des employés et des rôles

## Contexte
Je développe un système de gestion hôtelière avec un frontend React/TypeScript. J'ai besoin d'une page complète pour gérer les employés et leurs rôles avec permissions.

## Structure existante

### Services TypeScript disponibles :
```typescript
// employee.service.ts
export interface Employee {
  employe_id: number;
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  role_id: number;
  role_nom: string;
  complexe_id: number;
  actif: boolean;
  date_embauche: string;
  created_at: string;
  updated_at: string;
}

export interface CreateEmployeeData {
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  role_id: number;
  password: string;
}

// Méthodes disponibles :
employeeService.getEmployees(filters?: {role_id?: number; search?: string})
employeeService.getEmployee(id: number)
employeeService.createEmployee(data: CreateEmployeeData)
employeeService.updateEmployee(id: number, data: UpdateEmployeeData)
employeeService.updatePassword(id: number, currentPassword: string, newPassword: string)
employeeService.deleteEmployee(id: number)
employeeService.getRoles()
```

```typescript
// role.service.ts
export interface Role {
  role_id: number;
  nom: string;
  description: string;
  permissions: string[];
  complexe_id: number;
  created_at: string;
  updated_at: string;
}

// Méthodes disponibles :
roleService.getRoles()
roleService.getRole(id: number)
roleService.createRole(data: CreateRoleData)
roleService.updateRole(id: number, data: UpdateRoleData)
roleService.deleteRole(id: number)
roleService.assignRoleToEmployee(employeeId: number, roleId: number)
roleService.getEmployeePermissions(employeeId: number)
roleService.getAvailablePermissions() // Retourne les permissions disponibles
```

### Permissions disponibles :
```typescript
const permissions = [
  'manage_employees', 'manage_roles', 'manage_products', 'manage_reservations',
  'view_reservations', 'create_reservation', 'update_reservation',
  'manage_clients', 'view_clients', 'create_client', 'update_client',
  'manage_payments', 'view_reports', 'view_statistics'
];
```

## Spécifications de la page à créer

### 1. Page principale : `EmployeeManagement.tsx`

**Layout :**
- Header avec titre "Gestion des Employés" et boutons d'action
- Deux onglets principaux : "Employés" et "Rôles"
- Barre de recherche et filtres
- Tableau avec actions (modifier, supprimer, voir détails)

**Fonctionnalités Employés :**
- ✅ Liste paginée des employés avec colonnes : Nom, Prénom, Email, Téléphone, Rôle, Statut, Actions
- ✅ Recherche par nom/prénom/email
- ✅ Filtre par rôle
- ✅ Bouton "Ajouter Employé" → Modal de création
- ✅ Actions par ligne : Modifier, Changer mot de passe, Activer/Désactiver, Supprimer
- ✅ Modal de création/modification d'employé
- ✅ Modal de changement de mot de passe
- ✅ Confirmations pour les suppressions

**Fonctionnalités Rôles :**
- ✅ Liste des rôles avec colonnes : Nom, Description, Nombre de permissions, Employés assignés, Actions
- ✅ Bouton "Créer Rôle" → Modal de création
- ✅ Actions par ligne : Modifier, Supprimer
- ✅ Modal de création/modification de rôle avec sélection des permissions
- ✅ Interface de gestion des permissions (checkboxes groupées)

### 2. Composants à créer :

#### `EmployeeModal.tsx`
- Formulaire de création/modification d'employé
- Champs : nom, prénom, email, téléphone, rôle (select), mot de passe (création uniquement)
- Validation des champs
- Gestion des erreurs

#### `RoleModal.tsx`
- Formulaire de création/modification de rôle
- Champs : nom, description
- Interface de sélection des permissions avec checkboxes
- Groupement logique des permissions
- Validation des champs

#### `PasswordChangeModal.tsx`
- Formulaire de changement de mot de passe
- Champs : mot de passe actuel, nouveau mot de passe, confirmation
- Validation des mots de passe

### 3. Styles et UX

**Design System :**
- Utiliser Tailwind CSS
- Couleurs : bleu pour les actions principales, rouge pour les suppressions, vert pour les validations
- Icons : Lucide React (User, UserPlus, Edit, Trash2, Eye, Lock, Shield, etc.)
- Responsive design

**Interactions :**
- Loading states pendant les requêtes
- Messages de succès/erreur avec toasts
- Confirmations pour les actions destructives
- Pagination pour les listes longues
- Tri des colonnes

### 4. Gestion d'état
- useState pour l'état local des composants
- Rechargement automatique des listes après modifications
- Gestion des erreurs avec try/catch et affichage user-friendly

### 5. Structure des fichiers
```
src/components/EmployeeManagement/
├── EmployeeManagement.tsx (composant principal)
├── EmployeeModal.tsx
├── RoleModal.tsx
├── PasswordChangeModal.tsx
└── index.ts (exports)
```

## Contraintes techniques
- React 18+ avec TypeScript
- Tailwind CSS pour les styles
- Lucide React pour les icônes
- Pas de librairies externes supplémentaires
- Code propre et commenté
- Gestion d'erreurs robuste
- Interface responsive

## Exemple de structure attendue

```tsx
const EmployeeManagement = () => {
  const [activeTab, setActiveTab] = useState<'employees' | 'roles'>('employees');
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  // ... autres états

  const loadEmployees = async () => {
    // Logique de chargement
  };

  const loadRoles = async () => {
    // Logique de chargement
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Gestion des Employés</h1>
        {/* Boutons d'action selon l'onglet actif */}
      </div>

      {/* Onglets */}
      <div className="border-b border-gray-200 mb-6">
        {/* Navigation tabs */}
      </div>

      {/* Contenu selon l'onglet actif */}
      {activeTab === 'employees' ? (
        <EmployeesTab />
      ) : (
        <RolesTab />
      )}

      {/* Modals */}
      {/* ... */}
    </div>
  );
};
```

## Fonctionnalités spécifiques demandées

### Gestion des employés :
1. **Tableau avec tri et pagination**
2. **Recherche en temps réel**
3. **Filtrage par rôle**
4. **Actions en ligne** : Modifier, Changer mot de passe, Activer/Désactiver, Supprimer
5. **Modal de création** avec validation
6. **Modal de modification** pré-rempli
7. **Modal de changement de mot de passe** sécurisé

### Gestion des rôles :
1. **Liste des rôles** avec nombre de permissions et employés assignés
2. **Modal de création/modification** avec sélection de permissions
3. **Groupement logique des permissions** (par catégorie)
4. **Validation** avant suppression (vérifier si des employés utilisent le rôle)

### UX/UI Requirements :
- **Loading spinners** pendant les requêtes
- **Messages de confirmation** pour les actions destructives
- **Toasts de succès/erreur** pour le feedback utilisateur
- **Responsive design** pour mobile et desktop
- **États vides** avec messages informatifs
- **Validation en temps réel** des formulaires

### Gestion d'erreurs :
- Try/catch sur toutes les requêtes API
- Messages d'erreur user-friendly
- Fallback UI en cas d'erreur
- Retry automatique pour certaines erreurs

Génère le code complet de cette page avec tous les composants, en respectant les bonnes pratiques React/TypeScript et en incluant une gestion d'erreurs robuste.
