require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const routes = require('./routes');
const logger = require('./logger');
const { errorHandler } = require('./middleware/error');
const db = require('./db');

// Log de démarrage
logger.info('Starting application...');
console.log('Starting application...');

try {
  const app = express();

  // Middleware de sécurité
  try {
    app.use(helmet());
    logger.info('Helmet middleware initialized');
    console.log('Helmet middleware initialized');
  } catch (error) {
    logger.error('Error initializing Helmet:', error);
    throw error;
  }

  try {
    app.use(cors({
      origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization']
    }));
    logger.info('CORS middleware initialized');
  } catch (error) {
    logger.error('Error initializing CORS:', error);
    throw error;
  }

  try {
    app.use(compression());
    logger.info('Compression middleware initialized');
    console.log('Compression middleware initialized');
  } catch (error) {
    logger.error('Error initializing Compression:', error);
    throw error;
  }

  // Middleware de parsing
  try {
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));
    logger.info('Body parsing middleware initialized');
    console.log('Body parsing middleware initialized');
  } catch (error) {
    logger.error('Error initializing body parsers:', error);
    throw error;
  }

  // Middleware de logging
  app.use((req, res, next) => {
    logger.info('Incoming request', {
      method: req.method,
      path: req.path,
      body: req.body,
      params: req.params,
      query: req.query,
      ip: req.ip,
      environment: process.env.NODE_ENV
    });
    next();
  });

  // Routes
  try {
    app.use('/api', routes);
    logger.info('Routes initialized');
    console.log('Routes initialized');
  } catch (error) {
    logger.error('Error initializing routes:', error);
    throw error;
  }

  // Error handling
  app.use(errorHandler);

  // Gestion des erreurs non capturées
  process.on('uncaughtException', (err) => {
    logger.error('Uncaught Exception:', {
      error: err.message,
      stack: err.stack
    });
    process.exit(1);
  });

  process.on('unhandledRejection', (err) => {
    logger.error('Unhandled Rejection:', {
      error: err.message,
      stack: err.stack
    });
    process.exit(1);
  });

  const PORT = process.env.PORT || 8080;

  // Test de la connexion à la base de données avant de démarrer le serveur
  db.pool.query('SELECT NOW()', (err, res) => {
    if (err) {
      logger.error('Erreur de connexion à la base de données:', {
        error: err.message,
        stack: err.stack,
        code: err.code
      });
      process.exit(1);
    }
    
    logger.info('Connexion à la base de données réussie');
    console.log('Connexion à la base de données réussie');
    try {
      app.listen(PORT, () => {
        logger.info(`Server is running on port ${PORT}`, {
          environment: process.env.NODE_ENV,
          service: 'hotel-saas-api',
          timestamp: new Date().toISOString()
        });
      });
      console.log('Server is running on port 8080');
    } catch (error) {
      logger.error('Error starting server:', {
        error: error.message,
        stack: error.stack
      });
      process.exit(1);
    }
  });
} catch (error) {
  logger.error('Error during application startup:', {
    error: error.message,
    stack: error.stack
  });
  process.exit(1);
}
