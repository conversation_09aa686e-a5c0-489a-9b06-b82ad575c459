# API de Gestion des Tarifications

Cette documentation décrit les endpoints pour gérer les tarifications des services du complexe hôtelier.

## Endpoints

### 1. Récupérer la tarification d'un service

**GET** `/api/services/:id/tarification`

Récupère la tarification actuelle d'un service spécifique.

#### Paramètres
- `id` (path) : ID du service

#### Réponse
```json
{
  "success": true,
  "data": {
    "service_id": 1,
    "nom": "Restaurant Principal",
    "type_service": "Restaurant",
    "tarification": {
      "menus": {
        "Menu du jour": 25.00,
        "Menu enfant": 15.00
      },
      "boissons": {
        "Eau plate": 3.00,
        "Soda": 4.00
      },
      "service_table": 2.00
    }
  }
}
```

### 2. Mettre à jour la tarification d'un service

**PUT** `/api/services/:id/tarification`

Met à jour la tarification d'un service. Nécessite des permissions appropriées.

#### Paramètres
- `id` (path) : ID du service

#### Corps de la requête
```json
{
  "menus": {
    "Menu du jour": 28.00,
    "Menu enfant": 16.00,
    "Menu dégustation": 45.00
  },
  "boissons": {
    "Eau plate": 3.50,
    "Eau gazeuse": 4.00,
    "Soda": 4.50
  },
  "service_table": 2.50,
  "couvert_par_personne": 1.50
}
```

#### Réponse
```json
{
  "success": true,
  "data": {
    "service_id": 1,
    "nom": "Restaurant Principal",
    "type_service": "Restaurant",
    "tarification": { /* tarification mise à jour */ },
    "updated_at": "2024-01-15T10:30:00Z"
  },
  "message": "Tarification mise à jour avec succès"
}
```

### 3. Obtenir un modèle de tarification

**GET** `/api/services/tarification/template/:type`

Récupère un modèle de tarification par défaut selon le type de service.

#### Paramètres
- `type` (path) : Type de service (`Restaurant`, `Bar`, `Piscine`)

#### Réponse
```json
{
  "success": true,
  "data": {
    "type_service": "Restaurant",
    "template": {
      "menus": {
        "Menu du jour": 25.00,
        "Menu enfant": 15.00,
        "Menu dégustation": 45.00
      },
      "boissons": {
        "Eau plate": 3.00,
        "Eau gazeuse": 3.50,
        "Soda": 4.00,
        "Jus de fruits": 5.00
      },
      "service_table": 2.00,
      "couvert_par_personne": 1.50
    }
  }
}
```

## Structures de Tarification par Type de Service

### Restaurant
```json
{
  "menus": {
    "nom_menu": prix_numerique
  },
  "boissons": {
    "nom_boisson": prix_numerique
  },
  "service_table": prix_numerique,
  "couvert_par_personne": prix_numerique
}
```

### Bar
```json
{
  "alcools": {
    "nom_alcool": prix_numerique
  },
  "soft_drinks": {
    "nom_boisson": prix_numerique
  },
  "cocktails": {
    "nom_cocktail": prix_numerique
  },
  "happy_hour": {
    "reduction_pourcentage": pourcentage_numerique,
    "heures": ["17:00-19:00"]
  }
}
```

### Piscine
```json
{
  "prix_par_personne": prix_numerique,
  "prix_par_heure": prix_numerique,
  "prix_forfaitaire": prix_numerique,
  "tarifs_age": {
    "tranche_age": prix_numerique
  },
  "tarifs_duree": {
    "duree": prix_numerique
  }
}
```

## Validation des Données

### Règles de Validation
1. Tous les prix doivent être des nombres positifs ou zéro
2. Les structures doivent respecter le format attendu selon le type de service
3. Les champs avec des prix négatifs sont automatiquement filtrés
4. Les objets vides ou invalides sont rejetés

### Codes d'Erreur
- `400` : Données de tarification invalides
- `401` : Authentification requise
- `403` : Permissions insuffisantes
- `404` : Service non trouvé
- `500` : Erreur serveur

## Permissions Requises

### Consultation
- Aucune permission spéciale requise (utilisateur authentifié)

### Modification
- Permission `MANAGE_SERVICES` ou
- Rôle `admin` ou
- Permission `ADMIN`

## Exemples d'Utilisation

### Créer une tarification pour un restaurant
```javascript
const tarification = {
  menus: {
    "Petit déjeuner": 12.00,
    "Déjeuner": 25.00,
    "Dîner": 35.00
  },
  boissons: {
    "Café": 2.50,
    "Thé": 2.00,
    "Jus d'orange": 4.00
  },
  service_table: 2.00
};

fetch('/api/services/1/tarification', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify(tarification)
});
```

### Récupérer un modèle pour une piscine
```javascript
fetch('/api/services/tarification/template/Piscine')
  .then(response => response.json())
  .then(data => {
    console.log('Modèle piscine:', data.data.template);
  });
```

## Logs et Traçabilité

Toutes les actions sur les tarifications sont loggées avec :
- ID de l'utilisateur
- ID du service
- Type d'action (consultation, mise à jour, etc.)
- Timestamp
- Durée de l'opération
- Statut de la réponse

Les logs sont accessibles pour audit et débogage.
