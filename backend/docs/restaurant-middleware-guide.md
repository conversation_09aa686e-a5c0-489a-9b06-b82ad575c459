# Guide des Middlewares Restaurant/Bar

## 📋 **Vue d'ensemble**

Ce guide explique l'utilisation des middlewares spécialisés pour les opérations restaurant et bar, implémentés dans l'Étape 2 du système POS.

## 🔧 **Middlewares Disponibles**

### **1. Validation Middleware (`restaurantValidation.middleware.js`)**

#### **`validateRestaurantCommande`**
- **Usage** : Validation complète d'une nouvelle commande
- **Vérifications** :
  - Disponibilité et statut de la table
  - Stock suffisant pour tous les items
  - Cohérence service/table
- **Données ajoutées à `req`** :
  - `req.tableInfo` : Informations de la table validée
  - `req.stockValidation` : Résultats de validation stock
  - `req.stockAlerts` : Alertes de stock détectées

#### **`validateAddItemToCommande`**
- **Usage** : Validation ajout d'item à commande existante
- **Vérifications** :
  - Disponibilité stock pour le produit
  - Quantité valide
- **Données ajoutées à `req`** :
  - `req.itemValidation` : Résultats de validation item

#### **`validatePOSSession`**
- **Usage** : Vérification session de caisse active
- **Vérifications** :
  - Session ouverte pour l'employé
  - POS actif sur le service
- **Données ajoutées à `req`** :
  - `req.posSession` : Informations session active

### **2. Logging Middleware (`restaurantLogging.middleware.js`)**

#### **`logCommandeOperation(operation)`**
- **Usage** : Logging détaillé des opérations de commande
- **Opérations supportées** :
  - `CREATE_COMMANDE`
  - `ADD_ITEM`
  - `UPDATE_ITEM`
  - `UPDATE_STATUS`

#### **`logStockAlert`**
- **Usage** : Logging automatique des alertes de stock
- **Déclenché** : Quand `req.stockAlerts` contient des alertes

#### **`auditLog(operation, details)`**
- **Usage** : Audit trail pour opérations critiques
- **Données loggées** : User, IP, timestamp, détails complets

### **3. Error Handler Middleware (`restaurantErrorHandler.middleware.js`)**

#### **`handleCommandeErrors`**
- **Usage** : Gestion d'erreurs spécifiques aux commandes
- **Codes d'erreur** :
  - `INSUFFICIENT_STOCK`
  - `TABLE_ERROR`
  - `SESSION_REQUIRED`

#### **`globalRestaurantErrorHandler`**
- **Usage** : Gestionnaire d'erreurs global
- **Fonctionnalités** :
  - Logging complet des erreurs
  - Réponses standardisées
  - ID d'erreur pour support

## 🚀 **Exemples d'utilisation**

### **Route de Création de Commande**
```javascript
router.post('/', 
  checkPermission('operate_restaurant_pos'),
  logCommandeOperation('CREATE_COMMANDE'),
  validateRestaurantCommande,
  logValidationResult,
  logStockAlert,
  auditLog('CREATE_COMMANDE'),
  CommandeController.createCommande
);
```

### **Route d'Ajout d'Item**
```javascript
router.post('/:id/items', 
  checkPermission('operate_restaurant_pos'),
  logCommandeOperation('ADD_ITEM'),
  validateAddItemToCommande,
  logValidationResult,
  CommandeController.addItemToCommande
);
```

### **Route de Changement Statut Table**
```javascript
router.put('/:id/status', 
  checkPermission('operate_restaurant_pos'),
  logTableOperation('UPDATE_TABLE_STATUS'),
  validateTableStatusChange,
  logValidationResult,
  TableController.updateTableStatus
);
```

## 📊 **Données de Validation Disponibles**

### **Dans les Contrôleurs**
Les contrôleurs peuvent accéder aux données de validation via `req` :

```javascript
// Dans CommandeController.createCommande
static async createCommande(request, response) {
  const tableInfo = request.tableInfo; // Infos table validée
  const stockValidation = request.stockValidation; // Résultats stock
  const stockAlerts = request.stockAlerts; // Alertes détectées
  const posSession = request.posSession; // Session active
  
  // Utiliser ces données pour optimiser le traitement
}
```

## 🔍 **Codes d'Erreur Standardisés**

### **Validation**
- `TABLE_NOT_FOUND` : Table inexistante
- `TABLE_OCCUPIED` : Table déjà occupée
- `TABLE_OUT_OF_SERVICE` : Table hors service
- `INSUFFICIENT_STOCK` : Stock insuffisant
- `NO_ACTIVE_SESSION` : Pas de session de caisse

### **Opérations**
- `COMMANDE_ERROR` : Erreur générale commande
- `STOCK_ERROR` : Erreur opération stock
- `TABLE_ERROR` : Erreur opération table
- `VALIDATION_ERROR` : Erreur de validation

## 📈 **Logging et Monitoring**

### **Niveaux de Log**
- **INFO** : Opérations normales, validations réussies
- **WARN** : Alertes de stock, échecs de validation
- **ERROR** : Erreurs système, exceptions

### **Données Loggées**
- Timestamp précis
- ID employé et complexe
- Détails de l'opération
- Durée d'exécution
- Résultats et codes d'erreur

## 🔐 **Sécurité et Audit**

### **Audit Trail**
Toutes les opérations critiques sont auditées :
- Création/modification de commandes
- Changements de statut de tables
- Opérations de stock

### **Données Sensibles**
- IP address et User-Agent loggés
- Pas de données de paiement dans les logs
- Anonymisation possible via configuration

## 🛠️ **Configuration et Personnalisation**

### **Variables d'Environnement**
```env
# Niveau de logging restaurant
RESTAURANT_LOG_LEVEL=info

# Activation audit détaillé
RESTAURANT_AUDIT_ENABLED=true

# Durée de rétention des logs
RESTAURANT_LOG_RETENTION_DAYS=30
```

### **Personnalisation des Validations**
Les middlewares peuvent être étendus pour des validations spécifiques :

```javascript
// Validation personnalisée pour un type de service
const validateSpecialService = async (req, res, next) => {
  // Logique spécifique
  next();
};
```

## 🚨 **Gestion des Erreurs**

### **Stratégie de Récupération**
1. **Validation échoue** → Réponse 400 avec détails
2. **Stock insuffisant** → Réponse 400 avec alternatives
3. **Session manquante** → Réponse 403 avec action requise
4. **Erreur système** → Réponse 500 avec ID d'erreur

### **Actions Correctives**
Chaque erreur inclut une `action_requise` pour guider l'utilisateur :
- "Ouvrir une session de caisse"
- "Choisir une autre table"
- "Vérifier la disponibilité des produits"

Ce système de middlewares fournit une validation robuste, un logging détaillé et une gestion d'erreurs cohérente pour toutes les opérations restaurant/bar.
