# Guide POS Analytics - Restaurant/Bar

## 📊 **Vue d'ensemble**

Le système POS Analytics fournit des analyses détaillées des performances des services Restaurant et Bar, avec des métriques en temps réel, des tendances historiques et des prévisions basées sur les données.

## 🎯 **Fonctionnalités Principales**

### **Dashboard Complet**
- Vue d'ensemble des performances d'un service
- Métriques clés : CA, commandes, panier moyen
- Comparaisons entre services
- Alertes et recommandations

### **Analyses Détaillées**
- **Performance** : Évolution du CA, taux de conversion
- **Produits** : Top ventes, analyse de rentabilité
- **Tables** : Taux d'occupation, rotation
- **Employés** : Performance individuelle
- **Revenus** : Tendances horaires, heures de pointe

### **Prévisions**
- Projections basées sur l'historique
- Tendances saisonnières
- Recommandations d'optimisation

## 🔗 **Endpoints API**

### **Dashboard et Vue d'ensemble**

#### `GET /api/pos-analytics/dashboard/:serviceId`
Dashboard complet d'un service
```javascript
// Paramètres query
{
  date_debut: "2024-01-01",  // Optionnel, défaut: -30 jours
  date_fin: "2024-01-31"     // Optionnel, défaut: aujourd'hui
}

// Réponse
{
  success: true,
  data: {
    periode: { debut: "2024-01-01", fin: "2024-01-31" },
    service_id: 1,
    performance: { /* métriques performance */ },
    top_products: { /* top produits */ },
    table_utilization: { /* utilisation tables */ },
    revenue_analysis: { /* analyse revenus */ }
  }
}
```

#### `GET /api/pos-analytics/comparison/:complexeId`
Comparaison entre services d'un complexe
```javascript
// Réponse
{
  success: true,
  data: {
    comparaison_services: [
      {
        service_id: 1,
        service_nom: "Restaurant Principal",
        type_service: "Restaurant",
        chiffre_affaires: 15000,
        nb_commandes: 450
      }
    ]
  }
}
```

### **Analyses Détaillées**

#### `GET /api/pos-analytics/performance/:serviceId`
Analyse des performances
```javascript
// Réponse
{
  success: true,
  data: {
    periode_details: [
      {
        date: "2024-01-01",
        nb_commandes: 25,
        chiffre_affaires: 850.50,
        panier_moyen: 34.02
      }
    ],
    resume: {
      total_commandes: 750,
      total_ca: 25500.00,
      panier_moyen_global: 34.00,
      taux_conversion: 95.2
    }
  }
}
```

#### `GET /api/pos-analytics/products/:serviceId`
Top des produits
```javascript
// Paramètres query
{
  limit: 10,  // Nombre de produits à retourner
  date_debut: "2024-01-01",
  date_fin: "2024-01-31"
}

// Réponse
{
  success: true,
  data: {
    top_products: [
      {
        produit_id: 1,
        produit_nom: "Pizza Margherita",
        quantite_vendue: 120,
        chiffre_affaires: 1800.00,
        pourcentage_ventes: 15.5
      }
    ]
  }
}
```

#### `GET /api/pos-analytics/tables/:serviceId`
Utilisation des tables
```javascript
// Réponse
{
  success: true,
  data: {
    table_details: [
      {
        table_id: 1,
        table_numero: "T01",
        capacite: 4,
        nb_commandes: 45,
        chiffre_affaires: 1500.00,
        pourcentage_utilisation: 12.5
      }
    ],
    statistiques_globales: {
      total_tables: 20,
      tables_utilisees: 18,
      taux_utilisation_tables: 90.0
    }
  }
}
```

#### `GET /api/pos-analytics/revenue/:serviceId`
Analyse des revenus
```javascript
// Réponse
{
  success: true,
  data: {
    revenus_par_heure: {
      "12": { revenus: 850.00, commandes: 25 },
      "19": { revenus: 1200.00, commandes: 35 }
    },
    heures_de_pointe: [
      { heure: 19, revenus: 1200.00, commandes: 35 },
      { heure: 20, revenus: 1150.00, commandes: 32 }
    ]
  }
}
```

### **Analyses Opérationnelles**

#### `GET /api/pos-analytics/employees/:serviceId`
Performance des employés
```javascript
// Réponse
{
  success: true,
  data: {
    performance_employes: [
      {
        employe_id: 1,
        employe_nom: "Dupont",
        employe_prenom: "Jean",
        nb_commandes: 150,
        chiffre_affaires: 5100.00,
        ca_par_jour: 170.00
      }
    ]
  }
}
```

#### `GET /api/pos-analytics/preparation-time/:serviceId`
Temps de préparation
```javascript
// Réponse
{
  success: true,
  data: {
    temps_preparation: [
      {
        produit_nom: "Pizza Margherita",
        temps_preparation_theorique: 15,
        temps_reel_moyen: 18.5,
        nb_commandes: 120
      }
    ]
  }
}
```

### **Prévisions**

#### `GET /api/pos-analytics/forecast/:serviceId`
Prévisions basées sur les tendances
```javascript
// Paramètres query
{
  jours: 7  // Nombre de jours à prévoir
}

// Réponse
{
  success: true,
  data: {
    previsions: [
      {
        date: "2024-02-01",
        ca_prevu: 850.00,
        commandes_prevues: 25,
        confiance: "Élevée"
      }
    ]
  }
}
```

#### `GET /api/pos-analytics/trends/:serviceId`
Tendances hebdomadaires
```javascript
// Réponse
{
  success: true,
  data: {
    tendances_hebdomadaires: [
      {
        jour_semaine: 1,
        nom_jour: "Lundi",
        nb_commandes: 85,
        chiffre_affaires: 2890.00
      }
    ],
    analyse: {
      jour_plus_rentable: "Samedi",
      ca_jour_max: 4500.00
    }
  }
}
```

## 🔧 **Configuration et Optimisation**

### **Cache et Performance**
- **Cache automatique** : 5-30 minutes selon l'endpoint
- **Compression** : Réponses > 10KB automatiquement compressées
- **Rate limiting** : Max 5 requêtes simultanées par utilisateur

### **Permissions Requises**
- `view_reports` : Consultation des analytics de base
- `view_financial_reports` : Accès aux données financières
- `export_reports` : Export des données
- `manage_reports` : Configuration des analytics

### **Paramètres de Date**
- **Format** : YYYY-MM-DD
- **Période max** : 365 jours
- **Défauts** : 
  - `date_debut` : -30 jours
  - `date_fin` : aujourd'hui

## 📈 **Métriques Disponibles**

### **Performance Globale**
- Chiffre d'affaires total et par période
- Nombre de commandes
- Panier moyen
- Taux de conversion (commandes payées/total)
- Taux d'annulation

### **Analyse Produits**
- Quantités vendues
- Revenus par produit
- Pourcentage des ventes totales
- Temps de préparation réel vs théorique

### **Utilisation Tables**
- Taux d'occupation par table
- Rotation des tables
- Revenus par table
- Durée moyenne d'occupation

### **Performance Employés**
- Commandes traitées
- Chiffre d'affaires généré
- Performance par jour travaillé

## 🚨 **Monitoring et Alertes**

### **Alertes Automatiques**
- Requêtes lentes (> 5 secondes)
- Trop de requêtes simultanées
- Erreurs de validation des données

### **Métriques de Performance**
- Temps de réponse par endpoint
- Taux de cache hit/miss
- Utilisation des ressources

## 🔍 **Exemples d'Utilisation**

### **Dashboard Manager Restaurant**
```javascript
// Récupérer le dashboard du mois en cours
GET /api/pos-analytics/dashboard/1?date_debut=2024-01-01&date_fin=2024-01-31

// Comparer les performances avec le mois précédent
GET /api/pos-analytics/performance/1?date_debut=2023-12-01&date_fin=2023-12-31
```

### **Analyse Produits Populaires**
```javascript
// Top 5 des produits cette semaine
GET /api/pos-analytics/products/1?limit=5&date_debut=2024-01-22&date_fin=2024-01-28
```

### **Optimisation Staffing**
```javascript
// Performance des employés ce mois
GET /api/pos-analytics/employees/1

// Tendances par jour de la semaine
GET /api/pos-analytics/trends/1
```

Ce système d'analytics fournit une vue complète et actionnable des performances des services restaurant/bar, permettant une prise de décision éclairée basée sur les données.
