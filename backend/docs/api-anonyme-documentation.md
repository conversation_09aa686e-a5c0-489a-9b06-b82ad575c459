# Documentation API - Réservations Anonymes

## Vue d'ensemble

Cette documentation décrit les endpoints de l'API pour les réservations anonymes, permettant aux clients de faire des réservations sans fournir d'informations personnelles complètes.

## Base URL

- **Production**: `https://votre-domaine.com/api`
- **Développement**: `http://localhost:3000/api`

## Routes Publiques

### 1. Créer une demande de réservation anonyme

**Endpoint**: `POST /reservations-anonymes/demande`

**Description**: Permet à un client de créer une demande de réservation anonyme.

**Headers**:
```
Content-Type: application/json
```

**Body**:
```json
{
  "date_arrivee": "2024-03-15",
  "date_depart": "2024-03-17",
  "heure_debut": "14:00",
  "heure_fin": "11:00",
  "chambres": [
    {
      "chambre_id": "123",
      "type_chambre": "Standard",
      "prix_nuit": 89.99
    }
  ],
  "pseudonyme": "VoyageurMystere",
  "commentaires": "Arrivée tardive prévue",
  "prix_total": 179.98,
  "complexe_id": 1
}
```

**Réponse succès (201)**:
```json
{
  "success": true,
  "data": {
    "reservation_id": 456,
    "numero_reservation": "RES-ABC12345",
    "code_acces_anonyme": "ANON-XYZ789012345",
    "statut": "en_attente",
    "montant_total": 179.98,
    "date_arrivee": "2024-03-15",
    "date_depart": "2024-03-17",
    "heure_debut": "14:00",
    "heure_fin": "11:00",
    "chambres": [...]
  }
}
```

**Réponse erreur (400)**:
```json
{
  "success": false,
  "message": "Données invalides",
  "errors": [
    "La date d'arrivée est requise",
    "Le prix total doit être un nombre positif"
  ]
}
```

### 2. Consulter une réservation anonyme

**Endpoint**: `GET /reservations-anonymes/:codeAcces`

**Description**: Permet de consulter les détails d'une réservation anonyme avec le code d'accès.

**Paramètres**:
- `codeAcces` (string): Code d'accès de la réservation (ex: ANON-XYZ789012345)

**Réponse succès (200)**:
```json
{
  "success": true,
  "data": {
    "reservation_id": 456,
    "numero_reservation": "RES-ABC12345",
    "statut": "en_attente",
    "statut_libelle": "En attente de validation",
    "date_arrivee": "2024-03-15",
    "date_depart": "2024-03-17",
    "heure_debut": "14:00",
    "heure_fin": "11:00",
    "montant_total": 179.98,
    "montant_paye": 0,
    "montant_restant": 179.98,
    "duree_sejour": 2,
    "pseudonyme": "VoyageurMystere",
    "commentaires": "Arrivée tardive prévue",
    "complexe_nom": "Hôtel Paradise",
    "chambres": [
      {
        "chambre_id": "123",
        "numero": "101",
        "type_chambre": "Standard",
        "prix_nuit": 89.99
      }
    ],
    "created_at": "2024-03-10T10:30:00Z"
  }
}
```

**Réponse erreur (404)**:
```json
{
  "success": false,
  "message": "Réservation non trouvée"
}
```

### 3. Modifier une réservation anonyme

**Endpoint**: `PATCH /reservations-anonymes/:codeAcces`

**Description**: Permet de modifier certains aspects d'une réservation anonyme (fonctionnalités limitées).

**Paramètres**:
- `codeAcces` (string): Code d'accès de la réservation

**Body**:
```json
{
  "commentaires": "Nouveau commentaire"
}
```

**Réponse succès (200)**:
```json
{
  "success": true,
  "data": {
    "reservation_id": 456,
    "numero_reservation": "RES-ABC12345",
    "commentaires": "Nouveau commentaire",
    "updated_at": "2024-03-10T15:45:00Z"
  }
}
```

### 4. Annuler une réservation anonyme

**Endpoint**: `DELETE /reservations-anonymes/:codeAcces`

**Description**: Permet d'annuler une réservation anonyme si les conditions le permettent.

**Paramètres**:
- `codeAcces` (string): Code d'accès de la réservation

**Réponse succès (200)**:
```json
{
  "success": true,
  "message": "Réservation annulée avec succès",
  "data": {
    "numero_reservation": "RES-ABC12345",
    "statut": "annulee"
  }
}
```

**Réponse erreur (400)**:
```json
{
  "success": false,
  "message": "Annulation impossible. Délai minimum: 24h avant l'arrivée"
}
```

### 5. Valider un code d'accès

**Endpoint**: `POST /reservations-anonymes/validate-code`

**Description**: Utilitaire pour valider un code d'accès côté frontend.

**Body**:
```json
{
  "code_acces": "ANON-XYZ789012345"
}
```

**Réponse succès (200)**:
```json
{
  "success": true,
  "valid": true,
  "message": "Code valide"
}
```

### 6. Statistiques publiques

**Endpoint**: `GET /reservations-anonymes/stats/public`

**Description**: Récupère des statistiques générales non sensibles sur les réservations anonymes.

**Réponse succès (200)**:
```json
{
  "success": true,
  "data": {
    "total_reservations": 1250,
    "taux_confirmation": 78.5,
    "montant_moyen": 156.75,
    "service_actif": true
  }
}
```

### 7. Vérifier la disponibilité du service

**Endpoint**: `GET /reservations-anonymes/availability/:complexeId`

**Description**: Vérifie si les réservations anonymes sont activées pour un complexe.

**Paramètres**:
- `complexeId` (number): ID du complexe

**Réponse succès (200)**:
```json
{
  "success": true,
  "available": true,
  "configuration": {
    "autoriser_modification": true,
    "autoriser_annulation": true,
    "delai_annulation_heures": 24,
    "pseudonyme_obligatoire": false
  }
}
```

## Routes d'Administration

### Authentification requise

Toutes les routes d'administration nécessitent un token d'authentification dans le header:

```
Authorization: Bearer <token>
```

### 1. Récupérer la configuration d'un complexe

**Endpoint**: `GET /admin/anonymous-config/:complexeId`

**Permissions**: `view_anonymous_config`

**Réponse succès (200)**:
```json
{
  "success": true,
  "data": {
    "config_id": 1,
    "complexe_id": 1,
    "actif": true,
    "duree_validite_code_heures": 72,
    "max_tentatives_acces": 5,
    "delai_blocage_minutes": 30,
    "autoriser_modification": true,
    "autoriser_annulation": true,
    "delai_annulation_heures": 24,
    "pseudonyme_obligatoire": false,
    "longueur_code_acces": 12,
    "prefixe_code": "ANON",
    "created_at": "2024-03-01T00:00:00Z",
    "updated_at": "2024-03-10T10:30:00Z"
  }
}
```

### 2. Mettre à jour la configuration

**Endpoint**: `PUT /admin/anonymous-config/:complexeId`

**Permissions**: `manage_anonymous_config`

**Body**:
```json
{
  "actif": true,
  "duree_validite_code_heures": 48,
  "max_tentatives_acces": 3,
  "delai_annulation_heures": 12
}
```

### 3. Activer/désactiver le service

**Endpoint**: `PATCH /admin/anonymous-config/:complexeId/toggle`

**Permissions**: `manage_anonymous_config`

**Body**:
```json
{
  "actif": false
}
```

### 4. Statistiques d'utilisation

**Endpoint**: `GET /admin/anonymous-config/:complexeId/stats`

**Permissions**: `view_anonymous_stats`

**Query Parameters**:
- `date_debut` (optional): Date de début (YYYY-MM-DD)
- `date_fin` (optional): Date de fin (YYYY-MM-DD)

**Réponse succès (200)**:
```json
{
  "success": true,
  "data": {
    "reservations": {
      "total_reservations": 150,
      "en_attente": 12,
      "confirmees": 118,
      "annulees": 15,
      "expirees": 5,
      "chiffre_affaires_total": 23456.78,
      "montant_moyen": 156.38,
      "clients_uniques": 142
    },
    "acces": {
      "total_acces": 450,
      "acces_reussis": 425,
      "acces_echecs": 25,
      "codes_utilises": 150,
      "ips_uniques": 138
    },
    "periode": {
      "date_debut": "2024-03-01",
      "date_fin": "2024-03-31"
    }
  }
}
```

## Codes d'erreur

| Code | Description |
|------|-------------|
| 400 | Données invalides ou paramètres manquants |
| 401 | Non authentifié (routes admin) |
| 403 | Permissions insuffisantes |
| 404 | Ressource non trouvée |
| 410 | Code d'accès expiré |
| 429 | Trop de tentatives (rate limiting) |
| 500 | Erreur serveur interne |

## Sécurité

### Rate Limiting

Les endpoints publics sont protégés par un système de rate limiting:
- Création de réservation: 5 tentatives par heure par IP
- Consultation: 50 tentatives par heure par IP
- Validation de code: 20 tentatives par heure par IP

### Logging

Tous les accès aux réservations anonymes sont loggés avec:
- Adresse IP
- User-Agent
- Timestamp
- Action effectuée
- Code d'accès utilisé (partiellement masqué)
- Résultat (succès/échec)

### Validation

- Tous les paramètres d'entrée sont validés et nettoyés
- Les codes d'accès suivent un format strict
- Les dates et heures sont validées selon les règles métier
- Protection contre les injections et attaques XSS

## Exemples d'utilisation

### Flux complet côté client

```javascript
// 1. Vérifier la disponibilité du service
const availability = await fetch('/api/reservations-anonymes/availability/1');

// 2. Créer une réservation anonyme
const reservation = await fetch('/api/reservations-anonymes/demande', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(reservationData)
});

// 3. Sauvegarder le code d'accès
const { code_acces_anonyme } = reservation.data;
localStorage.setItem('anonymous_code', code_acces_anonyme);

// 4. Consulter la réservation plus tard
const details = await fetch(`/api/reservations-anonymes/${code_acces_anonyme}`);
```

### Gestion d'erreurs

```javascript
try {
  const response = await fetch('/api/reservations-anonymes/demande', options);
  const data = await response.json();
  
  if (!data.success) {
    if (data.errors) {
      // Afficher les erreurs de validation
      data.errors.forEach(error => console.error(error));
    } else {
      console.error(data.message);
    }
  }
} catch (error) {
  console.error('Erreur réseau:', error);
}
```
