# Plan d'Implémentation Backend - Upload Excel et Inventaire

## Vue d'ensemble

Implémentation d'un système d'upload de fichiers Excel pour les tarifs de restaurants/bars, connecté au système d'inventaire existant pour la gestion automatique des coûts et des stocks.

## Phase 1 : Extension du Schéma de Base de Données

### 1.1 Nouvelles Tables

#### Table des Ingrédients
```sql
CREATE TABLE "Ingredients" (
  "ingredient_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "chaine_id" integer NOT NULL,
  "complexe_id" integer,
  "nom" varchar NOT NULL,
  "description" text,
  "unite_mesure" varchar NOT NULL, -- kg, L, unité, pièce
  "categorie" varchar NOT NULL, -- <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>sons, Épices
  "code_barre" varchar UNIQUE,
  "prix_unitaire_moyen" decimal DEFAULT 0,
  "fournisseur_principal_id" integer,
  "allergenes" json, -- ["gluten", "lactose", "noix"]
  "conservation" varchar, -- <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Ambiant
  "duree_conservation_jours" integer,
  "actif" boolean DEFAULT true,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp,
  FOREIGN KEY (chaine_id) REFERENCES "ChainesHotelieres" (chaine_id),
  FOREIGN KEY (complexe_id) REFERENCES "ComplexesHoteliers" (complexe_id),
  FOREIGN KEY (fournisseur_principal_id) REFERENCES "Fournisseurs" (fournisseur_id)
);
```

#### Table des Recettes
```sql
CREATE TABLE "Recettes" (
  "recette_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "produit_id" integer NOT NULL,
  "service_id" integer NOT NULL,
  "nom_recette" varchar NOT NULL,
  "description" text,
  "instructions" text,
  "temps_preparation" integer, -- en minutes
  "nombre_portions" integer DEFAULT 1,
  "cout_total_calcule" decimal DEFAULT 0,
  "marge_beneficiaire_cible" decimal DEFAULT 0,
  "prix_vente_suggere" decimal DEFAULT 0,
  "actif" boolean DEFAULT true,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp,
  FOREIGN KEY (produit_id) REFERENCES "Produits" (produit_id),
  FOREIGN KEY (service_id) REFERENCES "ServicesComplexe" (service_id)
);
```

#### Table de Liaison Recettes-Ingrédients
```sql
CREATE TABLE "RecettesIngredients" (
  "recette_ingredient_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "recette_id" integer NOT NULL,
  "ingredient_id" integer NOT NULL,
  "quantite_necessaire" decimal NOT NULL,
  "unite_mesure" varchar NOT NULL,
  "cout_unitaire" decimal NOT NULL DEFAULT 0,
  "cout_total" decimal GENERATED ALWAYS AS (quantite_necessaire * cout_unitaire) STORED,
  "optionnel" boolean DEFAULT false,
  "notes" text,
  "ordre_ajout" integer, -- Pour les instructions de préparation
  FOREIGN KEY (recette_id) REFERENCES "Recettes" (recette_id),
  FOREIGN KEY (ingredient_id) REFERENCES "Ingredients" (ingredient_id)
);
```

#### Table des Imports Excel
```sql
CREATE TABLE "ImportsExcel" (
  "import_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "complexe_id" integer NOT NULL,
  "service_id" integer,
  "employe_id" integer NOT NULL,
  "type_import" varchar NOT NULL, -- MENU_RESTAURANT, CARTE_BAR, INVENTAIRE_INGREDIENTS
  "nom_fichier" varchar NOT NULL,
  "chemin_fichier" varchar NOT NULL,
  "taille_fichier" integer,
  "statut" varchar NOT NULL DEFAULT 'EN_COURS', -- EN_COURS, VALIDE, ERREUR, IMPORTE, ANNULE
  "nombre_lignes_total" integer DEFAULT 0,
  "nombre_lignes_valides" integer DEFAULT 0,
  "nombre_erreurs" integer DEFAULT 0,
  "donnees_parsees" json, -- Contenu du fichier Excel parsé
  "erreurs_detectees" json, -- Erreurs par ligne avec détails
  "mapping_colonnes" json, -- Correspondance colonnes Excel -> champs DB
  "parametres_import" json, -- Options spécifiques à l'import
  "date_import" timestamp DEFAULT (now()),
  "date_traitement" timestamp,
  "date_finalisation" timestamp,
  "notes" text,
  FOREIGN KEY (complexe_id) REFERENCES "ComplexesHoteliers" (complexe_id),
  FOREIGN KEY (service_id) REFERENCES "ServicesComplexe" (service_id),
  FOREIGN KEY (employe_id) REFERENCES "Employes" (employe_id)
);
```

#### Table des Templates d'Import
```sql
CREATE TABLE "TemplatesImport" (
  "template_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "chaine_id" integer NOT NULL,
  "type_service" varchar NOT NULL, -- Restaurant, Bar
  "type_import" varchar NOT NULL, -- MENU, CARTE, INVENTAIRE
  "nom_template" varchar NOT NULL,
  "description" text,
  "colonnes_requises" json NOT NULL, -- Structure attendue du fichier Excel
  "exemple_donnees" json, -- Données d'exemple pour le template
  "regles_validation" json, -- Règles de validation spécifiques
  "mapping_defaut" json, -- Mapping par défaut des colonnes
  "actif" boolean DEFAULT true,
  "version" varchar DEFAULT '1.0',
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp,
  FOREIGN KEY (chaine_id) REFERENCES "ChainesHotelieres" (chaine_id)
);
```

### 1.2 Extensions des Tables Existantes

#### Extension de la table Produits
```sql
-- Liaison avec les recettes et coûts
ALTER TABLE "Produits" ADD COLUMN "recette_id" integer;
ALTER TABLE "Produits" ADD COLUMN "cout_ingredients" decimal DEFAULT 0;
ALTER TABLE "Produits" ADD COLUMN "marge_calculee" decimal DEFAULT 0;
ALTER TABLE "Produits" ADD COLUMN "import_id" integer; -- Traçabilité de l'import
ALTER TABLE "Produits" ADD FOREIGN KEY ("recette_id") REFERENCES "Recettes" ("recette_id");
ALTER TABLE "Produits" ADD FOREIGN KEY ("import_id") REFERENCES "ImportsExcel" ("import_id");
```

#### Extension de la table MouvementsStock
```sql
-- Traçabilité des mouvements liés aux imports et recettes
ALTER TABLE "MouvementsStock" ADD COLUMN "import_id" integer;
ALTER TABLE "MouvementsStock" ADD COLUMN "recette_id" integer;
ALTER TABLE "MouvementsStock" ADD COLUMN "cout_unitaire_import" decimal;
ALTER TABLE "MouvementsStock" ADD FOREIGN KEY ("import_id") REFERENCES "ImportsExcel" ("import_id");
ALTER TABLE "MouvementsStock" ADD FOREIGN KEY ("recette_id") REFERENCES "Recettes" ("recette_id");
```

#### Extension de la table LignesInventaire
```sql
-- Liaison avec les ingrédients
ALTER TABLE "LignesInventaire" ADD COLUMN "ingredient_id" integer;
ALTER TABLE "LignesInventaire" ADD COLUMN "prix_unitaire_reel" decimal;
ALTER TABLE "LignesInventaire" ADD FOREIGN KEY ("ingredient_id") REFERENCES "Ingredients" ("ingredient_id");
```

### 1.3 Index et Contraintes

```sql
-- Index pour les performances
CREATE INDEX idx_ingredients_complexe ON "Ingredients" (complexe_id);
CREATE INDEX idx_ingredients_categorie ON "Ingredients" (categorie);
CREATE INDEX idx_recettes_service ON "Recettes" (service_id);
CREATE INDEX idx_recettes_produit ON "Recettes" (produit_id);
CREATE INDEX idx_imports_statut ON "ImportsExcel" (statut);
CREATE INDEX idx_imports_date ON "ImportsExcel" (date_import);

-- Contraintes métier
ALTER TABLE "ImportsExcel" ADD CONSTRAINT check_statut_import 
  CHECK (statut IN ('EN_COURS', 'VALIDE', 'ERREUR', 'IMPORTE', 'ANNULE'));

ALTER TABLE "ImportsExcel" ADD CONSTRAINT check_type_import 
  CHECK (type_import IN ('MENU_RESTAURANT', 'CARTE_BAR', 'INVENTAIRE_INGREDIENTS'));

ALTER TABLE "Ingredients" ADD CONSTRAINT check_unite_mesure 
  CHECK (unite_mesure IN ('kg', 'g', 'L', 'mL', 'unité', 'pièce', 'portion'));

ALTER TABLE "Ingredients" ADD CONSTRAINT check_conservation 
  CHECK (conservation IN ('Frais', 'Congelé', 'Sec', 'Ambiant'));
```

## Phase 2 : Services Backend

### 2.1 Service de Gestion des Fichiers (`FileUploadService`)

```javascript
class FileUploadService {
  /**
   * Upload et validation initiale du fichier Excel
   */
  static async uploadExcelFile(file, metadata) {
    // Validation du format (xlsx, xls)
    // Vérification de la taille (max 10MB)
    // Scan de sécurité basique
    // Stockage temporaire sécurisé
    // Création de l'enregistrement ImportsExcel
  }

  /**
   * Validation approfondie du fichier
   */
  static async validateFile(filePath, templateId) {
    // Vérification de l'intégrité du fichier
    // Validation de la structure Excel
    // Vérification des permissions de lecture
  }

  /**
   * Nettoyage des fichiers temporaires
   */
  static async cleanupTempFiles(importId, olderThanDays = 7) {
    // Suppression des fichiers temporaires
    // Nettoyage des données de cache
  }

  /**
   * Génération de fichiers template
   */
  static async generateTemplate(templateId) {
    // Création d'un fichier Excel template
    // Avec exemples et validation
  }
}
```

### 2.2 Service de Parsing Excel (`ExcelParserService`)

```javascript
class ExcelParserService {
  /**
   * Parse le fichier Excel selon le template
   */
  static async parseExcelFile(filePath, templateConfig) {
    // Lecture du fichier Excel (xlsx library)
    // Extraction des données par feuille
    // Application du mapping des colonnes
    // Conversion des types de données
    // Validation de base des données
  }

  /**
   * Validation avancée des données
   */
  static async validateData(data, validationRules) {
    // Validation des types de données
    // Vérification des contraintes métier
    // Détection des doublons
    // Validation des références (ingrédients existants)
    // Génération du rapport d'erreurs détaillé
  }

  /**
   * Mapping automatique des colonnes
   */
  static autoMapColumns(headers, expectedColumns) {
    // Correspondance automatique par nom
    // Correspondance par similarité (fuzzy matching)
    // Suggestions de mapping manuel
  }

  /**
   * Prévisualisation des données
   */
  static async generatePreview(importId, maxRows = 50) {
    // Génération d'un aperçu des données
    // Avec indicateurs de validation
    // Statistiques de qualité des données
  }
}
```

### 2.3 Service d'Inventaire (`InventaireService`)

```javascript
class InventaireService {
  /**
   * Gestion des ingrédients
   */
  static async createIngredient(ingredientData) {
    // Création d'un nouvel ingrédient
    // Validation des données
    // Vérification des doublons
    // Initialisation du stock à zéro
  }

  static async updateIngredient(ingredientId, data) {
    // Mise à jour des informations ingrédient
    // Recalcul des coûts des recettes liées
    // Historisation des changements de prix
  }

  static async getIngredients(complexeId, filters = {}) {
    // Récupération avec filtres (catégorie, actif, etc.)
    // Pagination et tri
    // Calcul des stocks actuels
  }

  static async searchIngredients(query, complexeId) {
    // Recherche textuelle avancée
    // Par nom, description, code-barres
    // Suggestions de correspondance
  }

  /**
   * Gestion du stock (utilise les tables existantes)
   */
  static async updateStockFromInventaire(inventaireId) {
    // Mise à jour du stock basée sur inventaire physique
    // Calcul des écarts
    // Génération des mouvements de stock
  }

  static async getStockIngredients(complexeId, serviceId = null) {
    // Récupération du stock par ingrédient
    // Calcul de la valeur totale
    // Alertes de stock faible
  }

  static async calculateStockValue(complexeId) {
    // Calcul de la valeur totale du stock
    // Par catégorie d'ingrédients
    // Évolution dans le temps
  }

  /**
   * Calculs de coûts et analyses
   */
  static async calculateRecipeCost(recetteId) {
    // Calcul du coût total d'une recette
    // Basé sur les prix actuels des ingrédients
    // Prise en compte des quantités
  }

  static async updateAllRecipeCosts(serviceId) {
    // Recalcul de tous les coûts de recettes
    // Pour un service donné
    // Mise à jour des marges
  }

  static async analyzeIngredientUsage(ingredientId, dateDebut, dateFin) {
    // Analyse de consommation d'un ingrédient
    // Prévisions de besoin
    // Optimisation des commandes
  }
}
```

### 2.4 Service de Recettes (`RecetteService`)

```javascript
class RecetteService {
  /**
   * CRUD des recettes
   */
  static async createRecette(recetteData, ingredients = []) {
    // Création de la recette
    // Ajout des ingrédients associés
    // Calcul du coût initial
    // Suggestion de prix de vente
  }

  static async updateRecette(recetteId, data) {
    // Mise à jour des informations recette
    // Recalcul automatique des coûts
    // Mise à jour du produit associé
  }

  static async getRecettesByService(serviceId, includeInactive = false) {
    // Récupération des recettes par service
    // Avec détails des ingrédients
    // Calculs de coûts et marges
  }

  static async getRecetteDetails(recetteId) {
    // Détails complets d'une recette
    // Liste des ingrédients avec coûts
    // Analyses de rentabilité
    // Historique des modifications
  }

  /**
   * Gestion des ingrédients de recettes
   */
  static async addIngredientToRecette(recetteId, ingredientData) {
    // Ajout d'un ingrédient à une recette
    // Validation des quantités et unités
    // Recalcul du coût total
  }

  static async updateRecetteIngredient(recetteIngredientId, data) {
    // Modification quantité/unité d'un ingrédient
    // Recalcul automatique des coûts
  }

  static async removeIngredientFromRecette(recetteIngredientId) {
    // Suppression d'un ingrédient de la recette
    // Recalcul des coûts
    // Vérification de l'impact sur le prix
  }

  /**
   * Analyses et optimisations
   */
  static async analyzeRecetteProfitability(recetteId) {
    // Analyse de rentabilité détaillée
    // Comparaison avec la concurrence
    // Suggestions d'optimisation
  }

  static async suggestPriceOptimization(recetteId, targetMargin) {
    // Suggestion de prix optimal
    // Basé sur la marge cible
    // Prise en compte du positionnement
  }

  static async findRecipesByIngredient(ingredientId) {
    // Recherche des recettes utilisant un ingrédient
    // Impact des changements de prix
    // Suggestions de substitution
  }
}
```

### 2.5 Service d'Import (`ImportService`)

```javascript
class ImportService {
  /**
   * Orchestration de l'import
   */
  static async processImport(importId) {
    // Orchestration complète du processus d'import
    // Gestion des étapes et du statut
    // Rollback en cas d'erreur
  }

  static async validateImportData(importId) {
    // Validation complète des données
    // Vérification des contraintes métier
    // Génération du rapport de validation
  }

  static async executeImport(importId) {
    // Exécution de l'import validé
    // Transaction atomique
    // Mise à jour des données liées
  }

  /**
   * Import spécialisé par type
   */
  static async importMenuRestaurant(importId, serviceId) {
    // Import spécifique menu restaurant
    // Création des produits et recettes
    // Liaison avec les ingrédients
    // Mise à jour de la tarification
  }

  static async importCarteBar(importId, serviceId) {
    // Import spécifique carte de bar
    // Gestion des cocktails et ingrédients
    // Configuration happy hour
  }

  static async importInventaireIngredients(importId, complexeId) {
    // Import des ingrédients et stocks
    // Mise à jour des prix
    // Génération des mouvements de stock
  }

  /**
   * Gestion des erreurs et rollback
   */
  static async handleImportErrors(importId) {
    // Traitement des erreurs d'import
    // Suggestions de correction
    // Import partiel si possible
  }

  static async rollbackImport(importId) {
    // Annulation complète d'un import
    // Restauration de l'état précédent
    // Nettoyage des données créées
  }

  /**
   * Rapports et historique
   */
  static async generateImportReport(importId) {
    // Génération du rapport d'import
    // Statistiques détaillées
    // Export en PDF/Excel
  }

  static async getImportHistory(complexeId, filters = {}) {
    // Historique des imports
    // Filtres par type, statut, date
    // Statistiques de succès
  }
}
```

## Phase 3 : Contrôleurs API

### 3.1 Contrôleur d'Upload (`UploadController`)

```javascript
class UploadController {
  /**
   * POST /api/upload/excel
   * Upload d'un fichier Excel
   */
  static async uploadExcelFile(request, response) {
    // Validation du fichier uploadé
    // Création de l'enregistrement d'import
    // Démarrage du parsing asynchrone
    // Retour de l'ID d'import pour suivi
  }

  /**
   * GET /api/upload/templates/:type
   * Téléchargement des templates d'import
   */
  static async getImportTemplate(request, response) {
    // Génération du fichier template Excel
    // Avec exemples et instructions
    // Headers appropriés pour téléchargement
  }

  /**
   * POST /api/upload/validate/:importId
   * Validation des données importées
   */
  static async validateImportData(request, response) {
    // Déclenchement de la validation
    // Retour du statut de validation
    // Liste des erreurs détectées
  }

  /**
   * GET /api/upload/preview/:importId
   * Prévisualisation des données
   */
  static async previewImportData(request, response) {
    // Aperçu des données parsées
    // Indicateurs de qualité
    // Suggestions de correction
  }

  /**
   * GET /api/upload/status/:importId
   * Statut de l'import en cours
   */
  static async getImportStatus(request, response) {
    // Statut actuel de l'import
    // Progression si en cours
    // Résultats si terminé
  }
}
```

### 3.2 Contrôleur d'Inventaire (`InventaireController`)

```javascript
class InventaireController {
  /**
   * Gestion des ingrédients
   */

  // GET /api/inventaire/ingredients
  static async getIngredients(request, response) {
    // Liste paginée des ingrédients
    // Filtres par catégorie, statut
    // Informations de stock associées
  }

  // POST /api/inventaire/ingredients
  static async createIngredient(request, response) {
    // Création d'un nouvel ingrédient
    // Validation des données
    // Initialisation du stock
  }

  // PUT /api/inventaire/ingredients/:id
  static async updateIngredient(request, response) {
    // Mise à jour d'un ingrédient
    // Recalcul des coûts impactés
    // Historisation des changements
  }

  // DELETE /api/inventaire/ingredients/:id
  static async deleteIngredient(request, response) {
    // Suppression logique
    // Vérification des dépendances
    // Impact sur les recettes
  }

  /**
   * Gestion du stock
   */

  // GET /api/inventaire/stock/:complexeId
  static async getStock(request, response) {
    // État du stock par complexe
    // Valeurs et quantités
    // Alertes de stock faible
  }

  // PUT /api/inventaire/stock/:ingredientId
  static async updateStock(request, response) {
    // Mise à jour manuelle du stock
    // Génération du mouvement
    // Recalcul des valeurs
  }

  // GET /api/inventaire/alerts/:complexeId
  static async getStockAlerts(request, response) {
    // Alertes de stock faible
    // Dates d'expiration proches
    // Suggestions de commande
  }

  /**
   * Analyses et rapports
   */

  // GET /api/inventaire/analytics/:complexeId
  static async getInventaireAnalytics(request, response) {
    // Analyses de consommation
    // Évolution des coûts
    // Optimisations suggérées
  }
}
```

### 3.3 Contrôleur de Recettes (`RecetteController`)

```javascript
class RecetteController {
  /**
   * CRUD des recettes
   */

  // GET /api/recettes/service/:serviceId
  static async getRecettesByService(request, response) {
    // Recettes par service
    // Avec coûts et marges calculés
    // Filtres par rentabilité
  }

  // POST /api/recettes
  static async createRecette(request, response) {
    // Création d'une nouvelle recette
    // Avec liste d'ingrédients
    // Calcul automatique des coûts
  }

  // PUT /api/recettes/:id
  static async updateRecette(request, response) {
    // Mise à jour de la recette
    // Recalcul des coûts
    // Impact sur le produit lié
  }

  // DELETE /api/recettes/:id
  static async deleteRecette(request, response) {
    // Suppression de la recette
    // Vérification des dépendances
    // Mise à jour du produit
  }

  /**
   * Gestion des ingrédients de recettes
   */

  // POST /api/recettes/:id/ingredients
  static async addIngredientToRecette(request, response) {
    // Ajout d'ingrédient à la recette
    // Validation des quantités
    // Recalcul des coûts
  }

  // PUT /api/recettes/:id/ingredients/:ingredientId
  static async updateRecetteIngredient(request, response) {
    // Modification quantité/unité
    // Recalcul automatique
  }

  // DELETE /api/recettes/:id/ingredients/:ingredientId
  static async removeIngredientFromRecette(request, response) {
    // Suppression d'ingrédient
    // Recalcul des coûts
  }

  /**
   * Analyses et optimisations
   */

  // POST /api/recettes/:id/calculate-cost
  static async calculateCost(request, response) {
    // Recalcul forcé du coût
    // Avec prix actuels des ingrédients
  }

  // GET /api/recettes/:id/profitability
  static async analyzeProfitability(request, response) {
    // Analyse de rentabilité détaillée
    // Comparaisons et suggestions
  }

  // POST /api/recettes/:id/optimize-price
  static async optimizePrice(request, response) {
    // Optimisation du prix de vente
    // Basé sur marge cible
  }
}
```

### 3.4 Contrôleur d'Import (`ImportController`)

```javascript
class ImportController {
  /**
   * Gestion du processus d'import
   */

  // POST /api/imports/:importId/process
  static async processImport(request, response) {
    // Démarrage du traitement d'import
    // Validation et exécution
    // Suivi asynchrone
  }

  // GET /api/imports/:importId/status
  static async getImportStatus(request, response) {
    // Statut détaillé de l'import
    // Progression et erreurs
    // Résultats partiels
  }

  // GET /api/imports/history/:complexeId
  static async getImportHistory(request, response) {
    // Historique des imports
    // Filtres et pagination
    // Statistiques de succès
  }

  // POST /api/imports/:importId/rollback
  static async rollbackImport(request, response) {
    // Annulation d'un import
    // Restauration de l'état précédent
    // Nettoyage des données
  }

  // GET /api/imports/:importId/report
  static async getImportReport(request, response) {
    // Rapport détaillé d'import
    // Export en différents formats
    // Statistiques et métriques
  }

  /**
   * Gestion des templates
   */

  // GET /api/imports/templates
  static async getTemplates(request, response) {
    // Liste des templates disponibles
    // Par type de service
    // Avec descriptions
  }

  // POST /api/imports/templates
  static async createTemplate(request, response) {
    // Création d'un nouveau template
    // Validation de la structure
    // Génération d'exemples
  }
}
```

## Phase 4 : Middleware et Utilitaires

### 4.1 Middleware de Validation

```javascript
/**
 * Validation des fichiers uploadés
 */
const validateFileUpload = (req, res, next) => {
  // Vérification du type MIME
  // Limitation de taille
  // Validation de l'extension
  // Scan de sécurité basique
};

/**
 * Validation des permissions d'import
 */
const checkImportPermissions = (req, res, next) => {
  // Vérification des droits utilisateur
  // Permissions par type d'import
  // Restrictions par complexe/service
};

/**
 * Configuration multer pour upload
 */
const fileUploadConfig = multer({
  dest: 'uploads/temp/',
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 1
  },
  fileFilter: (req, file, cb) => {
    // Filtrage par type de fichier
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];
    cb(null, allowedTypes.includes(file.mimetype));
  }
});
```

### 4.2 Utilitaires

```javascript
/**
 * Conversion d'unités de mesure
 */
class UnitConverter {
  static convert(value, fromUnit, toUnit) {
    // Conversion entre unités compatibles
    // kg <-> g, L <-> mL, etc.
  }

  static getSupportedUnits() {
    // Liste des unités supportées
    // Avec facteurs de conversion
  }

  static validateUnitCompatibility(unit1, unit2) {
    // Vérification de compatibilité
    // Pour conversions automatiques
  }
}

/**
 * Calculs de coûts et marges
 */
class CostCalculator {
  static calculateRecipeCost(ingredients) {
    // Calcul du coût total d'une recette
    // Prise en compte des quantités et prix
  }

  static calculateMargin(cost, price) {
    // Calcul de la marge bénéficiaire
    // En valeur et pourcentage
  }

  static suggestPrice(cost, targetMargin) {
    // Suggestion de prix de vente
    // Basé sur marge cible
  }

  static analyzeMarginEvolution(recetteId, period) {
    // Évolution de la marge dans le temps
    // Impact des variations de prix
  }
}

/**
 * Validation des données d'import
 */
class ImportValidator {
  static validateMenuData(data) {
    // Validation spécifique menu restaurant
    // Vérification des prix, catégories
    // Validation des ingrédients
  }

  static validateBarData(data) {
    // Validation spécifique carte bar
    // Types de boissons, degrés d'alcool
    // Configuration happy hour
  }

  static validateInventaireData(data) {
    // Validation données inventaire
    // Quantités, prix, dates d'expiration
    // Cohérence des unités
  }

  static generateValidationReport(errors) {
    // Génération de rapport d'erreurs
    // Formatage pour affichage utilisateur
    // Suggestions de correction
  }
}

/**
 * Générateur de templates Excel
 */
class TemplateGenerator {
  static generateRestaurantTemplate() {
    // Génération template restaurant
    // Avec exemples et validation
  }

  static generateBarTemplate() {
    // Génération template bar
    // Structure spécialisée cocktails
  }

  static generateInventaireTemplate() {
    // Génération template inventaire
    // Avec calculs automatiques
  }
}
```

## Phase 5 : Intégration avec l'Existant ✅ **TERMINÉE**

### 5.1 Extension du Service de Tarification ✅

```javascript
// Extension du ServiceComplexeService existant
class ServiceComplexeService {
  /**
   * Mise à jour automatique des tarifs depuis les recettes
   */
  static async updateTarificationFromRecipes(serviceId) {
    // Récupération des recettes du service
    // Calcul des prix suggérés
    // Mise à jour de la tarification JSON
    // Préservation des prix manuels
  }

  /**
   * Synchronisation prix/coûts
   */
  static async syncPricesWithCosts(serviceId, targetMargin) {
    // Ajustement des prix selon les coûts
    // Application de la marge cible
    // Respect des contraintes de positionnement
  }

  /**
   * Génération de tarification depuis import
   */
  static async generateTarificationFromImport(importId, serviceId) {
    // Création automatique de la tarification
    // Basée sur les données importées
    // Avec calculs de coûts et marges
  }
}
```

### 5.2 Intégration POS ✅

**Implémentation complète réalisée :**
- ✅ Service `POSStockIntegration` créé avec toutes les méthodes
- ✅ Contrôleur `POSStockController` avec 10 endpoints
- ✅ Routes `/api/pos-stock/*` avec middlewares de sécurité
- ✅ Extension `ServiceComplexeService` avec 4 nouvelles méthodes
- ✅ Intégration complète dans le système de routes
- ✅ Script de test `test-phase5-integration.js` créé

```javascript
/**
 * Extension du système POS pour gestion stock
 */
class POSStockIntegration {
  static async processTransactionWithStock(transactionData) {
    // Traitement de transaction POS
    // Déduction automatique du stock
    // Vérification disponibilité ingrédients
  }

  static async checkIngredientAvailability(produitId, quantite) {
    // Vérification stock suffisant
    // Pour les ingrédients de la recette
    // Alertes si stock insuffisant
  }

  static async updateStockFromSales(transactionId) {
    // Mise à jour stock après vente
    // Génération mouvements de stock
    // Calcul consommation réelle
  }

  static async generateStockAlerts() {
    // Génération alertes stock faible
    // Basées sur les ventes récentes
    // Prévisions de rupture
  }
}
```

## Phase 6 : Sécurité et Performance

### 6.1 Sécurité

```javascript
/**
 * Validation de sécurité des fichiers
 */
const fileSecurityCheck = (file) => {
  // Vérification type MIME réel
  // Scan antivirus si disponible
  // Validation structure Excel
  // Détection de macros malveillantes
};

/**
 * Permissions granulaires
 */
const inventairePermissions = {
  VIEW_INVENTORY: 'view_inventory',
  MANAGE_INVENTORY: 'manage_inventory',
  IMPORT_DATA: 'import_data',
  MANAGE_RECIPES: 'manage_recipes',
  VIEW_COSTS: 'view_costs',
  MANAGE_PRICING: 'manage_pricing'
};

/**
 * Audit trail
 */
const auditLogger = {
  logImport: (userId, action, details) => {
    // Log des actions d'import
    // Traçabilité complète
  },

  logPriceChange: (userId, produitId, oldPrice, newPrice) => {
    // Log des changements de prix
    // Avec justification
  }
};
```

### 6.2 Performance

```javascript
/**
 * Traitement asynchrone des gros imports
 */
const processLargeImport = async (importId) => {
  // Queue de traitement Redis/Bull
  // Traitement par chunks
  // Mise à jour statut temps réel
  // Gestion des timeouts
};

/**
 * Cache pour calculs coûteux
 */
const costCache = {
  // Cache Redis pour coûts de recettes
  // Invalidation intelligente
  // TTL adaptatif selon fréquence d'accès
};

/**
 * Optimisations base de données
 */
const dbOptimizations = {
  // Index composites pour requêtes fréquentes
  // Vues matérialisées pour rapports
  // Partitioning des tables historiques
};
```

## Phase 7 : Monitoring et Logs

### 7.1 Logs Spécialisés

```javascript
/**
 * Logs d'import
 */
logger.info('Import started', {
  importId,
  type,
  fileSize,
  userId,
  complexeId
});

logger.error('Import failed', {
  importId,
  errors: errorDetails,
  stage: 'validation|processing|execution'
});

/**
 * Logs de stock
 */
logger.warn('Stock alert', {
  ingredientId,
  currentStock,
  minStock,
  complexeId
});

/**
 * Logs de calculs
 */
logger.info('Recipe cost calculated', {
  recetteId,
  oldCost,
  newCost,
  priceImpact
});
```

### 7.2 Métriques et KPIs

```javascript
/**
 * Métriques d'import
 */
const importMetrics = {
  // Nombre d'imports par jour/type
  // Taux de succès des imports
  // Temps moyen de traitement
  // Volume de données traitées
};

/**
 * Métriques d'inventaire
 */
const inventoryMetrics = {
  // Valeur totale du stock
  // Nombre d'alertes de stock
  // Rotation des stocks
  // Évolution des coûts
};

/**
 * Métriques de performance
 */
const performanceMetrics = {
  // Temps de calcul des coûts
  // Performance des requêtes
  // Utilisation du cache
  // Charge système
};
```

## Phase 8 : Tests et Validation

### 8.1 Tests Unitaires

```javascript
// Tests des services principaux
describe('ExcelParserService', () => {
  test('should parse restaurant menu correctly');
  test('should validate data according to template');
  test('should handle malformed Excel files');
});

describe('InventaireService', () => {
  test('should calculate recipe costs correctly');
  test('should update stock movements');
  test('should generate stock alerts');
});
```

### 8.2 Tests d'Intégration

```javascript
// Tests de bout en bout
describe('Import Process Integration', () => {
  test('should complete full restaurant menu import');
  test('should handle import rollback correctly');
  test('should update tarification after import');
});
```

### 8.3 Tests de Performance

```javascript
// Tests de charge
describe('Performance Tests', () => {
  test('should handle large Excel files (1000+ rows)');
  test('should process multiple concurrent imports');
  test('should maintain response times under load');
});
```

## Ordre d'Implémentation Recommandé

### Sprint 1 (3-4 semaines) - Infrastructure de base
1. Extension du schéma de base de données
2. Service de gestion des fichiers
3. Parser Excel basique
4. API d'upload simple

### Sprint 2 (3-4 semaines) - Validation et templates
1. Système de validation avancé
2. Templates d'import standardisés
3. Interface de prévisualisation
4. Gestion des erreurs

### Sprint 3 (4-5 semaines) - Inventaire et recettes
1. Service d'inventaire complet
2. Gestion des recettes et ingrédients
3. Calculs de coûts automatiques
4. Intégration avec tables existantes

### Sprint 4 (3-4 semaines) - Import spécialisé
1. Import menu restaurant
2. Import carte bar
3. Import inventaire ingrédients
4. Synchronisation avec tarification

### Sprint 5 (2-3 semaines) - Optimisations et intégrations
1. Intégration POS
2. Optimisations de performance
3. Sécurité renforcée
4. Monitoring et métriques

### Sprint 6 (2 semaines) - Tests et déploiement
1. Tests complets
2. Documentation utilisateur
3. Formation équipes
4. Déploiement progressif

## Considérations Techniques

### Dépendances NPM Requises
```json
{
  "xlsx": "^0.18.5",
  "multer": "^1.4.5",
  "bull": "^4.10.4",
  "redis": "^4.6.5",
  "node-cron": "^3.0.2",
  "pdf-lib": "^1.17.1"
}
```

### Configuration Système
- **Stockage** : Minimum 50GB pour fichiers temporaires
- **RAM** : 8GB minimum pour traitement gros fichiers
- **Redis** : Pour cache et queues de traitement
- **Cron Jobs** : Pour nettoyage automatique et calculs périodiques

Ce plan détaillé fournit une roadmap complète pour l'implémentation du système d'upload Excel et d'inventaire, en s'appuyant sur l'infrastructure existante et en respectant les bonnes pratiques de développement.
```
