const { transports, createLogger, format } = require('winston');
const path = require('path');

const logger = createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: format.combine(
    format.timestamp(),
    format.errors({ stack: true }),
    format.json()
  ),
  defaultMeta: { 
    service: 'hotel-saas-api',
    environment: process.env.NODE_ENV 
  },
  transports: [
    new transports.Console({
      format: format.combine(
        format.colorize(),
        format.simple()
      )
    }),
    new transports.File({ 
      filename: path.join('logs', 'error.log'), 
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    new transports.File({ 
      filename: path.join('logs', 'combined.log'),
      maxsize: 5242880,
      maxFiles: 5
    })
  ]
});

// Handle uncaught exceptions and unhandled rejections
logger.exceptions.handle(
  new transports.File({ 
    filename: path.join('logs', 'exceptions.log'),
    maxsize: 5242880,
    maxFiles: 5
  })
);

logger.rejections.handle(
  new transports.File({ 
    filename: path.join('logs', 'rejections.log'),
    maxsize: 5242880,
    maxFiles: 5
  })
);

module.exports = logger;
