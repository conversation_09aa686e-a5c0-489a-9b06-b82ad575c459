const NodeCache = require('node-cache');
const logger = require('../logger');

/**
 * Configuration des caches par type de données
 */
const CACHE_CONFIGS = {
  // Cache pour les templates (TTL: 1 heure)
  templates: {
    stdTTL: 3600,
    checkperiod: 600,
    useClones: false
  },
  
  // Cache pour les ingrédients (TTL: 30 minutes)
  ingredients: {
    stdTTL: 1800,
    checkperiod: 300,
    useClones: false
  },
  
  // Cache pour les recettes (TTL: 15 minutes)
  recipes: {
    stdTTL: 900,
    checkperiod: 180,
    useClones: false
  },
  
  // Cache pour les analyses (TTL: 5 minutes)
  analytics: {
    stdTTL: 300,
    checkperiod: 60,
    useClones: false
  },
  
  // Cache pour les conversions d'unités (TTL: 24 heures)
  conversions: {
    stdTTL: 86400,
    checkperiod: 3600,
    useClones: false
  }
};

/**
 * Instances de cache
 */
const caches = {};

// Initialisation des caches
for (const [name, config] of Object.entries(CACHE_CONFIGS)) {
  caches[name] = new NodeCache(config);
  
  // Événements de cache
  caches[name].on('set', (key, value) => {
    logger.debug(`Cache SET: ${name}:${key}`);
  });
  
  caches[name].on('del', (key, value) => {
    logger.debug(`Cache DEL: ${name}:${key}`);
  });
  
  caches[name].on('expired', (key, value) => {
    logger.debug(`Cache EXPIRED: ${name}:${key}`);
  });
}

/**
 * Middleware de cache générique
 */
const cacheMiddleware = (cacheType, keyGenerator, ttl = null) => {
  return (req, res, next) => {
    try {
      const cache = caches[cacheType];
      
      if (!cache) {
        logger.warn(`Cache type not found: ${cacheType}`);
        return next();
      }

      // Génération de la clé de cache
      const cacheKey = typeof keyGenerator === 'function' 
        ? keyGenerator(req) 
        : keyGenerator;

      // Tentative de récupération depuis le cache
      const cachedData = cache.get(cacheKey);
      
      if (cachedData) {
        logger.debug(`Cache HIT: ${cacheType}:${cacheKey}`);
        
        return res.json({
          success: true,
          data: cachedData.data,
          message: cachedData.message,
          cached: true,
          cacheTimestamp: cachedData.timestamp
        });
      }

      logger.debug(`Cache MISS: ${cacheType}:${cacheKey}`);

      // Interception de la réponse pour mise en cache
      const originalJson = res.json;
      res.json = function(data) {
        // Mise en cache seulement si succès
        if (data.success && data.data) {
          const cacheData = {
            data: data.data,
            message: data.message,
            timestamp: new Date().toISOString()
          };
          
          if (ttl) {
            cache.set(cacheKey, cacheData, ttl);
          } else {
            cache.set(cacheKey, cacheData);
          }
          
          logger.debug(`Cache STORED: ${cacheType}:${cacheKey}`);
        }
        
        return originalJson.call(this, data);
      };

      next();

    } catch (error) {
      logger.error('Error in cache middleware:', error);
      next(); // Continue sans cache en cas d'erreur
    }
  };
};

/**
 * Middleware de cache pour les templates
 */
const cacheTemplates = cacheMiddleware('templates', (req) => {
  const { chaineId, typeService, typeImport } = req.query;
  return `templates:${chaineId}:${typeService || 'all'}:${typeImport || 'all'}`;
});

/**
 * Middleware de cache pour les ingrédients
 */
const cacheIngredients = cacheMiddleware('ingredients', (req) => {
  const { complexeId } = req.query;
  const { categorie, actif, search, page = 1, limit = 50 } = req.query;
  return `ingredients:${complexeId}:${categorie || 'all'}:${actif || 'all'}:${search || 'none'}:${page}:${limit}`;
});

/**
 * Middleware de cache pour les recettes
 */
const cacheRecipes = cacheMiddleware('recipes', (req) => {
  const { serviceId } = req.params;
  const { includeInactive = false } = req.query;
  return `recipes:service:${serviceId}:${includeInactive}`;
});

/**
 * Middleware de cache pour les analyses
 */
const cacheAnalytics = cacheMiddleware('analytics', (req) => {
  const { complexeId } = req.params;
  const endpoint = req.route.path;
  return `analytics:${complexeId}:${endpoint}`;
});

/**
 * Middleware de cache pour les conversions d'unités
 */
const cacheConversions = cacheMiddleware('conversions', (req) => {
  const { fromUnit, toUnit, value } = req.query;
  return `conversion:${fromUnit}:${toUnit}:${value}`;
});

/**
 * Invalidation de cache
 */
const invalidateCache = (cacheType, pattern = null) => {
  try {
    const cache = caches[cacheType];
    
    if (!cache) {
      logger.warn(`Cache type not found for invalidation: ${cacheType}`);
      return false;
    }

    if (pattern) {
      // Invalidation par pattern
      const keys = cache.keys();
      const matchingKeys = keys.filter(key => key.includes(pattern));
      
      for (const key of matchingKeys) {
        cache.del(key);
      }
      
      logger.info(`Cache invalidated: ${cacheType} (${matchingKeys.length} keys with pattern: ${pattern})`);
      return matchingKeys.length;
    } else {
      // Invalidation complète
      cache.flushAll();
      logger.info(`Cache completely invalidated: ${cacheType}`);
      return true;
    }

  } catch (error) {
    logger.error('Error invalidating cache:', error);
    return false;
  }
};

/**
 * Middleware d'invalidation automatique après modification
 */
const autoInvalidateCache = (cacheTypes) => {
  return (req, res, next) => {
    const originalJson = res.json;
    
    res.json = function(data) {
      // Invalidation seulement si modification réussie
      if (data.success && ['POST', 'PUT', 'DELETE'].includes(req.method)) {
        for (const cacheType of cacheTypes) {
          // Invalidation basée sur les paramètres de la requête
          let pattern = null;
          
          if (cacheType === 'ingredients' && req.query.complexeId) {
            pattern = `ingredients:${req.query.complexeId}`;
          } else if (cacheType === 'recipes' && req.params.serviceId) {
            pattern = `recipes:service:${req.params.serviceId}`;
          } else if (cacheType === 'analytics' && req.params.complexeId) {
            pattern = `analytics:${req.params.complexeId}`;
          }
          
          invalidateCache(cacheType, pattern);
        }
      }
      
      return originalJson.call(this, data);
    };
    
    next();
  };
};

/**
 * Statistiques de cache
 */
const getCacheStats = () => {
  const stats = {};
  
  for (const [name, cache] of Object.entries(caches)) {
    const cacheStats = cache.getStats();
    stats[name] = {
      keys: cacheStats.keys,
      hits: cacheStats.hits,
      misses: cacheStats.misses,
      hitRate: cacheStats.hits > 0 ? (cacheStats.hits / (cacheStats.hits + cacheStats.misses)) * 100 : 0,
      vsize: cacheStats.vsize,
      ksize: cacheStats.ksize
    };
  }
  
  return stats;
};

/**
 * Nettoyage périodique des caches
 */
const cleanupCaches = () => {
  for (const [name, cache] of Object.entries(caches)) {
    const beforeKeys = cache.keys().length;
    cache.flushAll();
    logger.info(`Cache cleaned: ${name} (${beforeKeys} keys removed)`);
  }
};

/**
 * Middleware de cache conditionnel
 */
const conditionalCache = (cacheType, condition, keyGenerator, ttl = null) => {
  return (req, res, next) => {
    if (typeof condition === 'function' && !condition(req)) {
      return next(); // Pas de cache si condition non remplie
    }
    
    return cacheMiddleware(cacheType, keyGenerator, ttl)(req, res, next);
  };
};

/**
 * Préchargement de cache
 */
const preloadCache = async (cacheType, data, key, ttl = null) => {
  try {
    const cache = caches[cacheType];
    
    if (!cache) {
      throw new Error(`Cache type not found: ${cacheType}`);
    }

    const cacheData = {
      data,
      message: 'Preloaded data',
      timestamp: new Date().toISOString()
    };

    if (ttl) {
      cache.set(key, cacheData, ttl);
    } else {
      cache.set(key, cacheData);
    }

    logger.info(`Cache preloaded: ${cacheType}:${key}`);
    return true;

  } catch (error) {
    logger.error('Error preloading cache:', error);
    return false;
  }
};

/**
 * Middleware de cache pour les réponses lourdes
 */
const heavyResponseCache = (cacheType, keyGenerator, ttl = 3600) => {
  return cacheMiddleware(cacheType, keyGenerator, ttl);
};

module.exports = {
  // Middlewares spécialisés
  cacheTemplates,
  cacheIngredients,
  cacheRecipes,
  cacheAnalytics,
  cacheConversions,
  
  // Middlewares génériques
  cacheMiddleware,
  conditionalCache,
  heavyResponseCache,
  autoInvalidateCache,
  
  // Fonctions utilitaires
  invalidateCache,
  getCacheStats,
  cleanupCaches,
  preloadCache,
  
  // Accès aux instances de cache
  caches
};
