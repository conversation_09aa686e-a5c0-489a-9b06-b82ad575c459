const logger = require('../logger');

/**
 * Middleware de logging spécialisé pour les opérations restaurant/bar
 * Enregistre les actions importantes avec contexte détaillé
 */

/**
 * Logger pour les opérations de commande
 */
const logCommandeOperation = (operation) => {
  return (req, res, next) => {
    const startTime = Date.now();
    const { complexe_id, employe_id, id: user_id } = req.user;
    const { service_id, table_id } = req.body;
    const { id: commande_id } = req.params;

    // Informations de base
    const logData = {
      operation,
      timestamp: new Date().toISOString(),
      user_id,
      employe_id,
      complexe_id,
      service_id,
      table_id,
      commande_id,
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    };

    // Ajouter des données spécifiques selon l'opération
    switch (operation) {
      case 'CREATE_COMMANDE':
        logData.items_count = req.body.items ? req.body.items.length : 0;
        logData.type_commande = req.body.type_commande;
        break;
      
      case 'ADD_ITEM':
        logData.produit_id = req.body.produit_id;
        logData.quantite = req.body.quantite;
        break;
      
      case 'UPDATE_ITEM':
        logData.item_id = req.params.itemId;
        logData.nouvelle_quantite = req.body.quantite;
        break;
      
      case 'UPDATE_STATUS':
        logData.nouveau_statut = req.body.statut;
        break;
    }

    // Logger le début de l'opération
    logger.info(`[RESTAURANT] ${operation} - Début`, logData);

    // Intercepter la réponse pour logger le résultat
    const originalSend = res.send;
    res.send = function(data) {
      const duration = Date.now() - startTime;
      const responseData = typeof data === 'string' ? JSON.parse(data) : data;
      
      const resultLog = {
        ...logData,
        duration_ms: duration,
        success: responseData.success || false,
        status_code: res.statusCode
      };

      if (responseData.success) {
        logger.info(`[RESTAURANT] ${operation} - Succès`, resultLog);
      } else {
        logger.warn(`[RESTAURANT] ${operation} - Échec`, {
          ...resultLog,
          error_message: responseData.message,
          error_code: responseData.code
        });
      }

      originalSend.call(this, data);
    };

    next();
  };
};

/**
 * Logger pour les opérations de table
 */
const logTableOperation = (operation) => {
  return (req, res, next) => {
    const startTime = Date.now();
    const { complexe_id, employe_id } = req.user;
    const { id: table_id } = req.params;

    const logData = {
      operation,
      timestamp: new Date().toISOString(),
      employe_id,
      complexe_id,
      table_id,
      ip_address: req.ip
    };

    // Ajouter des données spécifiques
    if (operation === 'UPDATE_TABLE_STATUS') {
      logData.nouveau_statut = req.body.statut;
    }

    logger.info(`[TABLE] ${operation} - Début`, logData);

    // Intercepter la réponse
    const originalSend = res.send;
    res.send = function(data) {
      const duration = Date.now() - startTime;
      const responseData = typeof data === 'string' ? JSON.parse(data) : data;
      
      const resultLog = {
        ...logData,
        duration_ms: duration,
        success: responseData.success || false,
        status_code: res.statusCode
      };

      if (responseData.success) {
        logger.info(`[TABLE] ${operation} - Succès`, resultLog);
      } else {
        logger.warn(`[TABLE] ${operation} - Échec`, {
          ...resultLog,
          error_message: responseData.message
        });
      }

      originalSend.call(this, data);
    };

    next();
  };
};

/**
 * Logger pour les alertes de stock
 */
const logStockAlert = (req, res, next) => {
  // Si des alertes de stock ont été détectées pendant la validation
  if (req.stockAlerts && req.stockAlerts.length > 0) {
    const { complexe_id, employe_id } = req.user;
    const { service_id } = req.body;

    logger.warn('[STOCK] Alertes détectées pendant opération restaurant', {
      timestamp: new Date().toISOString(),
      employe_id,
      complexe_id,
      service_id,
      alertes_count: req.stockAlerts.length,
      alertes: req.stockAlerts.map(alert => ({
        type: alert.type,
        ingredient_nom: alert.ingredient_nom,
        stock_actuel: alert.stock_actuel,
        stock_minimal: alert.stock_minimal
      }))
    });
  }

  next();
};

/**
 * Logger pour les opérations de validation
 */
const logValidationResult = (req, res, next) => {
  const { complexe_id, employe_id } = req.user;
  
  const validationData = {
    timestamp: new Date().toISOString(),
    employe_id,
    complexe_id,
    ip_address: req.ip
  };

  // Logger les résultats de validation de table
  if (req.tableInfo) {
    logger.info('[VALIDATION] Table validée', {
      ...validationData,
      table_id: req.tableInfo.table_id,
      table_statut: req.tableInfo.statut,
      service_id: req.tableInfo.service_id
    });
  }

  // Logger les résultats de validation de stock
  if (req.stockValidation) {
    logger.info('[VALIDATION] Stock validé', {
      ...validationData,
      items_checked: req.stockValidation.items_checked,
      all_available: req.stockValidation.all_available,
      alerts_count: req.stockValidation.alerts_count
    });
  }

  // Logger les résultats de validation de session POS
  if (req.posSession) {
    logger.info('[VALIDATION] Session POS validée', {
      ...validationData,
      pos_id: req.posSession.pos_id,
      service_id: req.posSession.service_id
    });
  }

  next();
};

/**
 * Logger d'audit pour les opérations critiques
 */
const auditLog = (operation, details = {}) => {
  return (req, res, next) => {
    const { complexe_id, employe_id, id: user_id } = req.user;
    
    logger.info('[AUDIT] Opération critique restaurant', {
      operation,
      timestamp: new Date().toISOString(),
      user_id,
      employe_id,
      complexe_id,
      ip_address: req.ip,
      user_agent: req.get('User-Agent'),
      details,
      request_body: req.body,
      request_params: req.params
    });

    next();
  };
};

module.exports = {
  logCommandeOperation,
  logTableOperation,
  logStockAlert,
  logValidationResult,
  auditLog
};
