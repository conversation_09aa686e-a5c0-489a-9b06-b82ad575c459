const logger = require('../logger');
const db = require('../db');

/**
 * Middleware d'inventaire - Système simplifié
 * Phase 4 : Refactoring des middlewares
 */

/**
 * Mapping des permissions d'inventaire vers le système simplifié
 */
const INVENTAIRE_PERMISSIONS_MAPPING = {
  // Anciennes permissions -> Nouvelles permissions simplifiées
  'view_inventory': 'service_operations',
  'manage_inventory': 'management_operations',
  'import_data': 'management_operations',
  'manage_recipes': 'management_operations',
  'view_costs': 'management_operations',
  'manage_pricing': 'management_operations',
  'export_data': 'service_operations',
  'delete_inventory': 'management_operations'
};

/**
 * Types d'imports autorisés selon le type d'utilisateur (système simplifié)
 */
const IMPORT_TYPE_PERMISSIONS = {
  'super_admin': ['MENU_RESTAURANT', 'CARTE_BAR', 'INVENTAIRE_INGREDIENTS'],
  'admin_chaine': ['MENU_RESTAURANT', 'CARTE_BAR', 'INVENTAIRE_INGREDIENTS'],
  'admin_complexe': ['MENU_RESTAURANT', 'CARTE_BAR', 'INVENTAIRE_INGREDIENTS'],
  'employe': {
    'gerant_services': ['MENU_RESTAURANT', 'CARTE_BAR', 'INVENTAIRE_INGREDIENTS'],
    'cuisine': ['INVENTAIRE_INGREDIENTS'],
    'serveuse': [],
    'reception': [],
    'gerant_piscine': []
  }
};

/**
 * Middleware de vérification des permissions d'inventaire (système simplifié)
 */
const checkInventairePermissions = (requiredPermissions) => {
  return async (req, res, next) => {
    try {
      const user = req.user;

      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Authentification requise'
        });
      }

      // Les admins ont tous les droits
      if (['super_admin', 'admin_chaine', 'admin_complexe'].includes(user.role)) {
        return next();
      }

      // Pour les employés, vérifier selon le nouveau système
      if (user.role === 'employe') {
        // Normaliser les permissions en tableau
        const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];

        // Mapper les anciennes permissions vers les nouvelles
        const simplifiedPermissions = permissions.map(perm =>
          INVENTAIRE_PERMISSIONS_MAPPING[perm] || perm
        );

        // Vérifier si l'employé a au moins une des permissions requises
        const employeePermissions = getEmployeePermissionsByType(user.type_employe);
        const hasPermission = simplifiedPermissions.some(perm =>
          employeePermissions.includes(perm)
        );

        if (!hasPermission) {
          logger.warn('Permission denied for inventory action', {
            userId: user.id,
            role: user.role,
            employeeType: user.type_employe,
            requiredPermissions: simplifiedPermissions,
            employeePermissions,
            action: req.method + ' ' + req.route.path
          });

          return res.status(403).json({
            success: false,
            message: 'Permissions insuffisantes pour cette action d\'inventaire',
            required_permissions: simplifiedPermissions,
            employee_type: user.type_employe
          });
        }
      }

      next();

    } catch (error) {
      logger.error('Error checking inventory permissions:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification des permissions'
      });
    }
  };
};

/**
 * Middleware de vérification des permissions d'import
 */
const checkImportPermissions = async (req, res, next) => {
  try {
    const user = req.user;
    const { typeImport } = req.body;

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Authentification requise'
      });
    }

    // Vérification de la permission générale d'import
    const hasImportPermission = await checkUserPermission(user.employe_id, INVENTAIRE_PERMISSIONS.IMPORT_DATA);
    
    if (!hasImportPermission && user.role !== 'super_admin') {
      return res.status(403).json({
        success: false,
        message: 'Permission d\'import non accordée'
      });
    }

    // Vérification du type d'import autorisé selon le type d'utilisateur (système simplifié)
    let allowedTypes = [];

    if (['super_admin', 'admin_chaine', 'admin_complexe'].includes(user.role)) {
      allowedTypes = IMPORT_TYPE_PERMISSIONS[user.role] || [];
    } else if (user.role === 'employe') {
      allowedTypes = IMPORT_TYPE_PERMISSIONS.employe[user.type_employe] || [];
    }

    if (!allowedTypes.includes(typeImport)) {
      logger.warn('Import type not allowed for user', {
        userId: user.id,
        role: user.role,
        employeeType: user.type_employe,
        requestedType: typeImport,
        allowedTypes
      });

      return res.status(403).json({
        success: false,
        message: `Type d'import '${typeImport}' non autorisé`,
        allowed_types: allowedTypes,
        user_type: user.role,
        employee_type: user.type_employe
      });
    }

    next();

  } catch (error) {
    logger.error('Error checking import permissions:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la vérification des permissions d\'import'
    });
  }
};

/**
 * Middleware de vérification d'accès aux services
 */
const checkServiceAccess = async (req, res, next) => {
  try {
    const user = req.user;
    const serviceId = req.params.serviceId || req.body.serviceId || req.query.serviceId;

    if (!serviceId) {
      return next(); // Pas de service spécifique, on continue
    }

    // Super admin et admin chaine ont accès à tous les services
    if (user.role === 'super_admin' || user.role === 'admin_chaine') {
      return next();
    }

    // Vérification que le service appartient au complexe de l'utilisateur
    const serviceCheck = await db.query(
      'SELECT complexe_id, type_service FROM "ServicesComplexe" WHERE service_id = $1',
      [serviceId]
    );

    if (serviceCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Service non trouvé'
      });
    }

    const service = serviceCheck.rows[0];

    // Vérification de l'accès au complexe
    if (service.complexe_id !== user.complexe_id) {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé à ce service'
      });
    }

    // Vérification de l'accès au type de service selon le rôle
    const hasServiceAccess = await checkServiceTypeAccess(user, service.type_service);
    
    if (!hasServiceAccess) {
      return res.status(403).json({
        success: false,
        message: `Accès non autorisé au service de type '${service.type_service}'`
      });
    }

    next();

  } catch (error) {
    logger.error('Error checking service access:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la vérification d\'accès au service'
    });
  }
};

/**
 * Middleware de validation des données d'inventaire
 */
const validateInventaireData = (req, res, next) => {
  try {
    const { body } = req;
    const errors = [];

    // Validation selon l'action
    if (req.method === 'POST' && req.route.path.includes('/ingredients')) {
      // Validation pour création d'ingrédient
      if (!body.nom || body.nom.trim().length === 0) {
        errors.push('Le nom de l\'ingrédient est requis');
      }

      if (!body.uniteMesure) {
        errors.push('L\'unité de mesure est requise');
      }

      if (!body.categorie) {
        errors.push('La catégorie est requise');
      }

      if (body.prixUnitaireMoyen !== undefined && body.prixUnitaireMoyen < 0) {
        errors.push('Le prix unitaire ne peut pas être négatif');
      }
    }

    if (req.method === 'POST' && req.route.path.includes('/recettes')) {
      // Validation pour création de recette
      if (!body.recetteData || !body.recetteData.nomRecette) {
        errors.push('Le nom de la recette est requis');
      }

      if (!body.recetteData.produitId) {
        errors.push('L\'ID du produit est requis');
      }

      if (!body.recetteData.serviceId) {
        errors.push('L\'ID du service est requis');
      }
    }

    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Données invalides',
        errors
      });
    }

    next();

  } catch (error) {
    logger.error('Error validating inventory data:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la validation des données'
    });
  }
};

/**
 * Middleware de logging des actions d'inventaire
 */
const logInventaireAction = (action) => {
  return (req, res, next) => {
    const startTime = Date.now();
    
    // Log de l'action
    logger.info('Inventory action started', {
      action,
      userId: req.user?.employe_id,
      method: req.method,
      path: req.path,
      params: req.params,
      query: req.query,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });

    // Log de la fin de l'action
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      
      logger.info('Inventory action completed', {
        action,
        userId: req.user?.employe_id,
        statusCode: res.statusCode,
        duration,
        success: res.statusCode < 400
      });
    });

    next();
  };
};

/**
 * Fonctions utilitaires
 */

/**
 * Obtenir les permissions d'un employé selon son type (système simplifié)
 */
function getEmployeePermissionsByType(employeeType) {
  const typePermissions = {
    'reception': ['reception_operations'],
    'gerant_piscine': ['piscine_operations'],
    'serveuse': ['service_operations'],
    'gerant_services': ['service_operations', 'management_operations'],
    'cuisine': ['kitchen_operations']
  };

  return typePermissions[employeeType] || [];
}

/**
 * Vérification d'accès au type de service (système simplifié)
 */
function checkServiceTypeAccess(user, serviceType) {
  // Les admins ont accès à tous les services
  if (['super_admin', 'admin_chaine', 'admin_complexe'].includes(user.role)) {
    return true;
  }

  // Pour les employés, vérifier selon le type et les services autorisés
  if (user.role === 'employe') {
    const serviceAccessRules = {
      'gerant_services': ['Restaurant', 'Bar'],
      'cuisine': ['Restaurant'],
      'serveuse': ['Restaurant', 'Bar'],
      'gerant_piscine': ['Piscine'],
      'reception': []
    };

    const allowedServices = serviceAccessRules[user.type_employe] || [];
    return allowedServices.includes(serviceType);
  }

  return false;
}

module.exports = {
  INVENTAIRE_PERMISSIONS_MAPPING,
  IMPORT_TYPE_PERMISSIONS,
  checkInventairePermissions,
  checkImportPermissions,
  checkServiceAccess,
  validateInventaireData,
  logInventaireAction,
  getEmployeePermissionsByType,
  checkServiceTypeAccess
};
