const logger = require('../logger');

// Validation des données de paiement
const validatePaiement = (request, response, next) => {
  const { montant, mode_paiement, reference_paiement } = request.body;

  if (!montant || montant <= 0) {
    return response.status(400).json({
      success: false,
      message: 'Le montant du paiement est invalide'
    });
  }

  const modesPaiementValides = ['mixx', 'flooz', 'especes', 'carte', 'virement', 'cheque'];
  if (!mode_paiement || !modesPaiementValides.includes(mode_paiement.toLowerCase())) {
    return response.status(400).json({
      success: false,
      message: 'Mode de paiement invalide',
      modes_valides: modesPaiementValides
    });
  }

  if (!reference_paiement) {
    return response.status(400).json({
      success: false,
      message: 'La référence du paiement est requise'
    });
  }

  next();
};

// Vérification des permissions
const checkPermissions = (request, response, next) => {
  const { role } = request.user;

  if (!['ADMIN', 'RECEPTION', 'CAISSE'].includes(role)) {
    return response.status(403).json({
      success: false,
      message: 'Permission refusée'
    });
  }

  next();
};

// Journalisation des actions sur les paiements
const logPaiementAction = (action) => {
  return (request, response, next) => {
    const { user } = request;
    const paiementId = request.params.id;

    logger.info('Action paiement', {
      action,
      paiement_id: paiementId,
      utilisateur_id: user.id,
      role: user.role
    });

    next();
  };
};

module.exports = {
  validatePaiement,
  checkPermissions,
  logPaiementAction
}; 