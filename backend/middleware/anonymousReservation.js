const rateLimit = require('express-rate-limit');
const AnonymousValidationService = require('../services/anonymousValidation.service');
const AnonymousReservationService = require('../services/anonymousReservation.service');
const logger = require('../logger');
const db = require('../db');

/**
 * Middleware spécialisé pour les réservations anonymes
 * Gestion de la sécurité, validation et logging
 */

/**
 * Rate limiting adaptatif pour les réservations anonymes
 * Différents limites selon l'action
 */
const createRateLimiter = (action, maxRequests, windowMinutes) => {
  return rateLimit({
    windowMs: windowMinutes * 60 * 1000,
    max: maxRequests,
    message: {
      success: false,
      message: `Trop de tentatives de ${action}. Veuillez patienter ${windowMinutes} minutes.`,
      code: 'RATE_LIMIT_EXCEEDED',
      retryAfter: windowMinutes * 60
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
      // Utiliser l'IP + User-Agent pour une identification plus précise
      return `${req.ip}-${req.get('User-Agent') || 'unknown'}`.substring(0, 100);
    },
    handler: (req, res) => {
      logger.warn('Rate limit dépassé', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        action: action,
        url: req.originalUrl
      });
      
      res.status(429).json({
        success: false,
        message: `Trop de tentatives de ${action}. Veuillez patienter ${windowMinutes} minutes.`,
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: windowMinutes * 60
      });
    }
  });
};

// Rate limiters spécifiques par action
const rateLimiters = {
  creation: createRateLimiter('création de réservation', 5, 60), // 5 créations par heure
  consultation: createRateLimiter('consultation', 50, 60), // 50 consultations par heure
  modification: createRateLimiter('modification', 10, 60), // 10 modifications par heure
  annulation: createRateLimiter('annulation', 3, 60), // 3 annulations par heure
  validation: createRateLimiter('validation de code', 20, 15) // 20 validations par 15 minutes
};

/**
 * Middleware de rate limiting pour la création de réservations
 */
const rateLimitCreation = rateLimiters.creation;

/**
 * Middleware de rate limiting pour la consultation
 */
const rateLimitConsultation = rateLimiters.consultation;

/**
 * Middleware de rate limiting pour la modification
 */
const rateLimitModification = rateLimiters.modification;

/**
 * Middleware de rate limiting pour l'annulation
 */
const rateLimitAnnulation = rateLimiters.annulation;

/**
 * Middleware de rate limiting pour la validation de codes
 */
const rateLimitValidation = rateLimiters.validation;

/**
 * Middleware de validation des paramètres de réservation anonyme
 */
const validateAnonymousReservation = (req, res, next) => {
  try {
    // Nettoyer les données d'entrée
    req.body = AnonymousValidationService.sanitizeInput(req.body);
    
    // Valider les paramètres
    const validation = AnonymousValidationService.validateAnonymousReservationParams(req.body);
    
    if (!validation.valid) {
      logger.warn('Validation échouée pour réservation anonyme', {
        ip: req.ip,
        errors: validation.errors,
        body: { ...req.body, client_info: 'MASKED' }
      });
      
      return res.status(400).json({
        success: false,
        message: 'Données de réservation invalides',
        errors: validation.errors,
        code: 'VALIDATION_FAILED'
      });
    }
    
    // Ajouter les données validées à la requête
    req.validatedData = req.body;
    next();
    
  } catch (error) {
    logger.error('Erreur middleware validation réservation anonyme:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la validation des données',
      code: 'VALIDATION_ERROR'
    });
  }
};

/**
 * Middleware de validation du code d'accès
 */
const validateAccessCode = async (req, res, next) => {
  try {
    const codeAcces = req.params.codeAcces || req.body.code_acces;
    
    if (!codeAcces) {
      return res.status(400).json({
        success: false,
        message: 'Code d\'accès requis',
        code: 'MISSING_ACCESS_CODE'
      });
    }
    
    // Valider le format du code
    const formatValidation = AnonymousValidationService.validateAccessCode(codeAcces);
    if (!formatValidation.valid) {
      logger.warn('Format de code d\'accès invalide', {
        ip: req.ip,
        code_partial: codeAcces.substring(0, 8) + '***',
        error: formatValidation.error
      });
      
      return res.status(400).json({
        success: false,
        message: formatValidation.error,
        code: 'INVALID_ACCESS_CODE_FORMAT'
      });
    }
    
    // Vérifier la sécurité (tentatives d'accès)
    const securityCheck = await checkAccessSecurity(codeAcces, req.ip);
    if (!securityCheck.allowed) {
      return res.status(403).json({
        success: false,
        message: securityCheck.message,
        code: 'ACCESS_BLOCKED',
        blockedUntil: securityCheck.blockedUntil
      });
    }
    
    // Ajouter le code validé à la requête
    req.accessCode = formatValidation.cleaned;
    next();
    
  } catch (error) {
    logger.error('Erreur middleware validation code d\'accès:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la validation du code d\'accès',
      code: 'ACCESS_CODE_VALIDATION_ERROR'
    });
  }
};

/**
 * Middleware de logging des accès anonymes
 */
const logAnonymousAccess = (action) => {
  return async (req, res, next) => {
    const startTime = Date.now();
    
    // Capturer la réponse originale
    const originalSend = res.send;
    let responseData = null;
    
    res.send = function(data) {
      responseData = data;
      originalSend.call(this, data);
    };
    
    // Continuer avec la requête
    res.on('finish', async () => {
      try {
        const duration = Date.now() - startTime;
        const codeAcces = req.accessCode || req.params.codeAcces || req.body.code_acces || 'UNKNOWN';
        
        let success = res.statusCode >= 200 && res.statusCode < 400;
        let parsedResponse = null;
        
        try {
          parsedResponse = typeof responseData === 'string' ? JSON.parse(responseData) : responseData;
          if (parsedResponse && typeof parsedResponse.success === 'boolean') {
            success = parsedResponse.success;
          }
        } catch (e) {
          // Ignore parsing errors
        }
        
        // Logger l'accès
        await AnonymousReservationService.logAnonymousAccess(
          codeAcces,
          action + (success ? '_REUSSIE' : '_ECHEC'),
          {
            status_code: res.statusCode,
            duration_ms: duration,
            url: req.originalUrl,
            method: req.method,
            response_size: responseData ? responseData.length : 0,
            error_message: !success && parsedResponse ? parsedResponse.message : null
          },
          req
        );
        
        // Mettre à jour les statistiques de sécurité
        await updateAccessSecurity(codeAcces, req.ip, success);
        
      } catch (error) {
        logger.error('Erreur logging accès anonyme:', error);
      }
    });
    
    next();
  };
};

/**
 * Middleware de vérification de la disponibilité du service
 */
const checkServiceAvailability = async (req, res, next) => {
  try {
    const complexeId = req.body.complexe_id || req.params.complexeId;
    
    if (!complexeId) {
      return res.status(400).json({
        success: false,
        message: 'ID de complexe requis',
        code: 'MISSING_COMPLEXE_ID'
      });
    }
    
    // Vérifier que le service est activé pour ce complexe
    const AnonymousConfigService = require('../services/anonymousConfig.service');
    const configResult = await AnonymousConfigService.getConfigurationComplexe(parseInt(complexeId));
    
    if (!configResult.success || !configResult.data.actif) {
      logger.warn('Tentative d\'accès à un service désactivé', {
        complexe_id: complexeId,
        ip: req.ip
      });
      
      return res.status(403).json({
        success: false,
        message: 'Les réservations anonymes ne sont pas disponibles pour ce complexe',
        code: 'SERVICE_UNAVAILABLE'
      });
    }
    
    // Ajouter la configuration à la requête
    req.anonymousConfig = configResult.data;
    next();
    
  } catch (error) {
    logger.error('Erreur vérification disponibilité service:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la vérification de la disponibilité du service',
      code: 'SERVICE_CHECK_ERROR'
    });
  }
};

/**
 * Middleware de protection CSRF pour les réservations anonymes
 */
const csrfProtection = (req, res, next) => {
  // Pour les réservations anonymes, on utilise une protection basée sur les headers
  const origin = req.get('Origin');
  const referer = req.get('Referer');
  const userAgent = req.get('User-Agent');
  
  // Vérifier que la requête vient d'un navigateur légitime
  if (!userAgent || userAgent.length < 10) {
    logger.warn('Tentative d\'accès avec User-Agent suspect', {
      ip: req.ip,
      userAgent: userAgent
    });
    
    return res.status(403).json({
      success: false,
      message: 'Requête non autorisée',
      code: 'INVALID_REQUEST'
    });
  }
  
  // Pour les requêtes POST/PUT/PATCH/DELETE, vérifier l'origine
  if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method)) {
    const allowedOrigins = process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : [];
    
    if (allowedOrigins.length > 0 && origin && !allowedOrigins.includes(origin)) {
      logger.warn('Tentative d\'accès depuis une origine non autorisée', {
        ip: req.ip,
        origin: origin,
        allowedOrigins: allowedOrigins
      });
      
      return res.status(403).json({
        success: false,
        message: 'Origine non autorisée',
        code: 'FORBIDDEN_ORIGIN'
      });
    }
  }
  
  next();
};

/**
 * Middleware de validation des paramètres de modification
 */
const validateUpdateParams = (req, res, next) => {
  try {
    // Nettoyer les données
    req.body = AnonymousValidationService.sanitizeInput(req.body);
    
    // Valider les paramètres de modification
    const validation = AnonymousValidationService.validateUpdateParams(req.body);
    
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        message: 'Paramètres de modification invalides',
        errors: validation.errors,
        code: 'UPDATE_VALIDATION_FAILED'
      });
    }
    
    req.validatedUpdateData = req.body;
    next();
    
  } catch (error) {
    logger.error('Erreur validation paramètres modification:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la validation des paramètres',
      code: 'UPDATE_VALIDATION_ERROR'
    });
  }
};

/**
 * Vérifier la sécurité d'accès pour un code et une IP
 */
async function checkAccessSecurity(codeAcces, ip) {
  try {
    const query = `
      SELECT * FROM "SecuriteAccesAnonymes"
      WHERE (adresse_ip = $1 OR code_acces_anonyme = $2)
      AND bloque_jusqu > CURRENT_TIMESTAMP
    `;
    
    const result = await db.query(query, [ip, codeAcces]);
    
    if (result.rows.length > 0) {
      const blocage = result.rows[0];
      return {
        allowed: false,
        message: `Accès bloqué jusqu'à ${blocage.bloque_jusqu}. Raison: ${blocage.raison_blocage}`,
        blockedUntil: blocage.bloque_jusqu
      };
    }
    
    return { allowed: true };
    
  } catch (error) {
    logger.error('Erreur vérification sécurité accès:', error);
    return { allowed: true }; // En cas d'erreur, on autorise l'accès
  }
}

/**
 * Mettre à jour les statistiques de sécurité
 */
async function updateAccessSecurity(codeAcces, ip, success) {
  try {
    if (success) {
      // Réinitialiser les tentatives d'échec en cas de succès
      await db.query(`
        UPDATE "SecuriteAccesAnonymes"
        SET tentatives_echec = 0, updated_at = CURRENT_TIMESTAMP
        WHERE adresse_ip = $1 OR code_acces_anonyme = $2
      `, [ip, codeAcces]);
    } else {
      // Incrémenter les tentatives d'échec
      const upsertQuery = `
        INSERT INTO "SecuriteAccesAnonymes" (adresse_ip, code_acces_anonyme, tentatives_echec, derniere_tentative)
        VALUES ($1, $2, 1, CURRENT_TIMESTAMP)
        ON CONFLICT (adresse_ip)
        DO UPDATE SET
          tentatives_echec = "SecuriteAccesAnonymes".tentatives_echec + 1,
          derniere_tentative = CURRENT_TIMESTAMP,
          code_acces_anonyme = COALESCE("SecuriteAccesAnonymes".code_acces_anonyme, $2),
          updated_at = CURRENT_TIMESTAMP
      `;
      
      await db.query(upsertQuery, [ip, codeAcces]);
      
      // Vérifier si on doit bloquer l'accès
      const checkQuery = `
        SELECT tentatives_echec FROM "SecuriteAccesAnonymes"
        WHERE adresse_ip = $1
      `;
      
      const result = await db.query(checkQuery, [ip]);
      
      if (result.rows.length > 0 && result.rows[0].tentatives_echec >= 5) {
        // Bloquer l'accès pour 30 minutes
        await db.query(`
          UPDATE "SecuriteAccesAnonymes"
          SET 
            bloque_jusqu = CURRENT_TIMESTAMP + INTERVAL '30 minutes',
            raison_blocage = 'Trop de tentatives d''accès échouées',
            updated_at = CURRENT_TIMESTAMP
          WHERE adresse_ip = $1
        `, [ip]);
        
        logger.warn('IP bloquée pour tentatives d\'accès répétées', {
          ip: ip,
          code_acces: codeAcces.substring(0, 8) + '***'
        });
      }
    }
    
  } catch (error) {
    logger.error('Erreur mise à jour sécurité accès:', error);
  }
}

module.exports = {
  // Rate limiting
  rateLimitCreation,
  rateLimitConsultation,
  rateLimitModification,
  rateLimitAnnulation,
  rateLimitValidation,
  
  // Validation
  validateAnonymousReservation,
  validateAccessCode,
  validateUpdateParams,
  
  // Logging et sécurité
  logAnonymousAccess,
  checkServiceAvailability,
  csrfProtection,
  
  // Utilitaires
  checkAccessSecurity,
  updateAccessSecurity
};
