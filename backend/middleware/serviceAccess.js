const PermissionService = require('../services/permission.service');
const logger = require('../logger');

/**
 * Middleware pour vérifier l'accès aux interfaces de services spécifiques
 * @param {string} serviceType - Type de service (Restaurant, Bar, Piscine)
 * @returns {Function} Middleware function
 */
const checkServiceAccess = (serviceType) => {
  return async (req, res, next) => {
    try {
      const user = req.user;
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Authentification requise'
        });
      }

      // Super admin a accès à tout
      if (user.role === 'super_admin') {
        return next();
      }

      // Déterminer la permission requise selon le type de service
      let requiredPermission;
      switch (serviceType.toLowerCase()) {
        case 'restaurant':
          requiredPermission = 'access_restaurant_interface';
          break;
        case 'bar':
          requiredPermission = 'access_bar_interface';
          break;
        case 'piscine':
          requiredPermission = 'access_piscine_interface';
          break;
        default:
          return res.status(400).json({
            success: false,
            message: 'Type de service invalide'
          });
      }

      // Vérifier si l'utilisateur a la permission
      const hasPermission = await PermissionService.hasPermission(
        user.id,
        user.role,
        requiredPermission,
        user.complexe_id
      );

      if (!hasPermission) {
        logger.warn('Accès refusé à l\'interface de service', {
          userId: user.id,
          userRole: user.role,
          serviceType,
          requiredPermission
        });

        return res.status(403).json({
          success: false,
          message: `Accès refusé à l'interface ${serviceType}. Permission requise: ${requiredPermission}`
        });
      }

      // Ajouter le type de service à la requête pour usage ultérieur
      req.serviceType = serviceType;
      next();

    } catch (error) {
      logger.error('Erreur lors de la vérification d\'accès au service:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur interne du serveur'
      });
    }
  };
};

/**
 * Middleware pour vérifier l'accès aux opérations sur les services
 * @param {string} serviceType - Type de service (Restaurant, Bar, Piscine)
 * @returns {Function} Middleware function
 */
const checkServiceOperation = (serviceType) => {
  return async (req, res, next) => {
    try {
      const user = req.user;
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Authentification requise'
        });
      }

      // Super admin a accès à tout
      if (user.role === 'super_admin') {
        return next();
      }

      // Déterminer la permission requise selon le type de service
      let requiredPermission;
      switch (serviceType.toLowerCase()) {
        case 'restaurant':
          requiredPermission = 'operate_restaurant';
          break;
        case 'bar':
          requiredPermission = 'operate_bar';
          break;
        case 'piscine':
          requiredPermission = 'operate_piscine';
          break;
        default:
          return res.status(400).json({
            success: false,
            message: 'Type de service invalide'
          });
      }

      // Vérifier si l'utilisateur a la permission
      const hasPermission = await PermissionService.hasPermission(
        user.id,
        user.role,
        requiredPermission,
        user.complexe_id
      );

      if (!hasPermission) {
        logger.warn('Accès refusé aux opérations du service', {
          userId: user.id,
          userRole: user.role,
          serviceType,
          requiredPermission
        });

        return res.status(403).json({
          success: false,
          message: `Accès refusé aux opérations ${serviceType}. Permission requise: ${requiredPermission}`
        });
      }

      next();

    } catch (error) {
      logger.error('Erreur lors de la vérification d\'accès aux opérations du service:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur interne du serveur'
      });
    }
  };
};

/**
 * Middleware pour vérifier l'accès à plusieurs types de services
 * @param {string[]} serviceTypes - Types de services autorisés
 * @returns {Function} Middleware function
 */
const checkMultipleServiceAccess = (serviceTypes) => {
  return async (req, res, next) => {
    try {
      const user = req.user;
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Authentification requise'
        });
      }

      // Super admin a accès à tout
      if (user.role === 'super_admin') {
        return next();
      }

      // Vérifier si l'utilisateur a accès à au moins un des services
      const permissionChecks = serviceTypes.map(async (serviceType) => {
        let requiredPermission;
        switch (serviceType.toLowerCase()) {
          case 'restaurant':
            requiredPermission = 'access_restaurant_interface';
            break;
          case 'bar':
            requiredPermission = 'access_bar_interface';
            break;
          case 'piscine':
            requiredPermission = 'access_piscine_interface';
            break;
          default:
            return false;
        }

        return await PermissionService.hasPermission(
          user.id,
          user.role,
          requiredPermission,
          user.complexe_id
        );
      });

      const results = await Promise.all(permissionChecks);
      const hasAnyAccess = results.some(result => result === true);

      if (!hasAnyAccess) {
        logger.warn('Accès refusé à tous les services demandés', {
          userId: user.id,
          userRole: user.role,
          serviceTypes
        });

        return res.status(403).json({
          success: false,
          message: `Accès refusé. Aucune permission pour les services: ${serviceTypes.join(', ')}`
        });
      }

      // Ajouter les types de services autorisés à la requête
      const authorizedServices = serviceTypes.filter((_, index) => results[index]);
      req.authorizedServices = authorizedServices;
      
      next();

    } catch (error) {
      logger.error('Erreur lors de la vérification d\'accès aux services multiples:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur interne du serveur'
      });
    }
  };
};

module.exports = {
  checkServiceAccess,
  checkServiceOperation,
  checkMultipleServiceAccess
};
