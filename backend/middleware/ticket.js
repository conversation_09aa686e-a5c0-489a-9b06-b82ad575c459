const logger = require('../logger');

// Validation de la génération de ticket
const validateTicketGeneration = (request, response, next) => {
  const { reservation_id } = request.body;

  if (!reservation_id) {
    return response.status(400).json({
      success: false,
      message: 'L\'ID de réservation est requis'
    });
  }

  next();
};

// Validation du statut de ticket
const validateTicketStatus = (request, response, next) => {
  const { statut } = request.body;
  const statutsValides = ['ACTIF', 'UTILISE', 'ANNULE', 'EXPIRE'];

  if (!statut) {
    return response.status(400).json({
      success: false,
      message: 'Le statut est requis'
    });
  }

  if (!statutsValides.includes(statut)) {
    return response.status(400).json({
      success: false,
      message: 'Statut invalide',
      statuts_valides: statutsValides
    });
  }

  next();
};

// Vérification des permissions
const checkPermissions = (request, response, next) => {
  const { role } = request.user;

  if (!['ADMIN', 'RECEPTION'].includes(role)) {
    return response.status(403).json({
      success: false,
      message: 'Permission refusée'
    });
  }

  next();
};

// Validation de la création de ticket de service
const validateServiceTicketCreation = (request, response, next) => {
  const {
    service_id,
    pos_id,
    session_id,
    nom_client,
    nombre_personnes,
    mode_paiement,
    prix_total
  } = request.body;

  // Vérifications obligatoires
  if (!service_id) {
    return response.status(400).json({
      success: false,
      message: 'L\'ID du service est requis'
    });
  }

  if (!pos_id) {
    return response.status(400).json({
      success: false,
      message: 'L\'ID du point de vente est requis'
    });
  }

  if (!session_id) {
    return response.status(400).json({
      success: false,
      message: 'L\'ID de la session de caisse est requis'
    });
  }

  if (!nom_client || nom_client.trim() === '') {
    return response.status(400).json({
      success: false,
      message: 'Le nom du client est requis'
    });
  }

  if (!nombre_personnes || nombre_personnes < 1) {
    return response.status(400).json({
      success: false,
      message: 'Le nombre de personnes doit être supérieur à 0'
    });
  }

  if (!mode_paiement) {
    return response.status(400).json({
      success: false,
      message: 'Le mode de paiement est requis'
    });
  }

  const modesValides = ['Espèces', 'Carte', 'Virement', 'Chèque'];
  if (!modesValides.includes(mode_paiement)) {
    return response.status(400).json({
      success: false,
      message: 'Mode de paiement invalide',
      modes_valides: modesValides
    });
  }

  if (!prix_total || prix_total <= 0) {
    return response.status(400).json({
      success: false,
      message: 'Le prix total doit être supérieur à 0'
    });
  }

  // Validation optionnelle de la durée pour les services temporaires
  if (request.body.duree_heures !== undefined && request.body.duree_heures <= 0) {
    return response.status(400).json({
      success: false,
      message: 'La durée doit être supérieure à 0'
    });
  }

  next();
};

// Journalisation des actions sur les tickets
const logTicketAction = (action) => {
  return (request, response, next) => {
    const { user } = request;
    const ticketId = request.params.id || request.params.numero || request.params.ticketId;

    logger.info('Action ticket', {
      action,
      ticket_id: ticketId,
      utilisateur_id: user.id,
      role: user.role
    });

    next();
  };
};

module.exports = {
  validateTicketGeneration,
  validateTicketStatus,
  validateServiceTicketCreation,
  checkPermissions,
  logTicketAction
};