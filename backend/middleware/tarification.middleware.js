const logger = require('../logger');

/**
 * Middleware de validation pour les données de tarification
 */
const validateTarificationData = (req, res, next) => {
  try {
    const tarificationData = req.body;

    // Vérifier que les données sont présentes
    if (!tarificationData || typeof tarificationData !== 'object') {
      return res.status(400).json({
        success: false,
        message: 'Les données de tarification sont requises et doivent être un objet JSON valide'
      });
    }

    // Vérifier qu'il n'y a pas de données vides
    if (Object.keys(tarificationData).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Les données de tarification ne peuvent pas être vides'
      });
    }

    // Validation basique des types de données
    const validatePriceValue = (value, fieldName) => {
      if (typeof value === 'number') {
        if (value < 0) {
          throw new Error(`Le prix pour "${fieldName}" ne peut pas être négatif`);
        }
        if (!isFinite(value)) {
          throw new Error(`Le prix pour "${fieldName}" doit être un nombre valide`);
        }
      } else if (typeof value === 'object' && value !== null) {
        // Validation récursive pour les objets imbriqués
        for (const [subKey, subValue] of Object.entries(value)) {
          validatePriceValue(subValue, `${fieldName}.${subKey}`);
        }
      } else if (typeof value !== 'string' && typeof value !== 'boolean' && value !== null) {
        throw new Error(`Le type de données pour "${fieldName}" n'est pas valide`);
      }
    };

    // Valider tous les champs
    for (const [key, value] of Object.entries(tarificationData)) {
      validatePriceValue(value, key);
    }

    logger.info('Validation tarification réussie', { 
      serviceId: req.params.id,
      fieldsCount: Object.keys(tarificationData).length 
    });

    next();
  } catch (error) {
    logger.error('Erreur validation tarification', error);
    return res.status(400).json({
      success: false,
      message: error.message || 'Erreur lors de la validation des données de tarification'
    });
  }
};

/**
 * Middleware pour logger les actions de tarification
 */
const logTarificationAction = (action) => {
  return (req, res, next) => {
    const startTime = Date.now();
    
    // Log de l'action
    logger.info(`Action tarification: ${action}`, {
      serviceId: req.params.id,
      type: req.params.type,
      userId: req.user?.id,
      timestamp: new Date().toISOString()
    });

    // Intercepter la réponse pour logger le résultat
    const originalSend = res.send;
    res.send = function(data) {
      const duration = Date.now() - startTime;
      const responseData = typeof data === 'string' ? JSON.parse(data) : data;
      
      logger.info(`Action tarification terminée: ${action}`, {
        serviceId: req.params.id,
        success: responseData.success,
        duration: `${duration}ms`,
        statusCode: res.statusCode
      });

      originalSend.call(this, data);
    };

    next();
  };
};

/**
 * Middleware pour vérifier les permissions de modification des tarifs
 */
const checkTarificationPermissions = (req, res, next) => {
  try {
    // Pour l'instant, on vérifie juste que l'utilisateur est authentifié
    // Dans une version future, on pourrait ajouter des vérifications de rôles spécifiques
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentification requise pour modifier les tarifications'
      });
    }

    // Vérifier si l'utilisateur a les permissions pour modifier les tarifs
    // Cette logique peut être étendue selon les besoins
    const userPermissions = req.user.permissions || [];
    const hasPermission = userPermissions.includes('MANAGE_SERVICES') || 
                         userPermissions.includes('ADMIN') ||
                         req.user.role === 'admin';

    if (!hasPermission) {
      logger.warn('Tentative de modification tarification sans permission', {
        userId: req.user.id,
        serviceId: req.params.id
      });
      
      return res.status(403).json({
        success: false,
        message: 'Permissions insuffisantes pour modifier les tarifications'
      });
    }

    next();
  } catch (error) {
    logger.error('Erreur vérification permissions tarification', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la vérification des permissions'
    });
  }
};

module.exports = {
  validateTarificationData,
  logTarificationAction,
  checkTarificationPermissions
};
