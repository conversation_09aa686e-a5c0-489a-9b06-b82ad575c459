// eslint-disable-next-line import/no-extraneous-dependencies
const jwt = require('jsonwebtoken');
const TokenService = require('../services/token.service');
const logger = require('../logger');
const { JWT_SECRET } = require('../config/jwt.config');

// Middleware to verify JWT token
const verifyToken = (req, res, next) => {
  const token = req.headers['x-access-token'] || req.headers.authorization;

  if (!token) {
    return res.status(403).json({ message: 'No token provided' });
  }

  try {
    const cleanToken = token.replace('Bearer ', '');
    const decoded = TokenService.verifyToken(cleanToken);
    req.user = decoded;
    return next();
  } catch (err) {
    logger.error('Token verification failed:', err);
    return res.status(401).json({ message: 'Unauthorized' });
  }
};

// Role-based access control middleware
const checkRole = (roles) => (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  if (!roles.includes(req.user.role)) {
    return res.status(403).json({ message: 'Forbidden: Insufficient permissions' });
  }

  return next();
};

// Check if user has access to specific complexe
const checkComplexeAccess = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const complexeId = req.params.complexeId || req.body.complexe_id;

  if (req.user.role === 'super_admin') {
    return next();
  }

  if (req.user.role === 'admin_chaine' && req.user.chaine_id) {
    // Admin chaine can access all complexes in their chain
    return next();
  }

  if (req.user.role === 'admin_complexe' && req.user.complexe_id === complexeId) {
    return next();
  }

  if (req.user.role === 'employe' && req.user.complexe_id === complexeId) {
    return next();
  }

  return res.status(403).json({ message: 'Forbidden: No access to this complex' });
};

module.exports = {
  verifyToken,
  checkRole,
  checkComplexeAccess,
};
