const logger = require('../logger');

/**
 * Middleware de gestion d'erreurs spécialisé pour les opérations restaurant/bar
 * Fournit des messages d'erreur contextuels et des codes d'erreur spécifiques
 */

/**
 * Gestionnaire d'erreurs pour les opérations de commande
 */
const handleCommandeErrors = (err, req, res, next) => {
  const { complexe_id, employe_id } = req.user || {};
  const { service_id, table_id } = req.body || {};

  // Logger l'erreur avec contexte
  logger.error('[RESTAURANT] Erreur opération commande', {
    error: err.message,
    stack: err.stack,
    employe_id,
    complexe_id,
    service_id,
    table_id,
    url: req.originalUrl,
    method: req.method,
    body: req.body,
    params: req.params
  });

  // Déterminer le type d'erreur et la réponse appropriée
  let errorResponse = {
    success: false,
    message: 'Erreur lors de l\'opération de commande',
    code: 'COMMANDE_ERROR',
    timestamp: new Date().toISOString()
  };

  // Erreurs spécifiques aux commandes
  if (err.message.includes('stock insuffisant')) {
    errorResponse = {
      ...errorResponse,
      message: 'Stock insuffisant pour traiter cette commande',
      code: 'INSUFFICIENT_STOCK',
      action_requise: 'Vérifier la disponibilité des produits'
    };
    return res.status(400).json(errorResponse);
  }

  if (err.message.includes('table')) {
    errorResponse = {
      ...errorResponse,
      message: 'Problème avec la table sélectionnée',
      code: 'TABLE_ERROR',
      action_requise: 'Vérifier le statut de la table'
    };
    return res.status(400).json(errorResponse);
  }

  if (err.message.includes('session')) {
    errorResponse = {
      ...errorResponse,
      message: 'Session de caisse requise pour cette opération',
      code: 'SESSION_REQUIRED',
      action_requise: 'Ouvrir une session de caisse'
    };
    return res.status(403).json(errorResponse);
  }

  // Erreur générique
  res.status(500).json(errorResponse);
};

/**
 * Gestionnaire d'erreurs pour les opérations de stock
 */
const handleStockErrors = (err, req, res, next) => {
  const { complexe_id, employe_id } = req.user || {};

  logger.error('[STOCK] Erreur opération stock', {
    error: err.message,
    stack: err.stack,
    employe_id,
    complexe_id,
    url: req.originalUrl,
    method: req.method
  });

  let errorResponse = {
    success: false,
    message: 'Erreur lors de l\'opération de stock',
    code: 'STOCK_ERROR',
    timestamp: new Date().toISOString()
  };

  if (err.message.includes('ingredient')) {
    errorResponse = {
      ...errorResponse,
      message: 'Problème avec les ingrédients',
      code: 'INGREDIENT_ERROR',
      action_requise: 'Vérifier la configuration des recettes'
    };
    return res.status(400).json(errorResponse);
  }

  if (err.message.includes('recette')) {
    errorResponse = {
      ...errorResponse,
      message: 'Recette non trouvée ou incomplète',
      code: 'RECIPE_ERROR',
      action_requise: 'Configurer la recette du produit'
    };
    return res.status(404).json(errorResponse);
  }

  res.status(500).json(errorResponse);
};

/**
 * Gestionnaire d'erreurs pour les opérations de table
 */
const handleTableErrors = (err, req, res, next) => {
  const { complexe_id, employe_id } = req.user || {};
  const { id: table_id } = req.params || {};

  logger.error('[TABLE] Erreur opération table', {
    error: err.message,
    stack: err.stack,
    employe_id,
    complexe_id,
    table_id,
    url: req.originalUrl,
    method: req.method
  });

  let errorResponse = {
    success: false,
    message: 'Erreur lors de l\'opération de table',
    code: 'TABLE_ERROR',
    timestamp: new Date().toISOString()
  };

  if (err.message.includes('occupée')) {
    errorResponse = {
      ...errorResponse,
      message: 'Table déjà occupée',
      code: 'TABLE_OCCUPIED',
      action_requise: 'Choisir une autre table'
    };
    return res.status(409).json(errorResponse);
  }

  if (err.message.includes('hors service')) {
    errorResponse = {
      ...errorResponse,
      message: 'Table hors service',
      code: 'TABLE_OUT_OF_SERVICE',
      action_requise: 'Choisir une table disponible'
    };
    return res.status(400).json(errorResponse);
  }

  res.status(500).json(errorResponse);
};

/**
 * Gestionnaire d'erreurs pour les validations
 */
const handleValidationErrors = (err, req, res, next) => {
  const { complexe_id, employe_id } = req.user || {};

  logger.error('[VALIDATION] Erreur validation', {
    error: err.message,
    stack: err.stack,
    employe_id,
    complexe_id,
    url: req.originalUrl,
    method: req.method,
    body: req.body
  });

  let errorResponse = {
    success: false,
    message: 'Erreur de validation',
    code: 'VALIDATION_ERROR',
    timestamp: new Date().toISOString()
  };

  // Erreurs de validation spécifiques
  if (err.message.includes('requis')) {
    errorResponse = {
      ...errorResponse,
      message: 'Champs requis manquants',
      code: 'MISSING_REQUIRED_FIELDS',
      action_requise: 'Vérifier les données envoyées'
    };
    return res.status(400).json(errorResponse);
  }

  if (err.message.includes('permission')) {
    errorResponse = {
      ...errorResponse,
      message: 'Permissions insuffisantes',
      code: 'INSUFFICIENT_PERMISSIONS',
      action_requise: 'Contacter un administrateur'
    };
    return res.status(403).json(errorResponse);
  }

  res.status(400).json(errorResponse);
};

/**
 * Gestionnaire d'erreurs global pour les opérations restaurant
 */
const globalRestaurantErrorHandler = (err, req, res, next) => {
  const { complexe_id, employe_id } = req.user || {};

  // Logger toutes les erreurs non gérées
  logger.error('[RESTAURANT] Erreur non gérée', {
    error: err.message,
    stack: err.stack,
    employe_id,
    complexe_id,
    url: req.originalUrl,
    method: req.method,
    body: req.body,
    params: req.params,
    query: req.query
  });

  // Réponse d'erreur générique
  const errorResponse = {
    success: false,
    message: 'Une erreur inattendue s\'est produite',
    code: 'INTERNAL_ERROR',
    timestamp: new Date().toISOString(),
    support_info: {
      error_id: `ERR_${Date.now()}`,
      message: 'Contactez le support technique avec cet ID d\'erreur'
    }
  };

  res.status(500).json(errorResponse);
};

/**
 * Wrapper pour capturer les erreurs async dans les middlewares
 */
const asyncErrorHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

module.exports = {
  handleCommandeErrors,
  handleStockErrors,
  handleTableErrors,
  handleValidationErrors,
  globalRestaurantErrorHandler,
  asyncErrorHandler
};
