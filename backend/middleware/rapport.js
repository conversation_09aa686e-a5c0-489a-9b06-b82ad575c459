const logger = require('../logger');

// Vérification des permissions
const checkPermissions = (request, response, next) => {
  const { role } = request.user;

  if (!['ADMIN', 'MANAGER', 'COMPTABLE'].includes(role)) {
    return response.status(403).json({
      success: false,
      message: 'Permission refusée pour accéder aux rapports'
    });
  }

  next();
};

module.exports = {
  checkPermissions
};