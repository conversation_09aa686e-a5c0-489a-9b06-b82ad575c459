const logger = require('../logger');

/**
 * Middleware de cache pour les réservations anonymes
 * Optimise les performances et réduit la charge sur la base de données
 */

// Cache en mémoire simple (en production, utiliser Redis)
const cache = new Map();
const cacheStats = {
  hits: 0,
  misses: 0,
  sets: 0,
  deletes: 0
};

/**
 * Configuration du cache
 */
const CACHE_CONFIG = {
  // Durées de vie par type de données (en secondes)
  TTL: {
    configuration: 300, // 5 minutes
    validation: 60, // 1 minute
    stats: 120, // 2 minutes
    availability: 180 // 3 minutes
  },
  
  // Taille maximale du cache
  MAX_SIZE: 1000,
  
  // Préfixes pour les clés
  PREFIXES: {
    config: 'config:',
    validation: 'validation:',
    stats: 'stats:',
    availability: 'availability:',
    rateLimit: 'rateLimit:'
  }
};

/**
 * Middleware de cache pour la configuration des complexes
 */
const cacheConfiguration = (req, res, next) => {
  const complexeId = req.params.complexeId || req.body.complexe_id;
  
  if (!complexeId) {
    return next();
  }
  
  const cacheKey = `${CACHE_CONFIG.PREFIXES.config}${complexeId}`;
  const cached = getFromCache(cacheKey);
  
  if (cached) {
    logger.debug('Configuration récupérée du cache', { complexe_id: complexeId });
    cacheStats.hits++;
    
    req.cachedConfig = cached;
    return res.json({
      success: true,
      data: cached,
      cached: true
    });
  }
  
  cacheStats.misses++;
  
  // Intercepter la réponse pour la mettre en cache
  const originalSend = res.send;
  res.send = function(data) {
    try {
      const parsedData = typeof data === 'string' ? JSON.parse(data) : data;
      
      if (parsedData.success && parsedData.data) {
        setInCache(cacheKey, parsedData.data, CACHE_CONFIG.TTL.configuration);
        logger.debug('Configuration mise en cache', { complexe_id: complexeId });
      }
    } catch (error) {
      logger.error('Erreur mise en cache configuration:', error);
    }
    
    originalSend.call(this, data);
  };
  
  next();
};

/**
 * Middleware de cache pour la validation des codes d'accès
 */
const cacheValidation = (req, res, next) => {
  const codeAcces = req.params.codeAcces || req.body.code_acces;
  
  if (!codeAcces) {
    return next();
  }
  
  const cacheKey = `${CACHE_CONFIG.PREFIXES.validation}${codeAcces}`;
  const cached = getFromCache(cacheKey);
  
  if (cached) {
    logger.debug('Validation récupérée du cache', { 
      code_acces: codeAcces.substring(0, 8) + '***' 
    });
    cacheStats.hits++;
    
    if (cached.valid) {
      req.cachedValidation = cached;
      return next();
    } else {
      return res.status(cached.statusCode || 400).json({
        success: false,
        message: cached.message,
        cached: true
      });
    }
  }
  
  cacheStats.misses++;
  
  // Intercepter la réponse pour la mettre en cache
  const originalSend = res.send;
  res.send = function(data) {
    try {
      const parsedData = typeof data === 'string' ? JSON.parse(data) : data;
      
      // Mettre en cache le résultat de validation
      const cacheData = {
        valid: parsedData.success,
        message: parsedData.message,
        statusCode: res.statusCode
      };
      
      setInCache(cacheKey, cacheData, CACHE_CONFIG.TTL.validation);
      logger.debug('Validation mise en cache', { 
        code_acces: codeAcces.substring(0, 8) + '***',
        valid: cacheData.valid
      });
    } catch (error) {
      logger.error('Erreur mise en cache validation:', error);
    }
    
    originalSend.call(this, data);
  };
  
  next();
};

/**
 * Middleware de cache pour les statistiques
 */
const cacheStatistics = (req, res, next) => {
  const complexeId = req.params.complexeId;
  const { date_debut, date_fin } = req.query;
  
  const cacheKey = `${CACHE_CONFIG.PREFIXES.stats}${complexeId}:${date_debut || 'all'}:${date_fin || 'all'}`;
  const cached = getFromCache(cacheKey);
  
  if (cached) {
    logger.debug('Statistiques récupérées du cache', { complexe_id: complexeId });
    cacheStats.hits++;
    
    return res.json({
      success: true,
      data: cached,
      cached: true
    });
  }
  
  cacheStats.misses++;
  
  // Intercepter la réponse pour la mettre en cache
  const originalSend = res.send;
  res.send = function(data) {
    try {
      const parsedData = typeof data === 'string' ? JSON.parse(data) : data;
      
      if (parsedData.success && parsedData.data) {
        setInCache(cacheKey, parsedData.data, CACHE_CONFIG.TTL.stats);
        logger.debug('Statistiques mises en cache', { complexe_id: complexeId });
      }
    } catch (error) {
      logger.error('Erreur mise en cache statistiques:', error);
    }
    
    originalSend.call(this, data);
  };
  
  next();
};

/**
 * Middleware de cache pour la disponibilité du service
 */
const cacheAvailability = (req, res, next) => {
  const complexeId = req.params.complexeId;
  
  if (!complexeId) {
    return next();
  }
  
  const cacheKey = `${CACHE_CONFIG.PREFIXES.availability}${complexeId}`;
  const cached = getFromCache(cacheKey);
  
  if (cached) {
    logger.debug('Disponibilité récupérée du cache', { complexe_id: complexeId });
    cacheStats.hits++;
    
    return res.json({
      success: true,
      ...cached,
      cached: true
    });
  }
  
  cacheStats.misses++;
  
  // Intercepter la réponse pour la mettre en cache
  const originalSend = res.send;
  res.send = function(data) {
    try {
      const parsedData = typeof data === 'string' ? JSON.parse(data) : data;
      
      if (parsedData.success) {
        const cacheData = {
          available: parsedData.available,
          configuration: parsedData.configuration
        };
        
        setInCache(cacheKey, cacheData, CACHE_CONFIG.TTL.availability);
        logger.debug('Disponibilité mise en cache', { complexe_id: complexeId });
      }
    } catch (error) {
      logger.error('Erreur mise en cache disponibilité:', error);
    }
    
    originalSend.call(this, data);
  };
  
  next();
};

/**
 * Middleware pour invalider le cache lors des modifications
 */
const invalidateCache = (patterns) => {
  return (req, res, next) => {
    // Intercepter la réponse pour invalider le cache en cas de succès
    const originalSend = res.send;
    res.send = function(data) {
      try {
        const parsedData = typeof data === 'string' ? JSON.parse(data) : data;
        
        if (parsedData.success) {
          // Invalider les clés correspondant aux patterns
          for (const pattern of patterns) {
            const keysToDelete = [];
            
            for (const [key] of cache) {
              if (key.includes(pattern)) {
                keysToDelete.push(key);
              }
            }
            
            for (const key of keysToDelete) {
              cache.delete(key);
              cacheStats.deletes++;
            }
            
            logger.debug('Cache invalidé', { 
              pattern: pattern, 
              keysDeleted: keysToDelete.length 
            });
          }
        }
      } catch (error) {
        logger.error('Erreur invalidation cache:', error);
      }
      
      originalSend.call(this, data);
    };
    
    next();
  };
};

/**
 * Fonctions utilitaires du cache
 */

function getFromCache(key) {
  const item = cache.get(key);
  
  if (!item) {
    return null;
  }
  
  // Vérifier l'expiration
  if (Date.now() > item.expiry) {
    cache.delete(key);
    cacheStats.deletes++;
    return null;
  }
  
  return item.data;
}

function setInCache(key, data, ttlSeconds) {
  // Vérifier la taille du cache
  if (cache.size >= CACHE_CONFIG.MAX_SIZE) {
    // Supprimer les entrées les plus anciennes
    const oldestKeys = Array.from(cache.keys()).slice(0, Math.floor(CACHE_CONFIG.MAX_SIZE * 0.1));
    for (const oldKey of oldestKeys) {
      cache.delete(oldKey);
      cacheStats.deletes++;
    }
  }
  
  const expiry = Date.now() + (ttlSeconds * 1000);
  cache.set(key, { data, expiry });
  cacheStats.sets++;
}

function clearCache(pattern = null) {
  if (pattern) {
    const keysToDelete = [];
    for (const [key] of cache) {
      if (key.includes(pattern)) {
        keysToDelete.push(key);
      }
    }
    
    for (const key of keysToDelete) {
      cache.delete(key);
      cacheStats.deletes++;
    }
    
    return keysToDelete.length;
  } else {
    const size = cache.size;
    cache.clear();
    cacheStats.deletes += size;
    return size;
  }
}

function getCacheStats() {
  const hitRate = cacheStats.hits + cacheStats.misses > 0 ? 
    (cacheStats.hits / (cacheStats.hits + cacheStats.misses) * 100).toFixed(2) : 0;
  
  return {
    ...cacheStats,
    hitRate: `${hitRate}%`,
    size: cache.size,
    maxSize: CACHE_CONFIG.MAX_SIZE
  };
}

/**
 * Middleware pour nettoyer le cache expiré
 */
const cleanupExpiredCache = () => {
  const now = Date.now();
  const keysToDelete = [];
  
  for (const [key, item] of cache) {
    if (now > item.expiry) {
      keysToDelete.push(key);
    }
  }
  
  for (const key of keysToDelete) {
    cache.delete(key);
    cacheStats.deletes++;
  }
  
  if (keysToDelete.length > 0) {
    logger.debug('Cache expiré nettoyé', { 
      keysDeleted: keysToDelete.length 
    });
  }
  
  return keysToDelete.length;
};

// Nettoyer le cache expiré toutes les 5 minutes
setInterval(cleanupExpiredCache, 5 * 60 * 1000);

/**
 * Middleware pour exposer les statistiques du cache (admin uniquement)
 */
const exposeCacheStats = (req, res, next) => {
  if (req.path === '/cache-stats' && req.method === 'GET') {
    return res.json({
      success: true,
      data: getCacheStats()
    });
  }
  next();
};

module.exports = {
  // Middlewares de cache
  cacheConfiguration,
  cacheValidation,
  cacheStatistics,
  cacheAvailability,
  invalidateCache,
  exposeCacheStats,
  
  // Utilitaires
  getFromCache,
  setInCache,
  clearCache,
  getCacheStats,
  cleanupExpiredCache,
  
  // Configuration
  CACHE_CONFIG
};
