const logger = require('../logger');

// Vérification des permissions
const checkPermissions = (request, response, next) => {
  const { role } = request.user;

  if (!['ADMIN', 'DIRECTION'].includes(role)) {
    return response.status(403).json({
      success: false,
      message: 'Permission refusée'
    });
  }

  next();
};

// Validation des paramètres de date
const validateDateParams = (request, response, next) => {
  const { date_debut, date_fin } = request.query;

  if (!date_debut || !date_fin) {
    return response.status(400).json({
      success: false,
      message: 'Les dates de début et de fin sont requises'
    });
  }

  const dateDebut = new Date(date_debut);
  const dateFin = new Date(date_fin);

  if (isNaN(dateDebut.getTime()) || isNaN(dateFin.getTime())) {
    return response.status(400).json({
      success: false,
      message: 'Format de date invalide'
    });
  }

  if (dateDebut > dateFin) {
    return response.status(400).json({
      success: false,
      message: 'La date de début doit être antérieure à la date de fin'
    });
  }

  // Limiter la période à 1 an maximum
  const unAn = 365 * 24 * 60 * 60 * 1000;
  if (dateFin - dateDebut > unAn) {
    return response.status(400).json({
      success: false,
      message: 'La période ne peut pas dépasser 1 an'
    });
  }

  next();
};

// Journalisation des actions
const logStatistiqueAction = (action) => {
  return (request, response, next) => {
    const { user } = request;
    const { date_debut, date_fin, type_rapport, format } = request.query;

    logger.info('Action statistique', {
      action,
      utilisateur_id: user.id,
      role: user.role,
      periode: {
        date_debut,
        date_fin
      },
      type_rapport,
      format
    });

    next();
  };
};

module.exports = {
  checkPermissions,
  validateDateParams,
  logStatistiqueAction
}; 