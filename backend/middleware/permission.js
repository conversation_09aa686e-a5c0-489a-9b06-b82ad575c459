const PermissionService = require('../services/permission.service');
const EmployeeTypeService = require('../services/employeeType.service');
const logger = require('../logger');

/**
 * Middleware de permissions - Système simplifié
 * Phase 4 : Refactoring des middlewares
 */

// Middleware pour vérifier une permission spécifique (système simplifié)
const checkPermission = (permission) => async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentification requise'
      });
    }

    const hasPermission = await PermissionService.hasPermission(
      req.user.id,
      req.user.role,
      permission,
      req.user.complexe_id,
      req.user.type_employe
    );

    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: 'Permissions insuffisantes',
        required: permission,
        user_type: req.user.role,
        employee_type: req.user.type_employe
      });
    }

    return next();
  } catch (error) {
    logger.error('Permission check error:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la vérification des permissions'
    });
  }
};

// Middleware pour vérifier plusieurs permissions (au moins une doit être présente)
const checkAnyPermission = (permissions) => async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentification requise'
      });
    }

    const userPermissions = await PermissionService.getUserPermissions(
      req.user.id,
      req.user.role,
      req.user.complexe_id,
      req.user.type_employe
    );

    const hasAnyPermission = permissions.some(permission =>
      userPermissions.includes(permission)
    );

    if (!hasAnyPermission) {
      return res.status(403).json({
        success: false,
        message: 'Permissions insuffisantes',
        required: permissions,
        user_permissions: userPermissions
      });
    }

    return next();
  } catch (error) {
    logger.error('Permission check error:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la vérification des permissions'
    });
  }
};

// Middleware pour vérifier plusieurs permissions (toutes doivent être présentes)
const checkAllPermissions = (permissions) => async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentification requise'
      });
    }

    const userPermissions = await PermissionService.getUserPermissions(
      req.user.id,
      req.user.role,
      req.user.complexe_id,
      req.user.type_employe
    );

    const hasAllPermissions = permissions.every(permission =>
      userPermissions.includes(permission)
    );

    if (!hasAllPermissions) {
      return res.status(403).json({
        success: false,
        message: 'Permissions insuffisantes',
        required: permissions,
        user_permissions: userPermissions
      });
    }

    return next();
  } catch (error) {
    logger.error('Permission check error:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la vérification des permissions'
    });
  }
};

/**
 * Middleware simplifié pour vérifier les types d'utilisateurs
 */
const checkUserType = (allowedTypes) => (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentification requise'
      });
    }

    const userTypes = Array.isArray(allowedTypes) ? allowedTypes : [allowedTypes];

    if (!userTypes.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Type d\'utilisateur non autorisé',
        required_types: userTypes,
        user_type: req.user.role
      });
    }

    return next();
  } catch (error) {
    logger.error('User type check error:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la vérification du type d\'utilisateur'
    });
  }
};

/**
 * Middleware pour vérifier l'accès admin (simplifié)
 */
const checkAdminAccess = (req, res, next) => {
  return checkUserType(['super_admin', 'admin_chaine', 'admin_complexe'])(req, res, next);
};

/**
 * Middleware pour vérifier l'accès employé selon le type
 */
const checkEmployeeType = (allowedEmployeeTypes) => (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentification requise'
      });
    }

    // Les admins ont accès à tout
    if (['super_admin', 'admin_chaine', 'admin_complexe'].includes(req.user.role)) {
      return next();
    }

    // Pour les employés, vérifier le type
    if (req.user.role === 'employe') {
      const employeeTypes = Array.isArray(allowedEmployeeTypes) ? allowedEmployeeTypes : [allowedEmployeeTypes];

      if (!employeeTypes.includes(req.user.type_employe)) {
        return res.status(403).json({
          success: false,
          message: 'Type d\'employé non autorisé',
          required_employee_types: employeeTypes,
          employee_type: req.user.type_employe
        });
      }
    }

    return next();
  } catch (error) {
    logger.error('Employee type check error:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la vérification du type d\'employé'
    });
  }
};

/**
 * Middleware pour vérifier l'accès aux services selon le type d'employé
 */
const checkServiceAccess = (serviceType) => (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentification requise'
      });
    }

    // Les admins ont accès à tous les services
    if (['super_admin', 'admin_chaine', 'admin_complexe'].includes(req.user.role)) {
      return next();
    }

    // Pour les employés, vérifier l'accès au service
    if (req.user.role === 'employe') {
      const hasAccess = EmployeeTypeService.validateServiceAccess(req.user, serviceType);

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: `Accès non autorisé au service ${serviceType}`,
          employee_type: req.user.type_employe,
          services_autorises: req.user.services_autorises
        });
      }
    }

    return next();
  } catch (error) {
    logger.error('Service access check error:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la vérification de l\'accès au service'
    });
  }
};

module.exports = {
  // Middlewares de base (refactorisés)
  checkPermission,
  checkAnyPermission,
  checkAllPermissions,

  // Nouveaux middlewares simplifiés
  checkUserType,
  checkAdminAccess,
  checkEmployeeType,
  checkServiceAccess
};