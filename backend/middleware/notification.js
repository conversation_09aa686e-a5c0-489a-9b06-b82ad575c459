const logger = require('../logger');

// Validation des données de notification
const validateNotification = (request, response, next) => {
  const { titre, message, type, destinataire_id } = request.body;

  if (!titre || !message || !type || !destinataire_id) {
    return response.status(400).json({
      success: false,
      message: 'Tous les champs sont requis (titre, message, type, destinataire_id)'
    });
  }

  const typesValides = ['INFO', 'ALERTE', 'SUCCES', 'ERREUR'];
  if (!typesValides.includes(type)) {
    return response.status(400).json({
      success: false,
      message: 'Type de notification invalide',
      types_valides: typesValides
    });
  }

  next();
};

// Vérification des permissions
const checkPermissions = (request, response, next) => {
  const { role } = request.user;

  if (!['ADMIN', 'RECEPTION', 'CLIENT'].includes(role)) {
    return response.status(403).json({
      success: false,
      message: 'Permission refusée'
    });
  }

  next();
};

// Journalisation des actions sur les notifications
const logNotificationAction = (action) => {
  return (request, response, next) => {
    const { user } = request;
    const notificationId = request.params.id;

    logger.info('Action notification', {
      action,
      notification_id: notificationId,
      utilisateur_id: user.id,
      role: user.role
    });

    next();
  };
};

module.exports = {
  validateNotification,
  checkPermissions,
  logNotificationAction
}; 