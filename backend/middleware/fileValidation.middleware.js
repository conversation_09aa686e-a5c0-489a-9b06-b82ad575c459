const multer = require('multer');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const logger = require('../logger');

/**
 * Configuration de stockage pour multer
 */
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/temp/';
    
    // Création du répertoire s'il n'existe pas
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Génération d'un nom de fichier sécurisé et unique
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(8).toString('hex');
    const extension = path.extname(file.originalname);
    const safeName = `temp-${timestamp}-${randomString}${extension}`;
    
    cb(null, safeName);
  }
});

/**
 * Validation des types de fichiers Excel
 */
const fileFilter = (req, file, cb) => {
  const allowedMimeTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.ms-excel', // .xls
    'application/octet-stream' // Fallback pour certains navigateurs
  ];
  
  const allowedExtensions = ['.xlsx', '.xls'];
  const fileExtension = path.extname(file.originalname).toLowerCase();
  
  // Vérification du type MIME
  if (!allowedMimeTypes.includes(file.mimetype)) {
    logger.warn('File rejected - invalid MIME type', {
      originalName: file.originalname,
      mimetype: file.mimetype,
      expectedTypes: allowedMimeTypes
    });
    
    return cb(new Error('Type de fichier non autorisé. Seuls les fichiers Excel (.xlsx, .xls) sont acceptés.'), false);
  }
  
  // Vérification de l'extension
  if (!allowedExtensions.includes(fileExtension)) {
    logger.warn('File rejected - invalid extension', {
      originalName: file.originalname,
      extension: fileExtension,
      expectedExtensions: allowedExtensions
    });
    
    return cb(new Error('Extension de fichier non autorisée. Seuls les fichiers .xlsx et .xls sont acceptés.'), false);
  }
  
  // Validation du nom de fichier (sécurité)
  const dangerousChars = /[<>:"/\\|?*\x00-\x1f]/;
  if (dangerousChars.test(file.originalname)) {
    logger.warn('File rejected - dangerous characters in filename', {
      originalName: file.originalname
    });
    
    return cb(new Error('Nom de fichier contient des caractères non autorisés.'), false);
  }
  
  cb(null, true);
};

/**
 * Configuration multer pour upload de fichiers Excel
 */
const fileUploadConfig = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB maximum
    files: 1, // Un seul fichier à la fois
    fieldSize: 1024 * 1024, // 1MB pour les champs texte
    fieldNameSize: 100, // Longueur max du nom de champ
    fields: 10 // Nombre max de champs non-fichier
  }
});

/**
 * Middleware de validation avancée des fichiers uploadés
 */
const validateFileUpload = (req, res, next) => {
  try {
    const file = req.file;
    
    if (!file) {
      return res.status(400).json({
        success: false,
        message: 'Aucun fichier fourni'
      });
    }
    
    // Validation de la taille réelle du fichier
    if (file.size === 0) {
      return res.status(400).json({
        success: false,
        message: 'Le fichier est vide'
      });
    }
    
    if (file.size > 10 * 1024 * 1024) {
      return res.status(400).json({
        success: false,
        message: 'Fichier trop volumineux. Taille maximale: 10MB'
      });
    }
    
    // Validation de l'intégrité du fichier (vérification basique)
    if (!fs.existsSync(file.path)) {
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la sauvegarde du fichier'
      });
    }
    
    // Log de l'upload réussi
    logger.info('File upload validated successfully', {
      originalName: file.originalname,
      filename: file.filename,
      size: file.size,
      mimetype: file.mimetype,
      userId: req.user?.employe_id
    });
    
    next();
    
  } catch (error) {
    logger.error('Error in file validation middleware:', error);
    
    // Nettoyage du fichier en cas d'erreur
    if (req.file && req.file.path && fs.existsSync(req.file.path)) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (cleanupError) {
        logger.error('Error cleaning up file after validation error:', cleanupError);
      }
    }
    
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la validation du fichier'
    });
  }
};

/**
 * Middleware de gestion des erreurs multer
 */
const handleMulterError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    logger.warn('Multer error occurred', {
      code: error.code,
      message: error.message,
      field: error.field
    });
    
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        return res.status(400).json({
          success: false,
          message: 'Fichier trop volumineux. Taille maximale: 10MB'
        });
        
      case 'LIMIT_FILE_COUNT':
        return res.status(400).json({
          success: false,
          message: 'Trop de fichiers. Un seul fichier autorisé'
        });
        
      case 'LIMIT_UNEXPECTED_FILE':
        return res.status(400).json({
          success: false,
          message: 'Champ de fichier inattendu'
        });
        
      case 'LIMIT_FIELD_KEY':
        return res.status(400).json({
          success: false,
          message: 'Nom de champ trop long'
        });
        
      case 'LIMIT_FIELD_VALUE':
        return res.status(400).json({
          success: false,
          message: 'Valeur de champ trop longue'
        });
        
      case 'LIMIT_FIELD_COUNT':
        return res.status(400).json({
          success: false,
          message: 'Trop de champs dans la requête'
        });
        
      default:
        return res.status(400).json({
          success: false,
          message: `Erreur d'upload: ${error.message}`
        });
    }
  }
  
  // Erreur personnalisée du fileFilter
  if (error.message.includes('Type de fichier') || 
      error.message.includes('Extension') || 
      error.message.includes('caractères non autorisés')) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
  
  // Autres erreurs
  logger.error('Unexpected error in file upload:', error);
  return res.status(500).json({
    success: false,
    message: 'Erreur interne lors de l\'upload du fichier'
  });
};

/**
 * Middleware de nettoyage automatique des fichiers temporaires
 */
const cleanupTempFiles = (req, res, next) => {
  // Nettoyage après traitement de la réponse
  res.on('finish', () => {
    if (req.file && req.file.path && req.cleanupFile !== false) {
      setTimeout(() => {
        try {
          if (fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
            logger.debug('Temporary file cleaned up', { path: req.file.path });
          }
        } catch (error) {
          logger.warn('Failed to cleanup temporary file', {
            path: req.file.path,
            error: error.message
          });
        }
      }, 5000); // Délai de 5 secondes pour permettre le traitement
    }
  });
  
  next();
};

/**
 * Middleware de validation de sécurité avancée
 */
const advancedSecurityCheck = async (req, res, next) => {
  try {
    const file = req.file;
    
    if (!file) {
      return next();
    }
    
    // Lecture des premiers octets pour vérification du magic number
    const buffer = Buffer.alloc(8);
    const fd = fs.openSync(file.path, 'r');
    fs.readSync(fd, buffer, 0, 8, 0);
    fs.closeSync(fd);
    
    // Vérification des signatures de fichiers Excel
    const xlsxSignature = Buffer.from([0x50, 0x4B, 0x03, 0x04]); // ZIP signature (XLSX)
    const xlsSignature = Buffer.from([0xD0, 0xCF, 0x11, 0xE0]); // OLE signature (XLS)
    
    const isValidXlsx = buffer.subarray(0, 4).equals(xlsxSignature);
    const isValidXls = buffer.subarray(0, 4).equals(xlsSignature);
    
    if (!isValidXlsx && !isValidXls) {
      logger.warn('File rejected - invalid file signature', {
        originalName: file.originalname,
        signature: buffer.toString('hex')
      });
      
      return res.status(400).json({
        success: false,
        message: 'Le fichier ne semble pas être un fichier Excel valide'
      });
    }
    
    // Vérification de la taille minimale (fichier Excel vide fait au moins quelques Ko)
    if (file.size < 1024) {
      logger.warn('File rejected - too small to be valid Excel', {
        originalName: file.originalname,
        size: file.size
      });
      
      return res.status(400).json({
        success: false,
        message: 'Le fichier semble être corrompu ou vide'
      });
    }
    
    logger.info('Advanced security check passed', {
      originalName: file.originalname,
      isXlsx: isValidXlsx,
      isXls: isValidXls
    });
    
    next();
    
  } catch (error) {
    logger.error('Error in advanced security check:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la vérification de sécurité du fichier'
    });
  }
};

module.exports = {
  fileUploadConfig,
  validateFileUpload,
  handleMulterError,
  cleanupTempFiles,
  advancedSecurityCheck
};
