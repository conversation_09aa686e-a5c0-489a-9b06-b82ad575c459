const logger = require('../logger');
const ReservationService = require('../services/reservation.service');
const DisponibiliteService = require('../services/disponibilite.service');

// Validation du format de date
const isValidDate = (date) => {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(date)) return false;
  const d = new Date(date);
  return d instanceof Date && !isNaN(d);
};

// Validation du format d'heure
const isValidTime = (time) => {
  const regex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return regex.test(time);
};

// Validation des paramètres de réservation
const validateReservationDemande = (req, res, next) => {
  const { date, heure_debut, heure_fin, type_chambre } = req.body;

  if (!date || !heure_debut || !heure_fin || !type_chambre) {
    return res.status(400).json({
      status: 'error',
      message: 'Date, heures et type de chambre requis'
    });
  }

  // Validation du format de date
  if (!isValidDate(date)) {
    return res.status(400).json({
      status: 'error',
      message: 'Format de date invalide (YYYY-MM-DD)'
    });
  }

  // Validation des heures
  if (!isValidTime(heure_debut) || !isValidTime(heure_fin)) {
    return res.status(400).json({
      status: 'error',
      message: 'Format d\'heure invalide (HH:mm)'
    });
  }

  // Validation que l'heure de fin est après l'heure de début
  if (new Date(`${date}T${heure_fin}`) <= new Date(`${date}T${heure_debut}`)) {
    return res.status(400).json({
      status: 'error',
      message: 'L\'heure de fin doit être après l\'heure de début'
    });
  }

  next();
};

// Vérification de la disponibilité
const checkDisponibilite = async (req, res, next) => {
  try {
    const { date, heure_debut, heure_fin, type_chambre } = req.body;
    const disponibilite = await DisponibiliteService.verifierDisponibilite({
      date_debut: date,
      date_fin: date,
      heure_debut,
      heure_fin,
      type_chambre
    });

    if (!disponibilite.disponible) {
      return res.status(400).json({
        status: 'error',
        message: 'Aucune chambre disponible pour ce créneau'
      });
    }

    req.disponibilite = disponibilite;
    next();
  } catch (error) {
    logger.error('Erreur vérification disponibilité:', error);
    res.status(500).json({
      status: 'error',
      message: 'Erreur lors de la vérification de la disponibilité'
    });
  }
};

// Validation des données de paiement
const validatePaiement = (req, res, next) => {
  const { montant, mode_paiement, reference_paiement } = req.body;

  if (!montant || montant <= 0) {
    return res.status(400).json({ message: 'Le montant du paiement est invalide' });
  }

  if (!mode_paiement || !['carte', 'especes', 'mobile_money'].includes(mode_paiement)) {
    return res.status(400).json({ message: 'Le mode de paiement est invalide' });
  }

  if (!reference_paiement) {
    return res.status(400).json({ message: 'La référence du paiement est requise' });
  }

  next();
};

// Vérification des permissions selon le rôle
const checkPermissions = (req, res, next) => {
  const { role } = req.user;
  const { numero } = req.params;

  // Les clients ne peuvent accéder qu'à leurs propres réservations
  if (role === 'client') {
    if (!numero) {
      return res.status(403).json({ message: 'Accès non autorisé' });
    }
    // Vérifier si la réservation appartient au client
    return ReservationService.verifierProprietaireReservation(numero, req.user.id)
      .then((estProprietaire) => {
        if (!estProprietaire) {
          return res.status(403).json({ message: 'Accès non autorisé à cette réservation' });
        }
        next();
      })
      .catch((error) => {
        logger.error('Erreur vérification propriétaire:', error);
        res.status(500).json({ message: 'Erreur lors de la vérification des permissions' });
      });
  }

  // Les employés de réception peuvent accéder à toutes les réservations
  if (role === 'reception') {
    return next();
  }

  // Les administrateurs peuvent accéder à toutes les réservations
  if (role.toLowerCase().includes('admin')) {
    return next();
  }

  return res.status(403).json({ message: 'Rôle non autorisé' });
};

// Journalisation des actions
const logReservationAction = (action) => {
  return (req, res, next) => {
    logger.info(`Action réservation: ${action}`, {
      body: req.body,
      params: req.params,
      user: req.user
    });
    next();
  };
};

module.exports = {
  validateReservationDemande,
  checkDisponibilite,
  validatePaiement,
  checkPermissions,
  logReservationAction
}; 