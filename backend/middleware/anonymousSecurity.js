const logger = require('../logger');
const db = require('../db');

/**
 * Middleware de sécurité avancée pour les réservations anonymes
 * Protection contre les attaques et abus
 */

/**
 * Middleware de détection d'anomalies
 */
const anomalyDetection = async (req, res, next) => {
  try {
    const ip = req.ip;
    const userAgent = req.get('User-Agent') || '';
    const now = new Date();
    
    // Détecter les patterns suspects
    const suspiciousPatterns = [
      // User-Agents suspects
      /bot|crawler|spider|scraper/i,
      // Tentatives d'injection
      /<script|javascript:|data:|vbscript:/i,
      // User-Agents trop courts ou vides
      /^.{0,5}$/
    ];
    
    const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(userAgent));
    
    if (isSuspicious) {
      logger.warn('Pattern suspect détecté', {
        ip: ip,
        userAgent: userAgent,
        url: req.originalUrl,
        method: req.method
      });
      
      // Enregistrer l'incident
      await logSecurityIncident(ip, 'SUSPICIOUS_PATTERN', {
        userAgent: userAgent,
        url: req.originalUrl,
        pattern: 'User-Agent ou contenu suspect'
      });
      
      return res.status(403).json({
        success: false,
        message: 'Requête non autorisée',
        code: 'SUSPICIOUS_ACTIVITY'
      });
    }
    
    // Vérifier la fréquence des requêtes
    const recentRequests = await getRecentRequestCount(ip, 60); // Dernière minute
    
    if (recentRequests > 30) { // Plus de 30 requêtes par minute
      logger.warn('Fréquence de requêtes anormalement élevée', {
        ip: ip,
        requestCount: recentRequests,
        timeWindow: '60 seconds'
      });
      
      await logSecurityIncident(ip, 'HIGH_FREQUENCY', {
        requestCount: recentRequests,
        timeWindow: 60
      });
      
      return res.status(429).json({
        success: false,
        message: 'Trop de requêtes. Veuillez ralentir.',
        code: 'HIGH_FREQUENCY_DETECTED'
      });
    }
    
    next();
    
  } catch (error) {
    logger.error('Erreur détection d\'anomalies:', error);
    next(); // Continuer en cas d'erreur pour ne pas bloquer le service
  }
};

/**
 * Middleware de protection contre les attaques par force brute
 */
const bruteForcePrevention = async (req, res, next) => {
  try {
    const ip = req.ip;
    const action = req.route?.path || req.originalUrl;
    
    // Vérifier les tentatives récentes pour cette IP
    const recentFailures = await getRecentFailures(ip, action, 3600); // Dernière heure
    
    if (recentFailures >= 10) {
      logger.warn('Tentative de force brute détectée', {
        ip: ip,
        action: action,
        failures: recentFailures
      });
      
      await logSecurityIncident(ip, 'BRUTE_FORCE_ATTEMPT', {
        action: action,
        failures: recentFailures,
        timeWindow: 3600
      });
      
      // Bloquer temporairement
      await blockIP(ip, 'Tentatives de force brute', 60); // 1 heure
      
      return res.status(429).json({
        success: false,
        message: 'Trop de tentatives échouées. Accès temporairement bloqué.',
        code: 'BRUTE_FORCE_PROTECTION'
      });
    }
    
    next();
    
  } catch (error) {
    logger.error('Erreur prévention force brute:', error);
    next();
  }
};

/**
 * Middleware de validation géographique
 */
const geoValidation = async (req, res, next) => {
  try {
    const ip = req.ip;
    
    // Liste des pays bloqués (si configuré)
    const blockedCountries = process.env.BLOCKED_COUNTRIES ? 
      process.env.BLOCKED_COUNTRIES.split(',') : [];
    
    if (blockedCountries.length > 0) {
      // Ici, on pourrait intégrer un service de géolocalisation IP
      // Pour l'exemple, on simule une vérification basique
      
      // Bloquer les IPs privées suspectes en production
      if (process.env.NODE_ENV === 'production' && isPrivateIP(ip)) {
        logger.warn('Tentative d\'accès depuis IP privée en production', {
          ip: ip
        });
        
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé depuis cette localisation',
          code: 'GEO_BLOCKED'
        });
      }
    }
    
    next();
    
  } catch (error) {
    logger.error('Erreur validation géographique:', error);
    next();
  }
};

/**
 * Middleware de protection contre les injections
 */
const injectionProtection = (req, res, next) => {
  try {
    const suspiciousPatterns = [
      // SQL Injection
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      // XSS
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      // Command Injection
      /(\||;|&|`|\$\(|\${)/,
      // Path Traversal
      /\.\.\//g,
      /\.\.\\/g
    ];
    
    const checkObject = (obj, path = '') => {
      for (const [key, value] of Object.entries(obj)) {
        const currentPath = path ? `${path}.${key}` : key;
        
        if (typeof value === 'string') {
          for (const pattern of suspiciousPatterns) {
            if (pattern.test(value)) {
              logger.warn('Tentative d\'injection détectée', {
                ip: req.ip,
                field: currentPath,
                value: value.substring(0, 100),
                pattern: pattern.toString()
              });
              
              throw new Error(`Contenu suspect détecté dans le champ: ${currentPath}`);
            }
          }
        } else if (typeof value === 'object' && value !== null) {
          checkObject(value, currentPath);
        }
      }
    };
    
    // Vérifier le body
    if (req.body && typeof req.body === 'object') {
      checkObject(req.body);
    }
    
    // Vérifier les query parameters
    if (req.query && typeof req.query === 'object') {
      checkObject(req.query);
    }
    
    // Vérifier les paramètres d'URL
    if (req.params && typeof req.params === 'object') {
      checkObject(req.params);
    }
    
    next();
    
  } catch (error) {
    if (error.message.includes('Contenu suspect')) {
      return res.status(400).json({
        success: false,
        message: 'Contenu de requête invalide',
        code: 'INJECTION_ATTEMPT'
      });
    }
    
    logger.error('Erreur protection injection:', error);
    next();
  }
};

/**
 * Middleware de limitation de taille des requêtes
 */
const requestSizeLimit = (req, res, next) => {
  const maxSize = 1024 * 1024; // 1MB
  
  if (req.get('Content-Length') && parseInt(req.get('Content-Length')) > maxSize) {
    logger.warn('Requête trop volumineuse', {
      ip: req.ip,
      contentLength: req.get('Content-Length'),
      maxSize: maxSize
    });
    
    return res.status(413).json({
      success: false,
      message: 'Requête trop volumineuse',
      code: 'REQUEST_TOO_LARGE'
    });
  }
  
  next();
};

/**
 * Middleware de validation des headers
 */
const headerValidation = (req, res, next) => {
  try {
    const requiredHeaders = ['user-agent'];
    const suspiciousHeaders = ['x-forwarded-for', 'x-real-ip'];
    
    // Vérifier les headers requis
    for (const header of requiredHeaders) {
      if (!req.get(header)) {
        logger.warn('Header requis manquant', {
          ip: req.ip,
          missingHeader: header
        });
        
        return res.status(400).json({
          success: false,
          message: 'Headers de requête invalides',
          code: 'INVALID_HEADERS'
        });
      }
    }
    
    // Détecter les tentatives de spoofing d'IP
    for (const header of suspiciousHeaders) {
      const value = req.get(header);
      if (value && value !== req.ip) {
        logger.warn('Tentative possible de spoofing d\'IP', {
          realIP: req.ip,
          spoofedIP: value,
          header: header
        });
      }
    }
    
    next();
    
  } catch (error) {
    logger.error('Erreur validation headers:', error);
    next();
  }
};

/**
 * Fonctions utilitaires
 */

async function getRecentRequestCount(ip, seconds) {
  try {
    const query = `
      SELECT COUNT(*) as count
      FROM "LogsAccesAnonymes"
      WHERE adresse_ip = $1
      AND timestamp > CURRENT_TIMESTAMP - INTERVAL '${seconds} seconds'
    `;
    
    const result = await db.query(query, [ip]);
    return parseInt(result.rows[0].count) || 0;
  } catch (error) {
    logger.error('Erreur récupération compteur requêtes:', error);
    return 0;
  }
}

async function getRecentFailures(ip, action, seconds) {
  try {
    const query = `
      SELECT COUNT(*) as count
      FROM "LogsAccesAnonymes"
      WHERE adresse_ip = $1
      AND action LIKE '%ECHEC%'
      AND timestamp > CURRENT_TIMESTAMP - INTERVAL '${seconds} seconds'
    `;
    
    const result = await db.query(query, [ip]);
    return parseInt(result.rows[0].count) || 0;
  } catch (error) {
    logger.error('Erreur récupération échecs récents:', error);
    return 0;
  }
}

async function logSecurityIncident(ip, type, details) {
  try {
    const query = `
      INSERT INTO "LogsAccesAnonymes" (
        code_acces_anonyme,
        adresse_ip,
        action,
        details,
        success
      ) VALUES ($1, $2, $3, $4, false)
    `;
    
    await db.query(query, [
      'SECURITY_INCIDENT',
      ip,
      `SECURITY_${type}`,
      JSON.stringify(details)
    ]);
  } catch (error) {
    logger.error('Erreur logging incident sécurité:', error);
  }
}

async function blockIP(ip, reason, minutes) {
  try {
    const query = `
      INSERT INTO "SecuriteAccesAnonymes" (
        adresse_ip,
        tentatives_echec,
        bloque_jusqu,
        raison_blocage,
        derniere_tentative
      ) VALUES ($1, 999, CURRENT_TIMESTAMP + INTERVAL '${minutes} minutes', $2, CURRENT_TIMESTAMP)
      ON CONFLICT (adresse_ip)
      DO UPDATE SET
        bloque_jusqu = CURRENT_TIMESTAMP + INTERVAL '${minutes} minutes',
        raison_blocage = $2,
        updated_at = CURRENT_TIMESTAMP
    `;
    
    await db.query(query, [ip, reason]);
  } catch (error) {
    logger.error('Erreur blocage IP:', error);
  }
}

function isPrivateIP(ip) {
  const privateRanges = [
    /^10\./,
    /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
    /^192\.168\./,
    /^127\./,
    /^::1$/,
    /^fc00:/,
    /^fe80:/
  ];
  
  return privateRanges.some(range => range.test(ip));
}

module.exports = {
  anomalyDetection,
  bruteForcePrevention,
  geoValidation,
  injectionProtection,
  requestSizeLimit,
  headerValidation,
  
  // Utilitaires
  getRecentRequestCount,
  getRecentFailures,
  logSecurityIncident,
  blockIP,
  isPrivateIP
};
