const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const logger = require('../logger');

/**
 * Configuration des limites par type d'endpoint
 */
const RATE_LIMIT_CONFIGS = {
  // Upload de fichiers - très restrictif
  fileUpload: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 uploads par fenêtre
    message: {
      success: false,
      message: 'Trop d\'uploads de fichiers. Veuillez patienter avant de réessayer.',
      retryAfter: '15 minutes'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: false,
    skipFailedRequests: true
  },

  // Parsing de fichiers - modérément restrictif
  fileParsing: {
    windowMs: 10 * 60 * 1000, // 10 minutes
    max: 10, // 10 parsing par fenêtre
    message: {
      success: false,
      message: 'Trop de demandes de parsing. Veuillez patienter avant de réessayer.',
      retryAfter: '10 minutes'
    },
    standardHeaders: true,
    legacyHeaders: false
  },

  // Import de données - restrictif
  dataImport: {
    windowMs: 30 * 60 * 1000, // 30 minutes
    max: 3, // 3 imports par fenêtre
    message: {
      success: false,
      message: 'Trop d\'imports de données. Veuillez patienter avant de réessayer.',
      retryAfter: '30 minutes'
    },
    standardHeaders: true,
    legacyHeaders: false
  },

  // API générale - permissif
  general: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // 100 requêtes par fenêtre
    message: {
      success: false,
      message: 'Trop de requêtes. Veuillez patienter avant de réessayer.',
      retryAfter: '15 minutes'
    },
    standardHeaders: true,
    legacyHeaders: false
  },

  // Création d'entités - modéré
  creation: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    max: 20, // 20 créations par fenêtre
    message: {
      success: false,
      message: 'Trop de créations d\'entités. Veuillez patienter avant de réessayer.',
      retryAfter: '5 minutes'
    },
    standardHeaders: true,
    legacyHeaders: false
  }
};

/**
 * Configuration du ralentissement progressif
 */
const SLOW_DOWN_CONFIGS = {
  // Ralentissement pour les uploads
  fileUpload: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    delayAfter: 2, // Ralentir après 2 requêtes
    delayMs: 500, // Délai initial de 500ms
    maxDelayMs: 10000, // Délai maximum de 10 secondes
    skipFailedRequests: true,
    skipSuccessfulRequests: false
  },

  // Ralentissement pour les requêtes générales
  general: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    delayAfter: 50, // Ralentir après 50 requêtes
    delayMs: 100, // Délai initial de 100ms
    maxDelayMs: 2000, // Délai maximum de 2 secondes
    skipFailedRequests: true,
    skipSuccessfulRequests: false
  }
};

/**
 * Fonction de génération de clé personnalisée
 */
const generateKey = (req) => {
  // Utilisation de l'ID utilisateur si disponible, sinon IP
  const userId = req.user?.employe_id;
  const ip = req.ip || req.connection.remoteAddress;
  
  if (userId) {
    return `user:${userId}`;
  }
  
  return `ip:${ip}`;
};

/**
 * Handler personnalisé pour les limites atteintes
 */
const rateLimitHandler = (req, res, next, options) => {
  const userId = req.user?.employe_id;
  const ip = req.ip;
  
  logger.warn('Rate limit exceeded', {
    userId,
    ip,
    endpoint: req.path,
    method: req.method,
    userAgent: req.get('User-Agent')
  });

  // Réponse personnalisée selon le type d'endpoint
  let message = options.message;
  
  if (req.path.includes('/upload/')) {
    message = {
      success: false,
      message: 'Limite d\'upload atteinte. Veuillez patienter avant de télécharger un nouveau fichier.',
      details: 'Cette limitation protège le système contre la surcharge.',
      retryAfter: options.windowMs / 1000 / 60 + ' minutes'
    };
  } else if (req.path.includes('/import/')) {
    message = {
      success: false,
      message: 'Limite d\'import atteinte. Veuillez patienter avant de lancer un nouvel import.',
      details: 'Les imports sont des opérations coûteuses qui nécessitent une limitation.',
      retryAfter: options.windowMs / 1000 / 60 + ' minutes'
    };
  }

  res.status(429).json(message);
};

/**
 * Middleware de rate limiting pour uploads
 */
const fileUploadLimiter = rateLimit({
  ...RATE_LIMIT_CONFIGS.fileUpload,
  keyGenerator: generateKey,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * Middleware de rate limiting pour parsing
 */
const fileParsingLimiter = rateLimit({
  ...RATE_LIMIT_CONFIGS.fileParsing,
  keyGenerator: generateKey,
  handler: rateLimitHandler
});

/**
 * Middleware de rate limiting pour imports
 */
const dataImportLimiter = rateLimit({
  ...RATE_LIMIT_CONFIGS.dataImport,
  keyGenerator: generateKey,
  handler: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * Middleware de rate limiting général
 */
const generalLimiter = rateLimit({
  ...RATE_LIMIT_CONFIGS.general,
  keyGenerator: generateKey,
  handler: rateLimitHandler
});

/**
 * Middleware de rate limiting pour créations
 */
const creationLimiter = rateLimit({
  ...RATE_LIMIT_CONFIGS.creation,
  keyGenerator: generateKey,
  handler: rateLimitHandler
});

/**
 * Middleware de ralentissement pour uploads
 */
const fileUploadSlowDown = slowDown({
  ...SLOW_DOWN_CONFIGS.fileUpload,
  keyGenerator: generateKey,
  validate: { delayMs: false } // Supprime le warning delayMs
});

/**
 * Middleware de ralentissement général
 */
const generalSlowDown = slowDown({
  ...SLOW_DOWN_CONFIGS.general,
  keyGenerator: generateKey,
  validate: { delayMs: false } // Supprime le warning delayMs
});

/**
 * Middleware de rate limiting adaptatif selon le rôle
 */
const adaptiveRateLimiter = (baseConfig, roleMultipliers = {}) => {
  return (req, res, next) => {
    const userRole = req.user?.role;
    const multiplier = roleMultipliers[userRole] || 1;
    
    const adaptedConfig = {
      ...baseConfig,
      max: Math.floor(baseConfig.max * multiplier),
      keyGenerator: generateKey,
      handler: rateLimitHandler
    };

    return rateLimit(adaptedConfig)(req, res, next);
  };
};

/**
 * Middleware de rate limiting pour les super admins (plus permissif)
 */
const adminFileUploadLimiter = adaptiveRateLimiter(
  RATE_LIMIT_CONFIGS.fileUpload,
  {
    'super_admin': 3,
    'admin_chaine': 2,
    'admin_complexe': 1.5
  }
);

/**
 * Middleware de protection contre les attaques par déni de service
 */
const ddosProtection = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 200, // 200 requêtes par minute maximum
  message: {
    success: false,
    message: 'Trop de requêtes détectées. Accès temporairement bloqué.',
    details: 'Cette protection empêche les attaques par déni de service.'
  },
  keyGenerator: (req) => req.ip, // Basé uniquement sur l'IP
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res, next, options) => {
    logger.error('Potential DDoS attack detected', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
      method: req.method
    });
    
    res.status(429).json(options.message);
  }
});

/**
 * Middleware de rate limiting par taille de fichier
 */
const fileSizeBasedLimiter = (req, res, next) => {
  const file = req.file;
  
  if (!file) {
    return next();
  }

  // Limites plus strictes pour les gros fichiers
  let maxRequests = 5;
  let windowMs = 15 * 60 * 1000; // 15 minutes

  if (file.size > 5 * 1024 * 1024) { // > 5MB
    maxRequests = 2;
    windowMs = 30 * 60 * 1000; // 30 minutes
  } else if (file.size > 1 * 1024 * 1024) { // > 1MB
    maxRequests = 3;
    windowMs = 20 * 60 * 1000; // 20 minutes
  }

  const limiter = rateLimit({
    windowMs,
    max: maxRequests,
    keyGenerator: generateKey,
    message: {
      success: false,
      message: `Limite d'upload atteinte pour les fichiers de cette taille (${Math.round(file.size / 1024 / 1024)}MB).`,
      retryAfter: windowMs / 1000 / 60 + ' minutes'
    },
    handler: rateLimitHandler
  });

  return limiter(req, res, next);
};

/**
 * Middleware de monitoring des rate limits
 */
const rateLimitMonitoring = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    // Log des informations de rate limiting
    const rateLimitHeaders = {
      limit: res.get('X-RateLimit-Limit'),
      remaining: res.get('X-RateLimit-Remaining'),
      reset: res.get('X-RateLimit-Reset')
    };

    if (rateLimitHeaders.limit) {
      logger.debug('Rate limit info', {
        userId: req.user?.employe_id,
        ip: req.ip,
        endpoint: req.path,
        ...rateLimitHeaders
      });
    }

    return originalSend.call(this, data);
  };

  next();
};

/**
 * Limiters spécialisés pour l'inventaire
 */
const ingredientLimiter = rateLimit({
  ...RATE_LIMIT_CONFIGS.general,
  max: 50, // 50 requêtes par fenêtre pour les ingrédients
  keyGenerator: generateKey,
  handler: rateLimitHandler
});

const recipeLimiter = rateLimit({
  ...RATE_LIMIT_CONFIGS.creation,
  max: 30, // 30 requêtes par fenêtre pour les recettes
  keyGenerator: generateKey,
  handler: rateLimitHandler
});

const stockLimiter = rateLimit({
  ...RATE_LIMIT_CONFIGS.general,
  max: 40, // 40 requêtes par fenêtre pour le stock
  keyGenerator: generateKey,
  handler: rateLimitHandler
});

const analyticsLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 20, // 20 requêtes d'analytics par fenêtre
  keyGenerator: generateKey,
  handler: rateLimitHandler,
  message: {
    success: false,
    message: 'Trop de requêtes d\'analytics. Veuillez patienter avant de réessayer.',
    retryAfter: '5 minutes'
  }
});

const importLimiter = rateLimit({
  ...RATE_LIMIT_CONFIGS.dataImport,
  keyGenerator: generateKey,
  handler: rateLimitHandler
});

const heavyImportLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 heure
  max: 2, // 2 imports lourds par heure
  keyGenerator: generateKey,
  handler: rateLimitHandler,
  message: {
    success: false,
    message: 'Limite d\'imports lourds atteinte. Veuillez patienter avant de relancer un import.',
    retryAfter: '1 heure'
  }
});

const templateLimiter = rateLimit({
  ...RATE_LIMIT_CONFIGS.general,
  max: 30, // 30 requêtes par fenêtre pour les templates
  keyGenerator: generateKey,
  handler: rateLimitHandler
});

module.exports = {
  // Limiters spécialisés
  fileUploadLimiter,
  fileParsingLimiter,
  dataImportLimiter,
  generalLimiter,
  creationLimiter,

  // Limiters pour l'inventaire
  ingredientLimiter,
  recipeLimiter,
  stockLimiter,
  analyticsLimiter,
  importLimiter,
  heavyImportLimiter,
  templateLimiter,

  // Limiters adaptatifs
  adminFileUploadLimiter,
  adaptiveRateLimiter,

  // Slow down
  fileUploadSlowDown,
  generalSlowDown,

  // Protection
  ddosProtection,
  fileSizeBasedLimiter,

  // Monitoring
  rateLimitMonitoring,

  // Configurations
  RATE_LIMIT_CONFIGS,
  SLOW_DOWN_CONFIGS
};
