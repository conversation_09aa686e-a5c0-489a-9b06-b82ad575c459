const logger = require('../logger');
const db = require('../db');

/**
 * Middleware de monitoring et métriques pour les réservations anonymes
 * Collecte des données de performance et d'utilisation
 */

// Métriques en mémoire
const metrics = {
  requests: {
    total: 0,
    success: 0,
    errors: 0,
    byEndpoint: new Map(),
    byStatus: new Map()
  },
  performance: {
    totalResponseTime: 0,
    requestCount: 0,
    slowRequests: 0, // > 2 secondes
    averageResponseTime: 0
  },
  security: {
    blockedRequests: 0,
    suspiciousActivity: 0,
    rateLimitHits: 0
  },
  cache: {
    hits: 0,
    misses: 0,
    hitRate: 0
  }
};

/**
 * Middleware de collecte de métriques
 */
const collectMetrics = (req, res, next) => {
  const startTime = Date.now();
  const endpoint = req.route?.path || req.path;
  
  // Incrémenter le compteur de requêtes
  metrics.requests.total++;
  
  // Compter par endpoint
  const currentCount = metrics.requests.byEndpoint.get(endpoint) || 0;
  metrics.requests.byEndpoint.set(endpoint, currentCount + 1);
  
  // Intercepter la réponse
  const originalSend = res.send;
  res.send = function(data) {
    const responseTime = Date.now() - startTime;
    
    // Métriques de performance
    metrics.performance.totalResponseTime += responseTime;
    metrics.performance.requestCount++;
    metrics.performance.averageResponseTime = 
      metrics.performance.totalResponseTime / metrics.performance.requestCount;
    
    if (responseTime > 2000) {
      metrics.performance.slowRequests++;
    }
    
    // Métriques par statut
    const statusCode = res.statusCode;
    const statusCount = metrics.requests.byStatus.get(statusCode) || 0;
    metrics.requests.byStatus.set(statusCode, statusCount + 1);
    
    // Compter succès/erreurs
    if (statusCode >= 200 && statusCode < 400) {
      metrics.requests.success++;
    } else {
      metrics.requests.errors++;
    }
    
    // Logger les requêtes lentes
    if (responseTime > 5000) {
      logger.warn('Requête très lente détectée', {
        endpoint: endpoint,
        responseTime: responseTime,
        statusCode: statusCode,
        ip: req.ip
      });
    }
    
    originalSend.call(this, data);
  };
  
  next();
};

/**
 * Middleware de monitoring de la santé du système
 */
const healthCheck = async (req, res, next) => {
  if (req.path === '/health' && req.method === 'GET') {
    try {
      // Vérifier la connexion à la base de données
      const dbStart = Date.now();
      await db.query('SELECT 1');
      const dbResponseTime = Date.now() - dbStart;
      
      // Calculer les métriques de santé
      const errorRate = metrics.requests.total > 0 ? 
        (metrics.requests.errors / metrics.requests.total * 100).toFixed(2) : 0;
      
      const slowRequestRate = metrics.performance.requestCount > 0 ?
        (metrics.performance.slowRequests / metrics.performance.requestCount * 100).toFixed(2) : 0;
      
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        database: {
          connected: true,
          responseTime: dbResponseTime
        },
        metrics: {
          totalRequests: metrics.requests.total,
          errorRate: `${errorRate}%`,
          averageResponseTime: Math.round(metrics.performance.averageResponseTime),
          slowRequestRate: `${slowRequestRate}%`
        },
        memory: process.memoryUsage(),
        version: process.env.npm_package_version || '1.0.0'
      };
      
      // Déterminer le statut de santé
      if (dbResponseTime > 1000 || errorRate > 10 || slowRequestRate > 20) {
        health.status = 'degraded';
      }
      
      if (dbResponseTime > 5000 || errorRate > 50) {
        health.status = 'unhealthy';
      }
      
      const statusCode = health.status === 'healthy' ? 200 : 
                        health.status === 'degraded' ? 200 : 503;
      
      return res.status(statusCode).json(health);
      
    } catch (error) {
      logger.error('Erreur health check:', error);
      return res.status(503).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Database connection failed'
      });
    }
  }
  
  next();
};

/**
 * Middleware de monitoring des erreurs
 */
const errorMonitoring = (err, req, res, next) => {
  // Incrémenter les métriques d'erreur
  metrics.requests.errors++;
  
  // Logger l'erreur avec contexte
  logger.error('Erreur dans les réservations anonymes', {
    error: err.message,
    stack: err.stack,
    endpoint: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    body: req.body,
    params: req.params,
    query: req.query
  });
  
  // Détecter les erreurs critiques
  if (err.message.includes('ECONNREFUSED') || 
      err.message.includes('database') ||
      err.message.includes('timeout')) {
    
    logger.error('Erreur critique détectée', {
      error: err.message,
      type: 'CRITICAL_ERROR'
    });
    
    // Ici, on pourrait envoyer une alerte
    sendAlert('CRITICAL_ERROR', {
      message: err.message,
      endpoint: req.path,
      timestamp: new Date().toISOString()
    });
  }
  
  // Réponse d'erreur standardisée
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur',
    code: 'INTERNAL_ERROR',
    timestamp: new Date().toISOString()
  });
};

/**
 * Middleware de monitoring des performances
 */
const performanceMonitoring = (req, res, next) => {
  const startTime = process.hrtime.bigint();
  
  res.on('finish', () => {
    const endTime = process.hrtime.bigint();
    const duration = Number(endTime - startTime) / 1000000; // Convertir en millisecondes
    
    // Enregistrer les métriques de performance
    if (duration > 1000) { // Plus d'1 seconde
      logger.warn('Requête lente', {
        endpoint: req.path,
        method: req.method,
        duration: `${duration.toFixed(2)}ms`,
        ip: req.ip
      });
    }
    
    // Enregistrer en base pour analyse historique
    recordPerformanceMetric(req.path, req.method, duration, res.statusCode);
  });
  
  next();
};

/**
 * Middleware d'alerte en temps réel
 */
const realTimeAlerts = (req, res, next) => {
  // Vérifier les seuils d'alerte
  const now = Date.now();
  const oneMinuteAgo = now - 60000;
  
  // Compter les erreurs de la dernière minute
  const recentErrors = getRecentErrors(oneMinuteAgo);
  
  if (recentErrors > 10) {
    logger.error('Pic d\'erreurs détecté', {
      errorCount: recentErrors,
      timeWindow: '1 minute'
    });
    
    sendAlert('HIGH_ERROR_RATE', {
      errorCount: recentErrors,
      timeWindow: '1 minute',
      timestamp: new Date().toISOString()
    });
  }
  
  // Vérifier l'utilisation mémoire
  const memUsage = process.memoryUsage();
  const memUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
  
  if (memUsagePercent > 90) {
    logger.warn('Utilisation mémoire élevée', {
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      percentage: `${memUsagePercent.toFixed(2)}%`
    });
    
    sendAlert('HIGH_MEMORY_USAGE', {
      percentage: memUsagePercent,
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal
    });
  }
  
  next();
};

/**
 * Fonctions utilitaires
 */

async function recordPerformanceMetric(endpoint, method, duration, statusCode) {
  try {
    // En production, on pourrait utiliser une base de données de métriques
    // comme InfluxDB ou envoyer vers un service de monitoring
    
    // Pour l'instant, on log simplement
    if (duration > 500) { // Plus de 500ms
      logger.info('Métrique de performance', {
        endpoint: endpoint,
        method: method,
        duration: `${duration.toFixed(2)}ms`,
        statusCode: statusCode,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    logger.error('Erreur enregistrement métrique:', error);
  }
}

function getRecentErrors(since) {
  // Simuler le comptage des erreurs récentes
  // En production, cela viendrait d'une base de données ou cache
  return Math.floor(Math.random() * 5); // Simulation
}

function sendAlert(type, data) {
  // Simuler l'envoi d'alerte
  // En production, cela pourrait envoyer vers Slack, email, PagerDuty, etc.
  logger.error(`ALERTE ${type}`, data);
  
  // Exemple d'intégration webhook
  if (process.env.WEBHOOK_URL) {
    // fetch(process.env.WEBHOOK_URL, {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ type, data, timestamp: new Date().toISOString() })
    // }).catch(err => logger.error('Erreur envoi webhook:', err));
  }
}

/**
 * Middleware pour exposer les métriques (format Prometheus)
 */
const exposeMetrics = (req, res, next) => {
  if (req.path === '/metrics' && req.method === 'GET') {
    const prometheusMetrics = `
# HELP anonymous_requests_total Total number of requests to anonymous reservation endpoints
# TYPE anonymous_requests_total counter
anonymous_requests_total ${metrics.requests.total}

# HELP anonymous_requests_success_total Total number of successful requests
# TYPE anonymous_requests_success_total counter
anonymous_requests_success_total ${metrics.requests.success}

# HELP anonymous_requests_errors_total Total number of error requests
# TYPE anonymous_requests_errors_total counter
anonymous_requests_errors_total ${metrics.requests.errors}

# HELP anonymous_response_time_avg Average response time in milliseconds
# TYPE anonymous_response_time_avg gauge
anonymous_response_time_avg ${metrics.performance.averageResponseTime}

# HELP anonymous_slow_requests_total Total number of slow requests (>2s)
# TYPE anonymous_slow_requests_total counter
anonymous_slow_requests_total ${metrics.performance.slowRequests}

# HELP anonymous_blocked_requests_total Total number of blocked requests
# TYPE anonymous_blocked_requests_total counter
anonymous_blocked_requests_total ${metrics.security.blockedRequests}
`;

    res.set('Content-Type', 'text/plain');
    return res.send(prometheusMetrics);
  }
  
  next();
};

/**
 * Fonction pour obtenir les métriques actuelles
 */
function getMetrics() {
  return {
    ...metrics,
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  };
}

/**
 * Fonction pour réinitialiser les métriques
 */
function resetMetrics() {
  metrics.requests.total = 0;
  metrics.requests.success = 0;
  metrics.requests.errors = 0;
  metrics.requests.byEndpoint.clear();
  metrics.requests.byStatus.clear();
  
  metrics.performance.totalResponseTime = 0;
  metrics.performance.requestCount = 0;
  metrics.performance.slowRequests = 0;
  metrics.performance.averageResponseTime = 0;
  
  metrics.security.blockedRequests = 0;
  metrics.security.suspiciousActivity = 0;
  metrics.security.rateLimitHits = 0;
  
  logger.info('Métriques réinitialisées');
}

// Réinitialiser les métriques toutes les 24 heures
setInterval(resetMetrics, 24 * 60 * 60 * 1000);

module.exports = {
  collectMetrics,
  healthCheck,
  errorMonitoring,
  performanceMonitoring,
  realTimeAlerts,
  exposeMetrics,
  
  // Utilitaires
  getMetrics,
  resetMetrics,
  recordPerformanceMetric,
  sendAlert
};
