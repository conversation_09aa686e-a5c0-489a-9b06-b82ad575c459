const logger = require('../logger');

/**
 * Middleware d'optimisation pour les analytics POS
 * Gère le cache intelligent, la compression et l'optimisation des requêtes
 */

/**
 * Middleware de validation des paramètres de date
 */
const validateDateParams = (req, res, next) => {
  try {
    const { date_debut, date_fin } = req.query;

    // Valider le format des dates si fournies
    if (date_debut && !isValidDate(date_debut)) {
      return res.status(400).json({
        success: false,
        message: 'Format de date_debut invalide (YYYY-MM-DD attendu)',
        code: 'INVALID_DATE_FORMAT'
      });
    }

    if (date_fin && !isValidDate(date_fin)) {
      return res.status(400).json({
        success: false,
        message: 'Format de date_fin invalide (YYYY-MM-DD attendu)',
        code: 'INVALID_DATE_FORMAT'
      });
    }

    // Vérifier que date_debut <= date_fin
    if (date_debut && date_fin && new Date(date_debut) > new Date(date_fin)) {
      return res.status(400).json({
        success: false,
        message: 'La date de début doit être antérieure à la date de fin',
        code: 'INVALID_DATE_RANGE'
      });
    }

    // Vérifier que la période n'est pas trop longue (max 1 an)
    if (date_debut && date_fin) {
      const diffTime = Math.abs(new Date(date_fin) - new Date(date_debut));
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays > 365) {
        return res.status(400).json({
          success: false,
          message: 'La période d\'analyse ne peut pas dépasser 365 jours',
          code: 'DATE_RANGE_TOO_LARGE'
        });
      }
    }

    next();
  } catch (error) {
    logger.error('Erreur validation paramètres de date:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la validation des dates',
      code: 'DATE_VALIDATION_ERROR'
    });
  }
};

/**
 * Middleware de compression des réponses analytics
 */
const compressAnalyticsResponse = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    try {
      // Ajouter des headers de cache appropriés
      res.set({
        'Cache-Control': 'public, max-age=300', // 5 minutes
        'ETag': generateETag(data),
        'Last-Modified': new Date().toUTCString(),
        'Content-Type': 'application/json; charset=utf-8'
      });

      // Compresser les gros datasets
      if (typeof data === 'string' && data.length > 10000) {
        res.set('X-Data-Compressed', 'true');
      }

      originalSend.call(this, data);
    } catch (error) {
      logger.error('Erreur compression réponse analytics:', error);
      originalSend.call(this, data);
    }
  };

  next();
};

/**
 * Middleware de logging des performances analytics
 */
const logAnalyticsPerformance = (req, res, next) => {
  const startTime = Date.now();
  const { serviceId, complexeId } = req.params;
  const { date_debut, date_fin } = req.query;

  // Logger le début de la requête
  logger.info('[ANALYTICS] Début requête', {
    endpoint: req.originalUrl,
    method: req.method,
    serviceId,
    complexeId,
    periode: { date_debut, date_fin },
    user_id: req.user?.id,
    timestamp: new Date().toISOString()
  });

  // Intercepter la réponse pour mesurer les performances
  const originalSend = res.send;
  res.send = function(data) {
    const duration = Date.now() - startTime;
    const responseData = typeof data === 'string' ? JSON.parse(data) : data;
    
    // Logger les métriques de performance
    logger.info('[ANALYTICS] Fin requête', {
      endpoint: req.originalUrl,
      duration_ms: duration,
      success: responseData.success || false,
      data_size: JSON.stringify(data).length,
      status_code: res.statusCode,
      serviceId,
      complexeId
    });

    // Alerter si la requête est lente
    if (duration > 5000) {
      logger.warn('[ANALYTICS] Requête lente détectée', {
        endpoint: req.originalUrl,
        duration_ms: duration,
        serviceId,
        complexeId,
        action_requise: 'Optimisation nécessaire'
      });
    }

    originalSend.call(this, data);
  };

  next();
};

/**
 * Middleware de limitation des requêtes concurrentes
 */
const limitConcurrentAnalytics = (() => {
  const activeRequests = new Map();
  const maxConcurrent = 5;

  return (req, res, next) => {
    const userId = req.user?.id;
    const userRequests = activeRequests.get(userId) || 0;

    if (userRequests >= maxConcurrent) {
      return res.status(429).json({
        success: false,
        message: 'Trop de requêtes analytics simultanées',
        code: 'TOO_MANY_CONCURRENT_REQUESTS',
        retry_after: 30
      });
    }

    // Incrémenter le compteur
    activeRequests.set(userId, userRequests + 1);

    // Décrémenter à la fin de la requête
    res.on('finish', () => {
      const current = activeRequests.get(userId) || 0;
      if (current <= 1) {
        activeRequests.delete(userId);
      } else {
        activeRequests.set(userId, current - 1);
      }
    });

    next();
  };
})();

/**
 * Middleware de détection de cache hit/miss
 */
const detectCacheStatus = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    // Détecter si la réponse vient du cache
    const fromCache = res.get('X-Cache-Status') === 'HIT';
    
    if (fromCache) {
      logger.info('[ANALYTICS] Cache HIT', {
        endpoint: req.originalUrl,
        cache_key: res.get('X-Cache-Key'),
        user_id: req.user?.id
      });
    } else {
      logger.info('[ANALYTICS] Cache MISS', {
        endpoint: req.originalUrl,
        user_id: req.user?.id,
        note: 'Données calculées en temps réel'
      });
    }

    originalSend.call(this, data);
  };

  next();
};

/**
 * Middleware de formatage des réponses analytics
 */
const formatAnalyticsResponse = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    try {
      const responseData = typeof data === 'string' ? JSON.parse(data) : data;
      
      // Ajouter des métadonnées standard aux réponses analytics
      if (responseData.success && responseData.data) {
        const enhancedResponse = {
          ...responseData,
          metadata: {
            generated_at: new Date().toISOString(),
            endpoint: req.originalUrl,
            version: '1.0.0',
            cache_status: res.get('X-Cache-Status') || 'MISS',
            processing_time: res.get('X-Response-Time'),
            data_freshness: calculateDataFreshness(req.query)
          }
        };

        originalSend.call(this, JSON.stringify(enhancedResponse));
      } else {
        originalSend.call(this, data);
      }
    } catch (error) {
      logger.error('Erreur formatage réponse analytics:', error);
      originalSend.call(this, data);
    }
  };

  next();
};

/**
 * Middleware de validation des permissions analytics
 */
const validateAnalyticsPermissions = (req, res, next) => {
  const { serviceId, complexeId } = req.params;
  const userComplexeId = req.user?.complexe_id;
  const userRole = req.user?.role;

  // Vérifier que l'utilisateur a accès au complexe
  if (complexeId && userComplexeId && parseInt(complexeId) !== userComplexeId) {
    if (userRole !== 'admin_chaine') {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé à ce complexe',
        code: 'UNAUTHORIZED_COMPLEXE_ACCESS'
      });
    }
  }

  // Ajouter des informations de contexte à la requête
  req.analyticsContext = {
    serviceId: serviceId ? parseInt(serviceId) : null,
    complexeId: complexeId ? parseInt(complexeId) : userComplexeId,
    userRole,
    accessLevel: determineAccessLevel(userRole)
  };

  next();
};

// Fonctions utilitaires
function isValidDate(dateString) {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(dateString)) return false;
  
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date);
}

function generateETag(data) {
  const crypto = require('crypto');
  return crypto.createHash('md5').update(JSON.stringify(data)).digest('hex');
}

function calculateDataFreshness(query) {
  const { date_fin } = query;
  if (!date_fin) return 'real-time';
  
  const endDate = new Date(date_fin);
  const now = new Date();
  const diffHours = Math.abs(now - endDate) / (1000 * 60 * 60);
  
  if (diffHours < 1) return 'real-time';
  if (diffHours < 24) return 'recent';
  if (diffHours < 168) return 'weekly';
  return 'historical';
}

function determineAccessLevel(role) {
  switch (role) {
    case 'admin_chaine': return 'full';
    case 'admin_complexe': return 'complexe';
    case 'manager': return 'service';
    default: return 'limited';
  }
}

module.exports = {
  validateDateParams,
  compressAnalyticsResponse,
  logAnalyticsPerformance,
  limitConcurrentAnalytics,
  detectCacheStatus,
  formatAnalyticsResponse,
  validateAnalyticsPermissions
};
