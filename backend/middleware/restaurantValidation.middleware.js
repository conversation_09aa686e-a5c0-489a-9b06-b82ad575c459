const TableService = require('../services/table.service');
const POSStockIntegration = require('../services/posStockIntegration.service');
const POSService = require('../services/pos.service');
const SessionService = require('../services/session.service');
const logger = require('../logger');

/**
 * Middleware de validation pour les commandes restaurant/bar
 * Vérifie la disponibilité des tables et du stock avant de traiter les commandes
 */

/**
 * Valider une commande restaurant avec vérification table et stock
 */
const validateRestaurantCommande = async (req, res, next) => {
  try {
    const { table_id, items = [], service_id } = req.body;
    const { complexe_id } = req.user;

    logger.info('Validation commande restaurant', { 
      table_id, 
      service_id, 
      items_count: items.length 
    });

    // 1. Vérifier que la table existe et est disponible (si spécifiée)
    if (table_id) {
      const tableResult = await TableService.getTableById(table_id);
      
      if (!tableResult.success) {
        return res.status(404).json({
          success: false,
          message: 'Table non trouvée',
          code: 'TABLE_NOT_FOUND'
        });
      }

      const table = tableResult.data;

      // Vérifier que la table appartient au bon service
      if (table.service_id !== service_id) {
        return res.status(400).json({
          success: false,
          message: 'Table non associée à ce service',
          code: 'TABLE_SERVICE_MISMATCH'
        });
      }

      // Vérifier le statut de la table
      if (table.statut === 'Hors service') {
        return res.status(400).json({
          success: false,
          message: 'Table hors service',
          code: 'TABLE_OUT_OF_SERVICE'
        });
      }

      if (table.statut === 'Occupée') {
        return res.status(400).json({
          success: false,
          message: 'Table déjà occupée',
          code: 'TABLE_OCCUPIED'
        });
      }

      // Ajouter les infos de la table à la requête pour usage ultérieur
      req.tableInfo = table;
    }

    // 2. Vérifier la disponibilité des ingrédients pour tous les items
    if (items && items.length > 0) {
      // Préparer les items pour la vérification
      const itemsToCheck = items.map(item => ({
        produit_id: item.produit_id,
        quantite: item.quantite || 1,
        nom: item.nom || `Produit ${item.produit_id}`
      }));

      // Vérifier la disponibilité de tous les items
      const availabilityResult = await POSStockIntegration.checkIngredientAvailability(
        itemsToCheck[0].produit_id,
        itemsToCheck[0].quantite
      );

      // Vérifier chaque item individuellement
      const unavailableItems = [];
      const stockAlerts = [];

      for (const item of itemsToCheck) {
        const stockCheck = await POSStockIntegration.checkIngredientAvailability(
          item.produit_id,
          item.quantite
        );

        if (!stockCheck.success || !stockCheck.data.disponible) {
          unavailableItems.push({
            produit_id: item.produit_id,
            nom: item.nom,
            quantite: item.quantite,
            raison: stockCheck.message || 'Stock insuffisant'
          });
        }

        // Collecter les alertes de stock
        if (stockCheck.data && stockCheck.data.alertes) {
          stockAlerts.push(...stockCheck.data.alertes);
        }
      }

      // Si des items ne sont pas disponibles, bloquer la commande
      if (unavailableItems.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Certains produits ne sont pas disponibles en stock',
          code: 'INSUFFICIENT_STOCK',
          details: {
            unavailable_items: unavailableItems,
            available_items_count: itemsToCheck.length - unavailableItems.length,
            total_items_count: itemsToCheck.length
          }
        });
      }

      // Ajouter les alertes de stock à la requête pour information
      if (stockAlerts.length > 0) {
        req.stockAlerts = stockAlerts;
        logger.warn('Alertes de stock détectées', { 
          alertes_count: stockAlerts.length,
          service_id 
        });
      }

      // Ajouter les infos de stock à la requête
      req.stockValidation = {
        items_checked: itemsToCheck.length,
        all_available: true,
        alerts_count: stockAlerts.length
      };
    }

    // 3. Validation réussie, passer au middleware suivant
    logger.info('Validation commande restaurant réussie', {
      table_id,
      service_id,
      items_validated: items.length,
      stock_alerts: req.stockAlerts ? req.stockAlerts.length : 0
    });

    next();

  } catch (error) {
    logger.error('Erreur validation commande restaurant:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la validation de la commande',
      code: 'VALIDATION_ERROR'
    });
  }
};

/**
 * Valider l'ajout d'un item à une commande existante
 */
const validateAddItemToCommande = async (req, res, next) => {
  try {
    const { produit_id, quantite = 1 } = req.body;
    const { id: commandeId } = req.params;

    logger.info('Validation ajout item à commande', { 
      commande_id: commandeId, 
      produit_id, 
      quantite 
    });

    // Vérifier la disponibilité du produit en stock
    const stockCheck = await POSStockIntegration.checkIngredientAvailability(
      produit_id,
      quantite
    );

    if (!stockCheck.success || !stockCheck.data.disponible) {
      return res.status(400).json({
        success: false,
        message: 'Produit non disponible en stock',
        code: 'INSUFFICIENT_STOCK',
        details: {
          produit_id,
          quantite_demandee: quantite,
          raison: stockCheck.message || 'Stock insuffisant'
        }
      });
    }

    // Ajouter les infos de validation à la requête
    req.itemValidation = {
      produit_id,
      quantite,
      stock_available: true,
      alerts: stockCheck.data.alertes || []
    };

    logger.info('Validation ajout item réussie', { 
      commande_id: commandeId, 
      produit_id 
    });

    next();

  } catch (error) {
    logger.error('Erreur validation ajout item:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la validation de l\'item',
      code: 'ITEM_VALIDATION_ERROR'
    });
  }
};

/**
 * Valider la modification de quantité d'un item
 */
const validateUpdateItemQuantity = async (req, res, next) => {
  try {
    const { quantite } = req.body;
    const { id: commandeId, itemId } = req.params;

    if (!quantite || quantite <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Quantité invalide',
        code: 'INVALID_QUANTITY'
      });
    }

    // TODO: Récupérer l'item existant et vérifier la différence de stock
    // Pour l'instant, on passe la validation
    req.quantityValidation = {
      nouvelle_quantite: quantite,
      validation_passed: true
    };

    next();

  } catch (error) {
    logger.error('Erreur validation modification quantité:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la validation de la quantité',
      code: 'QUANTITY_VALIDATION_ERROR'
    });
  }
};

/**
 * Valider le changement de statut d'une table
 */
const validateTableStatusChange = async (req, res, next) => {
  try {
    const { statut } = req.body;
    const { id: tableId } = req.params;

    const statutsValides = ['Libre', 'Occupée', 'Réservée', 'Hors service'];

    if (!statutsValides.includes(statut)) {
      return res.status(400).json({
        success: false,
        message: 'Statut de table invalide',
        code: 'INVALID_TABLE_STATUS',
        details: {
          statut_fourni: statut,
          statuts_valides: statutsValides
        }
      });
    }

    req.tableStatusValidation = {
      nouveau_statut: statut,
      validation_passed: true
    };

    next();

  } catch (error) {
    logger.error('Erreur validation changement statut table:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la validation du statut',
      code: 'TABLE_STATUS_VALIDATION_ERROR'
    });
  }
};

/**
 * Valider qu'une session POS est ouverte pour le service
 */
const validatePOSSession = async (req, res, next) => {
  try {
    const { service_id } = req.body;
    const { employe_id, complexe_id } = req.user;

    if (!service_id) {
      return res.status(400).json({
        success: false,
        message: 'ID du service requis',
        code: 'SERVICE_ID_REQUIRED'
      });
    }

    // Récupérer les POS du service
    const posResult = await POSService.getPOSByService(service_id);

    if (!posResult.success || posResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Aucun point de vente trouvé pour ce service',
        code: 'NO_POS_FOUND'
      });
    }

    // Vérifier qu'il y a au moins une session ouverte pour cet employé
    const activePOS = posResult.data.find(pos =>
      pos.caisse_ouverte && pos.employe_actuel_id === employe_id
    );

    if (!activePOS) {
      return res.status(403).json({
        success: false,
        message: 'Aucune session de caisse ouverte pour cet employé sur ce service',
        code: 'NO_ACTIVE_SESSION',
        details: {
          service_id,
          employe_id,
          action_requise: 'Ouvrir une session de caisse'
        }
      });
    }

    // Ajouter les infos de session à la requête
    req.posSession = {
      pos_id: activePOS.pos_id,
      session_active: true,
      employe_id,
      service_id
    };

    next();

  } catch (error) {
    logger.error('Erreur validation session POS:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la validation de la session POS',
      code: 'POS_SESSION_VALIDATION_ERROR'
    });
  }
};

/**
 * Valider les permissions spécifiques au type de service (Restaurant vs Bar)
 */
const validateServicePermissions = async (req, res, next) => {
  try {
    const { service_id } = req.body;
    const userPermissions = req.user.permissions || [];

    // Récupérer les infos du service pour déterminer le type
    // Pour l'instant, on assume que les permissions sont déjà vérifiées
    // Cette validation peut être étendue selon les besoins spécifiques

    req.servicePermissions = {
      service_id,
      permissions_validated: true
    };

    next();

  } catch (error) {
    logger.error('Erreur validation permissions service:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la validation des permissions',
      code: 'SERVICE_PERMISSIONS_ERROR'
    });
  }
};

/**
 * Middleware combiné pour validation complète restaurant
 */
const validateCompleteRestaurantOperation = async (req, res, next) => {
  try {
    // Chaîner les validations : Session POS -> Permissions -> Commande
    await validatePOSSession(req, res, async () => {
      await validateServicePermissions(req, res, async () => {
        await validateRestaurantCommande(req, res, next);
      });
    });
  } catch (error) {
    logger.error('Erreur validation complète restaurant:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la validation complète',
      code: 'COMPLETE_VALIDATION_ERROR'
    });
  }
};

module.exports = {
  validateRestaurantCommande,
  validateAddItemToCommande,
  validateUpdateItemQuantity,
  validateTableStatusChange,
  validatePOSSession,
  validateServicePermissions,
  validateCompleteRestaurantOperation
};
