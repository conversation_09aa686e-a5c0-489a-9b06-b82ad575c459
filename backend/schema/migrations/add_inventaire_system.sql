-- Migration: Ajout du système d'inventaire et d'import Excel
-- Phase 1: Extension du schéma de base de données
-- Date: 2024

-- =====================================================
-- 1. NOUVELLES TABLES POUR LE SYSTÈME D'INVENTAIRE
-- =====================================================

-- Table des Ingrédients
CREATE TABLE "Ingredients" (
  "ingredient_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "chaine_id" integer NOT NULL,
  "complexe_id" integer,
  "nom" varchar NOT NULL,
  "description" text,
  "unite_mesure" varchar NOT NULL, -- kg, L, unité, pièce
  "categorie" varchar NOT NULL, -- L<PERSON>gumes, <PERSON>ndes, Boissons, Épices
  "code_barre" varchar UNIQUE,
  "prix_unitaire_moyen" decimal DEFAULT 0,
  "stock_initial" decimal DEFAULT 0, -- Stock de départ lors de l'import
  "stock_actuel" decimal DEFAULT 0, -- Stock actuel calculé
  "stock_minimal" decimal DEFAULT 0, -- Seuil d'alerte
  "stock_maximal" decimal DEFAULT 0, -- Stock maximum recommandé
  "fournisseur_principal_id" integer,
  "allergenes" json, -- ["gluten", "lactose", "noix"]
  "conservation" varchar, -- Frais, Congelé, Sec, Ambiant
  "duree_conservation_jours" integer,
  "actif" boolean DEFAULT true,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp,
  FOREIGN KEY (chaine_id) REFERENCES "ChainesHotelieres" (chaine_id),
  FOREIGN KEY (complexe_id) REFERENCES "ComplexesHoteliers" (complexe_id),
  FOREIGN KEY (fournisseur_principal_id) REFERENCES "Fournisseurs" (fournisseur_id)
);

-- Table des Recettes
CREATE TABLE "Recettes" (
  "recette_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "produit_id" integer NOT NULL,
  "service_id" integer NOT NULL,
  "nom_recette" varchar NOT NULL,
  "description" text,
  "instructions" text,
  "temps_preparation" integer, -- en minutes
  "nombre_portions" integer DEFAULT 1,
  "cout_total_calcule" decimal DEFAULT 0,
  "marge_beneficiaire_cible" decimal DEFAULT 0,
  "prix_vente_suggere" decimal DEFAULT 0,
  "actif" boolean DEFAULT true,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp,
  FOREIGN KEY (produit_id) REFERENCES "Produits" (produit_id),
  FOREIGN KEY (service_id) REFERENCES "ServicesComplexe" (service_id)
);

-- Table de Liaison Recettes-Ingrédients
CREATE TABLE "RecettesIngredients" (
  "recette_ingredient_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "recette_id" integer NOT NULL,
  "ingredient_id" integer NOT NULL,
  "quantite_necessaire" decimal NOT NULL,
  "unite_mesure" varchar NOT NULL,
  "cout_unitaire" decimal NOT NULL DEFAULT 0,
  "cout_total" decimal GENERATED ALWAYS AS (quantite_necessaire * cout_unitaire) STORED,
  "optionnel" boolean DEFAULT false,
  "notes" text,
  "ordre_ajout" integer, -- Pour les instructions de préparation
  FOREIGN KEY (recette_id) REFERENCES "Recettes" (recette_id),
  FOREIGN KEY (ingredient_id) REFERENCES "Ingredients" (ingredient_id)
);

-- Table des Imports Excel
CREATE TABLE "ImportsExcel" (
  "import_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "complexe_id" integer NOT NULL,
  "service_id" integer,
  "employe_id" integer NOT NULL,
  "type_import" varchar NOT NULL, -- MENU_RESTAURANT, CARTE_BAR, INVENTAIRE_INGREDIENTS
  "nom_fichier" varchar NOT NULL,
  "chemin_fichier" varchar NOT NULL,
  "taille_fichier" integer,
  "statut" varchar NOT NULL DEFAULT 'EN_COURS', -- EN_COURS, VALIDE, ERREUR, IMPORTE, ANNULE
  "nombre_lignes_total" integer DEFAULT 0,
  "nombre_lignes_valides" integer DEFAULT 0,
  "nombre_erreurs" integer DEFAULT 0,
  "donnees_parsees" json, -- Contenu du fichier Excel parsé
  "erreurs_detectees" json, -- Erreurs par ligne avec détails
  "mapping_colonnes" json, -- Correspondance colonnes Excel -> champs DB
  "parametres_import" json, -- Options spécifiques à l'import
  "date_import" timestamp DEFAULT (now()),
  "date_traitement" timestamp,
  "date_finalisation" timestamp,
  "notes" text,
  FOREIGN KEY (complexe_id) REFERENCES "ComplexesHoteliers" (complexe_id),
  FOREIGN KEY (service_id) REFERENCES "ServicesComplexe" (service_id),
  FOREIGN KEY (employe_id) REFERENCES "Employes" (employe_id)
);

-- Table des Templates d'Import
CREATE TABLE "TemplatesImport" (
  "template_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "chaine_id" integer NOT NULL,
  "type_service" varchar NOT NULL, -- Restaurant, Bar
  "type_import" varchar NOT NULL, -- MENU, CARTE, INVENTAIRE
  "nom_template" varchar NOT NULL,
  "description" text,
  "colonnes_requises" json NOT NULL, -- Structure attendue du fichier Excel
  "exemple_donnees" json, -- Données d'exemple pour le template
  "regles_validation" json, -- Règles de validation spécifiques
  "mapping_defaut" json, -- Mapping par défaut des colonnes
  "actif" boolean DEFAULT true,
  "version" varchar DEFAULT '1.0',
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp,
  FOREIGN KEY (chaine_id) REFERENCES "ChainesHotelieres" (chaine_id)
);

-- =====================================================
-- 2. EXTENSIONS DES TABLES EXISTANTES
-- =====================================================

-- Extension de la table Produits
ALTER TABLE "Produits" ADD COLUMN "recette_id" integer;
ALTER TABLE "Produits" ADD COLUMN "cout_ingredients" decimal DEFAULT 0;
ALTER TABLE "Produits" ADD COLUMN "marge_calculee" decimal DEFAULT 0;
ALTER TABLE "Produits" ADD COLUMN "import_id" integer; -- Traçabilité de l'import
ALTER TABLE "Produits" ADD FOREIGN KEY ("recette_id") REFERENCES "Recettes" ("recette_id");
ALTER TABLE "Produits" ADD FOREIGN KEY ("import_id") REFERENCES "ImportsExcel" ("import_id");

-- Extension de la table MouvementsStock
ALTER TABLE "MouvementsStock" ADD COLUMN "import_id" integer;
ALTER TABLE "MouvementsStock" ADD COLUMN "recette_id" integer;
ALTER TABLE "MouvementsStock" ADD COLUMN "cout_unitaire_import" decimal;
ALTER TABLE "MouvementsStock" ADD FOREIGN KEY ("import_id") REFERENCES "ImportsExcel" ("import_id");
ALTER TABLE "MouvementsStock" ADD FOREIGN KEY ("recette_id") REFERENCES "Recettes" ("recette_id");

-- Extension de la table LignesInventaire
ALTER TABLE "LignesInventaire" ADD COLUMN "ingredient_id" integer;
ALTER TABLE "LignesInventaire" ADD COLUMN "prix_unitaire_reel" decimal;
ALTER TABLE "LignesInventaire" ADD FOREIGN KEY ("ingredient_id") REFERENCES "Ingredients" ("ingredient_id");

-- =====================================================
-- 3. INDEX POUR LES PERFORMANCES
-- =====================================================

-- Index pour les ingrédients
CREATE INDEX idx_ingredients_complexe ON "Ingredients" (complexe_id);
CREATE INDEX idx_ingredients_categorie ON "Ingredients" (categorie);
CREATE INDEX idx_ingredients_actif ON "Ingredients" (actif);
CREATE INDEX idx_ingredients_code_barre ON "Ingredients" (code_barre);
CREATE INDEX idx_ingredients_fournisseur ON "Ingredients" (fournisseur_principal_id);

-- Index pour les recettes
CREATE INDEX idx_recettes_service ON "Recettes" (service_id);
CREATE INDEX idx_recettes_produit ON "Recettes" (produit_id);
CREATE INDEX idx_recettes_actif ON "Recettes" (actif);

-- Index pour les liaisons recettes-ingrédients
CREATE INDEX idx_recettes_ingredients_recette ON "RecettesIngredients" (recette_id);
CREATE INDEX idx_recettes_ingredients_ingredient ON "RecettesIngredients" (ingredient_id);

-- Index pour les imports Excel
CREATE INDEX idx_imports_statut ON "ImportsExcel" (statut);
CREATE INDEX idx_imports_date ON "ImportsExcel" (date_import);
CREATE INDEX idx_imports_complexe ON "ImportsExcel" (complexe_id);
CREATE INDEX idx_imports_service ON "ImportsExcel" (service_id);
CREATE INDEX idx_imports_employe ON "ImportsExcel" (employe_id);
CREATE INDEX idx_imports_type ON "ImportsExcel" (type_import);

-- Index pour les templates d'import
CREATE INDEX idx_templates_chaine ON "TemplatesImport" (chaine_id);
CREATE INDEX idx_templates_type_service ON "TemplatesImport" (type_service);
CREATE INDEX idx_templates_type_import ON "TemplatesImport" (type_import);
CREATE INDEX idx_templates_actif ON "TemplatesImport" (actif);

-- =====================================================
-- 4. CONTRAINTES MÉTIER
-- =====================================================

-- Contraintes pour les imports Excel
ALTER TABLE "ImportsExcel" ADD CONSTRAINT check_statut_import
  CHECK (statut IN ('EN_COURS', 'VALIDE', 'ERREUR', 'IMPORTE', 'ANNULE'));

ALTER TABLE "ImportsExcel" ADD CONSTRAINT check_type_import
  CHECK (type_import IN ('MENU_RESTAURANT', 'CARTE_BAR', 'INVENTAIRE_INGREDIENTS'));

-- Contraintes pour les ingrédients
ALTER TABLE "Ingredients" ADD CONSTRAINT check_unite_mesure
  CHECK (unite_mesure IN ('kg', 'g', 'L', 'mL', 'unité', 'pièce', 'portion'));

ALTER TABLE "Ingredients" ADD CONSTRAINT check_conservation
  CHECK (conservation IN ('Frais', 'Congelé', 'Sec', 'Ambiant'));

ALTER TABLE "Ingredients" ADD CONSTRAINT check_prix_positif
  CHECK (prix_unitaire_moyen >= 0);

ALTER TABLE "Ingredients" ADD CONSTRAINT check_duree_conservation_positive
  CHECK (duree_conservation_jours > 0);

-- Contraintes pour les recettes
ALTER TABLE "Recettes" ADD CONSTRAINT check_temps_preparation_positif
  CHECK (temps_preparation > 0);

ALTER TABLE "Recettes" ADD CONSTRAINT check_nombre_portions_positif
  CHECK (nombre_portions > 0);

ALTER TABLE "Recettes" ADD CONSTRAINT check_cout_positif
  CHECK (cout_total_calcule >= 0);

ALTER TABLE "Recettes" ADD CONSTRAINT check_marge_positive
  CHECK (marge_beneficiaire_cible >= 0);

ALTER TABLE "Recettes" ADD CONSTRAINT check_prix_suggere_positif
  CHECK (prix_vente_suggere >= 0);

-- Contraintes pour les liaisons recettes-ingrédients
ALTER TABLE "RecettesIngredients" ADD CONSTRAINT check_quantite_positive
  CHECK (quantite_necessaire > 0);

ALTER TABLE "RecettesIngredients" ADD CONSTRAINT check_cout_unitaire_positif
  CHECK (cout_unitaire >= 0);

-- Contraintes pour les templates
ALTER TABLE "TemplatesImport" ADD CONSTRAINT check_type_service_template
  CHECK (type_service IN ('Restaurant', 'Bar', 'Piscine'));

ALTER TABLE "TemplatesImport" ADD CONSTRAINT check_type_import_template
  CHECK (type_import IN ('MENU', 'CARTE', 'INVENTAIRE'));

-- =====================================================
-- 5. COMMENTAIRES SUR LES COLONNES
-- =====================================================

-- Commentaires pour les ingrédients
COMMENT ON COLUMN "Ingredients"."unite_mesure" IS 'Unité de mesure: kg, g, L, mL, unité, pièce, portion';
COMMENT ON COLUMN "Ingredients"."categorie" IS 'Catégorie: Légumes, Viandes, Boissons, Épices, Produits laitiers, Céréales, etc.';
COMMENT ON COLUMN "Ingredients"."conservation" IS 'Type de conservation: Frais, Congelé, Sec, Ambiant';
COMMENT ON COLUMN "Ingredients"."allergenes" IS 'Liste des allergènes en format JSON: ["gluten", "lactose", "noix"]';

-- Commentaires pour les recettes
COMMENT ON COLUMN "Recettes"."temps_preparation" IS 'Temps de préparation en minutes';
COMMENT ON COLUMN "Recettes"."cout_total_calcule" IS 'Coût total calculé automatiquement basé sur les ingrédients';
COMMENT ON COLUMN "Recettes"."marge_beneficiaire_cible" IS 'Marge bénéficiaire cible en pourcentage';

-- Commentaires pour les imports
COMMENT ON COLUMN "ImportsExcel"."type_import" IS 'Type d''import: MENU_RESTAURANT, CARTE_BAR, INVENTAIRE_INGREDIENTS';
COMMENT ON COLUMN "ImportsExcel"."statut" IS 'Statut: EN_COURS, VALIDE, ERREUR, IMPORTE, ANNULE';
COMMENT ON COLUMN "ImportsExcel"."donnees_parsees" IS 'Contenu du fichier Excel parsé en format JSON';
COMMENT ON COLUMN "ImportsExcel"."erreurs_detectees" IS 'Erreurs détectées par ligne avec détails en format JSON';
COMMENT ON COLUMN "ImportsExcel"."mapping_colonnes" IS 'Correspondance entre colonnes Excel et champs de base de données';

-- Commentaires pour les templates
COMMENT ON COLUMN "TemplatesImport"."type_service" IS 'Type de service: Restaurant, Bar, Piscine';
COMMENT ON COLUMN "TemplatesImport"."type_import" IS 'Type d''import: MENU, CARTE, INVENTAIRE';
COMMENT ON COLUMN "TemplatesImport"."colonnes_requises" IS 'Structure attendue du fichier Excel en format JSON';
COMMENT ON COLUMN "TemplatesImport"."regles_validation" IS 'Règles de validation spécifiques en format JSON';

-- =====================================================
-- 6. VUES UTILES POUR LE SYSTÈME D'INVENTAIRE
-- =====================================================

-- Vue pour les coûts de recettes avec détails des ingrédients
CREATE VIEW "VueRecettesCouts" AS
SELECT
    r.recette_id,
    r.nom_recette,
    r.service_id,
    r.produit_id,
    r.nombre_portions,
    r.cout_total_calcule,
    r.prix_vente_suggere,
    r.marge_beneficiaire_cible,
    COUNT(ri.ingredient_id) as nombre_ingredients,
    SUM(ri.cout_total) as cout_ingredients_total,
    CASE
        WHEN r.prix_vente_suggere > 0 AND r.cout_total_calcule > 0
        THEN ((r.prix_vente_suggere - r.cout_total_calcule) / r.prix_vente_suggere * 100)
        ELSE 0
    END as marge_reelle_pourcentage
FROM "Recettes" r
LEFT JOIN "RecettesIngredients" ri ON r.recette_id = ri.recette_id
WHERE r.actif = true
GROUP BY r.recette_id, r.nom_recette, r.service_id, r.produit_id,
         r.nombre_portions, r.cout_total_calcule, r.prix_vente_suggere, r.marge_beneficiaire_cible;

-- Vue pour le stock des ingrédients avec valeurs
CREATE VIEW "VueStockIngredients" AS
SELECT
    i.ingredient_id,
    i.nom as nom_ingredient,
    i.categorie,
    i.unite_mesure,
    i.prix_unitaire_moyen,
    i.complexe_id,
    COALESCE(SUM(CASE WHEN ms.type_mouvement IN ('Réception', 'Ajustement') THEN ms.quantite ELSE 0 END), 0) as entrees,
    COALESCE(SUM(CASE WHEN ms.type_mouvement IN ('Vente', 'Perte', 'Transfert') THEN ms.quantite ELSE 0 END), 0) as sorties,
    COALESCE(SUM(CASE WHEN ms.type_mouvement IN ('Réception', 'Ajustement') THEN ms.quantite ELSE -ms.quantite END), 0) as stock_actuel,
    COALESCE(SUM(CASE WHEN ms.type_mouvement IN ('Réception', 'Ajustement') THEN ms.quantite ELSE -ms.quantite END), 0) * i.prix_unitaire_moyen as valeur_stock
FROM "Ingredients" i
LEFT JOIN "MouvementsStock" ms ON i.ingredient_id = ms.produit_id
WHERE i.actif = true
GROUP BY i.ingredient_id, i.nom, i.categorie, i.unite_mesure, i.prix_unitaire_moyen, i.complexe_id;

-- Vue pour les statistiques d'imports
CREATE VIEW "VueStatistiquesImports" AS
SELECT
    ie.complexe_id,
    ie.service_id,
    ie.type_import,
    COUNT(*) as nombre_imports,
    COUNT(CASE WHEN ie.statut = 'IMPORTE' THEN 1 END) as imports_reussis,
    COUNT(CASE WHEN ie.statut = 'ERREUR' THEN 1 END) as imports_echecs,
    AVG(ie.nombre_lignes_total) as moyenne_lignes_par_import,
    AVG(ie.nombre_lignes_valides) as moyenne_lignes_valides,
    MAX(ie.date_import) as dernier_import,
    CASE
        WHEN COUNT(*) > 0
        THEN (COUNT(CASE WHEN ie.statut = 'IMPORTE' THEN 1 END) * 100.0 / COUNT(*))
        ELSE 0
    END as taux_succes_pourcentage
FROM "ImportsExcel" ie
GROUP BY ie.complexe_id, ie.service_id, ie.type_import;

-- =====================================================
-- 7. DONNÉES DE BASE POUR LES TEMPLATES
-- =====================================================

-- Template de base pour menu restaurant
INSERT INTO "TemplatesImport" (
    chaine_id, type_service, type_import, nom_template, description,
    colonnes_requises, exemple_donnees, regles_validation, mapping_defaut
) VALUES (
    1, -- À adapter selon la chaîne
    'Restaurant',
    'MENU',
    'Template Menu Restaurant Standard',
    'Template standard pour l''import de menus de restaurant avec recettes et ingrédients',
    '{"colonnes": ["nom_produit", "categorie", "prix_vente", "description", "ingredients", "quantites", "unites"]}',
    '{"exemple": [{"nom_produit": "Salade César", "categorie": "Entrées", "prix_vente": 12.50, "description": "Salade avec croûtons et parmesan", "ingredients": "Salade,Croûtons,Parmesan", "quantites": "100,20,30", "unites": "g,g,g"}]}',
    '{"regles": {"nom_produit": {"required": true, "type": "string", "max_length": 255}, "prix_vente": {"required": true, "type": "decimal", "min": 0}}}',
    '{"nom_produit": "A", "categorie": "B", "prix_vente": "C", "description": "D", "ingredients": "E", "quantites": "F", "unites": "G"}'
);

-- Template de base pour carte bar
INSERT INTO "TemplatesImport" (
    chaine_id, type_service, type_import, nom_template, description,
    colonnes_requises, exemple_donnees, regles_validation, mapping_defaut
) VALUES (
    1, -- À adapter selon la chaîne
    'Bar',
    'CARTE',
    'Template Carte Bar Standard',
    'Template standard pour l''import de cartes de bar avec cocktails et ingrédients',
    '{"colonnes": ["nom_boisson", "categorie", "prix_vente", "degre_alcool", "ingredients", "quantites", "unites"]}',
    '{"exemple": [{"nom_boisson": "Mojito", "categorie": "Cocktails", "prix_vente": 8.50, "degre_alcool": 12, "ingredients": "Rhum blanc,Menthe,Citron vert", "quantites": "50,10,20", "unites": "mL,g,mL"}]}',
    '{"regles": {"nom_boisson": {"required": true, "type": "string", "max_length": 255}, "prix_vente": {"required": true, "type": "decimal", "min": 0}, "degre_alcool": {"type": "decimal", "min": 0, "max": 100}}}',
    '{"nom_boisson": "A", "categorie": "B", "prix_vente": "C", "degre_alcool": "D", "ingredients": "E", "quantites": "F", "unites": "G"}'
);

-- Template de base pour inventaire ingrédients
INSERT INTO "TemplatesImport" (
    chaine_id, type_service, type_import, nom_template, description,
    colonnes_requises, exemple_donnees, regles_validation, mapping_defaut
) VALUES (
    1, -- À adapter selon la chaîne
    'Restaurant',
    'INVENTAIRE',
    'Template Inventaire Ingrédients',
    'Template pour l''import d''inventaire d''ingrédients avec stocks et prix',
    '{"colonnes": ["nom_ingredient", "categorie", "unite_mesure", "prix_unitaire", "stock_actuel", "stock_minimal", "fournisseur", "conservation"]}',
    '{"exemple": [{"nom_ingredient": "Tomates", "categorie": "Légumes", "unite_mesure": "kg", "prix_unitaire": 3.50, "stock_actuel": 25, "stock_minimal": 5, "fournisseur": "Maraîcher Local", "conservation": "Frais"}]}',
    '{"regles": {"nom_ingredient": {"required": true, "type": "string", "max_length": 255}, "prix_unitaire": {"required": true, "type": "decimal", "min": 0}, "stock_actuel": {"type": "integer", "min": 0}}}',
    '{"nom_ingredient": "A", "categorie": "B", "unite_mesure": "C", "prix_unitaire": "D", "stock_actuel": "E", "stock_minimal": "F", "fournisseur": "G", "conservation": "H"}'
);

-- =====================================================
-- 8. TRIGGERS POUR MISE À JOUR AUTOMATIQUE DES COÛTS
-- =====================================================

-- Fonction pour recalculer le coût d'une recette
CREATE OR REPLACE FUNCTION recalculer_cout_recette()
RETURNS TRIGGER AS $$
BEGIN
    -- Recalcul du coût total de la recette
    UPDATE "Recettes"
    SET cout_total_calcule = (
        SELECT COALESCE(SUM(ri.cout_total), 0)
        FROM "RecettesIngredients" ri
        WHERE ri.recette_id = COALESCE(NEW.recette_id, OLD.recette_id)
    ),
    updated_at = now()
    WHERE recette_id = COALESCE(NEW.recette_id, OLD.recette_id);

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger pour recalculer automatiquement les coûts
CREATE TRIGGER trigger_recalcul_cout_recette
    AFTER INSERT OR UPDATE OR DELETE ON "RecettesIngredients"
    FOR EACH ROW
    EXECUTE FUNCTION recalculer_cout_recette();

-- Fonction pour mettre à jour le coût des ingrédients dans les produits
CREATE OR REPLACE FUNCTION mettre_a_jour_cout_produit()
RETURNS TRIGGER AS $$
BEGIN
    -- Mise à jour du coût des ingrédients dans la table Produits
    UPDATE "Produits"
    SET cout_ingredients = NEW.cout_total_calcule,
        marge_calculee = CASE
            WHEN prix_vente_defaut > 0 AND NEW.cout_total_calcule > 0
            THEN ((prix_vente_defaut - NEW.cout_total_calcule) / prix_vente_defaut * 100)
            ELSE 0
        END,
        updated_at = now()
    WHERE produit_id = NEW.produit_id;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour mettre à jour les coûts dans les produits
CREATE TRIGGER trigger_maj_cout_produit
    AFTER UPDATE OF cout_total_calcule ON "Recettes"
    FOR EACH ROW
    EXECUTE FUNCTION mettre_a_jour_cout_produit();
