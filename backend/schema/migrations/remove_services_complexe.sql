-- Supp<PERSON>er les contraintes de clés étrangères
ALTER TABLE "RolesComplexe" DROP CONSTRAINT IF EXISTS "RolesComplexe_service_id_fkey";
ALTER TABLE "Employes" DROP CONSTRAINT IF EXISTS "Employes_service_id_fkey";
ALTER TABLE "PointsDeVente" DROP CONSTRAINT IF EXISTS "PointsDeVente_service_id_fkey";
ALTER TABLE "SessionsCaisse" DROP CONSTRAINT IF EXISTS "SessionsCaisse_service_id_fkey";
ALTER TABLE "CategoriesProduits" DROP CONSTRAINT IF EXISTS "CategoriesProduits_service_id_fkey";
ALTER TABLE "Produits" DROP CONSTRAINT IF EXISTS "Produits_service_id_fkey";
ALTER TABLE "PrixProduits" DROP CONSTRAINT IF EXISTS "PrixProduits_service_id_fkey";
ALTER TABLE "Chambres" DROP CONSTRAINT IF EXISTS "Chambres_service_id_fkey";
ALTER TABLE "Reservations" DROP CONSTRAINT IF EXISTS "Reservations_service_id_fkey";
ALTER TABLE "TransactionsPOS" DROP CONSTRAINT IF EXISTS "TransactionsPOS_service_id_fkey";
ALTER TABLE "CodesPromo" DROP CONSTRAINT IF EXISTS "CodesPromo_service_id_fkey";
ALTER TABLE "UtilisationsCodes" DROP CONSTRAINT IF EXISTS "UtilisationsCodes_service_id_fkey";
ALTER TABLE "CommandesFournisseurs" DROP CONSTRAINT IF EXISTS "CommandesFournisseurs_service_id_fkey";
ALTER TABLE "MouvementsStock" DROP CONSTRAINT IF EXISTS "MouvementsStock_service_id_fkey";
ALTER TABLE "Inventaires" DROP CONSTRAINT IF EXISTS "Inventaires_service_id_fkey";
ALTER TABLE "Tables" DROP CONSTRAINT IF EXISTS "Tables_service_id_fkey";
ALTER TABLE "ReservationsTables" DROP CONSTRAINT IF EXISTS "ReservationsTables_service_id_fkey";
ALTER TABLE "Commandes" DROP CONSTRAINT IF EXISTS "Commandes_service_id_fkey";
ALTER TABLE "DetailsCommandes" DROP CONSTRAINT IF EXISTS "DetailsCommandes_service_id_fkey";

-- Supprimer la contrainte de clé étrangère de la table ServicesComplexe
ALTER TABLE "ServicesComplexe" DROP CONSTRAINT IF EXISTS "ServicesComplexe_complexe_id_fkey";

-- Supprimer la table ServicesComplexe
DROP TABLE IF EXISTS "ServicesComplexe"; 