-- Table pour les paiements de réservations
CREATE TABLE "PaiementsReservations" (
  "paiement_reservation_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "reservation_id" integer NOT NULL,
  "montant" decimal NOT NULL,
  "mode_paiement" varchar NOT NULL,
  "reference_paiement" varchar UNIQUE NOT NULL,
  "date_paiement" timestamp DEFAULT (now()),
  "statut" varchar DEFAULT 'Validé',
  "notes" text,
  "utilisateur_id" integer,
  "type_utilisateur" varchar DEFAULT 'employe',
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

-- Contraintes et commentaires
ALTER TABLE "PaiementsReservations" ADD FOREIGN KEY ("reservation_id") REFERENCES "Reservations" ("reservation_id");

COMMENT ON COLUMN "PaiementsReservations"."mode_paiement" IS 'especes, carte, virement, cheque, mobile';
COMMENT ON COLUMN "PaiementsReservations"."statut" IS 'Validé, Annulé, Remboursé';
COMMENT ON COLUMN "PaiementsReservations"."type_utilisateur" IS 'employe, admin_complexe, admin_chaine';

-- Index pour améliorer les performances
CREATE INDEX "idx_paiements_reservations_reservation_id" ON "PaiementsReservations" ("reservation_id");
CREATE INDEX "idx_paiements_reservations_date_paiement" ON "PaiementsReservations" ("date_paiement");
CREATE INDEX "idx_paiements_reservations_reference" ON "PaiementsReservations" ("reference_paiement");
