-- Migration pour ajouter les tables d'inventaire boissons
-- Date: 2024-12-11
-- Description: Création des tables pour gérer l'inventaire des boissons séparément des ingrédients

-- Table pour l'inventaire des boissons
CREATE TABLE "InventaireBoissons" (
  "boisson_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "chaine_id" integer NOT NULL,
  "complexe_id" integer NOT NULL,
  "nom" varchar NOT NULL,
  "description" text,
  "categorie" varchar NOT NULL, -- <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ux, Cocktail, Soft, Alcool
  "type_conditionnement" varchar NOT NULL, -- Bouteille, Canette, Fût, Magnum, J<PERSON>roboam
  "volume_unitaire" decimal NOT NULL, -- Volume en litres (0.33, 0.75, etc.)
  "prix_achat_unitaire" decimal DEFAULT 0,
  "prix_vente_unitaire" decimal DEFAULT 0,
  "fournisseur" varchar,
  "degre_alcool" decimal DEFAULT 0, -- Degré d'alcool (0 pour les softs)
  "code_barre" varchar,
  "stock_minimal" integer DEFAULT 1,
  "stock_maximal" integer DEFAULT 100,
  "emplacement_stockage" varchar DEFAULT 'Cave', -- Cave, Frigo, Bar, Réserve
  "temperature_stockage" varchar DEFAULT 'Frais', -- Ambiante, Frais, Très frais, Congelé
  "actif" boolean DEFAULT true,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp,
  FOREIGN KEY (chaine_id) REFERENCES "ChainesHotelieres" (chaine_id),
  FOREIGN KEY (complexe_id) REFERENCES "ComplexesHoteliers" (complexe_id)
);

-- Table pour le stock des boissons
CREATE TABLE "StockBoissons" (
  "stock_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "boisson_id" integer NOT NULL,
  "complexe_id" integer NOT NULL,
  "quantite_actuelle" integer DEFAULT 0,
  "quantite_minimale" integer DEFAULT 1,
  "quantite_maximale" integer DEFAULT 100,
  "valeur_stock_actuel" decimal DEFAULT 0, -- Calculé automatiquement
  "derniere_mise_a_jour" timestamp DEFAULT (now()),
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp,
  FOREIGN KEY (boisson_id) REFERENCES "InventaireBoissons" (boisson_id),
  FOREIGN KEY (complexe_id) REFERENCES "ComplexesHoteliers" (complexe_id)
);

-- Table pour les mouvements de stock des boissons
CREATE TABLE "MouvementsStockBoissons" (
  "mouvement_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "boisson_id" integer NOT NULL,
  "complexe_id" integer NOT NULL,
  "type_mouvement" varchar NOT NULL, -- Réception, Vente, Perte, Transfert, Ajustement
  "quantite" integer NOT NULL,
  "prix_unitaire" decimal,
  "valeur_totale" decimal,
  "date_mouvement" timestamp DEFAULT (now()),
  "reference_id" integer, -- ID de la commande, vente, etc.
  "reference_type" varchar, -- Type de référence (Commande, Vente, etc.)
  "employe_id" integer,
  "notes" text,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp,
  FOREIGN KEY (boisson_id) REFERENCES "InventaireBoissons" (boisson_id),
  FOREIGN KEY (complexe_id) REFERENCES "ComplexesHoteliers" (complexe_id),
  FOREIGN KEY (employe_id) REFERENCES "Employes" (employe_id)
);

-- Index pour optimiser les performances
CREATE INDEX "idx_inventaire_boissons_complexe" ON "InventaireBoissons" (complexe_id);
CREATE INDEX "idx_inventaire_boissons_categorie" ON "InventaireBoissons" (categorie);
CREATE INDEX "idx_inventaire_boissons_actif" ON "InventaireBoissons" (actif);
CREATE INDEX "idx_stock_boissons_boisson" ON "StockBoissons" (boisson_id);
CREATE INDEX "idx_stock_boissons_complexe" ON "StockBoissons" (complexe_id);
CREATE INDEX "idx_mouvements_stock_boissons_boisson" ON "MouvementsStockBoissons" (boisson_id);
CREATE INDEX "idx_mouvements_stock_boissons_date" ON "MouvementsStockBoissons" (date_mouvement);

-- Contraintes de validation
ALTER TABLE "InventaireBoissons" ADD CONSTRAINT "check_volume_unitaire" CHECK (volume_unitaire > 0);
ALTER TABLE "InventaireBoissons" ADD CONSTRAINT "check_degre_alcool" CHECK (degre_alcool >= 0 AND degre_alcool <= 100);
ALTER TABLE "InventaireBoissons" ADD CONSTRAINT "check_prix_positifs" CHECK (prix_achat_unitaire >= 0 AND prix_vente_unitaire >= 0);
ALTER TABLE "StockBoissons" ADD CONSTRAINT "check_quantites_positives" CHECK (quantite_actuelle >= 0 AND quantite_minimale >= 0 AND quantite_maximale >= 0);
ALTER TABLE "StockBoissons" ADD CONSTRAINT "check_quantite_max_min" CHECK (quantite_maximale >= quantite_minimale);

-- Contraintes d'unicité
ALTER TABLE "InventaireBoissons" ADD CONSTRAINT "unique_boisson_complexe" UNIQUE (nom, complexe_id);
ALTER TABLE "StockBoissons" ADD CONSTRAINT "unique_stock_boisson_complexe" UNIQUE (boisson_id, complexe_id);

-- Trigger pour mettre à jour automatiquement la valeur du stock
CREATE OR REPLACE FUNCTION update_stock_boisson_value()
RETURNS TRIGGER AS $$
BEGIN
  -- Mettre à jour la valeur du stock basée sur le prix d'achat
  UPDATE "StockBoissons" 
  SET valeur_stock_actuel = NEW.quantite_actuelle * (
    SELECT prix_achat_unitaire 
    FROM "InventaireBoissons" 
    WHERE boisson_id = NEW.boisson_id
  ),
  updated_at = NOW()
  WHERE boisson_id = NEW.boisson_id AND complexe_id = NEW.complexe_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_stock_boisson_value
  AFTER UPDATE OF quantite_actuelle ON "StockBoissons"
  FOR EACH ROW
  EXECUTE FUNCTION update_stock_boisson_value();

-- Trigger pour mettre à jour le stock lors des mouvements
CREATE OR REPLACE FUNCTION update_stock_from_mouvement_boisson()
RETURNS TRIGGER AS $$
BEGIN
  -- Mettre à jour le stock selon le type de mouvement
  IF NEW.type_mouvement IN ('Réception', 'Ajustement') THEN
    -- Augmenter le stock
    UPDATE "StockBoissons" 
    SET quantite_actuelle = quantite_actuelle + NEW.quantite,
        derniere_mise_a_jour = NOW(),
        updated_at = NOW()
    WHERE boisson_id = NEW.boisson_id AND complexe_id = NEW.complexe_id;
  ELSIF NEW.type_mouvement IN ('Vente', 'Perte', 'Transfert') THEN
    -- Diminuer le stock
    UPDATE "StockBoissons" 
    SET quantite_actuelle = GREATEST(0, quantite_actuelle - NEW.quantite),
        derniere_mise_a_jour = NOW(),
        updated_at = NOW()
    WHERE boisson_id = NEW.boisson_id AND complexe_id = NEW.complexe_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_stock_from_mouvement_boisson
  AFTER INSERT ON "MouvementsStockBoissons"
  FOR EACH ROW
  EXECUTE FUNCTION update_stock_from_mouvement_boisson();

-- Vue pour le stock des boissons avec informations détaillées
CREATE VIEW "VueStockBoissons" AS
SELECT
    ib.boisson_id,
    ib.nom as nom_boisson,
    ib.categorie,
    ib.type_conditionnement,
    ib.volume_unitaire,
    ib.prix_achat_unitaire,
    ib.prix_vente_unitaire,
    ib.degre_alcool,
    ib.emplacement_stockage,
    ib.temperature_stockage,
    ib.complexe_id,
    sb.quantite_actuelle,
    sb.quantite_minimale,
    sb.quantite_maximale,
    sb.valeur_stock_actuel,
    sb.derniere_mise_a_jour,
    CASE 
        WHEN sb.quantite_actuelle <= sb.quantite_minimale THEN 'CRITIQUE'
        WHEN sb.quantite_actuelle <= (sb.quantite_minimale * 1.5) THEN 'FAIBLE'
        ELSE 'NORMAL'
    END as statut_stock,
    (sb.quantite_actuelle * ib.prix_vente_unitaire) as valeur_vente_potentielle
FROM "InventaireBoissons" ib
LEFT JOIN "StockBoissons" sb ON ib.boisson_id = sb.boisson_id
WHERE ib.actif = true;

-- Vue pour les mouvements récents de boissons
CREATE VIEW "VueMouvementsBoissonsRecents" AS
SELECT
    msb.mouvement_id,
    ib.nom as nom_boisson,
    ib.categorie,
    msb.type_mouvement,
    msb.quantite,
    msb.prix_unitaire,
    msb.valeur_totale,
    msb.date_mouvement,
    msb.notes,
    e.nom as employe_nom,
    msb.complexe_id
FROM "MouvementsStockBoissons" msb
JOIN "InventaireBoissons" ib ON msb.boisson_id = ib.boisson_id
LEFT JOIN "Employes" e ON msb.employe_id = e.employe_id
ORDER BY msb.date_mouvement DESC;

-- Commentaires sur les tables
COMMENT ON TABLE "InventaireBoissons" IS 'Inventaire des boissons avec informations détaillées sur les produits';
COMMENT ON TABLE "StockBoissons" IS 'Stock actuel des boissons par complexe';
COMMENT ON TABLE "MouvementsStockBoissons" IS 'Historique des mouvements de stock des boissons';

-- Commentaires sur les colonnes importantes
COMMENT ON COLUMN "InventaireBoissons"."type_conditionnement" IS 'Type de conditionnement: Bouteille, Canette, Fût, Magnum, Jéroboam';
COMMENT ON COLUMN "InventaireBoissons"."volume_unitaire" IS 'Volume en litres (ex: 0.33 pour 33cl, 0.75 pour 75cl)';
COMMENT ON COLUMN "InventaireBoissons"."degre_alcool" IS 'Degré d alcool en pourcentage (0 pour les boissons sans alcool)';
COMMENT ON COLUMN "InventaireBoissons"."emplacement_stockage" IS 'Lieu de stockage: Cave, Frigo, Bar, Réserve';
COMMENT ON COLUMN "InventaireBoissons"."temperature_stockage" IS 'Température de stockage: Ambiante, Frais, Très frais, Congelé';

-- Données d'exemple pour les catégories
INSERT INTO "InventaireBoissons" (
    chaine_id, complexe_id, nom, description, categorie, type_conditionnement,
    volume_unitaire, prix_achat_unitaire, prix_vente_unitaire, degre_alcool,
    emplacement_stockage, temperature_stockage
) VALUES 
(1, 1, 'Exemple Bière Blonde', 'Bière blonde premium', 'Bière', 'Bouteille', 0.33, 2.50, 5.50, 5.0, 'Cave', 'Frais'),
(1, 1, 'Exemple Vin Rouge', 'Vin rouge de table', 'Vin', 'Bouteille', 0.75, 8.00, 18.00, 12.5, 'Cave à vin', 'Très frais'),
(1, 1, 'Exemple Coca-Cola', 'Boisson gazeuse', 'Soft', 'Canette', 0.33, 1.20, 3.50, 0, 'Réserve', 'Frais')
ON CONFLICT DO NOTHING;
