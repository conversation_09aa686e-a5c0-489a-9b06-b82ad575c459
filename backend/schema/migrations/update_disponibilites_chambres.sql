-- Supprimer l'ancienne contrainte check_statut_dispo
ALTER TABLE "DisponibilitesChambres" DROP CONSTRAINT IF EXISTS "check_statut_dispo";

-- Ajouter la colonne type_reservation
ALTER TABLE "DisponibilitesChambres" 
ADD COLUMN IF NOT EXISTS "type_reservation" varchar DEFAULT 'CONFIRMEE',
ADD CONSTRAINT "check_type_reservation" 
CHECK (type_reservation IN ('CONFIRMEE', 'DEMANDE'));

-- Recréer la contrainte check_statut_dispo avec le nouveau statut 'en_attente'
ALTER TABLE "DisponibilitesChambres" 
ADD CONSTRAINT "check_statut_dispo" 
CHECK (statut IN ('disponible', 'occupee', 'maintenance', 'bloquee', 'en_attente'));

-- Mettre à jour les index existants
DROP INDEX IF EXISTS idx_disponibilites_statut;
CREATE INDEX idx_disponibilites_statut ON "DisponibilitesChambres" (statut, type_reservation);

-- Ajouter un commentaire sur la colonne type_reservation
COMMENT ON COLUMN "DisponibilitesChambres"."type_reservation" IS 'Type de réservation: CONFIRMEE ou DEMANDE'; 