-- =====================================================
-- AMÉLIORATION DU SYSTÈME DE GESTION DE STOCKS
-- =====================================================

-- Ajouter les colonnes manquantes à la table Ingredients si elles n'existent pas
DO $$ 
BEGIN
    -- Ajouter stock_initial si n'existe pas
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'Ingredients' AND column_name = 'stock_initial') THEN
        ALTER TABLE "Ingredients" ADD COLUMN "stock_initial" decimal DEFAULT 0;
    END IF;
    
    -- Ajouter stock_actuel si n'existe pas
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'Ingredients' AND column_name = 'stock_actuel') THEN
        ALTER TABLE "Ingredients" ADD COLUMN "stock_actuel" decimal DEFAULT 0;
    END IF;
    
    -- Ajouter stock_minimal si n'existe pas
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'Ingredients' AND column_name = 'stock_minimal') THEN
        ALTER TABLE "Ingredients" ADD COLUMN "stock_minimal" decimal DEFAULT 0;
    END IF;
    
    -- Ajouter stock_maximal si n'existe pas
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'Ingredients' AND column_name = 'stock_maximal') THEN
        ALTER TABLE "Ingredients" ADD COLUMN "stock_maximal" decimal DEFAULT 0;
    END IF;
END $$;

-- Table pour l'historique des mouvements de stock des ingrédients
CREATE TABLE IF NOT EXISTS "MouvementsStockIngredients" (
  "mouvement_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "ingredient_id" integer NOT NULL,
  "complexe_id" integer NOT NULL,
  "type_mouvement" varchar NOT NULL, -- ENTREE, SORTIE, AJUSTEMENT, CONSOMMATION, PERTE
  "quantite" decimal NOT NULL,
  "quantite_avant" decimal NOT NULL,
  "quantite_apres" decimal NOT NULL,
  "prix_unitaire" decimal DEFAULT 0,
  "valeur_totale" decimal GENERATED ALWAYS AS (quantite * prix_unitaire) STORED,
  "reference_id" integer, -- ID de la commande, recette, etc.
  "reference_type" varchar, -- COMMANDE, RECETTE, INVENTAIRE, MANUEL
  "employe_id" integer,
  "notes" text,
  "date_mouvement" timestamp DEFAULT (now()),
  "created_at" timestamp DEFAULT (now()),
  FOREIGN KEY (ingredient_id) REFERENCES "Ingredients" (ingredient_id),
  FOREIGN KEY (complexe_id) REFERENCES "ComplexesHoteliers" (complexe_id),
  FOREIGN KEY (employe_id) REFERENCES "Employes" (employe_id)
);

-- Table pour lier directement les produits (plats) aux ingrédients
CREATE TABLE IF NOT EXISTS "ProduitsIngredients" (
  "produit_ingredient_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "produit_id" integer NOT NULL,
  "ingredient_id" integer NOT NULL,
  "quantite_necessaire" decimal NOT NULL, -- Quantité d'ingrédient nécessaire par portion
  "unite_mesure" varchar NOT NULL, -- Unité de mesure pour cette quantité
  "cout_unitaire" decimal DEFAULT 0, -- Coût de cet ingrédient pour ce plat
  "cout_total" decimal GENERATED ALWAYS AS (quantite_necessaire * cout_unitaire) STORED,
  "optionnel" boolean DEFAULT false, -- Si l'ingrédient est optionnel
  "notes" text, -- Notes sur l'utilisation de cet ingrédient
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp,
  FOREIGN KEY (produit_id) REFERENCES "Produits" (produit_id),
  FOREIGN KEY (ingredient_id) REFERENCES "Ingredients" (ingredient_id),
  UNIQUE(produit_id, ingredient_id) -- Un ingrédient ne peut être lié qu'une fois par produit
);

-- Table pour les consommations réelles lors des commandes
CREATE TABLE IF NOT EXISTS "ConsommationsIngredients" (
  "consommation_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "ingredient_id" integer NOT NULL,
  "produit_id" integer NOT NULL,
  "commande_id" integer NOT NULL,
  "quantite_theorique" decimal NOT NULL, -- Quantité selon la composition du plat
  "quantite_reelle" decimal, -- Quantité réellement utilisée (si mesurée)
  "ecart" decimal GENERATED ALWAYS AS (quantite_reelle - quantite_theorique) STORED,
  "cout_theorique" decimal NOT NULL,
  "cout_reel" decimal,
  "date_consommation" timestamp DEFAULT (now()),
  "created_at" timestamp DEFAULT (now()),
  FOREIGN KEY (ingredient_id) REFERENCES "Ingredients" (ingredient_id),
  FOREIGN KEY (produit_id) REFERENCES "Produits" (produit_id),
  FOREIGN KEY (commande_id) REFERENCES "Commandes" (commande_id)
);

-- Table pour les alertes de stock
CREATE TABLE IF NOT EXISTS "AlertesStock" (
  "alerte_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "ingredient_id" integer NOT NULL,
  "complexe_id" integer NOT NULL,
  "type_alerte" varchar NOT NULL, -- STOCK_FAIBLE, RUPTURE, PEREMPTION
  "niveau_urgence" varchar NOT NULL, -- FAIBLE, MOYEN, ELEVE, CRITIQUE
  "message" text NOT NULL,
  "stock_actuel" decimal NOT NULL,
  "seuil_declenche" decimal NOT NULL,
  "date_alerte" timestamp DEFAULT (now()),
  "date_resolution" timestamp,
  "statut" varchar DEFAULT 'ACTIVE', -- ACTIVE, RESOLUE, IGNOREE
  "employe_id" integer, -- Employé qui a résolu l'alerte
  "notes_resolution" text,
  "created_at" timestamp DEFAULT (now()),
  FOREIGN KEY (ingredient_id) REFERENCES "Ingredients" (ingredient_id),
  FOREIGN KEY (complexe_id) REFERENCES "ComplexesHoteliers" (complexe_id),
  FOREIGN KEY (employe_id) REFERENCES "Employes" (employe_id)
);

-- Index pour les performances
CREATE INDEX IF NOT EXISTS idx_mouvements_stock_ingredients_ingredient ON "MouvementsStockIngredients" (ingredient_id);
CREATE INDEX IF NOT EXISTS idx_mouvements_stock_ingredients_complexe ON "MouvementsStockIngredients" (complexe_id);
CREATE INDEX IF NOT EXISTS idx_mouvements_stock_ingredients_date ON "MouvementsStockIngredients" (date_mouvement);
CREATE INDEX IF NOT EXISTS idx_mouvements_stock_ingredients_type ON "MouvementsStockIngredients" (type_mouvement);

CREATE INDEX IF NOT EXISTS idx_produits_ingredients_produit ON "ProduitsIngredients" (produit_id);
CREATE INDEX IF NOT EXISTS idx_produits_ingredients_ingredient ON "ProduitsIngredients" (ingredient_id);

CREATE INDEX IF NOT EXISTS idx_consommations_ingredients_ingredient ON "ConsommationsIngredients" (ingredient_id);
CREATE INDEX IF NOT EXISTS idx_consommations_ingredients_produit ON "ConsommationsIngredients" (produit_id);
CREATE INDEX IF NOT EXISTS idx_consommations_ingredients_commande ON "ConsommationsIngredients" (commande_id);
CREATE INDEX IF NOT EXISTS idx_consommations_ingredients_date ON "ConsommationsIngredients" (date_consommation);

CREATE INDEX IF NOT EXISTS idx_alertes_stock_ingredient ON "AlertesStock" (ingredient_id);
CREATE INDEX IF NOT EXISTS idx_alertes_stock_complexe ON "AlertesStock" (complexe_id);
CREATE INDEX IF NOT EXISTS idx_alertes_stock_statut ON "AlertesStock" (statut);
CREATE INDEX IF NOT EXISTS idx_alertes_stock_date ON "AlertesStock" (date_alerte);

-- Fonction pour calculer automatiquement le stock actuel
CREATE OR REPLACE FUNCTION calculate_stock_actuel(p_ingredient_id INTEGER)
RETURNS DECIMAL AS $$
DECLARE
    stock_calcule DECIMAL := 0;
BEGIN
    -- Calculer le stock basé sur les mouvements
    SELECT COALESCE(SUM(
        CASE 
            WHEN type_mouvement IN ('ENTREE', 'AJUSTEMENT') THEN quantite
            WHEN type_mouvement IN ('SORTIE', 'CONSOMMATION', 'PERTE') THEN -quantite
            ELSE 0
        END
    ), 0) INTO stock_calcule
    FROM "MouvementsStockIngredients"
    WHERE ingredient_id = p_ingredient_id;
    
    RETURN stock_calcule;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour mettre à jour le stock actuel d'un ingrédient
CREATE OR REPLACE FUNCTION update_stock_actuel()
RETURNS TRIGGER AS $$
BEGIN
    -- Mettre à jour le stock actuel de l'ingrédient
    UPDATE "Ingredients" 
    SET stock_actuel = calculate_stock_actuel(NEW.ingredient_id),
        updated_at = NOW()
    WHERE ingredient_id = NEW.ingredient_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour mettre à jour automatiquement le stock
DROP TRIGGER IF EXISTS trigger_update_stock_actuel ON "MouvementsStockIngredients";
CREATE TRIGGER trigger_update_stock_actuel
    AFTER INSERT OR UPDATE OR DELETE ON "MouvementsStockIngredients"
    FOR EACH ROW
    EXECUTE FUNCTION update_stock_actuel();

-- Vue améliorée pour le stock des ingrédients
CREATE OR REPLACE VIEW "VueStockIngredientsDetaille" AS
SELECT
    i.ingredient_id,
    i.nom as nom_ingredient,
    i.description,
    i.categorie,
    i.unite_mesure,
    i.prix_unitaire_moyen,
    i.complexe_id,
    i.stock_initial,
    i.stock_actuel,
    i.stock_minimal,
    i.stock_maximal,
    i.conservation,
    i.duree_conservation_jours,
    -- Calculs de valeur
    i.stock_actuel * i.prix_unitaire_moyen as valeur_stock_actuel,
    i.stock_initial * i.prix_unitaire_moyen as valeur_stock_initial,
    -- Statut du stock
    CASE 
        WHEN i.stock_actuel <= 0 THEN 'RUPTURE'
        WHEN i.stock_actuel <= i.stock_minimal THEN 'CRITIQUE'
        WHEN i.stock_actuel <= i.stock_minimal * 1.5 THEN 'FAIBLE'
        WHEN i.stock_actuel >= i.stock_maximal THEN 'EXCESSIF'
        ELSE 'NORMAL'
    END as statut_stock,
    -- Pourcentage du stock
    CASE 
        WHEN i.stock_maximal > 0 THEN (i.stock_actuel / i.stock_maximal * 100)
        ELSE 0
    END as pourcentage_stock,
    -- Jours de stock restant (estimation basée sur consommation moyenne)
    COALESCE(
        (SELECT 
            CASE 
                WHEN AVG(ABS(msi.quantite)) > 0 
                THEN i.stock_actuel / AVG(ABS(msi.quantite))
                ELSE NULL
            END
        FROM "MouvementsStockIngredients" msi
        WHERE msi.ingredient_id = i.ingredient_id 
        AND msi.type_mouvement = 'CONSOMMATION'
        AND msi.date_mouvement >= NOW() - INTERVAL '30 days'
        ), 0
    ) as jours_stock_restant,
    -- Dernière mise à jour
    i.updated_at as derniere_mise_a_jour
FROM "Ingredients" i
WHERE i.actif = true;

-- Vue pour les alertes de stock actives
CREATE OR REPLACE VIEW "VueAlertes" AS
SELECT
    a.alerte_id,
    a.ingredient_id,
    i.nom as nom_ingredient,
    i.categorie,
    a.complexe_id,
    c.nom as nom_complexe,
    a.type_alerte,
    a.niveau_urgence,
    a.message,
    a.stock_actuel,
    a.seuil_declenche,
    a.date_alerte,
    a.statut,
    -- Durée depuis l'alerte
    EXTRACT(EPOCH FROM (NOW() - a.date_alerte))/3600 as heures_depuis_alerte
FROM "AlertesStock" a
JOIN "Ingredients" i ON a.ingredient_id = i.ingredient_id
JOIN "ComplexesHoteliers" c ON a.complexe_id = c.complexe_id
WHERE a.statut = 'ACTIVE'
ORDER BY 
    CASE a.niveau_urgence
        WHEN 'CRITIQUE' THEN 1
        WHEN 'ELEVE' THEN 2
        WHEN 'MOYEN' THEN 3
        WHEN 'FAIBLE' THEN 4
    END,
    a.date_alerte DESC;
