-- =====================================================
-- MIGRATION : Nettoyage de l'ancien système de recettes
-- Date : $(date)
-- Description : Suppression des tables et colonnes obsolètes du système de recettes
-- =====================================================

-- =====================================================
-- 1. SAUVEGARDE DES DONNÉES IMPORTANTES (si nécessaire)
-- =====================================================

-- Créer une table de sauvegarde des recettes existantes (optionnel)
-- CREATE TABLE "RecettesSauvegarde" AS SELECT * FROM "Recettes";
-- CREATE TABLE "RecettesIngredientsSauvegarde" AS SELECT * FROM "RecettesIngredients";

-- =====================================================
-- 2. SUPPRESSION DES CONTRAINTES ET INDEX
-- =====================================================

-- Supprimer les contraintes de clés étrangères
ALTER TABLE "Produits" DROP CONSTRAINT IF EXISTS "Produits_recette_id_fkey";
ALTER TABLE "MouvementsStock" DROP CONSTRAINT IF EXISTS "MouvementsStock_recette_id_fkey";

-- Supprimer les index liés aux recettes
DROP INDEX IF EXISTS idx_recettes_service;
DROP INDEX IF EXISTS idx_recettes_produit;
DROP INDEX IF EXISTS idx_recettes_actif;

-- =====================================================
-- 3. SUPPRESSION DES VUES
-- =====================================================

-- Supprimer la vue des coûts de recettes
DROP VIEW IF EXISTS "VueRecettesCouts";

-- =====================================================
-- 4. SUPPRESSION DES TABLES OBSOLÈTES
-- =====================================================

-- Supprimer la table de liaison recettes-ingrédients
DROP TABLE IF EXISTS "RecettesIngredients";

-- Supprimer la table des recettes
DROP TABLE IF EXISTS "Recettes";

-- =====================================================
-- 5. SUPPRESSION DES COLONNES OBSOLÈTES
-- =====================================================

-- Supprimer les colonnes liées aux recettes dans la table Produits
ALTER TABLE "Produits" DROP COLUMN IF EXISTS "recette_id";

-- Supprimer les colonnes liées aux recettes dans la table MouvementsStock
ALTER TABLE "MouvementsStock" DROP COLUMN IF EXISTS "recette_id";

-- =====================================================
-- 6. NETTOYAGE DES DONNÉES ORPHELINES (si nécessaire)
-- =====================================================

-- Nettoyer les mouvements de stock qui référençaient des recettes
-- UPDATE "MouvementsStock" SET notes = 'Référence recette supprimée' 
-- WHERE notes LIKE '%recette%' OR notes LIKE '%recipe%';

-- =====================================================
-- 7. OPTIMISATION POST-MIGRATION
-- =====================================================

-- Recalculer les statistiques des tables
ANALYZE "Produits";
ANALYZE "MouvementsStock";
ANALYZE "Ingredients";

-- =====================================================
-- 8. VÉRIFICATIONS POST-MIGRATION
-- =====================================================

-- Vérifier que les tables ont été supprimées
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'Recettes') THEN
        RAISE EXCEPTION 'ERREUR: La table Recettes existe encore';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'RecettesIngredients') THEN
        RAISE EXCEPTION 'ERREUR: La table RecettesIngredients existe encore';
    END IF;
    
    RAISE NOTICE 'Migration terminée avec succès - Ancien système de recettes supprimé';
END $$;
