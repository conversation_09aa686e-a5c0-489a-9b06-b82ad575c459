-- Migration: Étendre la table Tickets pour supporter les services (piscine, bar, restaurant)
-- Date: 2025-06-04
-- Description: Ajouter les colonnes nécessaires pour gérer les tickets de services en plus des tickets de réservation

-- Vérification préalable: s'assurer que la table Tickets existe
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'Tickets') THEN
        RAISE EXCEPTION 'La table Tickets n''existe pas. Veuillez d''abord exécuter le schéma principal.';
    END IF;
END $$;

-- Étape 1: Ajouter les nouvelles colonnes
ALTER TABLE "Tickets"
ADD COLUMN IF NOT EXISTS "type_ticket" varchar DEFAULT 'RESERVATION',
ADD COLUMN IF NOT EXISTS "service_id" integer,
ADD COLUMN IF NOT EXISTS "pos_id" integer,
ADD COLUMN IF NOT EXISTS "session_id" integer,
ADD COLUMN IF NOT EXISTS "nom_client" varchar,
ADD COLUMN IF NOT EXISTS "nombre_personnes" integer,
ADD COLUMN IF NOT EXISTS "duree_heures" decimal,
ADD COLUMN IF NOT EXISTS "prix_unitaire" decimal,
ADD COLUMN IF NOT EXISTS "prix_total" decimal,
ADD COLUMN IF NOT EXISTS "mode_paiement" varchar,
ADD COLUMN IF NOT EXISTS "statut_paiement" varchar DEFAULT 'En attente',
ADD COLUMN IF NOT EXISTS "date_expiration" timestamp;

-- Étape 2: Modifier la contrainte reservation_id pour qu'elle soit nullable
ALTER TABLE "Tickets" 
ALTER COLUMN "reservation_id" DROP NOT NULL;

-- Étape 3: Ajouter les contraintes de validation
ALTER TABLE "Tickets" 
ADD CONSTRAINT "check_type_ticket" 
CHECK (type_ticket IN ('RESERVATION', 'SERVICE'));

ALTER TABLE "Tickets" 
ADD CONSTRAINT "check_mode_paiement" 
CHECK (mode_paiement IS NULL OR mode_paiement IN ('Espèces', 'Carte', 'Virement', 'Chèque'));

ALTER TABLE "Tickets" 
ADD CONSTRAINT "check_statut_paiement" 
CHECK (statut_paiement IN ('En attente', 'Payé', 'Remboursé', 'Annulé'));

-- Étape 4: Ajouter les contraintes conditionnelles
-- Un ticket de réservation doit avoir un reservation_id et pas de service_id
-- Un ticket de service doit avoir un service_id et pas de reservation_id
ALTER TABLE "Tickets" 
ADD CONSTRAINT "check_ticket_type_consistency" 
CHECK (
  (type_ticket = 'RESERVATION' AND reservation_id IS NOT NULL AND service_id IS NULL) OR
  (type_ticket = 'SERVICE' AND reservation_id IS NULL AND service_id IS NOT NULL AND nom_client IS NOT NULL)
);

-- Étape 5: Ajouter les clés étrangères pour les tickets de service
ALTER TABLE "Tickets" 
ADD CONSTRAINT "fk_tickets_service" 
FOREIGN KEY (service_id) REFERENCES "ServicesComplexe" (service_id) ON DELETE CASCADE;

ALTER TABLE "Tickets" 
ADD CONSTRAINT "fk_tickets_pos" 
FOREIGN KEY (pos_id) REFERENCES "PointsDeVente" (pos_id) ON DELETE SET NULL;

ALTER TABLE "Tickets" 
ADD CONSTRAINT "fk_tickets_session" 
FOREIGN KEY (session_id) REFERENCES "SessionsCaisse" (session_id) ON DELETE SET NULL;

-- Étape 6: Ajouter des index pour optimiser les performances
CREATE INDEX idx_tickets_type ON "Tickets" (type_ticket);
CREATE INDEX idx_tickets_service ON "Tickets" (service_id) WHERE service_id IS NOT NULL;
CREATE INDEX idx_tickets_pos ON "Tickets" (pos_id) WHERE pos_id IS NOT NULL;
CREATE INDEX idx_tickets_session ON "Tickets" (session_id) WHERE session_id IS NOT NULL;
CREATE INDEX idx_tickets_statut_paiement ON "Tickets" (statut_paiement) WHERE statut_paiement IS NOT NULL;
CREATE INDEX idx_tickets_date_expiration ON "Tickets" (date_expiration) WHERE date_expiration IS NOT NULL;

-- Étape 7: Mettre à jour les tickets existants pour qu'ils aient le bon type
UPDATE "Tickets" 
SET type_ticket = 'RESERVATION' 
WHERE type_ticket IS NULL OR type_ticket = 'RESERVATION';

-- Étape 8: Ajouter des commentaires pour la documentation
COMMENT ON COLUMN "Tickets"."type_ticket" IS 'Type de ticket: RESERVATION pour les réservations de chambres, SERVICE pour les services (piscine, bar, restaurant)';
COMMENT ON COLUMN "Tickets"."service_id" IS 'ID du service pour les tickets de service (piscine, bar, restaurant)';
COMMENT ON COLUMN "Tickets"."pos_id" IS 'ID du point de vente utilisé pour la transaction';
COMMENT ON COLUMN "Tickets"."session_id" IS 'ID de la session de caisse pour traçabilité financière';
COMMENT ON COLUMN "Tickets"."nom_client" IS 'Nom du client pour les tickets de service';
COMMENT ON COLUMN "Tickets"."nombre_personnes" IS 'Nombre de personnes pour le ticket de service';
COMMENT ON COLUMN "Tickets"."duree_heures" IS 'Durée en heures pour les services temporaires (ex: piscine)';
COMMENT ON COLUMN "Tickets"."prix_total" IS 'Prix total payé pour le ticket de service';
COMMENT ON COLUMN "Tickets"."mode_paiement" IS 'Mode de paiement utilisé (Espèces, Carte, etc.)';
COMMENT ON COLUMN "Tickets"."date_expiration" IS 'Date expiration du ticket de service';

-- Étape 9: Vérification de l'intégrité des données
-- Cette requête doit retourner 0 pour confirmer que tous les tickets respectent les contraintes
SELECT COUNT(*) as tickets_invalides
FROM "Tickets" 
WHERE 
  (type_ticket = 'RESERVATION' AND (reservation_id IS NULL OR service_id IS NOT NULL)) OR
  (type_ticket = 'SERVICE' AND (reservation_id IS NOT NULL OR service_id IS NULL OR nom_client IS NULL));

-- Si la requête ci-dessus retourne 0, la migration est réussie
