-- Su<PERSON><PERSON>er les colonnes service_id des autres tables
ALTER TABLE "RolesComplexe" DROP COLUMN IF EXISTS "service_id";
ALTER TABLE "Employes" DROP COLUMN IF EXISTS "service_id";
ALTER TABLE "PointsDeVente" DROP COLUMN IF EXISTS "service_id";
ALTER TABLE "SessionsCaisse" DROP COLUMN IF EXISTS "service_id";
ALTER TABLE "CategoriesProduits" DROP COLUMN IF EXISTS "service_id";
ALTER TABLE "Produits" DROP COLUMN IF EXISTS "service_id";
ALTER TABLE "PrixProduits" DROP COLUMN IF EXISTS "service_id";
ALTER TABLE "Chambres" DROP COLUMN IF EXISTS "service_id";
ALTER TABLE "Reservations" DROP COLUMN IF EXISTS "service_id";
ALTER TABLE "TransactionsPOS" DROP COLUMN IF EXISTS "service_id";
ALTER TABLE "CodesPromo" DROP COLUMN IF EXISTS "service_id";
ALTER TABLE "UtilisationsCodes" DROP COLUMN IF EXISTS "service_id";
ALTER TABLE "CommandesFournisseurs" DROP COLUMN IF EXISTS "service_id";
ALTER TABLE "MouvementsStock" DROP COLUMN IF EXISTS "service_id";
ALTER TABLE "Inventaires" DROP COLUMN IF EXISTS "service_id";
ALTER TABLE "Tables" DROP COLUMN IF EXISTS "service_id";
ALTER TABLE "ReservationsTables" DROP COLUMN IF EXISTS "service_id";
ALTER TABLE "Commandes" DROP COLUMN IF EXISTS "service_id";
ALTER TABLE "DetailsCommandes" DROP COLUMN IF EXISTS "service_id"; 