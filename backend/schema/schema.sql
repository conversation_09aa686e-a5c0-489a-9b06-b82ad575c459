CREATE TABLE "UtilisateursSuperAdmin" (
  "superadmin_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "email" varchar UNIQUE NOT NULL,
  "mot_de_passe_hash" varchar NOT NULL,
  "nom" varchar NOT NULL,
  "prenom" varchar NOT NULL,
  "actif" boolean DEFAULT true,
  "derniere_connexion" timestamp,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "ChainesHotelieres" (
  "chaine_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "nom" varchar NOT NULL,
  "slug" varchar UNIQUE NOT NULL,
  "description" text,
  "logo_url" varchar,
  "site_web" varchar,
  "email_contact" varchar,
  "telephone_contact" varchar,
  "adresse_siege" text,
  "pays_origine" varchar,
  "devise_principale" varchar DEFAULT 'EUR',
  "fuseau_horaire_defaut" varchar DEFAULT 'Afique/Togo',
  "actif" boolean DEFAULT true,
  "superadmin_createur_id" integer,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "AdminsChaine" (
  "admin_chaine_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "chaine_id" integer NOT NULL,
  "email" varchar UNIQUE NOT NULL,
  "mot_de_passe_hash" varchar NOT NULL,
  "nom" varchar NOT NULL,
  "prenom" varchar NOT NULL,
  "telephone" varchar,
  "actif" boolean DEFAULT true,
  "derniere_connexion" timestamp,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "ComplexesHoteliers" (
  "complexe_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "chaine_id" integer NOT NULL,
  "nom" varchar NOT NULL,
  "slug" varchar UNIQUE NOT NULL,
  "type_etablissement" varchar NOT NULL,
  "adresse" text,
  "ville" varchar,
  "code_postal" varchar,
  "pays" varchar,
  "telephone" varchar,
  "email" varchar,
  "site_web" varchar,
  "logo_url" varchar,
  "devise" varchar DEFAULT 'EUR',
  "fuseau_horaire" varchar DEFAULT 'Europe/Paris',
  "actif" boolean DEFAULT true,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "ServicesComplexe" (
  "service_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "complexe_id" integer NOT NULL,
  "type_service" varchar NOT NULL,
  "nom" varchar NOT NULL,
  "description" text,
  "emplacement" varchar,
  "horaires_ouverture" json,
  "capacite" integer,
  "image_url" varchar,
  "contact_email" varchar,
  "contact_telephone" varchar,
  "actif" boolean DEFAULT true,
  "configuration" json,
  "tarification" json,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "AdminsComplexe" (
  "admin_complexe_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "complexe_id" integer NOT NULL,
  "email" varchar UNIQUE NOT NULL,
  "mot_de_passe_hash" varchar NOT NULL,
  "nom" varchar NOT NULL,
  "prenom" varchar NOT NULL,
  "telephone" varchar,
  "actif" boolean DEFAULT true,
  "derniere_connexion" timestamp,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "RolesComplexe" (
  "role_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "complexe_id" integer NOT NULL,
  "service_id" integer,
  "nom" varchar NOT NULL,
  "permissions" json NOT NULL,
  "description" text,
"created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
  );

CREATE TABLE "Employes" (
  "employe_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "complexe_id" integer NOT NULL,
  "service_id" integer,
  "role_id" integer,
  "nom" varchar NOT NULL,
  "prenom" varchar NOT NULL,
  "email" varchar NOT NULL,
  "telephone" varchar,
  "mot_de_passe_hash" varchar NOT NULL,
  "date_embauche" date NOT NULL,
  "actif" boolean DEFAULT true,
  "type_employe" varchar,
  "services_autorises" json,
  "derniere_connexion" timestamp,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "PointsDeVente" (
  "pos_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "complexe_id" integer NOT NULL,
  "service_id" integer NOT NULL,
  "nom" varchar NOT NULL,
  "emplacement" varchar NOT NULL,
  "caisse_ouverte" boolean DEFAULT false,
  "fonds_caisse" decimal DEFAULT 0,
  "employe_actuel_id" integer,
  "statut" varchar DEFAULT 'Actif',
  "configuration" json,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "SessionsCaisse" (
  "session_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "pos_id" integer NOT NULL,
  "employe_id" integer NOT NULL,
  "complexe_id" integer NOT NULL,
  "service_id" integer NOT NULL,
  "date_ouverture" timestamp DEFAULT (now()),
  "date_fermeture" timestamp,
  "fonds_ouverture" decimal NOT NULL,
  "fonds_fermeture" decimal,
  "total_ventes" decimal,
  "total_especes" decimal,
  "total_cartes" decimal,
  "total_autres" decimal,
  "notes" text,
  "statut" varchar DEFAULT 'Ouverte'
);

CREATE TABLE "CategoriesProduits" (
  "categorie_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "chaine_id" integer NOT NULL,
  "parent_id" integer,
  "nom" varchar NOT NULL,
  "description" text,
  "niveau" varchar NOT NULL DEFAULT 'Chaîne',
  "complexe_id" integer,
  "service_id" integer,
  "actif" boolean DEFAULT true,
"created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp);

CREATE TABLE "Produits" (
  "produit_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "categorie_id" integer NOT NULL,
  "chaine_id" integer NOT NULL,
  "niveau" varchar NOT NULL DEFAULT 'Chaîne',
  "complexe_id" integer,
  "service_id" integer,
  "nom" varchar NOT NULL,
  "code_barre" varchar UNIQUE,
  "sku" varchar UNIQUE,
  "type_produit" varchar NOT NULL,
  "prix_achat" decimal,
  "prix_vente_defaut" decimal NOT NULL,
  "description" text,
  "stock_actuel" integer DEFAULT 0,
  "stock_minimal" integer DEFAULT 10,
  "stock_maximal" integer,
  "actif" boolean DEFAULT true,
  "tva" decimal DEFAULT 20,
  "image_url" varchar,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "PrixProduits" (
  "prix_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "produit_id" integer NOT NULL,
  "complexe_id" integer NOT NULL,
  "service_id" integer,
  "prix_vente" decimal NOT NULL,
  "date_debut" timestamp NOT NULL DEFAULT (now()),
  "date_fin" timestamp,
  "actif" boolean DEFAULT true,
"created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
  );

CREATE TABLE "Chambres" (
  "chambre_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "complexe_id" integer NOT NULL,
  "service_id" integer NOT NULL,
  "numero" varchar NOT NULL,
  "type_chambre" varchar NOT NULL,
  "prix_base" decimal NOT NULL,
  "capacite" integer NOT NULL,
  "statut" varchar NOT NULL,
  "description" text,
  "etage" integer,
  "caracteristiques" json,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "Clients" (
  "client_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "chaine_id" integer NOT NULL,
  "complexe_creation_id" integer NOT NULL,
  "nom" varchar NOT NULL,
  "prenom" varchar NOT NULL,
  "email" varchar,
  "telephone" varchar,
  "adresse" text,
  "date_naissance" date,
  "nationalite" varchar,
  "numero_piece_identite" varchar,
  "type_piece_identite" varchar,
  "preferences" json,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "Reservations" (
  "reservation_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "complexe_id" integer NOT NULL,
  "service_id" integer NOT NULL,
  "client_id" integer NOT NULL,
  "numero_reservation" varchar NOT NULL,
  "date_creation" timestamp DEFAULT (now()),
  "date_arrivee" date NOT NULL,
  "date_depart" date NOT NULL,
  "heure_debut" time NOT NULL,
  "heure_fin" time NOT NULL,
  "statut" varchar NOT NULL,
  "montant_total" decimal,
  "acompte_verse" decimal DEFAULT 0,
  "code_promo_id" integer,
  "reduction_appliquee" decimal DEFAULT 0,
  "employe_id" integer,
  "commentaires" text,
  "type_reservation" varchar DEFAULT 'NUIT',
  "date_confirmation" timestamp,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp,
  CONSTRAINT "check_type_reservation" CHECK (type_reservation IN ('NUIT', 'HEURE', 'JOURNEE')),
  CONSTRAINT "check_dates" CHECK (date_arrivee <= date_depart),
  CONSTRAINT "check_heures" CHECK (
    (date_arrivee < date_depart) OR 
    (date_arrivee = date_depart AND heure_debut < heure_fin)
  )
);

CREATE TABLE "ChambresReservees" (
  "chambre_reservee_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "reservation_id" integer NOT NULL,
  "chambre_id" integer NOT NULL,
  "date_debut" date NOT NULL,
  "date_fin" date NOT NULL,
  "heure_debut" time NOT NULL,
  "heure_fin" time NOT NULL,
  "prix_nuit" decimal NOT NULL,
  "statut" varchar DEFAULT 'Réservée',
  "type_occupation" varchar DEFAULT 'NUIT',
  "statut_detaille" varchar DEFAULT 'RESERVEE',
  "date_confirmation" timestamp,
  "commentaires" text,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
  CONSTRAINT "check_type_occupation" CHECK (type_occupation IN ('NUIT', 'HEURE', 'JOURNEE')),
  CONSTRAINT "check_statut_detaille_chambre" CHECK (statut_detaille IN ('RESERVEE', 'CONFIRMEE', 'OCCUPEE', 'LIBEREE', 'ANNULEE'))
);

CREATE TABLE "TransactionsPOS" (
  "transaction_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "complexe_id" integer NOT NULL,
  "service_id" integer NOT NULL,
  "pos_id" integer NOT NULL,
  "session_id" integer NOT NULL,
  "employe_id" integer NOT NULL,
  "client_id" integer,
  "chambre_id" integer,
  "reference" varchar UNIQUE NOT NULL,
  "code_promo_id" integer,
  "date_transaction" timestamp DEFAULT (now()),
  "montant_total" decimal NOT NULL,
  "montant_paye" decimal NOT NULL,
  "monnaie_rendue" decimal,
  "mode_paiement" varchar NOT NULL,
  "statut" varchar NOT NULL,
  "notes" text,
"created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
  );

CREATE TABLE "LignesTransactionPOS" (
  "ligne_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "transaction_id" integer NOT NULL,
  "produit_id" integer NOT NULL,
  "quantite" integer NOT NULL,
  "code_promo_id" integer,
  "remise_promo" decimal DEFAULT 0,
  "prix_unitaire" decimal NOT NULL,
  "remise" decimal DEFAULT 0,
  "montant_ligne" decimal NOT NULL,
  "tva" decimal NOT NULL,
  "notes" text
);

CREATE TABLE "Paiements" (
  "paiement_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "transaction_id" integer NOT NULL,
  "montant" decimal NOT NULL,
  "mode_paiement" varchar NOT NULL,
  "reference_paiement" varchar,
  "date_paiement" timestamp DEFAULT (now()),
  "notes" text
);

CREATE TABLE "Fidelite" (
  "fidelite_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "chaine_id" integer NOT NULL,
  "client_id" integer NOT NULL,
  "points_disponibles" integer DEFAULT 0,
  "points_cumules" integer DEFAULT 0,
  "niveau" varchar DEFAULT 'Standard',
  "date_derniere_activite" timestamp,
  "date_inscription" timestamp DEFAULT (now())
);

CREATE TABLE "CodesPromo" (
  "code_promo_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "niveau" varchar NOT NULL,
  "chaine_id" integer NOT NULL,
  "complexe_id" integer,
  "service_id" integer,
  "code" varchar NOT NULL,
  "type_remise" varchar NOT NULL,
  "valeur" decimal NOT NULL,
  "date_debut" date NOT NULL,
  "date_expiration" date NOT NULL,
  "utilisation_max" integer,
  "utilisation_actuelle" integer DEFAULT 0,
  "actif" boolean DEFAULT true,
  "description" text,
  "conditions" text,
  "categorie" varchar DEFAULT 'Global',
  "applicable_sur" varchar DEFAULT 'Tout',
  "produits_associes" json,
  "categories_associees" json,
  "chambres_associees" json,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "UtilisationsCodes" (
  "utilisation_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "chaine_id" integer NOT NULL,
  "complexe_id" integer NOT NULL,
  "service_id" integer,
  "code_promo_id" integer NOT NULL,
  "client_id" integer,
  "date_utilisation" timestamp DEFAULT (now()),
  "reservation_id" integer,
  "transaction_pos_id" integer,
  "montant_avant" decimal NOT NULL,
  "montant_remise" decimal NOT NULL,
  "montant_apres" decimal NOT NULL,
  "employe_id" integer
);

CREATE TABLE "Fournisseurs" (
  "fournisseur_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "chaine_id" integer NOT NULL,
  "niveau" varchar NOT NULL DEFAULT 'Chaîne',
  "complexe_id" integer,
  "nom" varchar NOT NULL,
  "contact" varchar,
  "email" varchar,
  "telephone" varchar,
  "adresse" text,
  "informations_paiement" text,
  "notes" text,
  "actif" boolean DEFAULT true,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "CommandesFournisseurs" (
  "commande_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "chaine_id" integer NOT NULL,
  "complexe_id" integer NOT NULL,
  "service_id" integer,
  "fournisseur_id" integer NOT NULL,
  "reference" varchar NOT NULL,
  "date_commande" date NOT NULL,
  "date_livraison_prevue" date,
  "statut" varchar NOT NULL,
  "montant_total" decimal,
  "employe_id" integer NOT NULL,
  "notes" text,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "DetailsCommandesFournisseurs" (
  "detail_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "commande_id" integer NOT NULL,
  "produit_id" integer NOT NULL,
  "quantite_commandee" integer NOT NULL,
  "quantite_recue" integer DEFAULT 0,
  "prix_unitaire" decimal NOT NULL,
  "montant_ligne" decimal NOT NULL,
  "date_peremption" date,
  "numero_lot" varchar,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "MouvementsStock" (
  "mouvement_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "chaine_id" integer NOT NULL,
  "complexe_id" integer NOT NULL,
  "service_id" integer,
  "produit_id" integer NOT NULL,
  "type_mouvement" varchar NOT NULL,
  "quantite" integer NOT NULL,
  "date_mouvement" timestamp DEFAULT (now()),
  "reference_id" integer,
  "reference_type" varchar,
  "employe_id" integer NOT NULL,
  "notes" text,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "Inventaires" (
  "inventaire_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "chaine_id" integer NOT NULL,
  "complexe_id" integer NOT NULL,
  "service_id" integer,
  "reference" varchar NOT NULL,
  "date_debut" timestamp NOT NULL,
  "date_fin" timestamp,
  "statut" varchar NOT NULL,
  "employe_id" integer NOT NULL,
  "notes" text,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "LignesInventaire" (
  "ligne_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "inventaire_id" integer NOT NULL,
  "produit_id" integer NOT NULL,
  "quantite_theorique" integer NOT NULL,
  "quantite_reelle" integer,
  "ecart" integer,
  "notes" text,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "Tables" (
  "table_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "complexe_id" integer NOT NULL,
  "service_id" integer NOT NULL,
  "numero" varchar NOT NULL,
  "capacite" integer NOT NULL,
  "zone" varchar NOT NULL,
  "statut" varchar DEFAULT 'Libre',
  "position_x" integer,
  "position_y" integer,
  "rotation" integer DEFAULT 0,
  "shape" varchar DEFAULT 'Rectangle',
  "dimensions" varchar DEFAULT '100x100',
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "ReservationsTables" (
  "reservation_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "complexe_id" integer NOT NULL,
  "service_id" integer NOT NULL,
  "table_id" integer NOT NULL,
  "client_id" integer,
  "employe_id" integer NOT NULL,
  "date_debut" timestamp NOT NULL,
  "date_fin" timestamp NOT NULL,
  "nb_personnes" integer NOT NULL,
  "statut" varchar NOT NULL,
  "commentaires" text,
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "Commandes" (
  "commande_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "complexe_id" integer NOT NULL,
  "service_id" integer NOT NULL,
  "table_id" integer,
  "client_id" integer,
  "employe_id" integer NOT NULL,
  "date_commande" timestamp DEFAULT (now()),
  "type" varchar NOT NULL,
  "statut" varchar NOT NULL,
  "montant_total" decimal,
  "montant_tva" decimal,
  "note" text,
  "code_promo_id" integer,
  "reduction_appliquee" decimal DEFAULT 0,
  "reservation_id" integer,
  "chambre_id" integer,
  "statut_paiement" varchar DEFAULT 'Impayé',
  "mode_paiement" varchar DEFAULT 'Non spécifié',
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

CREATE TABLE "DetailsCommandes" (
  "detail_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "complexe_id" integer NOT NULL,
  "service_id" integer NOT NULL,
  "commande_id" integer NOT NULL,
  "produit_id" integer NOT NULL,
  "quantite" integer NOT NULL,
  "prix_unitaire" decimal NOT NULL,
  "remise" decimal DEFAULT 0,
  "montant_ligne" decimal NOT NULL,
  "tva" decimal NOT NULL,
  "commentaires" text,
  "statut" varchar DEFAULT 'En attente',
  "code_promo_id" integer,
  "remise_promo" decimal DEFAULT 0,
  "heure_demande" timestamp DEFAULT (now()),
  "heure_preparation" timestamp,
  "heure_servi" timestamp,
  "employe_preparation" integer
);


COMMENT ON COLUMN "ComplexesHoteliers"."type_etablissement" IS 'Hôtel, Résidence, Auberge, Complexe';

COMMENT ON COLUMN "ServicesComplexe"."type_service" IS 'Hébergement, Restaurant, Bar, Piscine, Spa, Fitness, Boutique, Événementiel, Autre';

COMMENT ON COLUMN "Employes"."type_employe" IS 'reception, gerant_piscine, serveuse, gerant_services, cuisine';

COMMENT ON COLUMN "Employes"."services_autorises" IS 'Array JSON des services autorisés: ["bar", "restaurant", "piscine"]';

COMMENT ON COLUMN "PointsDeVente"."statut" IS 'Actif, Inactif, Maintenance';

COMMENT ON COLUMN "SessionsCaisse"."statut" IS 'Ouverte, Fermée, Vérifiée';

COMMENT ON COLUMN "CategoriesProduits"."niveau" IS 'Chaîne, Complexe, Service';

COMMENT ON COLUMN "Produits"."niveau" IS 'Chaîne, Complexe, Service';

COMMENT ON COLUMN "Produits"."type_produit" IS 'Stockable, Consommable, Service';

COMMENT ON COLUMN "Chambres"."type_chambre" IS 'Standard, VIP, Suite';

COMMENT ON COLUMN "Chambres"."statut" IS 'Disponible, Occupée, Maintenance';

COMMENT ON COLUMN "Reservations"."statut" IS 'Confirmée, En attente, Annulée, Terminée, No-show';


COMMENT ON COLUMN "ChambresReservees"."statut" IS 'Réservée, CheckIn, CheckOut, Annulée';

COMMENT ON COLUMN "TransactionsPOS"."mode_paiement" IS 'Espèces, Carte, Virement, Chèque, Mobile';

COMMENT ON COLUMN "TransactionsPOS"."statut" IS 'En cours, Payée, Annulée, Remboursée';

COMMENT ON COLUMN "Paiements"."mode_paiement" IS 'Espèces, Carte, Virement, Chèque, Mobile';

COMMENT ON COLUMN "Fidelite"."niveau" IS 'Standard, Silver, Gold, Platinum';

COMMENT ON COLUMN "CodesPromo"."niveau" IS 'Chaîne, Complexe, Service';

COMMENT ON COLUMN "CodesPromo"."type_remise" IS 'Pourcentage, Montant, Gratuit, Nuitée gratuite';

COMMENT ON COLUMN "CodesPromo"."categorie" IS 'Hébergement, Restauration, Piscine, POS, Global';

COMMENT ON COLUMN "CodesPromo"."applicable_sur" IS 'Tout, Produits, Catégories, Chambres';

COMMENT ON COLUMN "Fournisseurs"."niveau" IS 'Chaîne, Complexe';

COMMENT ON COLUMN "CommandesFournisseurs"."statut" IS 'Brouillon, Confirmée, En cours, Livrée partiellement, Livrée, Annulée';

COMMENT ON COLUMN "MouvementsStock"."type_mouvement" IS 'Réception, Vente, Ajustement, Perte, Retour, Transfert';

COMMENT ON COLUMN "Inventaires"."statut" IS 'Planifié, En cours, Terminé, Annulé';

COMMENT ON COLUMN "Tables"."statut" IS 'Libre, Occupée, Réservée, Maintenance';

COMMENT ON COLUMN "Tables"."shape" IS 'Rectangle, Ronde, Carrée, Ovale';

COMMENT ON COLUMN "ReservationsTables"."statut" IS 'Confirmée, En attente, Annulée, Terminée';

COMMENT ON COLUMN "Commandes"."type" IS 'Restaurant, Bar, Room service, Événement';

COMMENT ON COLUMN "Commandes"."statut" IS 'En cours, Servie, Payée, Annulée, Facturée à la chambre';

COMMENT ON COLUMN "Commandes"."statut_paiement" IS 'Impayé, Partiel, Payé';

COMMENT ON COLUMN "Commandes"."mode_paiement" IS 'Non spécifié, Espèces, Carte, Virement';

COMMENT ON COLUMN "DetailsCommandes"."statut" IS 'En attente, En préparation, Prêt, Servi, Annulé, Remboursé';

ALTER TABLE "ChainesHotelieres" ADD FOREIGN KEY ("superadmin_createur_id") REFERENCES "UtilisateursSuperAdmin" ("superadmin_id");

ALTER TABLE "AdminsChaine" ADD FOREIGN KEY ("chaine_id") REFERENCES "ChainesHotelieres" ("chaine_id");

ALTER TABLE "ComplexesHoteliers" ADD FOREIGN KEY ("chaine_id") REFERENCES "ChainesHotelieres" ("chaine_id");

ALTER TABLE "ServicesComplexe" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "AdminsComplexe" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "RolesComplexe" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "RolesComplexe" ADD FOREIGN KEY ("service_id") REFERENCES "ServicesComplexe" ("service_id");

ALTER TABLE "Employes" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "Employes" ADD FOREIGN KEY ("service_id") REFERENCES "ServicesComplexe" ("service_id");

ALTER TABLE "Employes" ADD FOREIGN KEY ("role_id") REFERENCES "RolesComplexe" ("role_id");

ALTER TABLE "PointsDeVente" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "PointsDeVente" ADD FOREIGN KEY ("service_id") REFERENCES "ServicesComplexe" ("service_id");

ALTER TABLE "PointsDeVente" ADD FOREIGN KEY ("employe_actuel_id") REFERENCES "Employes" ("employe_id");

ALTER TABLE "SessionsCaisse" ADD FOREIGN KEY ("pos_id") REFERENCES "PointsDeVente" ("pos_id");

ALTER TABLE "SessionsCaisse" ADD FOREIGN KEY ("employe_id") REFERENCES "Employes" ("employe_id");

ALTER TABLE "SessionsCaisse" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "SessionsCaisse" ADD FOREIGN KEY ("service_id") REFERENCES "ServicesComplexe" ("service_id");

ALTER TABLE "CategoriesProduits" ADD FOREIGN KEY ("chaine_id") REFERENCES "ChainesHotelieres" ("chaine_id");

ALTER TABLE "CategoriesProduits" ADD FOREIGN KEY ("parent_id") REFERENCES "CategoriesProduits" ("categorie_id");

ALTER TABLE "CategoriesProduits" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "CategoriesProduits" ADD FOREIGN KEY ("service_id") REFERENCES "ServicesComplexe" ("service_id");

ALTER TABLE "Produits" ADD FOREIGN KEY ("categorie_id") REFERENCES "CategoriesProduits" ("categorie_id");

ALTER TABLE "Produits" ADD FOREIGN KEY ("chaine_id") REFERENCES "ChainesHotelieres" ("chaine_id");

ALTER TABLE "Produits" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "Produits" ADD FOREIGN KEY ("service_id") REFERENCES "ServicesComplexe" ("service_id");

ALTER TABLE "PrixProduits" ADD FOREIGN KEY ("produit_id") REFERENCES "Produits" ("produit_id");

ALTER TABLE "PrixProduits" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "PrixProduits" ADD FOREIGN KEY ("service_id") REFERENCES "ServicesComplexe" ("service_id");

ALTER TABLE "Chambres" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "Chambres" ADD FOREIGN KEY ("service_id") REFERENCES "ServicesComplexe" ("service_id");

ALTER TABLE "Clients" ADD FOREIGN KEY ("chaine_id") REFERENCES "ChainesHotelieres" ("chaine_id");

ALTER TABLE "Clients" ADD FOREIGN KEY ("complexe_creation_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "Reservations" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");


ALTER TABLE "Reservations" ADD FOREIGN KEY ("client_id") REFERENCES "Clients" ("client_id");

ALTER TABLE "Reservations" ADD FOREIGN KEY ("code_promo_id") REFERENCES "CodesPromo" ("code_promo_id");

ALTER TABLE "Reservations" ADD FOREIGN KEY ("employe_id") REFERENCES "Employes" ("employe_id");

ALTER TABLE "ChambresReservees" ADD FOREIGN KEY ("reservation_id") REFERENCES "Reservations" ("reservation_id");

ALTER TABLE "ChambresReservees" ADD FOREIGN KEY ("chambre_id") REFERENCES "Chambres" ("chambre_id");

ALTER TABLE "TransactionsPOS" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "TransactionsPOS" ADD FOREIGN KEY ("service_id") REFERENCES "ServicesComplexe" ("service_id");

ALTER TABLE "TransactionsPOS" ADD FOREIGN KEY ("pos_id") REFERENCES "PointsDeVente" ("pos_id");

ALTER TABLE "TransactionsPOS" ADD FOREIGN KEY ("session_id") REFERENCES "SessionsCaisse" ("session_id");

ALTER TABLE "TransactionsPOS" ADD FOREIGN KEY ("employe_id") REFERENCES "Employes" ("employe_id");

ALTER TABLE "TransactionsPOS" ADD FOREIGN KEY ("client_id") REFERENCES "Clients" ("client_id");

ALTER TABLE "TransactionsPOS" ADD FOREIGN KEY ("chambre_id") REFERENCES "Chambres" ("chambre_id");

ALTER TABLE "TransactionsPOS" ADD FOREIGN KEY ("code_promo_id") REFERENCES "CodesPromo" ("code_promo_id");

ALTER TABLE "LignesTransactionPOS" ADD FOREIGN KEY ("transaction_id") REFERENCES "TransactionsPOS" ("transaction_id");

ALTER TABLE "LignesTransactionPOS" ADD FOREIGN KEY ("produit_id") REFERENCES "Produits" ("produit_id");

ALTER TABLE "LignesTransactionPOS" ADD FOREIGN KEY ("code_promo_id") REFERENCES "CodesPromo" ("code_promo_id");

ALTER TABLE "Paiements" ADD FOREIGN KEY ("transaction_id") REFERENCES "TransactionsPOS" ("transaction_id");

ALTER TABLE "Fidelite" ADD FOREIGN KEY ("chaine_id") REFERENCES "ChainesHotelieres" ("chaine_id");

ALTER TABLE "Fidelite" ADD FOREIGN KEY ("client_id") REFERENCES "Clients" ("client_id");

ALTER TABLE "CodesPromo" ADD FOREIGN KEY ("chaine_id") REFERENCES "ChainesHotelieres" ("chaine_id");

ALTER TABLE "CodesPromo" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");


ALTER TABLE "UtilisationsCodes" ADD FOREIGN KEY ("chaine_id") REFERENCES "ChainesHotelieres" ("chaine_id");

ALTER TABLE "UtilisationsCodes" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");


ALTER TABLE "UtilisationsCodes" ADD FOREIGN KEY ("code_promo_id") REFERENCES "CodesPromo" ("code_promo_id");

ALTER TABLE "UtilisationsCodes" ADD FOREIGN KEY ("client_id") REFERENCES "Clients" ("client_id");

ALTER TABLE "UtilisationsCodes" ADD FOREIGN KEY ("reservation_id") REFERENCES "Reservations" ("reservation_id");

ALTER TABLE "UtilisationsCodes" ADD FOREIGN KEY ("transaction_pos_id") REFERENCES "TransactionsPOS" ("transaction_id");

ALTER TABLE "UtilisationsCodes" ADD FOREIGN KEY ("employe_id") REFERENCES "Employes" ("employe_id");

ALTER TABLE "Fournisseurs" ADD FOREIGN KEY ("chaine_id") REFERENCES "ChainesHotelieres" ("chaine_id");

ALTER TABLE "Fournisseurs" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "CommandesFournisseurs" ADD FOREIGN KEY ("chaine_id") REFERENCES "ChainesHotelieres" ("chaine_id");

ALTER TABLE "CommandesFournisseurs" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "CommandesFournisseurs" ADD FOREIGN KEY ("service_id") REFERENCES "ServicesComplexe" ("service_id");

ALTER TABLE "CommandesFournisseurs" ADD FOREIGN KEY ("fournisseur_id") REFERENCES "Fournisseurs" ("fournisseur_id");

ALTER TABLE "CommandesFournisseurs" ADD FOREIGN KEY ("employe_id") REFERENCES "Employes" ("employe_id");

ALTER TABLE "DetailsCommandesFournisseurs" ADD FOREIGN KEY ("commande_id") REFERENCES "CommandesFournisseurs" ("commande_id");

ALTER TABLE "DetailsCommandesFournisseurs" ADD FOREIGN KEY ("produit_id") REFERENCES "Produits" ("produit_id");

ALTER TABLE "MouvementsStock" ADD FOREIGN KEY ("chaine_id") REFERENCES "ChainesHotelieres" ("chaine_id");

ALTER TABLE "MouvementsStock" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "MouvementsStock" ADD FOREIGN KEY ("service_id") REFERENCES "ServicesComplexe" ("service_id");

ALTER TABLE "MouvementsStock" ADD FOREIGN KEY ("produit_id") REFERENCES "Produits" ("produit_id");

ALTER TABLE "MouvementsStock" ADD FOREIGN KEY ("employe_id") REFERENCES "Employes" ("employe_id");

ALTER TABLE "Inventaires" ADD FOREIGN KEY ("chaine_id") REFERENCES "ChainesHotelieres" ("chaine_id");

ALTER TABLE "Inventaires" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "Inventaires" ADD FOREIGN KEY ("service_id") REFERENCES "ServicesComplexe" ("service_id");

ALTER TABLE "Inventaires" ADD FOREIGN KEY ("employe_id") REFERENCES "Employes" ("employe_id");

ALTER TABLE "LignesInventaire" ADD FOREIGN KEY ("inventaire_id") REFERENCES "Inventaires" ("inventaire_id");

ALTER TABLE "LignesInventaire" ADD FOREIGN KEY ("produit_id") REFERENCES "Produits" ("produit_id");

ALTER TABLE "Tables" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "Tables" ADD FOREIGN KEY ("service_id") REFERENCES "ServicesComplexe" ("service_id");

ALTER TABLE "ReservationsTables" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "ReservationsTables" ADD FOREIGN KEY ("service_id") REFERENCES "ServicesComplexe" ("service_id");

ALTER TABLE "ReservationsTables" ADD FOREIGN KEY ("table_id") REFERENCES "Tables" ("table_id");

ALTER TABLE "ReservationsTables" ADD FOREIGN KEY ("client_id") REFERENCES "Clients" ("client_id");

ALTER TABLE "ReservationsTables" ADD FOREIGN KEY ("employe_id") REFERENCES "Employes" ("employe_id");

ALTER TABLE "Commandes" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "Commandes" ADD FOREIGN KEY ("service_id") REFERENCES "ServicesComplexe" ("service_id");

ALTER TABLE "Commandes" ADD FOREIGN KEY ("table_id") REFERENCES "Tables" ("table_id");

ALTER TABLE "Commandes" ADD FOREIGN KEY ("client_id") REFERENCES "Clients" ("client_id");

ALTER TABLE "Commandes" ADD FOREIGN KEY ("employe_id") REFERENCES "Employes" ("employe_id");

ALTER TABLE "Commandes" ADD FOREIGN KEY ("code_promo_id") REFERENCES "CodesPromo" ("code_promo_id");

ALTER TABLE "Commandes" ADD FOREIGN KEY ("reservation_id") REFERENCES "ReservationsTables" ("reservation_id");

ALTER TABLE "Commandes" ADD FOREIGN KEY ("chambre_id") REFERENCES "Chambres" ("chambre_id");

ALTER TABLE "DetailsCommandes" ADD FOREIGN KEY ("complexe_id") REFERENCES "ComplexesHoteliers" ("complexe_id");

ALTER TABLE "DetailsCommandes" ADD FOREIGN KEY ("service_id") REFERENCES "ServicesComplexe" ("service_id");

ALTER TABLE "DetailsCommandes" ADD FOREIGN KEY ("commande_id") REFERENCES "Commandes" ("commande_id");

ALTER TABLE "DetailsCommandes" ADD FOREIGN KEY ("produit_id") REFERENCES "Produits" ("produit_id");

ALTER TABLE "DetailsCommandes" ADD FOREIGN KEY ("code_promo_id") REFERENCES "CodesPromo" ("code_promo_id");

ALTER TABLE "DetailsCommandes" ADD FOREIGN KEY ("employe_preparation") REFERENCES "Employes" ("employe_id");

CREATE TABLE "HistoriqueReservations" (
  "historique_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "reservation_id" integer NOT NULL,
  "action" varchar NOT NULL,
  "statut_avant" varchar,
  "statut_apres" varchar,
  "employe_id" integer,
  "commentaire" text,
  "date_action" timestamp DEFAULT (now()),
  "metadata" json,
  FOREIGN KEY (reservation_id) REFERENCES "Reservations" (reservation_id),
  FOREIGN KEY (employe_id) REFERENCES "Employes" (employe_id)
);

CREATE INDEX idx_historique_reservation ON "HistoriqueReservations" (reservation_id);
CREATE INDEX idx_historique_date ON "HistoriqueReservations" (date_action);

CREATE TABLE "Tickets" (
  "ticket_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "reservation_id" integer NOT NULL,
  "numero_ticket" varchar UNIQUE NOT NULL,
  "qr_code_data" json NOT NULL,
  "date_emission" timestamp DEFAULT (now()),
  "employe_emission_id" integer NOT NULL,
  "statut" varchar DEFAULT 'ACTIF',
  "copie_client" boolean DEFAULT true,
  "copie_reception" boolean DEFAULT true,
  "date_impression" timestamp,
  "metadata" json,
  FOREIGN KEY (reservation_id) REFERENCES "Reservations" (reservation_id),
  FOREIGN KEY (employe_emission_id) REFERENCES "Employes" (employe_id)
);

CREATE INDEX idx_tickets_reservation ON "Tickets" (reservation_id);
CREATE INDEX idx_tickets_numero ON "Tickets" (numero_ticket);

CREATE TABLE "DisponibilitesChambres" (
  "disponibilite_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "chambre_id" integer NOT NULL,
  "date" date NOT NULL,
  "heure_debut" time NOT NULL,
  "heure_fin" time NOT NULL,
  "statut" varchar NOT NULL,
  "reservation_id" integer,
  "type_occupation" varchar DEFAULT 'HEURE',
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp,
  FOREIGN KEY (chambre_id) REFERENCES "Chambres" (chambre_id),
  FOREIGN KEY (reservation_id) REFERENCES "Reservations" (reservation_id),
  CONSTRAINT "check_type_occupation_dispo" CHECK (type_occupation IN ('NUIT', 'HEURE', 'JOURNEE')),
  CONSTRAINT "check_statut_dispo" CHECK (statut IN ('disponible', 'occupee', 'maintenance', 'bloquee')),
  CONSTRAINT "check_heures_valides" CHECK (heure_debut < heure_fin)
);

CREATE INDEX idx_disponibilites_chambre_date ON "DisponibilitesChambres" (chambre_id, date);
CREATE INDEX idx_disponibilites_statut ON "DisponibilitesChambres" (statut);
CREATE INDEX idx_disponibilites_heures ON "DisponibilitesChambres" (heure_debut, heure_fin);

CREATE TABLE "HistoriqueActions" (
  "historique_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "type_action" varchar NOT NULL,
  "entite_id" integer NOT NULL,
  "entite_type" varchar NOT NULL,
  "utilisateur_id" integer,
  "details" json,
  "statut_avant" varchar,
  "statut_apres" varchar,
  "date_action" timestamp DEFAULT (now()),
  CONSTRAINT "check_entite_type" 
  CHECK (entite_type IN ('RESERVATION', 'PAIEMENT', 'TICKET', 'TRANSACTION', 'CHAMBRE', 'CLIENT'))
);

CREATE TABLE "HistoriquePaiements" (
  "historique_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "paiement_id" integer NOT NULL,
  "montant" decimal NOT NULL,
  "type_paiement" varchar NOT NULL,
  "statut" varchar NOT NULL,
  "details" json,
  "date_paiement" timestamp DEFAULT (now()),
  FOREIGN KEY (paiement_id) REFERENCES "Paiements" (paiement_id)
);

CREATE TABLE "HistoriqueTickets" (
  "historique_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "ticket_id" integer NOT NULL,
  "utilisateur_id" integer,
  "type_utilisation" varchar NOT NULL,
  "details" json,
  "date_utilisation" timestamp DEFAULT (now()),
  FOREIGN KEY (ticket_id) REFERENCES "Tickets" (ticket_id),
  FOREIGN KEY (utilisateur_id) REFERENCES "Employes" (employe_id)
);

CREATE INDEX idx_historique_actions_entite ON "HistoriqueActions" (entite_id, entite_type);
CREATE INDEX idx_historique_actions_date ON "HistoriqueActions" (date_action);
CREATE INDEX idx_historique_actions_utilisateur ON "HistoriqueActions" (utilisateur_id);

CREATE INDEX idx_historique_paiements_paiement ON "HistoriquePaiements" (paiement_id);
CREATE INDEX idx_historique_paiements_date ON "HistoriquePaiements" (date_paiement);

CREATE INDEX idx_historique_tickets_ticket ON "HistoriqueTickets" (ticket_id);
CREATE INDEX idx_historique_tickets_utilisateur ON "HistoriqueTickets" (utilisateur_id);
CREATE INDEX idx_historique_tickets_date ON "HistoriqueTickets" (date_utilisation);