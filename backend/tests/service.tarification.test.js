const ServiceComplexeService = require('../services/service.service');

describe('ServiceComplexeService - Gestion des Tarifications', () => {
  
  describe('validateTarification', () => {
    
    test('should validate restaurant tarification correctly', () => {
      const tarificationData = {
        menus: {
          "Menu du jour": 25.00,
          "Menu enfant": 15.00
        },
        boissons: {
          "Eau plate": 3.00,
          "Soda": 4.00
        },
        service_table: 2.00,
        couvert_par_personne: 1.50
      };

      const result = ServiceComplexeService.validateTarification('Restaurant', tarificationData);
      
      expect(result).toHaveProperty('menus');
      expect(result).toHaveProperty('boissons');
      expect(result).toHaveProperty('service_table');
      expect(result).toHaveProperty('couvert_par_personne');
      expect(result.menus["Menu du jour"]).toBe(25.00);
      expect(result.service_table).toBe(2.00);
    });

    test('should validate bar tarification correctly', () => {
      const tarificationData = {
        alcools: {
          "Bière": 5.00,
          "Vin rouge": 7.00
        },
        soft_drinks: {
          "Coca-Cola": 4.00
        },
        cocktails: {
          "Mojito": 12.00
        },
        happy_hour: {
          reduction_pourcentage: 20,
          heures: ["17:00-19:00"]
        }
      };

      const result = ServiceComplexeService.validateTarification('Bar', tarificationData);
      
      expect(result).toHaveProperty('alcools');
      expect(result).toHaveProperty('soft_drinks');
      expect(result).toHaveProperty('cocktails');
      expect(result).toHaveProperty('happy_hour');
      expect(result.alcools["Bière"]).toBe(5.00);
      expect(result.happy_hour.reduction_pourcentage).toBe(20);
    });

    test('should validate piscine tarification correctly', () => {
      const tarificationData = {
        prix_par_personne: 10.00,
        prix_par_heure: 5.00,
        prix_forfaitaire: 25.00,
        tarifs_age: {
          "Enfant": 5.00,
          "Adulte": 10.00
        },
        tarifs_duree: {
          "1 heure": 10.00,
          "Journée": 40.00
        }
      };

      const result = ServiceComplexeService.validateTarification('Piscine', tarificationData);
      
      expect(result).toHaveProperty('prix_par_personne');
      expect(result).toHaveProperty('prix_par_heure');
      expect(result).toHaveProperty('prix_forfaitaire');
      expect(result).toHaveProperty('tarifs_age');
      expect(result).toHaveProperty('tarifs_duree');
      expect(result.prix_par_personne).toBe(10.00);
      expect(result.tarifs_age["Enfant"]).toBe(5.00);
    });

    test('should reject negative prices', () => {
      const tarificationData = {
        menus: {
          "Menu du jour": -25.00  // Prix négatif
        }
      };

      const result = ServiceComplexeService.validateTarification('Restaurant', tarificationData);
      
      // Les prix négatifs doivent être filtrés
      expect(result.menus).toEqual({});
    });

    test('should handle invalid data types', () => {
      expect(() => {
        ServiceComplexeService.validateTarification('Restaurant', null);
      }).toThrow('Les données de tarification doivent être un objet JSON valide');

      expect(() => {
        ServiceComplexeService.validateTarification('Restaurant', "invalid");
      }).toThrow('Les données de tarification doivent être un objet JSON valide');
    });

  });

  describe('getDefaultTarificationTemplate', () => {
    
    test('should return restaurant template', () => {
      const template = ServiceComplexeService.getDefaultTarificationTemplate('Restaurant');
      
      expect(template).toHaveProperty('menus');
      expect(template).toHaveProperty('boissons');
      expect(template).toHaveProperty('service_table');
      expect(template).toHaveProperty('couvert_par_personne');
      expect(typeof template.menus).toBe('object');
      expect(typeof template.service_table).toBe('number');
    });

    test('should return bar template', () => {
      const template = ServiceComplexeService.getDefaultTarificationTemplate('Bar');
      
      expect(template).toHaveProperty('alcools');
      expect(template).toHaveProperty('soft_drinks');
      expect(template).toHaveProperty('cocktails');
      expect(template).toHaveProperty('happy_hour');
      expect(typeof template.alcools).toBe('object');
      expect(Array.isArray(template.happy_hour.heures)).toBe(true);
    });

    test('should return piscine template', () => {
      const template = ServiceComplexeService.getDefaultTarificationTemplate('Piscine');
      
      expect(template).toHaveProperty('prix_par_personne');
      expect(template).toHaveProperty('prix_par_heure');
      expect(template).toHaveProperty('prix_forfaitaire');
      expect(template).toHaveProperty('tarifs_age');
      expect(template).toHaveProperty('tarifs_duree');
      expect(typeof template.prix_par_personne).toBe('number');
      expect(typeof template.tarifs_age).toBe('object');
    });

    test('should return default template for unknown service type', () => {
      const template = ServiceComplexeService.getDefaultTarificationTemplate('Unknown');
      
      expect(template).toHaveProperty('prix_base');
      expect(template).toHaveProperty('description');
      expect(template.prix_base).toBe(0.00);
    });

  });

  describe('validateBasicTarification', () => {
    
    test('should validate basic tarification with numeric values', () => {
      const tarificationData = {
        prix_base: 10.00,
        prix_premium: 15.00,
        reduction: 5.00
      };

      const result = ServiceComplexeService.validateBasicTarification(tarificationData);
      
      expect(result.prix_base).toBe(10.00);
      expect(result.prix_premium).toBe(15.00);
      expect(result.reduction).toBe(5.00);
    });

    test('should filter out negative values', () => {
      const tarificationData = {
        prix_base: 10.00,
        prix_negatif: -5.00,
        prix_zero: 0.00
      };

      const result = ServiceComplexeService.validateBasicTarification(tarificationData);
      
      expect(result.prix_base).toBe(10.00);
      expect(result.prix_zero).toBe(0.00);
      expect(result).not.toHaveProperty('prix_negatif');
    });

    test('should handle nested objects', () => {
      const tarificationData = {
        categories: {
          standard: 10.00,
          premium: 20.00,
          invalid: -5.00
        }
      };

      const result = ServiceComplexeService.validateBasicTarification(tarificationData);
      
      expect(result.categories.standard).toBe(10.00);
      expect(result.categories.premium).toBe(20.00);
      expect(result.categories).not.toHaveProperty('invalid');
    });

  });

});

// Tests d'intégration (nécessitent une base de données de test)
describe('ServiceComplexeService - Intégration Tarifications', () => {
  
  // Ces tests nécessiteraient une configuration de base de données de test
  // et seraient exécutés dans un environnement de test séparé
  
  test.skip('should get tarification by service ID', async () => {
    // Test d'intégration pour getTarificationByService
  });

  test.skip('should update tarification', async () => {
    // Test d'intégration pour updateTarification
  });

});
