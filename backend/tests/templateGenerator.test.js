const request = require('supertest');
const express = require('express');
const path = require('path');
const fs = require('fs');
const TemplateGeneratorService = require('../services/templateGenerator.service');
const TemplateGeneratorController = require('../controllers/templateGenerator.controller');

// Mock des middlewares avant d'importer les routes
jest.mock('../middleware/auth', () => ({
  verifyToken: (req, res, next) => {
    req.user = { id: 1, role: 'admin' };
    next();
  }
}));

jest.mock('../middleware/permission', () => ({
  checkPermission: (permission) => (req, res, next) => {
    next();
  }
}));

const templateGeneratorRoutes = require('../routes/templateGenerator.routes');

// Setup de l'app de test
const app = express();
app.use(express.json());
app.use('/api/template-generator', templateGeneratorRoutes);

describe('TemplateGenerator Service', () => {
  
  describe('generateRestaurantTemplate', () => {
    test('should generate restaurant template successfully', () => {
      const workbook = TemplateGeneratorService.generateRestaurantTemplate();
      
      expect(workbook).toBeDefined();
      expect(workbook.SheetNames).toContain('Menu Restaurant');
      
      const worksheet = workbook.Sheets['Menu Restaurant'];
      expect(worksheet).toBeDefined();
      
      // Vérifier les headers
      expect(worksheet['A1'].v).toBe('nom_plat');
      expect(worksheet['B1'].v).toBe('description');
      expect(worksheet['C1'].v).toBe('categorie');
      expect(worksheet['D1'].v).toBe('prix_vente');
    });
  });

  describe('generateBarTemplate', () => {
    test('should generate bar template successfully', () => {
      const workbook = TemplateGeneratorService.generateBarTemplate();
      
      expect(workbook).toBeDefined();
      expect(workbook.SheetNames).toContain('Carte Bar');
      
      const worksheet = workbook.Sheets['Carte Bar'];
      expect(worksheet).toBeDefined();
      
      // Vérifier les headers
      expect(worksheet['A1'].v).toBe('nom_boisson');
      expect(worksheet['B1'].v).toBe('description');
      expect(worksheet['C1'].v).toBe('categorie');
      expect(worksheet['E1'].v).toBe('degre_alcool');
    });
  });

  describe('generateCuisineIngredientTemplate', () => {
    test('should generate cuisine ingredient template successfully', () => {
      const workbook = TemplateGeneratorService.generateCuisineIngredientTemplate();
      
      expect(workbook).toBeDefined();
      expect(workbook.SheetNames).toContain('Ingrédients Cuisine');
      
      const worksheet = workbook.Sheets['Ingrédients Cuisine'];
      expect(worksheet).toBeDefined();
      
      // Vérifier les headers
      expect(worksheet['A1'].v).toBe('nom_ingredient');
      expect(worksheet['D1'].v).toBe('unite_mesure');
      expect(worksheet['E1'].v).toBe('prix_unitaire');
    });
  });

  describe('generateBoissonInventoryTemplate', () => {
    test('should generate boisson inventory template successfully', () => {
      const workbook = TemplateGeneratorService.generateBoissonInventoryTemplate();
      
      expect(workbook).toBeDefined();
      expect(workbook.SheetNames).toContain('Inventaire Boissons');
      
      const worksheet = workbook.Sheets['Inventaire Boissons'];
      expect(worksheet).toBeDefined();
      
      // Vérifier les headers spécifiques aux boissons
      expect(worksheet['A1'].v).toBe('nom_boisson');
      expect(worksheet['D1'].v).toBe('type_conditionnement');
      expect(worksheet['E1'].v).toBe('volume_unitaire');
      expect(worksheet['F1'].v).toBe('prix_achat_unitaire');
      expect(worksheet['G1'].v).toBe('prix_vente_unitaire');
    });
  });

  describe('templateExists', () => {
    test('should return false for non-existent template', () => {
      const exists = TemplateGeneratorService.templateExists('non-existent-template.xlsx');
      expect(exists).toBe(false);
    });
  });

  describe('getTemplatePath', () => {
    test('should return correct template path', () => {
      const templatePath = TemplateGeneratorService.getTemplatePath('test-template.xlsx');
      expect(templatePath).toContain('templates');
      expect(templatePath).toContain('test-template.xlsx');
    });
  });
});

describe('TemplateGenerator API Routes', () => {
  
  describe('GET /api/template-generator/info', () => {
    test('should return template information', async () => {
      const response = await request(app)
        .get('/api/template-generator/info')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.templates).toHaveLength(4);
      expect(response.body.data.usage).toBeDefined();
    });
  });

  describe('GET /api/template-generator/list', () => {
    test('should return list of templates', async () => {
      const response = await request(app)
        .get('/api/template-generator/list')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(4);
      
      const restaurantTemplate = response.body.data.find(t => t.name === 'Menu Restaurant');
      expect(restaurantTemplate).toBeDefined();
      expect(restaurantTemplate.filename).toBe('restaurant-menu-template.xlsx');
    });
  });

  describe('POST /api/template-generator/generate-all', () => {
    test('should generate all templates', async () => {
      const response = await request(app)
        .post('/api/template-generator/generate-all')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(4);
      
      // Vérifier que les fichiers ont été créés
      response.body.data.forEach(template => {
        expect(template.success).toBe(true);
        expect(template.filename).toBeDefined();
      });
    });
  });
});

describe('Template File Generation Integration', () => {
  const templatesDir = path.join(__dirname, '../templates');
  
  beforeAll(async () => {
    // S'assurer que le dossier templates existe
    if (!fs.existsSync(templatesDir)) {
      fs.mkdirSync(templatesDir, { recursive: true });
    }
  });

  test('should generate and save all templates to filesystem', async () => {
    const result = await TemplateGeneratorService.generateAllTemplates();
    
    expect(result.success).toBe(true);
    expect(result.data).toHaveLength(4);
    
    // Vérifier que tous les fichiers existent
    const expectedFiles = [
      'restaurant-menu-template.xlsx',
      'bar-menu-template.xlsx',
      'cuisine-ingredients-template.xlsx',
      'boisson-inventory-template.xlsx'
    ];
    
    expectedFiles.forEach(filename => {
      const filepath = path.join(templatesDir, filename);
      expect(fs.existsSync(filepath)).toBe(true);
      
      // Vérifier que le fichier n'est pas vide
      const stats = fs.statSync(filepath);
      expect(stats.size).toBeGreaterThan(0);
    });
  });

  test('should detect existing templates correctly', () => {
    const exists = TemplateGeneratorService.templateExists('restaurant-menu-template.xlsx');
    expect(exists).toBe(true);
  });
});

describe('Template Content Validation', () => {
  test('restaurant template should have correct example data', () => {
    const workbook = TemplateGeneratorService.generateRestaurantTemplate();
    const worksheet = workbook.Sheets['Menu Restaurant'];
    
    // Vérifier les données d'exemple
    expect(worksheet['A2'].v).toBe('Salade César');
    expect(worksheet['C2'].v).toBe('Entrée');
    expect(worksheet['D2'].v).toBe(12.50);
  });

  test('bar template should have correct example data', () => {
    const workbook = TemplateGeneratorService.generateBarTemplate();
    const worksheet = workbook.Sheets['Carte Bar'];
    
    // Vérifier les données d'exemple
    expect(worksheet['A2'].v).toBe('Mojito');
    expect(worksheet['C2'].v).toBe('Cocktail');
    expect(worksheet['E2'].v).toBe(15.0);
  });

  test('cuisine ingredient template should have correct example data', () => {
    const workbook = TemplateGeneratorService.generateCuisineIngredientTemplate();
    const worksheet = workbook.Sheets['Ingrédients Cuisine'];
    
    // Vérifier les données d'exemple
    expect(worksheet['A2'].v).toBe('Tomates fraîches');
    expect(worksheet['C2'].v).toBe('Légume');
    expect(worksheet['D2'].v).toBe('kg');
  });

  test('boisson inventory template should have correct example data', () => {
    const workbook = TemplateGeneratorService.generateBoissonInventoryTemplate();
    const worksheet = workbook.Sheets['Inventaire Boissons'];
    
    // Vérifier les données d'exemple
    expect(worksheet['A2'].v).toBe('Heineken');
    expect(worksheet['C2'].v).toBe('Bière');
    expect(worksheet['D2'].v).toBe('Bouteille');
    expect(worksheet['E2'].v).toBe(0.33);
  });
});
