const request = require('supertest');
const express = require('express');
const XLSX = require('xlsx');
const MenuImportService = require('../services/menuImport.service');
const IngredientImportService = require('../services/ingredientImport.service');

// Mock des middlewares avant d'importer les routes
jest.mock('../middleware/auth', () => ({
  verifyToken: (req, res, next) => {
    req.user = { id: 1, role: 'admin' };
    next();
  }
}));

jest.mock('../middleware/permission', () => ({
  checkPermission: (permission) => (req, res, next) => {
    next();
  }
}));

// Mock de la base de données
jest.mock('../db', () => ({
  getClient: jest.fn(() => ({
    query: jest.fn(),
    release: jest.fn()
  }))
}));

const menuImportRoutes = require('../routes/menuImport.routes');
const ingredientImportRoutes = require('../routes/ingredientImport.routes');

// Setup de l'app de test
const app = express();
app.use(express.json());
app.use('/api/menu-import', menuImportRoutes);
app.use('/api/ingredient-import', ingredientImportRoutes);

describe('MenuImport Service', () => {
  
  describe('validateRestaurantMenuData', () => {
    test('should validate correct restaurant menu data', () => {
      const data = [
        {
          nom_plat: 'Salade César',
          categorie: 'Entrée',
          prix_vente: 12.50,
          description: 'Salade fraîche',
          temps_preparation: 15,
          allergenes: 'Gluten, Lait',
          actif: true
        }
      ];

      const result = MenuImportService.validateRestaurantMenuData(data);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should detect missing required fields', () => {
      const data = [
        {
          nom_plat: 'Salade César',
          // categorie manquante
          prix_vente: 12.50
        }
      ];

      const result = MenuImportService.validateRestaurantMenuData(data);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Ligne 2: categorie est obligatoire');
    });

    test('should detect invalid price format', () => {
      const data = [
        {
          nom_plat: 'Salade César',
          categorie: 'Entrée',
          prix_vente: 'invalid_price'
        }
      ];

      const result = MenuImportService.validateRestaurantMenuData(data);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Ligne 2: prix_vente doit être un nombre');
    });

    test('should detect invalid category', () => {
      const data = [
        {
          nom_plat: 'Salade César',
          categorie: 'Catégorie Invalide',
          prix_vente: 12.50
        }
      ];

      const result = MenuImportService.validateRestaurantMenuData(data);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('catégorie doit être une des valeurs'))).toBe(true);
    });
  });

  describe('validateBarMenuData', () => {
    test('should validate correct bar menu data', () => {
      const data = [
        {
          nom_boisson: 'Mojito',
          categorie: 'Cocktail',
          prix_vente: 12.00,
          degre_alcool: 15.0,
          volume_ml: 250,
          description: 'Cocktail à la menthe'
        }
      ];

      const result = MenuImportService.validateBarMenuData(data);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should detect invalid alcohol degree', () => {
      const data = [
        {
          nom_boisson: 'Mojito',
          categorie: 'Cocktail',
          prix_vente: 12.00,
          degre_alcool: 'invalid_degree'
        }
      ];

      const result = MenuImportService.validateBarMenuData(data);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Ligne 2: degre_alcool doit être un nombre');
    });
  });
});

describe('IngredientImport Service', () => {
  
  describe('validateCuisineIngredientData', () => {
    test('should validate correct cuisine ingredient data', () => {
      const data = [
        {
          nom_ingredient: 'Tomates fraîches',
          categorie: 'Légume',
          unite_mesure: 'kg',
          prix_unitaire: 4.50,
          fournisseur: 'Marché Central',
          conservation: 'Frais',
          duree_conservation_jours: 7,
          stock_minimal: 5
        }
      ];

      const result = IngredientImportService.validateCuisineIngredientData(data);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should detect invalid unit of measure', () => {
      const data = [
        {
          nom_ingredient: 'Tomates fraîches',
          categorie: 'Légume',
          unite_mesure: 'invalid_unit',
          prix_unitaire: 4.50
        }
      ];

      const result = IngredientImportService.validateCuisineIngredientData(data);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('unite_mesure doit être une des valeurs'))).toBe(true);
    });
  });

  describe('validateBoissonInventoryData', () => {
    test('should validate correct boisson inventory data', () => {
      const data = [
        {
          nom_boisson: 'Heineken',
          categorie: 'Bière',
          type_conditionnement: 'Bouteille',
          volume_unitaire: 0.33,
          prix_achat_unitaire: 2.50,
          prix_vente_unitaire: 5.50,
          degre_alcool: 5.0,
          stock_minimal: 24,
          stock_maximal: 200
        }
      ];

      const result = IngredientImportService.validateBoissonInventoryData(data);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should detect invalid packaging type', () => {
      const data = [
        {
          nom_boisson: 'Heineken',
          categorie: 'Bière',
          type_conditionnement: 'Invalid Package',
          volume_unitaire: 0.33
        }
      ];

      const result = IngredientImportService.validateBoissonInventoryData(data);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('type_conditionnement doit être une des valeurs'))).toBe(true);
    });
  });
});

describe('Excel File Processing', () => {
  
  function createTestExcelBuffer(data, sheetName = 'Sheet1') {
    const worksheet = XLSX.utils.json_to_sheet(data);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  }

  test('should parse Excel file correctly', () => {
    const testData = [
      {
        nom_plat: 'Salade César',
        categorie: 'Entrée',
        prix_vente: 12.50,
        description: 'Salade fraîche'
      }
    ];

    const buffer = createTestExcelBuffer(testData);
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const parsedData = XLSX.utils.sheet_to_json(worksheet);

    expect(parsedData).toHaveLength(1);
    expect(parsedData[0].nom_plat).toBe('Salade César');
    expect(parsedData[0].prix_vente).toBe(12.50);
  });
});

describe('Menu Import API Routes', () => {
  
  describe('GET /api/menu-import/info', () => {
    test('should return menu import information', async () => {
      const response = await request(app)
        .get('/api/menu-import/info')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.supportedFormats).toContain('.xlsx');
      expect(response.body.data.requiredFields.restaurant).toContain('nom_plat');
      expect(response.body.data.requiredFields.bar).toContain('nom_boisson');
    });
  });

  describe('POST /api/menu-import/validate/restaurant', () => {
    test('should validate restaurant menu file', async () => {
      const testData = [
        {
          nom_plat: 'Salade César',
          categorie: 'Entrée',
          prix_vente: 12.50
        }
      ];

      const buffer = createTestExcelBuffer(testData);

      const response = await request(app)
        .post('/api/menu-import/validate/restaurant')
        .attach('menuFile', buffer, 'test-menu.xlsx')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.isValid).toBe(true);
      expect(response.body.data.rowCount).toBe(1);
    });

    test('should detect validation errors', async () => {
      const testData = [
        {
          nom_plat: 'Salade César',
          // categorie manquante
          prix_vente: 'invalid_price'
        }
      ];

      const buffer = createTestExcelBuffer(testData);

      const response = await request(app)
        .post('/api/menu-import/validate/restaurant')
        .attach('menuFile', buffer, 'test-menu.xlsx')
        .expect(200);

      expect(response.body.success).toBe(false);
      expect(response.body.data.isValid).toBe(false);
      expect(response.body.data.errors.length).toBeGreaterThan(0);
    });
  });
});

describe('Ingredient Import API Routes', () => {
  
  describe('GET /api/ingredient-import/info', () => {
    test('should return ingredient import information', async () => {
      const response = await request(app)
        .get('/api/ingredient-import/info')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.requiredFields.cuisine).toContain('nom_ingredient');
      expect(response.body.data.requiredFields.boissons).toContain('nom_boisson');
      expect(response.body.data.validValues.unitesMesure).toContain('kg');
    });
  });

  describe('POST /api/ingredient-import/validate/cuisine', () => {
    test('should validate cuisine ingredients file', async () => {
      const testData = [
        {
          nom_ingredient: 'Tomates fraîches',
          categorie: 'Légume',
          unite_mesure: 'kg',
          prix_unitaire: 4.50
        }
      ];

      const buffer = createTestExcelBuffer(testData);

      const response = await request(app)
        .post('/api/ingredient-import/validate/cuisine')
        .attach('ingredientFile', buffer, 'test-ingredients.xlsx')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.isValid).toBe(true);
      expect(response.body.data.rowCount).toBe(1);
    });
  });

  describe('POST /api/ingredient-import/validate/boissons', () => {
    test('should validate boisson inventory file', async () => {
      const testData = [
        {
          nom_boisson: 'Heineken',
          categorie: 'Bière',
          type_conditionnement: 'Bouteille',
          volume_unitaire: 0.33
        }
      ];

      const buffer = createTestExcelBuffer(testData);

      const response = await request(app)
        .post('/api/ingredient-import/validate/boissons')
        .attach('boissonFile', buffer, 'test-boissons.xlsx')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.isValid).toBe(true);
      expect(response.body.data.rowCount).toBe(1);
    });
  });
});

// Helper function pour créer un buffer Excel dans les tests
function createTestExcelBuffer(data, sheetName = 'Sheet1') {
  const worksheet = XLSX.utils.json_to_sheet(data);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
  return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
}
