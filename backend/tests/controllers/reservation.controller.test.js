const ReservationController = require('../../controllers/reservation.controller');
const ReservationService = require('../../services/reservation.service');
const logger = require('../../logger');

// Mock des dépendances
jest.mock('../../services/reservation.service');
jest.mock('../../logger');

describe('ReservationController', () => {
    let mockReq;
    let mockRes;

    beforeEach(() => {
        mockReq = {
            body: {},
            params: {},
            query: {}
        };
        mockRes = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    // Routes client
    describe('createDemandeReservation', () => {
        it('devrait créer une demande de réservation avec succès', async () => {
            const mockReservation = { id: 1, status: 'pending' };
            mockReq.body = {
                dateArrivee: '2024-03-01',
                dateDepart: '2024-03-05',
                nombrePersonnes: 2
            };
            ReservationService.createDemandeReservation.mockResolvedValue(mockReservation);

            await ReservationController.createDemandeReservation(mockReq, mockRes);

            expect(ReservationService.createDemandeReservation).toHaveBeenCalledWith(mockReq.body);
            expect(mockRes.status).toHaveBeenCalledWith(201);
            expect(mockRes.json).toHaveBeenCalledWith(mockReservation);
        });

        it('devrait gérer les erreurs lors de la création', async () => {
            const error = new Error('Erreur de création');
            ReservationService.createDemandeReservation.mockRejectedValue(error);

            await ReservationController.createDemandeReservation(mockReq, mockRes);

            expect(logger.error).toHaveBeenCalled();
            expect(mockRes.status).toHaveBeenCalledWith(500);
            expect(mockRes.json).toHaveBeenCalledWith({
                message: 'Erreur lors de la création de la demande de réservation'
            });
        });
    });

    describe('verifierReservation', () => {
        it('devrait vérifier une réservation avec succès', async () => {
            const mockVerification = { numero: '123', status: 'confirmed' };
            mockReq.params = { numero: '123' };
            ReservationService.verifierReservation.mockResolvedValue(mockVerification);

            await ReservationController.verifierReservation(mockReq, mockRes);

            expect(ReservationService.verifierReservation).toHaveBeenCalledWith('123');
            expect(mockRes.json).toHaveBeenCalledWith(mockVerification);
        });
    });

    describe('getDisponibilite', () => {
        it('devrait récupérer les disponibilités avec succès', async () => {
            const mockDisponibilites = [{ date: '2024-03-01', disponible: true }];
            mockReq.query = {
                dateDebut: '2024-03-01',
                dateFin: '2024-03-05',
                nombrePersonnes: 2
            };
            ReservationService.getDisponibilite.mockResolvedValue(mockDisponibilites);

            await ReservationController.getDisponibilite(mockReq, mockRes);

            expect(ReservationService.getDisponibilite).toHaveBeenCalledWith(mockReq.query);
            expect(mockRes.json).toHaveBeenCalledWith(mockDisponibilites);
        });
    });

    // Routes réception
    describe('confirmerReservation', () => {
        it('devrait confirmer une réservation avec succès', async () => {
            const mockConfirmation = { numero: '123', status: 'confirmed' };
            mockReq.params = { numero: '123' };
            mockReq.body = { montant: 100, devise: 'EUR' };
            ReservationService.confirmerReservation.mockResolvedValue(mockConfirmation);

            await ReservationController.confirmerReservation(mockReq, mockRes);

            expect(ReservationService.confirmerReservation).toHaveBeenCalledWith('123', mockReq.body);
            expect(mockRes.json).toHaveBeenCalledWith(mockConfirmation);
        });
    });

    describe('enregistrerPaiement', () => {
        it('devrait enregistrer un paiement avec succès', async () => {
            const mockPaiement = { id: 1, montant: 100 };
            mockReq.params = { numero: '123' };
            mockReq.body = { montant: 100, devise: 'EUR' };
            ReservationService.enregistrerPaiement.mockResolvedValue(mockPaiement);

            await ReservationController.enregistrerPaiement(mockReq, mockRes);

            expect(ReservationService.enregistrerPaiement).toHaveBeenCalledWith('123', mockReq.body);
            expect(mockRes.json).toHaveBeenCalledWith(mockPaiement);
        });
    });

    describe('genererTicket', () => {
        it('devrait générer un ticket avec succès', async () => {
            const mockTicket = { id: 1, numero: 'T123' };
            mockReq.params = { numero: '123' };
            ReservationService.genererTicket.mockResolvedValue(mockTicket);

            await ReservationController.genererTicket(mockReq, mockRes);

            expect(ReservationService.genererTicket).toHaveBeenCalledWith('123');
            expect(mockRes.json).toHaveBeenCalledWith(mockTicket);
        });
    });

    describe('getCalendrier', () => {
        it('devrait récupérer le calendrier avec succès', async () => {
            const mockCalendrier = [{ date: '2024-03-01', reservations: [] }];
            mockReq.query = { mois: 3, annee: 2024 };
            ReservationService.getCalendrier.mockResolvedValue(mockCalendrier);

            await ReservationController.getCalendrier(mockReq, mockRes);

            expect(ReservationService.getCalendrier).toHaveBeenCalledWith(mockReq.query);
            expect(mockRes.json).toHaveBeenCalledWith(mockCalendrier);
        });
    });

    // Routes admin
    describe('getStatistiques', () => {
        it('devrait récupérer les statistiques avec succès', async () => {
            const mockStatistiques = { total: 100, confirmees: 80 };
            mockReq.query = { dateDebut: '2024-01-01', dateFin: '2024-01-31' };
            ReservationService.getStatistiques.mockResolvedValue(mockStatistiques);

            await ReservationController.getStatistiques(mockReq, mockRes);

            expect(ReservationService.getStatistiques).toHaveBeenCalledWith(mockReq.query);
            expect(mockRes.json).toHaveBeenCalledWith(mockStatistiques);
        });
    });

    describe('getHistorique', () => {
        it('devrait récupérer l\'historique avec succès', async () => {
            const mockHistorique = [{ id: 1 }, { id: 2 }];
            mockReq.query = { dateDebut: '2024-01-01', dateFin: '2024-01-31' };
            ReservationService.getHistorique.mockResolvedValue(mockHistorique);

            await ReservationController.getHistorique(mockReq, mockRes);

            expect(ReservationService.getHistorique).toHaveBeenCalledWith(mockReq.query);
            expect(mockRes.json).toHaveBeenCalledWith(mockHistorique);
        });
    });
}); 