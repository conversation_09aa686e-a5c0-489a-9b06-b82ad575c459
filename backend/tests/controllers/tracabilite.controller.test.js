const TracabiliteController = require('../../controllers/tracabilite.controller');
const TracabiliteService = require('../../services/tracabilite.service');
const logger = require('../../logger');

// Mock des dépendances
jest.mock('../../services/tracabilite.service');
jest.mock('../../logger');

describe('TracabiliteController', () => {
    let mockReq;
    let mockRes;

    beforeEach(() => {
        mockReq = {
            body: {},
            params: {},
            query: {}
        };
        mockRes = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('getHistoriqueEntite', () => {
        it('devrait récupérer l\'historique d\'une entité avec succès', async () => {
            const mockHistorique = [{ id: 1 }, { id: 2 }];
            mockReq.params = { entiteId: '123' };
            TracabiliteService.getHistoriqueEntite.mockResolvedValue(mockHistorique);

            await TracabiliteController.getHistoriqueEntite(mockReq, mockRes);

            expect(TracabiliteService.getHistoriqueEntite).toHaveBeenCalledWith('123');
            expect(mockRes.json).toHaveBeenCalledWith(mockHistorique);
        });
    });

    describe('getHistoriquePaiements', () => {
        it('devrait récupérer l\'historique des paiements avec succès', async () => {
            const mockHistorique = [{ id: 1 }, { id: 2 }];
            mockReq.params = { reservationId: '123' };
            TracabiliteService.getHistoriquePaiements.mockResolvedValue(mockHistorique);

            await TracabiliteController.getHistoriquePaiements(mockReq, mockRes);

            expect(TracabiliteService.getHistoriquePaiements).toHaveBeenCalledWith('123');
            expect(mockRes.json).toHaveBeenCalledWith(mockHistorique);
        });
    });

    describe('getHistoriqueTickets', () => {
        it('devrait récupérer l\'historique des tickets avec succès', async () => {
            const mockHistorique = [{ id: 1 }, { id: 2 }];
            mockReq.params = { reservationId: '123' };
            TracabiliteService.getHistoriqueTickets.mockResolvedValue(mockHistorique);

            await TracabiliteController.getHistoriqueTickets(mockReq, mockRes);

            expect(TracabiliteService.getHistoriqueTickets).toHaveBeenCalledWith('123');
            expect(mockRes.json).toHaveBeenCalledWith(mockHistorique);
        });
    });

    describe('getHistoriqueActions', () => {
        it('devrait récupérer l\'historique des actions avec succès', async () => {
            const mockHistorique = [{ id: 1 }, { id: 2 }];
            mockReq.params = { entiteId: '123' };
            TracabiliteService.getHistoriqueActions.mockResolvedValue(mockHistorique);

            await TracabiliteController.getHistoriqueActions(mockReq, mockRes);

            expect(TracabiliteService.getHistoriqueActions).toHaveBeenCalledWith('123');
            expect(mockRes.json).toHaveBeenCalledWith(mockHistorique);
        });
    });

    describe('getHistoriqueModifications', () => {
        it('devrait récupérer l\'historique des modifications avec succès', async () => {
            const mockHistorique = [{ id: 1 }, { id: 2 }];
            mockReq.params = { reservationId: '123' };
            TracabiliteService.getHistoriqueModifications.mockResolvedValue(mockHistorique);

            await TracabiliteController.getHistoriqueModifications(mockReq, mockRes);

            expect(TracabiliteService.getHistoriqueModifications).toHaveBeenCalledWith('123');
            expect(mockRes.json).toHaveBeenCalledWith(mockHistorique);
        });
    });

    describe('getHistoriqueUtilisations', () => {
        it('devrait récupérer l\'historique des utilisations avec succès', async () => {
            const mockHistorique = [{ id: 1 }, { id: 2 }];
            mockReq.params = { ticketId: '123' };
            TracabiliteService.getHistoriqueUtilisations.mockResolvedValue(mockHistorique);

            await TracabiliteController.getHistoriqueUtilisations(mockReq, mockRes);

            expect(TracabiliteService.getHistoriqueUtilisations).toHaveBeenCalledWith('123');
            expect(mockRes.json).toHaveBeenCalledWith(mockHistorique);
        });
    });

    describe('getAuditLog', () => {
        it('devrait récupérer les logs d\'audit avec succès', async () => {
            const mockLogs = [{ id: 1 }, { id: 2 }];
            mockReq.query = {
                dateDebut: '2024-01-01',
                dateFin: '2024-01-31',
                type: 'paiement'
            };
            TracabiliteService.getAuditLog.mockResolvedValue(mockLogs);

            await TracabiliteController.getAuditLog(mockReq, mockRes);

            expect(TracabiliteService.getAuditLog).toHaveBeenCalledWith('2024-01-01', '2024-01-31', 'paiement');
            expect(mockRes.json).toHaveBeenCalledWith(mockLogs);
        });
    });

    describe('getStatistiquesActions', () => {
        it('devrait récupérer les statistiques d\'actions avec succès', async () => {
            const mockStatistiques = { total: 100, types: { paiement: 50, reservation: 50 } };
            mockReq.query = { dateDebut: '2024-01-01', dateFin: '2024-01-31' };
            TracabiliteService.getStatistiquesActions.mockResolvedValue(mockStatistiques);

            await TracabiliteController.getStatistiquesActions(mockReq, mockRes);

            expect(TracabiliteService.getStatistiquesActions).toHaveBeenCalledWith('2024-01-01', '2024-01-31');
            expect(mockRes.json).toHaveBeenCalledWith(mockStatistiques);
        });
    });

    describe('genererRapports', () => {
        it('devrait générer des rapports avec succès', async () => {
            const mockRapport = { id: 1, type: 'journalier', contenu: {} };
            mockReq.query = {
                type: 'journalier',
                dateDebut: '2024-01-01',
                dateFin: '2024-01-31'
            };
            TracabiliteService.genererRapports.mockResolvedValue(mockRapport);

            await TracabiliteController.genererRapports(mockReq, mockRes);

            expect(TracabiliteService.genererRapports).toHaveBeenCalledWith('journalier', '2024-01-01', '2024-01-31');
            expect(mockRes.json).toHaveBeenCalledWith(mockRapport);
        });
    });
}); 