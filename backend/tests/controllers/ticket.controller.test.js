const TicketController = require('../../controllers/ticket.controller');
const TicketService = require('../../services/ticket.service');
const logger = require('../../logger');

// Mock des dépendances
jest.mock('../../services/ticket.service');
jest.mock('../../logger');

describe('TicketController', () => {
    let mockReq;
    let mockRes;

    beforeEach(() => {
        mockReq = {
            body: {},
            params: {},
            query: {}
        };
        mockRes = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn(),
            setHeader: jest.fn(),
            send: jest.fn()
        };
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('getTicketsReservation', () => {
        it('devrait récupérer les tickets d\'une réservation avec succès', async () => {
            const mockTickets = [{ id: 1 }, { id: 2 }];
            mockReq.params = { reservationId: '123' };
            TicketService.getTicketsReservation.mockResolvedValue(mockTickets);

            await TicketController.getTicketsReservation(mockReq, mockRes);

            expect(TicketService.getTicketsReservation).toHaveBeenCalledWith('123');
            expect(mockRes.json).toHaveBeenCalledWith(mockTickets);
        });
    });

    describe('getQRCode', () => {
        it('devrait générer un QR code avec succès', async () => {
            const mockQRCode = 'data:image/png;base64,ABC123';
            mockReq.params = { ticketId: '123' };
            TicketService.genererQRCode.mockResolvedValue(mockQRCode);

            await TicketController.getQRCode(mockReq, mockRes);

            expect(TicketService.genererQRCode).toHaveBeenCalledWith('123');
            expect(mockRes.json).toHaveBeenCalledWith({ qrCode: mockQRCode });
        });
    });

    describe('getPDFTicket', () => {
        it('devrait générer un PDF de ticket avec succès', async () => {
            const mockPDFBuffer = Buffer.from('PDF content');
            mockReq.params = { ticketId: '123' };
            TicketService.genererPDFTicket.mockResolvedValue(mockPDFBuffer);

            await TicketController.getPDFTicket(mockReq, mockRes);

            expect(TicketService.genererPDFTicket).toHaveBeenCalledWith('123');
            expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Type', 'application/pdf');
            expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Disposition', 'attachment; filename=ticket-123.pdf');
            expect(mockRes.send).toHaveBeenCalledWith(mockPDFBuffer);
        });
    });

    describe('genererTickets', () => {
        it('devrait générer des tickets avec succès', async () => {
            const mockTickets = [{ id: 1 }, { id: 2 }];
            mockReq.params = { reservationId: '123' };
            TicketService.genererTickets.mockResolvedValue(mockTickets);

            await TicketController.genererTickets(mockReq, mockRes);

            expect(TicketService.genererTickets).toHaveBeenCalledWith('123');
            expect(mockRes.status).toHaveBeenCalledWith(201);
            expect(mockRes.json).toHaveBeenCalledWith(mockTickets);
        });
    });

    describe('validerTicket', () => {
        it('devrait valider un ticket avec succès', async () => {
            const mockValidation = { id: '123', status: 'validated' };
            mockReq.params = { ticketId: '123' };
            TicketService.validerTicket.mockResolvedValue(mockValidation);

            await TicketController.validerTicket(mockReq, mockRes);

            expect(TicketService.validerTicket).toHaveBeenCalledWith('123');
            expect(mockRes.json).toHaveBeenCalledWith(mockValidation);
        });
    });

    describe('imprimerTicket', () => {
        it('devrait imprimer un ticket avec succès', async () => {
            const mockPDFBuffer = Buffer.from('PDF content');
            mockReq.params = { ticketId: '123' };
            TicketService.imprimerTicket.mockResolvedValue(mockPDFBuffer);

            await TicketController.imprimerTicket(mockReq, mockRes);

            expect(TicketService.imprimerTicket).toHaveBeenCalledWith('123');
            expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Type', 'application/pdf');
            expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Disposition', 'attachment; filename=ticket-123.pdf');
            expect(mockRes.send).toHaveBeenCalledWith(mockPDFBuffer);
        });
    });

    describe('getStatutTicket', () => {
        it('devrait récupérer le statut d\'un ticket avec succès', async () => {
            const mockStatut = { id: '123', status: 'active' };
            mockReq.params = { ticketId: '123' };
            TicketService.getStatutTicket.mockResolvedValue(mockStatut);

            await TicketController.getStatutTicket(mockReq, mockRes);

            expect(TicketService.getStatutTicket).toHaveBeenCalledWith('123');
            expect(mockRes.json).toHaveBeenCalledWith(mockStatut);
        });
    });

    describe('getStatistiquesTickets', () => {
        it('devrait récupérer les statistiques des tickets avec succès', async () => {
            const mockStatistiques = { total: 100, valides: 80, utilises: 60 };
            mockReq.query = { dateDebut: '2024-01-01', dateFin: '2024-01-31' };
            TicketService.getStatistiquesTickets.mockResolvedValue(mockStatistiques);

            await TicketController.getStatistiquesTickets(mockReq, mockRes);

            expect(TicketService.getStatistiquesTickets).toHaveBeenCalledWith('2024-01-01', '2024-01-31');
            expect(mockRes.json).toHaveBeenCalledWith(mockStatistiques);
        });
    });

    describe('genererRapportsTickets', () => {
        it('devrait générer des rapports de tickets avec succès', async () => {
            const mockRapport = { id: 1, type: 'journalier', contenu: {} };
            mockReq.query = { dateDebut: '2024-01-01', dateFin: '2024-01-31' };
            TicketService.genererRapportsTickets.mockResolvedValue(mockRapport);

            await TicketController.genererRapportsTickets(mockReq, mockRes);

            expect(TicketService.genererRapportsTickets).toHaveBeenCalledWith('2024-01-01', '2024-01-31');
            expect(mockRes.json).toHaveBeenCalledWith(mockRapport);
        });
    });

    describe('configurerTickets', () => {
        it('devrait configurer les tickets avec succès', async () => {
            const mockConfig = { id: 1, format: 'A4', logo: true };
            mockReq.body = mockConfig;
            TicketService.configurerTickets.mockResolvedValue(mockConfig);

            await TicketController.configurerTickets(mockReq, mockRes);

            expect(TicketService.configurerTickets).toHaveBeenCalledWith(mockConfig);
            expect(mockRes.json).toHaveBeenCalledWith(mockConfig);
        });
    });
}); 