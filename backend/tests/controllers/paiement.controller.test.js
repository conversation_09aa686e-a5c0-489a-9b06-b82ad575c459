const PaiementController = require('../../controllers/paiement.controller');
const PaiementService = require('../../services/paiement.service');
const logger = require('../../logger');

// Mock des dépendances
jest.mock('../../services/paiement.service');
jest.mock('../../logger');

describe('PaiementController', () => {
    let mockReq;
    let mockRes;

    beforeEach(() => {
        mockReq = {
            body: {},
            params: {},
            query: {}
        };
        mockRes = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('creerIntentionPaiement', () => {
        it('devrait créer une intention de paiement avec succès', async () => {
            const mockIntention = { id: 1, montant: 100, devise: 'EUR' };
            mockReq.body = { montant: 100, devise: 'EUR', description: 'Test' };
            PaiementService.creerIntentionPaiement.mockResolvedValue(mockIntention);

            await PaiementController.creerIntentionPaiement(mockReq, mockRes);

            expect(PaiementService.creerIntentionPaiement).toHaveBeenCalledWith(100, 'EUR', 'Test');
            expect(mockRes.status).toHaveBeenCalledWith(201);
            expect(mockRes.json).toHaveBeenCalledWith(mockIntention);
        });

        it('devrait gérer les erreurs lors de la création', async () => {
            const error = new Error('Erreur de paiement');
            PaiementService.creerIntentionPaiement.mockRejectedValue(error);

            await PaiementController.creerIntentionPaiement(mockReq, mockRes);

            expect(logger.error).toHaveBeenCalled();
            expect(mockRes.status).toHaveBeenCalledWith(500);
            expect(mockRes.json).toHaveBeenCalledWith({
                message: 'Erreur lors de la création de l\'intention de paiement'
            });
        });
    });

    describe('verifierStatutPaiement', () => {
        it('devrait vérifier le statut d\'un paiement avec succès', async () => {
            const mockStatut = { status: 'completed' };
            mockReq.params = { paiementId: '123' };
            PaiementService.verifierStatutPaiement.mockResolvedValue(mockStatut);

            await PaiementController.verifierStatutPaiement(mockReq, mockRes);

            expect(PaiementService.verifierStatutPaiement).toHaveBeenCalledWith('123');
            expect(mockRes.json).toHaveBeenCalledWith(mockStatut);
        });
    });

    describe('confirmerPaiement', () => {
        it('devrait confirmer un paiement avec succès', async () => {
            const mockPaiement = { id: '123', status: 'confirmed' };
            mockReq.params = { paiementId: '123' };
            PaiementService.confirmerPaiement.mockResolvedValue(mockPaiement);

            await PaiementController.confirmerPaiement(mockReq, mockRes);

            expect(PaiementService.confirmerPaiement).toHaveBeenCalledWith('123');
            expect(mockRes.json).toHaveBeenCalledWith(mockPaiement);
        });
    });

    describe('rembourserPaiement', () => {
        it('devrait effectuer un remboursement avec succès', async () => {
            const mockRemboursement = { id: '123', montant: 50 };
            mockReq.params = { paiementId: '123' };
            mockReq.body = { montant: 50, raison: 'Annulation' };
            PaiementService.rembourserPaiement.mockResolvedValue(mockRemboursement);

            await PaiementController.rembourserPaiement(mockReq, mockRes);

            expect(PaiementService.rembourserPaiement).toHaveBeenCalledWith('123', 50, 'Annulation');
            expect(mockRes.json).toHaveBeenCalledWith(mockRemboursement);
        });
    });

    describe('getHistoriquePaiements', () => {
        it('devrait récupérer l\'historique des paiements avec succès', async () => {
            const mockHistorique = [{ id: 1 }, { id: 2 }];
            mockReq.params = { reservationId: '123' };
            PaiementService.getHistoriquePaiements.mockResolvedValue(mockHistorique);

            await PaiementController.getHistoriquePaiements(mockReq, mockRes);

            expect(PaiementService.getHistoriquePaiements).toHaveBeenCalledWith('123');
            expect(mockRes.json).toHaveBeenCalledWith(mockHistorique);
        });
    });

    describe('getStatistiquesPaiements', () => {
        it('devrait récupérer les statistiques de paiement avec succès', async () => {
            const mockStatistiques = { total: 1000, count: 10 };
            mockReq.query = { dateDebut: '2024-01-01', dateFin: '2024-01-31' };
            PaiementService.getStatistiquesPaiements.mockResolvedValue(mockStatistiques);

            await PaiementController.getStatistiquesPaiements(mockReq, mockRes);

            expect(PaiementService.getStatistiquesPaiements).toHaveBeenCalledWith('2024-01-01', '2024-01-31');
            expect(mockRes.json).toHaveBeenCalledWith(mockStatistiques);
        });
    });

    describe('getHistoriqueRemboursements', () => {
        it('devrait récupérer l\'historique des remboursements avec succès', async () => {
            const mockRemboursements = [{ id: 1 }, { id: 2 }];
            mockReq.query = { dateDebut: '2024-01-01', dateFin: '2024-01-31' };
            PaiementService.getHistoriqueRemboursements.mockResolvedValue(mockRemboursements);

            await PaiementController.getHistoriqueRemboursements(mockReq, mockRes);

            expect(PaiementService.getHistoriqueRemboursements).toHaveBeenCalledWith('2024-01-01', '2024-01-31');
            expect(mockRes.json).toHaveBeenCalledWith(mockRemboursements);
        });
    });
}); 