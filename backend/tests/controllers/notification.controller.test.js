const NotificationController = require('../../controllers/notification.controller');
const NotificationService = require('../../services/notification.service');
const logger = require('../../logger');

// Mock des dépendances
jest.mock('../../services/notification.service');
jest.mock('../../logger');

describe('NotificationController', () => {
    let mockReq;
    let mockRes;

    beforeEach(() => {
        mockReq = {
            body: {},
            params: {},
            query: {},
            user: { id: '123' }
        };
        mockRes = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('updatePreferencesNotification', () => {
        it('devrait mettre à jour les préférences de notification avec succès', async () => {
            const mockPreferences = { email: true, sms: false, push: true };
            mockReq.body = mockPreferences;
            NotificationService.updatePreferencesNotification.mockResolvedValue(mockPreferences);

            await NotificationController.updatePreferencesNotification(mockReq, mockRes);

            expect(NotificationService.updatePreferencesNotification).toHaveBeenCalledWith('123', mockPreferences);
            expect(mockRes.json).toHaveBeenCalledWith(mockPreferences);
        });
    });

    describe('getHistoriqueNotifications', () => {
        it('devrait récupérer l\'historique des notifications avec succès', async () => {
            const mockHistorique = [{ id: 1 }, { id: 2 }];
            NotificationService.getHistoriqueNotifications.mockResolvedValue(mockHistorique);

            await NotificationController.getHistoriqueNotifications(mockReq, mockRes);

            expect(NotificationService.getHistoriqueNotifications).toHaveBeenCalledWith('123');
            expect(mockRes.json).toHaveBeenCalledWith(mockHistorique);
        });
    });

    describe('envoyerConfirmation', () => {
        it('devrait envoyer une confirmation avec succès', async () => {
            const mockConfirmation = { id: 1, status: 'sent' };
            mockReq.params = { reservationId: '123' };
            NotificationService.envoyerConfirmationReservation.mockResolvedValue(mockConfirmation);

            await NotificationController.envoyerConfirmation(mockReq, mockRes);

            expect(NotificationService.envoyerConfirmationReservation).toHaveBeenCalledWith('123');
            expect(mockRes.json).toHaveBeenCalledWith(mockConfirmation);
        });
    });

    describe('envoyerRappel', () => {
        it('devrait envoyer un rappel avec succès', async () => {
            const mockRappel = { id: 1, status: 'sent' };
            mockReq.params = { reservationId: '123' };
            NotificationService.envoyerRappelReservation.mockResolvedValue(mockRappel);

            await NotificationController.envoyerRappel(mockReq, mockRes);

            expect(NotificationService.envoyerRappelReservation).toHaveBeenCalledWith('123');
            expect(mockRes.json).toHaveBeenCalledWith(mockRappel);
        });
    });

    describe('envoyerEmail', () => {
        it('devrait envoyer un email avec succès', async () => {
            const mockEmail = { id: 1, status: 'sent' };
            mockReq.body = {
                destinataire: '<EMAIL>',
                sujet: 'Test',
                contenu: 'Contenu test'
            };
            NotificationService.envoyerEmail.mockResolvedValue(mockEmail);

            await NotificationController.envoyerEmail(mockReq, mockRes);

            expect(NotificationService.envoyerEmail).toHaveBeenCalledWith(
                '<EMAIL>',
                'Test',
                'Contenu test'
            );
            expect(mockRes.json).toHaveBeenCalledWith(mockEmail);
        });
    });

    describe('envoyerSMS', () => {
        it('devrait envoyer un SMS avec succès', async () => {
            const mockSMS = { id: 1, status: 'sent' };
            mockReq.body = {
                numero: '+33612345678',
                message: 'Test SMS'
            };
            NotificationService.envoyerSMS.mockResolvedValue(mockSMS);

            await NotificationController.envoyerSMS(mockReq, mockRes);

            expect(NotificationService.envoyerSMS).toHaveBeenCalledWith('+33612345678', 'Test SMS');
            expect(mockRes.json).toHaveBeenCalledWith(mockSMS);
        });
    });

    describe('getStatistiquesNotifications', () => {
        it('devrait récupérer les statistiques de notification avec succès', async () => {
            const mockStatistiques = { total: 100, emails: 60, sms: 40 };
            mockReq.query = { dateDebut: '2024-01-01', dateFin: '2024-01-31' };
            NotificationService.getStatistiquesNotifications.mockResolvedValue(mockStatistiques);

            await NotificationController.getStatistiquesNotifications(mockReq, mockRes);

            expect(NotificationService.getStatistiquesNotifications).toHaveBeenCalledWith('2024-01-01', '2024-01-31');
            expect(mockRes.json).toHaveBeenCalledWith(mockStatistiques);
        });
    });

    describe('getTemplates', () => {
        it('devrait récupérer les templates avec succès', async () => {
            const mockTemplates = [{ id: 1 }, { id: 2 }];
            NotificationService.getTemplates.mockResolvedValue(mockTemplates);

            await NotificationController.getTemplates(mockReq, mockRes);

            expect(NotificationService.getTemplates).toHaveBeenCalled();
            expect(mockRes.json).toHaveBeenCalledWith(mockTemplates);
        });
    });

    describe('creerTemplate', () => {
        it('devrait créer un template avec succès', async () => {
            const mockTemplate = { id: 1, type: 'email', sujet: 'Test' };
            mockReq.body = {
                type: 'email',
                sujet: 'Test',
                contenu: 'Contenu test'
            };
            NotificationService.creerTemplate.mockResolvedValue(mockTemplate);

            await NotificationController.creerTemplate(mockReq, mockRes);

            expect(NotificationService.creerTemplate).toHaveBeenCalledWith('email', 'Test', 'Contenu test');
            expect(mockRes.status).toHaveBeenCalledWith(201);
            expect(mockRes.json).toHaveBeenCalledWith(mockTemplate);
        });
    });
}); 