const ChambreController = require('../../controllers/chambre.controller');
const ChambreService = require('../../services/chambre.service');
const logger = require('../../logger');

// Mock des dépendances
jest.mock('../../services/chambre.service');
jest.mock('../../logger');

describe('ChambreController', () => {
    let mockReq;
    let mockRes;

    beforeEach(() => {
        mockReq = {
            body: {},
            params: {},
            query: {}
        };
        mockRes = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn()
        };
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    // Routes client
    describe('getDisponibilites', () => {
        it('devrait récupérer les disponibilités avec succès', async () => {
            const mockDisponibilites = {
                disponibilites: [
                    {
                        type: 'standard',
                        total: 10,
                        disponibles: 5,
                        chambres: []
                    }
                ],
                date_debut: '2024-03-01',
                date_fin: '2024-03-05'
            };
            mockReq.query = {
                date_debut: '2024-03-01',
                date_fin: '2024-03-05',
                type_chambre: 'standard'
            };
            ChambreService.getDisponibilites.mockResolvedValue(mockDisponibilites);

            await ChambreController.getDisponibilites(mockReq, mockRes);

            expect(ChambreService.getDisponibilites).toHaveBeenCalledWith(mockReq.query);
            expect(mockRes.json).toHaveBeenCalledWith(mockDisponibilites);
        });

        it('devrait gérer les erreurs lors de la récupération des disponibilités', async () => {
            const error = new Error('Erreur de disponibilités');
            ChambreService.getDisponibilites.mockRejectedValue(error);

            await ChambreController.getDisponibilites(mockReq, mockRes);

            expect(logger.error).toHaveBeenCalled();
            expect(mockRes.status).toHaveBeenCalledWith(500);
            expect(mockRes.json).toHaveBeenCalledWith({
                message: 'Erreur lors de la consultation des disponibilités'
            });
        });
    });

    // Routes réception
    describe('verrouillerChambre', () => {
        it('devrait verrouiller une chambre avec succès', async () => {
            const mockVerrou = { chambre_id: 1, reservation_id: 123 };
            mockReq.params = { chambreId: 1 };
            mockReq.body = { reservation_id: 123, duree_minutes: 30 };
            ChambreService.verrouillerChambre.mockResolvedValue(mockVerrou);

            await ChambreController.verrouillerChambre(mockReq, mockRes);

            expect(ChambreService.verrouillerChambre).toHaveBeenCalledWith(1, mockReq.body);
            expect(mockRes.json).toHaveBeenCalledWith(mockVerrou);
        });
    });

    describe('libererChambre', () => {
        it('devrait libérer une chambre avec succès', async () => {
            const mockLiberation = { message: 'Chambre libérée avec succès', chambre_id: 1 };
            mockReq.params = { chambreId: 1 };
            ChambreService.libererChambre.mockResolvedValue(mockLiberation);

            await ChambreController.libererChambre(mockReq, mockRes);

            expect(ChambreService.libererChambre).toHaveBeenCalledWith(1);
            expect(mockRes.json).toHaveBeenCalledWith(mockLiberation);
        });
    });

    describe('updateStatutChambre', () => {
        it('devrait mettre à jour le statut d\'une chambre avec succès', async () => {
            const mockUpdate = { chambre_id: 1, statut: 'maintenance' };
            mockReq.params = { chambreId: 1 };
            mockReq.body = { 
                statut: 'maintenance',
                raison: 'Nettoyage',
                utilisateur_id: 123
            };
            ChambreService.updateStatutChambre.mockResolvedValue(mockUpdate);

            await ChambreController.updateStatutChambre(mockReq, mockRes);

            expect(ChambreService.updateStatutChambre).toHaveBeenCalledWith(1, mockReq.body);
            expect(mockRes.json).toHaveBeenCalledWith(mockUpdate);
        });
    });

    // Routes admin
    describe('nettoyerVerrousExpires', () => {
        it('devrait nettoyer les verrous expirés avec succès', async () => {
            const mockNettoyage = {
                message: 'Nettoyage des verrous terminé',
                chambres_liberees: 5
            };
            ChambreService.nettoyerVerrousExpires.mockResolvedValue(mockNettoyage);

            await ChambreController.nettoyerVerrousExpires(mockReq, mockRes);

            expect(ChambreService.nettoyerVerrousExpires).toHaveBeenCalled();
            expect(mockRes.json).toHaveBeenCalledWith(mockNettoyage);
        });
    });

    // Tests d'erreurs supplémentaires
    describe('Gestion des erreurs', () => {
        it('devrait gérer les erreurs de verrouillage', async () => {
            const error = new Error('Chambre non disponible');
            mockReq.params = { chambreId: 1 };
            ChambreService.verrouillerChambre.mockRejectedValue(error);

            await ChambreController.verrouillerChambre(mockReq, mockRes);

            expect(logger.error).toHaveBeenCalled();
            expect(mockRes.status).toHaveBeenCalledWith(500);
            expect(mockRes.json).toHaveBeenCalledWith({
                message: 'Erreur lors du verrouillage de la chambre'
            });
        });

        it('devrait gérer les erreurs de libération', async () => {
            const error = new Error('Aucun verrou trouvé');
            mockReq.params = { chambreId: 1 };
            ChambreService.libererChambre.mockRejectedValue(error);

            await ChambreController.libererChambre(mockReq, mockRes);

            expect(logger.error).toHaveBeenCalled();
            expect(mockRes.status).toHaveBeenCalledWith(500);
            expect(mockRes.json).toHaveBeenCalledWith({
                message: 'Erreur lors de la libération de la chambre'
            });
        });

        it('devrait gérer les erreurs de mise à jour de statut', async () => {
            const error = new Error('Chambre non trouvée');
            mockReq.params = { chambreId: 1 };
            ChambreService.updateStatutChambre.mockRejectedValue(error);

            await ChambreController.updateStatutChambre(mockReq, mockRes);

            expect(logger.error).toHaveBeenCalled();
            expect(mockRes.status).toHaveBeenCalledWith(500);
            expect(mockRes.json).toHaveBeenCalledWith({
                message: 'Erreur lors de la mise à jour du statut de la chambre'
            });
        });
    });
}); 