require('dotenv').config();

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'hotel_saas',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'adminpass',
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
  connectionTimeoutMillis: 2000, // How long to wait for a connection
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  application_name: 'hotel_saas_api',
  statement_timeout: 30000, // 30 secondes
  query_timeout: 30000,
};

module.exports = dbConfig;
