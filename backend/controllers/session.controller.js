const SessionCaisseService = require('../services/session.service');
const logger = require('../logger');

class SessionController {
  // Ouvrir une nouvelle session de caisse
  static async ouvrirSession(request, response) {
    try {
      const sessionData = request.body;

      // Validation des champs requis
      if (!sessionData.pos_id || !sessionData.employe_id || !sessionData.complexe_id || 
          !sessionData.service_id || sessionData.fonds_ouverture === undefined) {
        return response.status(400).json({
          success: false,
          message: 'Les champs pos_id, employe_id, complexe_id, service_id et fonds_ouverture sont requis'
        });
      }

      logger.info('Ouverture nouvelle session caisse', { sessionData });

      const result = await SessionCaisseService.ouvrirSession(sessionData);
      
      if (result.success) {
        response.status(201).json({
          ...result,
          message: 'Session de caisse ouverte avec succès'
        });
      } else {
        response.status(result.code || 500).json(result);
      }
    } catch (error) {
      logger.error('Erreur ouverture session caisse', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de l\'ouverture de la session de caisse'
      });
    }
  }

  // Fermer une session de caisse
  static async fermerSession(request, response) {
    try {
      const { id } = request.params;
      const fermetureData = request.body;

      // Validation des champs requis
      if (fermetureData.fonds_fermeture === undefined) {
        return response.status(400).json({
          success: false,
          message: 'Le champ fonds_fermeture est requis'
        });
      }

      logger.info('Fermeture session caisse', { sessionId: id, fermetureData });

      const result = await SessionCaisseService.fermerSession(id, fermetureData);
      
      if (result.success) {
        response.json({
          ...result,
          message: 'Session de caisse fermée avec succès'
        });
      } else {
        response.status(result.code || 500).json(result);
      }
    } catch (error) {
      logger.error('Erreur fermeture session caisse', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la fermeture de la session de caisse'
      });
    }
  }

  // Récupérer les sessions d'un point de vente
  static async getSessionsByPOS(request, response) {
    try {
      const { posId } = request.params;
      
      logger.info('Consultation sessions par POS', { posId });
      
      const result = await SessionCaisseService.getSessionsByPOS(posId);
      
      if (result.success) {
        response.json(result);
      } else {
        response.status(result.code || 500).json(result);
      }
    } catch (error) {
      logger.error('Erreur consultation sessions POS', error);
      response.status(500).json({ 
        success: false,
        message: 'Erreur lors de la consultation des sessions du point de vente'
      });
    }
  }

  // Récupérer la session active d'un point de vente
  static async getActiveSession(request, response) {
    try {
      const { posId } = request.params;
      
      logger.info('Consultation session active', { posId });
      
      const result = await SessionCaisseService.getActiveSession(posId);
      
      if (result.success) {
        response.json(result);
      } else {
        response.status(result.code || 500).json(result);
      }
    } catch (error) {
      logger.error('Erreur consultation session active', error);
      response.status(500).json({ 
        success: false,
        message: 'Erreur lors de la consultation de la session active'
      });
    }
  }

  // Récupérer une session par ID
  static async getSessionById(request, response) {
    try {
      const { id } = request.params;
      
      logger.info('Consultation session', { sessionId: id });
      
      const result = await SessionCaisseService.getSessionById(id);
      
      if (result.success) {
        response.json(result);
      } else {
        response.status(result.code || 500).json(result);
      }
    } catch (error) {
      logger.error('Erreur consultation session', error);
      response.status(500).json({ 
        success: false,
        message: 'Erreur lors de la consultation de la session'
      });
    }
  }

  // Récupérer les sessions d'un complexe
  static async getSessionsByComplexe(request, response) {
    try {
      const { complexeId } = request.query;
      const params = request.query;
      
      if (!complexeId) {
        return response.status(400).json({
          success: false,
          message: 'L\'ID du complexe est requis'
        });
      }

      logger.info('Consultation sessions par complexe', { complexeId, params });
      
      const result = await SessionCaisseService.getSessionsByComplexe(complexeId, params);
      
      if (result.success) {
        response.json(result);
      } else {
        response.status(result.code || 500).json(result);
      }
    } catch (error) {
      logger.error('Erreur consultation sessions complexe', error);
      response.status(500).json({ 
        success: false,
        message: 'Erreur lors de la consultation des sessions du complexe'
      });
    }
  }
}

module.exports = SessionController;
