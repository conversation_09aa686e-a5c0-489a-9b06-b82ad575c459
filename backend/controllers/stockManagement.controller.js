const StockManagementService = require('../services/stockManagement.service');
const logger = require('../logger');

/**
 * Contrôleur pour la gestion avancée des stocks
 */
class StockManagementController {

  /**
   * POST /api/stock-management/mouvement
   * Enregistrer un mouvement de stock manuel
   */
  static async enregistrerMouvement(request, response) {
    try {
      const { 
        ingredient_id, 
        complexe_id, 
        type_mouvement, 
        quantite, 
        prix_unitaire,
        notes 
      } = request.body;
      const employe_id = request.user?.employe_id;

      // Validation des données
      if (!ingredient_id || !complexe_id || !type_mouvement || !quantite) {
        return response.status(400).json({
          success: false,
          message: 'Les champs ingredient_id, complexe_id, type_mouvement et quantite sont requis'
        });
      }

      const typesValides = ['ENTREE', 'SORTIE', 'AJUSTEMENT', 'PERTE'];
      if (!typesValides.includes(type_mouvement)) {
        return response.status(400).json({
          success: false,
          message: `Type de mouvement invalide. Types valides: ${typesValides.join(', ')}`
        });
      }

      logger.info('Enregistrement mouvement de stock', {
        ingredient_id,
        complexe_id,
        type_mouvement,
        quantite,
        employe_id
      });

      const result = await StockManagementService.enregistrerMouvement(
        ingredient_id,
        complexe_id,
        type_mouvement,
        parseFloat(quantite),
        {
          prixUnitaire: prix_unitaire ? parseFloat(prix_unitaire) : null,
          employeId: employe_id,
          notes,
          referenceType: 'MANUEL'
        }
      );

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Erreur lors de l\'enregistrement du mouvement:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de l\'enregistrement du mouvement'
      });
    }
  }

  /**
   * POST /api/stock-management/initialiser
   * Initialiser le stock d'un ingrédient
   */
  static async initialiserStock(request, response) {
    try {
      const { ingredient_id, complexe_id, stock_initial } = request.body;
      const employe_id = request.user?.employe_id;

      if (!ingredient_id || !complexe_id || stock_initial === undefined) {
        return response.status(400).json({
          success: false,
          message: 'Les champs ingredient_id, complexe_id et stock_initial sont requis'
        });
      }

      logger.info('Initialisation stock ingrédient', {
        ingredient_id,
        complexe_id,
        stock_initial,
        employe_id
      });

      const result = await StockManagementService.initialiserStock(
        ingredient_id,
        complexe_id,
        parseFloat(stock_initial),
        employe_id
      );

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Erreur lors de l\'initialisation du stock:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de l\'initialisation du stock'
      });
    }
  }

  /**
   * POST /api/stock-management/consommer-produit
   * Consommer les ingrédients d'un produit
   */
  static async consommerProduit(request, response) {
    try {
      const { produit_id, commande_id, quantite_portions = 1 } = request.body;

      if (!produit_id || !commande_id) {
        return response.status(400).json({
          success: false,
          message: 'Les champs produit_id et commande_id sont requis'
        });
      }

      logger.info('Consommation produit', {
        produit_id,
        commande_id,
        quantite_portions
      });

      const result = await StockManagementService.consommerProduit(
        produit_id,
        commande_id,
        parseInt(quantite_portions)
      );

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Erreur lors de la consommation de produit:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la consommation de produit'
      });
    }
  }

  /**
   * GET /api/stock-management/alertes/:complexeId
   * Récupérer les alertes de stock
   */
  static async getAlertes(request, response) {
    try {
      const { complexeId } = request.params;
      const { niveau_urgence } = request.query;

      logger.info('Récupération alertes stock', { complexeId, niveau_urgence });

      const result = await StockManagementService.getAlertes(
        parseInt(complexeId),
        niveau_urgence
      );

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération des alertes:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des alertes'
      });
    }
  }

  /**
   * GET /api/stock-management/rapport-consommation/:complexeId
   * Rapport de consommation par période
   */
  static async getRapportConsommation(request, response) {
    try {
      const { complexeId } = request.params;
      const { date_debut, date_fin } = request.query;

      if (!date_debut || !date_fin) {
        return response.status(400).json({
          success: false,
          message: 'Les paramètres date_debut et date_fin sont requis'
        });
      }

      logger.info('Génération rapport consommation', {
        complexeId,
        date_debut,
        date_fin
      });

      const result = await StockManagementService.getRapportConsommation(
        parseInt(complexeId),
        date_debut,
        date_fin
      );

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Erreur lors de la génération du rapport:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la génération du rapport'
      });
    }
  }

  /**
   * GET /api/stock-management/stock-detaille/:complexeId
   * Vue détaillée du stock avec statuts et alertes
   */
  static async getStockDetaille(request, response) {
    try {
      const { complexeId } = request.params;
      const { categorie, statut_stock } = request.query;

      logger.info('Récupération stock détaillé', { complexeId, categorie, statut_stock });

      // Utiliser la vue détaillée créée dans la migration
      let query = 'SELECT * FROM "VueStockIngredientsDetaille" WHERE complexe_id = $1';
      const params = [parseInt(complexeId)];

      if (categorie) {
        query += ' AND categorie = $' + (params.length + 1);
        params.push(categorie);
      }

      if (statut_stock) {
        query += ' AND statut_stock = $' + (params.length + 1);
        params.push(statut_stock);
      }

      query += ' ORDER BY statut_stock, nom_ingredient';

      const db = require('../db');
      const result = await db.query(query, params);

      // Calculer les statistiques
      const stats = {
        total_ingredients: result.rows.length,
        valeur_totale: result.rows.reduce((sum, item) => sum + parseFloat(item.valeur_stock_actuel || 0), 0),
        repartition_statuts: {}
      };

      // Compter par statut
      result.rows.forEach(item => {
        const statut = item.statut_stock;
        stats.repartition_statuts[statut] = (stats.repartition_statuts[statut] || 0) + 1;
      });

      response.json({
        success: true,
        data: {
          ingredients: result.rows,
          statistiques: stats
        },
        message: 'Stock détaillé récupéré avec succès'
      });

    } catch (error) {
      logger.error('Erreur lors de la récupération du stock détaillé:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération du stock détaillé'
      });
    }
  }
}

module.exports = StockManagementController;
