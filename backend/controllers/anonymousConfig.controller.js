const Controller = require('./Controller');
const AnonymousConfigService = require('../services/anonymousConfig.service');
const logger = require('../logger');

/**
 * Contrôleur pour gérer la configuration des réservations anonymes
 * Routes protégées pour les administrateurs
 */
class AnonymousConfigController extends Controller {

  /**
   * Récupérer la configuration d'un complexe
   * GET /api/admin/anonymous-config/:complexeId
   */
  static async getConfigurationComplexe(req, res) {
    try {
      const { complexeId } = req.params;

      if (!complexeId || isNaN(complexeId)) {
        return res.status(400).json({
          success: false,
          message: 'ID de complexe invalide'
        });
      }

      logger.info('Récupération configuration réservations anonymes', {
        complexe_id: complexeId,
        utilisateur_id: req.user?.id
      });

      const result = await AnonymousConfigService.getConfigurationComplexe(parseInt(complexeId));
      
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      res.json(result);

    } catch (error) {
      logger.error('Erreur contrôleur récupération configuration:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de la configuration'
      });
    }
  }

  /**
   * Mettre à jour la configuration d'un complexe
   * PUT /api/admin/anonymous-config/:complexeId
   */
  static async updateConfigurationComplexe(req, res) {
    try {
      const { complexeId } = req.params;

      if (!complexeId || isNaN(complexeId)) {
        return res.status(400).json({
          success: false,
          message: 'ID de complexe invalide'
        });
      }

      logger.info('Mise à jour configuration réservations anonymes', {
        complexe_id: complexeId,
        utilisateur_id: req.user?.id,
        modifications: Object.keys(req.body)
      });

      const result = await AnonymousConfigService.updateConfigurationComplexe(parseInt(complexeId), req.body);
      
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      res.json(result);

    } catch (error) {
      logger.error('Erreur contrôleur mise à jour configuration:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour de la configuration'
      });
    }
  }

  /**
   * Activer/désactiver les réservations anonymes pour un complexe
   * PATCH /api/admin/anonymous-config/:complexeId/toggle
   */
  static async toggleReservationsAnonymes(req, res) {
    try {
      const { complexeId } = req.params;
      const { actif } = req.body;

      if (!complexeId || isNaN(complexeId)) {
        return res.status(400).json({
          success: false,
          message: 'ID de complexe invalide'
        });
      }

      if (typeof actif !== 'boolean') {
        return res.status(400).json({
          success: false,
          message: 'Le paramètre "actif" doit être un booléen'
        });
      }

      logger.info(`${actif ? 'Activation' : 'Désactivation'} réservations anonymes`, {
        complexe_id: complexeId,
        utilisateur_id: req.user?.id
      });

      const result = await AnonymousConfigService.toggleReservationsAnonymes(parseInt(complexeId), actif);
      
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      res.json(result);

    } catch (error) {
      logger.error('Erreur contrôleur toggle réservations anonymes:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la modification du statut'
      });
    }
  }

  /**
   * Récupérer toutes les configurations (pour les admins de chaîne)
   * GET /api/admin/anonymous-config
   */
  static async getAllConfigurations(req, res) {
    try {
      const { chaine_id } = req.query;

      logger.info('Récupération toutes configurations réservations anonymes', {
        chaine_id: chaine_id,
        utilisateur_id: req.user?.id
      });

      const result = await AnonymousConfigService.getAllConfigurations(chaine_id ? parseInt(chaine_id) : null);
      
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      res.json(result);

    } catch (error) {
      logger.error('Erreur contrôleur récupération toutes configurations:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des configurations'
      });
    }
  }

  /**
   * Récupérer les statistiques d'utilisation
   * GET /api/admin/anonymous-config/:complexeId/stats
   */
  static async getUsageStatistics(req, res) {
    try {
      const { complexeId } = req.params;
      const { date_debut, date_fin } = req.query;

      if (!complexeId || isNaN(complexeId)) {
        return res.status(400).json({
          success: false,
          message: 'ID de complexe invalide'
        });
      }

      logger.info('Récupération statistiques usage réservations anonymes', {
        complexe_id: complexeId,
        periode: { date_debut, date_fin },
        utilisateur_id: req.user?.id
      });

      const result = await AnonymousConfigService.getUsageStatistics(
        parseInt(complexeId), 
        { date_debut, date_fin }
      );
      
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      res.json(result);

    } catch (error) {
      logger.error('Erreur contrôleur statistiques usage:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des statistiques'
      });
    }
  }

  /**
   * Nettoyer les configurations obsolètes
   * DELETE /api/admin/anonymous-config/cleanup
   */
  static async cleanupObsoleteConfigurations(req, res) {
    try {
      logger.info('Nettoyage configurations obsolètes', {
        utilisateur_id: req.user?.id
      });

      const result = await AnonymousConfigService.cleanupObsoleteConfigurations();
      
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      res.json(result);

    } catch (error) {
      logger.error('Erreur contrôleur nettoyage configurations:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors du nettoyage des configurations'
      });
    }
  }

  /**
   * Créer une configuration par défaut pour un complexe
   * POST /api/admin/anonymous-config/:complexeId/default
   */
  static async createDefaultConfiguration(req, res) {
    try {
      const { complexeId } = req.params;

      if (!complexeId || isNaN(complexeId)) {
        return res.status(400).json({
          success: false,
          message: 'ID de complexe invalide'
        });
      }

      logger.info('Création configuration par défaut', {
        complexe_id: complexeId,
        utilisateur_id: req.user?.id
      });

      const result = await AnonymousConfigService.createDefaultConfiguration(parseInt(complexeId));
      
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      res.status(201).json(result);

    } catch (error) {
      logger.error('Erreur contrôleur création configuration par défaut:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la création de la configuration par défaut'
      });
    }
  }

  /**
   * Exporter la configuration d'un complexe
   * GET /api/admin/anonymous-config/:complexeId/export
   */
  static async exportConfiguration(req, res) {
    try {
      const { complexeId } = req.params;

      if (!complexeId || isNaN(complexeId)) {
        return res.status(400).json({
          success: false,
          message: 'ID de complexe invalide'
        });
      }

      const result = await AnonymousConfigService.getConfigurationComplexe(parseInt(complexeId));
      
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      // Préparer les données pour l'export
      const exportData = {
        complexe_id: result.data.complexe_id,
        configuration: {
          actif: result.data.actif,
          duree_validite_code_heures: result.data.duree_validite_code_heures,
          max_tentatives_acces: result.data.max_tentatives_acces,
          delai_blocage_minutes: result.data.delai_blocage_minutes,
          autoriser_modification: result.data.autoriser_modification,
          autoriser_annulation: result.data.autoriser_annulation,
          delai_annulation_heures: result.data.delai_annulation_heures,
          pseudonyme_obligatoire: result.data.pseudonyme_obligatoire,
          longueur_code_acces: result.data.longueur_code_acces,
          prefixe_code: result.data.prefixe_code
        },
        export_date: new Date().toISOString(),
        export_by: req.user?.id
      };

      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="config-anonyme-complexe-${complexeId}.json"`);
      res.json(exportData);

    } catch (error) {
      logger.error('Erreur contrôleur export configuration:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'export de la configuration'
      });
    }
  }

  /**
   * Importer une configuration pour un complexe
   * POST /api/admin/anonymous-config/:complexeId/import
   */
  static async importConfiguration(req, res) {
    try {
      const { complexeId } = req.params;
      const { configuration } = req.body;

      if (!complexeId || isNaN(complexeId)) {
        return res.status(400).json({
          success: false,
          message: 'ID de complexe invalide'
        });
      }

      if (!configuration || typeof configuration !== 'object') {
        return res.status(400).json({
          success: false,
          message: 'Configuration invalide'
        });
      }

      logger.info('Import configuration réservations anonymes', {
        complexe_id: complexeId,
        utilisateur_id: req.user?.id
      });

      const result = await AnonymousConfigService.updateConfigurationComplexe(parseInt(complexeId), configuration);
      
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      res.json({
        success: true,
        message: 'Configuration importée avec succès',
        data: result.data
      });

    } catch (error) {
      logger.error('Erreur contrôleur import configuration:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'import de la configuration'
      });
    }
  }
}

module.exports = AnonymousConfigController;
