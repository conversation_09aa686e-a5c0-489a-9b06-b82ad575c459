const Controller = require('./Controller');
const ClientService = require('../services/client.service');
const logger = require('../logger');

class ClientController extends Controller {
  // Création d'un client
  static async createClient(request, response) {
    try {
      const params = this.collectRequestParams(request);
      logger.info('Création client', { params });
      
      const result = await ClientService.createClient(params);
      this.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur création client', error);
      this.sendError(response, error);
    }
  }

  // Récupération des détails d'un client
  static async getClientById(request, response) {
    try {
      const { id } = request.params;
      logger.info('Consultation détails client', { clientId: id });
      
      const result = await ClientService.getClientById(id);
      this.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur consultation détails client', error);
      this.sendError(response, error);
    }
  }

  // Mise à jour d'un client
  static async updateClient(request, response) {
    try {
      const { id } = request.params;
      const params = this.collectRequestParams(request);
      logger.info('Mise à jour client', { clientId: id, params });
      
      const result = await ClientService.updateClient(id, params);
      this.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur mise à jour client', error);
      this.sendError(response, error);
    }
  }

  // Récupération de l'historique des réservations d'un client
  static async getClientReservations(request, response) {
    try {
      const { id } = request.params;
      const params = this.collectRequestParams(request);
      logger.info('Consultation historique réservations client', { clientId: id, params });
      
      const result = await ClientService.getClientReservations(id, params);
      this.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur consultation historique réservations client', error);
      this.sendError(response, error);
    }
  }
}

module.exports = ClientController; 