const Controller = require('./Controller');
const TicketService = require('../services/ticket.service');
const logger = require('../logger');

class TicketController extends Controller {
    static async getTicketsReservation(req, res) {
        try {
            const { reservationId } = req.params;
            const tickets = await TicketService.getTicketsReservation(reservationId);
            res.json(tickets);
        } catch (error) {
            logger.error('Erreur lors de la récupération des tickets de la réservation:', error);
            res.status(500).json({ message: 'Erreur lors de la récupération des tickets de la réservation' });
        }
    }

    static async getQRCode(req, res) {
        try {
            const { ticketId } = req.params;
            const qrCode = await TicketService.genererQRCode(ticketId);
            res.json({ qrCode });
        } catch (error) {
            logger.error('Erreur lors de la génération du QR code:', error);
            res.status(500).json({ message: 'Erreur lors de la génération du QR code' });
        }
    }

    static async getPDFTicket(req, res) {
        try {
            const { ticketId } = req.params;
            const pdfBuffer = await TicketService.genererPDFTicket(ticketId);
            res.setHeader('Content-Type', 'application/pdf');
            res.setHeader('Content-Disposition', `attachment; filename=ticket-${ticketId}.pdf`);
            res.send(pdfBuffer);
        } catch (error) {
            logger.error('Erreur lors de la génération du PDF du ticket:', error);
            res.status(500).json({ message: 'Erreur lors de la génération du PDF du ticket' });
        }
    }

    static async genererTickets(req, res) {
        try {
            const { reservationId } = req.params;
            const tickets = await TicketService.genererTickets(reservationId);
            res.status(201).json(tickets);
        } catch (error) {
            logger.error('Erreur lors de la génération des tickets:', error);
            res.status(500).json({ message: 'Erreur lors de la génération des tickets' });
        }
    }

    static async validerTicket(req, res) {
        try {
            const { ticketId } = req.params;
            const validation = await TicketService.validerTicket(ticketId);
            res.json(validation);
        } catch (error) {
            logger.error('Erreur lors de la validation du ticket:', error);
            res.status(500).json({ message: 'Erreur lors de la validation du ticket' });
        }
    }

    static async imprimerTicket(req, res) {
        try {
            const { ticketId } = req.params;
            const pdfBuffer = await TicketService.imprimerTicket(ticketId);
            res.setHeader('Content-Type', 'application/pdf');
            res.setHeader('Content-Disposition', `attachment; filename=ticket-${ticketId}.pdf`);
            res.send(pdfBuffer);
        } catch (error) {
            logger.error('Erreur lors de l\'impression du ticket:', error);
            res.status(500).json({ message: 'Erreur lors de l\'impression du ticket' });
        }
    }

    static async getStatutTicket(req, res) {
        try {
            const { ticketId } = req.params;
            const statut = await TicketService.getStatutTicket(ticketId);
            res.json(statut);
        } catch (error) {
            logger.error('Erreur lors de la récupération du statut du ticket:', error);
            res.status(500).json({ message: 'Erreur lors de la récupération du statut du ticket' });
        }
    }

    static async getStatistiquesTickets(req, res) {
        try {
            const { dateDebut, dateFin } = req.query;
            const statistiques = await TicketService.getStatistiquesTickets(dateDebut, dateFin);
            res.json(statistiques);
        } catch (error) {
            logger.error('Erreur lors de la récupération des statistiques des tickets:', error);
            res.status(500).json({ message: 'Erreur lors de la récupération des statistiques des tickets' });
        }
    }

    static async genererRapportsTickets(req, res) {
        try {
            const { dateDebut, dateFin } = req.query;
            const rapport = await TicketService.genererRapportsTickets(dateDebut, dateFin);
            res.json(rapport);
        } catch (error) {
            logger.error('Erreur lors de la génération des rapports de tickets:', error);
            res.status(500).json({ message: 'Erreur lors de la génération des rapports de tickets' });
        }
    }

    static async configurerTickets(req, res) {
        try {
            const configuration = req.body;
            const config = await TicketService.configurerTickets(configuration);
            res.json(config);
        } catch (error) {
            logger.error('Erreur lors de la configuration des tickets:', error);
            res.status(500).json({ message: 'Erreur lors de la configuration des tickets' });
        }
    }

    // Générer un ticket à partir d'une réservation
    static async genererTicketFromReservation(req, res) {
        try {
            const { numero } = req.params;
            const result = await TicketService.genererTicketFromReservation(numero);
            
            if (!result.success) {
                return res.status(result.code || 500).json(result);
            }
            
            res.json(result);
        } catch (error) {
            logger.error('Erreur contrôleur génération ticket:', error);
            res.status(500).json({
                success: false,
                message: 'Erreur lors de la génération du ticket'
            });
        }
    }

    // Vérifier un ticket
    static async verifierTicket(req, res) {
        try {
            const { numero } = req.params;
            const result = await TicketService.verifierTicket(numero);
            
            if (!result.success) {
                return res.status(result.code || 500).json(result);
            }
            
            res.json(result);
        } catch (error) {
            logger.error('Erreur contrôleur vérification ticket:', error);
            res.status(500).json({
                success: false,
                message: 'Erreur lors de la vérification du ticket'
            });
        }
    }

    // Obtenir les détails d'un ticket
    static async getTicketById(req, res) {
        try {
            const { ticketId } = req.params;
            const result = await TicketService.getTicketById(ticketId);
            
            if (!result.success) {
                return res.status(result.code || 500).json(result);
            }
            
            res.json(result);
        } catch (error) {
            logger.error('Erreur contrôleur récupération ticket:', error);
            res.status(500).json({
                success: false,
                message: 'Erreur lors de la récupération du ticket'
            });
        }
    }

    // Mettre à jour le statut d'un ticket
    static async updateTicketStatus(req, res) {
        try {
            const { ticketId } = req.params;
            const { statut } = req.body;

            const result = await TicketService.updateTicketStatus(ticketId, statut);

            if (!result.success) {
                return res.status(result.code || 500).json(result);
            }

            res.json(result);
        } catch (error) {
            logger.error('Erreur contrôleur mise à jour statut ticket:', error);
            res.status(500).json({
                success: false,
                message: 'Erreur lors de la mise à jour du statut du ticket'
            });
        }
    }

    // ==================== MÉTHODES POUR TICKETS DE SERVICE ====================

    // Créer un ticket de service (piscine, bar, restaurant)
    static async createServiceTicket(req, res) {
        try {
            const {
                service_id,
                pos_id,
                session_id,
                nom_client,
                nombre_personnes,
                duree_heures,
                mode_paiement,
                prix_total
            } = req.body;

            // Récupérer l'ID de l'employé depuis le token
            const employe_id = req.user.employe_id || req.user.id;

            const ticketData = {
                service_id,
                pos_id,
                session_id,
                employe_id,
                nom_client,
                nombre_personnes,
                duree_heures,
                mode_paiement,
                prix_total
            };

            const result = await TicketService.creerTicketService(ticketData);

            if (!result.success) {
                return res.status(result.code || 500).json(result);
            }

            res.status(201).json(result);
        } catch (error) {
            logger.error('Erreur contrôleur création ticket service:', error);
            res.status(500).json({
                success: false,
                message: 'Erreur lors de la création du ticket de service'
            });
        }
    }

    // Récupérer les tickets d'un service
    static async getServiceTickets(req, res) {
        try {
            const { serviceId } = req.params;
            const filters = {
                statut: req.query.statut,
                date_debut: req.query.date_debut,
                date_fin: req.query.date_fin
            };

            const result = await TicketService.getServiceTickets(serviceId, filters);

            if (!result.success) {
                return res.status(result.code || 500).json(result);
            }

            res.json(result);
        } catch (error) {
            logger.error('Erreur contrôleur récupération tickets service:', error);
            res.status(500).json({
                success: false,
                message: 'Erreur lors de la récupération des tickets de service'
            });
        }
    }

    // Marquer un ticket de service comme utilisé
    static async markServiceTicketUsed(req, res) {
        try {
            const { ticketId } = req.params;

            const result = await TicketService.marquerTicketUtilise(ticketId);

            if (!result.success) {
                return res.status(result.code || 500).json(result);
            }

            res.json(result);
        } catch (error) {
            logger.error('Erreur contrôleur marquage ticket utilisé:', error);
            res.status(500).json({
                success: false,
                message: 'Erreur lors du marquage du ticket comme utilisé'
            });
        }
    }

    // Vérifier un ticket de service
    static async verifyServiceTicket(req, res) {
        try {
            const { numero } = req.params;

            const result = await TicketService.verifierTicketService(numero);

            if (!result.success) {
                return res.status(result.code || 500).json(result);
            }

            res.json(result);
        } catch (error) {
            logger.error('Erreur contrôleur vérification ticket service:', error);
            res.status(500).json({
                success: false,
                message: 'Erreur lors de la vérification du ticket de service'
            });
        }
    }

    // Récupérer les tickets d'une session de caisse
    static async getTicketsBySession(req, res) {
        try {
            const { sessionId } = req.params;

            const result = await TicketService.getTicketsBySession(sessionId);

            if (!result.success) {
                return res.status(result.code || 500).json(result);
            }

            res.json(result);
        } catch (error) {
            logger.error('Erreur contrôleur récupération tickets session:', error);
            res.status(500).json({
                success: false,
                message: 'Erreur lors de la récupération des tickets de la session'
            });
        }
    }
}

module.exports = TicketController;