const InventaireService = require('../services/inventaire.service');
const logger = require('../logger');

class InventaireController {

  /**
   * ==================== GESTION DES INGRÉDIENTS ====================
   */

  /**
   * GET /api/inventaire/ingredients
   * Liste paginée des ingrédients
   */
  static async getIngredients(request, response) {
    try {
      const { complexeId } = request.query;
      const filters = {
        categorie: request.query.categorie,
        actif: request.query.actif !== undefined ? request.query.actif === 'true' : undefined,
        search: request.query.search,
        page: parseInt(request.query.page) || 1,
        limit: parseInt(request.query.limit) || 50
      };

      if (!complexeId) {
        return response.status(400).json({
          success: false,
          message: 'L\'ID du complexe est requis'
        });
      }

      logger.info('Get ingredients', { complexeId, filters });

      const result = await InventaireService.getIngredients(parseInt(complexeId), filters);

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Error getting ingredients:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des ingrédients'
      });
    }
  }

  /**
   * POST /api/inventaire/ingredients
   * Création d'un nouvel ingrédient
   */
  static async createIngredient(request, response) {
    try {
      const ingredientData = request.body;

      // Validation des champs requis
      const requiredFields = ['chaineId', 'complexeId', 'nom', 'uniteMesure', 'categorie'];
      for (const field of requiredFields) {
        if (!ingredientData[field]) {
          return response.status(400).json({
            success: false,
            message: `Le champ ${field} est requis`
          });
        }
      }

      logger.info('Create ingredient', { ingredientData });

      const result = await InventaireService.createIngredient(ingredientData);

      response.status(201).json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Error creating ingredient:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la création de l\'ingrédient'
      });
    }
  }

  /**
   * PUT /api/inventaire/ingredients/:id
   * Mise à jour d'un ingrédient
   */
  static async updateIngredient(request, response) {
    try {
      const { id } = request.params;
      const ingredientData = request.body;

      logger.info('Update ingredient', { ingredientId: id, ingredientData });

      const result = await InventaireService.updateIngredient(parseInt(id), ingredientData);

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error updating ingredient ${request.params.id}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la mise à jour de l\'ingrédient'
      });
    }
  }

  /**
   * DELETE /api/inventaire/ingredients/:id
   * Suppression logique d'un ingrédient
   */
  static async deleteIngredient(request, response) {
    try {
      const { id } = request.params;

      logger.info('Delete ingredient', { ingredientId: id });

      // Suppression logique (désactivation)
      const result = await InventaireService.updateIngredient(parseInt(id), { actif: false });

      response.json({
        success: true,
        message: 'Ingrédient désactivé avec succès'
      });

    } catch (error) {
      logger.error(`Error deleting ingredient ${request.params.id}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la suppression de l\'ingrédient'
      });
    }
  }

  /**
   * GET /api/inventaire/ingredients/search
   * Recherche textuelle avancée d'ingrédients
   */
  static async searchIngredients(request, response) {
    try {
      const { query, complexeId } = request.query;

      if (!query || !complexeId) {
        return response.status(400).json({
          success: false,
          message: 'Les paramètres query et complexeId sont requis'
        });
      }

      logger.info('Search ingredients', { query, complexeId });

      const result = await InventaireService.searchIngredients(query, parseInt(complexeId));

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Error searching ingredients:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la recherche d\'ingrédients'
      });
    }
  }

  /**
   * GET /api/inventaire/ingredients/categories/:complexeId
   * Récupération des catégories d'ingrédients
   */
  static async getIngredientCategories(request, response) {
    try {
      const { complexeId } = request.params;

      logger.info('Get ingredient categories', { complexeId });

      const result = await InventaireService.getIngredientCategories(parseInt(complexeId));

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error getting ingredient categories for complexe ${request.params.complexeId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des catégories'
      });
    }
  }

  /**
   * ==================== GESTION DU STOCK ====================
   */

  /**
   * GET /api/inventaire/stock/:complexeId
   * État du stock par complexe
   */
  static async getStock(request, response) {
    try {
      const { complexeId } = request.params;
      const { serviceId } = request.query;

      logger.info('Get stock', { complexeId, serviceId });

      const result = await InventaireService.getStockIngredients(
        parseInt(complexeId),
        serviceId ? parseInt(serviceId) : null
      );

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error getting stock for complexe ${request.params.complexeId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération du stock'
      });
    }
  }

  /**
   * PUT /api/inventaire/stock/:ingredientId
   * Mise à jour manuelle du stock
   */
  static async updateStock(request, response) {
    try {
      const { ingredientId } = request.params;
      const { quantite, typeOperation, notes, employeId } = request.body;

      if (!quantite || !typeOperation || !employeId) {
        return response.status(400).json({
          success: false,
          message: 'Les champs quantite, typeOperation et employeId sont requis'
        });
      }

      logger.info('Update stock', { ingredientId, quantite, typeOperation });

      // TODO: Implémenter la mise à jour du stock via MouvementsStock
      // Cette fonctionnalité nécessite l'extension du service InventaireService

      response.json({
        success: true,
        message: 'Stock mis à jour avec succès'
      });

    } catch (error) {
      logger.error(`Error updating stock for ingredient ${request.params.ingredientId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la mise à jour du stock'
      });
    }
  }

  /**
   * GET /api/inventaire/alerts/:complexeId
   * Alertes de stock faible
   */
  static async getStockAlerts(request, response) {
    try {
      const { complexeId } = request.params;

      logger.info('Get stock alerts', { complexeId });

      const result = await InventaireService.generateStockAlerts(parseInt(complexeId));

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error getting stock alerts for complexe ${request.params.complexeId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des alertes de stock'
      });
    }
  }

  /**
   * POST /api/inventaire/stock/from-inventory/:inventaireId
   * Mise à jour du stock depuis un inventaire physique
   */
  static async updateStockFromInventaire(request, response) {
    try {
      const { inventaireId } = request.params;

      logger.info('Update stock from inventory', { inventaireId });

      const result = await InventaireService.updateStockFromInventaire(parseInt(inventaireId));

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error updating stock from inventory ${request.params.inventaireId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la mise à jour du stock depuis l\'inventaire'
      });
    }
  }

  /**
   * ==================== ANALYSES ET RAPPORTS ====================
   */

  /**
   * GET /api/inventaire/analytics/:complexeId
   * Analyses de consommation et valeur du stock
   */
  static async getInventaireAnalytics(request, response) {
    try {
      const { complexeId } = request.params;

      logger.info('Get inventory analytics', { complexeId });

      const result = await InventaireService.calculateStockValue(parseInt(complexeId));

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error getting inventory analytics for complexe ${request.params.complexeId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des analyses d\'inventaire'
      });
    }
  }

  /**
   * GET /api/inventaire/value/:complexeId
   * Calcul de la valeur totale du stock
   */
  static async getStockValue(request, response) {
    try {
      const { complexeId } = request.params;

      logger.info('Get stock value', { complexeId });

      const result = await InventaireService.calculateStockValue(parseInt(complexeId));

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error calculating stock value for complexe ${request.params.complexeId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors du calcul de la valeur du stock'
      });
    }
  }

  /**
   * GET /api/inventaire/dashboard/:complexeId
   * Dashboard avec métriques clés de l'inventaire
   */
  static async getInventaireDashboard(request, response) {
    try {
      const { complexeId } = request.params;

      logger.info('Get inventory dashboard', { complexeId });

      // Récupération de plusieurs métriques en parallèle
      const [stockResult, alertsResult, valueResult] = await Promise.all([
        InventaireService.getStockIngredients(parseInt(complexeId)),
        InventaireService.generateStockAlerts(parseInt(complexeId)),
        InventaireService.calculateStockValue(parseInt(complexeId))
      ]);

      const dashboardData = {
        stock: stockResult.data,
        alerts: alertsResult.data,
        value: valueResult.data,
        summary: {
          totalIngredients: stockResult.data.statistics.totalIngredients,
          totalValue: stockResult.data.statistics.totalValue,
          alertsCount: alertsResult.data.length,
          lowStockItems: stockResult.data.statistics.lowStockItems,
          outOfStockItems: stockResult.data.statistics.outOfStockItems
        }
      };

      response.json({
        success: true,
        data: dashboardData,
        message: 'Dashboard de l\'inventaire récupéré avec succès'
      });

    } catch (error) {
      logger.error(`Error getting inventory dashboard for complexe ${request.params.complexeId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération du dashboard'
      });
    }
  }
}

module.exports = InventaireController;
