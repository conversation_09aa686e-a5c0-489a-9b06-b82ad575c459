const PermissionService = require('../services/permission.service');
const EmployeeTypeService = require('../services/employeeType.service');
const logger = require('../logger');

/**
 * Contrôleur pour les permissions - Système simplifié
 * Phase 5 : Refactoring des contrôleurs
 */
class PermissionController {
  /**
   * Obtenir toutes les permissions simplifiées
   */
  static async getAllPermissions(req, res) {
    try {
      const permissions = PermissionService.getSimplifiedPermissions();
      res.json({
        success: true,
        data: permissions
      });
    } catch (error) {
      logger.error('Error getting all permissions:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des permissions'
      });
    }
  }

  /**
   * Obtenir la liste des permissions disponibles
   */
  static async getPermissionsList(req, res) {
    try {
      const permissions = PermissionService.getAvailablePermissions();
      res.json({
        success: true,
        data: permissions
      });
    } catch (error) {
      logger.error('Error getting permissions list:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de la liste des permissions'
      });
    }
  }

  /**
   * Obtenir les permissions par type d'utilisateur
   */
  static async getPermissionsByUserType(req, res) {
    try {
      const permissionsByType = PermissionService.getPermissionsByUserType();
      res.json({
        success: true,
        data: permissionsByType
      });
    } catch (error) {
      logger.error('Error getting permissions by user type:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des permissions par type'
      });
    }
  }

  /**
   * Obtenir les permissions d'un utilisateur selon le nouveau système
   */
  static async getUserPermissions(req, res) {
    try {
      const permissions = await PermissionService.getUserPermissions(
        req.user.id,
        req.user.role,
        req.user.complexe_id,
        req.user.type_employe
      );

      res.json({
        success: true,
        data: {
          permissions,
          user_type: req.user.role,
          employee_type: req.user.type_employe
        }
      });
    } catch (error) {
      logger.error('Error getting user permissions:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des permissions utilisateur'
      });
    }
  }

  /**
   * Vérifier une permission spécifique pour l'utilisateur
   */
  static async checkUserPermission(req, res) {
    try {
      const { permission } = req.params;

      const hasPermission = await PermissionService.hasPermission(
        req.user.id,
        req.user.role,
        permission,
        req.user.complexe_id,
        req.user.type_employe
      );

      res.json({
        success: true,
        data: {
          permission,
          hasPermission,
          user_type: req.user.role,
          employee_type: req.user.type_employe
        }
      });
    } catch (error) {
      logger.error('Error checking user permission:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification de la permission'
      });
    }
  }

  /**
   * Obtenir les permissions pour un type d'employé spécifique
   */
  static async getPermissionsForEmployeeType(req, res) {
    try {
      const { type } = req.params;

      if (!EmployeeTypeService.isValidType(type)) {
        return res.status(400).json({
          success: false,
          message: `Type d'employé invalide: ${type}`
        });
      }

      const permissions = PermissionService.getPermissionsForEmployeeType(type);
      const typeInfo = EmployeeTypeService.getTypeInfo(type);

      res.json({
        success: true,
        data: {
          employee_type: type,
          type_info: typeInfo,
          permissions
        }
      });
    } catch (error) {
      logger.error('Error getting permissions for employee type:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des permissions pour ce type d\'employé'
      });
    }
  }

  /**
   * Vérifier si l'utilisateur peut accéder à un service spécifique
   */
  static async checkServiceAccess(req, res) {
    try {
      const { serviceType } = req.params;

      // Pour les admins, accès à tous les services
      if (['super_admin', 'admin_chaine', 'admin_complexe'].includes(req.user.role)) {
        return res.json({
          success: true,
          data: {
            service_type: serviceType,
            has_access: true,
            reason: 'Admin access'
          }
        });
      }

      // Pour les employés, vérifier selon leur type et services autorisés
      if (req.user.role === 'employe') {
        const hasAccess = EmployeeTypeService.validateServiceAccess(req.user, serviceType);

        return res.json({
          success: true,
          data: {
            service_type: serviceType,
            has_access: hasAccess,
            employee_type: req.user.type_employe,
            services_autorises: req.user.services_autorises
          }
        });
      }

      res.json({
        success: true,
        data: {
          service_type: serviceType,
          has_access: false,
          reason: 'Unknown user type'
        }
      });
    } catch (error) {
      logger.error('Error checking service access:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification de l\'accès au service'
      });
    }
  }
}

module.exports = PermissionController;
