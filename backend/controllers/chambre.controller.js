const ChambreService = require('../services/chambre.service');
const logger = require('../logger');

class ChambreController {
  // Routes client
  static async getDisponibilites(request, response) {
    try {
      const params = request.query;
      logger.info('Consultation disponibilités chambres', { params });
      
      const result = await ChambreService.getDisponibilites(params);
      response.json(result);
    } catch (error) {
      logger.error('Erreur consultation disponibilités chambres', error);
      response.status(error.code || 500).json({ 
        success: false,
        message: error.message || 'Erreur lors de la consultation des disponibilités'
      });
    }
  }

  // Routes réception
  static async verrouillerChambre(request, response) {
    try {
      const { chambreId } = request.params;
      const params = request.body;
      logger.info('Verrouillage chambre', { chambreId, params });
      
      const result = await ChambreService.verrouillerChambre(chambreId, params);
      response.json(result);
    } catch (error) {
      logger.error('Erreur verrouillage chambre', error);
      response.status(error.code || 500).json({ 
        success: false,
        message: error.message || 'Erreur lors du verrouillage de la chambre'
      });
    }
  }

  static async libererChambre(request, response) {
    try {
      const { chambreId } = request.params;
      logger.info('Libération chambre', { chambreId });
      
      const result = await ChambreService.libererChambre(chambreId);
      response.json(result);
    } catch (error) {
      logger.error('Erreur libération chambre', error);
      response.status(error.code || 500).json({ 
        success: false,
        message: error.message || 'Erreur lors de la libération de la chambre'
      });
    }
  }

  static async updateStatutChambre(request, response) {
    try {
      const { chambreId } = request.params;
      const params = request.body;
      logger.info('Mise à jour statut chambre', { chambreId, params });
      
      const result = await ChambreService.updateStatutChambre(chambreId, params);
      response.json(result);
    } catch (error) {
      logger.error('Erreur mise à jour statut chambre', error);
      response.status(error.code || 500).json({ 
        success: false,
        message: error.message || 'Erreur lors de la mise à jour du statut de la chambre'
      });
    }
  }

  // Routes admin
  static async nettoyerVerrousExpires(request, response) {
    try {
      logger.info('Nettoyage verrous expirés');
      
      const result = await ChambreService.nettoyerVerrousExpires();
      response.json(result);
    } catch (error) {
      logger.error('Erreur nettoyage verrous expirés', error);
      response.status(error.code || 500).json({ 
        success: false,
        message: error.message || 'Erreur lors du nettoyage des verrous expirés'
      });
    }
  }

  // Routes publiques
  static async getChambres(request, response) {
    try {
      const params = request.query;
      logger.info('Consultation liste des chambres', { params });
      
      const result = await ChambreService.getChambres(params);
      response.json(result);
    } catch (error) {
      logger.error('Erreur consultation liste des chambres', error);
      response.status(error.code || 500).json({ 
        success: false,
        message: error.message || 'Erreur lors de la consultation de la liste des chambres'
      });
    }
  }

  static async getChambreById(request, response) {
    try {
      const { id } = request.params;
      logger.info('Consultation détails chambre', { chambreId: id });

      const result = await ChambreService.getChambreById(id);
      response.json(result);
    } catch (error) {
      logger.error('Erreur consultation détails chambre', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la consultation des détails de la chambre'
      });
    }
  }

  static async getStatistiquesChambre(request, response) {
    try {
      const { id } = request.params;
      logger.info('Consultation statistiques chambre', { chambreId: id });

      const result = await ChambreService.getStatistiquesChambre(id);
      response.json(result);
    } catch (error) {
      logger.error('Erreur consultation statistiques chambre', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la consultation des statistiques de la chambre'
      });
    }
  }

  // Création d'une nouvelle chambre
  static async createChambre(request, response) {
    try {
      const params = request.body;
      logger.info('Création nouvelle chambre', { params });
      
      const result = await ChambreService.createChambre(params);
      response.status(201).json(result);
    } catch (error) {
      logger.error('Erreur création chambre', error);
      response.status(error.code || 500).json({ 
        success: false,
        message: error.message || 'Erreur lors de la création de la chambre'
      });
    }
  }

  // Mise à jour d'une chambre
  static async updateChambre(request, response) {
    try {
      const { id } = request.params;
      const params = request.body;
      logger.info('Mise à jour chambre', { chambreId: id, params });
      
      const result = await ChambreService.updateChambre(id, params);
      response.json(result);
    } catch (error) {
      logger.error('Erreur mise à jour chambre', error);
      response.status(error.code || 500).json({ 
        success: false,
        message: error.message || 'Erreur lors de la mise à jour de la chambre'
      });
    }
  }

  // Suppression d'une chambre
  static async deleteChambre(request, response) {
    try {
      const { id } = request.params;
      logger.info('Suppression chambre', { chambreId: id });
      
      const result = await ChambreService.deleteChambre(id);
      response.json(result);
    } catch (error) {
      logger.error('Erreur suppression chambre', error);
      response.status(error.code || 500).json({ 
        success: false,
        message: error.message || 'Erreur lors de la suppression de la chambre'
      });
    }
  }

  static async getCalendrierChambre(request, response) {
    try {
      const { id } = request.params;
      const { date_debut, date_fin } = request.query;
      
      logger.info('Consultation calendrier chambre', { 
        chambreId: id, 
        date_debut, 
        date_fin 
      });
      
      const result = await ChambreService.getCalendrierChambre(id, date_debut, date_fin);
      response.json(result);
    } catch (error) {
      logger.error('Erreur consultation calendrier chambre', error);
      response.status(error.code || 500).json({ 
        success: false,
        message: error.message || 'Erreur lors de la consultation du calendrier de la chambre'
      });
    }
  }

  static async getHistoriqueChambre(request, response) {
    try {
      const { id } = request.params;
      logger.info('Consultation historique chambre', { chambreId: id });
      
      const result = await ChambreService.getHistoriqueChambre(id);
      response.json(result);
    } catch (error) {
      logger.error('Erreur consultation historique chambre', error);
      response.status(error.code || 500).json({ 
        success: false,
        message: error.message || 'Erreur lors de la consultation de l\'historique de la chambre'
      });
    }
  }
}

module.exports = ChambreController; 