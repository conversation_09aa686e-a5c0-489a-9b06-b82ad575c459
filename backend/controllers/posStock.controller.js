const POSStockIntegration = require('../services/posStockIntegration.service');
const logger = require('../logger');

/**
 * Contrôleur pour l'intégration POS-Stock
 * Gère les endpoints d'intégration entre le système POS et l'inventaire
 */
class POSStockController {

  /**
   * Traitement d'une transaction POS avec déduction automatique du stock
   */
  static async processTransactionWithStock(request, response) {
    try {
      const transactionData = request.body;
      
      // Validation des données requises
      if (!transactionData.transaction_id) {
        return response.status(400).json({
          success: false,
          message: 'L\'ID de transaction est requis'
        });
      }

      if (!transactionData.lignes_transaction || !Array.isArray(transactionData.lignes_transaction)) {
        return response.status(400).json({
          success: false,
          message: 'Les lignes de transaction sont requises et doivent être un tableau'
        });
      }

      logger.info('Traitement transaction avec déduction stock', { 
        transactionId: transactionData.transaction_id,
        lignesCount: transactionData.lignes_transaction.length 
      });

      const result = await POSStockIntegration.processTransactionWithStock(transactionData);
      
      if (result.success) {
        response.json({
          success: true,
          data: result.data,
          message: 'Transaction traitée avec succès'
        });
      } else {
        response.status(result.code || 400).json({
          success: false,
          message: result.message
        });
      }
    } catch (error) {
      logger.error('Erreur traitement transaction avec stock:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors du traitement de la transaction'
      });
    }
  }

  /**
   * Vérification de la disponibilité des ingrédients pour un produit
   */
  static async checkIngredientAvailability(request, response) {
    try {
      const { produit_id, quantite, complexe_id } = request.body;
      
      // Validation des données requises
      if (!produit_id || !quantite || !complexe_id) {
        return response.status(400).json({
          success: false,
          message: 'L\'ID du produit, la quantité et l\'ID du complexe sont requis'
        });
      }

      if (quantite <= 0) {
        return response.status(400).json({
          success: false,
          message: 'La quantité doit être supérieure à 0'
        });
      }

      logger.info('Vérification disponibilité ingrédients', { produitId: produit_id, quantite, complexeId: complexe_id });

      const result = await POSStockIntegration.checkIngredientAvailability(produit_id, quantite, complexe_id);
      
      if (result.success) {
        response.json({
          success: true,
          data: result.data,
          message: 'Vérification terminée'
        });
      } else {
        response.status(result.code || 400).json({
          success: false,
          message: result.message
        });
      }
    } catch (error) {
      logger.error('Erreur vérification disponibilité ingrédients:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification de la disponibilité'
      });
    }
  }

  /**
   * Génération des alertes de stock faible
   */
  static async generateStockAlerts(request, response) {
    try {
      const { complexeId } = request.params;
      
      logger.info('Génération alertes stock', { complexeId });

      const result = await POSStockIntegration.generateStockAlerts(complexeId);
      
      if (result.success) {
        response.json({
          success: true,
          data: result.data,
          message: `${result.data.total} alertes générées`
        });
      } else {
        response.status(result.code || 500).json({
          success: false,
          message: result.message
        });
      }
    } catch (error) {
      logger.error('Erreur génération alertes stock:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la génération des alertes'
      });
    }
  }

  /**
   * Calcul de la consommation réelle d'ingrédients
   */
  static async calculateIngredientConsumption(request, response) {
    try {
      const { complexeId } = request.params;
      const { date_debut, date_fin } = request.query;
      
      // Dates par défaut : 30 derniers jours
      const dateFin = date_fin ? new Date(date_fin) : new Date();
      const dateDebut = date_debut ? new Date(date_debut) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      logger.info('Calcul consommation ingrédients', { complexeId, dateDebut, dateFin });

      const result = await POSStockIntegration.calculateIngredientConsumption(complexeId, dateDebut, dateFin);
      
      if (result.success) {
        response.json({
          success: true,
          data: result.data,
          message: 'Calcul de consommation terminé'
        });
      } else {
        response.status(result.code || 500).json({
          success: false,
          message: result.message
        });
      }
    } catch (error) {
      logger.error('Erreur calcul consommation ingrédients:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors du calcul de la consommation'
      });
    }
  }

  /**
   * Prévisions de rupture de stock basées sur les ventes
   */
  static async predictStockBreakdown(request, response) {
    try {
      const { complexeId } = request.params;
      const { days = 7 } = request.query;
      
      const joursPrevisionnels = parseInt(days);
      if (joursPrevisionnels <= 0 || joursPrevisionnels > 365) {
        return response.status(400).json({
          success: false,
          message: 'Le nombre de jours doit être entre 1 et 365'
        });
      }

      logger.info('Prévisions rupture stock', { complexeId, joursPrevisionnels });

      const result = await POSStockIntegration.predictStockBreakdown(complexeId, joursPrevisionnels);
      
      if (result.success) {
        response.json({
          success: true,
          data: result.data,
          message: `Prévisions calculées pour ${joursPrevisionnels} jours`
        });
      } else {
        response.status(result.code || 500).json({
          success: false,
          message: result.message
        });
      }
    } catch (error) {
      logger.error('Erreur prévisions rupture stock:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors du calcul des prévisions'
      });
    }
  }

  /**
   * Suggestions de commandes optimales basées sur la consommation
   */
  static async suggestOptimalOrders(request, response) {
    try {
      const { complexeId } = request.params;
      const { days = 14 } = request.query;
      
      const joursCommande = parseInt(days);
      if (joursCommande <= 0 || joursCommande > 90) {
        return response.status(400).json({
          success: false,
          message: 'Le nombre de jours doit être entre 1 et 90'
        });
      }

      logger.info('Suggestions commandes optimales', { complexeId, joursCommande });

      const result = await POSStockIntegration.suggestOptimalOrders(complexeId, joursCommande);
      
      if (result.success) {
        response.json({
          success: true,
          data: result.data,
          message: `Suggestions calculées pour ${joursCommande} jours`
        });
      } else {
        response.status(result.code || 500).json({
          success: false,
          message: result.message
        });
      }
    } catch (error) {
      logger.error('Erreur suggestions commandes optimales:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors du calcul des suggestions'
      });
    }
  }

  /**
   * Dashboard complet de l'intégration POS-Stock
   */
  static async getDashboard(request, response) {
    try {
      const { complexeId } = request.params;
      
      logger.info('Récupération dashboard POS-Stock', { complexeId });

      // Récupérer toutes les données nécessaires en parallèle
      const [alertes, previsions, suggestions] = await Promise.all([
        POSStockIntegration.generateStockAlerts(complexeId),
        POSStockIntegration.predictStockBreakdown(complexeId, 7),
        POSStockIntegration.suggestOptimalOrders(complexeId, 14)
      ]);

      const dashboard = {
        alertes: alertes.success ? alertes.data : { alertes: [], total: 0 },
        previsions: previsions.success ? previsions.data : { ingredients_a_risque: [] },
        suggestions_commandes: suggestions.success ? suggestions.data : { ingredients_a_commander: [] },
        resume: {
          alertes_actives: alertes.success ? alertes.data.total : 0,
          ingredients_a_risque: previsions.success ? previsions.data.ingredients_a_risque.length : 0,
          commandes_suggeres: suggestions.success ? suggestions.data.ingredients_a_commander.length : 0,
          cout_commandes_estime: suggestions.success ? suggestions.data.cout_total_estime : 0
        }
      };

      response.json({
        success: true,
        data: dashboard,
        message: 'Dashboard récupéré avec succès'
      });
    } catch (error) {
      logger.error('Erreur récupération dashboard POS-Stock:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération du dashboard'
      });
    }
  }

  /**
   * Rapport d'impact des ventes sur le stock
   */
  static async getStockImpactReport(request, response) {
    try {
      const { complexeId } = request.params;
      const { date_debut, date_fin, service_id } = request.query;

      // Dates par défaut : 7 derniers jours
      const dateFin = date_fin ? new Date(date_fin) : new Date();
      const dateDebut = date_debut ? new Date(date_debut) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

      logger.info('Génération rapport impact stock', { complexeId, dateDebut, dateFin, serviceId: service_id });

      // Récupérer la consommation et les prévisions
      const [consommation, previsions] = await Promise.all([
        POSStockIntegration.calculateIngredientConsumption(complexeId, dateDebut, dateFin),
        POSStockIntegration.predictStockBreakdown(complexeId, 7)
      ]);

      const rapport = {
        periode: { debut: dateDebut, fin: dateFin },
        consommation: consommation.success ? consommation.data : { consommation: [] },
        impact_previsionnel: previsions.success ? previsions.data : { ingredients_a_risque: [] },
        resume: {
          ingredients_consommes: consommation.success ? consommation.data.total_ingredients : 0,
          consommation_totale: consommation.success ? consommation.data.consommation_totale : 0,
          ingredients_a_risque: previsions.success ? previsions.data.ingredients_a_risque.length : 0
        }
      };

      response.json({
        success: true,
        data: rapport,
        message: 'Rapport d\'impact généré avec succès'
      });
    } catch (error) {
      logger.error('Erreur génération rapport impact stock:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la génération du rapport d\'impact'
      });
    }
  }

  /**
   * Configuration de la déduction automatique pour un service
   */
  static async configureAutoDeduction(request, response) {
    try {
      const { serviceId } = request.params;
      const { auto_deduction_enabled = true, seuil_alerte = 10 } = request.body;

      logger.info('Configuration déduction automatique', { serviceId, autoDeductionEnabled: auto_deduction_enabled, seuilAlerte: seuil_alerte });

      // TODO: Implémenter la logique de configuration dans la base de données
      // Pour l'instant, on retourne une réponse simulée
      const configuration = {
        service_id: serviceId,
        auto_deduction_enabled,
        seuil_alerte,
        updated_at: new Date().toISOString()
      };

      response.json({
        success: true,
        data: configuration,
        message: 'Configuration mise à jour avec succès'
      });
    } catch (error) {
      logger.error('Erreur configuration déduction automatique:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la configuration'
      });
    }
  }

  /**
   * Récupération de la configuration d'un service
   */
  static async getServiceConfiguration(request, response) {
    try {
      const { serviceId } = request.params;

      logger.info('Récupération configuration service', { serviceId });

      // TODO: Récupérer la configuration depuis la base de données
      // Pour l'instant, on retourne une configuration par défaut
      const configuration = {
        service_id: serviceId,
        auto_deduction_enabled: true,
        seuil_alerte: 10,
        derniere_synchronisation: new Date().toISOString(),
        recettes_actives: 0, // À calculer depuis la base
        produits_lies: 0 // À calculer depuis la base
      };

      response.json({
        success: true,
        data: configuration,
        message: 'Configuration récupérée avec succès'
      });
    } catch (error) {
      logger.error('Erreur récupération configuration service:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de la configuration'
      });
    }
  }

  /**
   * Vérifier la disponibilité d'un produit
   */
  static async checkProductAvailability(request, response) {
    try {
      const { productId } = request.params;
      const { quantity = 1 } = request.query;

      logger.info('Vérification disponibilité produit', { 
        productId,
        quantity
      });

      const result = await POSStockIntegration.checkProductAvailability(
        parseInt(productId),
        parseInt(quantity)
      );

      if (result.success) {
        response.json({
          success: true,
          data: result.data,
          message: result.data.message
        });
      } else {
        response.status(result.code || 400).json({
          success: false,
          message: result.message
        });
      }
    } catch (error) {
      logger.error('Erreur vérification disponibilité produit:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification de disponibilité'
      });
    }
  }

  /**
   * Obtenir le statut de stock d'un produit
   */
  static async getProductStockStatus(request, response) {
    try {
      const { productId } = request.params;

      logger.info('Récupération statut stock produit', { productId });

      const result = await POSStockIntegration.getProductStockStatus(
        parseInt(productId)
      );

      if (result.success) {
        response.json({
          success: true,
          data: result.data,
          message: 'Statut de stock récupéré avec succès'
        });
      } else {
        response.status(result.code || 400).json({
          success: false,
          message: result.message
        });
      }
    } catch (error) {
      logger.error('Erreur récupération statut stock produit:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération du statut de stock'
      });
    }
  }

  /**
   * Valider la disponibilité des stocks pour une commande
   */
  static async validateOrderStock(request, response) {
    try {
      const { orderItems } = request.body;

      if (!orderItems || !Array.isArray(orderItems)) {
        return response.status(400).json({
          success: false,
          message: 'La liste des items de commande est requise et doit être un tableau'
        });
      }

      logger.info('Validation stocks commande', { 
        itemsCount: orderItems.length 
      });

      const result = await POSStockIntegration.validateOrderStock(orderItems);

      if (result.success) {
        response.json({
          success: true,
          data: result.data,
          message: result.data.message
        });
      } else {
        response.status(result.code || 400).json({
          success: false,
          message: result.message
        });
      }
    } catch (error) {
      logger.error('Erreur validation stocks commande:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la validation des stocks'
      });
    }
  }
}

module.exports = POSStockController;
