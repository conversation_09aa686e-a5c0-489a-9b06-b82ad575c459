const TemplateService = require('../services/template.service');
const logger = require('../logger');

class TemplateController {

  /**
   * ==================== CRUD DES TEMPLATES ====================
   */

  /**
   * GET /api/templates
   * Liste des templates avec filtres
   */
  static async getTemplates(request, response) {
    try {
      const { chaineId } = request.query;
      const filters = {
        typeService: request.query.typeService,
        typeImport: request.query.typeImport,
        actif: request.query.actif !== undefined ? request.query.actif === 'true' : undefined
      };

      if (!chaineId) {
        return response.status(400).json({
          success: false,
          message: 'L\'ID de la chaîne est requis'
        });
      }

      logger.info('Get templates', { chaineId, filters });

      const result = await TemplateService.getTemplates(parseInt(chaineId), filters);

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Error getting templates:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des templates'
      });
    }
  }

  /**
   * GET /api/templates/:id
   * Détails d'un template
   */
  static async getTemplateById(request, response) {
    try {
      const { id } = request.params;

      logger.info('Get template by ID', { templateId: id });

      const result = await TemplateService.getTemplateById(parseInt(id));

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error getting template ${request.params.id}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération du template'
      });
    }
  }

  /**
   * POST /api/templates
   * Création d'un nouveau template
   */
  static async createTemplate(request, response) {
    try {
      const templateData = request.body;

      // Validation des champs requis
      const requiredFields = ['chaineId', 'typeService', 'typeImport', 'nomTemplate', 'colonnesRequises'];
      for (const field of requiredFields) {
        if (!templateData[field]) {
          return response.status(400).json({
            success: false,
            message: `Le champ ${field} est requis`
          });
        }
      }

      // Validation du type de service
      const allowedServiceTypes = ['Restaurant', 'Bar', 'Piscine'];
      if (!allowedServiceTypes.includes(templateData.typeService)) {
        return response.status(400).json({
          success: false,
          message: `Type de service invalide. Types autorisés: ${allowedServiceTypes.join(', ')}`
        });
      }

      // Validation du type d'import
      const allowedImportTypes = ['MENU', 'CARTE', 'INVENTAIRE'];
      if (!allowedImportTypes.includes(templateData.typeImport)) {
        return response.status(400).json({
          success: false,
          message: `Type d'import invalide. Types autorisés: ${allowedImportTypes.join(', ')}`
        });
      }

      logger.info('Create template', { templateData });

      const result = await TemplateService.createTemplate(templateData);

      response.status(201).json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Error creating template:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la création du template'
      });
    }
  }

  /**
   * PUT /api/templates/:id
   * Mise à jour d'un template
   */
  static async updateTemplate(request, response) {
    try {
      const { id } = request.params;
      const templateData = request.body;

      logger.info('Update template', { templateId: id, templateData });

      const result = await TemplateService.updateTemplate(parseInt(id), templateData);

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error updating template ${request.params.id}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la mise à jour du template'
      });
    }
  }

  /**
   * DELETE /api/templates/:id
   * Suppression d'un template
   */
  static async deleteTemplate(request, response) {
    try {
      const { id } = request.params;

      logger.info('Delete template', { templateId: id });

      const result = await TemplateService.deleteTemplate(parseInt(id));

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error deleting template ${request.params.id}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la suppression du template'
      });
    }
  }

  /**
   * ==================== GÉNÉRATION DE FICHIERS ====================
   */

  /**
   * POST /api/templates/:id/generate
   * Génération d'un fichier Excel template
   */
  static async generateTemplateFile(request, response) {
    try {
      const { id } = request.params;

      logger.info('Generate template file', { templateId: id });

      const result = await TemplateService.generateTemplate(parseInt(id));

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error generating template file ${request.params.id}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la génération du fichier template'
      });
    }
  }

  /**
   * GET /api/templates/:id/download
   * Téléchargement direct d'un fichier template
   */
  static async downloadTemplate(request, response) {
    try {
      const { id } = request.params;

      logger.info('Download template', { templateId: id });

      const templateFile = await TemplateService.generateTemplate(parseInt(id));
      const filePath = templateFile.data.filePath;
      const fileName = templateFile.data.fileName;

      // Configuration des headers pour le téléchargement
      response.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      response.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);

      // Envoi du fichier
      response.sendFile(filePath, { root: '.' }, (error) => {
        if (error) {
          logger.error('Error sending template file:', error);
          if (!response.headersSent) {
            response.status(500).json({
              success: false,
              message: 'Erreur lors du téléchargement du template'
            });
          }
        }
      });

    } catch (error) {
      logger.error(`Error downloading template ${request.params.id}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors du téléchargement du template'
      });
    }
  }

  /**
   * ==================== GESTION AVANCÉE ====================
   */

  /**
   * POST /api/templates/:id/duplicate
   * Duplication d'un template
   */
  static async duplicateTemplate(request, response) {
    try {
      const { id } = request.params;
      const { newName } = request.body;

      logger.info('Duplicate template', { templateId: id, newName });

      const result = await TemplateService.duplicateTemplate(parseInt(id), newName);

      response.status(201).json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error duplicating template ${request.params.id}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la duplication du template'
      });
    }
  }

  /**
   * GET /api/templates/defaults
   * Récupération des templates par défaut
   */
  static async getDefaultTemplates(request, response) {
    try {
      logger.info('Get default templates');

      const defaultTemplates = TemplateService.getDefaultTemplates();

      response.json({
        success: true,
        data: defaultTemplates,
        message: 'Templates par défaut récupérés avec succès'
      });

    } catch (error) {
      logger.error('Error getting default templates:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des templates par défaut'
      });
    }
  }

  /**
   * POST /api/templates/create-from-default
   * Création d'un template à partir d'un modèle par défaut
   */
  static async createFromDefault(request, response) {
    try {
      const { chaineId, typeService, customizations = {} } = request.body;

      if (!chaineId || !typeService) {
        return response.status(400).json({
          success: false,
          message: 'Les champs chaineId et typeService sont requis'
        });
      }

      logger.info('Create template from default', { chaineId, typeService, customizations });

      const defaultTemplates = TemplateService.getDefaultTemplates();
      const defaultTemplate = defaultTemplates[typeService.toLowerCase()];

      if (!defaultTemplate) {
        return response.status(404).json({
          success: false,
          message: `Aucun template par défaut trouvé pour le type de service: ${typeService}`
        });
      }

      // Fusion avec les personnalisations
      const templateData = {
        chaineId: parseInt(chaineId),
        typeService: defaultTemplate.typeService,
        typeImport: defaultTemplate.typeImport,
        nomTemplate: customizations.nomTemplate || `Template ${typeService} par défaut`,
        description: customizations.description || `Template par défaut pour ${typeService}`,
        colonnesRequises: customizations.colonnesRequises || defaultTemplate.colonnesRequises,
        exempleDonnees: customizations.exempleDonnees || defaultTemplate.exempleDonnees,
        reglesValidation: customizations.reglesValidation || defaultTemplate.reglesValidation,
        mappingDefaut: customizations.mappingDefaut || defaultTemplate.mappingDefaut
      };

      const result = await TemplateService.createTemplate(templateData);

      response.status(201).json({
        success: true,
        data: result.data,
        message: 'Template créé à partir du modèle par défaut avec succès'
      });

    } catch (error) {
      logger.error('Error creating template from default:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la création du template depuis le modèle par défaut'
      });
    }
  }

  /**
   * ==================== VALIDATION ET APERÇU ====================
   */

  /**
   * POST /api/templates/validate
   * Validation de la structure d'un template
   */
  static async validateTemplate(request, response) {
    try {
      const templateData = request.body;

      logger.info('Validate template structure', { templateData });

      // Validation via le service (qui lève une exception si invalide)
      TemplateService.validateTemplateStructure(templateData);

      response.json({
        success: true,
        message: 'Structure du template valide'
      });

    } catch (error) {
      logger.error('Error validating template:', error);
      response.status(error.code || 400).json({
        success: false,
        message: error.message || 'Erreur lors de la validation du template'
      });
    }
  }

  /**
   * GET /api/templates/types
   * Liste des types de services et d'imports disponibles
   */
  static async getTemplateTypes(request, response) {
    try {
      const templateTypes = {
        serviceTypes: [
          { value: 'Restaurant', label: 'Restaurant', description: 'Service de restauration' },
          { value: 'Bar', label: 'Bar', description: 'Service de bar et boissons' },
          { value: 'Piscine', label: 'Piscine', description: 'Service piscine et loisirs' }
        ],
        importTypes: [
          { value: 'MENU', label: 'Menu', description: 'Import de menus et plats' },
          { value: 'CARTE', label: 'Carte', description: 'Import de cartes de boissons' },
          { value: 'INVENTAIRE', label: 'Inventaire', description: 'Import d\'inventaire d\'ingrédients' }
        ]
      };

      response.json({
        success: true,
        data: templateTypes,
        message: 'Types de templates récupérés avec succès'
      });

    } catch (error) {
      logger.error('Error getting template types:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des types de templates'
      });
    }
  }

  /**
   * GET /api/templates/preview/:id
   * Aperçu de la structure d'un template
   */
  static async previewTemplate(request, response) {
    try {
      const { id } = request.params;

      logger.info('Preview template', { templateId: id });

      const templateResult = await TemplateService.getTemplateById(parseInt(id));
      const template = templateResult.data;

      const preview = {
        templateInfo: {
          nom: template.nom_template,
          typeService: template.type_service,
          typeImport: template.type_import,
          description: template.description
        },
        structure: {
          colonnesRequises: JSON.parse(template.colonnes_requises || '{}'),
          reglesValidation: JSON.parse(template.regles_validation || '{}'),
          mappingDefaut: JSON.parse(template.mapping_defaut || '{}')
        },
        exemples: JSON.parse(template.exemple_donnees || '{}')
      };

      response.json({
        success: true,
        data: preview,
        message: 'Aperçu du template généré avec succès'
      });

    } catch (error) {
      logger.error(`Error previewing template ${request.params.id}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la génération de l\'aperçu'
      });
    }
  }
}

module.exports = TemplateController;
