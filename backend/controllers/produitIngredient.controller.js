const ProduitIngredientService = require('../services/produitIngredient.service');
const logger = require('../logger');

/**
 * Contrôleur pour la gestion des liens produits-ingrédients
 */
class ProduitIngredientController {

  /**
   * POST /api/produit-ingredient/add
   * Ajouter un ingrédient à un produit
   */
  static async ajouterIngredientProduit(request, response) {
    try {
      const { produitId, ingredientId, quantiteNecessaire, uniteMesure, options = {} } = request.body;

      // Validation des champs requis
      if (!produitId || !ingredientId || !quantiteNecessaire || !uniteMesure) {
        return response.status(400).json({
          success: false,
          message: 'Produit ID, Ingrédient ID, quantité nécessaire et unité de mesure sont requis'
        });
      }

      logger.info('Ajout ingrédient à produit', { produitId, ingredientId, quantiteNecessaire });

      const result = await ProduitIngredientService.ajouterIngredientProduit(
        parseInt(produitId),
        parseInt(ingredientId),
        parseFloat(quantiteNecessaire),
        uniteMesure,
        options
      );

      response.status(201).json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Erreur ajout ingrédient à produit:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de l\'ajout de l\'ingrédient au produit'
      });
    }
  }

  /**
   * DELETE /api/produit-ingredient/:produitId/:ingredientId
   * Supprimer un ingrédient d'un produit
   */
  static async supprimerIngredientProduit(request, response) {
    try {
      const { produitId, ingredientId } = request.params;

      if (!produitId || !ingredientId) {
        return response.status(400).json({
          success: false,
          message: 'Produit ID et Ingrédient ID sont requis'
        });
      }

      logger.info('Suppression ingrédient de produit', { produitId, ingredientId });

      const result = await ProduitIngredientService.supprimerIngredientProduit(
        parseInt(produitId),
        parseInt(ingredientId)
      );

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Erreur suppression ingrédient de produit:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la suppression de l\'ingrédient du produit'
      });
    }
  }

  /**
   * GET /api/produit-ingredient/produit/:produitId
   * Récupérer les ingrédients d'un produit
   */
  static async getIngredientsProduit(request, response) {
    try {
      const { produitId } = request.params;

      if (!produitId) {
        return response.status(400).json({
          success: false,
          message: 'Produit ID est requis'
        });
      }

      logger.info('Récupération ingrédients produit', { produitId });

      const result = await ProduitIngredientService.getIngredientsProduit(parseInt(produitId));

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Erreur récupération ingrédients produit:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des ingrédients du produit'
      });
    }
  }

  /**
   * GET /api/produit-ingredient/complexe/:complexeId
   * Récupérer tous les produits avec leurs ingrédients pour un complexe
   */
  static async getProduitsAvecIngredients(request, response) {
    try {
      const { complexeId } = request.params;
      const { serviceId } = request.query;

      if (!complexeId) {
        return response.status(400).json({
          success: false,
          message: 'Complexe ID est requis'
        });
      }

      logger.info('Récupération produits avec ingrédients', { complexeId, serviceId });

      const result = await ProduitIngredientService.getProduitsAvecIngredients(
        parseInt(complexeId),
        serviceId ? parseInt(serviceId) : null
      );

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Erreur récupération produits avec ingrédients:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des produits avec ingrédients'
      });
    }
  }

  /**
   * PUT /api/produit-ingredient/:produitId/:ingredientId
   * Mettre à jour un lien produit-ingrédient
   */
  static async updateIngredientProduit(request, response) {
    try {
      const { produitId, ingredientId } = request.params;
      const updateData = request.body;

      if (!produitId || !ingredientId) {
        return response.status(400).json({
          success: false,
          message: 'Produit ID et Ingrédient ID sont requis'
        });
      }

      logger.info('Mise à jour lien produit-ingrédient', { produitId, ingredientId, updateData });

      const result = await ProduitIngredientService.updateIngredientProduit(
        parseInt(produitId),
        parseInt(ingredientId),
        updateData
      );

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Erreur mise à jour lien produit-ingrédient:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la mise à jour du lien produit-ingrédient'
      });
    }
  }

  /**
   * POST /api/produit-ingredient/check-availability
   * Vérifier la disponibilité des ingrédients pour un produit
   */
  static async verifierDisponibiliteProduit(request, response) {
    try {
      const { produitId, quantite = 1 } = request.body;

      if (!produitId) {
        return response.status(400).json({
          success: false,
          message: 'Produit ID est requis'
        });
      }

      logger.info('Vérification disponibilité produit', { produitId, quantite });

      const result = await ProduitIngredientService.verifierDisponibiliteProduit(
        parseInt(produitId),
        parseInt(quantite)
      );

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Erreur vérification disponibilité produit:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la vérification de disponibilité'
      });
    }
  }

  /**
   * POST /api/produit-ingredient/import/:complexeId
   * Import en masse des liens produits-ingrédients
   */
  static async importProduitsIngredients(request, response) {
    try {
      const { complexeId } = request.params;
      const { excelData } = request.body;

      if (!complexeId || !excelData) {
        return response.status(400).json({
          success: false,
          message: 'Complexe ID et données Excel sont requis'
        });
      }

      logger.info('Import produits-ingrédients', { complexeId, rowsCount: excelData.length });

      const result = await ProduitIngredientService.importProduitsIngredients(
        parseInt(complexeId),
        excelData
      );

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Erreur import produits-ingrédients:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de l\'import des liens produits-ingrédients'
      });
    }
  }

  /**
   * POST /api/produit-ingredient/calculate-cost/:produitId
   * Calculer le coût d'un produit
   */
  static async calculerCoutProduit(request, response) {
    try {
      const { produitId } = request.params;

      if (!produitId) {
        return response.status(400).json({
          success: false,
          message: 'Produit ID est requis'
        });
      }

      logger.info('Calcul coût produit', { produitId });

      const result = await ProduitIngredientService.calculerCoutProduit(parseInt(produitId));

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Erreur calcul coût produit:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors du calcul du coût du produit'
      });
    }
  }
}

module.exports = ProduitIngredientController;
