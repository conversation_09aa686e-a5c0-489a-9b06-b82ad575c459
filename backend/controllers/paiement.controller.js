const Controller = require('./Controller');
const PaiementService = require('../services/paiement.service');
const logger = require('../logger');

class PaiementController extends Controller {
    // Création d'un paiement
    static async creerPaiement(request, response) {
        try {
            const params = this.collectRequestParams(request);
            logger.info('Création paiement', { 
                params,
                utilisateur_id: request.user?.id
            });
            
            const result = await PaiementService.creerPaiement(params);
            this.sendResponse(response, result);
        } catch (error) {
            logger.error('Erreur création paiement', error);
            this.sendError(response, error);
        }
    }

    // Récupération des détails d'un paiement
    static async getPaiementById(request, response) {
        try {
            const { id } = request.params;
            logger.info('Consultation détails paiement', { 
                paiement_id: id,
                utilisateur_id: request.user?.id
            });
            
            const result = await PaiementService.getPaiementById(id);
            this.sendResponse(response, result);
        } catch (error) {
            logger.error('Erreur consultation détails paiement', error);
            this.sendError(response, error);
            }
    }

    // Mise à jour du statut d'un paiement
    static async updatePaiementStatus(request, response) {
        try {
            const { id } = request.params;
            const { statut } = request.body;
            
            logger.info('Mise à jour statut paiement', { 
                paiement_id: id,
                nouveau_statut: statut,
                utilisateur_id: request.user?.id
            });
            
            const result = await PaiementService.updatePaiementStatus(id, statut);
            this.sendResponse(response, result);
        } catch (error) {
            logger.error('Erreur mise à jour statut paiement', error);
            this.sendError(response, error);
        }
    }

    // Récupération des paiements d'une réservation
    static async getPaiementsReservation(request, response) {
        try {
            const { id } = request.params;
            logger.info('Consultation paiements réservation', { 
                reservation_id: id,
                utilisateur_id: request.user?.id
            });
            
            const result = await PaiementService.getPaiementsReservation(id);
            this.sendResponse(response, result);
        } catch (error) {
            logger.error('Erreur consultation paiements réservation', error);
            this.sendError(response, error);
        }
    }

    // Vérification du statut d'un paiement
    static async verifierStatutPaiement(request, response) {
        try {
            const { paiementId } = request.params;
            logger.info('Vérification statut paiement', { 
                paiement_id: paiementId,
                utilisateur_id: request.user?.id
            });
            
            const result = await PaiementService.verifierStatutPaiement(paiementId);
            this.sendResponse(response, result);
        } catch (error) {
            logger.error('Erreur vérification statut paiement', error);
            this.sendError(response, error);
        }
    }

    // Récupération de l'historique des paiements
    static async getHistoriquePaiements(request, response) {
        try {
            const { transactionId } = request.params;
            logger.info('Consultation historique paiements', { 
                transaction_id: transactionId,
                utilisateur_id: request.user?.id
            });
            
            const result = await PaiementService.getHistoriquePaiements(transactionId);
            this.sendResponse(response, result);
        } catch (error) {
            logger.error('Erreur consultation historique paiements', error);
            this.sendError(response, error);
        }
    }

    // Vérification du paiement complet
    static async verifierPaiementComplet(request, response) {
        try {
            const { transactionId } = request.params;
            logger.info('Vérification paiement complet', { 
                transaction_id: transactionId,
                utilisateur_id: request.user?.id
            });
            
            const result = await PaiementService.verifierPaiementComplet(transactionId);
            this.sendResponse(response, result);
        } catch (error) {
            logger.error('Erreur vérification paiement complet', error);
            this.sendError(response, error);
        }
    }
}

module.exports = PaiementController; 