const EmployeeTypeService = require('../services/employeeType.service');
const db = require('../db');
const logger = require('../logger');

/**
 * Contrôleur pour gérer les types d'employés
 * Phase 1 du plan de simplification des permissions
 */
class EmployeeTypeController {

  /**
   * Obtenir tous les types d'employés disponibles
   */
  static async getAvailableTypes(req, res) {
    try {
      const types = EmployeeTypeService.getAvailableTypes();
      res.json({
        success: true,
        data: types
      });
    } catch (error) {
      logger.error('Error getting available employee types:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des types d\'employés'
      });
    }
  }

  /**
   * Obtenir les informations d'un type d'employé spécifique
   */
  static async getTypeInfo(req, res) {
    try {
      const { type } = req.params;
      
      if (!EmployeeTypeService.isValidType(type)) {
        return res.status(400).json({
          success: false,
          message: `Type d'employé invalide: ${type}`
        });
      }

      const typeInfo = EmployeeTypeService.getTypeInfo(type);
      res.json({
        success: true,
        data: typeInfo
      });
    } catch (error) {
      logger.error('Error getting employee type info:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des informations du type'
      });
    }
  }

  /**
   * Obtenir les permissions pour un type d'employé
   */
  static async getTypePermissions(req, res) {
    try {
      const { type } = req.params;
      
      if (!EmployeeTypeService.isValidType(type)) {
        return res.status(400).json({
          success: false,
          message: `Type d'employé invalide: ${type}`
        });
      }

      const permissions = EmployeeTypeService.getPermissionsForType(type);
      const permissionDetails = permissions.map(perm => ({
        permission: perm,
        description: EmployeeTypeService.SIMPLIFIED_PERMISSIONS[perm] || 'Description non disponible'
      }));

      res.json({
        success: true,
        data: {
          type,
          permissions: permissionDetails
        }
      });
    } catch (error) {
      logger.error('Error getting employee type permissions:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des permissions'
      });
    }
  }

  /**
   * Changer le type d'un employé
   */
  static async changeEmployeeType(req, res) {
    try {
      const { id: employeeId } = req.params;
      const { type_employe, services_autorises } = req.body;

      // Validation du type
      if (!EmployeeTypeService.isValidType(type_employe)) {
        return res.status(400).json({
          success: false,
          message: `Type d'employé invalide: ${type_employe}`
        });
      }

      // Déterminer le complexe_id selon le type d'utilisateur
      let complexeId;
      if (req.user.role === 'super_admin' || req.user.role === 'admin_chaine') {
        complexeId = req.body.complexe_id;
        if (!complexeId) {
          return res.status(400).json({
            success: false,
            message: 'Complexe ID requis pour les super admins et admins de chaîne'
          });
        }
      } else if (req.user.role === 'admin_complexe') {
        complexeId = req.user.complexe_id;
      } else {
        return res.status(403).json({
          success: false,
          message: 'Non autorisé à modifier les types d\'employés'
        });
      }

      const updatedEmployee = await EmployeeTypeService.assignEmployeeType(
        employeeId,
        type_employe,
        complexeId,
        services_autorises
      );

      res.json({
        success: true,
        message: 'Type d\'employé modifié avec succès',
        data: updatedEmployee
      });
    } catch (error) {
      logger.error('Error changing employee type:', error);
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * Obtenir les employés par type dans un complexe
   */
  static async getEmployeesByType(req, res) {
    try {
      const { type } = req.query;

      // Déterminer le complexe_id selon le type d'utilisateur
      let complexeId;
      if (req.user.role === 'super_admin' || req.user.role === 'admin_chaine') {
        complexeId = req.query.complexe_id;
        if (!complexeId) {
          return res.status(400).json({
            success: false,
            message: 'Complexe ID requis pour les super admins et admins de chaîne'
          });
        }
      } else if (req.user.role === 'admin_complexe') {
        complexeId = req.user.complexe_id;
      } else {
        return res.status(403).json({
          success: false,
          message: 'Non autorisé à consulter les employés'
        });
      }

      // Validation du type si fourni
      if (type && !EmployeeTypeService.isValidType(type)) {
        return res.status(400).json({
          success: false,
          message: `Type d'employé invalide: ${type}`
        });
      }

      const employees = await EmployeeTypeService.getEmployeesByType(complexeId, type);

      res.json({
        success: true,
        data: employees
      });
    } catch (error) {
      logger.error('Error getting employees by type:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des employés'
      });
    }
  }

  /**
   * Obtenir les statistiques des types d'employés
   */
  static async getEmployeeTypeStats(req, res) {
    try {
      // Déterminer le complexe_id selon le type d'utilisateur
      let complexeId;
      if (req.user.role === 'super_admin' || req.user.role === 'admin_chaine') {
        complexeId = req.query.complexe_id;
        if (!complexeId) {
          return res.status(400).json({
            success: false,
            message: 'Complexe ID requis pour les super admins et admins de chaîne'
          });
        }
      } else if (req.user.role === 'admin_complexe') {
        complexeId = req.user.complexe_id;
      } else {
        return res.status(403).json({
          success: false,
          message: 'Non autorisé à consulter les statistiques'
        });
      }

      const stats = await EmployeeTypeService.getEmployeeTypeStats(complexeId);

      // Enrichir les stats avec les informations des types
      const enrichedStats = stats.map(stat => {
        const typeInfo = stat.type_employe 
          ? EmployeeTypeService.getTypeInfo(stat.type_employe)
          : { name: 'Non défini', description: 'Type non défini' };
        
        return {
          ...stat,
          type_info: typeInfo
        };
      });

      res.json({
        success: true,
        data: enrichedStats
      });
    } catch (error) {
      logger.error('Error getting employee type stats:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des statistiques'
      });
    }
  }

  /**
   * Valider l'accès d'un employé à un service
   */
  static async validateServiceAccess(req, res) {
    try {
      const { employeeId, serviceType } = req.params;

      // Récupérer l'employé
      const employeeResult = await db.query(
        'SELECT * FROM "Employes" WHERE employe_id = $1',
        [employeeId]
      );

      if (employeeResult.rows.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Employé non trouvé'
        });
      }

      const employee = employeeResult.rows[0];
      const hasAccess = EmployeeTypeService.validateServiceAccess(employee, serviceType);

      res.json({
        success: true,
        data: {
          employee_id: employeeId,
          service_type: serviceType,
          has_access: hasAccess,
          services_autorises: employee.services_autorises
        }
      });
    } catch (error) {
      logger.error('Error validating service access:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la validation de l\'accès'
      });
    }
  }

  /**
   * Obtenir les services par défaut pour un type d'employé
   */
  static async getDefaultServicesForType(req, res) {
    try {
      const { type } = req.params;
      
      if (!EmployeeTypeService.isValidType(type)) {
        return res.status(400).json({
          success: false,
          message: `Type d'employé invalide: ${type}`
        });
      }

      const defaultServices = EmployeeTypeService.getDefaultServicesForType(type);

      res.json({
        success: true,
        data: {
          type,
          default_services: defaultServices
        }
      });
    } catch (error) {
      logger.error('Error getting default services for type:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des services par défaut'
      });
    }
  }
}

module.exports = EmployeeTypeController;
