const Controller = require('./Controller');
const NotificationService = require('../services/notification.service');
const logger = require('../logger');

class NotificationController extends Controller {
  // Récupération de la liste des notifications
  static async getNotifications(request, response) {
    try {
      const params = this.collectRequestParams(request);
      logger.info('Consultation notifications', { 
        filtres: params,
        utilisateur_id: request.user.id
      });
      
      const result = await NotificationService.getNotifications(params);
      this.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur consultation notifications', error);
      this.sendError(response, error);
    }
  }

  // Création d'une notification
  static async createNotification(request, response) {
    try {
      const params = this.collectRequestParams(request);
      logger.info('Création notification', { 
        params,
        utilisateur_id: request.user.id
      });
      
      const result = await NotificationService.createNotification({
        ...params,
        emetteur_id: request.user.id
      });
      this.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur création notification', error);
      this.sendError(response, error);
    }
  }

  // Marquer une notification comme lue
  static async markAsRead(request, response) {
    try {
      const { id } = request.params;
      logger.info('Marquage notification comme lue', { 
        notification_id: id,
        utilisateur_id: request.user.id
      });
      
      const result = await NotificationService.markAsRead(id);
      this.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur marquage notification comme lue', error);
      this.sendError(response, error);
    }
  }

  // Récupération des notifications non lues
  static async getUnreadNotifications(request, response) {
    try {
      const params = this.collectRequestParams(request);
      logger.info('Consultation notifications non lues', { 
        filtres: params,
        utilisateur_id: request.user.id
      });
      
      const result = await NotificationService.getUnreadNotifications({
        ...params,
        destinataire_id: request.user.id
      });
      this.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur consultation notifications non lues', error);
      this.sendError(response, error);
    }
  }
}

module.exports = NotificationController; 