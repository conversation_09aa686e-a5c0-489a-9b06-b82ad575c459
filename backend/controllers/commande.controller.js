const Controller = require('./Controller');
const CommandeService = require('../services/commande.service');
const logger = require('../logger');

/**
 * Contrôleur pour la gestion des commandes de restaurant/bar
 */
class CommandeController extends Controller {

  /**
   * POST /api/commandes
   * Créer une nouvelle commande
   */
  static async createCommande(request, response) {
    try {
      const { complexe_id, id: employe_id } = request.user;
      const commandeData = {
        ...request.body,
        complexe_id,
        employe_id
      };

      // Validation des champs requis
      const requiredFields = ['service_id'];
      for (const field of requiredFields) {
        if (!commandeData[field]) {
          return response.status(400).json({
            success: false,
            message: `Le champ ${field} est requis`
          });
        }
      }

      logger.info('Création nouvelle commande', { commandeData });

      const result = await CommandeService.createCommande(commandeData);
      
      if (!result.success) {
        return response.status(result.code || 500).json(result);
      }

      response.status(201).json(result);
    } catch (error) {
      logger.error('Erreur création commande:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la création de la commande'
      });
    }
  }

  /**
   * GET /api/commandes/:id
   * Récupérer une commande par ID
   */
  static async getCommande(request, response) {
    try {
      const { id } = request.params;

      if (!id) {
        return response.status(400).json({
          success: false,
          message: 'ID de la commande requis'
        });
      }

      logger.info('Récupération commande', { commandeId: id });

      // Cette méthode n'existe pas encore dans le service, on peut l'ajouter
      const result = await CommandeService.getCommandeById(parseInt(id));
      
      response.json(result);
    } catch (error) {
      logger.error('Erreur récupération commande:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération de la commande'
      });
    }
  }

  /**
   * PUT /api/commandes/:id
   * Mettre à jour une commande
   */
  static async updateCommande(request, response) {
    try {
      const { id } = request.params;
      const updateData = request.body;

      if (!id) {
        return response.status(400).json({
          success: false,
          message: 'ID de la commande requis'
        });
      }

      logger.info('Mise à jour commande', { commandeId: id, updateData });

      // Pour l'instant, on gère principalement les changements de statut
      if (updateData.statut) {
        const result = await CommandeService.updateCommandeStatus(parseInt(id), updateData.statut);
        return response.json(result);
      }

      response.status(400).json({
        success: false,
        message: 'Aucune mise à jour valide fournie'
      });
    } catch (error) {
      logger.error('Erreur mise à jour commande:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la mise à jour de la commande'
      });
    }
  }

  /**
   * DELETE /api/commandes/:id
   * Annuler une commande
   */
  static async deleteCommande(request, response) {
    try {
      const { id } = request.params;

      if (!id) {
        return response.status(400).json({
          success: false,
          message: 'ID de la commande requis'
        });
      }

      logger.info('Annulation commande', { commandeId: id });

      const result = await CommandeService.cancelCommandeWithStockRestore(parseInt(id));
      
      response.json(result);
    } catch (error) {
      logger.error('Erreur annulation commande:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de l\'annulation de la commande'
      });
    }
  }

  /**
   * POST /api/commandes/:id/items
   * Ajouter un item à une commande
   */
  static async addItemToCommande(request, response) {
    try {
      const { id } = request.params;
      const { complexe_id } = request.user;
      const itemData = {
        ...request.body,
        complexe_id
      };

      if (!id) {
        return response.status(400).json({
          success: false,
          message: 'ID de la commande requis'
        });
      }

      // Validation des champs requis
      const requiredFields = ['service_id', 'produit_id', 'quantite', 'prix_unitaire'];
      for (const field of requiredFields) {
        if (!itemData[field]) {
          return response.status(400).json({
            success: false,
            message: `Le champ ${field} est requis`
          });
        }
      }

      if (itemData.quantite <= 0) {
        return response.status(400).json({
          success: false,
          message: 'La quantité doit être supérieure à 0'
        });
      }

      if (itemData.prix_unitaire < 0) {
        return response.status(400).json({
          success: false,
          message: 'Le prix unitaire ne peut pas être négatif'
        });
      }

      logger.info('Ajout item à commande', { commandeId: id, itemData });

      const result = await CommandeService.addItemToCommande(parseInt(id), itemData);
      
      if (!result.success) {
        return response.status(result.code || 500).json(result);
      }

      response.status(201).json(result);
    } catch (error) {
      logger.error('Erreur ajout item commande:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de l\'ajout de l\'item'
      });
    }
  }

  /**
   * PUT /api/commandes/:id/items/:itemId
   * Mettre à jour la quantité d'un item
   */
  static async updateItemQuantity(request, response) {
    try {
      const { id, itemId } = request.params;
      const { quantite } = request.body;

      if (!id || !itemId) {
        return response.status(400).json({
          success: false,
          message: 'ID de la commande et de l\'item requis'
        });
      }

      if (!quantite || quantite <= 0) {
        return response.status(400).json({
          success: false,
          message: 'Quantité valide requise (> 0)'
        });
      }

      logger.info('Mise à jour quantité item', { commandeId: id, itemId, quantite });

      const result = await CommandeService.updateItemQuantity(
        parseInt(id), 
        parseInt(itemId), 
        parseInt(quantite)
      );
      
      response.json(result);
    } catch (error) {
      logger.error('Erreur mise à jour quantité item:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la mise à jour de la quantité'
      });
    }
  }

  /**
   * DELETE /api/commandes/:id/items/:itemId
   * Supprimer un item d'une commande
   */
  static async removeItemFromCommande(request, response) {
    try {
      const { id, itemId } = request.params;

      if (!id || !itemId) {
        return response.status(400).json({
          success: false,
          message: 'ID de la commande et de l\'item requis'
        });
      }

      logger.info('Suppression item commande', { commandeId: id, itemId });

      const result = await CommandeService.removeItemFromCommande(parseInt(id), parseInt(itemId));

      response.json(result);
    } catch (error) {
      logger.error('Erreur suppression item commande:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la suppression de l\'item'
      });
    }
  }

  /**
   * PUT /api/commandes/:id/status
   * Mettre à jour le statut d'une commande
   */
  static async updateCommandeStatus(request, response) {
    try {
      const { id } = request.params;
      const { statut } = request.body;

      if (!id || !statut) {
        return response.status(400).json({
          success: false,
          message: 'ID de la commande et statut requis'
        });
      }

      logger.info('Mise à jour statut commande', { commandeId: id, statut });

      const result = await CommandeService.updateCommandeStatus(parseInt(id), statut);

      response.json(result);
    } catch (error) {
      logger.error('Erreur mise à jour statut commande:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la mise à jour du statut'
      });
    }
  }

  /**
   * GET /api/commandes/service/:serviceId
   * Récupérer les commandes d'un service
   */
  static async getCommandesByService(request, response) {
    try {
      const { serviceId } = request.params;
      const { statut } = request.query;

      if (!serviceId) {
        return response.status(400).json({
          success: false,
          message: 'ID du service requis'
        });
      }

      logger.info('Récupération commandes par service', { serviceId, statut });

      const result = await CommandeService.getCommandesByService(parseInt(serviceId), statut);

      response.json(result);
    } catch (error) {
      logger.error('Erreur récupération commandes par service:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des commandes'
      });
    }
  }

  /**
   * GET /api/commandes/table/:tableId
   * Récupérer les commandes d'une table
   */
  static async getCommandesByTable(request, response) {
    try {
      const { tableId } = request.params;

      if (!tableId) {
        return response.status(400).json({
          success: false,
          message: 'ID de la table requis'
        });
      }

      logger.info('Récupération commandes par table', { tableId });

      const result = await CommandeService.getCommandesByTable(parseInt(tableId));

      response.json(result);
    } catch (error) {
      logger.error('Erreur récupération commandes par table:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des commandes'
      });
    }
  }

  /**
   * POST /api/commandes/:id/payment
   * Traiter le paiement d'une commande
   */
  static async processPayment(request, response) {
    try {
      const { id } = request.params;
      const { mode_paiement, montant_paye } = request.body;

      if (!id) {
        return response.status(400).json({
          success: false,
          message: 'ID de la commande requis'
        });
      }

      if (!mode_paiement) {
        return response.status(400).json({
          success: false,
          message: 'Mode de paiement requis'
        });
      }

      logger.info('Traitement paiement commande', { commandeId: id, mode_paiement, montant_paye });

      // Cette méthode devra être ajoutée au service
      const result = await CommandeService.processPayment(parseInt(id), {
        mode_paiement,
        montant_paye
      });

      response.json(result);
    } catch (error) {
      logger.error('Erreur traitement paiement commande:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors du traitement du paiement'
      });
    }
  }

  /**
   * POST /api/commandes/:id/receipt
   * Générer et imprimer le ticket de caisse
   */
  static async printReceipt(request, response) {
    try {
      const { id } = request.params;

      if (!id) {
        return response.status(400).json({
          success: false,
          message: 'ID de la commande requis'
        });
      }

      logger.info('Génération ticket de caisse', { commandeId: id });

      // Cette méthode devra être ajoutée au service
      const result = await CommandeService.generateReceipt(parseInt(id));

      response.json(result);
    } catch (error) {
      logger.error('Erreur génération ticket de caisse:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la génération du ticket'
      });
    }
  }
}

module.exports = CommandeController;
