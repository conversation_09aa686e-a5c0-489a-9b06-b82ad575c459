const RoleService = require('../services/role.service');
const PermissionService = require('../services/permission.service');
const EmployeeTypeService = require('../services/employeeType.service');
const logger = require('../logger');

class RoleController {
  static async createRole(req, res) {
    try {
      // Debug: log user information
      logger.info('User creating role:', {
        user: req.user,
        body: req.body
      });

      let complexeId;

      // Déterminer le complexe_id selon le type d'utilisateur
      if (req.user.role === 'super_admin' || req.user.role === 'admin_chaine') {
        // Super admin et admin chaine peuvent créer des rôles pour n'importe quel complexe
        // Le complexe_id doit être fourni dans le body de la requête
        complexeId = req.body.complexe_id;

        if (!complexeId) {
          return res.status(400).json({
            message: 'Complexe ID requis dans le body de la requête pour les super admins et admins de chaîne.'
          });
        }
      } else if (req.user.role === 'admin_complexe') {
        // Admin complexe ne peut créer des rôles que pour son complexe
        complexeId = req.user.complexe_id;
        if (!complexeId) {
          return res.status(400).json({
            success: false,
            message: 'Complexe ID manquant dans le token utilisateur'
          });
        }
      } else {
        return res.status(403).json({
          success: false,
          message: 'Seuls les administrateurs peuvent créer des rôles'
        });
      }

      // Valider les permissions selon le nouveau système
      if (req.body.permissions) {
        const validation = RoleService.validateRolePermissions(req.body.permissions);
        if (!validation.isValid) {
          return res.status(400).json({
            success: false,
            message: 'Permissions invalides',
            invalid_permissions: validation.invalidPermissions
          });
        }
      }

      const roleData = {
        ...req.body,
        complexe_id: complexeId
      };

      const role = await RoleService.createRole(roleData);
      res.status(201).json({
        success: true,
        data: role
      });
    } catch (error) {
      logger.error('Error in createRole controller:', error);
      res.status(400).json({ message: error.message });
    }
  }

  static async getRole(req, res) {
    try {
      const role = await RoleService.getRoleById(req.params.id);
      
      if (!role) {
        return res.status(404).json({ message: 'Rôle non trouvé' });
      }

      // Vérifier si le rôle appartient au même complexe
      if (role.complexe_id !== req.user.complexe_id) {
        return res.status(403).json({ message: 'Accès non autorisé' });
      }

      res.json(role);
    } catch (error) {
      logger.error('Error in getRole controller:', error);
      res.status(500).json({ message: 'Erreur lors de la récupération du rôle' });
    }
  }

  static async getRoles(req, res) {
    try {
      let complexeId;

      // Déterminer le complexe_id selon le type d'utilisateur
      if (req.user.role === 'super_admin' || req.user.role === 'admin_chaine') {
        // Super admin et admin chaine peuvent voir les rôles de n'importe quel complexe
        // Le complexe_id peut être fourni en query parameter
        complexeId = req.query.complexe_id;

        if (!complexeId) {
          return res.status(400).json({
            message: 'Complexe ID requis en query parameter pour les super admins et admins de chaîne.'
          });
        }
      } else if (req.user.role === 'admin_complexe' || req.user.role === 'employe') {
        // Admin complexe et employés ne peuvent voir que les rôles de leur complexe
        complexeId = req.user.complexe_id;

        if (!complexeId) {
          return res.status(400).json({
            message: 'Complexe ID manquant dans le token utilisateur.'
          });
        }
      } else {
        return res.status(403).json({
          message: 'Type d\'utilisateur non autorisé à consulter les rôles.'
        });
      }

      const roles = await RoleService.getRolesByComplexe(complexeId);
      res.json(roles);
    } catch (error) {
      logger.error('Error in getRoles controller:', error);
      res.status(500).json({ message: 'Erreur lors de la récupération des rôles' });
    }
  }

  static async updateRole(req, res) {
    try {
      const role = await RoleService.getRoleById(req.params.id);

      if (!role) {
        return res.status(404).json({ message: 'Rôle non trouvé' });
      }

      // Vérifier les permissions selon le type d'utilisateur
      if (req.user.role === 'super_admin') {
        // Super admin peut modifier n'importe quel rôle
      } else if (req.user.role === 'admin_chaine') {
        // Admin chaine peut modifier des rôles de n'importe quel complexe
      } else if (req.user.role === 'admin_complexe' || req.user.role === 'employe') {
        // Admin complexe et employés ne peuvent modifier que les rôles de leur complexe
        if (!req.user.complexe_id) {
          return res.status(400).json({ message: 'Complexe ID manquant dans le token utilisateur' });
        }

        if (role.complexe_id !== req.user.complexe_id) {
          return res.status(403).json({ message: 'Accès non autorisé - vous ne pouvez modifier que les rôles de votre complexe' });
        }
      } else {
        return res.status(403).json({ message: 'Type d\'utilisateur non autorisé à modifier des rôles' });
      }

      const updatedRole = await RoleService.updateRole(req.params.id, req.body);
      res.json(updatedRole);
    } catch (error) {
      logger.error('Error in updateRole controller:', error);
      res.status(400).json({ message: error.message });
    }
  }

  static async deleteRole(req, res) {
    try {
      const role = await RoleService.getRoleById(req.params.id);

      if (!role) {
        return res.status(404).json({ message: 'Rôle non trouvé' });
      }

      // Vérifier les permissions selon le type d'utilisateur
      if (req.user.role === 'super_admin') {
        // Super admin peut supprimer n'importe quel rôle
      } else if (req.user.role === 'admin_chaine') {
        // Admin chaine peut supprimer des rôles de n'importe quel complexe
        // Mais on peut ajouter une vérification si nécessaire
      } else if (req.user.role === 'admin_complexe' || req.user.role === 'employe') {
        // Admin complexe et employés ne peuvent supprimer que les rôles de leur complexe
        if (!req.user.complexe_id) {
          return res.status(400).json({ message: 'Complexe ID manquant dans le token utilisateur' });
        }

        if (role.complexe_id !== req.user.complexe_id) {
          return res.status(403).json({ message: 'Accès non autorisé - vous ne pouvez supprimer que les rôles de votre complexe' });
        }
      } else {
        return res.status(403).json({ message: 'Type d\'utilisateur non autorisé à supprimer des rôles' });
      }

      await RoleService.deleteRole(req.params.id);
      res.json({ message: 'Rôle supprimé avec succès' });
    } catch (error) {
      logger.error('Error in deleteRole controller:', error);
      res.status(400).json({ message: error.message });
    }
  }

  static async assignRoleToEmployee(req, res) {
    try {
      const { employeeId, roleId } = req.body;

      if (!employeeId || !roleId) {
        return res.status(400).json({
          message: 'L\'ID de l\'employé et l\'ID du rôle sont requis',
        });
      }

      const role = await RoleService.getRoleById(roleId);

      if (!role) {
        return res.status(404).json({ message: 'Rôle non trouvé' });
      }

      // Vérifier les permissions selon le type d'utilisateur
      if (req.user.role === 'super_admin') {
        // Super admin peut assigner n'importe quel rôle
      } else if (req.user.role === 'admin_chaine') {
        // Admin chaine peut assigner des rôles de n'importe quel complexe
      } else if (req.user.role === 'admin_complexe' || req.user.role === 'employe') {
        // Admin complexe et employés ne peuvent assigner que les rôles de leur complexe
        if (!req.user.complexe_id) {
          return res.status(400).json({ message: 'Complexe ID manquant dans le token utilisateur' });
        }

        if (role.complexe_id !== req.user.complexe_id) {
          return res.status(403).json({ message: 'Accès non autorisé - vous ne pouvez assigner que les rôles de votre complexe' });
        }
      } else {
        return res.status(403).json({ message: 'Type d\'utilisateur non autorisé à assigner des rôles' });
      }

      const updatedEmployee = await RoleService.assignRoleToEmployee(employeeId, roleId);
      res.json(updatedEmployee);
    } catch (error) {
      logger.error('Error in assignRoleToEmployee controller:', error);
      res.status(400).json({ message: error.message });
    }
  }

  static async getEmployeePermissions(req, res) {
    try {
      const permissions = await RoleService.getEmployeePermissions(req.params.employeeId);
      res.json({
        success: true,
        data: { permissions }
      });
    } catch (error) {
      logger.error('Error in getEmployeePermissions controller:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des permissions'
      });
    }
  }

  /**
   * Créer les rôles prédéfinis pour un complexe
   * Système simplifié - Phase 5
   */
  static async createPredefinedRoles(req, res) {
    try {
      // Seuls les admins peuvent créer des rôles prédéfinis
      if (!['super_admin', 'admin_chaine', 'admin_complexe'].includes(req.user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Seuls les administrateurs peuvent créer des rôles prédéfinis'
        });
      }

      let complexeId;
      if (req.user.role === 'super_admin' || req.user.role === 'admin_chaine') {
        complexeId = req.body.complexe_id || req.query.complexe_id;
        if (!complexeId) {
          return res.status(400).json({
            success: false,
            message: 'Complexe ID requis'
          });
        }
      } else {
        complexeId = req.user.complexe_id;
      }

      const createdRoles = await RoleService.createPredefinedRolesForComplex(complexeId);

      res.json({
        success: true,
        message: `${createdRoles.length} rôles prédéfinis créés`,
        data: createdRoles
      });
    } catch (error) {
      logger.error('Error creating predefined roles:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la création des rôles prédéfinis'
      });
    }
  }

  /**
   * Obtenir le rôle prédéfini pour un type d'employé
   */
  static async getPredefinedRoleForType(req, res) {
    try {
      const { type } = req.params;

      if (!EmployeeTypeService.isValidType(type)) {
        return res.status(400).json({
          success: false,
          message: `Type d'employé invalide: ${type}`
        });
      }

      let complexeId;
      if (req.user.role === 'super_admin' || req.user.role === 'admin_chaine') {
        complexeId = req.query.complexe_id;
        if (!complexeId) {
          return res.status(400).json({
            success: false,
            message: 'Complexe ID requis'
          });
        }
      } else {
        complexeId = req.user.complexe_id;
      }

      const role = await RoleService.getPredefinedRoleForEmployeeType(complexeId, type);

      res.json({
        success: true,
        data: role
      });
    } catch (error) {
      logger.error('Error getting predefined role for type:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération du rôle prédéfini'
      });
    }
  }

  /**
   * Valider les permissions d'un rôle
   */
  static async validatePermissions(req, res) {
    try {
      const { permissions } = req.body;

      if (!permissions || !Array.isArray(permissions)) {
        return res.status(400).json({
          success: false,
          message: 'Liste de permissions requise'
        });
      }

      const validation = RoleService.validateRolePermissions(permissions);

      res.json({
        success: true,
        data: validation
      });
    } catch (error) {
      logger.error('Error validating permissions:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la validation des permissions'
      });
    }
  }
}

module.exports = RoleController;