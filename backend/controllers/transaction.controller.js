const TransactionService = require('../services/transaction.service');
const logger = require('../logger');

class TransactionController {
  // Créer une nouvelle transaction
  static async createTransaction(req, res) {
    try {
      const result = await TransactionService.createTransaction(req.body);
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }
      return res.status(201).json(result);
    } catch (error) {
      logger.error('Erreur contrôleur création transaction:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la création de la transaction'
      });
    }
  }

  // Récupérer une transaction par ID
  static async getTransactionById(req, res) {
    try {
      const { transactionId } = req.params;
      const result = await TransactionService.getTransactionById(transactionId);
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }
      return res.status(200).json(result);
    } catch (error) {
      logger.error('Erreur contrôleur récupération transaction:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de la transaction'
      });
    }
  }

  // Mettre à jour le statut d'une transaction
  static async updateTransactionStatus(req, res) {
    try {
      const { transactionId } = req.params;
      const { statut } = req.body;
      const result = await TransactionService.updateTransactionStatus(transactionId, statut);
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }
      return res.status(200).json(result);
    } catch (error) {
      logger.error('Erreur contrôleur mise à jour statut transaction:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour du statut de la transaction'
      });
    }
  }

  // Récupérer les transactions d'une session
  static async getTransactionsBySession(req, res) {
    try {
      const { sessionId } = req.params;
      const result = await TransactionService.getTransactionsBySession(sessionId);
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }
      return res.status(200).json(result);
    } catch (error) {
      logger.error('Erreur contrôleur récupération transactions session:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des transactions de la session'
      });
    }
  }

  // Récupérer les transactions d'un client
  static async getTransactionsByClient(req, res) {
    try {
      const { clientId } = req.params;
      const result = await TransactionService.getTransactionsByClient(clientId);
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }
      return res.status(200).json(result);
    } catch (error) {
      logger.error('Erreur contrôleur récupération transactions client:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des transactions du client'
      });
    }
  }

  // Vérifier le solde d'une transaction
  static async verifierSoldeTransaction(req, res) {
    try {
      const { transactionId } = req.params;
      const result = await TransactionService.verifierSoldeTransaction(transactionId);
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }
      return res.status(200).json(result);
    } catch (error) {
      logger.error('Erreur contrôleur vérification solde transaction:', error);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification du solde de la transaction'
      });
    }
  }
}

module.exports = TransactionController; 