const Controller = require('./Controller');
const DisponibiliteService = require('../services/disponibilite.service');
const logger = require('../logger');

class DisponibiliteController extends Controller {
  // Récupération de la liste des disponibilités
  static async getDisponibilites(request, response) {
    try {
      const params = {
        date_debut: request.query.date_debut,
        date_fin: request.query.date_fin,
        chambre_id: request.query.chambre_id
      };
      logger.info('Consultation liste des disponibilités', { params });
      
      const result = await DisponibiliteService.getDisponibilites(params);
      response.json(result);
    } catch (error) {
      logger.error('Erreur consultation liste des disponibilités', error);
      response.status(error.code || 500).json({ 
        success: false,
        message: error.message || 'Erreur lors de la consultation des disponibilités'
      });
    }
  }

  // Vérification des disponibilités
  static async verifierDisponibilite(request, response) {
    try {
      const params = {
        date_debut: request.query.date_debut,
        date_fin: request.query.date_fin,
        type_chambre: request.query.type_chambre,
        heure_debut: request.query.heure_debut,
        heure_fin: request.query.heure_fin
      };
      logger.info('Vérification disponibilités', { params });
      
      const result = await DisponibiliteService.verifierDisponibilite(params);
      response.json(result);
    } catch (error) {
      logger.error('Erreur vérification disponibilités', error);
      response.status(error.code || 500).json({ 
        success: false,
        message: error.message || 'Erreur lors de la vérification des disponibilités'
      });
    }
  }

  // Création d'une disponibilité
  static async createDisponibilite(request, response) {
    try {
      const params = {
        ...request.body,
        chambre_id: request.body.chambre_id,
        date: request.body.date,
        heure_debut: request.body.heure_debut,
        heure_fin: request.body.heure_fin,
        statut: request.body.statut,
        type_occupation: request.body.type_occupation
      };
      logger.info('Création disponibilité', { params });
      
      const result = await DisponibiliteService.createDisponibilite(params);
      response.json(result);
    } catch (error) {
      logger.error('Erreur création disponibilité', error);
      response.status(error.code || 500).json({ 
        success: false,
        message: error.message || 'Erreur lors de la création de la disponibilité'
      });
    }
  }

  // Mise à jour d'une disponibilité
  static async updateDisponibilite(request, response) {
    try {
      const { id } = request.params;
      const params = {
        ...request.body,
        chambre_id: request.body.chambre_id,
        date: request.body.date,
        heure_debut: request.body.heure_debut,
        heure_fin: request.body.heure_fin,
        statut: request.body.statut,
        type_occupation: request.body.type_occupation
      };
      logger.info('Mise à jour disponibilité', { id, params });
      
      const result = await DisponibiliteService.updateDisponibilite(id, params);
      response.json(result);
    } catch (error) {
      logger.error('Erreur mise à jour disponibilité', error);
      response.status(error.code || 500).json({ 
        success: false,
        message: error.message || 'Erreur lors de la mise à jour de la disponibilité'
      });
    }
  }
}

module.exports = DisponibiliteController; 