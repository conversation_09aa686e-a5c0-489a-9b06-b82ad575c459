const Controller = require('./Controller');
const StatistiqueService = require('../services/statistique.service');
const logger = require('../logger');

class StatistiqueController extends Controller {
  // Taux d'occupation
  static async getTauxOccupation(request, response) {
    try {
      const params = this.collectRequestParams(request);
      logger.info('Consultation taux occupation', { 
        filtres: params,
        utilisateur_id: request.user?.id
      });
      
      const result = await StatistiqueService.getTauxOccupation(params);
      this.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur consultation taux occupation', error);
      this.sendError(response, error);
    }
  }

  // Revenus
  static async getRevenus(request, response) {
    try {
      const params = this.collectRequestParams(request);
      logger.info('Consultation revenus', { 
        filtres: params,
        utilisateur_id: request.user?.id
      });
      
      const result = await StatistiqueService.getRevenus(params);
      this.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur consultation revenus', error);
      this.sendError(response, error);
    }
  }

  // Chambres les plus populaires
  static async getChambresPopulaires(request, response) {
    try {
      const params = this.collectRequestParams(request);
      logger.info('Consultation chambres populaires', { 
        filtres: params,
        utilisateur_id: request.user?.id
      });
      
      const result = await StatistiqueService.getChambresPopulaires(params);
      this.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur consultation chambres populaires', error);
      this.sendError(response, error);
    }
  }

  // Génération de rapports
  static async genererRapport(request, response) {
    try {
      const params = this.collectRequestParams(request);
      logger.info('Génération rapport', { 
        type: params.type_rapport,
        periode: {
          date_debut: params.date_debut,
          date_fin: params.date_fin
        },
        format: params.format,
        utilisateur_id: request.user?.id
      });
      
      const result = await StatistiqueService.genererRapport(params);
      this.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur génération rapport', error);
      this.sendError(response, error);
    }
  }
}

module.exports = StatistiqueController; 