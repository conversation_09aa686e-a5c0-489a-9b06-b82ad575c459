const ServiceComplexeService = require('../services/service.service');
const logger = require('../logger');

// Types de services autorisés
const ALLOWED_SERVICE_TYPES = ['Restaurant', 'Bar', 'Piscine'];

class ServiceController {
  // Récupérer tous les services d'un complexe
  static async getAllServices(request, response) {
    try {
      const { complexeId } = request.query;
      
      if (!complexeId) {
        return response.status(400).json({
          success: false,
          message: 'L\'ID du complexe est requis'
        });
      }

      logger.info('Consultation liste des services', { complexeId });
      
      const result = await ServiceComplexeService.getAllServices(complexeId);
      response.json({
        success: true,
        data: result
      });
    } catch (error) {
      logger.error('Erreur consultation liste des services', error);
      response.status(error.code || 500).json({ 
        success: false,
        message: error.message || 'Erreur lors de la consultation de la liste des services'
      });
    }
  }

  // Récupérer un service par son ID
  static async getServiceById(request, response) {
    try {
      const { id } = request.params;
      logger.info('Consultation détails service', { serviceId: id });

      const result = await ServiceComplexeService.getServiceById(id);
      response.json({
        success: true,
        data: result
      });
    } catch (error) {
      logger.error('Erreur consultation détails service', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la consultation des détails du service'
      });
    }
  }

  // Créer un nouveau service
  static async createService(request, response) {
    try {
      const serviceData = request.body;

      // Validation du type de service
      if (!serviceData.type_service || !ALLOWED_SERVICE_TYPES.includes(serviceData.type_service)) {
        return response.status(400).json({
          success: false,
          message: `Type de service invalide. Types autorisés: ${ALLOWED_SERVICE_TYPES.join(', ')}`
        });
      }

      logger.info('Création nouveau service', { serviceData });

      const result = await ServiceComplexeService.createService(serviceData);
      response.status(201).json({
        success: true,
        data: result,
        message: 'Service créé avec succès'
      });
    } catch (error) {
      logger.error('Erreur création service', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la création du service'
      });
    }
  }

  // Mettre à jour un service
  static async updateService(request, response) {
    try {
      const { id } = request.params;
      const serviceData = request.body;

      // Validation du type de service si fourni
      if (serviceData.type_service && !ALLOWED_SERVICE_TYPES.includes(serviceData.type_service)) {
        return response.status(400).json({
          success: false,
          message: `Type de service invalide. Types autorisés: ${ALLOWED_SERVICE_TYPES.join(', ')}`
        });
      }

      logger.info('Mise à jour service', { serviceId: id, serviceData });

      const result = await ServiceComplexeService.updateService(id, serviceData);
      response.json({
        success: true,
        data: result,
        message: 'Service mis à jour avec succès'
      });
    } catch (error) {
      logger.error('Erreur mise à jour service', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la mise à jour du service'
      });
    }
  }

  // Supprimer un service
  static async deleteService(request, response) {
    try {
      const { id } = request.params;
      logger.info('Suppression service', { serviceId: id });
      
      const result = await ServiceComplexeService.deleteService(id);
      response.json({
        success: true,
        message: 'Service supprimé avec succès'
      });
    } catch (error) {
      logger.error('Erreur suppression service', error);
      response.status(error.code || 500).json({ 
        success: false,
        message: error.message || 'Erreur lors de la suppression du service'
      });
    }
  }

  // Récupérer les services par type
  static async getServicesByType(request, response) {
    try {
      const { type } = request.params;
      const { complexeId } = request.query;

      if (!complexeId) {
        return response.status(400).json({
          success: false,
          message: 'L\'ID du complexe est requis'
        });
      }

      // Validation du type de service
      if (!ALLOWED_SERVICE_TYPES.includes(type)) {
        return response.status(400).json({
          success: false,
          message: `Type de service invalide. Types autorisés: ${ALLOWED_SERVICE_TYPES.join(', ')}`
        });
      }

      logger.info('Consultation services par type', { type, complexeId });

      const result = await ServiceComplexeService.getServicesByType(complexeId, type);
      response.json({
        success: true,
        data: result
      });
    } catch (error) {
      logger.error('Erreur consultation services par type', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la consultation des services par type'
      });
    }
  }

  // ==================== GESTION DES TARIFICATIONS ====================

  /**
   * Récupérer la tarification d'un service
   */
  static async getTarificationByService(request, response) {
    try {
      const { id } = request.params;
      logger.info('Consultation tarification service', { serviceId: id });

      const result = await ServiceComplexeService.getTarificationByService(id);
      response.json({
        success: true,
        data: result
      });
    } catch (error) {
      logger.error('Erreur consultation tarification service', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la consultation de la tarification du service'
      });
    }
  }

  /**
   * Mettre à jour la tarification d'un service
   */
  static async updateTarification(request, response) {
    try {
      const { id } = request.params;
      const tarificationData = request.body;

      logger.info('Mise à jour tarification service', { serviceId: id, tarificationData });

      const result = await ServiceComplexeService.updateTarification(id, tarificationData);
      response.json({
        success: true,
        data: result,
        message: 'Tarification mise à jour avec succès'
      });
    } catch (error) {
      logger.error('Erreur mise à jour tarification service', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la mise à jour de la tarification'
      });
    }
  }

  /**
   * Obtenir un modèle de tarification par défaut selon le type de service
   */
  static async getTarificationTemplate(request, response) {
    try {
      const { type } = request.params;

      // Validation du type de service
      if (!ALLOWED_SERVICE_TYPES.includes(type)) {
        return response.status(400).json({
          success: false,
          message: `Type de service invalide. Types autorisés: ${ALLOWED_SERVICE_TYPES.join(', ')}`
        });
      }

      logger.info('Consultation template tarification', { type });

      const template = ServiceComplexeService.getDefaultTarificationTemplate(type);
      response.json({
        success: true,
        data: {
          type_service: type,
          template: template
        }
      });
    } catch (error) {
      logger.error('Erreur consultation template tarification', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la consultation du modèle de tarification'
      });
    }
  }

  // ==================== INTÉGRATION AVEC LE SYSTÈME D'INVENTAIRE ====================

  /**
   * Mise à jour automatique des tarifs depuis les recettes
   */
  static async updateTarificationFromRecipes(request, response) {
    try {
      const { id } = request.params;

      logger.info('Mise à jour tarification depuis recettes', { serviceId: id });

      const result = await ServiceComplexeService.updateTarificationFromRecipes(id);

      if (result.updated) {
        response.json({
          success: true,
          data: result,
          message: `Tarification mise à jour: ${result.prix_mis_a_jour} prix modifiés sur ${result.total_recettes} recettes`
        });
      } else {
        response.json({
          success: true,
          data: result,
          message: result.message || 'Aucune mise à jour nécessaire'
        });
      }
    } catch (error) {
      logger.error('Erreur mise à jour tarification depuis recettes', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la mise à jour de la tarification depuis les recettes'
      });
    }
  }

  /**
   * Synchronisation prix/coûts avec marge cible
   */
  static async syncPricesWithCosts(request, response) {
    try {
      const { id } = request.params;
      const { target_margin = 30 } = request.body;

      // Validation de la marge
      if (target_margin < 0 || target_margin > 100) {
        return response.status(400).json({
          success: false,
          message: 'La marge cible doit être comprise entre 0 et 100%'
        });
      }

      logger.info('Synchronisation prix/coûts', { serviceId: id, targetMargin: target_margin });

      const result = await ServiceComplexeService.syncPricesWithCosts(id, target_margin);

      response.json({
        success: true,
        data: result,
        message: `Synchronisation terminée: ${result.produits_mis_a_jour} produits mis à jour avec une marge de ${target_margin}%`
      });
    } catch (error) {
      logger.error('Erreur synchronisation prix/coûts', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la synchronisation des prix et coûts'
      });
    }
  }

  /**
   * Génération de tarification depuis import Excel
   */
  static async generateTarificationFromImport(request, response) {
    try {
      const { id, importId } = request.params;

      logger.info('Génération tarification depuis import', { serviceId: id, importId });

      const result = await ServiceComplexeService.generateTarificationFromImport(importId, id);

      if (result.generated) {
        response.json({
          success: true,
          data: result,
          message: `Tarification générée: ${result.produits_ajoutes} produits ajoutés depuis l'import ${importId}`
        });
      } else {
        response.json({
          success: true,
          data: result,
          message: result.message || 'Aucun produit à traiter pour cet import'
        });
      }
    } catch (error) {
      logger.error('Erreur génération tarification depuis import', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la génération de la tarification depuis l\'import'
      });
    }
  }
}

module.exports = ServiceController;
