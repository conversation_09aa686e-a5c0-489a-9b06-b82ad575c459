const Controller = require('./Controller');
const TableService = require('../services/table.service');
const logger = require('../logger');

/**
 * Contrôleur pour la gestion des tables de restaurant/bar
 */
class TableController extends Controller {

  /**
   * GET /api/tables
   * Récupérer toutes les tables d'un complexe
   */
  static async getAllTables(request, response) {
    try {
      const { complexe_id } = request.user;
      
      if (!complexe_id) {
        return response.status(400).json({
          success: false,
          message: 'ID du complexe requis'
        });
      }

      logger.info('Récupération de toutes les tables', { complexe_id });

      // Pour récupérer toutes les tables, on peut faire une requête directe
      // ou étendre le service pour supporter cette fonctionnalité
      const result = await TableService.getAllTablesByComplexe(complexe_id);
      
      response.json(result);
    } catch (error) {
      logger.error('Erreur récupération toutes les tables:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des tables'
      });
    }
  }

  /**
   * GET /api/tables/service/:serviceId
   * Récupérer les tables d'un service spécifique
   */
  static async getTablesByService(request, response) {
    try {
      const { serviceId } = request.params;
      
      if (!serviceId) {
        return response.status(400).json({
          success: false,
          message: 'ID du service requis'
        });
      }

      logger.info('Récupération tables par service', { serviceId });

      const result = await TableService.getTablesByService(parseInt(serviceId));
      
      response.json(result);
    } catch (error) {
      logger.error('Erreur récupération tables par service:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des tables'
      });
    }
  }

  /**
   * POST /api/tables
   * Créer une nouvelle table
   */
  static async createTable(request, response) {
    try {
      const { complexe_id } = request.user;
      const tableData = {
        ...request.body,
        complexe_id
      };

      // Validation des champs requis
      const requiredFields = ['service_id', 'numero', 'capacite'];
      for (const field of requiredFields) {
        if (!tableData[field]) {
          return response.status(400).json({
            success: false,
            message: `Le champ ${field} est requis`
          });
        }
      }

      logger.info('Création nouvelle table', { tableData });

      const result = await TableService.createTable(tableData);
      
      if (!result.success) {
        return response.status(result.code || 500).json(result);
      }

      response.status(201).json(result);
    } catch (error) {
      logger.error('Erreur création table:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la création de la table'
      });
    }
  }

  /**
   * PUT /api/tables/:id
   * Mettre à jour une table
   */
  static async updateTable(request, response) {
    try {
      const { id } = request.params;
      const updateData = request.body;

      if (!id) {
        return response.status(400).json({
          success: false,
          message: 'ID de la table requis'
        });
      }

      logger.info('Mise à jour table', { tableId: id, updateData });

      // Pour l'instant, on utilise updateTableStatus si c'est un changement de statut
      // Sinon, il faudrait créer une méthode updateTable dans le service
      if (updateData.statut) {
        const result = await TableService.updateTableStatus(parseInt(id), updateData.statut);
        return response.json(result);
      }

      if (updateData.position_x !== undefined || updateData.position_y !== undefined) {
        const position = {
          position_x: updateData.position_x,
          position_y: updateData.position_y
        };
        const result = await TableService.updateTablePosition(parseInt(id), position);
        return response.json(result);
      }

      response.status(400).json({
        success: false,
        message: 'Aucune mise à jour valide fournie'
      });
    } catch (error) {
      logger.error('Erreur mise à jour table:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la mise à jour de la table'
      });
    }
  }

  /**
   * DELETE /api/tables/:id
   * Supprimer une table (désactivation)
   */
  static async deleteTable(request, response) {
    try {
      const { id } = request.params;

      if (!id) {
        return response.status(400).json({
          success: false,
          message: 'ID de la table requis'
        });
      }

      logger.info('Suppression table', { tableId: id });

      // Marquer la table comme hors service plutôt que la supprimer
      const result = await TableService.updateTableStatus(parseInt(id), 'Hors service');
      
      response.json({
        success: true,
        message: 'Table désactivée avec succès',
        data: result.data
      });
    } catch (error) {
      logger.error('Erreur suppression table:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la suppression de la table'
      });
    }
  }

  /**
   * PUT /api/tables/:id/status
   * Mettre à jour le statut d'une table
   */
  static async updateTableStatus(request, response) {
    try {
      const { id } = request.params;
      const { statut } = request.body;

      if (!id || !statut) {
        return response.status(400).json({
          success: false,
          message: 'ID de la table et statut requis'
        });
      }

      logger.info('Mise à jour statut table', { tableId: id, statut });

      const result = await TableService.updateTableStatus(parseInt(id), statut);
      
      response.json(result);
    } catch (error) {
      logger.error('Erreur mise à jour statut table:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la mise à jour du statut'
      });
    }
  }

  /**
   * GET /api/tables/service/:serviceId/layout
   * Récupérer le layout des tables d'un service
   */
  static async getTableLayout(request, response) {
    try {
      const { serviceId } = request.params;

      if (!serviceId) {
        return response.status(400).json({
          success: false,
          message: 'ID du service requis'
        });
      }

      logger.info('Récupération layout tables', { serviceId });

      const result = await TableService.getTableLayout(parseInt(serviceId));

      response.json(result);
    } catch (error) {
      logger.error('Erreur récupération layout tables:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération du layout'
      });
    }
  }

  /**
   * POST /api/tables/reservations
   * Créer une réservation de table
   */
  static async createTableReservation(request, response) {
    try {
      const { complexe_id } = request.user;
      const reservationData = {
        ...request.body,
        complexe_id
      };

      // Validation des champs requis
      const requiredFields = ['service_id', 'table_id', 'date_debut', 'date_fin', 'nombre_personnes'];
      for (const field of requiredFields) {
        if (!reservationData[field]) {
          return response.status(400).json({
            success: false,
            message: `Le champ ${field} est requis`
          });
        }
      }

      // Validation des dates
      const dateDebut = new Date(reservationData.date_debut);
      const dateFin = new Date(reservationData.date_fin);

      if (dateDebut >= dateFin) {
        return response.status(400).json({
          success: false,
          message: 'La date de fin doit être postérieure à la date de début'
        });
      }

      if (dateDebut < new Date()) {
        return response.status(400).json({
          success: false,
          message: 'La date de début ne peut pas être dans le passé'
        });
      }

      logger.info('Création réservation table', { reservationData });

      const result = await TableService.reserveTable(reservationData);

      if (!result.success) {
        return response.status(result.code || 500).json(result);
      }

      response.status(201).json(result);
    } catch (error) {
      logger.error('Erreur création réservation table:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la création de la réservation'
      });
    }
  }

  /**
   * GET /api/tables/service/:serviceId/reservations
   * Récupérer les réservations d'un service pour une date
   */
  static async getTableReservations(request, response) {
    try {
      const { serviceId } = request.params;
      const { date } = request.query;

      if (!serviceId) {
        return response.status(400).json({
          success: false,
          message: 'ID du service requis'
        });
      }

      if (!date) {
        return response.status(400).json({
          success: false,
          message: 'Date requise (format: YYYY-MM-DD)'
        });
      }

      logger.info('Récupération réservations tables', { serviceId, date });

      const result = await TableService.getTableReservations(parseInt(serviceId), date);

      response.json(result);
    } catch (error) {
      logger.error('Erreur récupération réservations tables:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des réservations'
      });
    }
  }

  /**
   * DELETE /api/tables/reservations/:id
   * Annuler une réservation de table
   */
  static async cancelTableReservation(request, response) {
    try {
      const { id } = request.params;

      if (!id) {
        return response.status(400).json({
          success: false,
          message: 'ID de la réservation requis'
        });
      }

      logger.info('Annulation réservation table', { reservationId: id });

      const result = await TableService.cancelTableReservation(parseInt(id));

      response.json(result);
    } catch (error) {
      logger.error('Erreur annulation réservation table:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de l\'annulation de la réservation'
      });
    }
  }
}

module.exports = TableController;
