const POSAnalyticsService = require('../services/posAnalytics.service');
const logger = require('../logger');

/**
 * Contrôleur pour les analytics des points de vente Restaurant/Bar
 * Gère les endpoints d'analyse des performances et statistiques
 */
class POSAnalyticsController {

  /**
   * GET /api/pos-analytics/dashboard/:serviceId
   * Dashboard complet des performances d'un service
   */
  static async getServiceDashboard(request, response) {
    try {
      const { serviceId } = request.params;
      const { 
        date_debut = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        date_fin = new Date().toISOString().split('T')[0]
      } = request.query;

      if (!serviceId) {
        return response.status(400).json({
          success: false,
          message: 'ID du service requis'
        });
      }

      logger.info('Génération dashboard analytics service', { 
        serviceId, 
        date_debut, 
        date_fin 
      });

      const result = await POSAnalyticsService.getServiceDashboard(
        parseInt(serviceId), 
        date_debut, 
        date_fin
      );

      if (result.success) {
        response.json({
          success: true,
          data: result.data,
          message: 'Dashboard généré avec succès'
        });
      } else {
        response.status(result.code || 500).json(result);
      }
    } catch (error) {
      logger.error('Erreur dashboard analytics service:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la génération du dashboard'
      });
    }
  }

  /**
   * GET /api/pos-analytics/performance/:serviceId
   * Analyse des performances d'un service
   */
  static async getServicePerformance(request, response) {
    try {
      const { serviceId } = request.params;
      const { 
        date_debut = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        date_fin = new Date().toISOString().split('T')[0]
      } = request.query;

      logger.info('Analyse performance service', { serviceId, date_debut, date_fin });

      const result = await POSAnalyticsService.getServicePerformance(
        parseInt(serviceId), 
        date_debut, 
        date_fin
      );

      response.json(result);
    } catch (error) {
      logger.error('Erreur analyse performance service:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de l\'analyse des performances'
      });
    }
  }

  /**
   * GET /api/pos-analytics/products/:serviceId
   * Top des produits les plus vendus
   */
  static async getTopProducts(request, response) {
    try {
      const { serviceId } = request.params;
      const { 
        date_debut = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        date_fin = new Date().toISOString().split('T')[0],
        limit = 10
      } = request.query;

      logger.info('Analyse top produits', { serviceId, limit });

      const result = await POSAnalyticsService.getTopProducts(
        parseInt(serviceId), 
        date_debut, 
        date_fin, 
        parseInt(limit)
      );

      response.json(result);
    } catch (error) {
      logger.error('Erreur analyse top produits:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de l\'analyse des produits'
      });
    }
  }

  /**
   * GET /api/pos-analytics/tables/:serviceId
   * Analyse de l'utilisation des tables
   */
  static async getTableUtilization(request, response) {
    try {
      const { serviceId } = request.params;
      const { 
        date_debut = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        date_fin = new Date().toISOString().split('T')[0]
      } = request.query;

      logger.info('Analyse utilisation tables', { serviceId });

      const result = await POSAnalyticsService.getTableUtilization(
        parseInt(serviceId), 
        date_debut, 
        date_fin
      );

      response.json(result);
    } catch (error) {
      logger.error('Erreur analyse utilisation tables:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de l\'analyse des tables'
      });
    }
  }

  /**
   * GET /api/pos-analytics/revenue/:serviceId
   * Analyse détaillée des revenus
   */
  static async getRevenueAnalysis(request, response) {
    try {
      const { serviceId } = request.params;
      const { 
        date_debut = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        date_fin = new Date().toISOString().split('T')[0]
      } = request.query;

      logger.info('Analyse revenus service', { serviceId });

      const result = await POSAnalyticsService.getRevenueAnalysis(
        parseInt(serviceId), 
        date_debut, 
        date_fin
      );

      response.json(result);
    } catch (error) {
      logger.error('Erreur analyse revenus:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de l\'analyse des revenus'
      });
    }
  }

  /**
   * GET /api/pos-analytics/preparation-time/:serviceId
   * Analyse des temps de préparation
   */
  static async getPreparationTime(request, response) {
    try {
      const { serviceId } = request.params;
      const { 
        date_debut = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        date_fin = new Date().toISOString().split('T')[0]
      } = request.query;

      logger.info('Analyse temps préparation', { serviceId });

      const result = await POSAnalyticsService.getAveragePreparationTime(
        parseInt(serviceId), 
        date_debut, 
        date_fin
      );

      response.json(result);
    } catch (error) {
      logger.error('Erreur analyse temps préparation:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de l\'analyse des temps de préparation'
      });
    }
  }

  /**
   * GET /api/pos-analytics/comparison/:complexeId
   * Comparaison entre services d'un complexe
   */
  static async getServiceComparison(request, response) {
    try {
      const { complexeId } = request.params;
      const { 
        date_debut = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        date_fin = new Date().toISOString().split('T')[0]
      } = request.query;

      logger.info('Comparaison services complexe', { complexeId });

      const result = await POSAnalyticsService.getServiceComparison(
        parseInt(complexeId), 
        date_debut, 
        date_fin
      );

      response.json(result);
    } catch (error) {
      logger.error('Erreur comparaison services:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la comparaison des services'
      });
    }
  }

  /**
   * GET /api/pos-analytics/trends/:serviceId
   * Tendances de fréquentation par jour de la semaine
   */
  static async getWeeklyTrends(request, response) {
    try {
      const { serviceId } = request.params;
      const { 
        date_debut = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        date_fin = new Date().toISOString().split('T')[0]
      } = request.query;

      logger.info('Analyse tendances hebdomadaires', { serviceId });

      const result = await POSAnalyticsService.getWeeklyTrends(
        parseInt(serviceId), 
        date_debut, 
        date_fin
      );

      response.json(result);
    } catch (error) {
      logger.error('Erreur analyse tendances:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de l\'analyse des tendances'
      });
    }
  }

  /**
   * GET /api/pos-analytics/employees/:serviceId
   * Performance des employés
   */
  static async getEmployeePerformance(request, response) {
    try {
      const { serviceId } = request.params;
      const { 
        date_debut = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        date_fin = new Date().toISOString().split('T')[0]
      } = request.query;

      logger.info('Analyse performance employés', { serviceId });

      const result = await POSAnalyticsService.getEmployeePerformance(
        parseInt(serviceId), 
        date_debut, 
        date_fin
      );

      response.json(result);
    } catch (error) {
      logger.error('Erreur analyse performance employés:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de l\'analyse des employés'
      });
    }
  }

  /**
   * GET /api/pos-analytics/forecast/:serviceId
   * Prévisions basées sur les tendances
   */
  static async getForecast(request, response) {
    try {
      const { serviceId } = request.params;
      const { jours = 7 } = request.query;

      logger.info('Génération prévisions', { serviceId, jours });

      const result = await POSAnalyticsService.getForecast(
        parseInt(serviceId), 
        parseInt(jours)
      );

      response.json(result);
    } catch (error) {
      logger.error('Erreur génération prévisions:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la génération des prévisions'
      });
    }
  }
}

module.exports = POSAnalyticsController;
