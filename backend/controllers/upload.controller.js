const FileUploadService = require('../services/fileUpload.service');
const ExcelParserService = require('../services/excelParser.service');
const TemplateService = require('../services/template.service');
const logger = require('../logger');

class UploadController {

  /**
   * POST /api/upload/excel
   * Upload d'un fichier Excel
   */
  static async uploadExcelFile(request, response) {
    try {
      const { complexeId, serviceId, employeId, typeImport } = request.body;
      const file = request.file;

      // Validation des paramètres requis
      if (!complexeId || !employeId || !typeImport) {
        return response.status(400).json({
          success: false,
          message: 'Les paramètres complexeId, employeId et typeImport sont requis'
        });
      }

      if (!file) {
        return response.status(400).json({
          success: false,
          message: 'Aucun fichier fourni'
        });
      }

      // Validation du type d'import
      const allowedTypes = ['MENU_RESTAURANT', 'CARTE_BAR', 'INVENTAIRE_INGREDIENTS'];
      if (!allowedTypes.includes(typeImport)) {
        return response.status(400).json({
          success: false,
          message: `Type d'import invalide. Types autorisés: ${allowedTypes.join(', ')}`
        });
      }

      logger.info('Upload Excel file', {
        originalName: file.originalname,
        size: file.size,
        complexeId,
        serviceId,
        typeImport
      });

      const metadata = {
        complexeId: parseInt(complexeId),
        serviceId: serviceId ? parseInt(serviceId) : null,
        employeId: parseInt(employeId),
        typeImport
      };

      const result = await FileUploadService.uploadExcelFile(file, metadata);

      response.status(201).json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Error uploading Excel file:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de l\'upload du fichier'
      });
    }
  }

  /**
   * POST /api/upload/parse/:importId
   * Parsing d'un fichier Excel uploadé
   */
  static async parseExcelFile(request, response) {
    try {
      const { importId } = request.params;
      const { templateId } = request.body;

      if (!templateId) {
        return response.status(400).json({
          success: false,
          message: 'L\'ID du template est requis'
        });
      }

      logger.info('Parse Excel file', { importId, templateId });

      // Récupération des informations d'import
      const importInfo = await FileUploadService.getImportInfo(importId);
      
      // Récupération du template
      const templateResult = await TemplateService.getTemplateById(templateId);
      const templateConfig = templateResult.data;

      // Parsing du fichier
      const result = await ExcelParserService.parseExcelFile(
        importInfo.chemin_fichier,
        templateConfig,
        importId
      );

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error parsing Excel file for import ${request.params.importId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors du parsing du fichier'
      });
    }
  }

  /**
   * GET /api/upload/templates/:type
   * Téléchargement des templates d'import
   */
  static async getImportTemplate(request, response) {
    try {
      const { type } = request.params;
      const { serviceType } = request.query;

      // Validation du type d'import
      const allowedTypes = ['MENU', 'CARTE', 'INVENTAIRE'];
      if (!allowedTypes.includes(type)) {
        return response.status(400).json({
          success: false,
          message: `Type de template invalide. Types autorisés: ${allowedTypes.join(', ')}`
        });
      }

      logger.info('Get import template', { type, serviceType });

      // Recherche du template approprié
      const templates = await TemplateService.getTemplates(1, { // TODO: récupérer chaineId depuis le contexte
        typeImport: type,
        typeService: serviceType,
        actif: true
      });

      if (!templates.data || templates.data.length === 0) {
        return response.status(404).json({
          success: false,
          message: 'Aucun template trouvé pour ce type'
        });
      }

      // Génération du fichier template
      const templateFile = await TemplateService.generateTemplate(templates.data[0].template_id);

      response.json({
        success: true,
        data: templateFile.data,
        message: 'Template généré avec succès'
      });

    } catch (error) {
      logger.error(`Error generating template for type ${request.params.type}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la génération du template'
      });
    }
  }

  /**
   * POST /api/upload/validate/:importId
   * Validation des données importées
   */
  static async validateImportData(request, response) {
    try {
      const { importId } = request.params;

      logger.info('Validate import data', { importId });

      const result = await ExcelParserService.validateData(importId);

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error validating import data ${request.params.importId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la validation des données'
      });
    }
  }

  /**
   * GET /api/upload/preview/:importId
   * Prévisualisation des données
   */
  static async previewImportData(request, response) {
    try {
      const { importId } = request.params;
      const { maxRows = 50 } = request.query;

      logger.info('Preview import data', { importId, maxRows });

      const result = await ExcelParserService.generatePreview(importId, parseInt(maxRows));

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error generating preview for import ${request.params.importId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la génération de l\'aperçu'
      });
    }
  }

  /**
   * GET /api/upload/status/:importId
   * Statut de l'import en cours
   */
  static async getImportStatus(request, response) {
    try {
      const { importId } = request.params;

      logger.info('Get import status', { importId });

      const importInfo = await FileUploadService.getImportInfo(importId);

      // Calcul du pourcentage de progression
      let progression = 0;
      switch (importInfo.statut) {
        case 'EN_COURS':
          progression = 25;
          break;
        case 'VALIDE':
          progression = 75;
          break;
        case 'IMPORTE':
          progression = 100;
          break;
        case 'ERREUR':
        case 'ANNULE':
          progression = 0;
          break;
      }

      const statusData = {
        importId: importInfo.import_id,
        statut: importInfo.statut,
        progression,
        nombreLignesTotal: importInfo.nombre_lignes_total,
        nombreLignesValides: importInfo.nombre_lignes_valides,
        nombreErreurs: importInfo.nombre_erreurs,
        dateImport: importInfo.date_import,
        dateTraitement: importInfo.date_traitement,
        dateFinalisation: importInfo.date_finalisation,
        nomFichier: importInfo.nom_fichier,
        tailleFichier: importInfo.taille_fichier,
        typeImport: importInfo.type_import,
        employeNom: importInfo.employe_nom,
        employePrenom: importInfo.employe_prenom,
        serviceName: importInfo.service_nom,
        complexeName: importInfo.complexe_nom
      };

      response.json({
        success: true,
        data: statusData
      });

    } catch (error) {
      logger.error(`Error getting import status ${request.params.importId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération du statut'
      });
    }
  }

  /**
   * DELETE /api/upload/:importId
   * Suppression d'un import et nettoyage des fichiers
   */
  static async deleteImport(request, response) {
    try {
      const { importId } = request.params;

      logger.info('Delete import', { importId });

      // Nettoyage du fichier
      await FileUploadService.cleanupTempFiles(importId);

      response.json({
        success: true,
        message: 'Import supprimé avec succès'
      });

    } catch (error) {
      logger.error(`Error deleting import ${request.params.importId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la suppression de l\'import'
      });
    }
  }

  /**
   * GET /api/upload/download/template/:templateId
   * Téléchargement direct d'un fichier template
   */
  static async downloadTemplate(request, response) {
    try {
      const { templateId } = request.params;

      logger.info('Download template', { templateId });

      const templateFile = await TemplateService.generateTemplate(templateId);
      const filePath = templateFile.data.filePath;
      const fileName = templateFile.data.fileName;

      // Configuration des headers pour le téléchargement
      response.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      response.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);

      // Envoi du fichier
      response.sendFile(filePath, { root: '.' }, (error) => {
        if (error) {
          logger.error('Error sending template file:', error);
          if (!response.headersSent) {
            response.status(500).json({
              success: false,
              message: 'Erreur lors du téléchargement du template'
            });
          }
        }
      });

    } catch (error) {
      logger.error(`Error downloading template ${request.params.templateId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors du téléchargement du template'
      });
    }
  }

  /**
   * POST /api/upload/cleanup
   * Nettoyage manuel des fichiers temporaires
   */
  static async cleanupTempFiles(request, response) {
    try {
      const { olderThanDays = 7 } = request.body;

      logger.info('Manual cleanup temp files', { olderThanDays });

      const cleanedCount = await FileUploadService.cleanupTempFiles(null, olderThanDays);

      response.json({
        success: true,
        data: { cleanedCount },
        message: `${cleanedCount} fichiers nettoyés avec succès`
      });

    } catch (error) {
      logger.error('Error during manual cleanup:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors du nettoyage des fichiers'
      });
    }
  }
}

module.exports = UploadController;
