const Controller = require('./Controller');
const RapportService = require('../services/rapport.service');
const logger = require('../logger');

class RapportController extends Controller {
  // Rapport des paiements
  static async getRapportPaiements(request, response) {
    try {
      const params = this.collectRequestParams(request);
      logger.info('Génération rapport paiements', { 
        params,
        utilisateur_id: request.user?.id
      });
      
      const result = await RapportService.getRapportPaiements(params);
      this.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur génération rapport paiements', error);
      this.sendError(response, error);
    }
  }

  // Rapport des revenus
  static async getRapportRevenus(request, response) {
    try {
      const params = this.collectRequestParams(request);
      logger.info('Génération rapport revenus', { 
        params,
        utilisateur_id: request.user?.id
      });
      
      const result = await RapportService.getRapportRevenus(params);
      this.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur génération rapport revenus', error);
      this.sendError(response, error);
    }
  }
}

module.exports = RapportController;