const bcrypt = require('bcrypt');
const EmployeeService = require('../services/employee.service');
const EmployeeTypeService = require('../services/employeeType.service');
const logger = require('../logger');
const db = require('../db');

class EmployeeController {
  static async createEmployee(req, res) {
    try {
      let complexeId;

      // Déterminer le complexe_id selon le type d'utilisateur
      if (req.user.role === 'super_admin' || req.user.role === 'admin_chaine') {
        // Super admin et admin chaine peuvent créer des employés pour n'importe quel complexe
        // Le complexe_id doit être fourni dans le body de la requête
        complexeId = req.body.complexe_id;

        if (!complexeId) {
          return res.status(400).json({
            message: 'Complexe ID requis dans le body de la requête pour les super admins et admins de chaîne.'
          });
        }
      } else if (req.user.role === 'admin_complexe' || req.user.role === 'employe') {
        // Admin complexe et employés ne peuvent créer des employés que pour leur complexe
        complexeId = req.user.complexe_id;

        if (!complexeId) {
          return res.status(400).json({
            message: 'Complexe ID manquant dans le token utilisateur.'
          });
        }
      } else {
        return res.status(403).json({
          message: 'Rôle non autorisé pour créer des employés.'
        });
      }

      // Validation du type d'employé si fourni
      if (req.body.type_employe && !EmployeeTypeService.isValidType(req.body.type_employe)) {
        return res.status(400).json({
          message: `Type d'employé invalide: ${req.body.type_employe}`
        });
      }

      const employeeData = {
        ...req.body,
        complexe_id: complexeId
      };

      const employee = await EmployeeService.createEmployee(employeeData);

      // Enrichir la réponse avec les informations de type si disponible
      let enrichedEmployee = employee;
      if (employee.type_employe && EmployeeTypeService.isValidType(employee.type_employe)) {
        enrichedEmployee = {
          ...employee,
          type_info: EmployeeTypeService.getTypeInfo(employee.type_employe)
        };
      }

      res.status(201).json(enrichedEmployee);
    } catch (error) {
      logger.error('Error in createEmployee controller:', error);
      res.status(400).json({ message: error.message });
    }
  }

  static async getEmployee(req, res) {
    try {
      const employee = await EmployeeService.getEmployeeById(req.params.id);
      
      if (!employee) {
        return res.status(404).json({ message: 'Employé non trouvé' });
      }

      // Vérifier l'autorisation selon le type d'utilisateur
      if (req.user.role === 'super_admin') {
        // Super admin peut voir n'importe quel employé
      } else if (req.user.role === 'admin_chaine') {
        // Admin chaîne peut voir les employés de tous les complexes de sa chaîne
        const complexeQuery = `
          SELECT chaine_id FROM "ComplexesHoteliers" WHERE complexe_id = $1
        `;
        const complexeResult = await db.query(complexeQuery, [employee.complexe_id]);

        if (complexeResult.rows.length === 0 || complexeResult.rows[0].chaine_id !== req.user.chaine_id) {
          return res.status(403).json({ message: 'Accès non autorisé - Employé ne fait pas partie de votre chaîne' });
        }
      } else if (req.user.role === 'admin_complexe' || req.user.role === 'employe') {
        // Admin complexe et employés ne peuvent voir que les employés de leur complexe
        if (employee.complexe_id !== req.user.complexe_id) {
          return res.status(403).json({ message: 'Accès non autorisé - Employé ne fait pas partie de votre complexe' });
        }
      } else {
        return res.status(403).json({ message: 'Rôle non autorisé pour consulter des employés' });
      }

      res.json(employee);
    } catch (error) {
      logger.error('Error in getEmployee controller:', error);
      res.status(500).json({ message: 'Erreur lors de la récupération de l\'employé' });
    }
  }

  static async getEmployees(req, res) {
    try {
      let complexeId;

      // Déterminer le complexe_id selon le type d'utilisateur
      if (req.user.role === 'super_admin' || req.user.role === 'admin_chaine') {
        // Super admin et admin chaine peuvent voir les employés de n'importe quel complexe
        // Le complexe_id peut être fourni en query parameter
        complexeId = req.query.complexe_id;

        if (!complexeId) {
          return res.status(400).json({
            message: 'Complexe ID requis en query parameter pour les super admins et admins de chaîne.'
          });
        }
      } else if (req.user.role === 'admin_complexe' || req.user.role === 'employe') {
        // Admin complexe et employés ne peuvent voir que les employés de leur complexe
        complexeId = req.user.complexe_id;

        if (!complexeId) {
          return res.status(400).json({
            message: 'Complexe ID manquant dans le token utilisateur.'
          });
        }
      } else {
        return res.status(403).json({
          message: 'Rôle non autorisé pour consulter les employés.'
        });
      }

      // Validation du type d'employé si fourni en filtre
      if (req.query.type_employe && !EmployeeTypeService.isValidType(req.query.type_employe)) {
        return res.status(400).json({
          message: `Type d'employé invalide: ${req.query.type_employe}`
        });
      }

      const filters = {
        role_id: req.query.role_id,
        search: req.query.search,
        type_employe: req.query.type_employe
      };

      // Utiliser la nouvelle méthode qui inclut les types d'employés
      const employees = await EmployeeService.getEmployeesWithTypes(
        complexeId,
        filters
      );

      res.json(employees);
    } catch (error) {
      logger.error('Error in getEmployees controller:', error);
      res.status(500).json({ message: 'Erreur lors de la récupération des employés' });
    }
  }

  static async updateEmployee(req, res) {
    try {
      const employee = await EmployeeService.getEmployeeById(req.params.id);

      if (!employee) {
        return res.status(404).json({ message: 'Employé non trouvé' });
      }

      // Vérifier l'autorisation selon le type d'utilisateur
      if (req.user.role === 'super_admin') {
        // Super admin peut modifier n'importe quel employé
      } else if (req.user.role === 'admin_chaine') {
        // Admin chaîne peut modifier les employés de tous les complexes de sa chaîne
        const complexeQuery = `
          SELECT chaine_id FROM "ComplexesHoteliers" WHERE complexe_id = $1
        `;
        const complexeResult = await db.query(complexeQuery, [employee.complexe_id]);

        if (complexeResult.rows.length === 0 || complexeResult.rows[0].chaine_id !== req.user.chaine_id) {
          return res.status(403).json({ message: 'Accès non autorisé - Employé ne fait pas partie de votre chaîne' });
        }
      } else if (req.user.role === 'admin_complexe' || req.user.role === 'employe') {
        // Admin complexe et employés ne peuvent modifier que les employés de leur complexe
        if (employee.complexe_id !== req.user.complexe_id) {
          return res.status(403).json({ message: 'Accès non autorisé - Employé ne fait pas partie de votre complexe' });
        }
      } else {
        return res.status(403).json({ message: 'Rôle non autorisé pour modifier des employés' });
      }

      // Validation du type d'employé si fourni
      if (req.body.type_employe && !EmployeeTypeService.isValidType(req.body.type_employe)) {
        return res.status(400).json({
          message: `Type d'employé invalide: ${req.body.type_employe}`
        });
      }

      const updatedEmployee = await EmployeeService.updateEmployee(
        req.params.id,
        req.body
      );

      // Enrichir la réponse avec les informations de type si disponible
      let enrichedEmployee = updatedEmployee;
      if (updatedEmployee.type_employe && EmployeeTypeService.isValidType(updatedEmployee.type_employe)) {
        enrichedEmployee = {
          ...updatedEmployee,
          type_info: EmployeeTypeService.getTypeInfo(updatedEmployee.type_employe)
        };
      }

      res.json(enrichedEmployee);
    } catch (error) {
      logger.error('Error in updateEmployee controller:', error);
      res.status(400).json({ message: error.message });
    }
  }

  static async updatePassword(req, res) {
    try {
      const { currentPassword, newPassword } = req.body;
      
      if (!currentPassword || !newPassword) {
        return res.status(400).json({ 
          message: 'Le mot de passe actuel et le nouveau mot de passe sont requis' 
        });
      }

      const employee = await EmployeeService.getEmployeeById(req.params.id);
      
      if (!employee) {
        return res.status(404).json({ message: 'Employé non trouvé' });
      }

      // Vérifier l'autorisation selon le type d'utilisateur
      if (req.user.role === 'super_admin') {
        // Super admin peut modifier n'importe quel mot de passe
      } else if (req.user.role === 'admin_chaine') {
        // Admin chaîne peut modifier les mots de passe des employés de sa chaîne
        const complexeQuery = `
          SELECT chaine_id FROM "ComplexesHoteliers" WHERE complexe_id = $1
        `;
        const complexeResult = await db.query(complexeQuery, [employee.complexe_id]);

        if (complexeResult.rows.length === 0 || complexeResult.rows[0].chaine_id !== req.user.chaine_id) {
          return res.status(403).json({ message: 'Accès non autorisé - Employé ne fait pas partie de votre chaîne' });
        }
      } else if (req.user.role === 'admin_complexe' || req.user.role === 'employe') {
        // Admin complexe et employés ne peuvent modifier que les mots de passe de leur complexe
        if (employee.complexe_id !== req.user.complexe_id) {
          return res.status(403).json({ message: 'Accès non autorisé - Employé ne fait pas partie de votre complexe' });
        }
      } else {
        return res.status(403).json({ message: 'Rôle non autorisé pour modifier des mots de passe' });
      }

      // Vérifier le mot de passe actuel
      const isValidPassword = await bcrypt.compare(currentPassword, employee.mot_de_passe_hash);
      if (!isValidPassword) {
        return res.status(400).json({ message: 'Mot de passe actuel incorrect' });
      }

      await EmployeeService.updatePassword(req.params.id, newPassword);
      res.json({ message: 'Mot de passe mis à jour avec succès' });
    } catch (error) {
      logger.error('Error in updatePassword controller:', error);
      res.status(400).json({ message: error.message });
    }
  }

  static async deleteEmployee(req, res) {
    try {
      const employee = await EmployeeService.getEmployeeById(req.params.id);

      if (!employee) {
        return res.status(404).json({ message: 'Employé non trouvé' });
      }

      // Vérifier l'autorisation selon le type d'utilisateur
      if (req.user.role === 'super_admin') {
        // Super admin peut supprimer n'importe quel employé
      } else if (req.user.role === 'admin_chaine') {
        // Admin chaîne peut supprimer les employés de tous les complexes de sa chaîne
        // Vérifier que l'employé appartient à un complexe de la même chaîne
        const complexeQuery = `
          SELECT chaine_id FROM "ComplexesHoteliers" WHERE complexe_id = $1
        `;
        const complexeResult = await db.query(complexeQuery, [employee.complexe_id]);

        if (complexeResult.rows.length === 0 || complexeResult.rows[0].chaine_id !== req.user.chaine_id) {
          return res.status(403).json({ message: 'Accès non autorisé - Employé ne fait pas partie de votre chaîne' });
        }
      } else if (req.user.role === 'admin_complexe' || req.user.role === 'employe') {
        // Admin complexe et employés ne peuvent supprimer que les employés de leur complexe
        if (employee.complexe_id !== req.user.complexe_id) {
          return res.status(403).json({ message: 'Accès non autorisé - Employé ne fait pas partie de votre complexe' });
        }
      } else {
        return res.status(403).json({ message: 'Rôle non autorisé pour supprimer des employés' });
      }

      await EmployeeService.deleteEmployee(req.params.id);
      res.json({ message: 'Employé supprimé avec succès' });
    } catch (error) {
      logger.error('Error in deleteEmployee controller:', error);
      res.status(500).json({ message: 'Erreur lors de la suppression de l\'employé' });
    }
  }

  static async hardDeleteEmployee(req, res) {
    try {
      const employee = await EmployeeService.getEmployeeById(req.params.id);

      if (!employee) {
        return res.status(404).json({ message: 'Employé non trouvé' });
      }

      // Vérifier l'autorisation selon le type d'utilisateur
      if (req.user.role === 'super_admin') {
        // Super admin peut supprimer définitivement n'importe quel employé
      } else if (req.user.role === 'admin_chaine') {
        // Admin chaîne peut supprimer définitivement les employés de tous les complexes de sa chaîne
        const complexeQuery = `
          SELECT chaine_id FROM "ComplexesHoteliers" WHERE complexe_id = $1
        `;
        const complexeResult = await db.query(complexeQuery, [employee.complexe_id]);

        if (complexeResult.rows.length === 0 || complexeResult.rows[0].chaine_id !== req.user.chaine_id) {
          return res.status(403).json({ message: 'Accès non autorisé - Employé ne fait pas partie de votre chaîne' });
        }
      } else if (req.user.role === 'admin_complexe') {
        // Admin complexe peut supprimer définitivement les employés de son complexe
        if (employee.complexe_id !== req.user.complexe_id) {
          return res.status(403).json({ message: 'Accès non autorisé - Employé ne fait pas partie de votre complexe' });
        }
      } else {
        return res.status(403).json({ message: 'Rôle non autorisé pour supprimer définitivement des employés' });
      }

      // Récupérer le paramètre force depuis la query string
      const force = req.query.force === 'true';

      const deletedEmployee = await EmployeeService.hardDeleteEmployee(req.params.id, force);
      res.json({
        message: 'Employé supprimé définitivement avec succès',
        employee: deletedEmployee
      });
    } catch (error) {
      logger.error('Error in hardDeleteEmployee controller:', error);

      // Si l'erreur concerne les dépendances, renvoyer un code 409 (Conflict)
      if (error.message.includes('enregistrement(s) dépendant(s)')) {
        return res.status(409).json({
          message: error.message,
          code: 'DEPENDENCIES_FOUND'
        });
      }

      res.status(500).json({ message: 'Erreur lors de la suppression définitive de l\'employé' });
    }
  }

  static async checkEmployeeDependencies(req, res) {
    try {
      const employee = await EmployeeService.getEmployeeById(req.params.id);

      if (!employee) {
        return res.status(404).json({ message: 'Employé non trouvé' });
      }

      // Vérifier l'autorisation (même logique que pour la suppression)
      if (req.user.role === 'super_admin') {
        // Super admin peut vérifier n'importe quel employé
      } else if (req.user.role === 'admin_chaine') {
        const complexeQuery = `
          SELECT chaine_id FROM "ComplexesHoteliers" WHERE complexe_id = $1
        `;
        const complexeResult = await db.query(complexeQuery, [employee.complexe_id]);

        if (complexeResult.rows.length === 0 || complexeResult.rows[0].chaine_id !== req.user.chaine_id) {
          return res.status(403).json({ message: 'Accès non autorisé' });
        }
      } else if (req.user.role === 'admin_complexe') {
        if (employee.complexe_id !== req.user.complexe_id) {
          return res.status(403).json({ message: 'Accès non autorisé' });
        }
      } else {
        return res.status(403).json({ message: 'Rôle non autorisé' });
      }

      const dependencies = await EmployeeService.checkEmployeeDependencies(req.params.id);
      res.json(dependencies);
    } catch (error) {
      logger.error('Error in checkEmployeeDependencies controller:', error);
      res.status(500).json({ message: 'Erreur lors de la vérification des dépendances' });
    }
  }

  static async getRoles(req, res) {
    try {
      let complexeId;

      // Déterminer le complexe_id selon le type d'utilisateur
      if (req.user.role === 'super_admin' || req.user.role === 'admin_chaine') {
        // Super admin et admin chaine peuvent voir les rôles de n'importe quel complexe
        // Le complexe_id peut être fourni en query parameter
        complexeId = req.query.complexe_id;

        if (!complexeId) {
          return res.status(400).json({
            message: 'Complexe ID requis en query parameter pour les super admins et admins de chaîne.'
          });
        }
      } else if (req.user.role === 'admin_complexe' || req.user.role === 'employe') {
        // Admin complexe et employés ne peuvent voir que les rôles de leur complexe
        complexeId = req.user.complexe_id;

        if (!complexeId) {
          return res.status(400).json({
            message: 'Complexe ID manquant dans le token utilisateur.'
          });
        }
      } else {
        return res.status(403).json({
          message: 'Rôle non autorisé pour consulter les rôles.'
        });
      }

      const roles = await EmployeeService.getEmployeeRoles(complexeId);
      res.json(roles);
    } catch (error) {
      logger.error('Error in getRoles controller:', error);
      res.status(500).json({ message: 'Erreur lors de la récupération des rôles' });
    }
  }

  /**
   * Obtenir les types d'employés disponibles
   */
  static async getEmployeeTypes(req, res) {
    try {
      const types = EmployeeTypeService.getAvailableTypes();
      res.json({
        success: true,
        data: types
      });
    } catch (error) {
      logger.error('Error getting employee types:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des types d\'employés'
      });
    }
  }

  /**
   * Assigner un type d'employé
   */
  static async assignEmployeeType(req, res) {
    try {
      const { id: employeeId } = req.params;
      const { type_employe, services_autorises } = req.body;

      // Validation du type
      if (!EmployeeTypeService.isValidType(type_employe)) {
        return res.status(400).json({
          message: `Type d'employé invalide: ${type_employe}`
        });
      }

      // Récupérer l'employé pour vérifier les autorisations
      const employee = await EmployeeService.getEmployeeById(employeeId);
      if (!employee) {
        return res.status(404).json({ message: 'Employé non trouvé' });
      }

      // Vérifier l'autorisation selon le type d'utilisateur
      let complexeId;
      if (req.user.role === 'super_admin' || req.user.role === 'admin_chaine') {
        complexeId = employee.complexe_id;
      } else if (req.user.role === 'admin_complexe') {
        if (employee.complexe_id !== req.user.complexe_id) {
          return res.status(403).json({ message: 'Accès non autorisé' });
        }
        complexeId = req.user.complexe_id;
      } else {
        return res.status(403).json({
          message: 'Non autorisé à modifier les types d\'employés'
        });
      }

      const updatedEmployee = await EmployeeService.assignEmployeeType(
        employeeId,
        type_employe,
        complexeId,
        services_autorises
      );

      res.json({
        success: true,
        message: 'Type d\'employé assigné avec succès',
        data: updatedEmployee
      });
    } catch (error) {
      logger.error('Error assigning employee type:', error);
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  /**
   * Valider l'accès d'un employé à un service
   */
  static async validateServiceAccess(req, res) {
    try {
      const { employeeId, serviceType } = req.params;

      const employee = await EmployeeService.getEmployeeById(employeeId);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: 'Employé non trouvé'
        });
      }

      // Vérifier l'autorisation
      if (req.user.role === 'employe' && req.user.id != employeeId) {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé'
        });
      }

      const hasAccess = EmployeeTypeService.validateServiceAccess(employee, serviceType);

      res.json({
        success: true,
        data: {
          employee_id: employeeId,
          service_type: serviceType,
          has_access: hasAccess,
          services_autorises: employee.services_autorises,
          type_employe: employee.type_employe
        }
      });
    } catch (error) {
      logger.error('Error validating service access:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la validation de l\'accès'
      });
    }
  }

}

module.exports = EmployeeController; 