const AutoStockDeductionService = require('../services/autoStockDeduction.service');
const logger = require('../logger');

/**
 * Contrôleur pour la déduction automatique du stock
 * Gère les endpoints liés à la déduction et restauration du stock
 */
class AutoStockDeductionController {

  /**
   * Déduire automatiquement le stock pour une transaction
   */
  static async deductStockForTransaction(request, response) {
    try {
      const transactionData = request.body;
      
      // Validation des données requises
      if (!transactionData.transaction_id) {
        return response.status(400).json({
          success: false,
          message: 'L\'ID de transaction est requis'
        });
      }

      if (!transactionData.lignes_transaction || !Array.isArray(transactionData.lignes_transaction)) {
        return response.status(400).json({
          success: false,
          message: 'Les lignes de transaction sont requises et doivent être un tableau'
        });
      }

      logger.info('Déduction automatique stock transaction', { 
        transactionId: transactionData.transaction_id,
        lignesCount: transactionData.lignes_transaction.length 
      });

      const result = await AutoStockDeductionService.deductStockForTransaction(transactionData);
      
      if (result.success) {
        response.json({
          success: true,
          data: result.data,
          message: 'Stock déduit avec succès'
        });
      } else {
        response.status(result.code || 400).json({
          success: false,
          message: result.message
        });
      }
    } catch (error) {
      logger.error('Erreur déduction automatique stock:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la déduction du stock'
      });
    }
  }

  /**
   * Restaurer le stock pour une transaction annulée
   */
  static async restoreStockForCancelledTransaction(request, response) {
    try {
      const { transactionId } = request.params;

      if (!transactionId) {
        return response.status(400).json({
          success: false,
          message: 'L\'ID de transaction est requis'
        });
      }

      logger.info('Restauration stock transaction annulée', { transactionId });

      const result = await AutoStockDeductionService.restoreStockForCancelledTransaction(
        parseInt(transactionId)
      );
      
      if (result.success) {
        response.json({
          success: true,
          data: result.data,
          message: 'Stock restauré avec succès'
        });
      } else {
        response.status(result.code || 400).json({
          success: false,
          message: result.message
        });
      }
    } catch (error) {
      logger.error('Erreur restauration stock transaction:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la restauration du stock'
      });
    }
  }

  /**
   * Vérifier le statut de déduction du stock pour une transaction
   */
  static async checkTransactionStockStatus(request, response) {
    try {
      const { transactionId } = request.params;

      if (!transactionId) {
        return response.status(400).json({
          success: false,
          message: 'L\'ID de transaction est requis'
        });
      }

      logger.info('Vérification statut stock transaction', { transactionId });

      const result = await AutoStockDeductionService.checkTransactionStockStatus(
        parseInt(transactionId)
      );
      
      if (result.success) {
        response.json({
          success: true,
          data: result.data,
          message: 'Statut du stock récupéré avec succès'
        });
      } else {
        response.status(result.code || 400).json({
          success: false,
          message: result.message
        });
      }
    } catch (error) {
      logger.error('Erreur vérification statut stock transaction:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification du statut du stock'
      });
    }
  }
}

module.exports = AutoStockDeductionController; 