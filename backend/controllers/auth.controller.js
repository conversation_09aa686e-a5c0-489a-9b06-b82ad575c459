// eslint-disable-next-line import/no-extraneous-dependencies
const { validationResult, body } = require('express-validator');
const AuthService = require('../services/auth.service');
const TokenService = require('../services/token.service');
const logger = require('../logger');

class AuthController {
  static async login(req, res) {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { email, password, userType } = req.body;

      const result = await AuthService.login(email, password, userType);
      return res.json(result);
    } catch (error) {
      logger.error('Login controller error:', error);
      return res.status(401).json({ message: error.message });
    }
  }

  static async logout(req, res) {
    try {
      const token = req.headers.authorization?.replace('Bearer ', '');
      
      if (!token) {
        return res.status(400).json({ message: 'No token provided' });
      }

      // Revoke the token
      const revoked = TokenService.revokeToken(token);
      
      if (!revoked) {
        return res.status(500).json({ message: 'Error revoking token' });
      }

      return res.json({ 
        message: 'Logged out successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Logout controller error:', error);
      return res.status(500).json({ message: 'Error during logout' });
    }
  }

  // Validation middleware
  static validateLogin() {
    return [
      body('email').isEmail().withMessage('Invalid email format'),
      body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters long'),
      body('userType').isIn(['super_admin', 'admin_chaine', 'admin_complexe', 'employe'])
        .withMessage('Invalid user type'),
    ];
  }
}

module.exports = AuthController;
