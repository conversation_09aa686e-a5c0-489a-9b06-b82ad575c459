const ImportService = require('../services/import.service');
const logger = require('../logger');

class ImportController {

  /**
   * ==================== PROCESSUS D'IMPORT ====================
   */

  /**
   * POST /api/imports/:importId/process
   * Traitement complet d'un import validé
   */
  static async processImport(request, response) {
    try {
      const { importId } = request.params;

      logger.info('Process import', { importId });

      const result = await ImportService.processImport(parseInt(importId));

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error processing import ${request.params.importId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors du traitement de l\'import'
      });
    }
  }

  /**
   * POST /api/imports/:importId/validate
   * Validation complète des données d'import
   */
  static async validateImportData(request, response) {
    try {
      const { importId } = request.params;

      logger.info('Validate import data', { importId });

      const result = await ImportService.validateImportData(parseInt(importId));

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error validating import data ${request.params.importId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la validation des données'
      });
    }
  }

  /**
   * POST /api/imports/:importId/rollback
   * Annulation complète d'un import
   */
  static async rollbackImport(request, response) {
    try {
      const { importId } = request.params;

      logger.info('Rollback import', { importId });

      const result = await ImportService.rollbackImport(parseInt(importId));

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error rolling back import ${request.params.importId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de l\'annulation de l\'import'
      });
    }
  }

  /**
   * ==================== RAPPORTS ET HISTORIQUE ====================
   */

  /**
   * GET /api/imports/:importId/report
   * Génération du rapport d'import
   */
  static async generateImportReport(request, response) {
    try {
      const { importId } = request.params;

      logger.info('Generate import report', { importId });

      const result = await ImportService.generateImportReport(parseInt(importId));

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error generating import report ${request.params.importId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la génération du rapport'
      });
    }
  }

  /**
   * GET /api/imports/history/:complexeId
   * Historique des imports par complexe
   */
  static async getImportHistory(request, response) {
    try {
      const { complexeId } = request.params;
      const filters = {
        typeImport: request.query.typeImport,
        statut: request.query.statut,
        page: parseInt(request.query.page) || 1,
        limit: parseInt(request.query.limit) || 20
      };

      logger.info('Get import history', { complexeId, filters });

      const result = await ImportService.getImportHistory(parseInt(complexeId), filters);

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error(`Error getting import history for complexe ${request.params.complexeId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération de l\'historique'
      });
    }
  }

  /**
   * GET /api/imports/statistics/:complexeId
   * Statistiques des imports par complexe
   */
  static async getImportStatistics(request, response) {
    try {
      const { complexeId } = request.params;
      const { period = '30' } = request.query; // Période en jours

      logger.info('Get import statistics', { complexeId, period });

      // TODO: Implémenter les statistiques d'imports
      // Cette fonctionnalité nécessite l'extension du ImportService

      const mockStatistics = {
        totalImports: 0,
        successfulImports: 0,
        failedImports: 0,
        successRate: 0,
        averageProcessingTime: 0,
        totalRowsProcessed: 0,
        byType: {
          MENU_RESTAURANT: { count: 0, successRate: 0 },
          CARTE_BAR: { count: 0, successRate: 0 },
          INVENTAIRE_INGREDIENTS: { count: 0, successRate: 0 }
        },
        recentActivity: []
      };

      response.json({
        success: true,
        data: mockStatistics,
        message: 'Statistiques d\'import récupérées avec succès'
      });

    } catch (error) {
      logger.error(`Error getting import statistics for complexe ${request.params.complexeId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des statistiques'
      });
    }
  }

  /**
   * ==================== GESTION DES ERREURS ====================
   */

  /**
   * GET /api/imports/:importId/errors
   * Récupération détaillée des erreurs d'import
   */
  static async getImportErrors(request, response) {
    try {
      const { importId } = request.params;

      logger.info('Get import errors', { importId });

      // Récupération des informations d'import avec erreurs
      const importInfo = await ImportService.getImportInfo(parseInt(importId));
      
      const errors = JSON.parse(importInfo.erreurs_detectees || '[]');

      const errorAnalysis = {
        totalErrors: errors.length,
        errorsByType: {},
        errorsByField: {},
        criticalErrors: errors.filter(e => e.severity === 'critical'),
        warningErrors: errors.filter(e => e.severity === 'warning'),
        detailedErrors: errors
      };

      // Analyse des erreurs par type
      errors.forEach(error => {
        const type = error.rule || 'unknown';
        errorAnalysis.errorsByType[type] = (errorAnalysis.errorsByType[type] || 0) + 1;

        const field = error.field || 'unknown';
        errorAnalysis.errorsByField[field] = (errorAnalysis.errorsByField[field] || 0) + 1;
      });

      response.json({
        success: true,
        data: errorAnalysis,
        message: 'Erreurs d\'import récupérées avec succès'
      });

    } catch (error) {
      logger.error(`Error getting import errors ${request.params.importId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des erreurs'
      });
    }
  }

  /**
   * POST /api/imports/:importId/fix-errors
   * Correction automatique des erreurs simples
   */
  static async fixImportErrors(request, response) {
    try {
      const { importId } = request.params;
      const { corrections } = request.body;

      if (!corrections || !Array.isArray(corrections)) {
        return response.status(400).json({
          success: false,
          message: 'Les corrections doivent être fournies sous forme de tableau'
        });
      }

      logger.info('Fix import errors', { importId, correctionsCount: corrections.length });

      // TODO: Implémenter la correction automatique des erreurs
      // Cette fonctionnalité nécessite l'extension du ImportService

      response.json({
        success: true,
        message: 'Corrections appliquées avec succès'
      });

    } catch (error) {
      logger.error(`Error fixing import errors ${request.params.importId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la correction des erreurs'
      });
    }
  }

  /**
   * ==================== IMPORTS SPÉCIALISÉS ====================
   */

  /**
   * POST /api/imports/menu-restaurant/:importId
   * Import spécialisé pour menu restaurant
   */
  static async importMenuRestaurant(request, response) {
    try {
      const { importId } = request.params;
      const { options = {} } = request.body;

      logger.info('Import menu restaurant', { importId, options });

      // Validation que l'import est du bon type
      const importInfo = await ImportService.getImportInfo(parseInt(importId));
      
      if (importInfo.type_import !== 'MENU_RESTAURANT') {
        return response.status(400).json({
          success: false,
          message: 'Cet import n\'est pas de type MENU_RESTAURANT'
        });
      }

      const result = await ImportService.processImport(parseInt(importId));

      response.json({
        success: true,
        data: result.data,
        message: 'Menu restaurant importé avec succès'
      });

    } catch (error) {
      logger.error(`Error importing menu restaurant ${request.params.importId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de l\'import du menu restaurant'
      });
    }
  }

  /**
   * POST /api/imports/carte-bar/:importId
   * Import spécialisé pour carte bar
   */
  static async importCarteBar(request, response) {
    try {
      const { importId } = request.params;
      const { options = {} } = request.body;

      logger.info('Import carte bar', { importId, options });

      // Validation que l'import est du bon type
      const importInfo = await ImportService.getImportInfo(parseInt(importId));
      
      if (importInfo.type_import !== 'CARTE_BAR') {
        return response.status(400).json({
          success: false,
          message: 'Cet import n\'est pas de type CARTE_BAR'
        });
      }

      const result = await ImportService.processImport(parseInt(importId));

      response.json({
        success: true,
        data: result.data,
        message: 'Carte bar importée avec succès'
      });

    } catch (error) {
      logger.error(`Error importing carte bar ${request.params.importId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de l\'import de la carte bar'
      });
    }
  }

  /**
   * POST /api/imports/inventaire/:importId
   * Import spécialisé pour inventaire ingrédients
   */
  static async importInventaireIngredients(request, response) {
    try {
      const { importId } = request.params;
      const { options = {} } = request.body;

      logger.info('Import inventaire ingredients', { importId, options });

      // Validation que l'import est du bon type
      const importInfo = await ImportService.getImportInfo(parseInt(importId));
      
      if (importInfo.type_import !== 'INVENTAIRE_INGREDIENTS') {
        return response.status(400).json({
          success: false,
          message: 'Cet import n\'est pas de type INVENTAIRE_INGREDIENTS'
        });
      }

      const result = await ImportService.processImport(parseInt(importId));

      response.json({
        success: true,
        data: result.data,
        message: 'Inventaire ingrédients importé avec succès'
      });

    } catch (error) {
      logger.error(`Error importing inventaire ingredients ${request.params.importId}:`, error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de l\'import de l\'inventaire'
      });
    }
  }

  /**
   * ==================== UTILITAIRES ====================
   */

  /**
   * GET /api/imports/types
   * Liste des types d'imports disponibles
   */
  static async getImportTypes(request, response) {
    try {
      const importTypes = [
        {
          type: 'MENU_RESTAURANT',
          name: 'Menu Restaurant',
          description: 'Import de menus avec plats, prix et recettes',
          requiredFields: ['nom_produit', 'prix_vente'],
          optionalFields: ['categorie', 'description', 'ingredients', 'quantites', 'unites']
        },
        {
          type: 'CARTE_BAR',
          name: 'Carte Bar',
          description: 'Import de cartes de bar avec boissons et cocktails',
          requiredFields: ['nom_boisson', 'prix_vente'],
          optionalFields: ['categorie', 'degre_alcool', 'ingredients', 'quantites', 'unites']
        },
        {
          type: 'INVENTAIRE_INGREDIENTS',
          name: 'Inventaire Ingrédients',
          description: 'Import d\'ingrédients avec stocks et prix',
          requiredFields: ['nom_ingredient', 'prix_unitaire'],
          optionalFields: ['categorie', 'unite_mesure', 'stock_actuel', 'conservation']
        }
      ];

      response.json({
        success: true,
        data: importTypes,
        message: 'Types d\'imports récupérés avec succès'
      });

    } catch (error) {
      logger.error('Error getting import types:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des types d\'imports'
      });
    }
  }

  /**
   * GET /api/imports/status-options
   * Liste des statuts d'imports possibles
   */
  static async getImportStatusOptions(request, response) {
    try {
      const statusOptions = [
        { value: 'EN_COURS', label: 'En cours', description: 'Import en cours de traitement' },
        { value: 'VALIDE', label: 'Validé', description: 'Données validées, prêt pour import' },
        { value: 'ERREUR', label: 'Erreur', description: 'Erreurs détectées dans les données' },
        { value: 'IMPORTE', label: 'Importé', description: 'Import terminé avec succès' },
        { value: 'ANNULE', label: 'Annulé', description: 'Import annulé par l\'utilisateur' }
      ];

      response.json({
        success: true,
        data: statusOptions,
        message: 'Options de statut récupérées avec succès'
      });

    } catch (error) {
      logger.error('Error getting import status options:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des options de statut'
      });
    }
  }
}

module.exports = ImportController;
