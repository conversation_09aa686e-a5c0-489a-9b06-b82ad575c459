const Controller = require('./Controller');
const ReservationService = require('../services/reservation.service');
const logger = require('../logger');

class ReservationController extends Controller {
  // Routes client (publiques)
  static async createDemandeReservation(req, res) {
    try {
      // Vérifier si c'est une demande de réservation anonyme
      if (req.body.is_anonymous || req.body.anonyme) {
        // Rediriger vers le contrôleur anonyme
        const AnonymousReservationController = require('./anonymousReservation.controller');
        return await AnonymousReservationController.createDemandeReservationAnonyme(req, res);
      }

      const result = await ReservationService.createDemandeReservation(req.body);

      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      res.json(result);
    } catch (error) {
      logger.error('Erreur contrôleur création demande réservation:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la création de la demande de réservation'
      });
    }
  }

  static async verifierReservation(request, response) {
    try {
      const { numero } = request.params;
      logger.info('Vérification réservation', { numero });
      
      const result = await ReservationService.verifierReservation(numero);
      Controller.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur vérification réservation', error);
      Controller.sendError(response, error);
    }
  }

  // Routes réception (protégées)
  static async createReservation(request, response) {
    try {
      const params = request.body;
      logger.info('Création réservation', { 
        client_id: params.client_id,
        type_chambre: params.type_chambre,
        nombre_chambres: params.nombre_chambres,
        utilisateur_id: request.user.id
      });
      
      const result = await ReservationService.createReservation({
        ...params,
        utilisateur_id: request.user.id
      });
      Controller.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur création réservation', error);
      Controller.sendError(response, error);
    }
  }

  static async getReservationById(req, res) {
    try {
      const { reservationId } = req.params;
      const result = await ReservationService.getReservationById(reservationId);

      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      res.json(result);
    } catch (error) {
      logger.error('Erreur contrôleur récupération réservation:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de la réservation'
      });
    }
  }

  static async getChambresReservation(req, res) {
    try {
      const { reservationId } = req.params;
      const result = await ReservationService.getChambresReservation(reservationId);

      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      res.json(result);
    } catch (error) {
      logger.error('Erreur contrôleur récupération chambres réservation:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des chambres de la réservation'
      });
    }
  }

  static async getPaiementsReservation(req, res) {
    try {
      const { reservationId } = req.params;
      const result = await ReservationService.getPaiementsReservation(reservationId);

      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      res.json(result);
    } catch (error) {
      logger.error('Erreur contrôleur récupération paiements réservation:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des paiements de la réservation'
      });
    }
  }

  static async updateReservationStatus(req, res) {
    try {
      const { reservationId } = req.params;
      const { statut } = req.body;
      
      const result = await ReservationService.updateReservationStatus(reservationId, statut);
      
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }
      
      res.json(result);
    } catch (error) {
      logger.error('Erreur contrôleur mise à jour statut réservation:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour du statut de la réservation'
      });
    }
  }

  static async getPendingReservations(req, res) {
    try {
      const result = await ReservationService.getPendingReservations(req.query);
      
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }
      
      res.json(result);
    } catch (error) {
      logger.error('Erreur contrôleur récupération réservations en attente:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des réservations en attente'
      });
    }
  }

  static async confirmReservation(req, res) {
    try {
      const { reservationId } = req.params;
      const { chambres_ids, paiement } = req.body;
      
      const result = await ReservationService.confirmReservation(reservationId, {
        chambres_ids,
        paiement,
        utilisateur_id: req.user.id
      });
      
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }
      
      res.json(result);
    } catch (error) {
      logger.error('Erreur contrôleur confirmation réservation:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la confirmation de la réservation'
      });
    }
  }

  static async genererTicket(request, response) {
    try {
      const { id } = request.params;
      logger.info('Génération ticket', { 
        reservation_id: id,
        utilisateur_id: request.user.id
      });
      
      const result = await ReservationService.genererTicket(id);
      Controller.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur génération ticket', error);
      Controller.sendError(response, error);
    }
  }

  static async getHistoriqueReservation(request, response) {
    try {
      const { numero } = request.params;
      logger.info('Consultation historique réservation', { 
        numero_reservation: numero,
        utilisateur_id: request.user.id
      });
      
      const result = await ReservationService.getHistoriqueReservation(numero);
      Controller.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur consultation historique réservation', error);
      Controller.sendError(response, error);
    }
  }

  // Routes admin (protégées)
  static async getStatistiques(request, response) {
    try {
      const params = request.query;
      logger.info('Consultation statistiques', { 
        filtres: params,
        utilisateur_id: request.user.id
      });
      
      const result = await ReservationService.getStatistiques(params);
      Controller.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur consultation statistiques', error);
      Controller.sendError(response, error);
    }
  }

  static async getHistorique(request, response) {
    try {
      const params = request.query;
      logger.info('Consultation historique', { 
        filtres: params,
        utilisateur_id: request.user.id
      });
      
      const result = await ReservationService.getHistorique(params);
      Controller.sendResponse(response, result);
    } catch (error) {
      logger.error('Erreur consultation historique', error);
      Controller.sendError(response, error);
    }
  }

  // Rejeter une réservation
  static async rejectReservation(req, res) {
    try {
      const { reservationId } = req.params;
      const { reason } = req.body;

      const result = await ReservationService.rejectReservation(reservationId, reason);

      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      res.json(result);
    } catch (error) {
      logger.error('Erreur contrôleur rejet réservation:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors du rejet de la réservation'
      });
    }
  }

  // Nettoyer les données orphelines
  static async cleanupOrphanedData(req, res) {
    try {
      logger.info('Nettoyage des données orphelines demandé', {
        utilisateur_id: req.user ? req.user.id : null
      });

      const result = await ReservationService.cleanupOrphanedData();

      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      res.json(result);
    } catch (error) {
      logger.error('Erreur contrôleur nettoyage données orphelines:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors du nettoyage des données orphelines'
      });
    }
  }

  // Récupérer l'historique des réservations pour le calendrier
  static async getHistoriqueReservationsCalendrier(req, res) {
    try {
      const result = await ReservationService.getHistoriqueReservationsCalendrier(req.query);

      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      res.json(result);
    } catch (error) {
      logger.error('Erreur contrôleur historique réservations calendrier:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de l\'historique des réservations'
      });
    }
  }

  // Générer le ticket de caisse avec nouveau QR code
  static async genererTicketCaisse(req, res) {
    try {
      const { reservationId } = req.params;

      logger.info('Génération ticket de caisse demandée', {
        reservationId,
        utilisateur_id: req.user ? req.user.id : null
      });

      const result = await ReservationService.genererTicketCaisse(reservationId);

      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      // Configurer les headers pour le téléchargement du PDF
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${result.data.filename}"`);
      res.setHeader('Content-Length', result.data.pdf.length);

      // Envoyer le PDF
      res.send(result.data.pdf);
    } catch (error) {
      logger.error('Erreur contrôleur génération ticket de caisse:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la génération du ticket de caisse'
      });
    }
  }
}

module.exports = ReservationController;
