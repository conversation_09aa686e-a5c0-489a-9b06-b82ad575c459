const Controller = require('./Controller');
const MenuService = require('../services/menu.service');
const logger = require('../logger');

/**
 * Contrôleur pour la gestion des menus de restaurant/bar
 */
class MenuController extends Controller {

  /**
   * GET /api/menu/service/:serviceId
   * Récupérer le menu complet d'un service
   */
  static async getMenuByService(request, response) {
    try {
      const { serviceId } = request.params;

      if (!serviceId) {
        return response.status(400).json({
          success: false,
          message: 'ID du service requis'
        });
      }

      logger.info('Récupération menu par service', { serviceId });

      const result = await MenuService.getMenuByService(parseInt(serviceId));
      
      response.json(result);
    } catch (error) {
      logger.error('Erreur récupération menu par service:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération du menu'
      });
    }
  }

  /**
   * GET /api/menu/service/:serviceId/category/:categoryId
   * Récupérer les produits d'une catégorie spécifique
   */
  static async getProductsByCategory(request, response) {
    try {
      const { serviceId, categoryId } = request.params;

      if (!serviceId || !categoryId) {
        return response.status(400).json({
          success: false,
          message: 'ID du service et de la catégorie requis'
        });
      }

      logger.info('Récupération produits par catégorie', { serviceId, categoryId });

      const result = await MenuService.getProductsByCategory(
        parseInt(serviceId), 
        parseInt(categoryId)
      );
      
      response.json(result);
    } catch (error) {
      logger.error('Erreur récupération produits par catégorie:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération des produits'
      });
    }
  }

  /**
   * GET /api/menu/service/:serviceId/availability
   * Récupérer le menu avec informations de disponibilité en temps réel
   */
  static async getMenuWithAvailability(request, response) {
    try {
      const { serviceId } = request.params;

      if (!serviceId) {
        return response.status(400).json({
          success: false,
          message: 'ID du service requis'
        });
      }

      logger.info('Récupération menu avec disponibilité', { serviceId });

      const result = await MenuService.getMenuWithStock(parseInt(serviceId));
      
      response.json(result);
    } catch (error) {
      logger.error('Erreur récupération menu avec disponibilité:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération du menu avec disponibilité'
      });
    }
  }

  /**
   * PUT /api/menu/service/:serviceId/product/:productId/price
   * Mettre à jour le prix d'un produit pour un service
   */
  static async updateServicePrice(request, response) {
    try {
      const { serviceId, productId } = request.params;
      const { prix } = request.body;

      if (!serviceId || !productId) {
        return response.status(400).json({
          success: false,
          message: 'ID du service et du produit requis'
        });
      }

      if (!prix || prix < 0) {
        return response.status(400).json({
          success: false,
          message: 'Prix valide requis'
        });
      }

      logger.info('Mise à jour prix produit', { serviceId, productId, prix });

      const result = await MenuService.updateProductPrice(
        parseInt(productId),
        parseInt(serviceId),
        parseFloat(prix)
      );
      
      response.json(result);
    } catch (error) {
      logger.error('Erreur mise à jour prix produit:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la mise à jour du prix'
      });
    }
  }

  /**
   * PUT /api/menu/service/:serviceId/prices/bulk
   * Mise à jour en lot des prix pour un service
   */
  static async bulkUpdatePrices(request, response) {
    try {
      const { serviceId } = request.params;
      const { prices } = request.body;

      if (!serviceId) {
        return response.status(400).json({
          success: false,
          message: 'ID du service requis'
        });
      }

      if (!prices || !Array.isArray(prices) || prices.length === 0) {
        return response.status(400).json({
          success: false,
          message: 'Liste des prix requis (format: [{produit_id, nouveau_prix, ancien_prix}])'
        });
      }

      // Validation des données de prix
      for (const priceData of prices) {
        if (!priceData.produit_id || priceData.nouveau_prix === undefined) {
          return response.status(400).json({
            success: false,
            message: 'Chaque élément doit contenir produit_id et nouveau_prix'
          });
        }

        if (priceData.nouveau_prix < 0) {
          return response.status(400).json({
            success: false,
            message: 'Les prix ne peuvent pas être négatifs'
          });
        }
      }

      logger.info('Mise à jour en lot des prix', { serviceId, pricesCount: prices.length });

      const result = await MenuService.bulkUpdatePrices(parseInt(serviceId), prices);
      
      response.json(result);
    } catch (error) {
      logger.error('Erreur mise à jour en lot des prix:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la mise à jour des prix'
      });
    }
  }

  /**
   * GET /api/menu/service/:serviceId/search
   * Rechercher des produits dans le menu
   */
  static async searchProducts(request, response) {
    try {
      const { serviceId } = request.params;
      const { q: searchTerm } = request.query;

      if (!serviceId) {
        return response.status(400).json({
          success: false,
          message: 'ID du service requis'
        });
      }

      if (!searchTerm || searchTerm.trim().length < 2) {
        return response.status(400).json({
          success: false,
          message: 'Terme de recherche requis (minimum 2 caractères)'
        });
      }

      logger.info('Recherche produits menu', { serviceId, searchTerm });

      const result = await MenuService.searchProducts(parseInt(serviceId), searchTerm.trim());
      
      response.json(result);
    } catch (error) {
      logger.error('Erreur recherche produits menu:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la recherche de produits'
      });
    }
  }

  /**
   * GET /api/menu/product/:productId/availability
   * Vérifier la disponibilité d'un produit spécifique
   */
  static async checkProductAvailability(request, response) {
    try {
      const { productId } = request.params;
      const { quantite = 1 } = request.query;

      if (!productId) {
        return response.status(400).json({
          success: false,
          message: 'ID du produit requis'
        });
      }

      const quantity = parseInt(quantite);
      if (quantity <= 0) {
        return response.status(400).json({
          success: false,
          message: 'Quantité doit être supérieure à 0'
        });
      }

      logger.info('Vérification disponibilité produit', { productId, quantite: quantity });

      const result = await MenuService.checkProductAvailability(parseInt(productId), quantity);
      
      response.json(result);
    } catch (error) {
      logger.error('Erreur vérification disponibilité produit:', error);
      response.status(error.code || 500).json({
        success: false,
        message: error.message || 'Erreur lors de la vérification de disponibilité'
      });
    }
  }
}

module.exports = MenuController;
