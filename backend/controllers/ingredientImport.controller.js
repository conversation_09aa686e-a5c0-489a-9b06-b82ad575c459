const Controller = require('./Controller');
const IngredientImportService = require('../services/ingredientImport.service');
const logger = require('../logger');

/**
 * Contrôleur pour l'import d'ingrédients et inventaire boissons depuis des fichiers Excel
 */
class IngredientImportController extends Controller {

  /**
   * POST /api/ingredient-import/cuisine/:complexeId
   * Importer des ingrédients cuisine depuis un fichier Excel
   */
  static async importCuisineIngredients(request, response) {
    try {
      const { complexeId } = request.params;
      const { options = {} } = request.body;

      logger.info(`Import cuisine ingredients requested for complexe ${complexeId}`);

      // Vérifier qu'un fichier a été uploadé
      if (!request.file) {
        return response.status(400).json({
          success: false,
          message: 'Aucun fichier Excel fourni'
        });
      }

      // Vérifier le type de fichier
      const allowedMimeTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
      ];

      if (!allowedMimeTypes.includes(request.file.mimetype)) {
        return response.status(400).json({
          success: false,
          message: 'Le fichier doit être un fichier Excel (.xlsx ou .xls)'
        });
      }

      // Valider le complexeId
      if (!complexeId || isNaN(parseInt(complexeId))) {
        return response.status(400).json({
          success: false,
          message: 'ID de complexe invalide'
        });
      }

      // Importer les ingrédients
      const result = await IngredientImportService.importCuisineIngredients(
        parseInt(complexeId),
        request.file.buffer,
        options
      );

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Error in importCuisineIngredients:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de l\'import des ingrédients cuisine'
      });
    }
  }

  /**
   * POST /api/ingredient-import/boissons/:complexeId
   * Importer un inventaire boissons depuis un fichier Excel
   */
  static async importBoissonInventory(request, response) {
    try {
      const { complexeId } = request.params;
      const { options = {} } = request.body;

      logger.info(`Import boisson inventory requested for complexe ${complexeId}`);

      // Vérifier qu'un fichier a été uploadé
      if (!request.file) {
        return response.status(400).json({
          success: false,
          message: 'Aucun fichier Excel fourni'
        });
      }

      // Vérifier le type de fichier
      const allowedMimeTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
      ];

      if (!allowedMimeTypes.includes(request.file.mimetype)) {
        return response.status(400).json({
          success: false,
          message: 'Le fichier doit être un fichier Excel (.xlsx ou .xls)'
        });
      }

      // Valider le complexeId
      if (!complexeId || isNaN(parseInt(complexeId))) {
        return response.status(400).json({
          success: false,
          message: 'ID de complexe invalide'
        });
      }

      // Importer l'inventaire boissons
      const result = await IngredientImportService.importBoissonInventory(
        parseInt(complexeId),
        request.file.buffer,
        options
      );

      response.json({
        success: true,
        data: result.data,
        message: result.message
      });

    } catch (error) {
      logger.error('Error in importBoissonInventory:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de l\'import de l\'inventaire boissons'
      });
    }
  }

  /**
   * POST /api/ingredient-import/validate/cuisine
   * Valider un fichier Excel d'ingrédients cuisine sans l'importer
   */
  static async validateCuisineIngredients(request, response) {
    try {
      logger.info('Validate cuisine ingredients requested', {
        hasFile: !!request.file,
        bodyKeys: Object.keys(request.body || {}),
        fileInfo: request.file ? {
          originalname: request.file.originalname,
          mimetype: request.file.mimetype,
          size: request.file.size
        } : null
      });

      // Vérifier qu'un fichier a été uploadé
      if (!request.file) {
        logger.warn('No file received in request');
        return response.status(400).json({
          success: false,
          message: 'Aucun fichier Excel fourni'
        });
      }

      // Parser le fichier Excel
      const XLSX = require('xlsx');
      const workbook = XLSX.read(request.file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);

      // Valider les données
      const validationResult = IngredientImportService.validateCuisineIngredientData(data);

      response.json({
        success: true, // Toujours true car la requête a réussi
        data: {
          is_valid: validationResult.isValid,
          total_rows: data.length,
          valid_rows: validationResult.isValid ? data.length : data.length - validationResult.errors.length,
          errors: validationResult.errors.map(error => ({ message: error, row: 0, field: '' })),
          warnings: [],
          preview_data: data.slice(0, 5), // Aperçu des 5 premières lignes
          suggested_categories: [],
          suggested_suppliers: []
        },
        message: validationResult.isValid
          ? 'Fichier valide, prêt pour l\'import'
          : 'Erreurs de validation détectées'
      });

    } catch (error) {
      logger.error('Error in validateCuisineIngredients:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la validation du fichier'
      });
    }
  }

  /**
   * POST /api/ingredient-import/validate/boissons
   * Valider un fichier Excel d'inventaire boissons sans l'importer
   */
  static async validateBoissonInventory(request, response) {
    try {
      logger.info('Validate boisson inventory requested');

      // Vérifier qu'un fichier a été uploadé
      if (!request.file) {
        return response.status(400).json({
          success: false,
          message: 'Aucun fichier Excel fourni'
        });
      }

      // Parser le fichier Excel
      const XLSX = require('xlsx');
      const workbook = XLSX.read(request.file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);

      // Valider les données
      const validationResult = IngredientImportService.validateBoissonInventoryData(data);

      response.json({
        success: true, // Toujours true car la requête a réussi
        data: {
          is_valid: validationResult.isValid,
          total_rows: data.length,
          valid_rows: validationResult.isValid ? data.length : data.length - validationResult.errors.length,
          errors: validationResult.errors.map(error => ({ message: error, row: 0, field: '' })),
          warnings: [],
          preview_data: data.slice(0, 5), // Aperçu des 5 premières lignes
          suggested_categories: [],
          suggested_suppliers: []
        },
        message: validationResult.isValid
          ? 'Fichier valide, prêt pour l\'import'
          : 'Erreurs de validation détectées'
      });

    } catch (error) {
      logger.error('Error in validateBoissonInventory:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la validation du fichier'
      });
    }
  }

  /**
   * GET /api/ingredient-import/status/:complexeId
   * Obtenir le statut d'import pour un complexe
   */
  static async getImportStatus(request, response) {
    try {
      const { complexeId } = request.params;

      logger.info(`Import status requested for complexe ${complexeId}`);

      // Valider le complexeId
      if (!complexeId || isNaN(parseInt(complexeId))) {
        return response.status(400).json({
          success: false,
          message: 'ID de complexe invalide'
        });
      }

      // Récupérer les statistiques du complexe
      const db = require('../db');
      const client = await db.getClient();

      try {
        // Compter les ingrédients par catégorie
        const ingredientsResult = await client.query(`
          SELECT 
            categorie,
            COUNT(*) as count
          FROM "Ingredients"
          WHERE complexe_id = $1 AND actif = true
          GROUP BY categorie
          ORDER BY categorie
        `, [parseInt(complexeId)]);

        // Compter les boissons par catégorie
        const boissonsResult = await client.query(`
          SELECT 
            categorie,
            COUNT(*) as count
          FROM "InventaireBoissons"
          WHERE complexe_id = $1 AND actif = true
          GROUP BY categorie
          ORDER BY categorie
        `, [parseInt(complexeId)]);

        // Compter les totaux
        const totalIngredientsResult = await client.query(`
          SELECT COUNT(*) as total
          FROM "Ingredients"
          WHERE complexe_id = $1 AND actif = true
        `, [parseInt(complexeId)]);

        const totalBoissonsResult = await client.query(`
          SELECT COUNT(*) as total
          FROM "InventaireBoissons"
          WHERE complexe_id = $1 AND actif = true
        `, [parseInt(complexeId)]);

        const status = {
          complexeId: parseInt(complexeId),
          ingredients: {
            total: parseInt(totalIngredientsResult.rows[0].total),
            byCategory: ingredientsResult.rows,
            hasIngredients: parseInt(totalIngredientsResult.rows[0].total) > 0
          },
          boissons: {
            total: parseInt(totalBoissonsResult.rows[0].total),
            byCategory: boissonsResult.rows,
            hasBoissons: parseInt(totalBoissonsResult.rows[0].total) > 0
          },
          lastImport: null // TODO: Ajouter une table pour tracker les imports
        };

        response.json({
          success: true,
          data: status,
          message: 'Statut d\'import récupéré avec succès'
        });

      } finally {
        client.release();
      }

    } catch (error) {
      logger.error('Error in getImportStatus:', error);
      response.status(500).json({
        success: false,
        message: error.message || 'Erreur lors de la récupération du statut'
      });
    }
  }

  /**
   * GET /api/ingredient-import/template/cuisine
   * Télécharger le template pour ingrédients cuisine
   */
  static async downloadCuisineTemplate(request, response) {
    try {
      const TemplateGeneratorService = require('../services/templateGenerator.service');
      
      const filename = 'cuisine-ingredients-template.xlsx';
      
      // Vérifier si le template existe, sinon le générer
      if (!TemplateGeneratorService.templateExists(filename)) {
        const workbook = TemplateGeneratorService.generateCuisineIngredientTemplate();
        await TemplateGeneratorService.saveTemplate(workbook, filename);
      }

      const filepath = TemplateGeneratorService.getTemplatePath(filename);
      
      response.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      response.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      response.download(filepath, filename);

    } catch (error) {
      logger.error('Error downloading cuisine template:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors du téléchargement du template'
      });
    }
  }

  /**
   * GET /api/ingredient-import/template/boissons
   * Télécharger le template pour inventaire boissons
   */
  static async downloadBoissonTemplate(request, response) {
    try {
      const TemplateGeneratorService = require('../services/templateGenerator.service');
      
      const filename = 'boisson-inventory-template.xlsx';
      
      // Vérifier si le template existe, sinon le générer
      if (!TemplateGeneratorService.templateExists(filename)) {
        const workbook = TemplateGeneratorService.generateBoissonInventoryTemplate();
        await TemplateGeneratorService.saveTemplate(workbook, filename);
      }

      const filepath = TemplateGeneratorService.getTemplatePath(filename);
      
      response.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      response.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      response.download(filepath, filename);

    } catch (error) {
      logger.error('Error downloading boisson template:', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors du téléchargement du template'
      });
    }
  }
}

module.exports = IngredientImportController;
