const POSService = require('../services/pos.service');
const logger = require('../logger');

class POSController {
  // Récupérer tous les points de vente d'un complexe
  static async getAllPOS(request, response) {
    try {
      const { complexeId } = request.query;
      
      if (!complexeId) {
        return response.status(400).json({
          success: false,
          message: 'L\'ID du complexe est requis'
        });
      }

      logger.info('Consultation liste des points de vente', { complexeId });
      
      const result = await POSService.getAllPOS(complexeId);
      
      if (result.success) {
        response.json(result);
      } else {
        response.status(result.code || 500).json(result);
      }
    } catch (error) {
      logger.error('Erreur consultation liste des points de vente', error);
      response.status(500).json({ 
        success: false,
        message: 'Erreur lors de la consultation de la liste des points de vente'
      });
    }
  }

  // Récupérer les points de vente d'un service
  static async getPOSByService(request, response) {
    try {
      const { serviceId } = request.params;
      
      logger.info('Consultation points de vente par service', { serviceId });
      
      const result = await POSService.getPOSByService(serviceId);
      
      if (result.success) {
        response.json(result);
      } else {
        response.status(result.code || 500).json(result);
      }
    } catch (error) {
      logger.error('Erreur consultation points de vente par service', error);
      response.status(500).json({ 
        success: false,
        message: 'Erreur lors de la consultation des points de vente du service'
      });
    }
  }

  // Récupérer un point de vente par ID
  static async getPOSById(request, response) {
    try {
      const { id } = request.params;
      
      logger.info('Consultation point de vente', { posId: id });
      
      const result = await POSService.getPOSById(id);
      
      if (result.success) {
        response.json(result);
      } else {
        response.status(result.code || 500).json(result);
      }
    } catch (error) {
      logger.error('Erreur consultation point de vente', error);
      response.status(500).json({ 
        success: false,
        message: 'Erreur lors de la consultation du point de vente'
      });
    }
  }

  // Créer un nouveau point de vente
  static async createPOS(request, response) {
    try {
      const posData = request.body;

      // Validation des champs requis
      if (!posData.complexe_id || !posData.service_id || !posData.nom || !posData.emplacement) {
        return response.status(400).json({
          success: false,
          message: 'Les champs complexe_id, service_id, nom et emplacement sont requis'
        });
      }

      logger.info('Création nouveau point de vente', { posData });

      const result = await POSService.createPOS(posData);
      
      if (result.success) {
        response.status(201).json({
          ...result,
          message: 'Point de vente créé avec succès'
        });
      } else {
        response.status(result.code || 500).json(result);
      }
    } catch (error) {
      logger.error('Erreur création point de vente', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la création du point de vente'
      });
    }
  }

  // Mettre à jour un point de vente
  static async updatePOS(request, response) {
    try {
      const { id } = request.params;
      const posData = request.body;

      logger.info('Mise à jour point de vente', { posId: id, posData });

      const result = await POSService.updatePOS(id, posData);
      
      if (result.success) {
        response.json({
          ...result,
          message: 'Point de vente mis à jour avec succès'
        });
      } else {
        response.status(result.code || 500).json(result);
      }
    } catch (error) {
      logger.error('Erreur mise à jour point de vente', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la mise à jour du point de vente'
      });
    }
  }

  // Supprimer un point de vente
  static async deletePOS(request, response) {
    try {
      const { id } = request.params;
      
      logger.info('Suppression point de vente', { posId: id });
      
      const result = await POSService.deletePOS(id);
      
      if (result.success) {
        response.json(result);
      } else {
        response.status(result.code || 500).json(result);
      }
    } catch (error) {
      logger.error('Erreur suppression point de vente', error);
      response.status(500).json({ 
        success: false,
        message: 'Erreur lors de la suppression du point de vente'
      });
    }
  }

  // Créer automatiquement un point de vente pour un service
  static async createPOSForService(request, response) {
    try {
      const { serviceId } = request.params;
      const { complexeId } = request.body;

      if (!complexeId) {
        return response.status(400).json({
          success: false,
          message: 'L\'ID du complexe est requis'
        });
      }

      logger.info('Création automatique point de vente pour service', { serviceId, complexeId });

      const result = await POSService.createPOSForService(serviceId, complexeId);

      if (result.success) {
        response.status(201).json({
          ...result,
          message: 'Point de vente créé automatiquement avec succès'
        });
      } else {
        response.status(result.code || 500).json(result);
      }
    } catch (error) {
      logger.error('Erreur création automatique point de vente', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la création automatique du point de vente'
      });
    }
  }

  // Transférer des fonds entre POS
  static async transferFunds(request, response) {
    try {
      const { fromPosId, toPosId, montant, notes } = request.body;
      const employeId = request.user?.employe_id; // Récupéré du token

      if (!fromPosId || !toPosId || !montant) {
        return response.status(400).json({
          success: false,
          message: 'Les champs fromPosId, toPosId et montant sont requis'
        });
      }

      if (montant <= 0) {
        return response.status(400).json({
          success: false,
          message: 'Le montant doit être positif'
        });
      }

      logger.info('Transfert de fonds entre POS', { fromPosId, toPosId, montant, employeId });

      const result = await POSService.transferFunds(fromPosId, toPosId, montant, notes, employeId);

      if (result.success) {
        response.json(result);
      } else {
        response.status(result.code || 500).json(result);
      }
    } catch (error) {
      logger.error('Erreur transfert fonds', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors du transfert de fonds'
      });
    }
  }

  // Retirer des fonds d'un POS
  static async withdrawFunds(request, response) {
    try {
      const { id } = request.params;
      const { montant, notes } = request.body;
      const employeId = request.user?.employe_id; // Récupéré du token

      if (!montant) {
        return response.status(400).json({
          success: false,
          message: 'Le montant est requis'
        });
      }

      if (montant <= 0) {
        return response.status(400).json({
          success: false,
          message: 'Le montant doit être positif'
        });
      }

      logger.info('Retrait de fonds POS', { posId: id, montant, employeId });

      const result = await POSService.withdrawFunds(id, montant, notes, employeId);

      if (result.success) {
        response.json(result);
      } else {
        response.status(result.code || 500).json(result);
      }
    } catch (error) {
      logger.error('Erreur retrait fonds', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors du retrait de fonds'
      });
    }
  }

  // Ajouter des fonds à un POS
  static async addFunds(request, response) {
    try {
      const { id } = request.params;
      const { montant, notes } = request.body;
      const employeId = request.user?.employe_id; // Récupéré du token

      if (!montant) {
        return response.status(400).json({
          success: false,
          message: 'Le montant est requis'
        });
      }

      if (montant <= 0) {
        return response.status(400).json({
          success: false,
          message: 'Le montant doit être positif'
        });
      }

      logger.info('Ajout de fonds POS', { posId: id, montant, employeId });

      const result = await POSService.addFunds(id, montant, notes, employeId);

      if (result.success) {
        response.json(result);
      } else {
        response.status(result.code || 500).json(result);
      }
    } catch (error) {
      logger.error('Erreur ajout fonds', error);
      response.status(500).json({
        success: false,
        message: 'Erreur lors de l\'ajout de fonds'
      });
    }
  }
}

module.exports = POSController;
