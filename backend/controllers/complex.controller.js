const ComplexService = require('../services/complex.service');
const logger = require('../logger');

const complexController = {
  // Récupérer tous les complexes
  async getAllComplexes(req, res) {
    try {
      const complexes = await ComplexService.getAllComplexes();
      res.json(complexes);
    } catch (error) {
      logger.error('Error in getAllComplexes controller:', error);
      res.status(500).json({
        status: 'error',
        message: 'Erreur lors de la récupération des complexes',
        error: error.message
      });
    }
  },

  // Récupérer un complexe par son ID
  async getComplexById(req, res) {
    try {
      const { id } = req.params;
      const complex = await ComplexService.getComplexById(id);
      res.json(complex);
    } catch (error) {
      logger.error(`Error in getComplexById controller for id ${req.params.id}:`, error);
      if (error.message === 'Complexe non trouvé') {
        return res.status(404).json({
          status: 'error',
          message: 'Complexe non trouvé'
        });
      }
      res.status(500).json({
        status: 'error',
        message: 'Erreur lors de la récupération du complexe',
        error: error.message
      });
    }
  },

  // Créer un nouveau complexe
  async createComplex(req, res) {
    try {
      const complex = await ComplexService.createComplex(req.body);
      res.status(201).json(complex);
    } catch (error) {
      logger.error('Error in createComplex controller:', error);
      res.status(500).json({
        status: 'error',
        message: 'Erreur lors de la création du complexe',
        error: error.message
      });
    }
  },

  // Mettre à jour un complexe
  async updateComplex(req, res) {
    try {
      const { id } = req.params;
      const complex = await ComplexService.updateComplex(id, req.body);
      res.json(complex);
    } catch (error) {
      logger.error(`Error in updateComplex controller for id ${req.params.id}:`, error);
      if (error.message === 'Complexe non trouvé') {
        return res.status(404).json({
          status: 'error',
          message: 'Complexe non trouvé'
        });
      }
      res.status(500).json({
        status: 'error',
        message: 'Erreur lors de la mise à jour du complexe',
        error: error.message
      });
    }
  },

  // Supprimer un complexe
  async deleteComplex(req, res) {
    try {
      const { id } = req.params;
      await ComplexService.deleteComplex(id);
      res.json({
        status: 'success',
        message: 'Complexe supprimé avec succès'
      });
    } catch (error) {
      logger.error(`Error in deleteComplex controller for id ${req.params.id}:`, error);
      if (error.message === 'Complexe non trouvé') {
        return res.status(404).json({
          status: 'error',
          message: 'Complexe non trouvé'
        });
      }
      res.status(500).json({
        status: 'error',
        message: 'Erreur lors de la suppression du complexe',
        error: error.message
      });
    }
  }
};

module.exports = complexController; 