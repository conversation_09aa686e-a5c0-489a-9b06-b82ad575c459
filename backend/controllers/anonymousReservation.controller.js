const Controller = require('./Controller');
const AnonymousReservationService = require('../services/anonymousReservation.service');
const AnonymousValidationService = require('../services/anonymousValidation.service');
const logger = require('../logger');

/**
 * Contrôleur pour gérer les réservations anonymes
 * Toutes les routes publiques pour les clients anonymes
 */
class AnonymousReservationController extends Controller {

  /**
   * Créer une demande de réservation anonyme
   * POST /api/reservations-anonymes/demande
   */
  static async createDemandeReservationAnonyme(req, res) {
    try {
      logger.info('Création demande réservation anonyme', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        body: { ...req.body, client_info: 'MASKED' }
      });

      // Nettoyer et valider les données d'entrée
      const sanitizedData = AnonymousValidationService.sanitizeInput(req.body);
      
      // Validation des paramètres
      const validation = AnonymousValidationService.validateAnonymousReservationParams(sanitizedData);
      if (!validation.valid) {
        return res.status(400).json({
          success: false,
          message: 'Données invalides',
          errors: validation.errors
        });
      }

      // Créer la réservation anonyme
      const result = await AnonymousReservationService.createDemandeReservationAnonyme(sanitizedData);
      
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      // Masquer les informations sensibles dans la réponse
      const response = {
        ...result,
        data: {
          ...result.data,
          // Ne pas exposer l'ID client ou d'autres infos sensibles
          client_id: undefined,
          qr_code: undefined // Le QR code sera généré côté client si nécessaire
        }
      };

      res.status(201).json(response);

    } catch (error) {
      logger.error('Erreur contrôleur création demande réservation anonyme:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la création de la demande de réservation'
      });
    }
  }

  /**
   * Consulter une réservation anonyme par code d'accès
   * GET /api/reservations-anonymes/:codeAcces
   */
  static async getReservationAnonyme(req, res) {
    try {
      const { codeAcces } = req.params;

      logger.info('Consultation réservation anonyme', {
        code_acces: codeAcces.substring(0, 8) + '***', // Masquer une partie du code dans les logs
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      // Valider le format du code d'accès
      const codeValidation = AnonymousValidationService.validateAccessCode(codeAcces);
      if (!codeValidation.valid) {
        return res.status(400).json({
          success: false,
          message: codeValidation.error
        });
      }

      // Récupérer la réservation
      const result = await AnonymousReservationService.getReservationByAccessCode(codeAcces, req);
      
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      // Masquer les informations sensibles
      const response = {
        ...result,
        data: {
          ...result.data,
          // Masquer les informations sensibles du client
          client_id: undefined,
          code_acces_anonyme: undefined // Ne pas exposer le code complet
        }
      };

      res.json(response);

    } catch (error) {
      logger.error('Erreur contrôleur consultation réservation anonyme:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la consultation de la réservation'
      });
    }
  }

  /**
   * Modifier une réservation anonyme (fonctionnalités limitées)
   * PATCH /api/reservations-anonymes/:codeAcces
   */
  static async updateReservationAnonyme(req, res) {
    try {
      const { codeAcces } = req.params;

      logger.info('Modification réservation anonyme', {
        code_acces: codeAcces.substring(0, 8) + '***',
        ip: req.ip,
        modifications: Object.keys(req.body)
      });

      // Valider le format du code d'accès
      const codeValidation = AnonymousValidationService.validateAccessCode(codeAcces);
      if (!codeValidation.valid) {
        return res.status(400).json({
          success: false,
          message: codeValidation.error
        });
      }

      // Nettoyer et valider les paramètres de modification
      const sanitizedData = AnonymousValidationService.sanitizeInput(req.body);
      const validation = AnonymousValidationService.validateUpdateParams(sanitizedData);
      
      if (!validation.valid) {
        return res.status(400).json({
          success: false,
          message: 'Paramètres de modification invalides',
          errors: validation.errors
        });
      }

      // Modifier la réservation
      const result = await AnonymousReservationService.updateReservationAnonyme(codeAcces, sanitizedData, req);
      
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      res.json(result);

    } catch (error) {
      logger.error('Erreur contrôleur modification réservation anonyme:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la modification de la réservation'
      });
    }
  }

  /**
   * Annuler une réservation anonyme
   * DELETE /api/reservations-anonymes/:codeAcces
   */
  static async cancelReservationAnonyme(req, res) {
    try {
      const { codeAcces } = req.params;

      logger.info('Annulation réservation anonyme', {
        code_acces: codeAcces.substring(0, 8) + '***',
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      // Valider le format du code d'accès
      const codeValidation = AnonymousValidationService.validateAccessCode(codeAcces);
      if (!codeValidation.valid) {
        return res.status(400).json({
          success: false,
          message: codeValidation.error
        });
      }

      // Annuler la réservation
      const result = await AnonymousReservationService.cancelReservationAnonyme(codeAcces, req);
      
      if (!result.success) {
        return res.status(result.code || 500).json(result);
      }

      res.json({
        success: true,
        message: 'Réservation annulée avec succès',
        data: {
          numero_reservation: result.data.numero_reservation,
          statut: result.data.statut
        }
      });

    } catch (error) {
      logger.error('Erreur contrôleur annulation réservation anonyme:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'annulation de la réservation'
      });
    }
  }

  /**
   * Valider un code d'accès (utilitaire pour le frontend)
   * POST /api/reservations-anonymes/validate-code
   */
  static async validateAccessCode(req, res) {
    try {
      const { code_acces } = req.body;

      if (!code_acces) {
        return res.status(400).json({
          success: false,
          message: 'Code d\'accès requis'
        });
      }

      // Valider le format
      const formatValidation = AnonymousValidationService.validateAccessCode(code_acces);
      if (!formatValidation.valid) {
        return res.status(400).json({
          success: false,
          message: formatValidation.error
        });
      }

      // Valider l'existence et la validité
      const result = await AnonymousReservationService.validateAccessCode(code_acces);
      
      res.json({
        success: result.success,
        valid: result.success,
        message: result.success ? 'Code valide' : result.message
      });

    } catch (error) {
      logger.error('Erreur validation code d\'accès:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la validation du code'
      });
    }
  }

  /**
   * Récupérer les statistiques publiques (pour affichage général)
   * GET /api/reservations-anonymes/stats/public
   */
  static async getPublicStats(req, res) {
    try {
      // Statistiques générales non sensibles
      const result = await AnonymousReservationService.getAnonymousReservationStats();
      
      if (!result.success) {
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération des statistiques'
        });
      }

      // Ne retourner que des statistiques générales non sensibles
      const publicStats = {
        total_reservations: result.data.total_reservations,
        taux_confirmation: result.data.confirmees / result.data.total_reservations * 100,
        montant_moyen: result.data.montant_moyen,
        service_actif: result.data.total_reservations > 0
      };

      res.json({
        success: true,
        data: publicStats
      });

    } catch (error) {
      logger.error('Erreur récupération statistiques publiques:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des statistiques'
      });
    }
  }

  /**
   * Vérifier la disponibilité du service pour un complexe
   * GET /api/reservations-anonymes/availability/:complexeId
   */
  static async checkServiceAvailability(req, res) {
    try {
      const { complexeId } = req.params;

      if (!complexeId || isNaN(complexeId)) {
        return res.status(400).json({
          success: false,
          message: 'ID de complexe invalide'
        });
      }

      // Vérifier la configuration du complexe
      const AnonymousConfigService = require('../services/anonymousConfig.service');
      const configResult = await AnonymousConfigService.getConfigurationComplexe(parseInt(complexeId));
      
      if (!configResult.success) {
        return res.json({
          success: true,
          available: false,
          message: 'Service non configuré pour ce complexe'
        });
      }

      res.json({
        success: true,
        available: configResult.data.actif,
        configuration: {
          autoriser_modification: configResult.data.autoriser_modification,
          autoriser_annulation: configResult.data.autoriser_annulation,
          delai_annulation_heures: configResult.data.delai_annulation_heures,
          pseudonyme_obligatoire: configResult.data.pseudonyme_obligatoire
        }
      });

    } catch (error) {
      logger.error('Erreur vérification disponibilité service:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification de la disponibilité'
      });
    }
  }
}

module.exports = AnonymousReservationController;
