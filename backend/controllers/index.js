// Contrôleurs existants
const AuthController = require('./auth.controller');
const ReservationController = require('./reservation.controller');
const TicketController = require('./ticket.controller');
const TransactionController = require('./transaction.controller');
const ChambreController = require('./chambre.controller');
const DisponibiliteController = require('./disponibilite.controller');
const EmployeeController = require('./employee.controller');
const ComplexController = require('./complex.controller');
const RoleController = require('./role.controller');
const PaiementController = require('./paiement.controller');
const StatistiqueController = require('./statistique.controller');
const NotificationController = require('./notification.controller');
const ClientController = require('./client.controller');
const TracabiliteController = require('./tracabilite.controller');

// Nouveaux contrôleurs pour le système d'inventaire
const UploadController = require('./upload.controller');
const InventaireController = require('./inventaire.controller');
const RecetteController = require('./recette.controller');
const ImportController = require('./import.controller');
const TemplateController = require('./template.controller');

// Contrôleurs POS Restaurant/Bar
const TableController = require('./table.controller');
const MenuController = require('./menu.controller');
const CommandeController = require('./commande.controller');
const POSAnalyticsController = require('./posAnalytics.controller');

module.exports = {
  // Contrôleurs existants
  AuthController,
  ReservationController,
  TicketController,
  TransactionController,
  ChambreController,
  DisponibiliteController,
  EmployeeController,
  ComplexController,
  RoleController,
  PaiementController,
  StatistiqueController,
  NotificationController,
  ClientController,
  TracabiliteController,

  // Nouveaux contrôleurs d'inventaire
  UploadController,
  InventaireController,
  RecetteController,
  ImportController,
  TemplateController,

  // Contrôleurs POS Restaurant/Bar
  TableController,
  MenuController,
  CommandeController,
  POSAnalyticsController
};
