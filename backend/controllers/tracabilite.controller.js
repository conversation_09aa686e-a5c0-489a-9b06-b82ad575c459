const TracabiliteService = require('../services/tracabilite.service');
const logger = require('../logger');

class TracabiliteController {
    static async getHistoriqueEntite(req, res) {
        try {
            const { entiteId } = req.params;
            const historique = await TracabiliteService.getHistoriqueEntite(entiteId);
            res.json(historique);
        } catch (error) {
            logger.error('Erreur lors de la récupération de l\'historique de l\'entité:', error);
            res.status(500).json({ message: 'Erreur lors de la récupération de l\'historique de l\'entité' });
        }
    }

    static async getHistoriquePaiements(req, res) {
        try {
            const { reservationId } = req.params;
            const historique = await TracabiliteService.getHistoriquePaiements(reservationId);
            res.json(historique);
        } catch (error) {
            logger.error('Erreur lors de la récupération de l\'historique des paiements:', error);
            res.status(500).json({ message: 'Erreur lors de la récupération de l\'historique des paiements' });
        }
    }

    static async getHistoriqueTickets(req, res) {
        try {
            const { reservationId } = req.params;
            const historique = await TracabiliteService.getHistoriqueTickets(reservationId);
            res.json(historique);
        } catch (error) {
            logger.error('Erreur lors de la récupération de l\'historique des tickets:', error);
            res.status(500).json({ message: 'Erreur lors de la récupération de l\'historique des tickets' });
        }
    }

    static async getHistoriqueActions(req, res) {
        try {
            const { entiteId } = req.params;
            const historique = await TracabiliteService.getHistoriqueActions(entiteId);
            res.json(historique);
        } catch (error) {
            logger.error('Erreur lors de la récupération de l\'historique des actions:', error);
            res.status(500).json({ message: 'Erreur lors de la récupération de l\'historique des actions' });
        }
    }

    static async getHistoriqueModifications(req, res) {
        try {
            const { reservationId } = req.params;
            const historique = await TracabiliteService.getHistoriqueModifications(reservationId);
            res.json(historique);
        } catch (error) {
            logger.error('Erreur lors de la récupération de l\'historique des modifications:', error);
            res.status(500).json({ message: 'Erreur lors de la récupération de l\'historique des modifications' });
        }
    }

    static async getHistoriqueUtilisations(req, res) {
        try {
            const { ticketId } = req.params;
            const historique = await TracabiliteService.getHistoriqueUtilisations(ticketId);
            res.json(historique);
        } catch (error) {
            logger.error('Erreur lors de la récupération de l\'historique des utilisations:', error);
            res.status(500).json({ message: 'Erreur lors de la récupération de l\'historique des utilisations' });
        }
    }

    static async getAuditLog(req, res) {
        try {
            const { dateDebut, dateFin, type } = req.query;
            const logs = await TracabiliteService.getAuditLog(dateDebut, dateFin, type);
            res.json(logs);
        } catch (error) {
            logger.error('Erreur lors de la récupération des logs d\'audit:', error);
            res.status(500).json({ message: 'Erreur lors de la récupération des logs d\'audit' });
        }
    }

    static async getStatistiquesActions(req, res) {
        try {
            const { dateDebut, dateFin } = req.query;
            const statistiques = await TracabiliteService.getStatistiquesActions(dateDebut, dateFin);
            res.json(statistiques);
        } catch (error) {
            logger.error('Erreur lors de la récupération des statistiques d\'actions:', error);
            res.status(500).json({ message: 'Erreur lors de la récupération des statistiques d\'actions' });
        }
    }

    static async genererRapports(req, res) {
        try {
            const { type, dateDebut, dateFin } = req.query;
            const rapport = await TracabiliteService.genererRapports(type, dateDebut, dateFin);
            res.json(rapport);
        } catch (error) {
            logger.error('Erreur lors de la génération des rapports:', error);
            res.status(500).json({ message: 'Erreur lors de la génération des rapports' });
        }
    }
}

module.exports = TracabiliteController; 