openapi: 3.0.0
info:
  contact:
    name: API Generator
  description: REST API automatically generated from PostgreSQL schema in input.sql
  license:
    name: MIT
  title: API Generated from input.sql
  version: 1.0.0
servers:
- url: /
paths:
  /utilisateurssuperadmin:
    get:
      operationId: listUtilisateursSuperAdmin
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UtilisateursSuperAdmin'
                type: array
          description: Array of UtilisateursSuperAdmin items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all UtilisateursSuperAdmin
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createUtilisateursSuperAdmin
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UtilisateursSuperAdmin'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UtilisateursSuperAdmin'
          description: Created UtilisateursSuperAdmin
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new UtilisateursSuperAdmin
      x-eov-operation-handler: controllers/DefaultController
  /utilisateurssuperadmin/{id}:
    delete:
      operationId: deleteUtilisateursSuperAdmin
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a UtilisateursSuperAdmin
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getUtilisateursSuperAdmin
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UtilisateursSuperAdmin'
          description: UtilisateursSuperAdmin item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific UtilisateursSuperAdmin by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateUtilisateursSuperAdmin
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UtilisateursSuperAdmin'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UtilisateursSuperAdmin'
          description: Updated UtilisateursSuperAdmin
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a UtilisateursSuperAdmin partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceUtilisateursSuperAdmin
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UtilisateursSuperAdmin'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UtilisateursSuperAdmin'
          description: Updated UtilisateursSuperAdmin
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a UtilisateursSuperAdmin
      x-eov-operation-handler: controllers/DefaultController
  /utilisateurssuperadmin/{id}/chaineshotelieres:
    get:
      operationId: getUtilisateursSuperAdminChainesHotelieres
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ChainesHotelieres'
                type: array
          description: Array of related ChainesHotelieres items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get ChainesHotelieres items related to UtilisateursSuperAdmin
      x-eov-operation-handler: controllers/DefaultController
  /chaineshotelieres:
    get:
      operationId: listChainesHotelieres
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ChainesHotelieres'
                type: array
          description: Array of ChainesHotelieres items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all ChainesHotelieres
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createChainesHotelieres
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChainesHotelieres'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChainesHotelieres'
          description: Created ChainesHotelieres
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new ChainesHotelieres
      x-eov-operation-handler: controllers/DefaultController
  /chaineshotelieres/{id}:
    delete:
      operationId: deleteChainesHotelieres
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a ChainesHotelieres
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getChainesHotelieres
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChainesHotelieres'
          description: ChainesHotelieres item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific ChainesHotelieres by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateChainesHotelieres
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChainesHotelieres'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChainesHotelieres'
          description: Updated ChainesHotelieres
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a ChainesHotelieres partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceChainesHotelieres
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChainesHotelieres'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChainesHotelieres'
          description: Updated ChainesHotelieres
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a ChainesHotelieres
      x-eov-operation-handler: controllers/DefaultController
  /chaineshotelieres/{id}/adminschaine:
    get:
      operationId: getChainesHotelieresAdminsChaine
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/AdminsChaine'
                type: array
          description: Array of related AdminsChaine items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get AdminsChaine items related to ChainesHotelieres
      x-eov-operation-handler: controllers/DefaultController
  /chaineshotelieres/{id}/complexeshoteliers:
    get:
      operationId: getChainesHotelieresComplexesHoteliers
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ComplexesHoteliers'
                type: array
          description: Array of related ComplexesHoteliers items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get ComplexesHoteliers items related to ChainesHotelieres
      x-eov-operation-handler: controllers/DefaultController
  /chaineshotelieres/{id}/categoriesproduits:
    get:
      operationId: getChainesHotelieresCategoriesProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/CategoriesProduits'
                type: array
          description: Array of related CategoriesProduits items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get CategoriesProduits items related to ChainesHotelieres
      x-eov-operation-handler: controllers/DefaultController
  /chaineshotelieres/{id}/produits:
    get:
      operationId: getChainesHotelieresProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Produits'
                type: array
          description: Array of related Produits items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Produits items related to ChainesHotelieres
      x-eov-operation-handler: controllers/DefaultController
  /chaineshotelieres/{id}/clients:
    get:
      operationId: getChainesHotelieresClients
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Clients'
                type: array
          description: Array of related Clients items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Clients items related to ChainesHotelieres
      x-eov-operation-handler: controllers/DefaultController
  /chaineshotelieres/{id}/fidelite:
    get:
      operationId: getChainesHotelieresFidelite
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Fidelite'
                type: array
          description: Array of related Fidelite items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Fidelite items related to ChainesHotelieres
      x-eov-operation-handler: controllers/DefaultController
  /chaineshotelieres/{id}/codespromo:
    get:
      operationId: getChainesHotelieresCodesPromo
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/CodesPromo'
                type: array
          description: Array of related CodesPromo items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get CodesPromo items related to ChainesHotelieres
      x-eov-operation-handler: controllers/DefaultController
  /chaineshotelieres/{id}/utilisationscodes:
    get:
      operationId: getChainesHotelieresUtilisationsCodes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UtilisationsCodes'
                type: array
          description: Array of related UtilisationsCodes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get UtilisationsCodes items related to ChainesHotelieres
      x-eov-operation-handler: controllers/DefaultController
  /chaineshotelieres/{id}/fournisseurs:
    get:
      operationId: getChainesHotelieresFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Fournisseurs'
                type: array
          description: Array of related Fournisseurs items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Fournisseurs items related to ChainesHotelieres
      x-eov-operation-handler: controllers/DefaultController
  /chaineshotelieres/{id}/commandesfournisseurs:
    get:
      operationId: getChainesHotelieresCommandesFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/CommandesFournisseurs'
                type: array
          description: Array of related CommandesFournisseurs items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get CommandesFournisseurs items related to ChainesHotelieres
      x-eov-operation-handler: controllers/DefaultController
  /chaineshotelieres/{id}/mouvementsstock:
    get:
      operationId: getChainesHotelieresMouvementsStock
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/MouvementsStock'
                type: array
          description: Array of related MouvementsStock items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get MouvementsStock items related to ChainesHotelieres
      x-eov-operation-handler: controllers/DefaultController
  /chaineshotelieres/{id}/inventaires:
    get:
      operationId: getChainesHotelieresInventaires
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Inventaires'
                type: array
          description: Array of related Inventaires items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Inventaires items related to ChainesHotelieres
      x-eov-operation-handler: controllers/DefaultController
  /adminschaine:
    get:
      operationId: listAdminsChaine
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/AdminsChaine'
                type: array
          description: Array of AdminsChaine items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all AdminsChaine
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createAdminsChaine
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminsChaine'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminsChaine'
          description: Created AdminsChaine
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new AdminsChaine
      x-eov-operation-handler: controllers/DefaultController
  /adminschaine/{id}:
    delete:
      operationId: deleteAdminsChaine
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a AdminsChaine
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getAdminsChaine
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminsChaine'
          description: AdminsChaine item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific AdminsChaine by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateAdminsChaine
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminsChaine'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminsChaine'
          description: Updated AdminsChaine
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a AdminsChaine partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceAdminsChaine
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminsChaine'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminsChaine'
          description: Updated AdminsChaine
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a AdminsChaine
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers:
    get:
      operationId: listComplexesHoteliers
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ComplexesHoteliers'
                type: array
          description: Array of ComplexesHoteliers items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createComplexesHoteliers
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ComplexesHoteliers'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComplexesHoteliers'
          description: Created ComplexesHoteliers
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}:
    delete:
      operationId: deleteComplexesHoteliers
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getComplexesHoteliers
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComplexesHoteliers'
          description: ComplexesHoteliers item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific ComplexesHoteliers by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateComplexesHoteliers
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ComplexesHoteliers'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComplexesHoteliers'
          description: Updated ComplexesHoteliers
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a ComplexesHoteliers partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceComplexesHoteliers
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ComplexesHoteliers'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComplexesHoteliers'
          description: Updated ComplexesHoteliers
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/servicescomplexe:
    get:
      operationId: getComplexesHoteliersServicesComplexe
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ServicesComplexe'
                type: array
          description: Array of related ServicesComplexe items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get ServicesComplexe items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/adminscomplexe:
    get:
      operationId: getComplexesHoteliersAdminsComplexe
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/AdminsComplexe'
                type: array
          description: Array of related AdminsComplexe items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get AdminsComplexe items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/rolescomplexe:
    get:
      operationId: getComplexesHoteliersRolesComplexe
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/RolesComplexe'
                type: array
          description: Array of related RolesComplexe items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get RolesComplexe items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/employes:
    get:
      operationId: getComplexesHoteliersEmployes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Employes'
                type: array
          description: Array of related Employes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Employes items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/pointsdevente:
    get:
      operationId: getComplexesHoteliersPointsDeVente
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/PointsDeVente'
                type: array
          description: Array of related PointsDeVente items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get PointsDeVente items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/sessionscaisse:
    get:
      operationId: getComplexesHoteliersSessionsCaisse
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/SessionsCaisse'
                type: array
          description: Array of related SessionsCaisse items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get SessionsCaisse items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/categoriesproduits:
    get:
      operationId: getComplexesHoteliersCategoriesProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/CategoriesProduits'
                type: array
          description: Array of related CategoriesProduits items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get CategoriesProduits items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/produits:
    get:
      operationId: getComplexesHoteliersProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Produits'
                type: array
          description: Array of related Produits items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Produits items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/prixproduits:
    get:
      operationId: getComplexesHoteliersPrixProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/PrixProduits'
                type: array
          description: Array of related PrixProduits items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get PrixProduits items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/chambres:
    get:
      operationId: getComplexesHoteliersChambres
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Chambres'
                type: array
          description: Array of related Chambres items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Chambres items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/clients:
    get:
      operationId: getComplexesHoteliersClients
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Clients'
                type: array
          description: Array of related Clients items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Clients items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/reservations:
    get:
      operationId: getComplexesHoteliersReservations
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Reservations'
                type: array
          description: Array of related Reservations items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Reservations items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/transactionspos:
    get:
      operationId: getComplexesHoteliersTransactionsPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/TransactionsPOS'
                type: array
          description: Array of related TransactionsPOS items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get TransactionsPOS items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/codespromo:
    get:
      operationId: getComplexesHoteliersCodesPromo
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/CodesPromo'
                type: array
          description: Array of related CodesPromo items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get CodesPromo items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/utilisationscodes:
    get:
      operationId: getComplexesHoteliersUtilisationsCodes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UtilisationsCodes'
                type: array
          description: Array of related UtilisationsCodes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get UtilisationsCodes items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/fournisseurs:
    get:
      operationId: getComplexesHoteliersFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Fournisseurs'
                type: array
          description: Array of related Fournisseurs items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Fournisseurs items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/commandesfournisseurs:
    get:
      operationId: getComplexesHoteliersCommandesFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/CommandesFournisseurs'
                type: array
          description: Array of related CommandesFournisseurs items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get CommandesFournisseurs items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/mouvementsstock:
    get:
      operationId: getComplexesHoteliersMouvementsStock
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/MouvementsStock'
                type: array
          description: Array of related MouvementsStock items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get MouvementsStock items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/inventaires:
    get:
      operationId: getComplexesHoteliersInventaires
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Inventaires'
                type: array
          description: Array of related Inventaires items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Inventaires items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/tables:
    get:
      operationId: getComplexesHoteliersTables
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Tables'
                type: array
          description: Array of related Tables items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Tables items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/reservationstables:
    get:
      operationId: getComplexesHoteliersReservationsTables
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ReservationsTables'
                type: array
          description: Array of related ReservationsTables items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get ReservationsTables items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/commandes:
    get:
      operationId: getComplexesHoteliersCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Commandes'
                type: array
          description: Array of related Commandes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Commandes items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /complexeshoteliers/{id}/detailscommandes:
    get:
      operationId: getComplexesHoteliersDetailsCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/DetailsCommandes'
                type: array
          description: Array of related DetailsCommandes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get DetailsCommandes items related to ComplexesHoteliers
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe:
    get:
      operationId: listServicesComplexe
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ServicesComplexe'
                type: array
          description: Array of ServicesComplexe items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createServicesComplexe
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServicesComplexe'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServicesComplexe'
          description: Created ServicesComplexe
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}:
    delete:
      operationId: deleteServicesComplexe
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getServicesComplexe
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServicesComplexe'
          description: ServicesComplexe item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific ServicesComplexe by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateServicesComplexe
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServicesComplexe'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServicesComplexe'
          description: Updated ServicesComplexe
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a ServicesComplexe partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceServicesComplexe
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServicesComplexe'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServicesComplexe'
          description: Updated ServicesComplexe
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/rolescomplexe:
    get:
      operationId: getServicesComplexeRolesComplexe
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/RolesComplexe'
                type: array
          description: Array of related RolesComplexe items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get RolesComplexe items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/employes:
    get:
      operationId: getServicesComplexeEmployes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Employes'
                type: array
          description: Array of related Employes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Employes items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/pointsdevente:
    get:
      operationId: getServicesComplexePointsDeVente
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/PointsDeVente'
                type: array
          description: Array of related PointsDeVente items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get PointsDeVente items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/sessionscaisse:
    get:
      operationId: getServicesComplexeSessionsCaisse
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/SessionsCaisse'
                type: array
          description: Array of related SessionsCaisse items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get SessionsCaisse items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/categoriesproduits:
    get:
      operationId: getServicesComplexeCategoriesProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/CategoriesProduits'
                type: array
          description: Array of related CategoriesProduits items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get CategoriesProduits items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/produits:
    get:
      operationId: getServicesComplexeProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Produits'
                type: array
          description: Array of related Produits items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Produits items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/prixproduits:
    get:
      operationId: getServicesComplexePrixProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/PrixProduits'
                type: array
          description: Array of related PrixProduits items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get PrixProduits items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/chambres:
    get:
      operationId: getServicesComplexeChambres
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Chambres'
                type: array
          description: Array of related Chambres items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Chambres items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/reservations:
    get:
      operationId: getServicesComplexeReservations
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Reservations'
                type: array
          description: Array of related Reservations items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Reservations items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/transactionspos:
    get:
      operationId: getServicesComplexeTransactionsPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/TransactionsPOS'
                type: array
          description: Array of related TransactionsPOS items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get TransactionsPOS items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/codespromo:
    get:
      operationId: getServicesComplexeCodesPromo
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/CodesPromo'
                type: array
          description: Array of related CodesPromo items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get CodesPromo items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/utilisationscodes:
    get:
      operationId: getServicesComplexeUtilisationsCodes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UtilisationsCodes'
                type: array
          description: Array of related UtilisationsCodes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get UtilisationsCodes items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/commandesfournisseurs:
    get:
      operationId: getServicesComplexeCommandesFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/CommandesFournisseurs'
                type: array
          description: Array of related CommandesFournisseurs items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get CommandesFournisseurs items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/mouvementsstock:
    get:
      operationId: getServicesComplexeMouvementsStock
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/MouvementsStock'
                type: array
          description: Array of related MouvementsStock items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get MouvementsStock items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/inventaires:
    get:
      operationId: getServicesComplexeInventaires
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Inventaires'
                type: array
          description: Array of related Inventaires items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Inventaires items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/tables:
    get:
      operationId: getServicesComplexeTables
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Tables'
                type: array
          description: Array of related Tables items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Tables items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/reservationstables:
    get:
      operationId: getServicesComplexeReservationsTables
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ReservationsTables'
                type: array
          description: Array of related ReservationsTables items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get ReservationsTables items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/commandes:
    get:
      operationId: getServicesComplexeCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Commandes'
                type: array
          description: Array of related Commandes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Commandes items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /servicescomplexe/{id}/detailscommandes:
    get:
      operationId: getServicesComplexeDetailsCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/DetailsCommandes'
                type: array
          description: Array of related DetailsCommandes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get DetailsCommandes items related to ServicesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /adminscomplexe:
    get:
      operationId: listAdminsComplexe
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/AdminsComplexe'
                type: array
          description: Array of AdminsComplexe items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all AdminsComplexe
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createAdminsComplexe
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminsComplexe'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminsComplexe'
          description: Created AdminsComplexe
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new AdminsComplexe
      x-eov-operation-handler: controllers/DefaultController
  /adminscomplexe/{id}:
    delete:
      operationId: deleteAdminsComplexe
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a AdminsComplexe
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getAdminsComplexe
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminsComplexe'
          description: AdminsComplexe item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific AdminsComplexe by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateAdminsComplexe
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminsComplexe'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminsComplexe'
          description: Updated AdminsComplexe
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a AdminsComplexe partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceAdminsComplexe
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminsComplexe'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminsComplexe'
          description: Updated AdminsComplexe
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a AdminsComplexe
      x-eov-operation-handler: controllers/DefaultController
  /rolescomplexe:
    get:
      operationId: listRolesComplexe
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/RolesComplexe'
                type: array
          description: Array of RolesComplexe items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all RolesComplexe
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createRolesComplexe
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RolesComplexe'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RolesComplexe'
          description: Created RolesComplexe
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new RolesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /rolescomplexe/{id}:
    delete:
      operationId: deleteRolesComplexe
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a RolesComplexe
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getRolesComplexe
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RolesComplexe'
          description: RolesComplexe item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific RolesComplexe by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateRolesComplexe
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RolesComplexe'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RolesComplexe'
          description: Updated RolesComplexe
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a RolesComplexe partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceRolesComplexe
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RolesComplexe'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RolesComplexe'
          description: Updated RolesComplexe
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a RolesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /rolescomplexe/{id}/employes:
    get:
      operationId: getRolesComplexeEmployes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Employes'
                type: array
          description: Array of related Employes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Employes items related to RolesComplexe
      x-eov-operation-handler: controllers/DefaultController
  /employes:
    get:
      operationId: listEmployes
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Employes'
                type: array
          description: Array of Employes items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all Employes
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createEmployes
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Employes'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Employes'
          description: Created Employes
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new Employes
      x-eov-operation-handler: controllers/DefaultController
  /employes/{id}:
    delete:
      operationId: deleteEmployes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a Employes
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getEmployes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Employes'
          description: Employes item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific Employes by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateEmployes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Employes'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Employes'
          description: Updated Employes
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a Employes partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceEmployes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Employes'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Employes'
          description: Updated Employes
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a Employes
      x-eov-operation-handler: controllers/DefaultController
  /employes/{id}/pointsdevente:
    get:
      operationId: getEmployesPointsDeVente
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/PointsDeVente'
                type: array
          description: Array of related PointsDeVente items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get PointsDeVente items related to Employes
      x-eov-operation-handler: controllers/DefaultController
  /employes/{id}/sessionscaisse:
    get:
      operationId: getEmployesSessionsCaisse
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/SessionsCaisse'
                type: array
          description: Array of related SessionsCaisse items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get SessionsCaisse items related to Employes
      x-eov-operation-handler: controllers/DefaultController
  /employes/{id}/reservations:
    get:
      operationId: getEmployesReservations
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Reservations'
                type: array
          description: Array of related Reservations items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Reservations items related to Employes
      x-eov-operation-handler: controllers/DefaultController
  /employes/{id}/transactionspos:
    get:
      operationId: getEmployesTransactionsPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/TransactionsPOS'
                type: array
          description: Array of related TransactionsPOS items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get TransactionsPOS items related to Employes
      x-eov-operation-handler: controllers/DefaultController
  /employes/{id}/utilisationscodes:
    get:
      operationId: getEmployesUtilisationsCodes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UtilisationsCodes'
                type: array
          description: Array of related UtilisationsCodes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get UtilisationsCodes items related to Employes
      x-eov-operation-handler: controllers/DefaultController
  /employes/{id}/commandesfournisseurs:
    get:
      operationId: getEmployesCommandesFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/CommandesFournisseurs'
                type: array
          description: Array of related CommandesFournisseurs items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get CommandesFournisseurs items related to Employes
      x-eov-operation-handler: controllers/DefaultController
  /employes/{id}/mouvementsstock:
    get:
      operationId: getEmployesMouvementsStock
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/MouvementsStock'
                type: array
          description: Array of related MouvementsStock items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get MouvementsStock items related to Employes
      x-eov-operation-handler: controllers/DefaultController
  /employes/{id}/inventaires:
    get:
      operationId: getEmployesInventaires
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Inventaires'
                type: array
          description: Array of related Inventaires items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Inventaires items related to Employes
      x-eov-operation-handler: controllers/DefaultController
  /employes/{id}/reservationstables:
    get:
      operationId: getEmployesReservationsTables
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ReservationsTables'
                type: array
          description: Array of related ReservationsTables items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get ReservationsTables items related to Employes
      x-eov-operation-handler: controllers/DefaultController
  /employes/{id}/commandes:
    get:
      operationId: getEmployesCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Commandes'
                type: array
          description: Array of related Commandes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Commandes items related to Employes
      x-eov-operation-handler: controllers/DefaultController
  /employes/{id}/detailscommandes:
    get:
      operationId: getEmployesDetailsCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/DetailsCommandes'
                type: array
          description: Array of related DetailsCommandes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get DetailsCommandes items related to Employes
      x-eov-operation-handler: controllers/DefaultController
  /pointsdevente:
    get:
      operationId: listPointsDeVente
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/PointsDeVente'
                type: array
          description: Array of PointsDeVente items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all PointsDeVente
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createPointsDeVente
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PointsDeVente'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PointsDeVente'
          description: Created PointsDeVente
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new PointsDeVente
      x-eov-operation-handler: controllers/DefaultController
  /pointsdevente/{id}:
    delete:
      operationId: deletePointsDeVente
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a PointsDeVente
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getPointsDeVente
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PointsDeVente'
          description: PointsDeVente item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific PointsDeVente by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updatePointsDeVente
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PointsDeVente'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PointsDeVente'
          description: Updated PointsDeVente
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a PointsDeVente partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replacePointsDeVente
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PointsDeVente'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PointsDeVente'
          description: Updated PointsDeVente
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a PointsDeVente
      x-eov-operation-handler: controllers/DefaultController
  /pointsdevente/{id}/sessionscaisse:
    get:
      operationId: getPointsDeVenteSessionsCaisse
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/SessionsCaisse'
                type: array
          description: Array of related SessionsCaisse items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get SessionsCaisse items related to PointsDeVente
      x-eov-operation-handler: controllers/DefaultController
  /pointsdevente/{id}/transactionspos:
    get:
      operationId: getPointsDeVenteTransactionsPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/TransactionsPOS'
                type: array
          description: Array of related TransactionsPOS items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get TransactionsPOS items related to PointsDeVente
      x-eov-operation-handler: controllers/DefaultController
  /sessionscaisse:
    get:
      operationId: listSessionsCaisse
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/SessionsCaisse'
                type: array
          description: Array of SessionsCaisse items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all SessionsCaisse
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createSessionsCaisse
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SessionsCaisse'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionsCaisse'
          description: Created SessionsCaisse
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new SessionsCaisse
      x-eov-operation-handler: controllers/DefaultController
  /sessionscaisse/{id}:
    delete:
      operationId: deleteSessionsCaisse
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a SessionsCaisse
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getSessionsCaisse
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionsCaisse'
          description: SessionsCaisse item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific SessionsCaisse by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateSessionsCaisse
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SessionsCaisse'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionsCaisse'
          description: Updated SessionsCaisse
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a SessionsCaisse partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceSessionsCaisse
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SessionsCaisse'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionsCaisse'
          description: Updated SessionsCaisse
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a SessionsCaisse
      x-eov-operation-handler: controllers/DefaultController
  /sessionscaisse/{id}/transactionspos:
    get:
      operationId: getSessionsCaisseTransactionsPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/TransactionsPOS'
                type: array
          description: Array of related TransactionsPOS items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get TransactionsPOS items related to SessionsCaisse
      x-eov-operation-handler: controllers/DefaultController
  /categoriesproduits:
    get:
      operationId: listCategoriesProduits
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/CategoriesProduits'
                type: array
          description: Array of CategoriesProduits items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all CategoriesProduits
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createCategoriesProduits
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategoriesProduits'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategoriesProduits'
          description: Created CategoriesProduits
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new CategoriesProduits
      x-eov-operation-handler: controllers/DefaultController
  /categoriesproduits/{id}:
    delete:
      operationId: deleteCategoriesProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a CategoriesProduits
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getCategoriesProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategoriesProduits'
          description: CategoriesProduits item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific CategoriesProduits by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateCategoriesProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategoriesProduits'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategoriesProduits'
          description: Updated CategoriesProduits
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a CategoriesProduits partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceCategoriesProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategoriesProduits'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategoriesProduits'
          description: Updated CategoriesProduits
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a CategoriesProduits
      x-eov-operation-handler: controllers/DefaultController
  /categoriesproduits/{id}/categoriesproduits:
    get:
      operationId: getCategoriesProduitsCategoriesProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/CategoriesProduits'
                type: array
          description: Array of related CategoriesProduits items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get CategoriesProduits items related to CategoriesProduits
      x-eov-operation-handler: controllers/DefaultController
  /categoriesproduits/{id}/produits:
    get:
      operationId: getCategoriesProduitsProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Produits'
                type: array
          description: Array of related Produits items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Produits items related to CategoriesProduits
      x-eov-operation-handler: controllers/DefaultController
  /produits:
    get:
      operationId: listProduits
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Produits'
                type: array
          description: Array of Produits items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all Produits
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createProduits
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Produits'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Produits'
          description: Created Produits
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new Produits
      x-eov-operation-handler: controllers/DefaultController
  /produits/{id}:
    delete:
      operationId: deleteProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a Produits
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Produits'
          description: Produits item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific Produits by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Produits'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Produits'
          description: Updated Produits
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a Produits partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Produits'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Produits'
          description: Updated Produits
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a Produits
      x-eov-operation-handler: controllers/DefaultController
  /produits/{id}/prixproduits:
    get:
      operationId: getProduitsPrixProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/PrixProduits'
                type: array
          description: Array of related PrixProduits items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get PrixProduits items related to Produits
      x-eov-operation-handler: controllers/DefaultController
  /produits/{id}/lignestransactionpos:
    get:
      operationId: getProduitsLignesTransactionPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/LignesTransactionPOS'
                type: array
          description: Array of related LignesTransactionPOS items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get LignesTransactionPOS items related to Produits
      x-eov-operation-handler: controllers/DefaultController
  /produits/{id}/detailscommandesfournisseurs:
    get:
      operationId: getProduitsDetailsCommandesFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/DetailsCommandesFournisseurs'
                type: array
          description: Array of related DetailsCommandesFournisseurs items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get DetailsCommandesFournisseurs items related to Produits
      x-eov-operation-handler: controllers/DefaultController
  /produits/{id}/mouvementsstock:
    get:
      operationId: getProduitsMouvementsStock
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/MouvementsStock'
                type: array
          description: Array of related MouvementsStock items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get MouvementsStock items related to Produits
      x-eov-operation-handler: controllers/DefaultController
  /produits/{id}/lignesinventaire:
    get:
      operationId: getProduitsLignesInventaire
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/LignesInventaire'
                type: array
          description: Array of related LignesInventaire items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get LignesInventaire items related to Produits
      x-eov-operation-handler: controllers/DefaultController
  /produits/{id}/detailscommandes:
    get:
      operationId: getProduitsDetailsCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/DetailsCommandes'
                type: array
          description: Array of related DetailsCommandes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get DetailsCommandes items related to Produits
      x-eov-operation-handler: controllers/DefaultController
  /prixproduits:
    get:
      operationId: listPrixProduits
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/PrixProduits'
                type: array
          description: Array of PrixProduits items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all PrixProduits
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createPrixProduits
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrixProduits'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrixProduits'
          description: Created PrixProduits
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new PrixProduits
      x-eov-operation-handler: controllers/DefaultController
  /prixproduits/{id}:
    delete:
      operationId: deletePrixProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a PrixProduits
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getPrixProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrixProduits'
          description: PrixProduits item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific PrixProduits by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updatePrixProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrixProduits'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrixProduits'
          description: Updated PrixProduits
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a PrixProduits partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replacePrixProduits
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrixProduits'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrixProduits'
          description: Updated PrixProduits
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a PrixProduits
      x-eov-operation-handler: controllers/DefaultController
  /chambres:
    get:
      operationId: listChambres
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Chambres'
                type: array
          description: Array of Chambres items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all Chambres
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createChambres
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Chambres'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Chambres'
          description: Created Chambres
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new Chambres
      x-eov-operation-handler: controllers/DefaultController
  /chambres/{id}:
    delete:
      operationId: deleteChambres
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a Chambres
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getChambres
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Chambres'
          description: Chambres item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific Chambres by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateChambres
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Chambres'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Chambres'
          description: Updated Chambres
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a Chambres partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceChambres
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Chambres'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Chambres'
          description: Updated Chambres
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a Chambres
      x-eov-operation-handler: controllers/DefaultController
  /chambres/{id}/chambresreservees:
    get:
      operationId: getChambresChambresReservees
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ChambresReservees'
                type: array
          description: Array of related ChambresReservees items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get ChambresReservees items related to Chambres
      x-eov-operation-handler: controllers/DefaultController
  /chambres/{id}/transactionspos:
    get:
      operationId: getChambresTransactionsPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/TransactionsPOS'
                type: array
          description: Array of related TransactionsPOS items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get TransactionsPOS items related to Chambres
      x-eov-operation-handler: controllers/DefaultController
  /chambres/{id}/commandes:
    get:
      operationId: getChambresCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Commandes'
                type: array
          description: Array of related Commandes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Commandes items related to Chambres
      x-eov-operation-handler: controllers/DefaultController
  /clients:
    get:
      operationId: listClients
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Clients'
                type: array
          description: Array of Clients items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all Clients
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createClients
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Clients'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Clients'
          description: Created Clients
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new Clients
      x-eov-operation-handler: controllers/DefaultController
  /clients/{id}:
    delete:
      operationId: deleteClients
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a Clients
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getClients
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Clients'
          description: Clients item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific Clients by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateClients
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Clients'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Clients'
          description: Updated Clients
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a Clients partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceClients
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Clients'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Clients'
          description: Updated Clients
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a Clients
      x-eov-operation-handler: controllers/DefaultController
  /clients/{id}/reservations:
    get:
      operationId: getClientsReservations
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Reservations'
                type: array
          description: Array of related Reservations items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Reservations items related to Clients
      x-eov-operation-handler: controllers/DefaultController
  /clients/{id}/transactionspos:
    get:
      operationId: getClientsTransactionsPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/TransactionsPOS'
                type: array
          description: Array of related TransactionsPOS items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get TransactionsPOS items related to Clients
      x-eov-operation-handler: controllers/DefaultController
  /clients/{id}/fidelite:
    get:
      operationId: getClientsFidelite
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Fidelite'
                type: array
          description: Array of related Fidelite items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Fidelite items related to Clients
      x-eov-operation-handler: controllers/DefaultController
  /clients/{id}/utilisationscodes:
    get:
      operationId: getClientsUtilisationsCodes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UtilisationsCodes'
                type: array
          description: Array of related UtilisationsCodes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get UtilisationsCodes items related to Clients
      x-eov-operation-handler: controllers/DefaultController
  /clients/{id}/reservationstables:
    get:
      operationId: getClientsReservationsTables
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ReservationsTables'
                type: array
          description: Array of related ReservationsTables items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get ReservationsTables items related to Clients
      x-eov-operation-handler: controllers/DefaultController
  /clients/{id}/commandes:
    get:
      operationId: getClientsCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Commandes'
                type: array
          description: Array of related Commandes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Commandes items related to Clients
      x-eov-operation-handler: controllers/DefaultController
  /reservations:
    get:
      operationId: listReservations
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Reservations'
                type: array
          description: Array of Reservations items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all Reservations
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createReservations
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Reservations'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Reservations'
          description: Created Reservations
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new Reservations
      x-eov-operation-handler: controllers/DefaultController
  /reservations/{id}:
    delete:
      operationId: deleteReservations
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a Reservations
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getReservations
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Reservations'
          description: Reservations item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific Reservations by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateReservations
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Reservations'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Reservations'
          description: Updated Reservations
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a Reservations partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceReservations
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Reservations'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Reservations'
          description: Updated Reservations
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a Reservations
      x-eov-operation-handler: controllers/DefaultController
  /reservations/{id}/chambresreservees:
    get:
      operationId: getReservationsChambresReservees
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ChambresReservees'
                type: array
          description: Array of related ChambresReservees items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get ChambresReservees items related to Reservations
      x-eov-operation-handler: controllers/DefaultController
  /reservations/{id}/utilisationscodes:
    get:
      operationId: getReservationsUtilisationsCodes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UtilisationsCodes'
                type: array
          description: Array of related UtilisationsCodes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get UtilisationsCodes items related to Reservations
      x-eov-operation-handler: controllers/DefaultController
  /chambresreservees:
    get:
      operationId: listChambresReservees
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ChambresReservees'
                type: array
          description: Array of ChambresReservees items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all ChambresReservees
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createChambresReservees
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChambresReservees'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChambresReservees'
          description: Created ChambresReservees
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new ChambresReservees
      x-eov-operation-handler: controllers/DefaultController
  /chambresreservees/{id}:
    delete:
      operationId: deleteChambresReservees
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a ChambresReservees
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getChambresReservees
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChambresReservees'
          description: ChambresReservees item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific ChambresReservees by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateChambresReservees
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChambresReservees'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChambresReservees'
          description: Updated ChambresReservees
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a ChambresReservees partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceChambresReservees
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChambresReservees'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChambresReservees'
          description: Updated ChambresReservees
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a ChambresReservees
      x-eov-operation-handler: controllers/DefaultController
  /transactionspos:
    get:
      operationId: listTransactionsPOS
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/TransactionsPOS'
                type: array
          description: Array of TransactionsPOS items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all TransactionsPOS
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createTransactionsPOS
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionsPOS'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionsPOS'
          description: Created TransactionsPOS
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new TransactionsPOS
      x-eov-operation-handler: controllers/DefaultController
  /transactionspos/{id}:
    delete:
      operationId: deleteTransactionsPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a TransactionsPOS
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getTransactionsPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionsPOS'
          description: TransactionsPOS item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific TransactionsPOS by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateTransactionsPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionsPOS'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionsPOS'
          description: Updated TransactionsPOS
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a TransactionsPOS partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceTransactionsPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionsPOS'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionsPOS'
          description: Updated TransactionsPOS
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a TransactionsPOS
      x-eov-operation-handler: controllers/DefaultController
  /transactionspos/{id}/lignestransactionpos:
    get:
      operationId: getTransactionsPOSLignesTransactionPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/LignesTransactionPOS'
                type: array
          description: Array of related LignesTransactionPOS items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get LignesTransactionPOS items related to TransactionsPOS
      x-eov-operation-handler: controllers/DefaultController
  /transactionspos/{id}/paiements:
    get:
      operationId: getTransactionsPOSPaiements
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Paiements'
                type: array
          description: Array of related Paiements items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Paiements items related to TransactionsPOS
      x-eov-operation-handler: controllers/DefaultController
  /transactionspos/{id}/utilisationscodes:
    get:
      operationId: getTransactionsPOSUtilisationsCodes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UtilisationsCodes'
                type: array
          description: Array of related UtilisationsCodes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get UtilisationsCodes items related to TransactionsPOS
      x-eov-operation-handler: controllers/DefaultController
  /lignestransactionpos:
    get:
      operationId: listLignesTransactionPOS
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/LignesTransactionPOS'
                type: array
          description: Array of LignesTransactionPOS items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all LignesTransactionPOS
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createLignesTransactionPOS
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LignesTransactionPOS'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LignesTransactionPOS'
          description: Created LignesTransactionPOS
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new LignesTransactionPOS
      x-eov-operation-handler: controllers/DefaultController
  /lignestransactionpos/{id}:
    delete:
      operationId: deleteLignesTransactionPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a LignesTransactionPOS
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getLignesTransactionPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LignesTransactionPOS'
          description: LignesTransactionPOS item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific LignesTransactionPOS by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateLignesTransactionPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LignesTransactionPOS'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LignesTransactionPOS'
          description: Updated LignesTransactionPOS
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a LignesTransactionPOS partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceLignesTransactionPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LignesTransactionPOS'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LignesTransactionPOS'
          description: Updated LignesTransactionPOS
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a LignesTransactionPOS
      x-eov-operation-handler: controllers/DefaultController
  /paiements:
    get:
      operationId: listPaiements
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Paiements'
                type: array
          description: Array of Paiements items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all Paiements
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createPaiements
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Paiements'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Paiements'
          description: Created Paiements
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new Paiements
      x-eov-operation-handler: controllers/DefaultController
  /paiements/{id}:
    delete:
      operationId: deletePaiements
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a Paiements
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getPaiements
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Paiements'
          description: Paiements item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific Paiements by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updatePaiements
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Paiements'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Paiements'
          description: Updated Paiements
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a Paiements partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replacePaiements
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Paiements'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Paiements'
          description: Updated Paiements
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a Paiements
      x-eov-operation-handler: controllers/DefaultController
  /fidelite:
    get:
      operationId: listFidelite
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Fidelite'
                type: array
          description: Array of Fidelite items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all Fidelite
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createFidelite
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Fidelite'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Fidelite'
          description: Created Fidelite
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new Fidelite
      x-eov-operation-handler: controllers/DefaultController
  /fidelite/{id}:
    delete:
      operationId: deleteFidelite
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a Fidelite
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getFidelite
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Fidelite'
          description: Fidelite item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific Fidelite by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateFidelite
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Fidelite'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Fidelite'
          description: Updated Fidelite
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a Fidelite partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceFidelite
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Fidelite'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Fidelite'
          description: Updated Fidelite
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a Fidelite
      x-eov-operation-handler: controllers/DefaultController
  /codespromo:
    get:
      operationId: listCodesPromo
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/CodesPromo'
                type: array
          description: Array of CodesPromo items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all CodesPromo
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createCodesPromo
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CodesPromo'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CodesPromo'
          description: Created CodesPromo
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new CodesPromo
      x-eov-operation-handler: controllers/DefaultController
  /codespromo/{id}:
    delete:
      operationId: deleteCodesPromo
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a CodesPromo
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getCodesPromo
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CodesPromo'
          description: CodesPromo item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific CodesPromo by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateCodesPromo
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CodesPromo'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CodesPromo'
          description: Updated CodesPromo
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a CodesPromo partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceCodesPromo
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CodesPromo'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CodesPromo'
          description: Updated CodesPromo
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a CodesPromo
      x-eov-operation-handler: controllers/DefaultController
  /codespromo/{id}/reservations:
    get:
      operationId: getCodesPromoReservations
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Reservations'
                type: array
          description: Array of related Reservations items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Reservations items related to CodesPromo
      x-eov-operation-handler: controllers/DefaultController
  /codespromo/{id}/transactionspos:
    get:
      operationId: getCodesPromoTransactionsPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/TransactionsPOS'
                type: array
          description: Array of related TransactionsPOS items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get TransactionsPOS items related to CodesPromo
      x-eov-operation-handler: controllers/DefaultController
  /codespromo/{id}/lignestransactionpos:
    get:
      operationId: getCodesPromoLignesTransactionPOS
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/LignesTransactionPOS'
                type: array
          description: Array of related LignesTransactionPOS items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get LignesTransactionPOS items related to CodesPromo
      x-eov-operation-handler: controllers/DefaultController
  /codespromo/{id}/utilisationscodes:
    get:
      operationId: getCodesPromoUtilisationsCodes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UtilisationsCodes'
                type: array
          description: Array of related UtilisationsCodes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get UtilisationsCodes items related to CodesPromo
      x-eov-operation-handler: controllers/DefaultController
  /codespromo/{id}/commandes:
    get:
      operationId: getCodesPromoCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Commandes'
                type: array
          description: Array of related Commandes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Commandes items related to CodesPromo
      x-eov-operation-handler: controllers/DefaultController
  /codespromo/{id}/detailscommandes:
    get:
      operationId: getCodesPromoDetailsCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/DetailsCommandes'
                type: array
          description: Array of related DetailsCommandes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get DetailsCommandes items related to CodesPromo
      x-eov-operation-handler: controllers/DefaultController
  /utilisationscodes:
    get:
      operationId: listUtilisationsCodes
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/UtilisationsCodes'
                type: array
          description: Array of UtilisationsCodes items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all UtilisationsCodes
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createUtilisationsCodes
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UtilisationsCodes'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UtilisationsCodes'
          description: Created UtilisationsCodes
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new UtilisationsCodes
      x-eov-operation-handler: controllers/DefaultController
  /utilisationscodes/{id}:
    delete:
      operationId: deleteUtilisationsCodes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a UtilisationsCodes
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getUtilisationsCodes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UtilisationsCodes'
          description: UtilisationsCodes item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific UtilisationsCodes by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateUtilisationsCodes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UtilisationsCodes'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UtilisationsCodes'
          description: Updated UtilisationsCodes
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a UtilisationsCodes partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceUtilisationsCodes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UtilisationsCodes'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UtilisationsCodes'
          description: Updated UtilisationsCodes
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a UtilisationsCodes
      x-eov-operation-handler: controllers/DefaultController
  /fournisseurs:
    get:
      operationId: listFournisseurs
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Fournisseurs'
                type: array
          description: Array of Fournisseurs items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all Fournisseurs
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createFournisseurs
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Fournisseurs'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Fournisseurs'
          description: Created Fournisseurs
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new Fournisseurs
      x-eov-operation-handler: controllers/DefaultController
  /fournisseurs/{id}:
    delete:
      operationId: deleteFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a Fournisseurs
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Fournisseurs'
          description: Fournisseurs item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific Fournisseurs by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Fournisseurs'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Fournisseurs'
          description: Updated Fournisseurs
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a Fournisseurs partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Fournisseurs'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Fournisseurs'
          description: Updated Fournisseurs
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a Fournisseurs
      x-eov-operation-handler: controllers/DefaultController
  /fournisseurs/{id}/commandesfournisseurs:
    get:
      operationId: getFournisseursCommandesFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/CommandesFournisseurs'
                type: array
          description: Array of related CommandesFournisseurs items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get CommandesFournisseurs items related to Fournisseurs
      x-eov-operation-handler: controllers/DefaultController
  /commandesfournisseurs:
    get:
      operationId: listCommandesFournisseurs
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/CommandesFournisseurs'
                type: array
          description: Array of CommandesFournisseurs items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all CommandesFournisseurs
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createCommandesFournisseurs
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommandesFournisseurs'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommandesFournisseurs'
          description: Created CommandesFournisseurs
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new CommandesFournisseurs
      x-eov-operation-handler: controllers/DefaultController
  /commandesfournisseurs/{id}:
    delete:
      operationId: deleteCommandesFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a CommandesFournisseurs
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getCommandesFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommandesFournisseurs'
          description: CommandesFournisseurs item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific CommandesFournisseurs by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateCommandesFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommandesFournisseurs'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommandesFournisseurs'
          description: Updated CommandesFournisseurs
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a CommandesFournisseurs partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceCommandesFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommandesFournisseurs'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommandesFournisseurs'
          description: Updated CommandesFournisseurs
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a CommandesFournisseurs
      x-eov-operation-handler: controllers/DefaultController
  /commandesfournisseurs/{id}/detailscommandesfournisseurs:
    get:
      operationId: getCommandesFournisseursDetailsCommandesFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/DetailsCommandesFournisseurs'
                type: array
          description: Array of related DetailsCommandesFournisseurs items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get DetailsCommandesFournisseurs items related to CommandesFournisseurs
      x-eov-operation-handler: controllers/DefaultController
  /detailscommandesfournisseurs:
    get:
      operationId: listDetailsCommandesFournisseurs
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/DetailsCommandesFournisseurs'
                type: array
          description: Array of DetailsCommandesFournisseurs items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all DetailsCommandesFournisseurs
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createDetailsCommandesFournisseurs
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DetailsCommandesFournisseurs'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailsCommandesFournisseurs'
          description: Created DetailsCommandesFournisseurs
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new DetailsCommandesFournisseurs
      x-eov-operation-handler: controllers/DefaultController
  /detailscommandesfournisseurs/{id}:
    delete:
      operationId: deleteDetailsCommandesFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a DetailsCommandesFournisseurs
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getDetailsCommandesFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailsCommandesFournisseurs'
          description: DetailsCommandesFournisseurs item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific DetailsCommandesFournisseurs by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateDetailsCommandesFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DetailsCommandesFournisseurs'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailsCommandesFournisseurs'
          description: Updated DetailsCommandesFournisseurs
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a DetailsCommandesFournisseurs partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceDetailsCommandesFournisseurs
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DetailsCommandesFournisseurs'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailsCommandesFournisseurs'
          description: Updated DetailsCommandesFournisseurs
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a DetailsCommandesFournisseurs
      x-eov-operation-handler: controllers/DefaultController
  /mouvementsstock:
    get:
      operationId: listMouvementsStock
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/MouvementsStock'
                type: array
          description: Array of MouvementsStock items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all MouvementsStock
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createMouvementsStock
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MouvementsStock'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MouvementsStock'
          description: Created MouvementsStock
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new MouvementsStock
      x-eov-operation-handler: controllers/DefaultController
  /mouvementsstock/{id}:
    delete:
      operationId: deleteMouvementsStock
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a MouvementsStock
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getMouvementsStock
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MouvementsStock'
          description: MouvementsStock item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific MouvementsStock by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateMouvementsStock
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MouvementsStock'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MouvementsStock'
          description: Updated MouvementsStock
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a MouvementsStock partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceMouvementsStock
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MouvementsStock'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MouvementsStock'
          description: Updated MouvementsStock
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a MouvementsStock
      x-eov-operation-handler: controllers/DefaultController
  /inventaires:
    get:
      operationId: listInventaires
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Inventaires'
                type: array
          description: Array of Inventaires items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all Inventaires
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createInventaires
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Inventaires'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Inventaires'
          description: Created Inventaires
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new Inventaires
      x-eov-operation-handler: controllers/DefaultController
  /inventaires/{id}:
    delete:
      operationId: deleteInventaires
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a Inventaires
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getInventaires
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Inventaires'
          description: Inventaires item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific Inventaires by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateInventaires
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Inventaires'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Inventaires'
          description: Updated Inventaires
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a Inventaires partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceInventaires
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Inventaires'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Inventaires'
          description: Updated Inventaires
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a Inventaires
      x-eov-operation-handler: controllers/DefaultController
  /inventaires/{id}/lignesinventaire:
    get:
      operationId: getInventairesLignesInventaire
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/LignesInventaire'
                type: array
          description: Array of related LignesInventaire items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get LignesInventaire items related to Inventaires
      x-eov-operation-handler: controllers/DefaultController
  /lignesinventaire:
    get:
      operationId: listLignesInventaire
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/LignesInventaire'
                type: array
          description: Array of LignesInventaire items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all LignesInventaire
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createLignesInventaire
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LignesInventaire'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LignesInventaire'
          description: Created LignesInventaire
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new LignesInventaire
      x-eov-operation-handler: controllers/DefaultController
  /lignesinventaire/{id}:
    delete:
      operationId: deleteLignesInventaire
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a LignesInventaire
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getLignesInventaire
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LignesInventaire'
          description: LignesInventaire item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific LignesInventaire by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateLignesInventaire
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LignesInventaire'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LignesInventaire'
          description: Updated LignesInventaire
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a LignesInventaire partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceLignesInventaire
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LignesInventaire'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LignesInventaire'
          description: Updated LignesInventaire
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a LignesInventaire
      x-eov-operation-handler: controllers/DefaultController
  /tables:
    get:
      operationId: listTables
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Tables'
                type: array
          description: Array of Tables items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all Tables
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createTables
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Tables'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tables'
          description: Created Tables
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new Tables
      x-eov-operation-handler: controllers/DefaultController
  /tables/{id}:
    delete:
      operationId: deleteTables
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a Tables
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getTables
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tables'
          description: Tables item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific Tables by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateTables
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Tables'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tables'
          description: Updated Tables
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a Tables partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceTables
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Tables'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tables'
          description: Updated Tables
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a Tables
      x-eov-operation-handler: controllers/DefaultController
  /tables/{id}/reservationstables:
    get:
      operationId: getTablesReservationsTables
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ReservationsTables'
                type: array
          description: Array of related ReservationsTables items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get ReservationsTables items related to Tables
      x-eov-operation-handler: controllers/DefaultController
  /tables/{id}/commandes:
    get:
      operationId: getTablesCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Commandes'
                type: array
          description: Array of related Commandes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Commandes items related to Tables
      x-eov-operation-handler: controllers/DefaultController
  /reservationstables:
    get:
      operationId: listReservationsTables
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ReservationsTables'
                type: array
          description: Array of ReservationsTables items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all ReservationsTables
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createReservationsTables
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReservationsTables'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReservationsTables'
          description: Created ReservationsTables
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new ReservationsTables
      x-eov-operation-handler: controllers/DefaultController
  /reservationstables/{id}:
    delete:
      operationId: deleteReservationsTables
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a ReservationsTables
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getReservationsTables
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReservationsTables'
          description: ReservationsTables item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific ReservationsTables by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateReservationsTables
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReservationsTables'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReservationsTables'
          description: Updated ReservationsTables
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a ReservationsTables partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceReservationsTables
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReservationsTables'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReservationsTables'
          description: Updated ReservationsTables
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a ReservationsTables
      x-eov-operation-handler: controllers/DefaultController
  /reservationstables/{id}/commandes:
    get:
      operationId: getReservationsTablesCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Commandes'
                type: array
          description: Array of related Commandes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get Commandes items related to ReservationsTables
      x-eov-operation-handler: controllers/DefaultController
  /commandes:
    get:
      operationId: listCommandes
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Commandes'
                type: array
          description: Array of Commandes items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all Commandes
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createCommandes
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Commandes'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Commandes'
          description: Created Commandes
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new Commandes
      x-eov-operation-handler: controllers/DefaultController
  /commandes/{id}:
    delete:
      operationId: deleteCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a Commandes
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Commandes'
          description: Commandes item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific Commandes by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Commandes'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Commandes'
          description: Updated Commandes
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a Commandes partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Commandes'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Commandes'
          description: Updated Commandes
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a Commandes
      x-eov-operation-handler: controllers/DefaultController
  /commandes/{id}/detailscommandes:
    get:
      operationId: getCommandesDetailsCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/DetailsCommandes'
                type: array
          description: Array of related DetailsCommandes items
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get DetailsCommandes items related to Commandes
      x-eov-operation-handler: controllers/DefaultController
  /detailscommandes:
    get:
      operationId: listDetailsCommandes
      parameters:
      - description: Maximum number of items to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          type: integer
        style: form
      - description: Number of items to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/DetailsCommandes'
                type: array
          description: Array of DetailsCommandes items
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: List all DetailsCommandes
      x-eov-operation-handler: controllers/DefaultController
    post:
      operationId: createDetailsCommandes
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DetailsCommandes'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailsCommandes'
          description: Created DetailsCommandes
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Create a new DetailsCommandes
      x-eov-operation-handler: controllers/DefaultController
  /detailscommandes/{id}:
    delete:
      operationId: deleteDetailsCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "204":
          description: Deletion successful
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Delete a DetailsCommandes
      x-eov-operation-handler: controllers/DefaultController
    get:
      operationId: getDetailsCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailsCommandes'
          description: DetailsCommandes item
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Get a specific DetailsCommandes by ID
      x-eov-operation-handler: controllers/DefaultController
    patch:
      operationId: updateDetailsCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DetailsCommandes'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailsCommandes'
          description: Updated DetailsCommandes
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Update a DetailsCommandes partially
      x-eov-operation-handler: controllers/DefaultController
    put:
      operationId: replaceDetailsCommandes
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DetailsCommandes'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailsCommandes'
          description: Updated DetailsCommandes
        "400":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "404":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
        "422":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
          description: Error response
      summary: Replace a DetailsCommandes
      x-eov-operation-handler: controllers/DefaultController
components:
  responses:
    Error:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/listUtilisateursSuperAdmin_400_response'
      description: Error response
  schemas:
    UtilisateursSuperAdmin:
      example:
        mot_de_passe_hash: mot_de_passe_hash
        updated_at: 2000-01-23T04:56:07.000+00:00
        derniere_connexion: 2000-01-23T04:56:07.000+00:00
        created_at: created_at
        actif: actif
        superadmin_id: superadmin_id
        nom: nom
        prenom: prenom
        email: email
      properties:
        superadmin_id:
          type: string
        email:
          type: string
        mot_de_passe_hash:
          type: string
        nom:
          type: string
        prenom:
          type: string
        actif:
          type: string
        derniere_connexion:
          format: date-time
          type: string
        created_at:
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    ChainesHotelieres:
      example:
        superadmin_createur_id: 0
        chaine_id: chaine_id
        logo_url: logo_url
        description: description
        adresse_siege: adresse_siege
        actif: actif
        nom: nom
        date_creation: date_creation
        date_mise_a_jour: 2000-01-23T04:56:07.000+00:00
        devise_principale: devise_principale
        pays_origine: pays_origine
        telephone_contact: telephone_contact
        fuseau_horaire_defaut: fuseau_horaire_defaut
        email_contact: email_contact
        slug: slug
        site_web: site_web
      properties:
        chaine_id:
          type: string
        nom:
          type: string
        slug:
          type: string
        description:
          type: string
        logo_url:
          type: string
        site_web:
          type: string
        email_contact:
          type: string
        telephone_contact:
          type: string
        adresse_siege:
          type: string
        pays_origine:
          type: string
        devise_principale:
          type: string
        fuseau_horaire_defaut:
          type: string
        actif:
          type: string
        superadmin_createur_id:
          format: int32
          type: integer
        date_creation:
          type: string
        date_mise_a_jour:
          format: date-time
          type: string
      type: object
    AdminsChaine:
      example:
        chaine_id: chaine_id
        mot_de_passe_hash: mot_de_passe_hash
        updated_at: 2000-01-23T04:56:07.000+00:00
        derniere_connexion: 2000-01-23T04:56:07.000+00:00
        created_at: created_at
        telephone: telephone
        actif: actif
        admin_chaine_id: admin_chaine_id
        nom: nom
        prenom: prenom
        email: email
      properties:
        admin_chaine_id:
          type: string
        chaine_id:
          type: string
        email:
          type: string
        mot_de_passe_hash:
          type: string
        nom:
          type: string
        prenom:
          type: string
        telephone:
          type: string
        actif:
          type: string
        derniere_connexion:
          format: date-time
          type: string
        created_at:
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    ComplexesHoteliers:
      example:
        chaine_id: chaine_id
        type_etablissement: type_etablissement
        code_postal: code_postal
        ville: ville
        fuseau_horaire: fuseau_horaire
        logo_url: logo_url
        telephone: telephone
        actif: actif
        nom: nom
        devise: devise
        date_creation: date_creation
        complexe_id: complexe_id
        date_mise_a_jour: 2000-01-23T04:56:07.000+00:00
        adresse: adresse
        slug: slug
        email: email
        pays: pays
        site_web: site_web
      properties:
        complexe_id:
          type: string
        chaine_id:
          type: string
        nom:
          type: string
        slug:
          type: string
        type_etablissement:
          type: string
        adresse:
          type: string
        ville:
          type: string
        code_postal:
          type: string
        pays:
          type: string
        telephone:
          type: string
        email:
          type: string
        site_web:
          type: string
        logo_url:
          type: string
        devise:
          type: string
        fuseau_horaire:
          type: string
        actif:
          type: string
        date_creation:
          type: string
        date_mise_a_jour:
          format: date-time
          type: string
      type: object
    ServicesComplexe:
      example:
        emplacement: emplacement
        contact_telephone: contact_telephone
        configuration: "{}"
        image_url: image_url
        description: description
        created_at: created_at
        actif: actif
        nom: nom
        horaires_ouverture: "{}"
        contact_email: contact_email
        complexe_id: complexe_id
        capacite: 0
        updated_at: 2000-01-23T04:56:07.000+00:00
        tarification: "{}"
        service_id: service_id
        type_service: type_service
      properties:
        service_id:
          type: string
        complexe_id:
          type: string
        type_service:
          type: string
        nom:
          type: string
        description:
          type: string
        emplacement:
          type: string
        horaires_ouverture:
          type: object
        capacite:
          format: int32
          type: integer
        image_url:
          type: string
        contact_email:
          type: string
        contact_telephone:
          type: string
        actif:
          type: string
        configuration:
          type: object
        tarification:
          type: object
        created_at:
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    AdminsComplexe:
      example:
        complexe_id: complexe_id
        mot_de_passe_hash: mot_de_passe_hash
        updated_at: 2000-01-23T04:56:07.000+00:00
        derniere_connexion: 2000-01-23T04:56:07.000+00:00
        created_at: created_at
        telephone: telephone
        actif: actif
        admin_complexe_id: admin_complexe_id
        nom: nom
        prenom: prenom
        email: email
      properties:
        admin_complexe_id:
          type: string
        complexe_id:
          type: string
        email:
          type: string
        mot_de_passe_hash:
          type: string
        nom:
          type: string
        prenom:
          type: string
        telephone:
          type: string
        actif:
          type: string
        derniere_connexion:
          format: date-time
          type: string
        created_at:
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    RolesComplexe:
      example:
        complexe_id: complexe_id
        role_id: role_id
        permissions: permissions
        service_id: 0
        description: description
        created_at: created_at
        nom: nom
      properties:
        role_id:
          type: string
        complexe_id:
          type: string
        service_id:
          format: int32
          type: integer
        nom:
          type: string
        permissions:
          type: string
        description:
          type: string
        created_at:
          type: string
      type: object
    Employes:
      example:
        employe_id: employe_id
        date_embauche: date_embauche
        created_at: created_at
        telephone: telephone
        actif: actif
        nom: nom
        complexe_id: complexe_id
        mot_de_passe_hash: mot_de_passe_hash
        updated_at: 2000-01-23T04:56:07.000+00:00
        role_id: 6
        service_id: 0
        derniere_connexion: 2000-01-23T04:56:07.000+00:00
        prenom: prenom
        services_autorises: "{}"
        email: email
      properties:
        employe_id:
          type: string
        complexe_id:
          type: string
        service_id:
          format: int32
          type: integer
        role_id:
          format: int32
          type: integer
        nom:
          type: string
        prenom:
          type: string
        email:
          type: string
        telephone:
          type: string
        mot_de_passe_hash:
          type: string
        date_embauche:
          type: string
        actif:
          type: string
        services_autorises:
          type: object
        derniere_connexion:
          format: date-time
          type: string
        created_at:
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    PointsDeVente:
      example:
        emplacement: emplacement
        caisse_ouverte: caisse_ouverte
        complexe_id: complexe_id
        updated_at: 2000-01-23T04:56:07.000+00:00
        configuration: "{}"
        pos_id: pos_id
        service_id: service_id
        employe_actuel_id: 0
        created_at: created_at
        nom: nom
        statut: statut
        fonds_caisse: fonds_caisse
      properties:
        pos_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: string
        nom:
          type: string
        emplacement:
          type: string
        caisse_ouverte:
          type: string
        fonds_caisse:
          type: string
        employe_actuel_id:
          format: int32
          type: integer
        statut:
          type: string
        configuration:
          type: object
        created_at:
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    SessionsCaisse:
      example:
        employe_id: employe_id
        total_cartes: 5.962133916683182
        total_autres: 5.637376656633329
        notes: notes
        total_especes: 1.4658129805029452
        date_fermeture: 2000-01-23T04:56:07.000+00:00
        session_id: session_id
        date_ouverture: date_ouverture
        complexe_id: complexe_id
        total_ventes: 6.027456183070403
        pos_id: pos_id
        fonds_ouverture: fonds_ouverture
        service_id: service_id
        fonds_fermeture: 0.8008281904610115
        statut: statut
      properties:
        session_id:
          type: string
        pos_id:
          type: string
        employe_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: string
        date_ouverture:
          type: string
        date_fermeture:
          format: date-time
          type: string
        fonds_ouverture:
          type: string
        fonds_fermeture:
          format: double
          type: number
        total_ventes:
          format: double
          type: number
        total_especes:
          format: double
          type: number
        total_cartes:
          format: double
          type: number
        total_autres:
          format: double
          type: number
        notes:
          type: string
        statut:
          type: string
      type: object
    CategoriesProduits:
      example:
        chaine_id: chaine_id
        complexe_id: 6
        parent_id: 0
        service_id: 1
        description: description
        created_at: created_at
        actif: actif
        nom: nom
        niveau: niveau
        categorie_id: categorie_id
      properties:
        categorie_id:
          type: string
        chaine_id:
          type: string
        parent_id:
          format: int32
          type: integer
        nom:
          type: string
        description:
          type: string
        niveau:
          type: string
        complexe_id:
          format: int32
          type: integer
        service_id:
          format: int32
          type: integer
        actif:
          type: string
        created_at:
          type: string
      type: object
    Produits:
      example:
        produit_id: produit_id
        chaine_id: chaine_id
        stock_maximal: 5
        image_url: image_url
        prix_achat: 1.4658129805029452
        description: description
        stock_actuel: stock_actuel
        created_at: created_at
        prix_vente_defaut: prix_vente_defaut
        actif: actif
        niveau: niveau
        nom: nom
        categorie_id: categorie_id
        stock_minimal: stock_minimal
        complexe_id: 0
        type_produit: type_produit
        updated_at: 2000-01-23T04:56:07.000+00:00
        service_id: 6
        code_barre: code_barre
        sku: sku
        tva: tva
      properties:
        produit_id:
          type: string
        categorie_id:
          type: string
        chaine_id:
          type: string
        niveau:
          type: string
        complexe_id:
          format: int32
          type: integer
        service_id:
          format: int32
          type: integer
        nom:
          type: string
        code_barre:
          type: string
        sku:
          type: string
        type_produit:
          type: string
        prix_achat:
          format: double
          type: number
        prix_vente_defaut:
          type: string
        description:
          type: string
        stock_actuel:
          type: string
        stock_minimal:
          type: string
        stock_maximal:
          format: int32
          type: integer
        actif:
          type: string
        tva:
          type: string
        image_url:
          type: string
        created_at:
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    PrixProduits:
      example:
        produit_id: produit_id
        complexe_id: complexe_id
        service_id: 0
        date_debut: date_debut
        created_at: created_at
        date_fin: 2000-01-23T04:56:07.000+00:00
        actif: actif
        prix_id: prix_id
        prix_vente: prix_vente
      properties:
        prix_id:
          type: string
        produit_id:
          type: string
        complexe_id:
          type: string
        service_id:
          format: int32
          type: integer
        prix_vente:
          type: string
        date_debut:
          type: string
        date_fin:
          format: date-time
          type: string
        actif:
          type: string
        created_at:
          type: string
      type: object
    Chambres:
      example:
        numero: numero
        description: description
        created_at: created_at
        chambre_id: chambre_id
        type_chambre: type_chambre
        prix_base: prix_base
        caracteristiques: "{}"
        complexe_id: complexe_id
        capacite: capacite
        updated_at: 2000-01-23T04:56:07.000+00:00
        etage: 0
        service_id: service_id
        statut: statut
      properties:
        chambre_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: string
        numero:
          type: string
        type_chambre:
          type: string
        prix_base:
          type: string
        capacite:
          type: string
        statut:
          type: string
        description:
          type: string
        etage:
          format: int32
          type: integer
        caracteristiques:
          type: object
        created_at:
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    Clients:
      example:
        nationalite: nationalite
        chaine_id: chaine_id
        preferences: "{}"
        type_piece_identite: type_piece_identite
        created_at: created_at
        telephone: telephone
        complexe_creation_id: complexe_creation_id
        nom: nom
        client_id: client_id
        numero_piece_identite: numero_piece_identite
        updated_at: 2000-01-23T04:56:07.000+00:00
        adresse: adresse
        prenom: prenom
        email: email
        date_naissance: 2000-01-23
      properties:
        client_id:
          type: string
        chaine_id:
          type: string
        complexe_creation_id:
          type: string
        nom:
          type: string
        prenom:
          type: string
        email:
          type: string
        telephone:
          type: string
        adresse:
          type: string
        date_naissance:
          format: date
          type: string
        nationalite:
          type: string
        numero_piece_identite:
          type: string
        type_piece_identite:
          type: string
        preferences:
          type: object
        created_at:
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    Reservations:
      example:
        employe_id: 1
        date_arrivee: date_arrivee
        nb_adultes: nb_adultes
        created_at: created_at
        client_id: client_id
        date_creation: date_creation
        source_reservation: source_reservation
        reservation_id: reservation_id
        complexe_id: complexe_id
        code_promo_id: 6
        updated_at: 2000-01-23T04:56:07.000+00:00
        montant_total: 0.8008281904610115
        service_id: service_id
        acompte_verse: acompte_verse
        nb_enfants: nb_enfants
        commentaires: commentaires
        reduction_appliquee: reduction_appliquee
        statut: statut
        date_depart: date_depart
      properties:
        reservation_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: string
        client_id:
          type: string
        date_creation:
          type: string
        date_arrivee:
          type: string
        date_depart:
          type: string
        statut:
          type: string
        nb_adultes:
          type: string
        nb_enfants:
          type: string
        montant_total:
          format: double
          type: number
        acompte_verse:
          type: string
        code_promo_id:
          format: int32
          type: integer
        reduction_appliquee:
          type: string
        employe_id:
          format: int32
          type: integer
        commentaires:
          type: string
        source_reservation:
          type: string
        created_at:
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    ChambresReservees:
      example:
        reservation_id: reservation_id
        chambre_reservee_id: chambre_reservee_id
        date_debut: date_debut
        prix_nuit: prix_nuit
        created_at: created_at
        date_fin: date_fin
        commentaires: commentaires
        chambre_id: chambre_id
        statut: statut
      properties:
        chambre_reservee_id:
          type: string
        reservation_id:
          type: string
        chambre_id:
          type: string
        date_debut:
          type: string
        date_fin:
          type: string
        prix_nuit:
          type: string
        statut:
          type: string
        commentaires:
          type: string
        created_at:
          type: string
      type: object
    TransactionsPOS:
      example:
        transaction_id: transaction_id
        employe_id: employe_id
        montant_paye: montant_paye
        notes: notes
        session_id: session_id
        created_at: created_at
        chambre_id: 6
        client_id: 0
        reference: reference
        complexe_id: complexe_id
        date_transaction: date_transaction
        monnaie_rendue: 5.962133916683182
        code_promo_id: 1
        mode_paiement: mode_paiement
        montant_total: montant_total
        pos_id: pos_id
        service_id: service_id
        statut: statut
      properties:
        transaction_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: string
        pos_id:
          type: string
        session_id:
          type: string
        employe_id:
          type: string
        client_id:
          format: int32
          type: integer
        chambre_id:
          format: int32
          type: integer
        reference:
          type: string
        code_promo_id:
          format: int32
          type: integer
        date_transaction:
          type: string
        montant_total:
          type: string
        montant_paye:
          type: string
        monnaie_rendue:
          format: double
          type: number
        mode_paiement:
          type: string
        statut:
          type: string
        notes:
          type: string
        created_at:
          type: string
      type: object
    LignesTransactionPOS:
      example:
        transaction_id: transaction_id
        produit_id: produit_id
        code_promo_id: 0
        notes: notes
        ligne_id: ligne_id
        remise_promo: remise_promo
        remise: remise
        tva: tva
        quantite: quantite
        prix_unitaire: prix_unitaire
        montant_ligne: montant_ligne
      properties:
        ligne_id:
          type: string
        transaction_id:
          type: string
        produit_id:
          type: string
        quantite:
          type: string
        code_promo_id:
          format: int32
          type: integer
        remise_promo:
          type: string
        prix_unitaire:
          type: string
        remise:
          type: string
        montant_ligne:
          type: string
        tva:
          type: string
        notes:
          type: string
      type: object
    Paiements:
      example:
        paiement_id: paiement_id
        transaction_id: transaction_id
        date_paiement: date_paiement
        mode_paiement: mode_paiement
        notes: notes
        montant: montant
        reference_paiement: reference_paiement
      properties:
        paiement_id:
          type: string
        transaction_id:
          type: string
        montant:
          type: string
        mode_paiement:
          type: string
        reference_paiement:
          type: string
        date_paiement:
          type: string
        notes:
          type: string
      type: object
    Fidelite:
      example:
        chaine_id: chaine_id
        points_cumules: points_cumules
        points_disponibles: points_disponibles
        fidelite_id: fidelite_id
        date_inscription: date_inscription
        niveau: niveau
        client_id: client_id
        date_derniere_activite: 2000-01-23T04:56:07.000+00:00
      properties:
        fidelite_id:
          type: string
        chaine_id:
          type: string
        client_id:
          type: string
        points_disponibles:
          type: string
        points_cumules:
          type: string
        niveau:
          type: string
        date_derniere_activite:
          format: date-time
          type: string
        date_inscription:
          type: string
      type: object
    CodesPromo:
      example:
        categories_associees: "{}"
        chaine_id: chaine_id
        produits_associes: "{}"
        code: code
        categorie: categorie
        valeur: valeur
        utilisation_max: 1
        utilisation_actuelle: utilisation_actuelle
        date_debut: date_debut
        date_expiration: date_expiration
        description: description
        created_at: created_at
        actif: actif
        niveau: niveau
        chambres_associees: "{}"
        complexe_id: 0
        applicable_sur: applicable_sur
        code_promo_id: code_promo_id
        updated_at: 2000-01-23T04:56:07.000+00:00
        service_id: 6
        conditions: conditions
        type_remise: type_remise
      properties:
        code_promo_id:
          type: string
        niveau:
          type: string
        chaine_id:
          type: string
        complexe_id:
          format: int32
          type: integer
        service_id:
          format: int32
          type: integer
        code:
          type: string
        type_remise:
          type: string
        valeur:
          type: string
        date_debut:
          type: string
        date_expiration:
          type: string
        utilisation_max:
          format: int32
          type: integer
        utilisation_actuelle:
          type: string
        actif:
          type: string
        description:
          type: string
        conditions:
          type: string
        categorie:
          type: string
        applicable_sur:
          type: string
        produits_associes:
          type: object
        categories_associees:
          type: object
        chambres_associees:
          type: object
        created_at:
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    UtilisationsCodes:
      example:
        employe_id: 5
        chaine_id: chaine_id
        utilisation_id: utilisation_id
        date_utilisation: date_utilisation
        client_id: 6
        reservation_id: 1
        complexe_id: complexe_id
        montant_remise: montant_remise
        code_promo_id: code_promo_id
        transaction_pos_id: 5
        service_id: 0
        montant_apres: montant_apres
        montant_avant: montant_avant
      properties:
        utilisation_id:
          type: string
        chaine_id:
          type: string
        complexe_id:
          type: string
        service_id:
          format: int32
          type: integer
        code_promo_id:
          type: string
        client_id:
          format: int32
          type: integer
        date_utilisation:
          type: string
        reservation_id:
          format: int32
          type: integer
        transaction_pos_id:
          format: int32
          type: integer
        montant_avant:
          type: string
        montant_remise:
          type: string
        montant_apres:
          type: string
        employe_id:
          format: int32
          type: integer
      type: object
    Fournisseurs:
      example:
        chaine_id: chaine_id
        notes: notes
        created_at: created_at
        telephone: telephone
        informations_paiement: informations_paiement
        actif: actif
        niveau: niveau
        nom: nom
        fournisseur_id: fournisseur_id
        complexe_id: 0
        updated_at: 2000-01-23T04:56:07.000+00:00
        contact: contact
        adresse: adresse
        email: email
      properties:
        fournisseur_id:
          type: string
        chaine_id:
          type: string
        niveau:
          type: string
        complexe_id:
          format: int32
          type: integer
        nom:
          type: string
        contact:
          type: string
        email:
          type: string
        telephone:
          type: string
        adresse:
          type: string
        informations_paiement:
          type: string
        notes:
          type: string
        actif:
          type: string
        created_at:
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    CommandesFournisseurs:
      example:
        employe_id: employe_id
        chaine_id: chaine_id
        notes: notes
        date_livraison_prevue: 2000-01-23
        created_at: created_at
        commande_id: commande_id
        fournisseur_id: fournisseur_id
        reference: reference
        complexe_id: complexe_id
        updated_at: 2000-01-23T04:56:07.000+00:00
        montant_total: 6.027456183070403
        service_id: 0
        statut: statut
        date_commande: date_commande
      properties:
        commande_id:
          type: string
        chaine_id:
          type: string
        complexe_id:
          type: string
        service_id:
          format: int32
          type: integer
        fournisseur_id:
          type: string
        reference:
          type: string
        date_commande:
          type: string
        date_livraison_prevue:
          format: date
          type: string
        statut:
          type: string
        montant_total:
          format: double
          type: number
        employe_id:
          type: string
        notes:
          type: string
        created_at:
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    DetailsCommandesFournisseurs:
      example:
        produit_id: produit_id
        numero_lot: numero_lot
        quantite_commandee: quantite_commandee
        created_at: created_at
        detail_id: detail_id
        quantite_recue: quantite_recue
        commande_id: commande_id
        prix_unitaire: prix_unitaire
        montant_ligne: montant_ligne
        date_peremption: 2000-01-23
      properties:
        detail_id:
          type: string
        commande_id:
          type: string
        produit_id:
          type: string
        quantite_commandee:
          type: string
        quantite_recue:
          type: string
        prix_unitaire:
          type: string
        montant_ligne:
          type: string
        date_peremption:
          format: date
          type: string
        numero_lot:
          type: string
        created_at:
          type: string
      type: object
    MouvementsStock:
      example:
        produit_id: produit_id
        employe_id: employe_id
        chaine_id: chaine_id
        notes: notes
        reference_id: 6
        type_mouvement: type_mouvement
        created_at: created_at
        reference_type: reference_type
        date_mouvement: date_mouvement
        complexe_id: complexe_id
        service_id: 0
        mouvement_id: mouvement_id
        quantite: quantite
      properties:
        mouvement_id:
          type: string
        chaine_id:
          type: string
        complexe_id:
          type: string
        service_id:
          format: int32
          type: integer
        produit_id:
          type: string
        type_mouvement:
          type: string
        quantite:
          type: string
        date_mouvement:
          type: string
        reference_id:
          format: int32
          type: integer
        reference_type:
          type: string
        employe_id:
          type: string
        notes:
          type: string
        created_at:
          type: string
      type: object
    Inventaires:
      example:
        inventaire_id: inventaire_id
        reference: reference
        employe_id: employe_id
        chaine_id: chaine_id
        complexe_id: complexe_id
        notes: notes
        service_id: 0
        date_debut: date_debut
        created_at: created_at
        date_fin: 2000-01-23T04:56:07.000+00:00
        statut: statut
      properties:
        inventaire_id:
          type: string
        chaine_id:
          type: string
        complexe_id:
          type: string
        service_id:
          format: int32
          type: integer
        reference:
          type: string
        date_debut:
          type: string
        date_fin:
          format: date-time
          type: string
        statut:
          type: string
        employe_id:
          type: string
        notes:
          type: string
        created_at:
          type: string
      type: object
    LignesInventaire:
      example:
        inventaire_id: inventaire_id
        produit_id: produit_id
        notes: notes
        ligne_id: ligne_id
        ecart: 6
        quantite_reelle: 0
        created_at: created_at
        quantite_theorique: quantite_theorique
      properties:
        ligne_id:
          type: string
        inventaire_id:
          type: string
        produit_id:
          type: string
        quantite_theorique:
          type: string
        quantite_reelle:
          format: int32
          type: integer
        ecart:
          format: int32
          type: integer
        notes:
          type: string
        created_at:
          type: string
      type: object
    Tables:
      example:
        numero: numero
        shape: shape
        rotation: rotation
        created_at: created_at
        table_id: table_id
        complexe_id: complexe_id
        position_x: 0
        capacite: capacite
        position_y: 6
        updated_at: 2000-01-23T04:56:07.000+00:00
        zone: zone
        service_id: service_id
        statut: statut
        dimensions: dimensions
      properties:
        table_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: string
        numero:
          type: string
        capacite:
          type: string
        zone:
          type: string
        statut:
          type: string
        position_x:
          format: int32
          type: integer
        position_y:
          format: int32
          type: integer
        rotation:
          type: string
        shape:
          type: string
        dimensions:
          type: string
        created_at:
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    ReservationsTables:
      example:
        reservation_id: reservation_id
        employe_id: employe_id
        complexe_id: complexe_id
        nb_personnes: nb_personnes
        service_id: service_id
        date_debut: date_debut
        created_at: created_at
        date_fin: date_fin
        commentaires: commentaires
        table_id: table_id
        client_id: 0
        statut: statut
      properties:
        reservation_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: string
        table_id:
          type: string
        client_id:
          format: int32
          type: integer
        employe_id:
          type: string
        date_debut:
          type: string
        date_fin:
          type: string
        nb_personnes:
          type: string
        statut:
          type: string
        commentaires:
          type: string
        created_at:
          type: string
      type: object
    Commandes:
      example:
        employe_id: employe_id
        note: note
        statut_paiement: statut_paiement
        created_at: created_at
        chambre_id: 7
        table_id: 0
        type: type
        commande_id: commande_id
        client_id: 6
        reservation_id: 2
        complexe_id: complexe_id
        code_promo_id: 5
        mode_paiement: mode_paiement
        updated_at: 2000-01-23T04:56:07.000+00:00
        montant_total: 1.4658129805029452
        montant_tva: 5.962133916683182
        service_id: service_id
        reduction_appliquee: reduction_appliquee
        statut: statut
        date_commande: date_commande
      properties:
        commande_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: string
        table_id:
          format: int32
          type: integer
        client_id:
          format: int32
          type: integer
        employe_id:
          type: string
        date_commande:
          type: string
        type:
          type: string
        statut:
          type: string
        montant_total:
          format: double
          type: number
        montant_tva:
          format: double
          type: number
        note:
          type: string
        code_promo_id:
          format: int32
          type: integer
        reduction_appliquee:
          type: string
        reservation_id:
          format: int32
          type: integer
        chambre_id:
          format: int32
          type: integer
        statut_paiement:
          type: string
        mode_paiement:
          type: string
        created_at:
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    DetailsCommandes:
      example:
        produit_id: produit_id
        remise: remise
        detail_id: detail_id
        commande_id: commande_id
        prix_unitaire: prix_unitaire
        complexe_id: complexe_id
        employe_preparation: 6
        code_promo_id: 0
        heure_preparation: 2000-01-23T04:56:07.000+00:00
        remise_promo: remise_promo
        heure_demande: heure_demande
        service_id: service_id
        commentaires: commentaires
        heure_servi: 2000-01-23T04:56:07.000+00:00
        tva: tva
        statut: statut
        quantite: quantite
        montant_ligne: montant_ligne
      properties:
        detail_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: string
        commande_id:
          type: string
        produit_id:
          type: string
        quantite:
          type: string
        prix_unitaire:
          type: string
        remise:
          type: string
        montant_ligne:
          type: string
        tva:
          type: string
        commentaires:
          type: string
        statut:
          type: string
        code_promo_id:
          format: int32
          type: integer
        remise_promo:
          type: string
        heure_demande:
          type: string
        heure_preparation:
          format: date-time
          type: string
        heure_servi:
          format: date-time
          type: string
        employe_preparation:
          format: int32
          type: integer
      type: object
    listUtilisateursSuperAdmin_400_response:
      example:
        details: details
        error: error
      properties:
        error:
          type: string
        details:
          type: string
      type: object
