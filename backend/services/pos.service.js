const db = require('../db');
const logger = require('../logger');

class POSService {
  static successResponse(data) {
    return {
      success: true,
      data
    };
  }

  static rejectResponse(message, code = 500) {
    return {
      success: false,
      message,
      code
    };
  }

  // Récupérer tous les points de vente d'un complexe
  static async getAllPOS(complexeId) {
    try {
      const query = `
        SELECT 
          p.*,
          s.nom as service_nom,
          s.type_service,
          e.nom as employe_nom,
          e.prenom as employe_prenom
        FROM "PointsDeVente" p
        LEFT JOIN "ServicesComplexe" s ON p.service_id = s.service_id
        LEFT JOIN "Employes" e ON p.employe_actuel_id = e.employe_id
        WHERE p.complexe_id = $1
        ORDER BY s.type_service, p.nom
      `;
      
      const result = await db.query(query, [complexeId]);
      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération points de vente:', error);
      return this.rejectResponse('Erreur lors de la récupération des points de vente');
    }
  }

  // Récupérer les points de vente d'un service spécifique
  static async getPOSByService(serviceId) {
    try {
      const query = `
        SELECT 
          p.*,
          s.nom as service_nom,
          s.type_service,
          e.nom as employe_nom,
          e.prenom as employe_prenom
        FROM "PointsDeVente" p
        LEFT JOIN "ServicesComplexe" s ON p.service_id = s.service_id
        LEFT JOIN "Employes" e ON p.employe_actuel_id = e.employe_id
        WHERE p.service_id = $1
        ORDER BY p.nom
      `;
      
      const result = await db.query(query, [serviceId]);
      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération points de vente par service:', error);
      return this.rejectResponse('Erreur lors de la récupération des points de vente du service');
    }
  }

  // Récupérer un point de vente par ID
  static async getPOSById(posId) {
    try {
      const query = `
        SELECT 
          p.*,
          s.nom as service_nom,
          s.type_service,
          e.nom as employe_nom,
          e.prenom as employe_prenom
        FROM "PointsDeVente" p
        LEFT JOIN "ServicesComplexe" s ON p.service_id = s.service_id
        LEFT JOIN "Employes" e ON p.employe_actuel_id = e.employe_id
        WHERE p.pos_id = $1
      `;
      
      const result = await db.query(query, [posId]);
      
      if (result.rows.length === 0) {
        return this.rejectResponse('Point de vente non trouvé', 404);
      }
      
      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur récupération point de vente:', error);
      return this.rejectResponse('Erreur lors de la récupération du point de vente');
    }
  }

  // Créer un nouveau point de vente
  static async createPOS(posData) {
    const {
      complexe_id,
      service_id,
      nom,
      emplacement,
      fonds_caisse = 0,
      configuration = {}
    } = posData;

    try {
      // Vérifier que le service existe
      const serviceCheck = await db.query(
        'SELECT service_id FROM "ServicesComplexe" WHERE service_id = $1 AND complexe_id = $2',
        [service_id, complexe_id]
      );

      if (serviceCheck.rows.length === 0) {
        return this.rejectResponse('Service non trouvé dans ce complexe', 404);
      }

      const query = `
        INSERT INTO "PointsDeVente" (
          complexe_id,
          service_id,
          nom,
          emplacement,
          fonds_caisse,
          configuration,
          created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, NOW())
        RETURNING *
      `;
      
      const result = await db.query(query, [
        complexe_id,
        service_id,
        nom,
        emplacement,
        fonds_caisse,
        JSON.stringify(configuration)
      ]);
      
      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur création point de vente:', error);
      return this.rejectResponse('Erreur lors de la création du point de vente');
    }
  }

  // Mettre à jour un point de vente
  static async updatePOS(posId, posData) {
    const {
      nom,
      emplacement,
      statut,
      fonds_caisse,
      employe_actuel_id,
      configuration
    } = posData;

    try {
      const query = `
        UPDATE "PointsDeVente" 
        SET 
          nom = COALESCE($1, nom),
          emplacement = COALESCE($2, emplacement),
          statut = COALESCE($3, statut),
          fonds_caisse = COALESCE($4, fonds_caisse),
          employe_actuel_id = $5,
          configuration = COALESCE($6, configuration),
          updated_at = NOW()
        WHERE pos_id = $7
        RETURNING *
      `;
      
      const result = await db.query(query, [
        nom,
        emplacement,
        statut,
        fonds_caisse,
        employe_actuel_id,
        configuration ? JSON.stringify(configuration) : null,
        posId
      ]);
      
      if (result.rows.length === 0) {
        return this.rejectResponse('Point de vente non trouvé', 404);
      }
      
      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur mise à jour point de vente:', error);
      return this.rejectResponse('Erreur lors de la mise à jour du point de vente');
    }
  }

  // Supprimer un point de vente
  static async deletePOS(posId) {
    try {
      // Vérifier s'il y a des sessions de caisse ouvertes
      const sessionCheck = await db.query(
        'SELECT session_id FROM "SessionsCaisse" WHERE pos_id = $1 AND statut = \'Ouverte\'',
        [posId]
      );

      if (sessionCheck.rows.length > 0) {
        return this.rejectResponse('Impossible de supprimer: des sessions de caisse sont ouvertes', 400);
      }

      const result = await db.query(
        'DELETE FROM "PointsDeVente" WHERE pos_id = $1 RETURNING pos_id',
        [posId]
      );
      
      if (result.rows.length === 0) {
        return this.rejectResponse('Point de vente non trouvé', 404);
      }
      
      return this.successResponse({ message: 'Point de vente supprimé avec succès' });
    } catch (error) {
      logger.error('Erreur suppression point de vente:', error);
      return this.rejectResponse('Erreur lors de la suppression du point de vente');
    }
  }

  // Créer automatiquement un point de vente pour un service
  static async createPOSForService(serviceId, complexeId) {
    try {
      // Récupérer les informations du service
      const serviceQuery = await db.query(
        'SELECT * FROM "ServicesComplexe" WHERE service_id = $1 AND complexe_id = $2',
        [serviceId, complexeId]
      );

      if (serviceQuery.rows.length === 0) {
        return this.rejectResponse('Service non trouvé', 404);
      }

      const service = serviceQuery.rows[0];

      // Créer le point de vente avec un nom par défaut
      const posData = {
        complexe_id,
        service_id: serviceId,
        nom: `POS ${service.nom}`,
        emplacement: service.emplacement || 'Non spécifié',
        fonds_caisse: 0,
        configuration: {
          auto_created: true,
          service_type: service.type_service
        }
      };

      return await this.createPOS(posData);
    } catch (error) {
      logger.error('Erreur création POS automatique:', error);
      return this.rejectResponse('Erreur lors de la création automatique du point de vente');
    }
  }

  // Transférer de l'argent entre deux POS
  static async transferFunds(fromPosId, toPosId, montant, notes, employeId) {
    try {
      await db.query('BEGIN');

      // Vérifier que les deux POS existent
      const fromPosQuery = await db.query(
        'SELECT * FROM "PointsDeVente" WHERE pos_id = $1 FOR UPDATE',
        [fromPosId]
      );

      const toPosQuery = await db.query(
        'SELECT * FROM "PointsDeVente" WHERE pos_id = $1 FOR UPDATE',
        [toPosId]
      );

      if (fromPosQuery.rows.length === 0 || toPosQuery.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Un ou plusieurs points de vente non trouvés', 404);
      }

      const fromPos = fromPosQuery.rows[0];
      const toPos = toPosQuery.rows[0];

      // Vérifier que le POS source a suffisamment de fonds
      if (fromPos.fonds_caisse < montant) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Fonds insuffisants dans le POS source', 400);
      }

      // Effectuer le transfert
      await db.query(
        'UPDATE "PointsDeVente" SET fonds_caisse = fonds_caisse - $1, updated_at = NOW() WHERE pos_id = $2',
        [montant, fromPosId]
      );

      await db.query(
        'UPDATE "PointsDeVente" SET fonds_caisse = fonds_caisse + $1, updated_at = NOW() WHERE pos_id = $2',
        [montant, toPosId]
      );

      // Enregistrer la transaction de transfert (optionnel - créer une table MouvementsFonds si nécessaire)
      // Pour l'instant, on peut utiliser les notes dans une table de logs

      await db.query('COMMIT');

      return this.successResponse({
        message: 'Transfert effectué avec succès',
        from_pos: fromPos.nom,
        to_pos: toPos.nom,
        montant: montant
      });
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur transfert fonds:', error);
      return this.rejectResponse('Erreur lors du transfert de fonds');
    }
  }

  // Retirer de l'argent d'un POS
  static async withdrawFunds(posId, montant, notes, employeId) {
    try {
      await db.query('BEGIN');

      // Vérifier que le POS existe
      const posQuery = await db.query(
        'SELECT * FROM "PointsDeVente" WHERE pos_id = $1 FOR UPDATE',
        [posId]
      );

      if (posQuery.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Point de vente non trouvé', 404);
      }

      const pos = posQuery.rows[0];

      // Vérifier que le POS a suffisamment de fonds
      if (pos.fonds_caisse < montant) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Fonds insuffisants', 400);
      }

      // Effectuer le retrait
      await db.query(
        'UPDATE "PointsDeVente" SET fonds_caisse = fonds_caisse - $1, updated_at = NOW() WHERE pos_id = $2',
        [montant, posId]
      );

      await db.query('COMMIT');

      return this.successResponse({
        message: 'Retrait effectué avec succès',
        pos_nom: pos.nom,
        montant: montant,
        nouveau_solde: pos.fonds_caisse - montant
      });
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur retrait fonds:', error);
      return this.rejectResponse('Erreur lors du retrait de fonds');
    }
  }

  // Ajouter de l'argent à un POS
  static async addFunds(posId, montant, notes, employeId) {
    try {
      await db.query('BEGIN');

      // Vérifier que le POS existe
      const posQuery = await db.query(
        'SELECT * FROM "PointsDeVente" WHERE pos_id = $1 FOR UPDATE',
        [posId]
      );

      if (posQuery.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Point de vente non trouvé', 404);
      }

      const pos = posQuery.rows[0];

      // Ajouter les fonds
      await db.query(
        'UPDATE "PointsDeVente" SET fonds_caisse = fonds_caisse + $1, updated_at = NOW() WHERE pos_id = $2',
        [montant, posId]
      );

      await db.query('COMMIT');

      return this.successResponse({
        message: 'Fonds ajoutés avec succès',
        pos_nom: pos.nom,
        montant: montant,
        nouveau_solde: pos.fonds_caisse + montant
      });
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur ajout fonds:', error);
      return this.rejectResponse('Erreur lors de l\'ajout de fonds');
    }
  }
}

module.exports = POSService;
