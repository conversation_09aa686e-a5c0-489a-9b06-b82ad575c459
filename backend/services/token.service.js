const jwt = require('jsonwebtoken');
const { JWT_SECRET, JWT_EXPIRATION } = require('../config/jwt.config');
const logger = require('../logger');

// In-memory token blacklist (in production, use Redis or similar)
const tokenBlacklist = new Set();

class TokenService {
  static generateToken(userData) {
    const payload = {
      id: userData.id,
      email: userData.email,
      role: userData.role,
      chaine_id: userData.chaine_id,
      complexe_id: userData.complexe_id,
      iat: Math.floor(Date.now() / 1000),
    };

    return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRATION });
  }

  static verifyToken(token) {
    try {
      if (tokenBlacklist.has(token)) {
        throw new Error('Token has been revoked');
      }
      return jwt.verify(token, JWT_SECRET);
    } catch (error) {
      logger.error('Token verification error:', error);
      throw error;
    }
  }

  static revokeToken(token) {
    try {
      const decoded = jwt.decode(token);
      if (decoded && decoded.exp) {
        // Add to blacklist with expiration
        tokenBlacklist.add(token);
        // Remove from blacklist after expiration
        setTimeout(() => {
          tokenBlacklist.delete(token);
        }, (decoded.exp * 1000) - Date.now());
      }
      return true;
    } catch (error) {
      logger.error('Token revocation error:', error);
      return false;
    }
  }

  static isTokenBlacklisted(token) {
    return tokenBlacklist.has(token);
  }

  static getTokenPayload(token) {
    try {
      return jwt.decode(token);
    } catch (error) {
      logger.error('Token decode error:', error);
      return null;
    }
  }
}

module.exports = TokenService; 