const XLSX = require('xlsx');
const BaseService = require('./base.service');
const logger = require('../logger');
const db = require('../db');
const ProduitIngredientService = require('./produitIngredient.service');
const StockManagementService = require('./stockManagement.service');

/**
 * Service d'import de menus avec liens ingrédients intégrés
 * Combine l'import de produits et la création automatique des liens avec les ingrédients
 */
class MenuIngredientImportService extends BaseService {

  /**
   * Import menu restaurant avec liens ingrédients depuis Excel
   */
  static async importRestaurantMenuWithIngredients(serviceId, excelBuffer, options = {}) {
    const client = await db.getClient();
    
    try {
      await client.query('BEGIN');
      
      // 1. Parser le fichier Excel
      const workbook = XLSX.read(excelBuffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);

      logger.info(`Parsing Excel file: ${data.length} rows found`);

      // 2. Valider les données
      const validationResult = this.validateMenuWithIngredientsData(data);
      if (!validationResult.isValid) {
        throw new Error(`Validation failed: ${validationResult.errors.join(', ')}`);
      }

      // 3. Récupérer les informations du service
      const serviceResult = await client.query(
        'SELECT * FROM "ServicesComplexe" WHERE service_id = $1',
        [serviceId]
      );

      if (serviceResult.rows.length === 0) {
        throw new Error('Service non trouvé');
      }

      const service = serviceResult.rows[0];
      const { complexe_id, chaine_id } = service;

      // 4. Créer les catégories si nécessaire
      const categories = await this.createCategoriesIfNeeded(client, data, chaine_id);

      // 5. Traiter chaque ligne du menu
      const importResults = [];
      let successCount = 0;
      let errorCount = 0;

      for (const row of data) {
        try {
          // 5.1 Créer le produit
          const categoryId = categories[row.categorie];
          
          const productResult = await client.query(
            `INSERT INTO "Produits" (
              categorie_id, chaine_id, service_id, nom, type_produit,
              prix_vente_defaut, description, temps_preparation,
              allergenes, image_url, actif, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW())
            RETURNING *`,
            [
              categoryId,
              chaine_id,
              serviceId,
              row.nom_plat,
              'Plat',
              parseFloat(row.prix_vente) || 0,
              row.description || '',
              parseInt(row.temps_preparation) || 0,
              row.allergenes || '',
              row.image_url || '',
              row.actif !== false
            ]
          );

          const product = productResult.rows[0];

          // 5.2 Traiter les ingrédients du produit
          const ingredientsData = this.parseIngredientsFromRow(row);
          const ingredientLinks = [];

          for (const ingredientData of ingredientsData) {
            try {
              // Vérifier que l'ingrédient existe
              const ingredientResult = await client.query(
                'SELECT * FROM "Ingredients" WHERE nom = $1 AND complexe_id = $2',
                [ingredientData.nom, complexe_id]
              );

              if (ingredientResult.rows.length === 0) {
                logger.warn(`Ingrédient "${ingredientData.nom}" non trouvé pour le produit "${row.nom_plat}"`);
                continue;
              }

              const ingredient = ingredientResult.rows[0];

              // Créer le lien produit-ingrédient
              await ProduitIngredientService.ajouterIngredientProduit(
                product.produit_id,
                ingredient.ingredient_id,
                parseFloat(ingredientData.quantite),
                ingredientData.unite || 'unité',
                {
                  coutUnitaire: ingredient.prix_unitaire_moyen,
                  optionnel: ingredientData.optionnel || false,
                  notes: ingredientData.notes || null
                }
              );

              ingredientLinks.push({
                ingredient_id: ingredient.ingredient_id,
                nom: ingredient.nom,
                quantite: ingredientData.quantite,
                unite: ingredientData.unite
              });

            } catch (ingredientError) {
              logger.error(`Erreur lors de l'ajout de l'ingrédient "${ingredientData.nom}":`, ingredientError);
            }
          }

          // 5.3 Calculer le coût du produit
          await ProduitIngredientService.calculerCoutProduit(product.produit_id);

          importResults.push({
            row: row,
            success: true,
            product: product,
            ingredients: ingredientLinks,
            message: `Produit créé avec ${ingredientLinks.length} ingrédients`
          });
          successCount++;

        } catch (error) {
          logger.error(`Erreur lors du traitement de la ligne "${row.nom_plat}":`, error);
          importResults.push({
            row: row,
            success: false,
            error: error.message,
            message: `Erreur: ${error.message}`
          });
          errorCount++;
        }
      }

      await client.query('COMMIT');

      return this.successResponse({
        importResults,
        summary: {
          total: data.length,
          success: successCount,
          errors: errorCount,
          serviceId: serviceId
        }
      }, `Import terminé: ${successCount} produits créés, ${errorCount} erreurs`);

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Erreur lors de l\'import menu avec ingrédients:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Import carte bar avec liens ingrédients depuis Excel
   */
  static async importBarMenuWithIngredients(serviceId, excelBuffer, options = {}) {
    const client = await db.getClient();
    
    try {
      await client.query('BEGIN');
      
      // 1. Parser le fichier Excel
      const workbook = XLSX.read(excelBuffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);

      logger.info(`Parsing Excel file: ${data.length} rows found`);

      // 2. Valider les données
      const validationResult = this.validateBarMenuWithIngredientsData(data);
      if (!validationResult.isValid) {
        throw new Error(`Validation failed: ${validationResult.errors.join(', ')}`);
      }

      // 3. Récupérer les informations du service
      const serviceResult = await client.query(
        'SELECT * FROM "ServicesComplexe" WHERE service_id = $1',
        [serviceId]
      );

      if (serviceResult.rows.length === 0) {
        throw new Error('Service non trouvé');
      }

      const service = serviceResult.rows[0];
      const { complexe_id, chaine_id } = service;

      // 4. Créer les catégories si nécessaire
      const categories = await this.createCategoriesIfNeeded(client, data, chaine_id);

      // 5. Traiter chaque ligne de la carte
      const importResults = [];
      let successCount = 0;
      let errorCount = 0;

      for (const row of data) {
        try {
          // 5.1 Créer le produit boisson
          const categoryId = categories[row.categorie];
          
          const productResult = await client.query(
            `INSERT INTO "Produits" (
              categorie_id, chaine_id, service_id, nom, type_produit,
              prix_vente_defaut, description, degre_alcool, volume_ml,
              allergenes, image_url, actif, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW())
            RETURNING *`,
            [
              categoryId,
              chaine_id,
              serviceId,
              row.nom_boisson,
              'Boisson',
              parseFloat(row.prix_vente) || 0,
              row.description || '',
              parseFloat(row.degre_alcool) || 0,
              parseInt(row.volume_ml) || 0,
              row.allergenes || '',
              row.image_url || '',
              row.actif !== false
            ]
          );

          const product = productResult.rows[0];

          // 5.2 Traiter les ingrédients/composants de la boisson
          const ingredientsData = this.parseIngredientsFromRow(row);
          const ingredientLinks = [];

          for (const ingredientData of ingredientsData) {
            try {
              // Vérifier que l'ingrédient/boisson existe
              const ingredientResult = await client.query(
                'SELECT * FROM "Ingredients" WHERE nom = $1 AND complexe_id = $2',
                [ingredientData.nom, complexe_id]
              );

              if (ingredientResult.rows.length === 0) {
                logger.warn(`Ingrédient "${ingredientData.nom}" non trouvé pour la boisson "${row.nom_boisson}"`);
                continue;
              }

              const ingredient = ingredientResult.rows[0];

              // Créer le lien produit-ingrédient
              await ProduitIngredientService.ajouterIngredientProduit(
                product.produit_id,
                ingredient.ingredient_id,
                parseFloat(ingredientData.quantite),
                ingredientData.unite || 'ml',
                {
                  coutUnitaire: ingredient.prix_unitaire_moyen,
                  optionnel: ingredientData.optionnel || false,
                  notes: ingredientData.notes || null
                }
              );

              ingredientLinks.push({
                ingredient_id: ingredient.ingredient_id,
                nom: ingredient.nom,
                quantite: ingredientData.quantite,
                unite: ingredientData.unite
              });

            } catch (ingredientError) {
              logger.error(`Erreur lors de l'ajout de l'ingrédient "${ingredientData.nom}":`, ingredientError);
            }
          }

          // 5.3 Calculer le coût du produit
          await ProduitIngredientService.calculerCoutProduit(product.produit_id);

          importResults.push({
            row: row,
            success: true,
            product: product,
            ingredients: ingredientLinks,
            message: `Boisson créée avec ${ingredientLinks.length} ingrédients`
          });
          successCount++;

        } catch (error) {
          logger.error(`Erreur lors du traitement de la ligne "${row.nom_boisson}":`, error);
          importResults.push({
            row: row,
            success: false,
            error: error.message,
            message: `Erreur: ${error.message}`
          });
          errorCount++;
        }
      }

      await client.query('COMMIT');

      return this.successResponse({
        importResults,
        summary: {
          total: data.length,
          success: successCount,
          errors: errorCount,
          serviceId: serviceId
        }
      }, `Import terminé: ${successCount} boissons créées, ${errorCount} erreurs`);

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Erreur lors de l\'import carte bar avec ingrédients:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Parser les ingrédients depuis une ligne Excel
   * Format attendu: "ingredient1:quantite:unite,ingredient2:quantite:unite"
   */
  static parseIngredientsFromRow(row) {
    const ingredients = [];

    // Vérifier s'il y a une colonne ingrédients
    if (!row.ingredients) {
      return ingredients;
    }

    try {
      const ingredientsStr = row.ingredients.toString().trim();
      if (!ingredientsStr) {
        return ingredients;
      }

      // Séparer les ingrédients par virgule
      const ingredientParts = ingredientsStr.split(',');

      for (const part of ingredientParts) {
        const trimmedPart = part.trim();
        if (!trimmedPart) continue;

        // Format: "nom:quantite:unite" ou "nom:quantite"
        const components = trimmedPart.split(':');

        if (components.length >= 2) {
          ingredients.push({
            nom: components[0].trim(),
            quantite: parseFloat(components[1]) || 1,
            unite: components[2] ? components[2].trim() : 'unité',
            optionnel: false,
            notes: null
          });
        } else {
          // Format simple: juste le nom (quantité par défaut = 1)
          ingredients.push({
            nom: trimmedPart,
            quantite: 1,
            unite: 'unité',
            optionnel: false,
            notes: null
          });
        }
      }
    } catch (error) {
      logger.error('Erreur lors du parsing des ingrédients:', error);
    }

    return ingredients;
  }

  /**
   * Créer les catégories si elles n'existent pas
   */
  static async createCategoriesIfNeeded(client, data, chaineId) {
    const categories = {};
    const uniqueCategories = [...new Set(data.map(row => row.categorie).filter(Boolean))];

    for (const categoryName of uniqueCategories) {
      try {
        // Vérifier si la catégorie existe
        const existingCategory = await client.query(
          'SELECT * FROM "CategoriesProduits" WHERE nom = $1 AND chaine_id = $2',
          [categoryName, chaineId]
        );

        if (existingCategory.rows.length > 0) {
          categories[categoryName] = existingCategory.rows[0].categorie_id;
        } else {
          // Créer la catégorie
          const newCategory = await client.query(
            `INSERT INTO "CategoriesProduits" (chaine_id, nom, description, actif, created_at)
             VALUES ($1, $2, $3, true, NOW()) RETURNING *`,
            [chaineId, categoryName, `Catégorie ${categoryName}`]
          );
          categories[categoryName] = newCategory.rows[0].categorie_id;
        }
      } catch (error) {
        logger.error(`Erreur lors de la création de la catégorie "${categoryName}":`, error);
        // Utiliser une catégorie par défaut si elle existe
        categories[categoryName] = null;
      }
    }

    return categories;
  }

  /**
   * Validation des données de menu restaurant avec ingrédients
   */
  static validateMenuWithIngredientsData(data) {
    const errors = [];
    const requiredFields = ['nom_plat', 'prix_vente', 'categorie'];

    if (!Array.isArray(data) || data.length === 0) {
      errors.push('Aucune donnée trouvée dans le fichier Excel');
      return { isValid: false, errors };
    }

    data.forEach((row, index) => {
      const rowNumber = index + 2; // +2 car Excel commence à 1 et on ignore l'en-tête

      // Vérifier les champs requis
      requiredFields.forEach(field => {
        if (!row[field] || row[field].toString().trim() === '') {
          errors.push(`Ligne ${rowNumber}: Le champ "${field}" est requis`);
        }
      });

      // Validation du prix
      if (row.prix_vente && isNaN(parseFloat(row.prix_vente))) {
        errors.push(`Ligne ${rowNumber}: Le prix de vente doit être un nombre`);
      }

      // Validation du temps de préparation
      if (row.temps_preparation && isNaN(parseInt(row.temps_preparation))) {
        errors.push(`Ligne ${rowNumber}: Le temps de préparation doit être un nombre entier`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validation des données de carte bar avec ingrédients
   */
  static validateBarMenuWithIngredientsData(data) {
    const errors = [];
    const requiredFields = ['nom_boisson', 'prix_vente', 'categorie'];

    if (!Array.isArray(data) || data.length === 0) {
      errors.push('Aucune donnée trouvée dans le fichier Excel');
      return { isValid: false, errors };
    }

    data.forEach((row, index) => {
      const rowNumber = index + 2;

      // Vérifier les champs requis
      requiredFields.forEach(field => {
        if (!row[field] || row[field].toString().trim() === '') {
          errors.push(`Ligne ${rowNumber}: Le champ "${field}" est requis`);
        }
      });

      // Validation du prix
      if (row.prix_vente && isNaN(parseFloat(row.prix_vente))) {
        errors.push(`Ligne ${rowNumber}: Le prix de vente doit être un nombre`);
      }

      // Validation du degré d'alcool
      if (row.degre_alcool && isNaN(parseFloat(row.degre_alcool))) {
        errors.push(`Ligne ${rowNumber}: Le degré d'alcool doit être un nombre`);
      }

      // Validation du volume
      if (row.volume_ml && isNaN(parseInt(row.volume_ml))) {
        errors.push(`Ligne ${rowNumber}: Le volume doit être un nombre entier`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

module.exports = MenuIngredientImportService;