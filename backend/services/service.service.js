const db = require('../db');
const logger = require('../logger');

class ServiceComplexeService {
  static async getAllServices(complexeId) {
    try {
      const result = await db.query(
        'SELECT * FROM "ServicesComplexe" WHERE complexe_id = $1 ORDER BY nom',
        [complexeId]
      );
      return result.rows;
    } catch (error) {
      logger.error('Error fetching services:', error);
      throw error;
    }
  }

  static async getServiceById(id) {
    try {
      const result = await db.query(
        'SELECT * FROM "ServicesComplexe" WHERE service_id = $1',
        [id]
      );

      if (result.rows.length === 0) {
        throw new Error('Service non trouvé');
      }

      return result.rows[0];
    } catch (error) {
      logger.error(`Error fetching service ${id}:`, error);
      throw error;
    }
  }

  static async createService(serviceData) {
    const { 
      complexe_id, type_service, nom, description, emplacement, 
      horaires_ouverture, capacite, image_url, contact_email, 
      contact_telephone, configuration, tarification 
    } = serviceData;

    try {
      const result = await db.query(
        `INSERT INTO "ServicesComplexe" (
          complexe_id, type_service, nom, description, emplacement, 
          horaires_ouverture, capacite, image_url, contact_email, 
          contact_telephone, configuration, tarification, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW()) 
        RETURNING *`,
        [complexe_id, type_service, nom, description, emplacement, 
         horaires_ouverture, capacite, image_url, contact_email, 
         contact_telephone, configuration, tarification]
      );

      return result.rows[0];
    } catch (error) {
      logger.error('Error creating service:', error);
      throw error;
    }
  }

  static async updateService(id, serviceData) {
    const { 
      type_service, nom, description, emplacement, 
      horaires_ouverture, capacite, image_url, contact_email, 
      contact_telephone, actif, configuration, tarification 
    } = serviceData;

    try {
      const result = await db.query(
        `UPDATE "ServicesComplexe" 
        SET type_service = $1, nom = $2, description = $3, emplacement = $4, 
            horaires_ouverture = $5, capacite = $6, image_url = $7, 
            contact_email = $8, contact_telephone = $9, actif = $10, 
            configuration = $11, tarification = $12, updated_at = NOW()
        WHERE service_id = $13 
        RETURNING *`,
        [type_service, nom, description, emplacement, 
         horaires_ouverture, capacite, image_url, contact_email, 
         contact_telephone, actif, configuration, tarification, id]
      );

      if (result.rows.length === 0) {
        throw new Error('Service non trouvé');
      }

      return result.rows[0];
    } catch (error) {
      logger.error(`Error updating service ${id}:`, error);
      throw error;
    }
  }

  static async deleteService(id) {
    try {
      const result = await db.query(
        'DELETE FROM "ServicesComplexe" WHERE service_id = $1 RETURNING *',
        [id]
      );

      if (result.rows.length === 0) {
        throw new Error('Service non trouvé');
      }

      return true;
    } catch (error) {
      logger.error(`Error deleting service ${id}:`, error);
      throw error;
    }
  }

  static async getServicesByType(complexeId, typeService) {
    try {
      const result = await db.query(
        'SELECT * FROM "ServicesComplexe" WHERE complexe_id = $1 AND type_service = $2 ORDER BY nom',
        [complexeId, typeService]
      );
      return result.rows;
    } catch (error) {
      logger.error(`Error fetching services by type ${typeService}:`, error);
      throw error;
    }
  }

  // ==================== GESTION DES TARIFICATIONS ====================

  /**
   * Récupérer la tarification d'un service
   */
  static async getTarificationByService(serviceId) {
    try {
      const result = await db.query(
        'SELECT service_id, nom, type_service, tarification FROM "ServicesComplexe" WHERE service_id = $1',
        [serviceId]
      );

      if (result.rows.length === 0) {
        throw new Error('Service non trouvé');
      }

      const service = result.rows[0];
      return {
        service_id: service.service_id,
        nom: service.nom,
        type_service: service.type_service,
        tarification: service.tarification || {}
      };
    } catch (error) {
      logger.error(`Error fetching tarification for service ${serviceId}:`, error);
      throw error;
    }
  }

  /**
   * Mettre à jour uniquement la tarification d'un service
   */
  static async updateTarification(serviceId, tarificationData) {
    try {
      // Vérifier que le service existe
      const serviceExists = await db.query(
        'SELECT service_id, type_service FROM "ServicesComplexe" WHERE service_id = $1',
        [serviceId]
      );

      if (serviceExists.rows.length === 0) {
        throw new Error('Service non trouvé');
      }

      const service = serviceExists.rows[0];

      // Valider la tarification selon le type de service
      const validatedTarification = this.validateTarification(service.type_service, tarificationData);

      // Mettre à jour la tarification
      const result = await db.query(
        'UPDATE "ServicesComplexe" SET tarification = $1, updated_at = NOW() WHERE service_id = $2 RETURNING *',
        [JSON.stringify(validatedTarification), serviceId]
      );

      return result.rows[0];
    } catch (error) {
      logger.error(`Error updating tarification for service ${serviceId}:`, error);
      throw error;
    }
  }

  /**
   * Valider la structure de tarification selon le type de service
   */
  static validateTarification(typeService, tarificationData) {
    if (!tarificationData || typeof tarificationData !== 'object') {
      throw new Error('Les données de tarification doivent être un objet JSON valide');
    }

    const validated = { ...tarificationData };

    switch (typeService) {
      case 'Restaurant':
        return this.validateRestaurantTarification(validated);
      case 'Bar':
        return this.validateBarTarification(validated);
      case 'Piscine':
        return this.validatePiscineTarification(validated);
      default:
        // Pour les autres types de services, validation basique
        return this.validateBasicTarification(validated);
    }
  }

  /**
   * Validation spécifique pour les restaurants
   */
  static validateRestaurantTarification(tarification) {
    const validatedTarif = {};

    // Menus et plats
    if (tarification.menus && typeof tarification.menus === 'object') {
      validatedTarif.menus = {};
      for (const [nom, prix] of Object.entries(tarification.menus)) {
        if (typeof prix === 'number' && prix >= 0) {
          validatedTarif.menus[nom] = prix;
        }
      }
    }

    // Boissons
    if (tarification.boissons && typeof tarification.boissons === 'object') {
      validatedTarif.boissons = {};
      for (const [nom, prix] of Object.entries(tarification.boissons)) {
        if (typeof prix === 'number' && prix >= 0) {
          validatedTarif.boissons[nom] = prix;
        }
      }
    }

    // Service de table (optionnel)
    if (tarification.service_table && typeof tarification.service_table === 'number' && tarification.service_table >= 0) {
      validatedTarif.service_table = tarification.service_table;
    }

    // Couvert par personne (optionnel)
    if (tarification.couvert_par_personne && typeof tarification.couvert_par_personne === 'number' && tarification.couvert_par_personne >= 0) {
      validatedTarif.couvert_par_personne = tarification.couvert_par_personne;
    }

    return validatedTarif;
  }

  /**
   * Validation spécifique pour les bars
   */
  static validateBarTarification(tarification) {
    const validatedTarif = {};

    // Boissons alcoolisées
    if (tarification.alcools && typeof tarification.alcools === 'object') {
      validatedTarif.alcools = {};
      for (const [nom, prix] of Object.entries(tarification.alcools)) {
        if (typeof prix === 'number' && prix >= 0) {
          validatedTarif.alcools[nom] = prix;
        }
      }
    }

    // Boissons non alcoolisées
    if (tarification.soft_drinks && typeof tarification.soft_drinks === 'object') {
      validatedTarif.soft_drinks = {};
      for (const [nom, prix] of Object.entries(tarification.soft_drinks)) {
        if (typeof prix === 'number' && prix >= 0) {
          validatedTarif.soft_drinks[nom] = prix;
        }
      }
    }

    // Cocktails
    if (tarification.cocktails && typeof tarification.cocktails === 'object') {
      validatedTarif.cocktails = {};
      for (const [nom, prix] of Object.entries(tarification.cocktails)) {
        if (typeof prix === 'number' && prix >= 0) {
          validatedTarif.cocktails[nom] = prix;
        }
      }
    }

    // Happy hour (optionnel)
    if (tarification.happy_hour && typeof tarification.happy_hour === 'object') {
      validatedTarif.happy_hour = {
        reduction_pourcentage: tarification.happy_hour.reduction_pourcentage || 0,
        heures: tarification.happy_hour.heures || []
      };
    }

    return validatedTarif;
  }

  /**
   * Validation spécifique pour les piscines
   */
  static validatePiscineTarification(tarification) {
    const validatedTarif = {};

    // Prix par personne
    if (tarification.prix_par_personne && typeof tarification.prix_par_personne === 'number' && tarification.prix_par_personne >= 0) {
      validatedTarif.prix_par_personne = tarification.prix_par_personne;
    }

    // Prix par heure
    if (tarification.prix_par_heure && typeof tarification.prix_par_heure === 'number' && tarification.prix_par_heure >= 0) {
      validatedTarif.prix_par_heure = tarification.prix_par_heure;
    }

    // Prix forfaitaire
    if (tarification.prix_forfaitaire && typeof tarification.prix_forfaitaire === 'number' && tarification.prix_forfaitaire >= 0) {
      validatedTarif.prix_forfaitaire = tarification.prix_forfaitaire;
    }

    // Tarifs par tranche d'âge
    if (tarification.tarifs_age && typeof tarification.tarifs_age === 'object') {
      validatedTarif.tarifs_age = {};
      for (const [tranche, prix] of Object.entries(tarification.tarifs_age)) {
        if (typeof prix === 'number' && prix >= 0) {
          validatedTarif.tarifs_age[tranche] = prix;
        }
      }
    }

    // Tarifs par durée
    if (tarification.tarifs_duree && typeof tarification.tarifs_duree === 'object') {
      validatedTarif.tarifs_duree = {};
      for (const [duree, prix] of Object.entries(tarification.tarifs_duree)) {
        if (typeof prix === 'number' && prix >= 0) {
          validatedTarif.tarifs_duree[duree] = prix;
        }
      }
    }

    return validatedTarif;
  }

  /**
   * Validation basique pour les autres types de services
   */
  static validateBasicTarification(tarification) {
    const validatedTarif = {};

    // Validation générique : tous les champs numériques positifs
    for (const [key, value] of Object.entries(tarification)) {
      if (typeof value === 'number' && value >= 0) {
        validatedTarif[key] = value;
      } else if (typeof value === 'object' && value !== null) {
        // Pour les objets imbriqués, validation récursive simple
        validatedTarif[key] = {};
        for (const [subKey, subValue] of Object.entries(value)) {
          if (typeof subValue === 'number' && subValue >= 0) {
            validatedTarif[key][subKey] = subValue;
          }
        }
      }
    }

    return validatedTarif;
  }

  /**
   * Obtenir un modèle de tarification par défaut selon le type de service
   */
  static getDefaultTarificationTemplate(typeService) {
    switch (typeService) {
      case 'Restaurant':
        return {
          menus: {
            "Menu du jour": 25.00,
            "Menu enfant": 15.00,
            "Menu dégustation": 45.00
          },
          boissons: {
            "Eau plate": 3.00,
            "Eau gazeuse": 3.50,
            "Soda": 4.00,
            "Jus de fruits": 5.00
          },
          service_table: 2.00,
          couvert_par_personne: 1.50
        };

      case 'Bar':
        return {
          alcools: {
            "Bière pression": 5.00,
            "Bière bouteille": 6.00,
            "Vin rouge (verre)": 7.00,
            "Vin blanc (verre)": 7.00,
            "Whisky": 8.00,
            "Vodka": 7.00
          },
          soft_drinks: {
            "Coca-Cola": 4.00,
            "Jus d'orange": 5.00,
            "Eau minérale": 3.00
          },
          cocktails: {
            "Mojito": 12.00,
            "Piña Colada": 14.00,
            "Cosmopolitan": 13.00
          },
          happy_hour: {
            reduction_pourcentage: 20,
            heures: ["17:00-19:00"]
          }
        };

      case 'Piscine':
        return {
          prix_par_personne: 10.00,
          prix_par_heure: 5.00,
          prix_forfaitaire: 25.00,
          tarifs_age: {
            "Enfant (0-12 ans)": 5.00,
            "Adolescent (13-17 ans)": 8.00,
            "Adulte (18+ ans)": 10.00,
            "Senior (65+ ans)": 7.00
          },
          tarifs_duree: {
            "1 heure": 10.00,
            "2 heures": 18.00,
            "Demi-journée": 25.00,
            "Journée complète": 40.00
          }
        };

      default:
        return {
          prix_base: 0.00,
          description: "Tarification personnalisée"
        };
    }
  }

  // ==================== INTÉGRATION AVEC LE SYSTÈME D'INVENTAIRE ====================

  /**
   * Mise à jour automatique des tarifs depuis les recettes
   */
  static async updateTarificationFromRecipes(serviceId) {
    try {
      await db.query('BEGIN');

      // Récupérer le service
      const serviceResult = await db.query(
        'SELECT * FROM "ServicesComplexe" WHERE service_id = $1',
        [serviceId]
      );

      if (serviceResult.rows.length === 0) {
        throw new Error('Service non trouvé');
      }

      const service = serviceResult.rows[0];

      // Récupérer les recettes du service avec leurs coûts calculés
      const recettesResult = await db.query(`
        SELECT
          r.*,
          p.nom as produit_nom,
          p.prix_vente_defaut,
          p.produit_id
        FROM "Recettes" r
        JOIN "Produits" p ON r.produit_id = p.produit_id
        WHERE r.service_id = $1 AND r.actif = true
      `, [serviceId]);

      const recettes = recettesResult.rows;

      if (recettes.length === 0) {
        logger.info(`Aucune recette trouvée pour le service ${serviceId}`);
        await db.query('COMMIT');
        return { updated: false, message: 'Aucune recette à traiter' };
      }

      // Récupérer la tarification actuelle
      let tarificationActuelle = service.tarification || {};
      if (typeof tarificationActuelle === 'string') {
        tarificationActuelle = JSON.parse(tarificationActuelle);
      }

      // Mettre à jour les prix basés sur les recettes
      let prixMisAJour = 0;
      const nouveauxPrix = {};

      for (const recette of recettes) {
        const coutCalcule = recette.cout_total_calcule || 0;
        const margeCible = recette.marge_beneficiaire_cible || 30; // 30% par défaut

        // Calculer le prix suggéré
        const prixSuggere = coutCalcule > 0
          ? Math.round((coutCalcule * (1 + margeCible / 100)) * 100) / 100
          : recette.prix_vente_suggere || 0;

        // Déterminer la catégorie selon le type de service
        let categorie = 'produits';
        if (service.type_service === 'Restaurant') {
          categorie = recette.produit_nom.toLowerCase().includes('boisson') ? 'boissons' : 'menus';
        } else if (service.type_service === 'Bar') {
          if (recette.produit_nom.toLowerCase().includes('cocktail')) {
            categorie = 'cocktails';
          } else if (recette.produit_nom.toLowerCase().includes('alcool')) {
            categorie = 'alcools';
          } else {
            categorie = 'soft_drinks';
          }
        }

        // Initialiser la catégorie si elle n'existe pas
        if (!tarificationActuelle[categorie]) {
          tarificationActuelle[categorie] = {};
        }

        // Mettre à jour seulement si le prix a changé significativement (>5%)
        const prixActuel = tarificationActuelle[categorie][recette.produit_nom] || 0;
        const differencePercent = Math.abs((prixSuggere - prixActuel) / Math.max(prixActuel, 1)) * 100;

        if (differencePercent > 5 || prixActuel === 0) {
          tarificationActuelle[categorie][recette.produit_nom] = prixSuggere;
          nouveauxPrix[recette.produit_nom] = {
            ancien_prix: prixActuel,
            nouveau_prix: prixSuggere,
            cout_ingredients: coutCalcule,
            marge_appliquee: margeCible
          };
          prixMisAJour++;

          // Mettre à jour le prix de vente par défaut du produit
          await db.query(
            'UPDATE "Produits" SET prix_vente_defaut = $1, updated_at = NOW() WHERE produit_id = $2',
            [prixSuggere, recette.produit_id]
          );
        }
      }

      // Sauvegarder la tarification mise à jour
      await db.query(
        'UPDATE "ServicesComplexe" SET tarification = $1, updated_at = NOW() WHERE service_id = $2',
        [JSON.stringify(tarificationActuelle), serviceId]
      );

      await db.query('COMMIT');

      logger.info('Tarification mise à jour depuis les recettes', {
        service_id: serviceId,
        prix_mis_a_jour: prixMisAJour,
        total_recettes: recettes.length
      });

      return {
        updated: true,
        prix_mis_a_jour: prixMisAJour,
        total_recettes: recettes.length,
        nouveaux_prix: nouveauxPrix,
        tarification: tarificationActuelle
      };

    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur mise à jour tarification depuis recettes:', error);
      throw error;
    }
  }

  /**
   * Synchronisation prix/coûts avec marge cible
   */
  static async syncPricesWithCosts(serviceId, targetMargin = 30) {
    try {
      await db.query('BEGIN');

      // Récupérer toutes les recettes du service
      const recettesResult = await db.query(`
        SELECT
          r.*,
          p.nom as produit_nom,
          p.produit_id,
          p.prix_vente_defaut
        FROM "Recettes" r
        JOIN "Produits" p ON r.produit_id = p.produit_id
        WHERE r.service_id = $1 AND r.actif = true
      `, [serviceId]);

      const recettes = recettesResult.rows;
      const resultats = [];

      for (const recette of recettes) {
        const coutActuel = recette.cout_total_calcule || 0;

        if (coutActuel > 0) {
          // Calculer le nouveau prix avec la marge cible
          const nouveauPrix = Math.round((coutActuel * (1 + targetMargin / 100)) * 100) / 100;

          // Mettre à jour la recette
          await db.query(`
            UPDATE "Recettes"
            SET
              marge_beneficiaire_cible = $1,
              prix_vente_suggere = $2,
              updated_at = NOW()
            WHERE recette_id = $3
          `, [targetMargin, nouveauPrix, recette.recette_id]);

          // Mettre à jour le produit
          await db.query(
            'UPDATE "Produits" SET prix_vente_defaut = $1, updated_at = NOW() WHERE produit_id = $2',
            [nouveauPrix, recette.produit_id]
          );

          resultats.push({
            produit_nom: recette.produit_nom,
            cout_ingredients: coutActuel,
            ancien_prix: recette.prix_vente_defaut,
            nouveau_prix: nouveauPrix,
            marge_appliquee: targetMargin,
            marge_reelle: ((nouveauPrix - coutActuel) / nouveauPrix * 100).toFixed(2)
          });
        }
      }

      // Mettre à jour la tarification du service
      await this.updateTarificationFromRecipes(serviceId);

      await db.query('COMMIT');

      logger.info('Synchronisation prix/coûts terminée', {
        service_id: serviceId,
        marge_cible: targetMargin,
        produits_mis_a_jour: resultats.length
      });

      return {
        success: true,
        marge_cible: targetMargin,
        produits_mis_a_jour: resultats.length,
        details: resultats
      };

    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur synchronisation prix/coûts:', error);
      throw error;
    }
  }

  /**
   * Génération de tarification depuis import Excel
   */
  static async generateTarificationFromImport(importId, serviceId) {
    try {
      await db.query('BEGIN');

      // Récupérer les informations de l'import
      const importResult = await db.query(
        'SELECT * FROM "ImportsExcel" WHERE import_id = $1 AND statut = $2',
        [importId, 'IMPORTE']
      );

      if (importResult.rows.length === 0) {
        throw new Error('Import non trouvé ou non finalisé');
      }

      const importData = importResult.rows[0];

      // Vérifier que l'import correspond au service
      if (importData.service_id && importData.service_id !== serviceId) {
        throw new Error('L\'import ne correspond pas au service spécifié');
      }

      // Récupérer le service
      const serviceResult = await db.query(
        'SELECT * FROM "ServicesComplexe" WHERE service_id = $1',
        [serviceId]
      );

      if (serviceResult.rows.length === 0) {
        throw new Error('Service non trouvé');
      }

      const service = serviceResult.rows[0];

      // Récupérer les produits créés lors de cet import
      const produitsResult = await db.query(`
        SELECT
          p.*,
          r.cout_total_calcule,
          r.marge_beneficiaire_cible,
          r.prix_vente_suggere
        FROM "Produits" p
        LEFT JOIN "Recettes" r ON p.produit_id = r.produit_id
        WHERE p.import_id = $1 AND p.service_id = $2
      `, [importId, serviceId]);

      const produits = produitsResult.rows;

      if (produits.length === 0) {
        logger.info(`Aucun produit trouvé pour l'import ${importId} et le service ${serviceId}`);
        await db.query('COMMIT');
        return {
          generated: false,
          message: 'Aucun produit à traiter pour cet import'
        };
      }

      // Récupérer la tarification actuelle
      let tarificationActuelle = service.tarification || {};
      if (typeof tarificationActuelle === 'string') {
        tarificationActuelle = JSON.parse(tarificationActuelle);
      }

      // Générer la nouvelle tarification
      const nouveauxProduits = {};
      let produitsAjoutes = 0;

      for (const produit of produits) {
        // Déterminer le prix à utiliser
        let prix = produit.prix_vente_defaut;

        // Si une recette existe avec un prix suggéré, l'utiliser
        if (produit.prix_vente_suggere && produit.prix_vente_suggere > 0) {
          prix = produit.prix_vente_suggere;
        }

        // Déterminer la catégorie selon le type de service et le type de produit
        let categorie = this.determineCategorieProduit(service.type_service, produit);

        // Initialiser la catégorie si elle n'existe pas
        if (!tarificationActuelle[categorie]) {
          tarificationActuelle[categorie] = {};
        }

        // Ajouter le produit à la tarification
        tarificationActuelle[categorie][produit.nom] = prix;

        nouveauxProduits[produit.nom] = {
          categorie,
          prix,
          type_produit: produit.type_produit,
          cout_ingredients: produit.cout_total_calcule || 0,
          marge: produit.marge_beneficiaire_cible || 0
        };

        produitsAjoutes++;
      }

      // Sauvegarder la tarification mise à jour
      await db.query(
        'UPDATE "ServicesComplexe" SET tarification = $1, updated_at = NOW() WHERE service_id = $2',
        [JSON.stringify(tarificationActuelle), serviceId]
      );

      // Marquer l'import comme intégré à la tarification
      await db.query(
        'UPDATE "ImportsExcel" SET notes = COALESCE(notes, \'\') || $1 WHERE import_id = $2',
        [`\nTarification générée le ${new Date().toISOString()} pour le service ${serviceId}`, importId]
      );

      await db.query('COMMIT');

      logger.info('Tarification générée depuis import', {
        import_id: importId,
        service_id: serviceId,
        produits_ajoutes: produitsAjoutes
      });

      return {
        generated: true,
        import_id: importId,
        service_id: serviceId,
        produits_ajoutes: produitsAjoutes,
        nouveaux_produits: nouveauxProduits,
        tarification: tarificationActuelle
      };

    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur génération tarification depuis import:', error);
      throw error;
    }
  }

  /**
   * Déterminer la catégorie d'un produit selon le type de service
   */
  static determineCategorieProduit(typeService, produit) {
    const nomProduit = produit.nom.toLowerCase();
    const typeProduit = produit.type_produit?.toLowerCase() || '';

    switch (typeService) {
      case 'Restaurant':
        if (nomProduit.includes('boisson') || typeProduit.includes('boisson')) {
          return 'boissons';
        }
        if (nomProduit.includes('menu') || typeProduit.includes('menu')) {
          return 'menus';
        }
        return 'menus'; // Par défaut pour restaurant

      case 'Bar':
        if (nomProduit.includes('cocktail') || typeProduit.includes('cocktail')) {
          return 'cocktails';
        }
        if (nomProduit.includes('alcool') || nomProduit.includes('bière') ||
            nomProduit.includes('vin') || nomProduit.includes('whisky') ||
            typeProduit.includes('alcool')) {
          return 'alcools';
        }
        return 'soft_drinks'; // Par défaut pour bar

      case 'Piscine':
        if (nomProduit.includes('enfant') || typeProduit.includes('enfant')) {
          return 'tarifs_age';
        }
        if (nomProduit.includes('heure') || typeProduit.includes('duree')) {
          return 'tarifs_duree';
        }
        return 'services_additionnels';

      default:
        return 'produits';
    }
  }
}

module.exports = ServiceComplexeService;