const db = require('../db');
const logger = require('../logger');
const { v4: uuidv4 } = require('uuid');
const DisponibiliteService = require('./disponibilite.service');
const QRCode = require('qrcode');
const PDFDocument = require('pdfkit');

class ReservationService {
  static successResponse(data) {
    return {
      success: true,
      data
    };
  }

  static errorResponse(message, code = 500) {
    return {
      success: false,
      message,
      code
    };
  }

  static rejectResponse(message, code = 500) {
    return {
      success: false,
      message,
      code
    };
  }

  // Méthodes utilitaires
  static async verifierReservationExiste(reservationId, statut = null) {
    const query = `
      SELECT * 
      FROM "Reservations" 
      WHERE reservation_id = $1
      ${statut ? 'AND statut = $2' : ''}
      FOR UPDATE
    `;
    
    const values = statut ? [reservationId, statut] : [reservationId];
    const result = await db.query(query, values);
    
    return {
      existe: result.rows.length > 0,
      reservation: result.rows[0]
    };
  }

  // Création d'une demande de réservation anonyme (délégation vers le service spécialisé)
  static async createDemandeReservationAnonyme(params) {
    const AnonymousReservationService = require('./anonymousReservation.service');
    return await AnonymousReservationService.createDemandeReservationAnonyme(params);
  }

  // Création d'une demande de réservation
  static async createDemandeReservation(params) {
    const {
      date_arrivee,
      date_depart,
      heure_debut,
      heure_fin,
      chambres,
      client_info,
      commentaires,
      prix_total,
      complexe_id
    } = params;

    try {
      await db.query('BEGIN');

      // Récupérer le chaine_id du complexe
      const complexeQuery = `
        SELECT chaine_id 
        FROM "ComplexesHoteliers" 
        WHERE complexe_id = $1
      `;
      
      const complexeResult = await db.query(complexeQuery, [complexe_id]);
      
      if (complexeResult.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.errorResponse('Complexe non trouvé', 404);
      }

      const chaine_id = complexeResult.rows[0].chaine_id;

      // Vérifier si le client existe déjà
      const clientQuery = `
        SELECT client_id 
        FROM "Clients" 
        WHERE telephone = $1 
        AND chaine_id = $2
      `;

      const clientResult = await db.query(clientQuery, [
        client_info.telephone,
        chaine_id
      ]);

      let clientId;

      if (clientResult.rows.length > 0) {
        // Client existant
        clientId = clientResult.rows[0].client_id;
        
        // Mettre à jour les informations du client si nécessaire
        const updateClientQuery = `
          UPDATE "Clients"
          SET 
            nom = COALESCE($1, nom),
            prenom = COALESCE($2, prenom),
            email = COALESCE($3, email),
            updated_at = CURRENT_TIMESTAMP
          WHERE client_id = $4
        `;

        await db.query(updateClientQuery, [
          client_info.nom,
          client_info.prenom,
          client_info.email,
          clientId
        ]);
      } else {
        // Créer un nouveau client
        const createClientQuery = `
          INSERT INTO "Clients" (
            chaine_id,
            complexe_creation_id,
            nom,
            prenom,
            telephone,
            email
          ) VALUES ($1, $2, $3, $4, $5, $6)
          RETURNING client_id
        `;

        const newClientResult = await db.query(createClientQuery, [
          chaine_id,
          complexe_id,
          client_info.nom,
          client_info.prenom,
          client_info.telephone,
          client_info.email
        ]);

        clientId = newClientResult.rows[0].client_id;
      }

      // Générer un numéro de réservation unique
      const numero_reservation = `RES-${uuidv4().slice(0, 8).toUpperCase()}`;

      // Générer le QR code
      const qrCodeData = {
        numero_reservation,
        date_arrivee,
        date_depart,
        heure_debut,
        heure_fin,
        client: {
          nom: client_info.nom,
          prenom: client_info.prenom,
          telephone: client_info.telephone
        }
      };

      const qrCode = await QRCode.toDataURL(JSON.stringify(qrCodeData));

      const query = `
        INSERT INTO "Reservations" (
          numero_reservation,
          complexe_id,
          client_id,
          date_arrivee,
          date_depart,
          heure_debut,
          heure_fin,
          commentaires,
          statut,
          created_at,
          montant_total,
          type_reservation,
          qr_code
        ) VALUES ($1, $2, $3, $4::date, $5::date, $6::time, $7::time, $8, $9, CURRENT_TIMESTAMP, $10, 'HEURE', $11)
        RETURNING *
      `;

      const values = [
        numero_reservation,
        complexe_id,
        clientId,
        date_arrivee,
        date_depart,
        heure_debut,
        heure_fin,
        commentaires,
        'en_attente',
        prix_total,
        qrCode
      ];

      const result = await db.query(query, values);

      // Créer les entrées dans ChambresReservees et DisponibilitesChambres
      for (const chambre of chambres) {
        // Créer l'entrée dans ChambresReservees
        const insertChambreQuery = `
          INSERT INTO "ChambresReservees" (
            reservation_id,
            chambre_id,
            date_debut,
            date_fin,
            heure_debut,
            heure_fin,
            prix_nuit,
            type_occupation
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, 'HEURE')
        `;

        await db.query(insertChambreQuery, [
          result.rows[0].reservation_id,
          chambre.chambre_id,
          date_arrivee,
          date_depart,
          heure_debut,
          heure_fin,
          chambre.prix_nuit
        ]);

        // Créer l'entrée dans DisponibilitesChambres
        await DisponibiliteService.createDisponibilite({
          chambre_id: chambre.chambre_id,
          date: date_arrivee,
          heure_debut,
          heure_fin,
          statut: 'en_attente',
          reservation_id: result.rows[0].reservation_id,
          type_reservation: 'DEMANDE'
        });
      }

      await db.query('COMMIT');
      logger.info('Demande de réservation créée', { numero_reservation });

      return this.successResponse(result.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur création demande réservation:', error);
      return this.errorResponse('Erreur lors de la création de la demande de réservation');
    }
  }

  // Historique des modifications
  static async getHistoriqueReservation(numero) {
    try {
      const query = `
        SELECT h.*, u.nom as utilisateur_nom, u.prenom as utilisateur_prenom
        FROM "HistoriqueReservations" h
        LEFT JOIN "Employes" u ON h.employe_id = u.employe_id
        WHERE h.numero_reservation = $1
        ORDER BY h.date_action DESC
      `;

      const result = await db.query(query, [numero]);

      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération historique:', error);
      return this.errorResponse('Erreur lors de la récupération de l\'historique');
    }
  }

  // Vérifier si un client est propriétaire d'une réservation
  static async verifierProprietaireReservation(numero, clientId) {
    try {
      const query = `
        SELECT 1 FROM "Reservations"
        WHERE numero_reservation = $1
        AND client_id = $2
      `;

      const result = await db.query(query, [numero, clientId]);
      return result.rows.length > 0;
    } catch (error) {
      logger.error('Erreur vérification propriétaire:', error);
      throw new Error('Erreur lors de la vérification du propriétaire');
    }
  }

  // Ajouter une action à l'historique
  static async ajouterHistoriqueAction(numero, params) {
    const { action, utilisateur_id, details } = params;

    try {
      const query = `
        INSERT INTO "HistoriqueReservations" (
          numero_reservation,
          action,
          utilisateur_id,
          details,
          date_action
        ) VALUES ($1, $2, $3, $4, NOW())
      `;

      await db.query(query, [numero, action, utilisateur_id, details]);
    } catch (error) {
      logger.error('Erreur ajout historique:', error);
      throw new Error('Erreur lors de l\'ajout à l\'historique');
    }
  }

  // Création d'une réservation
  static async createReservation(params) {
    const {
      client_id,
      date_arrivee,
      date_depart,
      nombre_personnes,
      type_chambre,
      nombre_chambres,
      montant_total,
      commentaire,
      utilisateur_id
    } = params;

    try {
      await db.query('BEGIN');

      // Générer le numéro de réservation
      const numero_reservation = `RES-${uuidv4().slice(0, 8).toUpperCase()}`;

      // Créer la réservation
      const createQuery = `
        INSERT INTO "Reservations" (
          numero_reservation,
          client_id,
          date_arrivee,
          date_depart,
          nombre_personnes,
          type_chambre,
          nombre_chambres,
          montant_total,
          commentaire,
          statut,
          utilisateur_id,
          created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, 'en_attente', $10, NOW())
        RETURNING *
      `;

      const createResult = await db.query(createQuery, [
        numero_reservation,
        client_id,
        date_arrivee,
        date_depart,
        nombre_personnes,
        type_chambre,
        nombre_chambres,
        montant_total,
        commentaire,
        utilisateur_id
      ]);

      await db.query('COMMIT');
      logger.info('Réservation créée', {
        reservation_id: createResult.rows[0].reservation_id,
        numero_reservation,
        client_id,
        statut: 'EN_ATTENTE'
      });

      return this.successResponse(createResult.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur création réservation:', error);
      return this.errorResponse('Erreur lors de la création de la réservation');
    }
  }

  // Récupération des chambres d'une réservation
  static async getChambresReservation(reservationId) {
    try {
      const query = `
        SELECT
          cr.chambre_reservee_id,
          cr.chambre_id,
          cr.date_debut,
          cr.date_fin,
          cr.heure_debut,
          cr.heure_fin,
          cr.prix_nuit,
          cr.statut,
          cr.type_occupation,
          ch.numero,
          ch.type_chambre,
          ch.capacite,
          ch.description,
          ch.etage
        FROM "ChambresReservees" cr
        LEFT JOIN "Chambres" ch ON cr.chambre_id = ch.chambre_id
        WHERE cr.reservation_id = $1
        ORDER BY ch.numero
      `;

      const result = await db.query(query, [reservationId]);
      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération chambres réservation:', error);
      return this.errorResponse('Erreur lors de la récupération des chambres de la réservation');
    }
  }

  // Récupération des paiements d'une réservation
  static async getPaiementsReservation(reservationId) {
    try {
      const query = `
        SELECT
          pr.paiement_reservation_id,
          pr.montant,
          pr.mode_paiement,
          pr.reference_paiement,
          pr.date_paiement,
          pr.statut,
          pr.notes,
          pr.utilisateur_id,
          pr.type_utilisateur,
          pr.created_at,
          pr.updated_at
        FROM "PaiementsReservations" pr
        WHERE pr.reservation_id = $1
        ORDER BY pr.date_paiement DESC
      `;

      const result = await db.query(query, [reservationId]);

      // Calculer le total payé
      const totalPaye = result.rows
        .filter(p => p.statut === 'Validé')
        .reduce((total, paiement) => total + parseFloat(paiement.montant), 0);

      return this.successResponse({
        paiements: result.rows,
        total_paye: totalPaye,
        nombre_paiements: result.rows.length
      });
    } catch (error) {
      logger.error('Erreur récupération paiements réservation:', error);
      return this.errorResponse('Erreur lors de la récupération des paiements de la réservation');
    }
  }

  // Récupération des détails d'une réservation
  static async getReservationById(reservationId) {
    try {
      const query = `
        SELECT
          r.reservation_id,
          r.numero_reservation,
          r.date_arrivee,
          r.date_depart,
          r.statut,
          r.type_reservation,
          r.montant_total,
          r.commentaires,
          r.heure_debut,
          r.heure_fin,
          c.nom as client_nom,
          c.prenom as client_prenom,
          c.email as client_email,
          c.telephone as client_telephone
        FROM "Reservations" r
        LEFT JOIN "Clients" c ON r.client_id = c.client_id
        WHERE r.reservation_id = $1
      `;
      const result = await db.query(query, [reservationId]);
      if (result.rows.length === 0) {
        return this.errorResponse('Réservation non trouvée', 404);
      }

      const reservation = result.rows[0];

      // Récupérer les chambres de la réservation
      const chambresResult = await this.getChambresReservation(reservationId);
      if (chambresResult.success) {
        reservation.chambres = chambresResult.data;
      } else {
        reservation.chambres = [];
      }

      return this.successResponse(reservation);
    } catch (error) {
      logger.error('Erreur récupération détails réservation:', error);
      return this.errorResponse('Erreur lors de la récupération des détails de la réservation');
    }
  }

  // Mise à jour du statut d'une réservation
  static async updateReservationStatus(reservationId, statut) {
    try {
      await db.query('BEGIN');

      // Vérifier si la réservation existe
      const { existe, reservation } = await this.verifierReservationExiste(reservationId);
      
      if (!existe) {
        await db.query('ROLLBACK');
        return this.errorResponse('Réservation non trouvée', 404);
      }

      // Vérifier la validité du nouveau statut
      const statutsValides = ['en_attente', 'confirmee', 'annulee', 'terminee'];
      if (!statutsValides.includes(statut)) {
        await db.query('ROLLBACK');
        return this.errorResponse('Statut invalide', 400);
      }

      // Mettre à jour le statut
      const updateQuery = `
        UPDATE "Reservations" 
        SET 
          statut = $1,
          updated_at = CURRENT_TIMESTAMP
        WHERE reservation_id = $2
        RETURNING *
      `;

      const updateResult = await db.query(updateQuery, [statut, reservationId]);

      await db.query('COMMIT');
      logger.info('Statut réservation mis à jour', {
        reservation_id: reservationId,
        ancien_statut: reservation.statut,
        nouveau_statut: statut
      });

      return this.successResponse(updateResult.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur mise à jour statut réservation:', error);
      return this.errorResponse('Erreur lors de la mise à jour du statut de la réservation');
    }
  }

  // Liste des réservations en attente
  static async getPendingReservations(params) {
    const { type_chambre, tri = 'created_at' } = params;

    try {
      let query = `
        SELECT
          r.reservation_id,
          r.numero_reservation,
          r.date_arrivee,
          r.date_depart,
          r.statut,
          r.type_reservation,
          r.montant_total,
          r.commentaires,
          r.created_at,
          r.updated_at,
          c.nom as client_nom,
          c.prenom as client_prenom,
          c.email as client_email,
          c.telephone as client_telephone,
          cr.heure_debut,
          cr.heure_fin
        FROM "Reservations" r
        LEFT JOIN "Clients" c ON r.client_id = c.client_id
        LEFT JOIN "ChambresReservees" cr ON r.reservation_id = cr.reservation_id
        WHERE r.statut = 'en_attente'
      `;

      const values = [];
      let paramIndex = 1;

      if (type_chambre) {
        query += ` AND r.type_chambre = $${paramIndex}`;
        values.push(type_chambre);
        paramIndex++;
      }

      query += `
        ORDER BY 
          CASE 
            WHEN $${paramIndex} = 'created_at' THEN r.created_at
            WHEN $${paramIndex} = 'date_arrivee' THEN r.date_arrivee
            WHEN $${paramIndex} = 'date_depart' THEN r.date_depart
            ELSE r.created_at
          END DESC
      `;

      values.push(tri);

      const reservationsResult = await db.query(query, values);
      const reservations = reservationsResult.rows;

      if (reservations.length === 0) {
        return {
          success: true,
          data: {
            reservations: [],
            total: 0,
            filtres: { type_chambre, tri }
          }
        };
      }

      const reservationIds = reservations.map(r => r.reservation_id);
      
      const detailsQuery = `
        SELECT 
          r.reservation_id,
          COUNT(cr.chambre_id) as nombre_chambres_attribuees,
          COALESCE(SUM(p.montant), 0) as montant_paye
        FROM "Reservations" r
        LEFT JOIN "ChambresReservees" cr ON r.reservation_id = cr.reservation_id
        LEFT JOIN "Paiements" p ON r.reservation_id = p.transaction_id
        WHERE r.reservation_id = ANY($1)
        GROUP BY r.reservation_id
      `;
      
      const detailsResult = await db.query(detailsQuery, [reservationIds]);
      const details = detailsResult.rows;

      const reservationsWithDetails = reservations.map(reservation => {
        const detail = details.find(d => d.reservation_id === reservation.reservation_id);
        return {
          ...reservation,
          nombre_chambres_attribuees: detail ? detail.nombre_chambres_attribuees : 0,
          montant_paye: detail ? detail.montant_paye : 0,
          duree_sejour: reservation.date_depart - reservation.date_arrivee,
          montant_restant: reservation.montant_total - (detail ? detail.montant_paye : 0)
        };
      });

      return {
        success: true,
        data: {
          reservations: reservationsWithDetails,
          total: reservationsWithDetails.length,
          filtres: { type_chambre, tri }
        }
      };

    } catch (error) {
      logger.error('Erreur récupération réservations en attente:', error);
      return {
        success: false,
        message: 'Erreur lors de la récupération des réservations en attente'
      };
    }
  }

  // Confirmation d'une réservation
  static async confirmReservation(reservationId, params) {
    const { chambres_ids, utilisateur_id, paiement } = params;

    try {
      await db.query('BEGIN');

      // Vérifier si la réservation existe et est en attente
      const { existe, reservation } = await this.verifierReservationExiste(reservationId, 'en_attente');

      // Gérer les réservations anonymes différemment
      if (reservation && reservation.est_anonyme) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Les réservations anonymes doivent être confirmées via le service anonyme', 400);
      }
      
      if (!existe) {
        await db.query('ROLLBACK');
        return this.errorResponse('Réservation non trouvée ou déjà confirmée', 404);
      }

      // Vérifier si des chambres sont déjà attribuées (cas des demandes de réservation)
      const existingChambresQuery = `
        SELECT COUNT(*) as count FROM "ChambresReservees"
        WHERE reservation_id = $1
      `;
      const existingChambresResult = await db.query(existingChambresQuery, [reservationId]);
      const hasExistingChambres = existingChambresResult.rows[0].count > 0;

      if (hasExistingChambres) {
        // Les chambres sont déjà attribuées (demande de réservation), pas besoin de les re-attribuer
        logger.info('Chambres déjà attribuées pour cette réservation', { reservationId });
      } else if (chambres_ids && chambres_ids.length > 0) {
        // Attribuer les nouvelles chambres (réservation directe)
        const attributionQuery = `
          INSERT INTO "ChambresReservees" (
            reservation_id,
            chambre_id,
            created_at
          ) VALUES ($1, $2, NOW())
        `;

        for (const chambreId of chambres_ids) {
          await db.query(attributionQuery, [reservationId, chambreId]);
        }
        logger.info('Nouvelles chambres attribuées', { reservationId, chambres_ids });
      } else {
        await db.query('ROLLBACK');
        return this.errorResponse('Aucune chambre spécifiée et aucune chambre déjà attribuée', 400);
      }

      // Vérifier si l'utilisateur est un employé
      let isEmploye = false;
      if (utilisateur_id) {
        const employeCheckQuery = `
          SELECT employe_id FROM "Employes" WHERE employe_id = $1
        `;
        const employeCheckResult = await db.query(employeCheckQuery, [utilisateur_id]);
        isEmploye = employeCheckResult.rows.length > 0;
      }

      // Mettre à jour le statut de la réservation
      let updateQuery, updateValues;

      if (isEmploye) {
        updateQuery = `
          UPDATE "Reservations"
          SET
            statut = 'confirmee',
            updated_at = CURRENT_TIMESTAMP,
            employe_id = $1
          WHERE reservation_id = $2
          RETURNING *
        `;
        updateValues = [utilisateur_id, reservationId];
      } else {
        updateQuery = `
          UPDATE "Reservations"
          SET
            statut = 'confirmee',
            updated_at = CURRENT_TIMESTAMP
          WHERE reservation_id = $1
          RETURNING *
        `;
        updateValues = [reservationId];
      }

      const updateResult = await db.query(updateQuery, updateValues);
      
      // Enregistrer le paiement si des informations de paiement sont fournies
      if (paiement && paiement.montant > 0) {
        logger.info('Traitement du paiement pour la réservation', {
          reservationId,
          montant: paiement.montant
        });

        // Créer une référence de paiement unique
        const referencePaiement = paiement.reference_paiement || `PAY-${reservation.numero_reservation}-${Date.now()}`;

        // Déterminer le type d'utilisateur
        let typeUtilisateur = 'employe';
        if (!isEmploye) {
          // Vérifier si c'est un admin de complexe ou de chaîne
          const adminComplexeQuery = `
            SELECT admin_complexe_id FROM "AdminsComplexe" WHERE admin_complexe_id = $1
          `;
          const adminComplexeResult = await db.query(adminComplexeQuery, [utilisateur_id]);

          if (adminComplexeResult.rows.length > 0) {
            typeUtilisateur = 'admin_complexe';
          } else {
            const adminChaineQuery = `
              SELECT admin_chaine_id FROM "AdminsChaine" WHERE admin_complexe_id = $1
            `;
            const adminChaineResult = await db.query(adminChaineQuery, [utilisateur_id]);

            if (adminChaineResult.rows.length > 0) {
              typeUtilisateur = 'admin_chaine';
            }
          }
        }

        // Enregistrer le paiement dans la table dédiée aux réservations
        const paiementQuery = `
          INSERT INTO "PaiementsReservations" (
            reservation_id,
            montant,
            mode_paiement,
            reference_paiement,
            date_paiement,
            statut,
            notes,
            utilisateur_id,
            type_utilisateur
          ) VALUES ($1, $2, $3, $4, NOW(), 'Validé', $5, $6, $7)
          RETURNING *
        `;

        const paiementResult = await db.query(paiementQuery, [
          reservationId,
          paiement.montant,
          paiement.mode_paiement,
          referencePaiement,
          `Paiement initial pour réservation ${reservation.numero_reservation}`,
          utilisateur_id,
          typeUtilisateur
        ]);

        logger.info('Paiement enregistré', {
          paiement_id: paiementResult.rows[0].paiement_reservation_id,
          montant: paiement.montant,
          reference: referencePaiement
        });

        // Si le paiement couvre le montant total, mettre à jour le statut de la réservation à 'payee'
        if (paiement.montant >= reservation.montant_total) {
          await db.query(`
            UPDATE "Reservations"
            SET statut = 'payee'
            WHERE reservation_id = $1
          `, [reservationId]);

          logger.info('Réservation marquée comme payée', { reservationId });
        }
      }

      await db.query('COMMIT');
      logger.info('Réservation confirmée', {
        reservation_id: reservationId,
        chambres_ids,
        utilisateur_id,
        paiement: paiement ? true : false
      });

      // Récupérer la réservation mise à jour
      const reservationFinal = await this.getReservationById(reservationId);

      if (reservationFinal.success && reservationFinal.data) {
        // Générer un nouveau QR code avec les informations mises à jour
        const nouveauQRCode = await this.genererNouveauQRCodeConfirmation(reservationId, paiement);

        if (nouveauQRCode.success) {
          reservationFinal.data.nouveau_qr_code = nouveauQRCode.data.qr_code;
          reservationFinal.data.qr_code_data = nouveauQRCode.data.qr_data;
        }
      }

      return reservationFinal;
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur confirmation réservation:', error);
      return this.errorResponse('Erreur lors de la confirmation de la réservation');
    }
  }

  // Rejeter une réservation
  static async rejectReservation(reservationId, reason) {
    try {
      logger.info('Début du rejet de réservation', { reservationId, reason });
      await db.query('BEGIN');

      // Vérifier si la réservation existe
      const reservation = await this.getReservationById(reservationId);
      if (!reservation.success) {
        await db.query('ROLLBACK');
        throw new Error('Réservation non trouvée');
      }

      logger.info('Réservation trouvée', { statut: reservation.data.statut });

      // Vérifier si la réservation est en attente
      if (reservation.data.statut !== 'en_attente') {
        await db.query('ROLLBACK');
        throw new Error('Seules les réservations en attente peuvent être rejetées');
      }

      // Mettre à jour le statut de la réservation
      logger.info('Mise à jour du statut de la réservation');
      const updateReservationQuery = `
        UPDATE "Reservations"
        SET statut = 'annulee',
            raison_rejet = $1,
            updated_at = CURRENT_TIMESTAMP
        WHERE reservation_id = $2
        RETURNING *
      `;
      const result = await db.query(updateReservationQuery, [reason, reservationId]);
      logger.info('Statut mis à jour', { rowCount: result.rowCount });

      // Supprimer les entrées dans ChambresReservees (car la demande est rejetée)
      logger.info('Suppression des ChambresReservees');
      const deleteChambresReserveesQuery = `
        DELETE FROM "ChambresReservees"
        WHERE reservation_id = $1
      `;
      const chambresResult = await db.query(deleteChambresReserveesQuery, [reservationId]);
      logger.info('ChambresReservees supprimées', { rowCount: chambresResult.rowCount });

      // Supprimer les entrées dans DisponibilitesChambres (libérer les créneaux)
      logger.info('Suppression des DisponibilitesChambres');
      const deleteDisponibilitesQuery = `
        DELETE FROM "DisponibilitesChambres"
        WHERE reservation_id = $1 AND type_reservation = 'DEMANDE'
      `;
      const disponibilitesResult = await db.query(deleteDisponibilitesQuery, [reservationId]);
      logger.info('DisponibilitesChambres supprimées', { rowCount: disponibilitesResult.rowCount });

      await db.query('COMMIT');
      logger.info('Transaction commitée - Réservation rejetée avec nettoyage complet', {
        reservation_id: reservationId,
        raison: reason,
      });

      return this.successResponse(result.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur dans rejectReservation - Transaction rollback effectué:', error);
      return this.rejectResponse(error.message);
    }
  }

  // Fonction utilitaire pour nettoyer les données orphelines
  static async cleanupOrphanedData() {
    try {
      await db.query('BEGIN');

      // Nettoyer les ChambresReservees orphelines (sans réservation correspondante)
      const cleanupChambresQuery = `
        DELETE FROM "ChambresReservees"
        WHERE reservation_id NOT IN (
          SELECT reservation_id FROM "Reservations"
        )
      `;
      const chambresResult = await db.query(cleanupChambresQuery);

      // Nettoyer les DisponibilitesChambres orphelines
      const cleanupDisponibilitesQuery = `
        DELETE FROM "DisponibilitesChambres"
        WHERE reservation_id IS NOT NULL
        AND reservation_id NOT IN (
          SELECT reservation_id FROM "Reservations"
        )
      `;
      const disponibilitesResult = await db.query(cleanupDisponibilitesQuery);

      await db.query('COMMIT');

      logger.info('Nettoyage des données orphelines terminé', {
        chambres_supprimees: chambresResult.rowCount,
        disponibilites_supprimees: disponibilitesResult.rowCount,
      });

      return this.successResponse({
        chambres_supprimees: chambresResult.rowCount,
        disponibilites_supprimees: disponibilitesResult.rowCount,
      });
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur lors du nettoyage des données orphelines:', error);
      return this.rejectResponse('Erreur lors du nettoyage des données orphelines');
    }
  }

  // Récupérer l'historique des réservations pour le calendrier
  static async getHistoriqueReservationsCalendrier(params) {
    const { date_debut, date_fin, chambre_id } = params;

    try {
      let query = `
        SELECT
          r.reservation_id,
          r.numero_reservation,
          r.date_arrivee,
          r.date_depart,
          r.statut,
          r.type_reservation,
          r.montant_total,
          r.commentaires,
          r.created_at,
          r.updated_at,
          r.raison_rejet,
          c.nom as client_nom,
          c.prenom as client_prenom,
          c.email as client_email,
          c.telephone as client_telephone,
          ch.chambre_id,
          ch.numero as chambre_numero,
          ch.type_chambre,
          cr.heure_debut,
          cr.heure_fin,
          cr.prix_nuit
        FROM "Reservations" r
        LEFT JOIN "Clients" c ON r.client_id = c.client_id
        LEFT JOIN "ChambresReservees" cr ON r.reservation_id = cr.reservation_id
        LEFT JOIN "Chambres" ch ON cr.chambre_id = ch.chambre_id
        WHERE (r.date_arrivee <= $2 AND r.date_depart >= $1)
      `;

      const values = [date_debut, date_fin];
      let paramIndex = 3;

      // Filtrer par chambre spécifique si demandé
      if (chambre_id) {
        query += ` AND ch.chambre_id = $${paramIndex}`;
        values.push(chambre_id);
        paramIndex++;
      }

      query += `
        ORDER BY ch.chambre_id, r.date_arrivee
      `;

      const result = await db.query(query, values);
      const reservations = result.rows;

      // Grouper par chambre
      const reservationsParChambre = {};

      reservations.forEach(reservation => {
        const chambreId = reservation.chambre_id;
        if (!reservationsParChambre[chambreId]) {
          reservationsParChambre[chambreId] = {
            chambre_id: chambreId,
            chambre_numero: reservation.chambre_numero,
            type_chambre: reservation.type_chambre,
            reservations: []
          };
        }

        reservationsParChambre[chambreId].reservations.push({
          reservation_id: reservation.reservation_id,
          numero_reservation: reservation.numero_reservation,
          date_arrivee: reservation.date_arrivee,
          date_depart: reservation.date_depart,
          statut: reservation.statut,
          type_reservation: reservation.type_reservation,
          montant_total: reservation.montant_total,
          commentaires: reservation.commentaires,
          raison_rejet: reservation.raison_rejet,
          client_nom: reservation.client_nom,
          client_prenom: reservation.client_prenom,
          client_email: reservation.client_email,
          client_telephone: reservation.client_telephone,
          heure_debut: reservation.heure_debut,
          heure_fin: reservation.heure_fin,
          prix_nuit: reservation.prix_nuit,
          created_at: reservation.created_at,
          updated_at: reservation.updated_at
        });
      });

      return this.successResponse({
        chambres: Object.values(reservationsParChambre),
        periode: {
          date_debut,
          date_fin
        },
        total_reservations: reservations.length
      });

    } catch (error) {
      logger.error('Erreur récupération historique réservations calendrier:', error);
      return this.rejectResponse('Erreur lors de la récupération de l\'historique des réservations');
    }
  }

  // Générer un nouveau QR code avec les informations de confirmation
  static async genererNouveauQRCodeConfirmation(reservationId, paiementInfo) {
    try {
      // Récupérer les détails complets de la réservation confirmée
      const reservationQuery = `
        SELECT
          r.*,
          c.nom as client_nom,
          c.prenom as client_prenom,
          c.telephone as client_telephone,
          c.email as client_email
        FROM "Reservations" r
        LEFT JOIN "Clients" c ON r.client_id = c.client_id
        WHERE r.reservation_id = $1
      `;

      const reservationResult = await db.query(reservationQuery, [reservationId]);

      if (reservationResult.rows.length === 0) {
        return this.errorResponse('Réservation non trouvée', 404);
      }

      const reservation = reservationResult.rows[0];

      // Récupérer les informations de paiement
      let montantPaye = 0;
      if (paiementInfo && paiementInfo.montant) {
        montantPaye = paiementInfo.montant;
      }

      // Récupérer les chambres attribuées
      const chambresQuery = `
        SELECT
          ch.numero,
          ch.type_chambre,
          cr.prix_nuit
        FROM "ChambresReservees" cr
        JOIN "Chambres" ch ON cr.chambre_id = ch.chambre_id
        WHERE cr.reservation_id = $1
      `;

      const chambresResult = await db.query(chambresQuery, [reservationId]);

      // Créer les données du nouveau QR code avec toutes les informations de confirmation
      const qrCodeData = {
        numero_reservation: reservation.numero_reservation,
        statut: reservation.statut,
        date_confirmation: new Date().toISOString(),
        client: {
          nom: reservation.client_nom,
          prenom: reservation.client_prenom,
          telephone: reservation.client_telephone,
          email: reservation.client_email
        },
        sejour: {
          date_arrivee: reservation.date_arrivee,
          date_depart: reservation.date_depart,
          heure_debut: reservation.heure_debut,
          heure_fin: reservation.heure_fin
        },
        chambres: chambresResult.rows.map(ch => ({
          numero: ch.numero,
          type: ch.type_chambre,
          prix_nuit: ch.prix_nuit
        })),
        paiement: {
          montant_total: reservation.montant_total,
          montant_paye: montantPaye,
          montant_restant: reservation.montant_total - montantPaye,
          mode_paiement: paiementInfo ? paiementInfo.mode_paiement : null,
          reference: paiementInfo ? paiementInfo.reference_paiement : null
        },
        complexe_id: reservation.complexe_id,
        timestamp: Date.now()
      };

      // Générer le nouveau QR code
      const nouveauQRCode = await QRCode.toDataURL(JSON.stringify(qrCodeData));

      // Mettre à jour la réservation avec le nouveau QR code
      await db.query(`
        UPDATE "Reservations"
        SET qr_code = $1, updated_at = CURRENT_TIMESTAMP
        WHERE reservation_id = $2
      `, [nouveauQRCode, reservationId]);

      logger.info('Nouveau QR code généré pour la réservation confirmée', {
        reservationId,
        numero_reservation: reservation.numero_reservation
      });

      return this.successResponse({
        qr_code: nouveauQRCode,
        qr_data: qrCodeData
      });

    } catch (error) {
      logger.error('Erreur génération nouveau QR code:', error);
      return this.errorResponse('Erreur lors de la génération du nouveau QR code');
    }
  }

  // Générer un ticket de caisse avec le nouveau QR code
  static async genererTicketCaisse(reservationId) {
    try {
      // Récupérer les détails de la réservation avec le nouveau QR code
      const reservationQuery = `
        SELECT
          r.*,
          c.nom as client_nom,
          c.prenom as client_prenom,
          c.telephone as client_telephone,
          c.email as client_email
        FROM "Reservations" r
        LEFT JOIN "Clients" c ON r.client_id = c.client_id
        WHERE r.reservation_id = $1
      `;

      const reservationResult = await db.query(reservationQuery, [reservationId]);

      if (reservationResult.rows.length === 0) {
        return this.errorResponse('Réservation non trouvée', 404);
      }

      const reservation = reservationResult.rows[0];

      // Récupérer les chambres attribuées
      const chambresQuery = `
        SELECT
          ch.numero,
          ch.type_chambre,
          cr.prix_nuit
        FROM "ChambresReservees" cr
        JOIN "Chambres" ch ON cr.chambre_id = ch.chambre_id
        WHERE cr.reservation_id = $1
      `;

      const chambresResult = await db.query(chambresQuery, [reservationId]);

      // Récupérer les informations de paiement
      const paiementQuery = `
        SELECT
          montant,
          mode_paiement,
          reference_paiement,
          date_paiement
        FROM "PaiementsReservations"
        WHERE reservation_id = $1
        ORDER BY date_paiement DESC
        LIMIT 1
      `;

      const paiementResult = await db.query(paiementQuery, [reservationId]);
      const dernierPaiement = paiementResult.rows[0];

      // Créer le PDF format ticket de caisse (plus étroit)
      const doc = new PDFDocument({
        size: [226, 600], // Format ticket de caisse (80mm de large)
        margin: 10
      });
      const chunks = [];

      doc.on('data', chunk => chunks.push(chunk));

      // Fonction pour dessiner un ticket
      const drawTicket = (startY, copyNumber) => {
        let y = startY;

        // En-tête
        doc.fontSize(14).font('Helvetica-Bold')
           .text('TICKET DE RÉSERVATION', 10, y, { align: 'center', width: 206 });

        y += 25;
        doc.fontSize(10).font('Helvetica')
           .text(`Exemplaire ${copyNumber}`, 10, y, { align: 'center', width: 206 });

        y += 20;
        doc.text('================================', 10, y, { align: 'center', width: 206 });

        y += 20;

        // Informations de base
        doc.fontSize(9).font('Helvetica-Bold')
           .text(`N° Réservation: ${reservation.numero_reservation}`, 10, y);

        y += 15;
        doc.font('Helvetica')
           .text(`Statut: ${reservation.statut.toUpperCase()}`, 10, y);

        y += 15;
        doc.text(`Date: ${new Date().toLocaleDateString('fr-FR')} ${new Date().toLocaleTimeString('fr-FR')}`, 10, y);

        y += 20;
        doc.text('--------------------------------', 10, y, { align: 'center', width: 206 });

        y += 15;

        // Informations client
        doc.font('Helvetica-Bold').text('CLIENT:', 10, y);
        y += 12;
        doc.font('Helvetica')
           .text(`${reservation.client_nom} ${reservation.client_prenom}`, 10, y);

        y += 12;
        doc.text(`Tél: ${reservation.client_telephone}`, 10, y);

        if (reservation.client_email) {
          y += 12;
          doc.text(`Email: ${reservation.client_email}`, 10, y);
        }

        y += 20;
        doc.text('--------------------------------', 10, y, { align: 'center', width: 206 });

        y += 15;

        // Informations séjour
        doc.font('Helvetica-Bold').text('SÉJOUR:', 10, y);
        y += 12;
        doc.font('Helvetica')
           .text(`Arrivée: ${new Date(reservation.date_arrivee).toLocaleDateString('fr-FR')}`, 10, y);

        if (reservation.heure_debut) {
          y += 12;
          doc.text(`Heure début: ${reservation.heure_debut}`, 10, y);
        }

        y += 12;
        doc.text(`Départ: ${new Date(reservation.date_depart).toLocaleDateString('fr-FR')}`, 10, y);

        if (reservation.heure_fin) {
          y += 12;
          doc.text(`Heure fin: ${reservation.heure_fin}`, 10, y);
        }

        // Chambres
        if (chambresResult.rows.length > 0) {
          y += 20;
          doc.text('--------------------------------', 10, y, { align: 'center', width: 206 });
          y += 15;
          doc.font('Helvetica-Bold').text('CHAMBRES:', 10, y);

          chambresResult.rows.forEach(chambre => {
            y += 12;
            doc.font('Helvetica')
               .text(`Ch. ${chambre.numero} - ${chambre.type_chambre}`, 10, y);
            y += 10;
            doc.text(`Prix: ${chambre.prix_nuit} FCFA/h`, 10, y);
          });
        }

        y += 20;
        doc.text('--------------------------------', 10, y, { align: 'center', width: 206 });
        y += 15;

        // Informations paiement
        doc.font('Helvetica-Bold').text('PAIEMENT:', 10, y);
        y += 12;
        doc.font('Helvetica')
           .text(`Montant total: ${reservation.montant_total} FCFA`, 10, y);

        if (dernierPaiement) {
          y += 12;
          doc.text(`Montant payé: ${dernierPaiement.montant} FCFA`, 10, y);
          y += 12;
          doc.text(`Mode: ${dernierPaiement.mode_paiement}`, 10, y);

          if (dernierPaiement.reference_paiement) {
            y += 12;
            doc.text(`Réf: ${dernierPaiement.reference_paiement}`, 10, y);
          }

          const montantRestant = reservation.montant_total - dernierPaiement.montant;
          if (montantRestant > 0) {
            y += 12;
            doc.font('Helvetica-Bold')
               .text(`Restant dû: ${montantRestant} FCFA`, 10, y);
          } else {
            y += 12;
            doc.font('Helvetica-Bold')
               .text('PAYÉ INTÉGRALEMENT', 10, y);
          }
        }

        y += 20;
        doc.text('================================', 10, y, { align: 'center', width: 206 });
        y += 15;

        // QR Code
        if (reservation.qr_code) {
          try {
            const qrCodeBuffer = Buffer.from(reservation.qr_code.replace(/^data:image\/png;base64,/, ''), 'base64');
            doc.image(qrCodeBuffer, 63, y, { width: 100, height: 100 }); // Centré
            y += 110;
          } catch (error) {
            logger.error('Erreur QR code:', error);
            doc.text('QR Code non disponible', 10, y, { align: 'center', width: 206 });
            y += 15;
          }
        }

        y += 10;
        doc.fontSize(8).text('Merci de votre confiance!', 10, y, { align: 'center', width: 206 });

        return y + 30;
      };

      // Dessiner un seul ticket
      let currentY = 10;
      drawTicket(currentY, 1);

      // Finaliser le PDF
      doc.end();

      // Retourner le PDF sous forme de Buffer
      return new Promise((resolve) => {
        doc.on('end', () => {
          const pdfBuffer = Buffer.concat(chunks);
          resolve(this.successResponse({
            pdf: pdfBuffer,
            filename: `ticket-reservation-${reservation.numero_reservation}.pdf`,
            reservation: {
              numero_reservation: reservation.numero_reservation,
              client_nom: reservation.client_nom,
              client_prenom: reservation.client_prenom,
              date_arrivee: reservation.date_arrivee,
              date_depart: reservation.date_depart,
              statut: reservation.statut
            }
          }));
        });
      });

    } catch (error) {
      logger.error('Erreur génération ticket de caisse:', error);
      return this.errorResponse('Erreur lors de la génération du ticket de caisse');
    }
  }
}

module.exports = ReservationService;
