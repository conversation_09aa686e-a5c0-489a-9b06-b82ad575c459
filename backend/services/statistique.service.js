const Service = require('./Service');
const db = require('../db');
const logger = require('../logger');

class StatistiqueService  {
  // Taux d'occupation
  static async getTauxOccupation(params) {
    const { date_debut, date_fin, type_chambre } = params;

    try {
      const query = `
        WITH Periode AS (
          SELECT generate_series(
            $1::date,
            $2::date,
            '1 day'::interval
          )::date as date
        ),
        Occupation AS (
          SELECT 
            p.date,
            c.type_chambre,
            COUNT(DISTINCT c.chambre_id) as total_chambres,
            COUNT(DISTINCT CASE 
              WHEN cr.chambre_id IS NOT NULL 
              AND r.statut NOT IN ('annulee', 'terminee')
              THEN c.chambre_id 
            END) as chambres_occupees
          FROM Periode p
          CROSS JOIN Chambres c
          LEFT JOIN ChambresReservees cr ON c.chambre_id = cr.chambre_id
          LEFT JOIN Reservations r ON cr.reservation_id = r.reservation_id
          AND p.date BETWEEN r.date_arrivee AND r.date_depart
          WHERE c.statut = 'active'
          ${type_chambre ? 'AND c.type_chambre = $3' : ''}
          GROUP BY p.date, c.type_chambre
        )
        SELECT 
          date,
          type_chambre,
          total_chambres,
          chambres_occupees,
          ROUND((chambres_occupees::float / NULLIF(total_chambres, 0) * 100), 2) as taux_occupation
        FROM Occupation
        ORDER BY date, type_chambre
      `;

      const values = type_chambre 
        ? [date_debut, date_fin, type_chambre]
        : [date_debut, date_fin];

      const result = await db.query(query, values);

      // Calculer les moyennes par type de chambre
      const moyennes = result.rows.reduce((acc, row) => {
        if (!acc[row.type_chambre]) {
          acc[row.type_chambre] = {
            type_chambre: row.type_chambre,
            total_jours: 0,
            somme_taux: 0
          };
        }
        acc[row.type_chambre].total_jours++;
        acc[row.type_chambre].somme_taux += row.taux_occupation;
        return acc;
      }, {});

      Object.keys(moyennes).forEach(type => {
        moyennes[type].moyenne = 
          Math.round(moyennes[type].somme_taux / moyennes[type].total_jours * 100) / 100;
      });

      return this.successResponse({
        details: result.rows,
        moyennes: Object.values(moyennes)
      });
    } catch (error) {
      logger.error('Erreur calcul taux occupation:', error);
      return this.rejectResponse('Erreur lors du calcul du taux d\'occupation');
    }
  }

  // Revenus
  static async getRevenus(params) {
    const { date_debut, date_fin, type_revenu } = params;

    try {
      const query = `
        WITH Revenus AS (
          SELECT 
            date_trunc('day', date_paiement) as date,
            CASE 
              WHEN r.reservation_id IS NOT NULL THEN 'HEBERGEMENT'
              WHEN t.transaction_id IS NOT NULL THEN 'RESTAURATION'
              ELSE 'AUTRE'
            END as type_revenu,
            SUM(montant) as montant
          FROM Paiements p
          LEFT JOIN Reservations r ON p.reservation_id = r.reservation_id
          LEFT JOIN TransactionsPOS t ON p.transaction_id = t.transaction_id
          WHERE date_paiement BETWEEN $1 AND $2
          ${type_revenu ? 'AND CASE WHEN r.reservation_id IS NOT NULL THEN \'HEBERGEMENT\' WHEN t.transaction_id IS NOT NULL THEN \'RESTAURATION\' ELSE \'AUTRE\' END = $3' : ''}
          GROUP BY date_trunc('day', date_paiement), 
            CASE 
              WHEN r.reservation_id IS NOT NULL THEN 'HEBERGEMENT'
              WHEN t.transaction_id IS NOT NULL THEN 'RESTAURATION'
              ELSE 'AUTRE'
            END
        )
        SELECT 
          date,
          type_revenu,
          montant,
          SUM(montant) OVER (PARTITION BY type_revenu ORDER BY date) as montant_cumule
        FROM Revenus
        ORDER BY date, type_revenu
      `;

      const values = type_revenu 
        ? [date_debut, date_fin, type_revenu]
        : [date_debut, date_fin];

      const result = await db.query(query, values);

      // Calculer les totaux par type de revenu
      const totaux = result.rows.reduce((acc, row) => {
        if (!acc[row.type_revenu]) {
          acc[row.type_revenu] = {
            type_revenu: row.type_revenu,
            total: 0
          };
        }
        acc[row.type_revenu].total += parseFloat(row.montant);
        return acc;
      }, {});

      return this.successResponse({
        details: result.rows,
        totaux: Object.values(totaux)
      });
    } catch (error) {
      logger.error('Erreur calcul revenus:', error);
      return this.rejectResponse('Erreur lors du calcul des revenus');
    }
  }

  // Chambres les plus populaires
  static async getChambresPopulaires(params) {
    const { date_debut, date_fin, limite = 10 } = params;

    try {
      const query = `
        WITH StatistiquesChambres AS (
          SELECT 
            c.chambre_id,
            c.numero,
            c.type_chambre,
            COUNT(DISTINCT r.reservation_id) as nombre_reservations,
            SUM(r.date_depart - r.date_arrivee) as nombre_nuits,
            SUM(r.montant_total) as revenus_totaux,
            COUNT(DISTINCT r.client_id) as nombre_clients_uniques
          FROM Chambres c
          JOIN ChambresReservees cr ON c.chambre_id = cr.chambre_id
          JOIN Reservations r ON cr.reservation_id = r.reservation_id
          WHERE r.date_arrivee BETWEEN $1 AND $2
          AND r.statut NOT IN ('annulee', 'terminee')
          GROUP BY c.chambre_id, c.numero, c.type_chambre
        )
        SELECT 
          *,
          ROUND(revenus_totaux / NULLIF(nombre_nuits, 0), 2) as revenu_moyen_par_nuit
        FROM StatistiquesChambres
        ORDER BY nombre_reservations DESC, revenus_totaux DESC
        LIMIT $3
      `;

      const result = await db.query(query, [date_debut, date_fin, limite]);

      return this.successResponse({
        chambres: result.rows,
        periode: {
          date_debut,
          date_fin
        }
      });
    } catch (error) {
      logger.error('Erreur calcul chambres populaires:', error);
      return this.rejectResponse('Erreur lors du calcul des chambres les plus populaires');
    }
  }

  // Génération de rapports
  static async genererRapport(params) {
    const { type_rapport, date_debut, date_fin, format = 'JSON' } = params;

    try {
      let rapport;

      switch (type_rapport) {
        case 'OCCUPATION':
          rapport = await this.getTauxOccupation({ date_debut, date_fin });
          break;
        case 'REVENUS':
          rapport = await this.getRevenus({ date_debut, date_fin });
          break;
        case 'CHAMBRES_POPULAIRES':
          rapport = await this.getChambresPopulaires({ date_debut, date_fin });
          break;
        default:
          return this.rejectResponse('Type de rapport non supporté');
      }

      // Formater le rapport selon le format demandé
      if (format === 'CSV') {
        // TODO: Implémenter la conversion en CSV
        return this.rejectResponse('Format CSV non encore implémenté');
      }

      return this.successResponse({
        type_rapport,
        periode: {
          date_debut,
          date_fin
        },
        format,
        donnees: rapport.data
      });
    } catch (error) {
      logger.error('Erreur génération rapport:', error);
      return this.rejectResponse('Erreur lors de la génération du rapport');
    }
  }
}

module.exports = StatistiqueService; 