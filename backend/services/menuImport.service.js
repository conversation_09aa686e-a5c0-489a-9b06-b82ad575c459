const XLSX = require('xlsx');
const BaseService = require('./base.service');
const logger = require('../logger');
const db = require('../db');

/**
 * Service d'import de menus depuis des fichiers Excel
 */
class MenuImportService extends BaseService {

  /**
   * Import menu restaurant depuis Excel
   */
  static async importRestaurantMenu(serviceId, excelBuffer, options = {}) {
    const client = await db.getClient();
    
    try {
      await client.query('BEGIN');
      
      // 1. Parser le fichier Excel
      const workbook = XLSX.read(excelBuffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);

      logger.info(`Parsing Excel file: ${data.length} rows found`);

      // 2. Valider les données
      const validationResult = this.validateRestaurantMenuData(data);
      if (!validationResult.isValid) {
        throw new Error(`Validation failed: ${validationResult.errors.join(', ')}`);
      }

      // 3. Récupérer les informations du service
      const serviceResult = await client.query(
        'SELECT * FROM "ServicesComplexe" WHERE service_id = $1',
        [serviceId]
      );

      if (serviceResult.rows.length === 0) {
        throw new Error('Service non trouvé');
      }

      const service = serviceResult.rows[0];
      const { chaine_id, complexe_id } = service;

      // 4. Créer les catégories automatiquement
      const categories = await this.createCategoriesFromData(data, chaine_id, serviceId, client);

      // 5. Créer les produits
      const importResults = [];
      let successCount = 0;
      let errorCount = 0;

      for (const row of data) {
        try {
          const categoryId = categories[row.categorie];
          
          const productResult = await client.query(
            `INSERT INTO "Produits" (
              categorie_id, chaine_id, service_id, nom, type_produit,
              prix_vente_defaut, description, temps_preparation,
              allergenes, image_url, actif, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW())
            RETURNING *`,
            [
              categoryId,
              chaine_id,
              serviceId,
              row.nom_plat,
              'Plat',
              parseFloat(row.prix_vente) || 0,
              row.description || '',
              parseInt(row.temps_preparation) || 0,
              row.allergenes || '',
              row.image_url || '',
              row.actif !== false
            ]
          );

          importResults.push({
            row: row,
            success: true,
            product: productResult.rows[0],
            message: 'Produit créé avec succès'
          });
          successCount++;

        } catch (error) {
          logger.error(`Error importing row:`, error);
          importResults.push({
            row: row,
            success: false,
            error: error.message,
            message: `Erreur: ${error.message}`
          });
          errorCount++;
        }
      }

      await client.query('COMMIT');

      const report = {
        totalRows: data.length,
        successCount,
        errorCount,
        categories: Object.keys(categories).length,
        results: importResults
      };

      logger.info(`Restaurant menu import completed: ${successCount} success, ${errorCount} errors`);

      return this.successResponse(report, 'Import du menu restaurant terminé');

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error in importRestaurantMenu:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Import carte bar depuis Excel
   */
  static async importBarMenu(serviceId, excelBuffer, options = {}) {
    const client = await db.getClient();
    
    try {
      await client.query('BEGIN');
      
      // 1. Parser le fichier Excel
      const workbook = XLSX.read(excelBuffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);

      logger.info(`Parsing Excel file: ${data.length} rows found`);

      // 2. Valider les données
      const validationResult = this.validateBarMenuData(data);
      if (!validationResult.isValid) {
        throw new Error(`Validation failed: ${validationResult.errors.join(', ')}`);
      }

      // 3. Récupérer les informations du service
      const serviceResult = await client.query(
        'SELECT * FROM "ServicesComplexe" WHERE service_id = $1',
        [serviceId]
      );

      if (serviceResult.rows.length === 0) {
        throw new Error('Service non trouvé');
      }

      const service = serviceResult.rows[0];
      const { chaine_id, complexe_id } = service;

      // 4. Créer les catégories automatiquement
      const categories = await this.createCategoriesFromData(data, chaine_id, serviceId, client);

      // 5. Créer les produits boissons
      const importResults = [];
      let successCount = 0;
      let errorCount = 0;

      for (const row of data) {
        try {
          const categoryId = categories[row.categorie];
          
          const productResult = await client.query(
            `INSERT INTO "Produits" (
              categorie_id, chaine_id, service_id, nom, type_produit,
              prix_vente_defaut, description, degre_alcool, volume_ml,
              allergenes, image_url, actif, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW())
            RETURNING *`,
            [
              categoryId,
              chaine_id,
              serviceId,
              row.nom_boisson,
              'Boisson',
              parseFloat(row.prix_vente) || 0,
              row.description || '',
              parseFloat(row.degre_alcool) || 0,
              parseInt(row.volume_ml) || 0,
              row.allergenes || '',
              row.image_url || '',
              row.actif !== false
            ]
          );

          importResults.push({
            row: row,
            success: true,
            product: productResult.rows[0],
            message: 'Boisson créée avec succès'
          });
          successCount++;

        } catch (error) {
          logger.error(`Error importing row:`, error);
          importResults.push({
            row: row,
            success: false,
            error: error.message,
            message: `Erreur: ${error.message}`
          });
          errorCount++;
        }
      }

      await client.query('COMMIT');

      const report = {
        totalRows: data.length,
        successCount,
        errorCount,
        categories: Object.keys(categories).length,
        results: importResults
      };

      logger.info(`Bar menu import completed: ${successCount} success, ${errorCount} errors`);

      return this.successResponse(report, 'Import de la carte bar terminé');

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error in importBarMenu:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Validation des données menu restaurant
   */
  static validateRestaurantMenuData(data) {
    const errors = [];
    const requiredFields = ['nom_plat', 'categorie', 'prix_vente'];

    if (!Array.isArray(data) || data.length === 0) {
      errors.push('Aucune donnée trouvée dans le fichier');
      return { isValid: false, errors };
    }

    data.forEach((row, index) => {
      const rowNumber = index + 2; // +2 car Excel commence à 1 et on a les headers

      // Vérifier les champs obligatoires
      requiredFields.forEach(field => {
        if (!row[field] || row[field].toString().trim() === '') {
          errors.push(`Ligne ${rowNumber}: ${field} est obligatoire`);
        }
      });

      // Valider le prix
      if (row.prix_vente && isNaN(parseFloat(row.prix_vente))) {
        errors.push(`Ligne ${rowNumber}: prix_vente doit être un nombre`);
      }

      // Valider le temps de préparation
      if (row.temps_preparation && isNaN(parseInt(row.temps_preparation))) {
        errors.push(`Ligne ${rowNumber}: temps_preparation doit être un nombre entier`);
      }

      // Valider les catégories autorisées
      const validCategories = ['Entrée', 'Plat Principal', 'Dessert', 'Boisson'];
      if (row.categorie && !validCategories.includes(row.categorie)) {
        errors.push(`Ligne ${rowNumber}: catégorie doit être une des valeurs: ${validCategories.join(', ')}`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validation des données carte bar
   */
  static validateBarMenuData(data) {
    const errors = [];
    const requiredFields = ['nom_boisson', 'categorie', 'prix_vente'];

    if (!Array.isArray(data) || data.length === 0) {
      errors.push('Aucune donnée trouvée dans le fichier');
      return { isValid: false, errors };
    }

    data.forEach((row, index) => {
      const rowNumber = index + 2;

      // Vérifier les champs obligatoires
      requiredFields.forEach(field => {
        if (!row[field] || row[field].toString().trim() === '') {
          errors.push(`Ligne ${rowNumber}: ${field} est obligatoire`);
        }
      });

      // Valider le prix
      if (row.prix_vente && isNaN(parseFloat(row.prix_vente))) {
        errors.push(`Ligne ${rowNumber}: prix_vente doit être un nombre`);
      }

      // Valider le degré d'alcool
      if (row.degre_alcool && isNaN(parseFloat(row.degre_alcool))) {
        errors.push(`Ligne ${rowNumber}: degre_alcool doit être un nombre`);
      }

      // Valider le volume
      if (row.volume_ml && isNaN(parseInt(row.volume_ml))) {
        errors.push(`Ligne ${rowNumber}: volume_ml doit être un nombre entier`);
      }

      // Valider les catégories autorisées
      const validCategories = ['Cocktail', 'Bière', 'Vin', 'Soft', 'Spiritueux', 'Alcool'];
      if (row.categorie && !validCategories.includes(row.categorie)) {
        errors.push(`Ligne ${rowNumber}: catégorie doit être une des valeurs: ${validCategories.join(', ')}`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Créer les catégories automatiquement à partir des données
   */
  static async createCategoriesFromData(data, chaineId, serviceId, client) {
    const categories = {};
    const uniqueCategories = [...new Set(data.map(row => row.categorie).filter(Boolean))];

    for (const categoryName of uniqueCategories) {
      try {
        // Vérifier si la catégorie existe déjà
        const existingCategory = await client.query(
          'SELECT * FROM "CategoriesProduits" WHERE nom = $1 AND service_id = $2',
          [categoryName, serviceId]
        );

        if (existingCategory.rows.length > 0) {
          categories[categoryName] = existingCategory.rows[0].categorie_id;
        } else {
          // Créer la nouvelle catégorie
          const newCategory = await client.query(
            `INSERT INTO "CategoriesProduits" (
              chaine_id, service_id, nom, description, ordre_affichage, actif, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, NOW())
            RETURNING *`,
            [
              chaineId,
              serviceId,
              categoryName,
              `Catégorie ${categoryName}`,
              Object.keys(categories).length + 1,
              true
            ]
          );

          categories[categoryName] = newCategory.rows[0].categorie_id;
          logger.info(`Created category: ${categoryName}`);
        }
      } catch (error) {
        logger.error(`Error creating category ${categoryName}:`, error);
        throw error;
      }
    }

    return categories;
  }
}

module.exports = MenuImportService;
