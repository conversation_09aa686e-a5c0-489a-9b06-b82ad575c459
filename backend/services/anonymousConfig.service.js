const Service = require('./Service');
const db = require('../db');
const logger = require('../logger');

/**
 * Service pour gérer la configuration des réservations anonymes
 * Permet de configurer les paramètres par complexe
 */
class AnonymousConfigService extends Service {

  /**
   * Récupérer la configuration d'un complexe
   * @param {number} complexeId - ID du complexe
   * @returns {Object} Configuration du complexe
   */
  static async getConfigurationComplexe(complexeId) {
    try {
      const query = `
        SELECT * FROM "ConfigurationReservationsAnonymes"
        WHERE complexe_id = $1
      `;

      const result = await db.query(query, [complexeId]);

      if (result.rows.length === 0) {
        // Créer une configuration par défaut si elle n'existe pas
        return await this.createDefaultConfiguration(complexeId);
      }

      return this.successResponse(result.rows[0]);

    } catch (error) {
      logger.error('Erreur récupération configuration complexe:', error);
      return this.rejectResponse('Erreur lors de la récupération de la configuration');
    }
  }

  /**
   * Mettre à jour la configuration d'un complexe
   * @param {number} complexeId - ID du complexe
   * @param {Object} params - Nouveaux paramètres
   * @returns {Object} Configuration mise à jour
   */
  static async updateConfigurationComplexe(complexeId, params) {
    try {
      // Valider les paramètres
      const validation = this.validateConfigurationParams(params);
      if (!validation.valid) {
        return this.rejectResponse(`Paramètres invalides: ${validation.errors.join(', ')}`, 400);
      }

      const {
        actif,
        duree_validite_code_heures,
        max_tentatives_acces,
        delai_blocage_minutes,
        autoriser_modification,
        autoriser_annulation,
        delai_annulation_heures,
        pseudonyme_obligatoire,
        longueur_code_acces,
        prefixe_code
      } = params;

      const updateQuery = `
        UPDATE "ConfigurationReservationsAnonymes"
        SET 
          actif = COALESCE($1, actif),
          duree_validite_code_heures = COALESCE($2, duree_validite_code_heures),
          max_tentatives_acces = COALESCE($3, max_tentatives_acces),
          delai_blocage_minutes = COALESCE($4, delai_blocage_minutes),
          autoriser_modification = COALESCE($5, autoriser_modification),
          autoriser_annulation = COALESCE($6, autoriser_annulation),
          delai_annulation_heures = COALESCE($7, delai_annulation_heures),
          pseudonyme_obligatoire = COALESCE($8, pseudonyme_obligatoire),
          longueur_code_acces = COALESCE($9, longueur_code_acces),
          prefixe_code = COALESCE($10, prefixe_code),
          updated_at = CURRENT_TIMESTAMP
        WHERE complexe_id = $11
        RETURNING *
      `;

      const result = await db.query(updateQuery, [
        actif,
        duree_validite_code_heures,
        max_tentatives_acces,
        delai_blocage_minutes,
        autoriser_modification,
        autoriser_annulation,
        delai_annulation_heures,
        pseudonyme_obligatoire,
        longueur_code_acces,
        prefixe_code,
        complexeId
      ]);

      if (result.rows.length === 0) {
        return this.rejectResponse('Configuration non trouvée', 404);
      }

      logger.info('Configuration réservations anonymes mise à jour', {
        complexe_id: complexeId,
        modifications: Object.keys(params)
      });

      return this.successResponse(result.rows[0]);

    } catch (error) {
      logger.error('Erreur mise à jour configuration:', error);
      return this.rejectResponse('Erreur lors de la mise à jour de la configuration');
    }
  }

  /**
   * Créer une configuration par défaut pour un complexe
   * @param {number} complexeId - ID du complexe
   * @returns {Object} Configuration créée
   */
  static async createDefaultConfiguration(complexeId) {
    try {
      const createQuery = `
        INSERT INTO "ConfigurationReservationsAnonymes" (
          complexe_id,
          actif,
          duree_validite_code_heures,
          max_tentatives_acces,
          delai_blocage_minutes,
          autoriser_modification,
          autoriser_annulation,
          delai_annulation_heures,
          pseudonyme_obligatoire,
          longueur_code_acces,
          prefixe_code
        ) VALUES ($1, true, 72, 5, 30, true, true, 24, false, 12, 'ANON')
        RETURNING *
      `;

      const result = await db.query(createQuery, [complexeId]);

      logger.info('Configuration par défaut créée pour le complexe', { complexe_id: complexeId });

      return this.successResponse(result.rows[0]);

    } catch (error) {
      logger.error('Erreur création configuration par défaut:', error);
      return this.rejectResponse('Erreur lors de la création de la configuration par défaut');
    }
  }

  /**
   * Activer/désactiver les réservations anonymes pour un complexe
   * @param {number} complexeId - ID du complexe
   * @param {boolean} actif - État d'activation
   * @returns {Object} Résultat de l'opération
   */
  static async toggleReservationsAnonymes(complexeId, actif) {
    try {
      const updateQuery = `
        UPDATE "ConfigurationReservationsAnonymes"
        SET actif = $1, updated_at = CURRENT_TIMESTAMP
        WHERE complexe_id = $2
        RETURNING *
      `;

      const result = await db.query(updateQuery, [actif, complexeId]);

      if (result.rows.length === 0) {
        // Créer la configuration si elle n'existe pas
        const createResult = await this.createDefaultConfiguration(complexeId);
        if (createResult.success) {
          return await this.toggleReservationsAnonymes(complexeId, actif);
        }
        return createResult;
      }

      logger.info(`Réservations anonymes ${actif ? 'activées' : 'désactivées'}`, {
        complexe_id: complexeId
      });

      return this.successResponse(result.rows[0]);

    } catch (error) {
      logger.error('Erreur toggle réservations anonymes:', error);
      return this.rejectResponse('Erreur lors de la modification du statut des réservations anonymes');
    }
  }

  /**
   * Récupérer toutes les configurations (pour les admins de chaîne)
   * @param {number} chaineId - ID de la chaîne (optionnel)
   * @returns {Object} Liste des configurations
   */
  static async getAllConfigurations(chaineId = null) {
    try {
      let query = `
        SELECT 
          config.*,
          complexe.nom as complexe_nom,
          complexe.ville as complexe_ville,
          chaine.nom as chaine_nom
        FROM "ConfigurationReservationsAnonymes" config
        JOIN "ComplexesHoteliers" complexe ON config.complexe_id = complexe.complexe_id
        JOIN "ChainesHotelieres" chaine ON complexe.chaine_id = chaine.chaine_id
      `;

      const params = [];

      if (chaineId) {
        query += ' WHERE chaine.chaine_id = $1';
        params.push(chaineId);
      }

      query += ' ORDER BY chaine.nom, complexe.nom';

      const result = await db.query(query, params);

      return this.successResponse({
        configurations: result.rows,
        total: result.rows.length
      });

    } catch (error) {
      logger.error('Erreur récupération toutes configurations:', error);
      return this.rejectResponse('Erreur lors de la récupération des configurations');
    }
  }

  /**
   * Valider les paramètres de configuration
   * @param {Object} params - Paramètres à valider
   * @returns {Object} Résultat de la validation
   */
  static validateConfigurationParams(params) {
    const errors = [];

    if (params.duree_validite_code_heures !== undefined) {
      if (!Number.isInteger(params.duree_validite_code_heures) || params.duree_validite_code_heures < 1 || params.duree_validite_code_heures > 168) {
        errors.push('La durée de validité doit être entre 1 et 168 heures (7 jours)');
      }
    }

    if (params.max_tentatives_acces !== undefined) {
      if (!Number.isInteger(params.max_tentatives_acces) || params.max_tentatives_acces < 1 || params.max_tentatives_acces > 20) {
        errors.push('Le nombre maximum de tentatives doit être entre 1 et 20');
      }
    }

    if (params.delai_blocage_minutes !== undefined) {
      if (!Number.isInteger(params.delai_blocage_minutes) || params.delai_blocage_minutes < 5 || params.delai_blocage_minutes > 1440) {
        errors.push('Le délai de blocage doit être entre 5 minutes et 24 heures');
      }
    }

    if (params.delai_annulation_heures !== undefined) {
      if (!Number.isInteger(params.delai_annulation_heures) || params.delai_annulation_heures < 0 || params.delai_annulation_heures > 168) {
        errors.push('Le délai d\'annulation doit être entre 0 et 168 heures');
      }
    }

    if (params.longueur_code_acces !== undefined) {
      if (!Number.isInteger(params.longueur_code_acces) || params.longueur_code_acces < 8 || params.longueur_code_acces > 20) {
        errors.push('La longueur du code d\'accès doit être entre 8 et 20 caractères');
      }
    }

    if (params.prefixe_code !== undefined) {
      if (typeof params.prefixe_code !== 'string' || params.prefixe_code.length < 3 || params.prefixe_code.length > 10) {
        errors.push('Le préfixe du code doit contenir entre 3 et 10 caractères');
      } else if (!/^[A-Z]+$/.test(params.prefixe_code)) {
        errors.push('Le préfixe du code ne peut contenir que des lettres majuscules');
      }
    }

    return {
      valid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * Récupérer les statistiques d'utilisation des réservations anonymes
   * @param {number} complexeId - ID du complexe
   * @param {Object} filters - Filtres optionnels (date_debut, date_fin)
   * @returns {Object} Statistiques d'utilisation
   */
  static async getUsageStatistics(complexeId, filters = {}) {
    try {
      const { date_debut, date_fin } = filters;

      let whereClause = 'WHERE r.complexe_id = $1 AND r.est_anonyme = true';
      const params = [complexeId];
      let paramIndex = 2;

      if (date_debut) {
        whereClause += ` AND r.created_at >= $${paramIndex}::date`;
        params.push(date_debut);
        paramIndex++;
      }

      if (date_fin) {
        whereClause += ` AND r.created_at <= $${paramIndex}::date`;
        params.push(date_fin);
        paramIndex++;
      }

      const query = `
        SELECT 
          COUNT(*) as total_reservations,
          COUNT(CASE WHEN r.statut = 'en_attente' THEN 1 END) as en_attente,
          COUNT(CASE WHEN r.statut = 'confirmee' THEN 1 END) as confirmees,
          COUNT(CASE WHEN r.statut = 'annulee' THEN 1 END) as annulees,
          COUNT(CASE WHEN r.statut = 'expiree' THEN 1 END) as expirees,
          COALESCE(SUM(r.montant_total), 0) as chiffre_affaires_total,
          COALESCE(AVG(r.montant_total), 0) as montant_moyen,
          MIN(r.created_at) as premiere_reservation,
          MAX(r.created_at) as derniere_reservation,
          COUNT(DISTINCT r.client_id) as clients_uniques
        FROM "Reservations" r
        ${whereClause}
      `;

      const result = await db.query(query, params);

      // Récupérer aussi les statistiques d'accès
      const accessQuery = `
        SELECT 
          COUNT(*) as total_acces,
          COUNT(CASE WHEN success = true THEN 1 END) as acces_reussis,
          COUNT(CASE WHEN success = false THEN 1 END) as acces_echecs,
          COUNT(DISTINCT code_acces_anonyme) as codes_utilises,
          COUNT(DISTINCT adresse_ip) as ips_uniques
        FROM "LogsAccesAnonymes" logs
        WHERE EXISTS (
          SELECT 1 FROM "Reservations" r 
          WHERE r.code_acces_direct = logs.code_acces_anonyme 
          AND r.complexe_id = $1
        )
        ${date_debut ? `AND logs.timestamp >= $${params.length + 1}::date` : ''}
        ${date_fin ? `AND logs.timestamp <= $${params.length + (date_debut ? 2 : 1)}::date` : ''}
      `;

      const accessParams = [complexeId];
      if (date_debut) accessParams.push(date_debut);
      if (date_fin) accessParams.push(date_fin);

      const accessResult = await db.query(accessQuery, accessParams);

      return this.successResponse({
        reservations: result.rows[0],
        acces: accessResult.rows[0],
        periode: { date_debut, date_fin }
      });

    } catch (error) {
      logger.error('Erreur récupération statistiques usage:', error);
      return this.rejectResponse('Erreur lors de la récupération des statistiques d\'usage');
    }
  }

  /**
   * Nettoyer les configurations obsolètes
   * @returns {Object} Résultat du nettoyage
   */
  static async cleanupObsoleteConfigurations() {
    try {
      // Supprimer les configurations pour des complexes qui n'existent plus
      const cleanupQuery = `
        DELETE FROM "ConfigurationReservationsAnonymes"
        WHERE complexe_id NOT IN (
          SELECT complexe_id FROM "ComplexesHoteliers"
        )
        RETURNING complexe_id
      `;

      const result = await db.query(cleanupQuery);

      logger.info('Nettoyage configurations obsolètes terminé', {
        configurations_supprimees: result.rows.length
      });

      return this.successResponse({
        configurations_supprimees: result.rows.length,
        complexes_supprimes: result.rows.map(row => row.complexe_id)
      });

    } catch (error) {
      logger.error('Erreur nettoyage configurations:', error);
      return this.rejectResponse('Erreur lors du nettoyage des configurations');
    }
  }
}

module.exports = AnonymousConfigService;
