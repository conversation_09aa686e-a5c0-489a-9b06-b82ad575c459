const XLSX = require('xlsx');
const db = require('../db');
const logger = require('../logger');
const BaseService = require('./base.service');

class ExcelParserService extends BaseService {

  /**
   * Parse le fichier Excel selon le template
   */
  static async parseExcelFile(filePath, templateConfig, importId) {
    try {
      logger.info(`Starting Excel parsing for import ${importId}`);

      // Lecture du fichier Excel
      const workbook = XLSX.readFile(filePath);
      const sheetNames = workbook.SheetNames;

      if (sheetNames.length === 0) {
        this.rejectResponse('Le fichier Excel ne contient aucune feuille', 400);
      }

      // Utilisation de la première feuille par défaut
      const worksheet = workbook.Sheets[sheetNames[0]];
      const rawData = XLSX.utils.sheet_to_json(worksheet, { 
        header: 1, // Utilise les indices numériques comme en-têtes
        defval: '' // Valeur par défaut pour les cellules vides
      });

      if (rawData.length === 0) {
        this.rejectResponse('La feuille Excel est vide', 400);
      }

      // Extraction des en-têtes (première ligne)
      const headers = rawData[0];
      const dataRows = rawData.slice(1);

      logger.info(`Found ${headers.length} columns and ${dataRows.length} data rows`);

      // Application du mapping des colonnes
      const mappedData = await this.mapColumns(headers, dataRows, templateConfig);

      // Validation de base des données
      const validationResult = await this.validateData(mappedData, templateConfig);

      // Sauvegarde des données parsées
      await this.saveParsingResults(importId, {
        headers,
        mappedData: validationResult.validData,
        errors: validationResult.errors,
        totalRows: dataRows.length,
        validRows: validationResult.validData.length,
        errorCount: validationResult.errors.length
      });

      return this.successResponse({
        totalRows: dataRows.length,
        validRows: validationResult.validData.length,
        errorCount: validationResult.errors.length,
        headers,
        preview: validationResult.validData.slice(0, 10), // Aperçu des 10 premières lignes
        errors: validationResult.errors.slice(0, 20) // Aperçu des 20 premières erreurs
      }, 'Fichier Excel parsé avec succès');

    } catch (error) {
      logger.error(`Error parsing Excel file for import ${importId}:`, error);
      
      // Mise à jour du statut d'erreur
      await this.updateImportError(importId, error.message);
      throw error;
    }
  }

  /**
   * Mapping automatique des colonnes
   */
  static async mapColumns(headers, dataRows, templateConfig) {
    const mappingConfig = templateConfig.mapping_defaut || {};
    const requiredColumns = templateConfig.colonnes_requises?.colonnes || [];
    
    // Mapping automatique par correspondance exacte
    const columnMapping = {};
    const mappedData = [];

    // Création du mapping
    for (let i = 0; i < headers.length; i++) {
      const header = headers[i]?.toString().trim();
      if (header) {
        // Recherche de correspondance exacte
        const mappedField = this.findColumnMapping(header, mappingConfig);
        if (mappedField) {
          columnMapping[i] = mappedField;
        }
      }
    }

    logger.info('Column mapping created:', columnMapping);

    // Application du mapping aux données
    for (let rowIndex = 0; rowIndex < dataRows.length; rowIndex++) {
      const row = dataRows[rowIndex];
      const mappedRow = {
        _rowIndex: rowIndex + 2, // +2 car ligne 1 = headers, et index commence à 0
        _originalData: row
      };

      // Mapping des colonnes
      for (const [colIndex, fieldName] of Object.entries(columnMapping)) {
        const cellValue = row[parseInt(colIndex)];
        mappedRow[fieldName] = this.cleanCellValue(cellValue);
      }

      // Vérification que la ligne n'est pas complètement vide
      const hasData = Object.keys(mappedRow)
        .filter(key => !key.startsWith('_'))
        .some(key => mappedRow[key] !== '' && mappedRow[key] !== null && mappedRow[key] !== undefined);

      if (hasData) {
        mappedData.push(mappedRow);
      }
    }

    return mappedData;
  }

  /**
   * Recherche de correspondance de colonne
   */
  static findColumnMapping(header, mappingConfig) {
    const normalizedHeader = header.toLowerCase().trim();
    
    // Correspondance exacte
    for (const [excelCol, dbField] of Object.entries(mappingConfig)) {
      if (excelCol.toLowerCase() === normalizedHeader) {
        return dbField;
      }
    }

    // Correspondance par similarité (fuzzy matching basique)
    const similarityThreshold = 0.7;
    let bestMatch = null;
    let bestScore = 0;

    for (const [excelCol, dbField] of Object.entries(mappingConfig)) {
      const score = this.calculateSimilarity(normalizedHeader, excelCol.toLowerCase());
      if (score > similarityThreshold && score > bestScore) {
        bestMatch = dbField;
        bestScore = score;
      }
    }

    return bestMatch;
  }

  /**
   * Calcul de similarité simple entre deux chaînes
   */
  static calculateSimilarity(str1, str2) {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /**
   * Distance de Levenshtein
   */
  static levenshteinDistance(str1, str2) {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  /**
   * Nettoyage des valeurs de cellules
   */
  static cleanCellValue(value) {
    if (value === null || value === undefined) return '';
    
    const stringValue = value.toString().trim();
    
    // Conversion des nombres Excel en format correct
    if (!isNaN(stringValue) && stringValue !== '') {
      const numValue = parseFloat(stringValue);
      return isNaN(numValue) ? stringValue : numValue;
    }
    
    return stringValue;
  }

  /**
   * Validation avancée des données
   */
  static async validateData(mappedData, templateConfig) {
    const validationRules = templateConfig.regles_validation?.regles || {};
    const validData = [];
    const errors = [];

    for (const row of mappedData) {
      const rowErrors = [];
      const validatedRow = { ...row };

      // Validation de chaque champ selon les règles
      for (const [fieldName, rules] of Object.entries(validationRules)) {
        const value = row[fieldName];
        const fieldErrors = this.validateField(fieldName, value, rules, row._rowIndex);
        
        if (fieldErrors.length > 0) {
          rowErrors.push(...fieldErrors);
        }
      }

      // Validation métier spécifique
      const businessErrors = await this.validateBusinessRules(row, templateConfig);
      rowErrors.push(...businessErrors);

      if (rowErrors.length === 0) {
        validData.push(validatedRow);
      } else {
        errors.push({
          rowIndex: row._rowIndex,
          errors: rowErrors,
          data: row
        });
      }
    }

    return {
      validData,
      errors
    };
  }

  /**
   * Validation d'un champ individuel
   */
  static validateField(fieldName, value, rules, rowIndex) {
    const errors = [];

    // Champ requis
    if (rules.required && (value === '' || value === null || value === undefined)) {
      errors.push({
        field: fieldName,
        message: `Le champ '${fieldName}' est requis`,
        value,
        rule: 'required'
      });
      return errors; // Arrêt si champ requis manquant
    }

    // Si la valeur est vide et non requise, pas d'autres validations
    if (value === '' || value === null || value === undefined) {
      return errors;
    }

    // Validation du type
    if (rules.type) {
      const typeError = this.validateType(fieldName, value, rules.type);
      if (typeError) errors.push(typeError);
    }

    // Validation de la longueur maximale
    if (rules.max_length && value.toString().length > rules.max_length) {
      errors.push({
        field: fieldName,
        message: `Le champ '${fieldName}' ne peut pas dépasser ${rules.max_length} caractères`,
        value,
        rule: 'max_length'
      });
    }

    // Validation des valeurs numériques
    if (rules.min !== undefined && parseFloat(value) < rules.min) {
      errors.push({
        field: fieldName,
        message: `Le champ '${fieldName}' doit être supérieur ou égal à ${rules.min}`,
        value,
        rule: 'min'
      });
    }

    if (rules.max !== undefined && parseFloat(value) > rules.max) {
      errors.push({
        field: fieldName,
        message: `Le champ '${fieldName}' doit être inférieur ou égal à ${rules.max}`,
        value,
        rule: 'max'
      });
    }

    return errors;
  }

  /**
   * Validation du type de données
   */
  static validateType(fieldName, value, expectedType) {
    switch (expectedType) {
      case 'string':
        if (typeof value !== 'string') {
          return {
            field: fieldName,
            message: `Le champ '${fieldName}' doit être une chaîne de caractères`,
            value,
            rule: 'type'
          };
        }
        break;

      case 'decimal':
      case 'number':
        if (isNaN(parseFloat(value))) {
          return {
            field: fieldName,
            message: `Le champ '${fieldName}' doit être un nombre`,
            value,
            rule: 'type'
          };
        }
        break;

      case 'integer':
        if (!Number.isInteger(parseFloat(value))) {
          return {
            field: fieldName,
            message: `Le champ '${fieldName}' doit être un nombre entier`,
            value,
            rule: 'type'
          };
        }
        break;
    }

    return null;
  }

  /**
   * Validation des règles métier spécifiques
   */
  static async validateBusinessRules(row, templateConfig) {
    const errors = [];
    const typeImport = templateConfig.type_import;

    try {
      switch (typeImport) {
        case 'MENU':
          return await this.validateMenuData(row);
        case 'CARTE':
          return await this.validateBarData(row);
        case 'INVENTAIRE':
          return await this.validateInventaireData(row);
        default:
          return errors;
      }
    } catch (error) {
      logger.error('Error in business validation:', error);
      errors.push({
        field: 'general',
        message: 'Erreur lors de la validation métier',
        value: null,
        rule: 'business'
      });
    }

    return errors;
  }

  /**
   * Validation spécifique pour les données de menu
   */
  static async validateMenuData(row) {
    const errors = [];

    // Vérification de l'unicité du nom de produit
    if (row.nom_produit) {
      const existing = await db.query(
        'SELECT produit_id FROM "Produits" WHERE nom = $1',
        [row.nom_produit]
      );
      
      if (existing.rows.length > 0) {
        errors.push({
          field: 'nom_produit',
          message: `Un produit avec le nom '${row.nom_produit}' existe déjà`,
          value: row.nom_produit,
          rule: 'unique'
        });
      }
    }

    return errors;
  }

  /**
   * Validation spécifique pour les données de bar
   */
  static async validateBarData(row) {
    const errors = [];

    // Validation du degré d'alcool
    if (row.degre_alcool !== undefined && row.degre_alcool !== '') {
      const degre = parseFloat(row.degre_alcool);
      if (degre < 0 || degre > 100) {
        errors.push({
          field: 'degre_alcool',
          message: 'Le degré d\'alcool doit être entre 0 et 100',
          value: row.degre_alcool,
          rule: 'range'
        });
      }
    }

    return errors;
  }

  /**
   * Validation spécifique pour les données d'inventaire
   */
  static async validateInventaireData(row) {
    const errors = [];

    // Validation des unités de mesure
    const unitesValides = ['kg', 'g', 'L', 'mL', 'unité', 'pièce', 'portion'];
    if (row.unite_mesure && !unitesValides.includes(row.unite_mesure)) {
      errors.push({
        field: 'unite_mesure',
        message: `Unité de mesure non valide. Unités acceptées: ${unitesValides.join(', ')}`,
        value: row.unite_mesure,
        rule: 'enum'
      });
    }

    return errors;
  }

  /**
   * Sauvegarde des résultats du parsing
   */
  static async saveParsingResults(importId, results) {
    await db.query(
      `UPDATE "ImportsExcel" 
       SET donnees_parsees = $1, 
           erreurs_detectees = $2,
           nombre_lignes_total = $3,
           nombre_lignes_valides = $4,
           nombre_erreurs = $5,
           statut = CASE WHEN $5 = 0 THEN 'VALIDE' ELSE 'ERREUR' END,
           date_traitement = NOW()
       WHERE import_id = $6`,
      [
        JSON.stringify(results.mappedData),
        JSON.stringify(results.errors),
        results.totalRows,
        results.validRows,
        results.errorCount,
        importId
      ]
    );
  }

  /**
   * Mise à jour en cas d'erreur de parsing
   */
  static async updateImportError(importId, errorMessage) {
    await db.query(
      `UPDATE "ImportsExcel" 
       SET statut = 'ERREUR',
           erreurs_detectees = $1,
           date_traitement = NOW()
       WHERE import_id = $2`,
      [JSON.stringify([{ error: errorMessage }]), importId]
    );
  }

  /**
   * Génération d'un aperçu des données
   */
  static async generatePreview(importId, maxRows = 50) {
    try {
      const importInfo = await db.query(
        'SELECT donnees_parsees, erreurs_detectees, nombre_lignes_total, nombre_lignes_valides FROM "ImportsExcel" WHERE import_id = $1',
        [importId]
      );

      if (importInfo.rows.length === 0) {
        this.rejectResponse('Import non trouvé', 404);
      }

      const data = importInfo.rows[0];
      const parsedData = data.donnees_parsees || [];
      const errors = data.erreurs_detectees || [];

      return this.successResponse({
        preview: parsedData.slice(0, maxRows),
        errors: errors.slice(0, 20),
        statistics: {
          totalRows: data.nombre_lignes_total,
          validRows: data.nombre_lignes_valides,
          errorCount: errors.length,
          successRate: data.nombre_lignes_total > 0 
            ? ((data.nombre_lignes_valides / data.nombre_lignes_total) * 100).toFixed(2)
            : 0
        }
      }, 'Aperçu généré avec succès');

    } catch (error) {
      logger.error(`Error generating preview for import ${importId}:`, error);
      throw error;
    }
  }
}

module.exports = ExcelParserService;
