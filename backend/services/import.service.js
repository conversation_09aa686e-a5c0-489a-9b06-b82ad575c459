const db = require('../db');
const logger = require('../logger');
const BaseService = require('./base.service');

class ImportService extends BaseService {

  /**
   * ==================== ORCHESTRATION DE L'IMPORT ====================
   */

  /**
   * Processus complet d'import
   */
  static async processImport(importId) {
    const client = await db.getClient();
    
    try {
      await client.query('BEGIN');

      // Récupération des informations d'import
      const importInfo = await this.getImportInfo(importId);
      
      if (importInfo.statut !== 'VALIDE') {
        this.rejectResponse('L\'import doit être validé avant d\'être traité', 400);
      }

      logger.info(`Starting import process for import ${importId}`, {
        type: importInfo.type_import,
        service: importInfo.service_id
      });

      // Traitement selon le type d'import
      let result;
      switch (importInfo.type_import) {
        case 'MENU_RESTAURANT':
          result = await this.importMenuRestaurant(client, importId, importInfo);
          break;
        case 'CARTE_BAR':
          result = await this.importCarteBar(client, importId, importInfo);
          break;
        case 'INVENTAIRE_INGREDIENTS':
          result = await this.importInventaireIngredients(client, importId, importInfo);
          break;
        default:
          this.rejectResponse(`Type d'import non supporté: ${importInfo.type_import}`, 400);
      }

      // Mise à jour du statut final
      await client.query(
        `UPDATE "ImportsExcel" 
         SET statut = 'IMPORTE', date_finalisation = NOW()
         WHERE import_id = $1`,
        [importId]
      );

      await client.query('COMMIT');

      logger.info(`Import ${importId} completed successfully`, result);

      return this.successResponse(result, 'Import traité avec succès');

    } catch (error) {
      await client.query('ROLLBACK');
      
      // Mise à jour du statut d'erreur
      await this.handleImportErrors(importId, error);
      
      logger.error(`Import ${importId} failed:`, error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Validation complète des données d'import
   */
  static async validateImportData(importId) {
    try {
      const importInfo = await this.getImportInfo(importId);
      
      if (!importInfo.donnees_parsees) {
        this.rejectResponse('Aucune donnée parsée trouvée', 400);
      }

      const donnees = JSON.parse(importInfo.donnees_parsees);
      const validationResults = {
        totalRows: donnees.length,
        validRows: 0,
        errors: [],
        warnings: []
      };

      // Validation métier spécifique selon le type
      for (let i = 0; i < donnees.length; i++) {
        const row = donnees[i];
        const rowValidation = await this.validateRowData(row, importInfo.type_import, i + 1);
        
        if (rowValidation.isValid) {
          validationResults.validRows++;
        } else {
          validationResults.errors.push(...rowValidation.errors);
        }
        
        validationResults.warnings.push(...rowValidation.warnings);
      }

      // Mise à jour du statut
      const newStatus = validationResults.errors.length === 0 ? 'VALIDE' : 'ERREUR';
      await db.query(
        `UPDATE "ImportsExcel" 
         SET statut = $1, erreurs_detectees = $2, nombre_lignes_valides = $3
         WHERE import_id = $4`,
        [
          newStatus,
          JSON.stringify(validationResults.errors),
          validationResults.validRows,
          importId
        ]
      );

      return this.successResponse(validationResults, 'Validation terminée');

    } catch (error) {
      logger.error(`Error validating import ${importId}:`, error);
      throw error;
    }
  }

  /**
   * ==================== IMPORTS SPÉCIALISÉS ====================
   */

  /**
   * Import spécifique menu restaurant
   */
  static async importMenuRestaurant(client, importId, importInfo) {
    const donnees = JSON.parse(importInfo.donnees_parsees);
    const results = {
      produitsCreated: 0,
      recettesCreated: 0,
      ingredientsCreated: 0,
      errors: []
    };

    for (const row of donnees) {
      try {
        // Création du produit
        const produitResult = await client.query(
          `INSERT INTO "Produits" (
            categorie_id, chaine_id, complexe_id, service_id, nom,
            type_produit, prix_vente_defaut, description, import_id
          ) VALUES (
            (SELECT categorie_id FROM "CategoriesProduits" WHERE nom = $1 AND complexe_id = $2 LIMIT 1),
            (SELECT chaine_id FROM "ComplexesHoteliers" WHERE complexe_id = $2),
            $2, $3, $4, 'Service', $5, $6, $7
          ) RETURNING *`,
          [
            row.categorie || 'Général',
            importInfo.complexe_id,
            importInfo.service_id,
            row.nom_produit,
            row.prix_vente,
            row.description,
            importId
          ]
        );

        const produit = produitResult.rows[0];
        results.produitsCreated++;

        // Création de la recette si des ingrédients sont spécifiés
        if (row.ingredients) {
          const recetteResult = await client.query(
            `INSERT INTO "Recettes" (
              produit_id, service_id, nom_recette, description
            ) VALUES ($1, $2, $3, $4) RETURNING *`,
            [
              produit.produit_id,
              importInfo.service_id,
              row.nom_produit,
              row.description
            ]
          );

          const recette = recetteResult.rows[0];
          results.recettesCreated++;

          // Traitement des ingrédients
          await this.processRecipeIngredients(
            client, recette.recette_id, row, importInfo.complexe_id
          );
        }

      } catch (rowError) {
        results.errors.push({
          row: row._rowIndex,
          error: rowError.message,
          data: row.nom_produit
        });
      }
    }

    return results;
  }

  /**
   * Import spécifique carte bar
   */
  static async importCarteBar(client, importId, importInfo) {
    const donnees = JSON.parse(importInfo.donnees_parsees);
    const results = {
      boissonsCreated: 0,
      cocktailsCreated: 0,
      errors: []
    };

    for (const row of donnees) {
      try {
        // Création de la boisson
        const produitResult = await client.query(
          `INSERT INTO "Produits" (
            categorie_id, chaine_id, complexe_id, service_id, nom,
            type_produit, prix_vente_defaut, description, import_id
          ) VALUES (
            (SELECT categorie_id FROM "CategoriesProduits" WHERE nom = $1 AND complexe_id = $2 LIMIT 1),
            (SELECT chaine_id FROM "ComplexesHoteliers" WHERE complexe_id = $2),
            $2, $3, $4, 'Service', $5, $6, $7
          ) RETURNING *`,
          [
            row.categorie || 'Boissons',
            importInfo.complexe_id,
            importInfo.service_id,
            row.nom_boisson,
            row.prix_vente,
            `${row.description || ''} ${row.degre_alcool ? `(${row.degre_alcool}°)` : ''}`.trim(),
            importId
          ]
        );

        results.boissonsCreated++;

        // Si c'est un cocktail avec ingrédients, créer la recette
        if (row.ingredients && row.categorie?.toLowerCase().includes('cocktail')) {
          const recetteResult = await client.query(
            `INSERT INTO "Recettes" (
              produit_id, service_id, nom_recette, description
            ) VALUES ($1, $2, $3, $4) RETURNING *`,
            [
              produitResult.rows[0].produit_id,
              importInfo.service_id,
              row.nom_boisson,
              `Cocktail - ${row.degre_alcool || 0}°`
            ]
          );

          await this.processRecipeIngredients(
            client, recetteResult.rows[0].recette_id, row, importInfo.complexe_id
          );

          results.cocktailsCreated++;
        }

      } catch (rowError) {
        results.errors.push({
          row: row._rowIndex,
          error: rowError.message,
          data: row.nom_boisson
        });
      }
    }

    return results;
  }

  /**
   * Import des ingrédients et stocks
   */
  static async importInventaireIngredients(client, importId, importInfo) {
    const donnees = JSON.parse(importInfo.donnees_parsees);
    const results = {
      ingredientsCreated: 0,
      stockMovements: 0,
      errors: []
    };

    for (const row of donnees) {
      try {
        // Vérification si l'ingrédient existe déjà
        const existingIngredient = await client.query(
          'SELECT ingredient_id FROM "Ingredients" WHERE nom = $1 AND complexe_id = $2',
          [row.nom_ingredient, importInfo.complexe_id]
        );

        let ingredientId;

        if (existingIngredient.rows.length > 0) {
          // Mise à jour de l'ingrédient existant
          ingredientId = existingIngredient.rows[0].ingredient_id;
          await client.query(
            `UPDATE "Ingredients" 
             SET prix_unitaire_moyen = $1, updated_at = NOW()
             WHERE ingredient_id = $2`,
            [row.prix_unitaire, ingredientId]
          );
        } else {
          // Création d'un nouvel ingrédient
          const ingredientResult = await client.query(
            `INSERT INTO "Ingredients" (
              chaine_id, complexe_id, nom, categorie, unite_mesure,
              prix_unitaire_moyen, conservation
            ) VALUES (
              (SELECT chaine_id FROM "ComplexesHoteliers" WHERE complexe_id = $1),
              $1, $2, $3, $4, $5, $6
            ) RETURNING *`,
            [
              importInfo.complexe_id,
              row.nom_ingredient,
              row.categorie,
              row.unite_mesure,
              row.prix_unitaire,
              row.conservation
            ]
          );

          ingredientId = ingredientResult.rows[0].ingredient_id;
          results.ingredientsCreated++;
        }

        // Création du mouvement de stock si stock_actuel est spécifié
        if (row.stock_actuel && row.stock_actuel > 0) {
          await client.query(
            `INSERT INTO "MouvementsStock" (
              chaine_id, complexe_id, service_id, produit_id, type_mouvement,
              quantite, reference_id, reference_type, employe_id, notes,
              cout_unitaire_import
            ) VALUES (
              (SELECT chaine_id FROM "ComplexesHoteliers" WHERE complexe_id = $1),
              $1, $2, $3, 'Réception', $4, $5, 'IMPORT', $6, $7, $8
            )`,
            [
              importInfo.complexe_id,
              importInfo.service_id,
              ingredientId,
              row.stock_actuel,
              importId,
              importInfo.employe_id,
              `Import initial - ${row.nom_ingredient}`,
              row.prix_unitaire
            ]
          );

          results.stockMovements++;
        }

      } catch (rowError) {
        results.errors.push({
          row: row._rowIndex,
          error: rowError.message,
          data: row.nom_ingredient
        });
      }
    }

    return results;
  }

  /**
   * ==================== MÉTHODES UTILITAIRES ====================
   */

  /**
   * Traitement des ingrédients d'une recette
   */
  static async processRecipeIngredients(client, recetteId, row, complexeId) {
    if (!row.ingredients || !row.quantites || !row.unites) return;

    const ingredients = row.ingredients.split(',');
    const quantites = row.quantites.split(',');
    const unites = row.unites.split(',');

    for (let i = 0; i < ingredients.length; i++) {
      const nomIngredient = ingredients[i]?.trim();
      const quantite = parseFloat(quantites[i]?.trim());
      const unite = unites[i]?.trim();

      if (!nomIngredient || !quantite || !unite) continue;

      // Recherche ou création de l'ingrédient
      let ingredientResult = await client.query(
        'SELECT ingredient_id, prix_unitaire_moyen FROM "Ingredients" WHERE nom = $1 AND complexe_id = $2',
        [nomIngredient, complexeId]
      );

      let ingredientId, prixUnitaire;

      if (ingredientResult.rows.length === 0) {
        // Création de l'ingrédient
        const newIngredient = await client.query(
          `INSERT INTO "Ingredients" (
            chaine_id, complexe_id, nom, categorie, unite_mesure, prix_unitaire_moyen
          ) VALUES (
            (SELECT chaine_id FROM "ComplexesHoteliers" WHERE complexe_id = $1),
            $1, $2, 'Général', $3, 0
          ) RETURNING ingredient_id, prix_unitaire_moyen`,
          [complexeId, nomIngredient, unite]
        );

        ingredientId = newIngredient.rows[0].ingredient_id;
        prixUnitaire = 0;
      } else {
        ingredientId = ingredientResult.rows[0].ingredient_id;
        prixUnitaire = ingredientResult.rows[0].prix_unitaire_moyen;
      }

      // Ajout à la recette
      await client.query(
        `INSERT INTO "RecettesIngredients" (
          recette_id, ingredient_id, quantite_necessaire, unite_mesure, cout_unitaire
        ) VALUES ($1, $2, $3, $4, $5)`,
        [recetteId, ingredientId, quantite, unite, prixUnitaire]
      );
    }
  }

  /**
   * Validation d'une ligne de données
   */
  static async validateRowData(row, typeImport, rowNumber) {
    const validation = {
      isValid: true,
      errors: [],
      warnings: []
    };

    try {
      switch (typeImport) {
        case 'MENU_RESTAURANT':
          if (!row.nom_produit) {
            validation.errors.push({
              row: rowNumber,
              field: 'nom_produit',
              message: 'Le nom du produit est requis'
            });
            validation.isValid = false;
          }
          if (!row.prix_vente || row.prix_vente <= 0) {
            validation.errors.push({
              row: rowNumber,
              field: 'prix_vente',
              message: 'Le prix de vente doit être supérieur à 0'
            });
            validation.isValid = false;
          }
          break;

        case 'CARTE_BAR':
          if (!row.nom_boisson) {
            validation.errors.push({
              row: rowNumber,
              field: 'nom_boisson',
              message: 'Le nom de la boisson est requis'
            });
            validation.isValid = false;
          }
          break;

        case 'INVENTAIRE_INGREDIENTS':
          if (!row.nom_ingredient) {
            validation.errors.push({
              row: rowNumber,
              field: 'nom_ingredient',
              message: 'Le nom de l\'ingrédient est requis'
            });
            validation.isValid = false;
          }
          break;
      }
    } catch (error) {
      validation.errors.push({
        row: rowNumber,
        field: 'general',
        message: `Erreur de validation: ${error.message}`
      });
      validation.isValid = false;
    }

    return validation;
  }

  /**
   * Récupération des informations d'import
   */
  static async getImportInfo(importId) {
    const result = await db.query(
      'SELECT * FROM "ImportsExcel" WHERE import_id = $1',
      [importId]
    );

    if (result.rows.length === 0) {
      this.rejectResponse('Import non trouvé', 404);
    }

    return result.rows[0];
  }

  /**
   * Gestion des erreurs d'import
   */
  static async handleImportErrors(importId, error) {
    try {
      await db.query(
        `UPDATE "ImportsExcel" 
         SET statut = 'ERREUR', 
             erreurs_detectees = $1,
             date_traitement = NOW()
         WHERE import_id = $2`,
        [JSON.stringify([{ error: error.message, timestamp: new Date() }]), importId]
      );
    } catch (updateError) {
      logger.error('Error updating import error status:', updateError);
    }
  }

  /**
   * Annulation complète d'un import
   */
  static async rollbackImport(importId) {
    const client = await db.getClient();

    try {
      await client.query('BEGIN');

      // Suppression des données créées par cet import
      await client.query('DELETE FROM "RecettesIngredients" WHERE recette_id IN (SELECT recette_id FROM "Recettes" WHERE produit_id IN (SELECT produit_id FROM "Produits" WHERE import_id = $1))', [importId]);
      await client.query('DELETE FROM "Recettes" WHERE produit_id IN (SELECT produit_id FROM "Produits" WHERE import_id = $1)', [importId]);
      await client.query('DELETE FROM "Produits" WHERE import_id = $1', [importId]);
      await client.query('DELETE FROM "MouvementsStock" WHERE import_id = $1', [importId]);

      // Mise à jour du statut
      await client.query(
        'UPDATE "ImportsExcel" SET statut = \'ANNULE\', notes = $1 WHERE import_id = $2',
        [`Import annulé le ${new Date().toISOString()}`, importId]
      );

      await client.query('COMMIT');

      return this.successResponse({ importId }, 'Import annulé avec succès');

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error(`Error rolling back import ${importId}:`, error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Génération du rapport d'import
   */
  static async generateImportReport(importId) {
    try {
      const importInfo = await this.getImportInfo(importId);
      
      const report = {
        import_info: importInfo,
        summary: {
          total_rows: importInfo.nombre_lignes_total,
          valid_rows: importInfo.nombre_lignes_valides,
          error_count: importInfo.nombre_erreurs,
          success_rate: importInfo.nombre_lignes_total > 0 
            ? ((importInfo.nombre_lignes_valides / importInfo.nombre_lignes_total) * 100).toFixed(2)
            : 0
        },
        errors: JSON.parse(importInfo.erreurs_detectees || '[]'),
        created_data: await this.getCreatedDataSummary(importId)
      };

      return this.successResponse(report, 'Rapport généré avec succès');

    } catch (error) {
      logger.error(`Error generating import report ${importId}:`, error);
      throw error;
    }
  }

  /**
   * Résumé des données créées
   */
  static async getCreatedDataSummary(importId) {
    const produits = await db.query(
      'SELECT COUNT(*) as count FROM "Produits" WHERE import_id = $1',
      [importId]
    );

    const mouvements = await db.query(
      'SELECT COUNT(*) as count FROM "MouvementsStock" WHERE import_id = $1',
      [importId]
    );

    return {
      produits_created: parseInt(produits.rows[0].count),
      stock_movements: parseInt(mouvements.rows[0].count)
    };
  }

  /**
   * Historique des imports
   */
  static async getImportHistory(complexeId, filters = {}) {
    try {
      const { typeImport, statut, page = 1, limit = 20 } = filters;
      
      let query = `
        SELECT ie.*, e.nom as employe_nom, e.prenom as employe_prenom,
               sc.nom as service_nom
        FROM "ImportsExcel" ie
        LEFT JOIN "Employes" e ON ie.employe_id = e.employe_id
        LEFT JOIN "ServicesComplexe" sc ON ie.service_id = sc.service_id
        WHERE ie.complexe_id = $1
      `;
      
      const params = [complexeId];
      let paramIndex = 2;

      if (typeImport) {
        query += ` AND ie.type_import = $${paramIndex}`;
        params.push(typeImport);
        paramIndex++;
      }

      if (statut) {
        query += ` AND ie.statut = $${paramIndex}`;
        params.push(statut);
        paramIndex++;
      }

      const offset = (page - 1) * limit;
      query += ` ORDER BY ie.date_import DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
      params.push(limit, offset);

      const result = await db.query(query, params);

      return this.successResponse({
        imports: result.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit)
        }
      }, 'Historique récupéré avec succès');

    } catch (error) {
      logger.error(`Error fetching import history for complexe ${complexeId}:`, error);
      throw error;
    }
  }
}

module.exports = ImportService;
