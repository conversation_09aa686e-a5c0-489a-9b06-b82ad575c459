const db = require('../db');
const logger = require('../logger');
const BaseService = require('./base.service');

class ChambreService extends BaseService {
  // Mise à jour du statut d'une chambre
  static async updateStatutChambre(chambreId, params) {
    const { statut, raison, utilisateur_id } = params;

    try {
      const client = await db.query('BEGIN');

      // Vérifier si la chambre existe
      const checkQuery = `
        SELECT statut 
        FROM "Chambres" 
        WHERE chambre_id = $1 
        FOR UPDATE
      `;
      
      const checkResult = await db.query(checkQuery, [chambreId]);
      
      if (checkResult.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Chambre non trouvée', 404);
      }

      const ancienStatut = checkResult.rows[0].statut;

      // Mettre à jour le statut
      const updateQuery = `
        UPDATE "Chambres" 
        SET statut = $1,
        updated_at = CURRENT_TIMESTAMP
        WHERE chambre_id = $2
        RETURNING *
      `;
      
      const updateResult = await db.query(updateQuery, [statut, chambreId]);

      // Enregistrer l'historique
      const historiqueQuery = `
        INSERT INTO HistoriqueChambres (
          chambre_id,
          ancien_statut,
          nouveau_statut,
          raison,
          utilisateur_id,
          updated_at
        ) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
      `;

      await db.query(historiqueQuery, [
        chambreId,
        ancienStatut,
        statut,
        raison,
        utilisateur_id
      ]);

      await db.query('COMMIT');
      logger.info('Statut chambre mis à jour', { 
        chambreId, 
        ancienStatut, 
        nouveauStatut: statut 
      });

      return this.successResponse(updateResult.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur mise à jour statut chambre:', error);
      return this.rejectResponse('Erreur lors de la mise à jour du statut de la chambre');
    }
  }

  // Récupération de la liste des chambres (simplifiée)
  static async getChambres(params) {
    try {
      const { complexe_id, type_chambre, statut, min_capacite, max_prix, etage } = params;
      let query = `
        SELECT c.*
        FROM "Chambres" c
        WHERE 1=1
      `;

      const values = [];
      let paramIndex = 1;

      if (complexe_id) {
        query += ` AND c.complexe_id = $${paramIndex}`;
        values.push(complexe_id);
        paramIndex++;
      }

      if (type_chambre) {
        query += ` AND c.type_chambre = $${paramIndex}`;
        values.push(type_chambre);
        paramIndex++;
      }

      if (statut) {
        query += ` AND c.statut = $${paramIndex}`;
        values.push(statut);
        paramIndex++;
      }

      if (min_capacite) {
        query += ` AND c.capacite >= $${paramIndex}`;
        values.push(min_capacite);
        paramIndex++;
      }

      if (max_prix) {
        query += ` AND c.prix_base <= $${paramIndex}`;
        values.push(max_prix);
        paramIndex++;
      }

      if (etage) {
        query += ` AND c.etage = $${paramIndex}`;
        values.push(etage);
        paramIndex++;
      }

      query += ` ORDER BY c.numero`;

      const result = await db.query(query, values);

      return this.successResponse({
        chambres: result.rows,
        total: result.rows.length
      });
    } catch (error) {
      logger.error('Erreur récupération liste des chambres:', error);
      return this.rejectResponse('Erreur lors de la récupération de la liste des chambres');
    }
  }

  // Récupération d'une chambre par ID
  static async getChambreById(chambreId) {
    try {
      const query = `
        SELECT c.*
        FROM "Chambres" c
        WHERE c.chambre_id = $1
      `;

      const result = await db.query(query, [chambreId]);

      if (result.rows.length === 0) {
        return this.rejectResponse('Chambre non trouvée', 404);
      }

      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur récupération chambre:', error);
      return this.rejectResponse('Erreur lors de la récupération de la chambre');
    }
  }

  // Récupération des statistiques d'une chambre
  static async getStatistiquesChambre(chambreId) {
    try {
      const query = `
        SELECT
          c.chambre_id,
          c.numero,
          c.type_chambre,
          COALESCE(r.nombre_reservations, 0) as nombre_reservations,
          COALESCE(r.derniere_reservation, NULL) as derniere_reservation,
          COALESCE(r.taux_occupation, 0) as taux_occupation,
          COALESCE(r.revenus_total, 0) as revenus_total,
          COALESCE(r.nombre_nuits, 0) as nombre_nuits
        FROM "Chambres" c
        LEFT JOIN (
          SELECT
            chambre_id,
            COUNT(*) as nombre_reservations,
            MAX(r.date_arrivee) as derniere_reservation,
            ROUND(COUNT(*) * 100.0 / NULLIF(EXTRACT(DAY FROM (NOW() - MIN(r.date_arrivee))), 0), 2) as taux_occupation,
            SUM(r.montant_total) as revenus_total,
            SUM(EXTRACT(DAY FROM (r.date_depart - r.date_arrivee))) as nombre_nuits
          FROM "ChambresReservees" cr
          JOIN "Reservations" r ON cr.reservation_id = r.reservation_id
          WHERE r.statut NOT IN ('annulee', 'terminee')
            AND chambre_id = $1
          GROUP BY chambre_id
        ) r ON c.chambre_id = r.chambre_id
        WHERE c.chambre_id = $1
      `;

      const result = await db.query(query, [chambreId]);

      if (result.rows.length === 0) {
        return this.rejectResponse('Chambre non trouvée', 404);
      }

      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur récupération statistiques chambre:', error);
      return this.rejectResponse('Erreur lors de la récupération des statistiques de la chambre');
    }
  }

  // Création d'une nouvelle chambre
  static async createChambre(params) {
    const { 
      numero, 
      type_chambre, 
      capacite, 
      prix_base,
      description,
      complexe_id,
      etage,
      caracteristiques,
      equipements
    } = params;

    try {
      const client = await db.query('BEGIN');

      // Vérifier si le numéro de chambre existe déjà
      const checkQuery = `
        SELECT chambre_id 
        FROM "Chambres" 
        WHERE numero = $1 AND complexe_id = $2
      `;
      
      const checkResult = await db.query(checkQuery, [numero, complexe_id]);
      
      if (checkResult.rows.length > 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Une chambre avec ce numéro existe déjà dans ce complexe', 409);
      }

      // Créer la nouvelle chambre
      const createQuery = `
        INSERT INTO "Chambres" (
          numero,
          type_chambre,
          statut,
          capacite,
          prix_base,
          description,
          complexe_id,
          etage,
          caracteristiques,
          equipements,
          created_at
        ) VALUES ($1, $2, 'active', $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP)
        RETURNING *
      `;

      const createResult = await db.query(createQuery, [
        numero,
        type_chambre,
        capacite,
        prix_base,
        description,
        complexe_id,
        etage,
        caracteristiques,
        equipements
      ]);

      await db.query('COMMIT');
      logger.info('Nouvelle chambre créée', { chambreId: createResult.rows[0].chambre_id });

      return this.successResponse(createResult.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur création chambre:', error);
      return this.rejectResponse('Erreur lors de la création de la chambre');
    }
  }

  // Mise à jour d'une chambre
  static async updateChambre(chambreId, params) {
    try {
      const { 
        numero, 
        type_chambre, 
        capacite, 
        prix_base, 
        description, 
        statut,
        equipements,
        caracteristiques
      } = params;

      // Vérifier si la chambre existe
      const checkQuery = `
        SELECT chambre_id 
        FROM "Chambres" 
        WHERE chambre_id = $1
      `;
      
      const checkResult = await db.query(checkQuery, [chambreId]);
      
      if (checkResult.rows.length === 0) {
        return this.rejectResponse('Chambre non trouvée', 404);
      }

      // Mettre à jour la chambre
      const updateQuery = `
        UPDATE "Chambres"
        SET 
          numero = COALESCE($1, numero),
          type_chambre = COALESCE($2, type_chambre),
          capacite = COALESCE($3, capacite),
          prix_base = COALESCE($4, prix_base),
          description = COALESCE($5, description),
          statut = COALESCE($6, statut),
          equipements = COALESCE($7, equipements),
          caracteristiques = COALESCE($8, caracteristiques),
          updated_at = CURRENT_TIMESTAMP
        WHERE chambre_id = $9
        RETURNING *
      `;

      const result = await db.query(updateQuery, [
        numero,
        type_chambre,
        capacite,
        prix_base,
        description,
        statut,
        equipements,
        caracteristiques,
        chambreId
      ]);

      logger.info('Chambre mise à jour', { chambreId, params });
      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur mise à jour chambre:', error);
      return this.rejectResponse('Erreur lors de la mise à jour de la chambre');
    }
  }

  static async getCalendrierChambre(chambreId, date_debut, date_fin) {
    try {
    // Récupérer réservations
    const reservationsQuery = `
        SELECT 
          r.reservation_id as id, 
          'reservation' as type, 
          r.date_arrivee as start, 
          r.date_depart as end, 
          r.statut, 
          c.nom as client_nom, 
          c.prenom as client_prenom,
          r.montant_total,
          r.nombre_personnes
      FROM "Reservations" r
      LEFT JOIN "Clients" c ON r.client_id = c.client_id
      LEFT JOIN "ChambresReservees" cr ON r.reservation_id = cr.reservation_id
        WHERE cr.chambre_id = $1 
        AND r.date_arrivee >= $2 
        AND r.date_depart <= $3
        ORDER BY r.date_arrivee
    `;
    const reservations = (await db.query(reservationsQuery, [chambreId, date_debut, date_fin])).rows;

      // Récupérer maintenances/nettoyages
    const interventionsQuery = `
        SELECT 
          n.id, 
          n.type, 
          n.date_debut as start, 
          n.date_fin as end, 
          n.statut, 
          n.description,
          n.priorite,
          n.responsable
      FROM "InterventionsChambre" n
        WHERE n.chambre_id = $1 
        AND n.date_debut >= $2 
        AND n.date_fin <= $3
        ORDER BY n.date_debut
    `;
    let interventions = [];
    try {
      interventions = (await db.query(interventionsQuery, [chambreId, date_debut, date_fin])).rows;
      } catch (e) { 
        logger.warn('Table InterventionsChambre non trouvée ou erreur de requête:', e);
      }

      return this.successResponse({
        reservations,
        interventions,
        periode: {
          date_debut,
          date_fin
        }
      });
    } catch (error) {
      logger.error('Erreur récupération calendrier chambre:', error);
      return this.rejectResponse('Erreur lors de la récupération du calendrier de la chambre');
    }
  }

  static async getHistoriqueChambre(chambreId) {
    try {
    // Historique des réservations
    const reservationsQuery = `
        SELECT 
          r.reservation_id as id, 
          'reservation' as type, 
          r.date_arrivee as date, 
          r.statut, 
          c.nom as client_nom, 
          c.prenom as client_prenom,
          r.montant_total,
          r.nombre_personnes,
          r.created_at
      FROM "Reservations" r
      LEFT JOIN "Clients" c ON r.client_id = c.client_id
      LEFT JOIN "ChambresReservees" cr ON r.reservation_id = cr.reservation_id
      WHERE cr.chambre_id = $1
      ORDER BY r.date_arrivee DESC
    `;
    const reservations = (await db.query(reservationsQuery, [chambreId])).rows;

      // Historique des changements de statut
      const statutsQuery = `
        SELECT 
          hc.id,
          'changement_statut' as type,
          hc.updated_at as date,
          hc.ancien_statut,
          hc.nouveau_statut,
          hc.raison,
          u.nom as utilisateur_nom,
          u.prenom as utilisateur_prenom
        FROM "HistoriqueChambres" hc
        LEFT JOIN "Utilisateurs" u ON hc.utilisateur_id = u.utilisateur_id
        WHERE hc.chambre_id = $1
        ORDER BY hc.updated_at DESC
      `;
      const statuts = (await db.query(statutsQuery, [chambreId])).rows;

      // Historique interventions
    const interventionsQuery = `
        SELECT 
          n.id, 
          n.type, 
          n.date_debut as date, 
          n.statut, 
          n.description,
          n.priorite,
          n.responsable,
          n.created_at
      FROM "InterventionsChambre" n
      WHERE n.chambre_id = $1
      ORDER BY n.date_debut DESC
    `;
    let interventions = [];
    try {
      interventions = (await db.query(interventionsQuery, [chambreId])).rows;
      } catch (e) { 
        logger.warn('Table InterventionsChambre non trouvée ou erreur de requête:', e);
      }

      // Combiner et trier tous les événements
      const historique = [...reservations, ...statuts, ...interventions]
        .sort((a, b) => new Date(b.date) - new Date(a.date));

      return this.successResponse({
        historique,
        total: historique.length
      });
    } catch (error) {
      logger.error('Erreur récupération historique chambre:', error);
      return this.rejectResponse('Erreur lors de la récupération de l\'historique de la chambre');
    }
  }
}

module.exports = ChambreService;