const Service = require('./Service');
const db = require('../db');
const logger = require('../logger');

class TracabiliteService  {
  // Enregistrement d'une action dans l'historique
  static async enregistrerAction(params) {
    const {
      type_action,
      entite_id,
      entite_type,
      utilisateur_id,
      details,
      statut_avant = null,
      statut_apres = null
    } = params;

    try {
      const query = `
        INSERT INTO HistoriqueActions (
          type_action,
          entite_id,
          entite_type,
          utilisateur_id,
          details,
          statut_avant,
          statut_apres,
          date_action
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
        RETURNING *
      `;

      const result = await db.query(query, [
        type_action,
        entite_id,
        entite_type,
        utilisateur_id,
        details,
        statut_avant,
        statut_apres
      ]);

      logger.info('Action enregistrée', {
        type_action,
        entite_id,
        entite_type,
        utilisateur_id
      });

      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur enregistrement action:', error);
      return this.rejectResponse('Erreur lors de l\'enregistrement de l\'action');
    }
  }

  // Enregistrement d'une modification de réservation
  static async enregistrerModificationReservation(params) {
    const {
      reservation_id,
      utilisateur_id,
      modifications,
      raison
    } = params;

    try {
      const query = `
        INSERT INTO HistoriqueReservations (
          reservation_id,
          utilisateur_id,
          modifications,
          raison,
          updated_at
        ) VALUES ($1, $2, $3, $4, NOW())
        RETURNING *
      `;

      const result = await db.query(query, [
        reservation_id,
        utilisateur_id,
        JSON.stringify(modifications),
        raison
      ]);

      logger.info('Modification réservation enregistrée', {
        reservation_id,
        utilisateur_id
      });

      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur enregistrement modification réservation:', error);
      return this.rejectResponse('Erreur lors de l\'enregistrement de la modification');
    }
  }

  // Enregistrement d'un paiement
  static async enregistrerPaiement(params) {
    const {
      paiement_id,
      montant,
      type_paiement,
      statut,
      details
    } = params;

    try {
      const query = `
        INSERT INTO HistoriquePaiements (
          paiement_id,
          montant,
          type_paiement,
          statut,
          details,
          date_paiement
        ) VALUES ($1, $2, $3, $4, $5, NOW())
        RETURNING *
      `;

      const result = await db.query(query, [
        paiement_id,
        montant,
        type_paiement,
        statut,
        JSON.stringify(details)
      ]);

      logger.info('Paiement enregistré', {
        paiement_id,
        montant,
        type_paiement
      });

      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur enregistrement paiement:', error);
      return this.rejectResponse('Erreur lors de l\'enregistrement du paiement');
    }
  }

  // Enregistrement d'une utilisation de ticket
  static async enregistrerUtilisationTicket(params) {
    const {
      ticket_id,
      utilisateur_id,
      type_utilisation,
      details
    } = params;

    try {
      const query = `
        INSERT INTO HistoriqueTickets (
          ticket_id,
          utilisateur_id,
          type_utilisation,
          details,
          date_utilisation
        ) VALUES ($1, $2, $3, $4, NOW())
        RETURNING *
      `;

      const result = await db.query(query, [
        ticket_id,
        utilisateur_id,
        type_utilisation,
        JSON.stringify(details)
      ]);

      logger.info('Utilisation ticket enregistrée', {
        ticket_id,
        utilisateur_id,
        type_utilisation
      });

      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur enregistrement utilisation ticket:', error);
      return this.rejectResponse('Erreur lors de l\'enregistrement de l\'utilisation');
    }
  }

  // Récupération de l'historique d'une entité
  static async getHistoriqueEntite(params) {
    const { entite_id, entite_type, date_debut, date_fin } = params;

    try {
      const query = `
        SELECT *
        FROM HistoriqueActions
        WHERE entite_id = $1
        AND entite_type = $2
        AND date_action BETWEEN $3 AND $4
        ORDER BY date_action DESC
      `;

      const result = await db.query(query, [
        entite_id,
        entite_type,
        date_debut,
        date_fin
      ]);

      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération historique:', error);
      return this.rejectResponse('Erreur lors de la récupération de l\'historique');
    }
  }

  // Récupération de l'historique des paiements
  static async getHistoriquePaiements(params) {
    const { reservation_id, date_debut, date_fin } = params;

    try {
      const query = `
        SELECT hp.*, p.reservation_id
        FROM HistoriquePaiements hp
        JOIN Paiements p ON hp.paiement_id = p.paiement_id
        WHERE p.reservation_id = $1
        AND hp.date_paiement BETWEEN $2 AND $3
        ORDER BY hp.date_paiement DESC
      `;

      const result = await db.query(query, [
        reservation_id,
        date_debut,
        date_fin
      ]);

      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération historique paiements:', error);
      return this.rejectResponse('Erreur lors de la récupération de l\'historique des paiements');
    }
  }

  // Récupération de l'historique des tickets
  static async getHistoriqueTickets(params) {
    const { reservation_id, date_debut, date_fin } = params;

    try {
      const query = `
        SELECT ht.*, t.reservation_id
        FROM HistoriqueTickets ht
        JOIN Tickets t ON ht.ticket_id = t.ticket_id
        WHERE t.reservation_id = $1
        AND ht.date_utilisation BETWEEN $2 AND $3
        ORDER BY ht.date_utilisation DESC
      `;

      const result = await db.query(query, [
        reservation_id,
        date_debut,
        date_fin
      ]);

      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération historique tickets:', error);
      return this.rejectResponse('Erreur lors de la récupération de l\'historique des tickets');
    }
  }
}

module.exports = TracabiliteService; 