const db = require('../db');
const logger = require('../logger');

class ComplexService {
  static async getAllComplexes() {
    try {
      const result = await db.query(
        'SELECT * FROM "ComplexesHoteliers" ORDER BY nom'
      );
      return result.rows;
    } catch (error) {
      logger.error('Error fetching complexes:', error);
      throw error;
    }
  }

  static async getComplexById(id) {
    try {
      const result = await db.query(
        'SELECT * FROM "ComplexesHoteliers" WHERE complexe_id = $1',
        [id]
      );

      if (result.rows.length === 0) {
        throw new Error('Complexe non trouvé');
      }

      return result.rows[0];
    } catch (error) {
      logger.error(`Error fetching complex ${id}:`, error);
      throw error;
    }
  }

  static async createComplex(complexData) {
    const { nom, ville, pays, type_etablissement, chaine_id, slug, adresse, code_postal, telephone, email, site_web, logo_url, devise, fuseau_horaire } = complexData;

    try {
      const result = await db.query(
        `INSERT INTO "ComplexesHoteliers" (
          nom, ville, pays, type_etablissement, chaine_id, slug, adresse, 
          code_postal, telephone, email, site_web, logo_url, devise, fuseau_horaire
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14) 
        RETURNING *`,
        [nom, ville, pays, type_etablissement, chaine_id, slug, adresse, 
         code_postal, telephone, email, site_web, logo_url, devise, fuseau_horaire]
      );

      return result.rows[0];
    } catch (error) {
      logger.error('Error creating complex:', error);
      throw error;
    }
  }

  static async updateComplex(id, complexData) {
    const { nom, ville, pays, type_etablissement, slug, adresse, code_postal, telephone, email, site_web, logo_url, devise, fuseau_horaire } = complexData;

    try {
      const result = await db.query(
        `UPDATE "ComplexesHoteliers" 
        SET nom = $1, ville = $2, pays = $3, type_etablissement = $4, 
            slug = $5, adresse = $6, code_postal = $7, telephone = $8, 
            email = $9, site_web = $10, logo_url = $11, devise = $12, 
            fuseau_horaire = $13, date_mise_a_jour = NOW()
        WHERE complexe_id = $14 
        RETURNING *`,
        [nom, ville, pays, type_etablissement, slug, adresse, code_postal, 
         telephone, email, site_web, logo_url, devise, fuseau_horaire, id]
      );

      if (result.rows.length === 0) {
        throw new Error('Complexe non trouvé');
      }

      return result.rows[0];
    } catch (error) {
      logger.error(`Error updating complex ${id}:`, error);
      throw error;
    }
  }

  static async deleteComplex(id) {
    try {
      const result = await db.query(
        'DELETE FROM "ComplexesHoteliers" WHERE complexe_id = $1 RETURNING *',
        [id]
      );

      if (result.rows.length === 0) {
        throw new Error('Complexe non trouvé');
      }

      return true;
    } catch (error) {
      logger.error(`Error deleting complex ${id}:`, error);
      throw error;
    }
  }
}

module.exports = ComplexService; 