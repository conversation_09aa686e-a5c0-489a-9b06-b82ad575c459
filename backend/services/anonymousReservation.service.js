const Service = require('./Service');
const db = require('../db');
const logger = require('../logger');
const { v4: uuidv4 } = require('uuid');
const QRCode = require('qrcode');
const DisponibiliteService = require('./disponibilite.service');

/**
 * Service pour gérer les réservations anonymes
 * Permet aux clients de faire des réservations sans fournir d'informations personnelles complètes
 */
class AnonymousReservationService extends Service {

  /**
   * Générer un code d'accès unique pour une réservation anonyme
   * @param {string} prefixe - Préfixe du code (par défaut 'ANON')
   * @param {number} longueur - Longueur de la partie aléatoire (par défaut 12)
   * @returns {string} Code d'accès unique
   */
  static generateAccessCode(prefixe = 'ANON', longueur = 12) {
    const randomPart = uuidv4().replace(/-/g, '').slice(0, longueur).toUpperCase();
    return `${prefixe}-${randomPart}`;
  }

  /**
   * Créer une demande de réservation anonyme
   * @param {Object} params - Paramètres de la réservation
   * @returns {Object} Résultat de la création
   */
  static async createDemandeReservationAnonyme(params) {
    const {
      date_arrivee,
      date_depart,
      heure_debut,
      heure_fin,
      chambres,
      pseudonyme,
      commentaires,
      prix_total,
      complexe_id
    } = params;

    try {
      await db.query('BEGIN');

      // Récupérer la configuration du complexe
      const configQuery = `
        SELECT * FROM "ConfigurationReservationsAnonymes" 
        WHERE complexe_id = $1 AND actif = true
      `;
      const configResult = await db.query(configQuery, [complexe_id]);
      
      if (configResult.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Les réservations anonymes ne sont pas activées pour ce complexe', 403);
      }

      const config = configResult.rows[0];

      // Générer un code d'accès unique
      const codeAcces = await this.generateUniqueAccessCode(config.prefixe_code, config.longueur_code_acces);

      // Créer un client anonyme
      const createClientQuery = `
        INSERT INTO "Clients" (
          chaine_id,
          complexe_creation_id,
          pseudonyme,
          est_anonyme,
          code_acces_anonyme,
          created_at
        ) VALUES (
          (SELECT chaine_id FROM "ComplexesHoteliers" WHERE complexe_id = $1),
          $1, $2, true, $3, CURRENT_TIMESTAMP
        )
        RETURNING client_id
      `;

      const clientResult = await db.query(createClientQuery, [
        complexe_id,
        pseudonyme || `Client-${Date.now()}`,
        codeAcces
      ]);

      const clientId = clientResult.rows[0].client_id;

      // Générer le numéro de réservation
      const numeroReservation = `RES-${uuidv4().slice(0, 8).toUpperCase()}`;

      // Créer les données pour le QR code
      const qrCodeData = {
        numero_reservation: numeroReservation,
        code_acces_anonyme: codeAcces,
        date_arrivee,
        date_depart,
        heure_debut,
        heure_fin,
        type: 'anonyme',
        complexe_id
      };

      const qrCode = await QRCode.toDataURL(JSON.stringify(qrCodeData));

      // Créer la réservation
      const createReservationQuery = `
        INSERT INTO "Reservations" (
          numero_reservation,
          complexe_id,
          service_id,
          client_id,
          date_arrivee,
          date_depart,
          heure_debut,
          heure_fin,
          commentaires,
          statut,
          montant_total,
          type_reservation,
          qr_code,
          est_anonyme,
          code_acces_direct,
          created_at
        ) VALUES ($1, $2, 1, $3, $4::date, $5::date, $6::time, $7::time, $8, 'en_attente', $9, 'HEURE', $10, true, $11, CURRENT_TIMESTAMP)
        RETURNING *
      `;

      const reservationResult = await db.query(createReservationQuery, [
        numeroReservation,
        complexe_id,
        clientId,
        date_arrivee,
        date_depart,
        heure_debut,
        heure_fin,
        commentaires,
        prix_total,
        qrCode,
        codeAcces
      ]);

      const reservation = reservationResult.rows[0];

      // Créer les disponibilités pour chaque chambre
      for (const chambre of chambres) {
        await DisponibiliteService.createDisponibilite({
          chambre_id: chambre.chambre_id,
          date: date_arrivee,
          heure_debut,
          heure_fin,
          statut: 'en_attente',
          reservation_id: reservation.reservation_id,
          type_reservation: 'DEMANDE_ANONYME'
        });
      }

      // Logger la création
      await this.logAnonymousAccess(codeAcces, 'CREATION_RESERVATION', {
        reservation_id: reservation.reservation_id,
        numero_reservation: numeroReservation,
        montant_total: prix_total,
        nombre_chambres: chambres.length
      });

      await db.query('COMMIT');

      logger.info('Demande de réservation anonyme créée', {
        numero_reservation: numeroReservation,
        code_acces: codeAcces,
        complexe_id
      });

      return this.successResponse({
        ...reservation,
        code_acces_anonyme: codeAcces,
        chambres
      });

    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur création demande réservation anonyme:', error);
      return this.rejectResponse('Erreur lors de la création de la demande de réservation anonyme');
    }
  }

  /**
   * Consulter une réservation anonyme par code d'accès
   * @param {string} codeAcces - Code d'accès de la réservation
   * @param {Object} req - Objet request pour le logging
   * @returns {Object} Détails de la réservation
   */
  static async getReservationByAccessCode(codeAcces, req = null) {
    try {
      // Valider le code d'accès
      const validationResult = await this.validateAccessCode(codeAcces);
      if (!validationResult.success) {
        await this.logAnonymousAccess(codeAcces, 'CONSULTATION_ECHEC', {
          raison: 'Code invalide'
        }, req);
        return validationResult;
      }

      // Récupérer la réservation avec tous les détails
      const query = `
        SELECT 
          r.*,
          c.pseudonyme,
          c.code_acces_anonyme,
          co.nom as complexe_nom,
          COALESCE(
            json_agg(
              json_build_object(
                'chambre_id', ch.chambre_id,
                'numero', ch.numero,
                'type_chambre', ch.type_chambre,
                'prix_nuit', cr.prix_nuit
              )
            ) FILTER (WHERE ch.chambre_id IS NOT NULL), 
            '[]'::json
          ) as chambres,
          COALESCE(
            (SELECT SUM(montant) FROM "PaiementsReservations" WHERE reservation_id = r.reservation_id),
            0
          ) as montant_paye
        FROM "Reservations" r
        JOIN "Clients" c ON r.client_id = c.client_id
        JOIN "ComplexesHoteliers" co ON r.complexe_id = co.complexe_id
        LEFT JOIN "ChambresReservees" cr ON r.reservation_id = cr.reservation_id
        LEFT JOIN "Chambres" ch ON cr.chambre_id = ch.chambre_id
        WHERE r.est_anonyme = true 
        AND (r.code_acces_direct = $1 OR c.code_acces_anonyme = $1)
        GROUP BY r.reservation_id, c.client_id, co.complexe_id
      `;

      const result = await db.query(query, [codeAcces]);

      if (result.rows.length === 0) {
        await this.logAnonymousAccess(codeAcces, 'CONSULTATION_ECHEC', {
          raison: 'Réservation non trouvée'
        }, req);
        return this.rejectResponse('Réservation non trouvée', 404);
      }

      const reservation = result.rows[0];

      // Logger la consultation réussie
      await this.logAnonymousAccess(codeAcces, 'CONSULTATION_REUSSIE', {
        reservation_id: reservation.reservation_id,
        numero_reservation: reservation.numero_reservation
      }, req);

      // Calculer les informations supplémentaires
      const montantRestant = reservation.montant_total - reservation.montant_paye;
      const dureeSejour = this.calculateDuration(reservation.date_arrivee, reservation.date_depart);

      return this.successResponse({
        ...reservation,
        montant_restant: montantRestant,
        duree_sejour: dureeSejour,
        statut_libelle: this.getStatusLabel(reservation.statut)
      });

    } catch (error) {
      logger.error('Erreur consultation réservation anonyme:', error);
      await this.logAnonymousAccess(codeAcces, 'CONSULTATION_ERREUR', {
        erreur: error.message
      }, req);
      return this.rejectResponse('Erreur lors de la consultation de la réservation');
    }
  }

  /**
   * Modifier une réservation anonyme (fonctionnalités limitées)
   * @param {string} codeAcces - Code d'accès de la réservation
   * @param {Object} params - Nouveaux paramètres
   * @param {Object} req - Objet request pour le logging
   * @returns {Object} Résultat de la modification
   */
  static async updateReservationAnonyme(codeAcces, params, req = null) {
    try {
      // Valider le code d'accès
      const validationResult = await this.validateAccessCode(codeAcces);
      if (!validationResult.success) {
        return validationResult;
      }

      // Vérifier que la réservation existe et peut être modifiée
      const checkQuery = `
        SELECT r.*, config.autoriser_modification
        FROM "Reservations" r
        JOIN "ConfigurationReservationsAnonymes" config ON r.complexe_id = config.complexe_id
        WHERE r.est_anonyme = true 
        AND r.code_acces_direct = $1
        AND r.statut = 'en_attente'
      `;

      const checkResult = await db.query(checkQuery, [codeAcces]);

      if (checkResult.rows.length === 0) {
        await this.logAnonymousAccess(codeAcces, 'MODIFICATION_ECHEC', {
          raison: 'Réservation non trouvée ou non modifiable'
        }, req);
        return this.rejectResponse('Réservation non trouvée ou non modifiable', 404);
      }

      const reservation = checkResult.rows[0];

      if (!reservation.autoriser_modification) {
        await this.logAnonymousAccess(codeAcces, 'MODIFICATION_ECHEC', {
          raison: 'Modifications non autorisées'
        }, req);
        return this.rejectResponse('Les modifications ne sont pas autorisées pour ce complexe', 403);
      }

      // Construire la requête de mise à jour (seuls certains champs sont modifiables)
      const allowedFields = ['commentaires'];
      const updateFields = [];
      const updateValues = [];
      let paramIndex = 2;

      for (const field of allowedFields) {
        if (params[field] !== undefined) {
          updateFields.push(`${field} = $${paramIndex}`);
          updateValues.push(params[field]);
          paramIndex++;
        }
      }

      if (updateFields.length === 0) {
        return this.rejectResponse('Aucun champ modifiable fourni', 400);
      }

      updateFields.push('updated_at = CURRENT_TIMESTAMP');

      const updateQuery = `
        UPDATE "Reservations" 
        SET ${updateFields.join(', ')}
        WHERE reservation_id = $1
        RETURNING *
      `;

      await db.query('BEGIN');

      const updateResult = await db.query(updateQuery, [reservation.reservation_id, ...updateValues]);

      // Logger la modification
      await this.logAnonymousAccess(codeAcces, 'MODIFICATION_REUSSIE', {
        reservation_id: reservation.reservation_id,
        champs_modifies: Object.keys(params).filter(key => allowedFields.includes(key))
      }, req);

      await db.query('COMMIT');

      return this.successResponse(updateResult.rows[0]);

    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur modification réservation anonyme:', error);
      await this.logAnonymousAccess(codeAcces, 'MODIFICATION_ERREUR', {
        erreur: error.message
      }, req);
      return this.rejectResponse('Erreur lors de la modification de la réservation');
    }
  }

  /**
   * Annuler une réservation anonyme
   * @param {string} codeAcces - Code d'accès de la réservation
   * @param {Object} req - Objet request pour le logging
   * @returns {Object} Résultat de l'annulation
   */
  static async cancelReservationAnonyme(codeAcces, req = null) {
    try {
      // Valider le code d'accès
      const validationResult = await this.validateAccessCode(codeAcces);
      if (!validationResult.success) {
        return validationResult;
      }

      // Vérifier que la réservation existe et peut être annulée
      const checkQuery = `
        SELECT r.*, config.autoriser_annulation, config.delai_annulation_heures
        FROM "Reservations" r
        JOIN "ConfigurationReservationsAnonymes" config ON r.complexe_id = config.complexe_id
        WHERE r.est_anonyme = true
        AND r.code_acces_direct = $1
        AND r.statut IN ('en_attente', 'confirmee')
      `;

      const checkResult = await db.query(checkQuery, [codeAcces]);

      if (checkResult.rows.length === 0) {
        await this.logAnonymousAccess(codeAcces, 'ANNULATION_ECHEC', {
          raison: 'Réservation non trouvée ou non annulable'
        }, req);
        return this.rejectResponse('Réservation non trouvée ou non annulable', 404);
      }

      const reservation = checkResult.rows[0];

      if (!reservation.autoriser_annulation) {
        await this.logAnonymousAccess(codeAcces, 'ANNULATION_ECHEC', {
          raison: 'Annulations non autorisées'
        }, req);
        return this.rejectResponse('Les annulations ne sont pas autorisées pour ce complexe', 403);
      }

      // Vérifier le délai d'annulation
      const heuresAvantArrivee = (new Date(reservation.date_arrivee) - new Date()) / (1000 * 60 * 60);
      if (heuresAvantArrivee < reservation.delai_annulation_heures) {
        await this.logAnonymousAccess(codeAcces, 'ANNULATION_ECHEC', {
          raison: 'Délai d\'annulation dépassé',
          heures_restantes: heuresAvantArrivee
        }, req);
        return this.rejectResponse(`Annulation impossible. Délai minimum: ${reservation.delai_annulation_heures}h avant l'arrivée`, 400);
      }

      await db.query('BEGIN');

      // Mettre à jour le statut de la réservation
      const updateQuery = `
        UPDATE "Reservations"
        SET statut = 'annulee', updated_at = CURRENT_TIMESTAMP
        WHERE reservation_id = $1
        RETURNING *
      `;

      const updateResult = await db.query(updateQuery, [reservation.reservation_id]);

      // Libérer les disponibilités
      const updateDispoQuery = `
        UPDATE "DisponibilitesChambres"
        SET statut = 'disponible', reservation_id = NULL
        WHERE reservation_id = $1
      `;

      await db.query(updateDispoQuery, [reservation.reservation_id]);

      // Logger l'annulation
      await this.logAnonymousAccess(codeAcces, 'ANNULATION_REUSSIE', {
        reservation_id: reservation.reservation_id,
        numero_reservation: reservation.numero_reservation,
        montant_total: reservation.montant_total
      }, req);

      await db.query('COMMIT');

      logger.info('Réservation anonyme annulée', {
        numero_reservation: reservation.numero_reservation,
        code_acces: codeAcces
      });

      return this.successResponse(updateResult.rows[0]);

    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur annulation réservation anonyme:', error);
      await this.logAnonymousAccess(codeAcces, 'ANNULATION_ERREUR', {
        erreur: error.message
      }, req);
      return this.rejectResponse('Erreur lors de l\'annulation de la réservation');
    }
  }

  /**
   * Logger un accès anonyme
   * @param {string} codeAcces - Code d'accès utilisé
   * @param {string} action - Action effectuée
   * @param {Object} details - Détails supplémentaires
   * @param {Object} req - Objet request pour extraire IP, user-agent, etc.
   */
  static async logAnonymousAccess(codeAcces, action, details = {}, req = null) {
    try {
      const logQuery = `
        INSERT INTO "LogsAccesAnonymes" (
          code_acces_anonyme,
          adresse_ip,
          user_agent,
          action,
          details,
          success,
          session_id,
          referer
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `;

      const success = !action.includes('ECHEC') && !action.includes('ERREUR');
      const adresseIp = req ? (req.ip || req.connection.remoteAddress || 'unknown') : 'system';
      const userAgent = req ? req.get('User-Agent') : 'system';
      const sessionId = req ? req.sessionID : null;
      const referer = req ? req.get('Referer') : null;

      await db.query(logQuery, [
        codeAcces,
        adresseIp,
        userAgent,
        action,
        JSON.stringify(details),
        success,
        sessionId,
        referer
      ]);

    } catch (error) {
      logger.error('Erreur lors du logging d\'accès anonyme:', error);
      // Ne pas faire échouer l'opération principale pour un problème de log
    }
  }

  /**
   * Valider un code d'accès anonyme
   * @param {string} codeAcces - Code d'accès à valider
   * @returns {Object} Résultat de la validation
   */
  static async validateAccessCode(codeAcces) {
    try {
      // Vérifier le format du code
      if (!codeAcces || typeof codeAcces !== 'string') {
        return this.rejectResponse('Code d\'accès invalide', 400);
      }

      // Vérifier que le code existe
      const query = `
        SELECT c.client_id, r.reservation_id, r.statut, config.duree_validite_code_heures
        FROM "Clients" c
        LEFT JOIN "Reservations" r ON c.client_id = r.client_id AND r.est_anonyme = true
        LEFT JOIN "ConfigurationReservationsAnonymes" config ON r.complexe_id = config.complexe_id
        WHERE c.code_acces_anonyme = $1 AND c.est_anonyme = true
      `;

      const result = await db.query(query, [codeAcces]);

      if (result.rows.length === 0) {
        return this.rejectResponse('Code d\'accès non trouvé', 404);
      }

      const data = result.rows[0];

      // Vérifier si le code n'a pas expiré (si une réservation existe)
      if (data.reservation_id && data.duree_validite_code_heures) {
        const query2 = `
          SELECT created_at FROM "Reservations" WHERE reservation_id = $1
        `;
        const reservationResult = await db.query(query2, [data.reservation_id]);

        if (reservationResult.rows.length > 0) {
          const createdAt = new Date(reservationResult.rows[0].created_at);
          const expirationTime = new Date(createdAt.getTime() + (data.duree_validite_code_heures * 60 * 60 * 1000));

          if (new Date() > expirationTime && data.statut === 'en_attente') {
            return this.rejectResponse('Code d\'accès expiré', 410);
          }
        }
      }

      return this.successResponse({ valid: true });

    } catch (error) {
      logger.error('Erreur validation code d\'accès:', error);
      return this.rejectResponse('Erreur lors de la validation du code d\'accès');
    }
  }

  /**
   * Générer un code d'accès unique (vérifie l'unicité en base)
   * @param {string} prefixe - Préfixe du code
   * @param {number} longueur - Longueur de la partie aléatoire
   * @returns {string} Code d'accès unique
   */
  static async generateUniqueAccessCode(prefixe = 'ANON', longueur = 12) {
    let attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
      const code = this.generateAccessCode(prefixe, longueur);

      // Vérifier l'unicité
      const checkQuery = `
        SELECT 1 FROM "Clients" WHERE code_acces_anonyme = $1
        UNION
        SELECT 1 FROM "Reservations" WHERE code_acces_direct = $1
      `;

      const result = await db.query(checkQuery, [code]);

      if (result.rows.length === 0) {
        return code;
      }

      attempts++;
    }

    throw new Error('Impossible de générer un code d\'accès unique après plusieurs tentatives');
  }

  /**
   * Nettoyer les réservations anonymes expirées
   * @returns {Object} Résultat du nettoyage
   */
  static async cleanupExpiredAnonymousReservations() {
    try {
      await db.query('BEGIN');

      // Marquer comme expirées les réservations en attente qui ont dépassé leur délai
      const expireQuery = `
        UPDATE "Reservations" r
        SET statut = 'expiree', updated_at = CURRENT_TIMESTAMP
        FROM "ConfigurationReservationsAnonymes" config
        WHERE r.complexe_id = config.complexe_id
        AND r.est_anonyme = true
        AND r.statut = 'en_attente'
        AND r.created_at < (CURRENT_TIMESTAMP - INTERVAL '1 hour' * config.duree_validite_code_heures)
        RETURNING r.reservation_id, r.numero_reservation
      `;

      const expiredResult = await db.query(expireQuery);

      // Libérer les disponibilités des réservations expirées
      if (expiredResult.rows.length > 0) {
        const reservationIds = expiredResult.rows.map(row => row.reservation_id);

        const updateDispoQuery = `
          UPDATE "DisponibilitesChambres"
          SET statut = 'disponible', reservation_id = NULL
          WHERE reservation_id = ANY($1)
        `;

        await db.query(updateDispoQuery, [reservationIds]);

        // Logger le nettoyage
        for (const reservation of expiredResult.rows) {
          await this.logAnonymousAccess('SYSTEM', 'EXPIRATION_AUTOMATIQUE', {
            reservation_id: reservation.reservation_id,
            numero_reservation: reservation.numero_reservation
          });
        }
      }

      await db.query('COMMIT');

      logger.info('Nettoyage des réservations anonymes expirées terminé', {
        reservations_expirees: expiredResult.rows.length
      });

      return this.successResponse({
        reservations_expirees: expiredResult.rows.length,
        details: expiredResult.rows
      });

    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur lors du nettoyage des réservations expirées:', error);
      return this.rejectResponse('Erreur lors du nettoyage des réservations expirées');
    }
  }

  /**
   * Calculer la durée d'un séjour
   * @param {string} dateArrivee - Date d'arrivée
   * @param {string} dateDepart - Date de départ
   * @returns {number} Durée en jours
   */
  static calculateDuration(dateArrivee, dateDepart) {
    const arrivee = new Date(dateArrivee);
    const depart = new Date(dateDepart);
    const diffTime = Math.abs(depart - arrivee);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * Obtenir le libellé d'un statut
   * @param {string} statut - Statut de la réservation
   * @returns {string} Libellé du statut
   */
  static getStatusLabel(statut) {
    const labels = {
      'en_attente': 'En attente de validation',
      'confirmee': 'Confirmée',
      'annulee': 'Annulée',
      'expiree': 'Expirée',
      'terminee': 'Terminée'
    };
    return labels[statut] || statut;
  }

  /**
   * Obtenir les statistiques des réservations anonymes
   * @param {number} complexeId - ID du complexe (optionnel)
   * @returns {Object} Statistiques
   */
  static async getAnonymousReservationStats(complexeId = null) {
    try {
      let whereClause = 'WHERE r.est_anonyme = true';
      const params = [];

      if (complexeId) {
        whereClause += ' AND r.complexe_id = $1';
        params.push(complexeId);
      }

      const query = `
        SELECT
          COUNT(*) as total_reservations,
          COUNT(CASE WHEN r.statut = 'en_attente' THEN 1 END) as en_attente,
          COUNT(CASE WHEN r.statut = 'confirmee' THEN 1 END) as confirmees,
          COUNT(CASE WHEN r.statut = 'annulee' THEN 1 END) as annulees,
          COUNT(CASE WHEN r.statut = 'expiree' THEN 1 END) as expirees,
          COALESCE(SUM(r.montant_total), 0) as chiffre_affaires_total,
          COALESCE(AVG(r.montant_total), 0) as montant_moyen,
          COUNT(DISTINCT r.complexe_id) as complexes_utilises,
          MIN(r.created_at) as premiere_reservation,
          MAX(r.created_at) as derniere_reservation
        FROM "Reservations" r
        ${whereClause}
      `;

      const result = await db.query(query, params);

      return this.successResponse(result.rows[0]);

    } catch (error) {
      logger.error('Erreur lors de la récupération des statistiques:', error);
      return this.rejectResponse('Erreur lors de la récupération des statistiques');
    }
  }
}

module.exports = AnonymousReservationService;
