const db = require('../db');
const logger = require('../logger');
const BaseService = require('./base.service');

/**
 * Service de gestion des tables pour les restaurants et bars
 */
class TableService extends BaseService {

  /**
   * Récupérer toutes les tables d'un complexe
   */
  static async getAllTablesByComplexe(complexeId) {
    try {
      const query = `
        SELECT
          t.*,
          s.nom as service_nom,
          s.type_service,
          COUNT(rt.reservation_id) as reservations_actives
        FROM "Tables" t
        LEFT JOIN "ServicesComplexe" s ON t.service_id = s.service_id
        LEFT JOIN "ReservationsTables" rt ON t.table_id = rt.table_id
          AND rt.statut = 'Confirmée'
          AND rt.date_fin > NOW()
        WHERE t.complexe_id = $1 AND t.actif = true
        GROUP BY t.table_id, s.nom, s.type_service
        ORDER BY s.nom, t.numero
      `;

      const result = await db.query(query, [complexeId]);
      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération toutes les tables:', error);
      return this.rejectResponse('Erreur lors de la récupération des tables');
    }
  }

  /**
   * Récupérer toutes les tables d'un service
   */
  static async getTablesByService(serviceId) {
    try {
      const query = `
        SELECT 
          t.*,
          s.nom as service_nom,
          s.type_service,
          COUNT(rt.reservation_id) as reservations_actives
        FROM "Tables" t
        LEFT JOIN "ServicesComplexe" s ON t.service_id = s.service_id
        LEFT JOIN "ReservationsTables" rt ON t.table_id = rt.table_id 
          AND rt.statut = 'Confirmée' 
          AND rt.date_fin > NOW()
        WHERE t.service_id = $1 AND t.actif = true
        GROUP BY t.table_id, s.nom, s.type_service
        ORDER BY t.numero
      `;
      
      const result = await db.query(query, [serviceId]);
      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération tables par service:', error);
      return this.rejectResponse('Erreur lors de la récupération des tables');
    }
  }

  /**
   * Créer une nouvelle table
   */
  static async createTable(tableData) {
    const {
      complexe_id,
      service_id,
      numero,
      capacite,
      position_x = 0,
      position_y = 0,
      forme = 'Rectangulaire',
      couleur = '#FFFFFF',
      dimensions = '100x100'
    } = tableData;

    try {
      // Vérifier que le numéro de table n'existe pas déjà pour ce service
      const existingTable = await db.query(
        'SELECT table_id FROM "Tables" WHERE service_id = $1 AND numero = $2 AND actif = true',
        [service_id, numero]
      );

      if (existingTable.rows.length > 0) {
        return this.rejectResponse('Une table avec ce numéro existe déjà pour ce service', 400);
      }

      const query = `
        INSERT INTO "Tables" (
          complexe_id, service_id, numero, capacite, statut,
          position_x, position_y, forme, couleur, dimensions
        ) VALUES ($1, $2, $3, $4, 'Libre', $5, $6, $7, $8, $9)
        RETURNING *
      `;

      const result = await db.query(query, [
        complexe_id, service_id, numero, capacite,
        position_x, position_y, forme, couleur, dimensions
      ]);

      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur création table:', error);
      return this.rejectResponse('Erreur lors de la création de la table');
    }
  }

  /**
   * Mettre à jour le statut d'une table
   */
  static async updateTableStatus(tableId, statut) {
    try {
      // Vérifier que le statut est valide
      const statutsValides = ['Libre', 'Occupée', 'Réservée', 'Hors service'];
      if (!statutsValides.includes(statut)) {
        return this.rejectResponse('Statut de table invalide', 400);
      }

      const query = `
        UPDATE "Tables" 
        SET statut = $1, updated_at = NOW()
        WHERE table_id = $2 AND actif = true
        RETURNING *
      `;

      const result = await db.query(query, [statut, tableId]);

      if (result.rows.length === 0) {
        return this.rejectResponse('Table non trouvée', 404);
      }

      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur mise à jour statut table:', error);
      return this.rejectResponse('Erreur lors de la mise à jour du statut');
    }
  }

  /**
   * Récupérer le layout des tables d'un service
   */
  static async getTableLayout(serviceId) {
    try {
      const query = `
        SELECT 
          table_id,
          numero,
          capacite,
          statut,
          position_x,
          position_y,
          forme,
          couleur,
          dimensions
        FROM "Tables"
        WHERE service_id = $1 AND actif = true
        ORDER BY numero
      `;

      const result = await db.query(query, [serviceId]);
      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération layout tables:', error);
      return this.rejectResponse('Erreur lors de la récupération du layout');
    }
  }

  /**
   * Mettre à jour la position d'une table
   */
  static async updateTablePosition(tableId, position) {
    const { position_x, position_y } = position;

    try {
      const query = `
        UPDATE "Tables" 
        SET position_x = $1, position_y = $2, updated_at = NOW()
        WHERE table_id = $3 AND actif = true
        RETURNING *
      `;

      const result = await db.query(query, [position_x, position_y, tableId]);

      if (result.rows.length === 0) {
        return this.rejectResponse('Table non trouvée', 404);
      }

      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur mise à jour position table:', error);
      return this.rejectResponse('Erreur lors de la mise à jour de la position');
    }
  }

  /**
   * Réserver une table
   */
  static async reserveTable(reservationData) {
    const {
      complexe_id,
      service_id,
      table_id,
      client_id,
      nom_client,
      telephone_client,
      date_debut,
      date_fin,
      nombre_personnes,
      commentaires
    } = reservationData;

    try {
      await db.query('BEGIN');

      // Vérifier que la table existe et est active
      const tableCheck = await db.query(
        'SELECT * FROM "Tables" WHERE table_id = $1 AND service_id = $2 AND actif = true',
        [table_id, service_id]
      );

      if (tableCheck.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Table non trouvée', 404);
      }

      const table = tableCheck.rows[0];

      // Vérifier la capacité
      if (nombre_personnes > table.capacite) {
        await db.query('ROLLBACK');
        return this.rejectResponse(`La table ${table.numero} ne peut accueillir que ${table.capacite} personnes`, 400);
      }

      // Vérifier les conflits de réservation
      const conflictCheck = await db.query(`
        SELECT reservation_id FROM "ReservationsTables"
        WHERE table_id = $1
        AND statut = 'Confirmée'
        AND (
          (date_debut <= $2 AND date_fin > $2) OR
          (date_debut < $3 AND date_fin >= $3) OR
          (date_debut >= $2 AND date_fin <= $3)
        )
      `, [table_id, date_debut, date_fin]);

      if (conflictCheck.rows.length > 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('La table est déjà réservée sur ce créneau', 400);
      }

      // Créer la réservation
      const query = `
        INSERT INTO "ReservationsTables" (
          complexe_id, service_id, table_id, client_id,
          nom_client, telephone_client, date_debut, date_fin,
          nombre_personnes, commentaires, statut
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, 'Confirmée')
        RETURNING *
      `;

      const result = await db.query(query, [
        complexe_id, service_id, table_id, client_id,
        nom_client, telephone_client, date_debut, date_fin,
        nombre_personnes, commentaires
      ]);

      // Mettre à jour le statut de la table si la réservation commence maintenant
      const now = new Date();
      const debutReservation = new Date(date_debut);
      const finReservation = new Date(date_fin);

      if (debutReservation <= now && finReservation > now) {
        await db.query(
          'UPDATE "Tables" SET statut = \'Réservée\', updated_at = NOW() WHERE table_id = $1',
          [table_id]
        );
      }

      await db.query('COMMIT');

      return this.successResponse({
        ...result.rows[0],
        table_numero: table.numero
      });
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur réservation table:', error);
      return this.rejectResponse('Erreur lors de la réservation de la table');
    }
  }

  /**
   * Annuler une réservation de table
   */
  static async cancelTableReservation(reservationId) {
    try {
      await db.query('BEGIN');

      // Récupérer la réservation
      const reservationQuery = await db.query(
        'SELECT * FROM "ReservationsTables" WHERE reservation_id = $1',
        [reservationId]
      );

      if (reservationQuery.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Réservation non trouvée', 404);
      }

      const reservation = reservationQuery.rows[0];

      // Annuler la réservation
      await db.query(
        'UPDATE "ReservationsTables" SET statut = \'Annulée\', updated_at = NOW() WHERE reservation_id = $1',
        [reservationId]
      );

      // Vérifier s'il faut libérer la table
      const now = new Date();
      const debutReservation = new Date(reservation.date_debut);
      const finReservation = new Date(reservation.date_fin);

      if (debutReservation <= now && finReservation > now) {
        // Vérifier s'il y a d'autres réservations actives
        const autresReservations = await db.query(`
          SELECT reservation_id FROM "ReservationsTables"
          WHERE table_id = $1 AND statut = 'Confirmée'
          AND date_debut <= NOW() AND date_fin > NOW()
        `, [reservation.table_id]);

        if (autresReservations.rows.length === 0) {
          await db.query(
            'UPDATE "Tables" SET statut = \'Libre\', updated_at = NOW() WHERE table_id = $1',
            [reservation.table_id]
          );
        }
      }

      await db.query('COMMIT');

      return this.successResponse({
        message: 'Réservation annulée avec succès',
        reservation_id: reservationId
      });
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur annulation réservation:', error);
      return this.rejectResponse('Erreur lors de l\'annulation de la réservation');
    }
  }

  /**
   * Récupérer les réservations d'un service pour une date donnée
   */
  static async getTableReservations(serviceId, date) {
    try {
      const dateDebut = new Date(date);
      dateDebut.setHours(0, 0, 0, 0);

      const dateFin = new Date(date);
      dateFin.setHours(23, 59, 59, 999);

      const query = `
        SELECT
          rt.*,
          t.numero as table_numero,
          t.capacite as table_capacite,
          c.nom as client_nom,
          c.prenom as client_prenom,
          c.email as client_email
        FROM "ReservationsTables" rt
        LEFT JOIN "Tables" t ON rt.table_id = t.table_id
        LEFT JOIN "Clients" c ON rt.client_id = c.client_id
        WHERE rt.service_id = $1
        AND rt.date_debut BETWEEN $2 AND $3
        AND rt.statut != 'Annulée'
        ORDER BY rt.date_debut, t.numero
      `;

      const result = await db.query(query, [serviceId, dateDebut, dateFin]);
      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération réservations tables:', error);
      return this.rejectResponse('Erreur lors de la récupération des réservations');
    }
  }
}

module.exports = TableService;
