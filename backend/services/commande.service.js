const db = require('../db');
const logger = require('../logger');
const BaseService = require('./base.service');
const POSStockIntegration = require('./posStockIntegration.service');

/**
 * Service de gestion des commandes pour les restaurants et bars
 */
class CommandeService extends BaseService {

  /**
   * Créer une nouvelle commande
   */
  static async createCommande(commandeData) {
    const {
      complexe_id,
      service_id,
      table_id,
      client_id,
      employe_id,
      type_commande = 'Sur place',
      notes
    } = commandeData;

    try {
      await db.query('BEGIN');

      // Vérifier que la table existe et est disponible (si spécifiée)
      if (table_id) {
        const tableCheck = await db.query(
          'SELECT * FROM "Tables" WHERE table_id = $1 AND service_id = $2',
          [table_id, service_id]
        );

        if (tableCheck.rows.length === 0) {
          await db.query('ROLLBACK');
          return this.rejectResponse('Table non trouvée', 404);
        }

        const table = tableCheck.rows[0];
        if (table.statut === 'Hors service') {
          await db.query('ROLLBACK');
          return this.rejectResponse('Table hors service', 400);
        }
      }

      // Créer la commande
      const query = `
        INSERT INTO "Commandes" (
          complexe_id, service_id, table_id, client_id, employe_id,
          type_commande, statut, notes, date_commande
        ) VALUES ($1, $2, $3, $4, $5, $6, 'En préparation', $7, NOW())
        RETURNING *
      `;

      const result = await db.query(query, [
        complexe_id, service_id, table_id, client_id, employe_id,
        type_commande, notes
      ]);

      // Mettre à jour le statut de la table si nécessaire
      if (table_id) {
        await db.query(
          'UPDATE "Tables" SET statut = \'Occupée\', updated_at = NOW() WHERE table_id = $1',
          [table_id]
        );
      }

      await db.query('COMMIT');

      return this.successResponse(result.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur création commande:', error);
      return this.rejectResponse('Erreur lors de la création de la commande');
    }
  }

  /**
   * Ajouter un item à une commande
   */
  static async addItemToCommande(commandeId, itemData) {
    const {
      complexe_id,
      service_id,
      produit_id,
      quantite,
      prix_unitaire,
      notes_item
    } = itemData;

    try {
      await db.query('BEGIN');

      // Vérifier que la commande existe et n'est pas finalisée
      const commandeCheck = await db.query(
        'SELECT * FROM "Commandes" WHERE commande_id = $1',
        [commandeId]
      );

      if (commandeCheck.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Commande non trouvée', 404);
      }

      const commande = commandeCheck.rows[0];
      if (['Payée', 'Annulée'].includes(commande.statut)) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Impossible de modifier une commande finalisée', 400);
      }

      // Vérifier la disponibilité du produit en stock
      const stockCheck = await POSStockIntegration.checkIngredientAvailability(produit_id, quantite);
      if (!stockCheck.success || !stockCheck.data.disponible) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Produit non disponible en stock', 400);
      }

      // Calculer le montant de la ligne
      const montant_ligne = prix_unitaire * quantite;

      // Ajouter l'item à la commande
      const query = `
        INSERT INTO "DetailsCommandes" (
          complexe_id, service_id, commande_id, produit_id,
          quantite, prix_unitaire, montant_ligne, notes_item
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `;

      const result = await db.query(query, [
        complexe_id, service_id, commandeId, produit_id,
        quantite, prix_unitaire, montant_ligne, notes_item
      ]);

      // Mettre à jour le montant total de la commande
      await this.updateCommandeTotal(commandeId);

      // Réserver les ingrédients en stock
      await POSStockIntegration.reserveIngredientsForProduct(produit_id, quantite);

      await db.query('COMMIT');

      return this.successResponse(result.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur ajout item commande:', error);
      return this.rejectResponse('Erreur lors de l\'ajout de l\'item');
    }
  }

  /**
   * Mettre à jour la quantité d'un item
   */
  static async updateItemQuantity(commandeId, detailId, nouvelleQuantite) {
    try {
      await db.query('BEGIN');

      // Récupérer l'item actuel
      const itemCheck = await db.query(
        'SELECT * FROM "DetailsCommandes" WHERE detail_id = $1 AND commande_id = $2',
        [detailId, commandeId]
      );

      if (itemCheck.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Item non trouvé', 404);
      }

      const item = itemCheck.rows[0];
      const ancienneQuantite = item.quantite;
      const differenceQuantite = nouvelleQuantite - ancienneQuantite;

      // Vérifier la disponibilité si on augmente la quantité
      if (differenceQuantite > 0) {
        const stockCheck = await POSStockIntegration.checkIngredientAvailability(
          item.produit_id, 
          differenceQuantite
        );
        
        if (!stockCheck.success || !stockCheck.data.disponible) {
          await db.query('ROLLBACK');
          return this.rejectResponse('Stock insuffisant pour cette quantité', 400);
        }
      }

      // Mettre à jour l'item
      const nouveauMontant = item.prix_unitaire * nouvelleQuantite;
      
      const query = `
        UPDATE "DetailsCommandes" 
        SET quantite = $1, montant_ligne = $2, updated_at = NOW()
        WHERE detail_id = $3 AND commande_id = $4
        RETURNING *
      `;

      const result = await db.query(query, [
        nouvelleQuantite, nouveauMontant, detailId, commandeId
      ]);

      // Ajuster la réservation de stock
      if (differenceQuantite !== 0) {
        if (differenceQuantite > 0) {
          await POSStockIntegration.reserveIngredientsForProduct(item.produit_id, differenceQuantite);
        } else {
          await POSStockIntegration.releaseIngredientsForProduct(item.produit_id, Math.abs(differenceQuantite));
        }
      }

      // Mettre à jour le montant total de la commande
      await this.updateCommandeTotal(commandeId);

      await db.query('COMMIT');

      return this.successResponse(result.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur mise à jour quantité item:', error);
      return this.rejectResponse('Erreur lors de la mise à jour de la quantité');
    }
  }

  /**
   * Supprimer un item d'une commande
   */
  static async removeItemFromCommande(commandeId, detailId) {
    try {
      await db.query('BEGIN');

      // Récupérer l'item à supprimer
      const itemCheck = await db.query(
        'SELECT * FROM "DetailsCommandes" WHERE detail_id = $1 AND commande_id = $2',
        [detailId, commandeId]
      );

      if (itemCheck.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Item non trouvé', 404);
      }

      const item = itemCheck.rows[0];

      // Supprimer l'item
      await db.query(
        'DELETE FROM "DetailsCommandes" WHERE detail_id = $1',
        [detailId]
      );

      // Libérer les ingrédients réservés
      await POSStockIntegration.releaseIngredientsForProduct(item.produit_id, item.quantite);

      // Mettre à jour le montant total de la commande
      await this.updateCommandeTotal(commandeId);

      await db.query('COMMIT');

      return this.successResponse({
        message: 'Item supprimé avec succès',
        detail_id: detailId
      });
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur suppression item commande:', error);
      return this.rejectResponse('Erreur lors de la suppression de l\'item');
    }
  }

  /**
   * Mettre à jour le statut d'une commande
   */
  static async updateCommandeStatus(commandeId, nouveauStatut) {
    try {
      const statutsValides = ['En préparation', 'Prête', 'Servie', 'Payée', 'Annulée'];
      if (!statutsValides.includes(nouveauStatut)) {
        return this.rejectResponse('Statut de commande invalide', 400);
      }

      const query = `
        UPDATE "Commandes" 
        SET statut = $1, updated_at = NOW()
        WHERE commande_id = $2
        RETURNING *
      `;

      const result = await db.query(query, [nouveauStatut, commandeId]);

      if (result.rows.length === 0) {
        return this.rejectResponse('Commande non trouvée', 404);
      }

      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur mise à jour statut commande:', error);
      return this.rejectResponse('Erreur lors de la mise à jour du statut');
    }
  }

  /**
   * Récupérer une commande par ID avec ses détails
   */
  static async getCommandeById(commandeId) {
    try {
      const query = `
        SELECT
          c.*,
          t.numero as table_numero,
          e.nom as employe_nom,
          e.prenom as employe_prenom,
          cl.nom as client_nom,
          cl.prenom as client_prenom,
          s.nom as service_nom,
          s.type_service
        FROM "Commandes" c
        LEFT JOIN "Tables" t ON c.table_id = t.table_id
        LEFT JOIN "Employes" e ON c.employe_id = e.employe_id
        LEFT JOIN "Clients" cl ON c.client_id = cl.client_id
        LEFT JOIN "ServicesComplexe" s ON c.service_id = s.service_id
        WHERE c.commande_id = $1
      `;

      const result = await db.query(query, [commandeId]);

      if (result.rows.length === 0) {
        return this.rejectResponse('Commande non trouvée', 404);
      }

      const commande = result.rows[0];

      // Récupérer les détails des items
      const itemsQuery = `
        SELECT
          dc.*,
          p.nom as produit_nom,
          p.description as produit_description
        FROM "DetailsCommandes" dc
        LEFT JOIN "Produits" p ON dc.produit_id = p.produit_id
        WHERE dc.commande_id = $1
        ORDER BY dc.created_at
      `;

      const itemsResult = await db.query(itemsQuery, [commandeId]);

      return this.successResponse({
        ...commande,
        items: itemsResult.rows
      });
    } catch (error) {
      logger.error('Erreur récupération commande par ID:', error);
      return this.rejectResponse('Erreur lors de la récupération de la commande');
    }
  }

  /**
   * Récupérer les commandes d'une table
   */
  static async getCommandesByTable(tableId) {
    try {
      const query = `
        SELECT
          c.*,
          t.numero as table_numero,
          e.nom as employe_nom,
          e.prenom as employe_prenom,
          cl.nom as client_nom,
          cl.prenom as client_prenom
        FROM "Commandes" c
        LEFT JOIN "Tables" t ON c.table_id = t.table_id
        LEFT JOIN "Employes" e ON c.employe_id = e.employe_id
        LEFT JOIN "Clients" cl ON c.client_id = cl.client_id
        WHERE c.table_id = $1
        AND c.statut NOT IN ('Payée', 'Annulée')
        ORDER BY c.date_commande DESC
      `;

      const result = await db.query(query, [tableId]);
      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération commandes par table:', error);
      return this.rejectResponse('Erreur lors de la récupération des commandes');
    }
  }

  /**
   * Récupérer les commandes d'un service avec filtrage par statut
   */
  static async getCommandesByService(serviceId, statut = null) {
    try {
      let query = `
        SELECT
          c.*,
          t.numero as table_numero,
          e.nom as employe_nom,
          e.prenom as employe_prenom,
          cl.nom as client_nom,
          cl.prenom as client_prenom,
          COUNT(dc.detail_id) as nombre_items
        FROM "Commandes" c
        LEFT JOIN "Tables" t ON c.table_id = t.table_id
        LEFT JOIN "Employes" e ON c.employe_id = e.employe_id
        LEFT JOIN "Clients" cl ON c.client_id = cl.client_id
        LEFT JOIN "DetailsCommandes" dc ON c.commande_id = dc.commande_id
        WHERE c.service_id = $1
      `;

      const params = [serviceId];

      if (statut) {
        query += ' AND c.statut = $2';
        params.push(statut);
      }

      query += `
        GROUP BY c.commande_id, t.numero, e.nom, e.prenom, cl.nom, cl.prenom
        ORDER BY c.date_commande DESC
      `;

      const result = await db.query(query, params);
      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération commandes par service:', error);
      return this.rejectResponse('Erreur lors de la récupération des commandes');
    }
  }

  /**
   * Traitement d'une commande avec déduction automatique du stock
   */
  static async processCommandeWithStock(commandeData) {
    const { commande_id, items } = commandeData;

    try {
      await db.query('BEGIN');

      // Créer la commande si elle n'existe pas encore
      let commande;
      if (commande_id) {
        const commandeCheck = await db.query(
          'SELECT * FROM "Commandes" WHERE commande_id = $1',
          [commande_id]
        );
        commande = commandeCheck.rows[0];
      } else {
        const commandeResult = await this.createCommande(commandeData);
        if (!commandeResult.success) {
          await db.query('ROLLBACK');
          return commandeResult;
        }
        commande = commandeResult.data;
      }

      // Traiter chaque item avec vérification et déduction de stock
      const processedItems = [];
      for (const item of items) {
        // Vérifier la disponibilité
        const stockCheck = await POSStockIntegration.checkIngredientAvailability(
          item.produit_id,
          item.quantite
        );

        if (!stockCheck.success || !stockCheck.data.disponible) {
          await db.query('ROLLBACK');
          return this.rejectResponse(`Stock insuffisant pour le produit ${item.nom || item.produit_id}`);
        }

        // Ajouter l'item à la commande
        const itemResult = await this.addItemToCommande(commande.commande_id, item);
        if (!itemResult.success) {
          await db.query('ROLLBACK');
          return itemResult;
        }

        // Déduire le stock
        const stockDeduction = await POSStockIntegration.processProductSale(
          item.produit_id,
          item.quantite,
          commande.service_id
        );

        processedItems.push({
          ...itemResult.data,
          stock_deduction: stockDeduction.success
        });
      }

      await db.query('COMMIT');

      return this.successResponse({
        commande,
        items: processedItems,
        message: 'Commande traitée avec succès'
      });
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur traitement commande avec stock:', error);
      return this.rejectResponse('Erreur lors du traitement de la commande');
    }
  }

  /**
   * Vérifier la disponibilité des ingrédients pour plusieurs items de menu
   */
  static async checkMenuItemsAvailability(serviceId, items) {
    try {
      const availabilityResults = [];
      const unavailableItems = [];
      const stockAlerts = [];

      for (const item of items) {
        const { produit_id, quantite, nom } = item;

        // Vérifier la disponibilité pour chaque item
        const stockCheck = await POSStockIntegration.checkIngredientAvailability(
          produit_id,
          quantite
        );

        const itemResult = {
          produit_id,
          nom: nom || `Produit ${produit_id}`,
          quantite,
          disponible: stockCheck.success && stockCheck.data.disponible,
          details: stockCheck.data || {}
        };

        if (!itemResult.disponible) {
          unavailableItems.push({
            ...itemResult,
            raison: stockCheck.message || 'Stock insuffisant'
          });
        }

        // Collecter les alertes de stock
        if (stockCheck.data && stockCheck.data.alertes) {
          stockAlerts.push(...stockCheck.data.alertes);
        }

        availabilityResults.push(itemResult);
      }

      const allAvailable = unavailableItems.length === 0;

      return this.successResponse({
        service_id: serviceId,
        all_available: allAvailable,
        items_checked: availabilityResults.length,
        available_items: availabilityResults.filter(item => item.disponible),
        unavailable_items: unavailableItems,
        stock_alerts: stockAlerts,
        summary: {
          total_items: items.length,
          available_count: availabilityResults.filter(item => item.disponible).length,
          unavailable_count: unavailableItems.length,
          alerts_count: stockAlerts.length
        }
      });
    } catch (error) {
      logger.error('Erreur vérification disponibilité items menu:', error);
      return this.rejectResponse('Erreur lors de la vérification de disponibilité');
    }
  }

  /**
   * Annuler une commande avec restauration du stock
   */
  static async cancelCommandeWithStockRestore(commandeId) {
    try {
      await db.query('BEGIN');

      // Récupérer la commande et ses items
      const commandeQuery = await db.query(
        'SELECT * FROM "Commandes" WHERE commande_id = $1',
        [commandeId]
      );

      if (commandeQuery.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Commande non trouvée', 404);
      }

      const commande = commandeQuery.rows[0];

      if (commande.statut === 'Annulée') {
        await db.query('ROLLBACK');
        return this.rejectResponse('Commande déjà annulée', 400);
      }

      // Récupérer tous les items de la commande
      const itemsQuery = await db.query(
        'SELECT * FROM "DetailsCommandes" WHERE commande_id = $1',
        [commandeId]
      );

      // Restaurer le stock pour chaque item
      for (const item of itemsQuery.rows) {
        await POSStockIntegration.restoreStockForCancelledSale(
          item.produit_id,
          item.quantite,
          commande.service_id
        );
      }

      // Marquer la commande comme annulée
      await db.query(
        'UPDATE "Commandes" SET statut = \'Annulée\', updated_at = NOW() WHERE commande_id = $1',
        [commandeId]
      );

      // Libérer la table si nécessaire
      if (commande.table_id) {
        await db.query(
          'UPDATE "Tables" SET statut = \'Libre\', updated_at = NOW() WHERE table_id = $1',
          [commande.table_id]
        );
      }

      await db.query('COMMIT');

      return this.successResponse({
        message: 'Commande annulée et stock restauré',
        commande_id: commandeId
      });
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur annulation commande avec restauration stock:', error);
      return this.rejectResponse('Erreur lors de l\'annulation de la commande');
    }
  }

  /**
   * Méthode utilitaire pour mettre à jour le montant total d'une commande
   */
  static async updateCommandeTotal(commandeId) {
    try {
      const totalQuery = `
        SELECT COALESCE(SUM(montant_ligne), 0) as total
        FROM "DetailsCommandes"
        WHERE commande_id = $1
      `;

      const totalResult = await db.query(totalQuery, [commandeId]);
      const montantTotal = totalResult.rows[0].total;

      await db.query(
        'UPDATE "Commandes" SET montant_total = $1, updated_at = NOW() WHERE commande_id = $2',
        [montantTotal, commandeId]
      );

      return montantTotal;
    } catch (error) {
      logger.error('Erreur mise à jour total commande:', error);
      throw error;
    }
  }
}

module.exports = CommandeService;
