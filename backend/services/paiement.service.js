const Service = require('./Service');
const db = require('../db');
const logger = require('../logger');

class PaiementService {
  // Création d'un paiement
  static async creerPaiement(params) {
    const { transaction_id, montant, mode_paiement, reference_paiement, notes } = params;

    try {
      await db.query('BEGIN');

      // Vérifier si la transaction existe
      const transactionQuery = `
        SELECT * FROM TransactionsPOS
        WHERE transaction_id = $1
      `;
      
      const transactionResult = await db.query(transactionQuery, [transaction_id]);
      
      if (transactionResult.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Transaction non trouvée', 404);
      }

      // Valider le mode de paiement
      const modesPaiementValides = ['mixx', 'flooz', 'especes', 'carte', 'virement', 'cheque'];
      if (!modesPaiementValides.includes(mode_paiement.toLowerCase())) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Mode de paiement invalide. Les modes acceptés sont: mixx, flooz, especes, carte, virement, cheque');
      }

      // Enregistrer le paiement
      const paiementQuery = `
        INSERT INTO Paiements (
          transaction_id,
          montant,
          mode_paiement,
          reference_paiement,
          date_paiement,
          notes
        ) VALUES ($1, $2, $3, $4, NOW(), $5)
        RETURNING *
      `;

      const paiementResult = await db.query(paiementQuery, [
        transaction_id,
        montant,
        mode_paiement.toLowerCase(),
        reference_paiement,
        notes
      ]);

      // Mettre à jour le montant payé dans la transaction
      const updateTransactionQuery = `
        UPDATE TransactionsPOS
        SET montant_paye = montant_paye + $1
        WHERE transaction_id = $2
        RETURNING *
      `;

      await db.query(updateTransactionQuery, [montant, transaction_id]);

      // Enregistrer dans l'historique
      const historiqueQuery = `
        INSERT INTO HistoriquePaiements (
          paiement_id,
          montant,
          type_paiement,
          statut,
          details
        ) VALUES ($1, $2, $3, 'ENREGISTRE', $4)
      `;

      await db.query(historiqueQuery, [
        paiementResult.rows[0].paiement_id,
        montant,
        mode_paiement.toLowerCase(),
        JSON.stringify({ reference_paiement, notes })
      ]);

      await db.query('COMMIT');
      logger.info('Paiement créé', { 
        paiement_id: paiementResult.rows[0].paiement_id,
        transaction_id 
      });

      return this.successResponse(paiementResult.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur création paiement:', error);
      return this.rejectResponse('Erreur lors de la création du paiement');
    }
  }

  // Récupération des détails d'un paiement
  static async getPaiementById(paiementId) {
    try {
      const query = `
        SELECT 
          p.*,
          t.transaction_id,
          t.reference as reference_transaction,
          t.montant_total,
          t.statut as statut_transaction,
          c.nom as client_nom,
          c.prenom as client_prenom
        FROM Paiements p
        JOIN TransactionsPOS t ON p.transaction_id = t.transaction_id
        LEFT JOIN Clients c ON t.client_id = c.client_id
        WHERE p.paiement_id = $1
      `;
      
      const result = await db.query(query, [paiementId]);
      
      if (result.rows.length === 0) {
        return this.rejectResponse('Paiement non trouvé', 404);
      }

      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur récupération détails paiement:', error);
      return this.rejectResponse('Erreur lors de la récupération des détails du paiement');
    }
  }

  // Mise à jour du statut d'un paiement
  static async updatePaiementStatus(paiementId, nouveauStatut) {
    try {
      await db.query('BEGIN');

      // Vérifier si le paiement existe
      const checkQuery = `
        SELECT * FROM Paiements 
        WHERE paiement_id = $1
        FOR UPDATE
      `;
      
      const checkResult = await db.query(checkQuery, [paiementId]);
      
      if (checkResult.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Paiement non trouvé', 404);
      }

      // Valider le nouveau statut
      const statutsValides = ['ENREGISTRE', 'CONFIRME', 'ANNULE', 'REMBOURSE'];
      if (!statutsValides.includes(nouveauStatut)) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Statut invalide');
      }

      // Mettre à jour le statut
      const updateQuery = `
        UPDATE Paiements 
        SET statut = $1
        WHERE paiement_id = $2
        RETURNING *
      `;

      const updateResult = await db.query(updateQuery, [nouveauStatut, paiementId]);

      // Enregistrer dans l'historique
      const historiqueQuery = `
        INSERT INTO HistoriquePaiements (
          paiement_id,
          montant,
          type_paiement,
          statut,
          details
        ) VALUES ($1, $2, $3, $4, $5)
      `;

      await db.query(historiqueQuery, [
        paiementId,
        checkResult.rows[0].montant,
        checkResult.rows[0].mode_paiement,
        nouveauStatut,
        JSON.stringify({ ancien_statut: checkResult.rows[0].statut })
      ]);

      await db.query('COMMIT');
      logger.info('Statut paiement mis à jour', {
        paiement_id: paiementId,
        nouveau_statut: nouveauStatut
      });

      return this.successResponse(updateResult.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur mise à jour statut paiement:', error);
      return this.rejectResponse('Erreur lors de la mise à jour du statut du paiement');
    }
  }

  // Récupération des paiements d'une réservation
  static async getPaiementsReservation(reservationId) {
    try {
      const query = `
        SELECT 
          p.*,
          t.transaction_id,
          t.reference as reference_transaction,
          t.montant_total,
          t.statut as statut_transaction
        FROM Paiements p
        JOIN TransactionsPOS t ON p.transaction_id = t.transaction_id
        WHERE t.reservation_id = $1
        ORDER BY p.date_paiement DESC
      `;
      
      const result = await db.query(query, [reservationId]);

      // Calculer les totaux
      const totaux = result.rows.reduce((acc, paiement) => {
        acc.montant_total += paiement.montant;
        return acc;
      }, { montant_total: 0 });

      return this.successResponse({
        paiements: result.rows,
        totaux
      });
    } catch (error) {
      logger.error('Erreur récupération paiements réservation:', error);
      return this.rejectResponse('Erreur lors de la récupération des paiements de la réservation');
    }
  }

  // Vérification du statut d'un paiement
  static async verifierStatutPaiement(paiementId) {
    try {
      const query = `
        SELECT p.*, t.transaction_id, t.statut as statut_transaction
        FROM Paiements p
        JOIN TransactionsPOS t ON p.transaction_id = t.transaction_id
        WHERE p.paiement_id = $1
      `;
      
      const result = await db.query(query, [paiementId]);
      
      if (result.rows.length === 0) {
        return this.rejectResponse('Paiement non trouvé', 404);
      }

      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur vérification statut paiement:', error);
      return this.rejectResponse('Erreur lors de la vérification du statut du paiement');
    }
  }

  // Récupérer l'historique des paiements d'une transaction
  static async getHistoriquePaiements(transactionId) {
    try {
      const query = `
        SELECT p.*, t.reference as reference_transaction
        FROM Paiements p
        JOIN TransactionsPOS t ON p.transaction_id = t.transaction_id
        WHERE p.transaction_id = $1
        ORDER BY p.date_paiement DESC
      `;
      
      const result = await db.query(query, [transactionId]);
      
      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération historique paiements:', error);
      return this.rejectResponse('Erreur lors de la récupération de l\'historique des paiements');
    }
  }

  // Vérifier si une transaction est entièrement payée
  static async verifierPaiementComplet(transactionId) {
    try {
      const query = `
        SELECT 
          t.transaction_id,
          t.montant_total,
          COALESCE(SUM(p.montant), 0) as montant_paye
        FROM TransactionsPOS t
        LEFT JOIN Paiements p ON t.transaction_id = p.transaction_id
        WHERE t.transaction_id = $1
        GROUP BY t.transaction_id, t.montant_total
      `;
      
      const result = await db.query(query, [transactionId]);
      
      if (result.rows.length === 0) {
        return this.rejectResponse('Transaction non trouvée', 404);
      }

      const { montant_total, montant_paye } = result.rows[0];
      const estPayee = montant_paye >= montant_total;

      return this.successResponse({
        transaction_id: transactionId,
        montant_total,
        montant_paye,
        est_payee: estPayee,
        reste_a_payer: Math.max(0, montant_total - montant_paye)
      });
    } catch (error) {
      logger.error('Erreur vérification paiement complet:', error);
      return this.rejectResponse('Erreur lors de la vérification du paiement complet');
    }
  }
}

module.exports = PaiementService; 