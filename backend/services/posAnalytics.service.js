const db = require('../db');
const logger = require('../logger');
const BaseService = require('./base.service');

/**
 * Service d'analytics pour les points de vente Restaurant/Bar
 * Fournit des analyses détaillées des performances, ventes et utilisation
 */
class POSAnalyticsService extends BaseService {

  /**
   * Dashboard complet des performances d'un service
   */
  static async getServiceDashboard(serviceId, dateDebut, dateFin) {
    try {
      // Récupérer les métriques en parallèle
      const [performance, topProducts, tableUtilization, revenueAnalysis] = await Promise.all([
        this.getServicePerformance(serviceId, dateDebut, dateFin),
        this.getTopProducts(serviceId, dateDebut, dateFin, 10),
        this.getTableUtilization(serviceId, dateDebut, dateFin),
        this.getRevenueAnalysis(serviceId, dateDebut, dateFin)
      ]);

      return this.successResponse({
        periode: { debut: dateDebut, fin: dateFin },
        service_id: serviceId,
        performance: performance.success ? performance.data : null,
        top_products: topProducts.success ? topProducts.data : null,
        table_utilization: tableUtilization.success ? tableUtilization.data : null,
        revenue_analysis: revenueAnalysis.success ? revenueAnalysis.data : null,
        generated_at: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Erreur dashboard service:', error);
      return this.rejectResponse('Erreur lors de la génération du dashboard');
    }
  }

  /**
   * Analyse des performances d'un service sur une période
   */
  static async getServicePerformance(serviceId, dateDebut, dateFin) {
    try {
      const query = `
        SELECT
          DATE(c.date_commande) as date,
          COUNT(c.commande_id) as nb_commandes,
          SUM(c.montant_total) as chiffre_affaires,
          AVG(c.montant_total) as panier_moyen,
          COUNT(DISTINCT c.table_id) as tables_utilisees,
          COUNT(DISTINCT c.employe_id) as employes_actifs,
          AVG(EXTRACT(EPOCH FROM (c.updated_at - c.date_commande)) / 60) as duree_moyenne_commande,
          SUM(CASE WHEN c.statut = 'Payée' THEN 1 ELSE 0 END) as commandes_payees,
          SUM(CASE WHEN c.statut = 'Annulée' THEN 1 ELSE 0 END) as commandes_annulees
        FROM "Commandes" c
        WHERE c.service_id = $1
        AND c.date_commande BETWEEN $2 AND $3
        GROUP BY DATE(c.date_commande)
        ORDER BY date
      `;

      const result = await db.query(query, [serviceId, dateDebut, dateFin]);
      
      // Calculer les totaux et moyennes
      const totals = result.rows.reduce((acc, row) => ({
        total_commandes: acc.total_commandes + parseInt(row.nb_commandes),
        total_ca: acc.total_ca + parseFloat(row.chiffre_affaires || 0),
        total_tables_utilisees: Math.max(acc.total_tables_utilisees, parseInt(row.tables_utilisees || 0)),
        total_commandes_payees: acc.total_commandes_payees + parseInt(row.commandes_payees || 0),
        total_commandes_annulees: acc.total_commandes_annulees + parseInt(row.commandes_annulees || 0)
      }), {
        total_commandes: 0,
        total_ca: 0,
        total_tables_utilisees: 0,
        total_commandes_payees: 0,
        total_commandes_annulees: 0
      });

      const performance = {
        periode_details: result.rows,
        resume: {
          ...totals,
          panier_moyen_global: totals.total_commandes > 0 ? totals.total_ca / totals.total_commandes : 0,
          taux_conversion: totals.total_commandes > 0 ? (totals.total_commandes_payees / totals.total_commandes) * 100 : 0,
          taux_annulation: totals.total_commandes > 0 ? (totals.total_commandes_annulees / totals.total_commandes) * 100 : 0
        }
      };

      return this.successResponse(performance);
    } catch (error) {
      logger.error('Erreur analyse performance service:', error);
      return this.rejectResponse('Erreur lors de l\'analyse des performances');
    }
  }

  /**
   * Top des produits les plus vendus
   */
  static async getTopProducts(serviceId, dateDebut, dateFin, limit = 10) {
    try {
      const query = `
        SELECT
          p.produit_id,
          p.nom as produit_nom,
          p.prix as prix_unitaire,
          cp.nom as categorie_nom,
          SUM(dc.quantite) as quantite_vendue,
          SUM(dc.montant_ligne) as chiffre_affaires,
          COUNT(DISTINCT dc.commande_id) as nb_commandes,
          AVG(dc.prix_unitaire) as prix_moyen_vente,
          SUM(dc.quantite) * 100.0 / (
            SELECT SUM(dc2.quantite) 
            FROM "DetailsCommandes" dc2 
            JOIN "Commandes" c2 ON dc2.commande_id = c2.commande_id 
            WHERE c2.service_id = $1 AND c2.date_commande BETWEEN $2 AND $3
          ) as pourcentage_ventes
        FROM "DetailsCommandes" dc
        JOIN "Commandes" c ON dc.commande_id = c.commande_id
        JOIN "Produits" p ON dc.produit_id = p.produit_id
        LEFT JOIN "CategoriesProduits" cp ON p.categorie_id = cp.categorie_id
        WHERE c.service_id = $1
        AND c.date_commande BETWEEN $2 AND $3
        AND c.statut = 'Payée'
        GROUP BY p.produit_id, p.nom, p.prix, cp.nom
        ORDER BY quantite_vendue DESC
        LIMIT $4
      `;

      const result = await db.query(query, [serviceId, dateDebut, dateFin, limit]);
      
      return this.successResponse({
        top_products: result.rows,
        total_products_analyzed: result.rows.length
      });
    } catch (error) {
      logger.error('Erreur analyse top produits:', error);
      return this.rejectResponse('Erreur lors de l\'analyse des produits');
    }
  }

  /**
   * Analyse de l'utilisation des tables
   */
  static async getTableUtilization(serviceId, dateDebut, dateFin) {
    try {
      const query = `
        SELECT
          t.table_id,
          t.numero as table_numero,
          t.capacite,
          COUNT(c.commande_id) as nb_commandes,
          SUM(c.montant_total) as chiffre_affaires,
          AVG(c.montant_total) as panier_moyen,
          AVG(EXTRACT(EPOCH FROM (c.updated_at - c.date_commande)) / 60) as duree_moyenne_occupation,
          COUNT(c.commande_id) * 100.0 / (
            SELECT COUNT(*) 
            FROM "Commandes" c2 
            WHERE c2.service_id = $1 AND c2.date_commande BETWEEN $2 AND $3
          ) as pourcentage_utilisation
        FROM "Tables" t
        LEFT JOIN "Commandes" c ON t.table_id = c.table_id
          AND c.date_commande BETWEEN $2 AND $3
          AND c.statut = 'Payée'
        WHERE t.service_id = $1 AND t.actif = true
        GROUP BY t.table_id, t.numero, t.capacite
        ORDER BY nb_commandes DESC
      `;

      const result = await db.query(query, [serviceId, dateDebut, dateFin]);
      
      // Calculer les statistiques globales
      const stats = result.rows.reduce((acc, row) => ({
        total_tables: acc.total_tables + 1,
        tables_utilisees: acc.tables_utilisees + (parseInt(row.nb_commandes) > 0 ? 1 : 0),
        total_commandes: acc.total_commandes + parseInt(row.nb_commandes || 0),
        total_ca: acc.total_ca + parseFloat(row.chiffre_affaires || 0)
      }), {
        total_tables: 0,
        tables_utilisees: 0,
        total_commandes: 0,
        total_ca: 0
      });

      return this.successResponse({
        table_details: result.rows,
        statistiques_globales: {
          ...stats,
          taux_utilisation_tables: stats.total_tables > 0 ? (stats.tables_utilisees / stats.total_tables) * 100 : 0,
          commandes_par_table: stats.tables_utilisees > 0 ? stats.total_commandes / stats.tables_utilisees : 0
        }
      });
    } catch (error) {
      logger.error('Erreur analyse utilisation tables:', error);
      return this.rejectResponse('Erreur lors de l\'analyse des tables');
    }
  }

  /**
   * Analyse détaillée des revenus
   */
  static async getRevenueAnalysis(serviceId, dateDebut, dateFin) {
    try {
      const query = `
        SELECT
          DATE(c.date_commande) as date,
          EXTRACT(HOUR FROM c.date_commande) as heure,
          SUM(c.montant_total) as revenus,
          COUNT(c.commande_id) as nb_commandes,
          AVG(c.montant_total) as panier_moyen
        FROM "Commandes" c
        WHERE c.service_id = $1
        AND c.date_commande BETWEEN $2 AND $3
        AND c.statut = 'Payée'
        GROUP BY DATE(c.date_commande), EXTRACT(HOUR FROM c.date_commande)
        ORDER BY date, heure
      `;

      const result = await db.query(query, [serviceId, dateDebut, dateFin]);
      
      // Analyser les tendances par heure
      const revenusParHeure = {};
      result.rows.forEach(row => {
        const heure = parseInt(row.heure);
        if (!revenusParHeure[heure]) {
          revenusParHeure[heure] = { revenus: 0, commandes: 0 };
        }
        revenusParHeure[heure].revenus += parseFloat(row.revenus);
        revenusParHeure[heure].commandes += parseInt(row.nb_commandes);
      });

      // Trouver les heures de pointe
      const heuresPointe = Object.entries(revenusParHeure)
        .sort(([,a], [,b]) => b.revenus - a.revenus)
        .slice(0, 3)
        .map(([heure, data]) => ({
          heure: parseInt(heure),
          revenus: data.revenus,
          commandes: data.commandes
        }));

      return this.successResponse({
        revenus_detailles: result.rows,
        revenus_par_heure: revenusParHeure,
        heures_de_pointe: heuresPointe,
        analyse_tendances: {
          heure_plus_rentable: heuresPointe[0]?.heure || null,
          revenus_max_heure: heuresPointe[0]?.revenus || 0
        }
      });
    } catch (error) {
      logger.error('Erreur analyse revenus:', error);
      return this.rejectResponse('Erreur lors de l\'analyse des revenus');
    }
  }

  /**
   * Temps de préparation moyen par produit/catégorie
   */
  static async getAveragePreparationTime(serviceId, dateDebut, dateFin) {
    try {
      const query = `
        SELECT
          p.produit_id,
          p.nom as produit_nom,
          cp.nom as categorie_nom,
          AVG(p.temps_preparation) as temps_preparation_theorique,
          COUNT(dc.detail_id) as nb_commandes,
          AVG(EXTRACT(EPOCH FROM (c.updated_at - c.date_commande)) / 60) as temps_reel_moyen
        FROM "DetailsCommandes" dc
        JOIN "Commandes" c ON dc.commande_id = c.commande_id
        JOIN "Produits" p ON dc.produit_id = p.produit_id
        LEFT JOIN "CategoriesProduits" cp ON p.categorie_id = cp.categorie_id
        WHERE c.service_id = $1
        AND c.date_commande BETWEEN $2 AND $3
        AND c.statut IN ('Servie', 'Payée')
        AND p.temps_preparation IS NOT NULL
        GROUP BY p.produit_id, p.nom, cp.nom
        HAVING COUNT(dc.detail_id) >= 3
        ORDER BY temps_reel_moyen DESC
      `;

      const result = await db.query(query, [serviceId, dateDebut, dateFin]);

      return this.successResponse({
        temps_preparation: result.rows,
        statistiques: {
          produits_analyses: result.rows.length,
          temps_moyen_global: result.rows.length > 0
            ? result.rows.reduce((sum, row) => sum + parseFloat(row.temps_reel_moyen || 0), 0) / result.rows.length
            : 0
        }
      });
    } catch (error) {
      logger.error('Erreur analyse temps préparation:', error);
      return this.rejectResponse('Erreur lors de l\'analyse des temps de préparation');
    }
  }

  /**
   * Analyse comparative entre services (Restaurant vs Bar)
   */
  static async getServiceComparison(complexeId, dateDebut, dateFin) {
    try {
      const query = `
        SELECT
          s.service_id,
          s.nom as service_nom,
          s.type_service,
          COUNT(c.commande_id) as nb_commandes,
          SUM(c.montant_total) as chiffre_affaires,
          AVG(c.montant_total) as panier_moyen,
          COUNT(DISTINCT c.table_id) as tables_utilisees,
          COUNT(DISTINCT DATE(c.date_commande)) as jours_activite
        FROM "ServicesComplexe" s
        LEFT JOIN "Commandes" c ON s.service_id = c.service_id
          AND c.date_commande BETWEEN $2 AND $3
          AND c.statut = 'Payée'
        WHERE s.complexe_id = $1
        AND s.type_service IN ('Restaurant', 'Bar')
        GROUP BY s.service_id, s.nom, s.type_service
        ORDER BY chiffre_affaires DESC
      `;

      const result = await db.query(query, [complexeId, dateDebut, dateFin]);

      return this.successResponse({
        comparaison_services: result.rows,
        resume_comparaison: {
          total_services: result.rows.length,
          ca_total: result.rows.reduce((sum, row) => sum + parseFloat(row.chiffre_affaires || 0), 0),
          service_plus_performant: result.rows[0] || null
        }
      });
    } catch (error) {
      logger.error('Erreur comparaison services:', error);
      return this.rejectResponse('Erreur lors de la comparaison des services');
    }
  }

  /**
   * Analyse des tendances de fréquentation par jour de la semaine
   */
  static async getWeeklyTrends(serviceId, dateDebut, dateFin) {
    try {
      const query = `
        SELECT
          EXTRACT(DOW FROM c.date_commande) as jour_semaine,
          CASE EXTRACT(DOW FROM c.date_commande)
            WHEN 0 THEN 'Dimanche'
            WHEN 1 THEN 'Lundi'
            WHEN 2 THEN 'Mardi'
            WHEN 3 THEN 'Mercredi'
            WHEN 4 THEN 'Jeudi'
            WHEN 5 THEN 'Vendredi'
            WHEN 6 THEN 'Samedi'
          END as nom_jour,
          COUNT(c.commande_id) as nb_commandes,
          SUM(c.montant_total) as chiffre_affaires,
          AVG(c.montant_total) as panier_moyen,
          COUNT(DISTINCT c.table_id) as tables_utilisees
        FROM "Commandes" c
        WHERE c.service_id = $1
        AND c.date_commande BETWEEN $2 AND $3
        AND c.statut = 'Payée'
        GROUP BY EXTRACT(DOW FROM c.date_commande)
        ORDER BY jour_semaine
      `;

      const result = await db.query(query, [serviceId, dateDebut, dateFin]);

      // Identifier le jour le plus rentable
      const jourPlusRentable = result.rows.reduce((max, row) =>
        parseFloat(row.chiffre_affaires || 0) > parseFloat(max.chiffre_affaires || 0) ? row : max
      , result.rows[0] || {});

      return this.successResponse({
        tendances_hebdomadaires: result.rows,
        analyse: {
          jour_plus_rentable: jourPlusRentable.nom_jour || null,
          ca_jour_max: parseFloat(jourPlusRentable.chiffre_affaires || 0),
          variation_ca: this.calculateVariation(result.rows.map(r => parseFloat(r.chiffre_affaires || 0)))
        }
      });
    } catch (error) {
      logger.error('Erreur analyse tendances hebdomadaires:', error);
      return this.rejectResponse('Erreur lors de l\'analyse des tendances');
    }
  }

  /**
   * Analyse de rentabilité par employé
   */
  static async getEmployeePerformance(serviceId, dateDebut, dateFin) {
    try {
      const query = `
        SELECT
          e.employe_id,
          e.nom as employe_nom,
          e.prenom as employe_prenom,
          COUNT(c.commande_id) as nb_commandes,
          SUM(c.montant_total) as chiffre_affaires,
          AVG(c.montant_total) as panier_moyen,
          COUNT(DISTINCT DATE(c.date_commande)) as jours_travailles,
          SUM(c.montant_total) / NULLIF(COUNT(DISTINCT DATE(c.date_commande)), 0) as ca_par_jour
        FROM "Employes" e
        JOIN "Commandes" c ON e.employe_id = c.employe_id
        WHERE c.service_id = $1
        AND c.date_commande BETWEEN $2 AND $3
        AND c.statut = 'Payée'
        GROUP BY e.employe_id, e.nom, e.prenom
        ORDER BY chiffre_affaires DESC
      `;

      const result = await db.query(query, [serviceId, dateDebut, dateFin]);

      return this.successResponse({
        performance_employes: result.rows,
        statistiques: {
          employes_actifs: result.rows.length,
          meilleur_vendeur: result.rows[0] || null,
          ca_moyen_par_employe: result.rows.length > 0
            ? result.rows.reduce((sum, row) => sum + parseFloat(row.chiffre_affaires || 0), 0) / result.rows.length
            : 0
        }
      });
    } catch (error) {
      logger.error('Erreur analyse performance employés:', error);
      return this.rejectResponse('Erreur lors de l\'analyse des employés');
    }
  }

  /**
   * Prévisions basées sur les tendances historiques
   */
  static async getForecast(serviceId, joursPrevisionnels = 7) {
    try {
      // Analyser les 30 derniers jours pour établir des tendances
      const dateFin = new Date();
      const dateDebut = new Date();
      dateDebut.setDate(dateFin.getDate() - 30);

      const query = `
        SELECT
          DATE(c.date_commande) as date,
          SUM(c.montant_total) as chiffre_affaires,
          COUNT(c.commande_id) as nb_commandes
        FROM "Commandes" c
        WHERE c.service_id = $1
        AND c.date_commande BETWEEN $2 AND $3
        AND c.statut = 'Payée'
        GROUP BY DATE(c.date_commande)
        ORDER BY date
      `;

      const result = await db.query(query, [serviceId, dateDebut, dateFin]);

      if (result.rows.length < 7) {
        return this.successResponse({
          previsions: [],
          message: 'Données insuffisantes pour établir des prévisions'
        });
      }

      // Calculer les moyennes mobiles
      const caQuotidienMoyen = result.rows.reduce((sum, row) =>
        sum + parseFloat(row.chiffre_affaires || 0), 0) / result.rows.length;

      const commandesQuotidiennesMoyennes = result.rows.reduce((sum, row) =>
        sum + parseInt(row.nb_commandes || 0), 0) / result.rows.length;

      // Générer les prévisions
      const previsions = [];
      for (let i = 1; i <= joursPrevisionnels; i++) {
        const datePrevision = new Date();
        datePrevision.setDate(datePrevision.getDate() + i);

        previsions.push({
          date: datePrevision.toISOString().split('T')[0],
          ca_prevu: Math.round(caQuotidienMoyen * 100) / 100,
          commandes_prevues: Math.round(commandesQuotidiennesMoyennes),
          confiance: result.rows.length >= 21 ? 'Élevée' : 'Moyenne'
        });
      }

      return this.successResponse({
        previsions,
        base_calcul: {
          jours_analyses: result.rows.length,
          ca_quotidien_moyen: caQuotidienMoyen,
          commandes_quotidiennes_moyennes: commandesQuotidiennesMoyennes
        }
      });
    } catch (error) {
      logger.error('Erreur génération prévisions:', error);
      return this.rejectResponse('Erreur lors de la génération des prévisions');
    }
  }

  /**
   * Méthode utilitaire pour calculer la variation
   */
  static calculateVariation(values) {
    if (values.length < 2) return 0;
    const max = Math.max(...values);
    const min = Math.min(...values);
    return max > 0 ? ((max - min) / max) * 100 : 0;
  }
}

module.exports = POSAnalyticsService;
