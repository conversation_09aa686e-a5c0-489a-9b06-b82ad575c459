const db = require('../db');
const logger = require('../logger');

/**
 * Service pour gérer les types d'employés et leurs permissions
 * Phase 1 du plan de simplification des permissions
 */
class EmployeeTypeService {
  
  // Définition des types d'employés et leurs permissions
  static EMPLOYEE_TYPES = {
    reception: {
      name: 'Réception',
      description: 'Employé de réception - Gestion des chambres, clients et réservations',
      permissions: ['reception_operations'],
      default_services: ['hebergement'],
      role_name: 'Employé Réception'
    },
    gerant_piscine: {
      name: '<PERSON><PERSON><PERSON>',
      description: 'Gérant de piscine - Billetterie et gestion des accès piscine',
      permissions: ['piscine_operations'],
      default_services: ['piscine'],
      role_name: '<PERSON><PERSON><PERSON>'
    },
    serveuse: {
      name: 'Serveuse',
      description: 'Serveuse - Prise de commandes bar et restaurant',
      permissions: ['service_operations'],
      default_services: ['bar', 'restaurant'],
      role_name: 'Serveuse'
    },
    gerant_services: {
      name: 'Gérant Services',
      description: 'Gérant de services - Validation commandes et paiements bar/restaurant',
      permissions: ['service_operations', 'management_operations'],
      default_services: ['bar', 'restaurant'],
      role_name: 'Gérant Services'
    },
    cuisine: {
      name: 'Cuisine',
      description: 'Employé de cuisine - Gestion des commandes restaurant',
      permissions: ['kitchen_operations'],
      default_services: ['restaurant'],
      role_name: 'Employé Cuisine'
    }
  };

  // Permissions simplifiées selon le plan
  static SIMPLIFIED_PERMISSIONS = {
    // Permissions administratives (Super Admin, Admin Chaîne, Admin Complexe)
    'full_access': 'Accès complet au système (super admin)',
    'chain_access': 'Accès complet à la chaîne (admin chaîne)',
    'complex_access': 'Accès complet au complexe (admin complexe)',

    // Permissions opérationnelles employés (PAS d'accès config/rapports)
    'reception_operations': 'Opérations réception (chambres, clients, réservations)',
    'piscine_operations': 'Opérations piscine (billetterie, accès)',
    'service_operations': 'Opérations service (commandes bar/restaurant)',
    'management_operations': 'Opérations gestion (validation, paiements)',
    'kitchen_operations': 'Opérations cuisine (voir commandes restaurant)'
  };

  /**
   * Obtenir tous les types d'employés disponibles
   */
  static getAvailableTypes() {
    return Object.keys(this.EMPLOYEE_TYPES).map(type => ({
      type,
      ...this.EMPLOYEE_TYPES[type]
    }));
  }

  /**
   * Obtenir les informations d'un type d'employé spécifique
   */
  static getTypeInfo(type) {
    if (!this.EMPLOYEE_TYPES[type]) {
      throw new Error(`Type d'employé invalide: ${type}`);
    }
    return {
      type,
      ...this.EMPLOYEE_TYPES[type]
    };
  }

  /**
   * Valider un type d'employé
   */
  static isValidType(type) {
    return Object.keys(this.EMPLOYEE_TYPES).includes(type);
  }

  /**
   * Obtenir les permissions pour un type d'employé
   */
  static getPermissionsForType(type) {
    if (!this.isValidType(type)) {
      throw new Error(`Type d'employé invalide: ${type}`);
    }
    return this.EMPLOYEE_TYPES[type].permissions;
  }

  /**
   * Obtenir les services par défaut pour un type d'employé
   */
  static getDefaultServicesForType(type) {
    if (!this.isValidType(type)) {
      throw new Error(`Type d'employé invalide: ${type}`);
    }
    return this.EMPLOYEE_TYPES[type].default_services;
  }

  /**
   * Créer ou obtenir le rôle prédéfini pour un type d'employé dans un complexe
   */
  static async getOrCreatePredefinedRole(complexeId, employeeType) {
    try {
      if (!this.isValidType(employeeType)) {
        throw new Error(`Type d'employé invalide: ${employeeType}`);
      }

      const typeInfo = this.EMPLOYEE_TYPES[employeeType];
      
      // Vérifier si le rôle existe déjà
      const existingRole = await db.query(
        'SELECT * FROM "RolesComplexe" WHERE complexe_id = $1 AND nom = $2',
        [complexeId, typeInfo.role_name]
      );

      if (existingRole.rows.length > 0) {
        return existingRole.rows[0];
      }

      // Créer le rôle s'il n'existe pas
      const newRole = await db.query(`
        INSERT INTO "RolesComplexe" (
          complexe_id, nom, description, permissions, created_at
        ) VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
        RETURNING *
      `, [
        complexeId,
        typeInfo.role_name,
        typeInfo.description,
        JSON.stringify(typeInfo.permissions)
      ]);

      logger.info(`Rôle prédéfini créé: ${typeInfo.role_name} pour complexe ${complexeId}`);
      return newRole.rows[0];
    } catch (error) {
      logger.error('Error creating predefined role:', error);
      throw error;
    }
  }

  /**
   * Assigner un type d'employé et créer/assigner le rôle correspondant
   */
  static async assignEmployeeType(employeeId, employeeType, complexeId, servicesAutorises = null) {
    try {
      if (!this.isValidType(employeeType)) {
        throw new Error(`Type d'employé invalide: ${employeeType}`);
      }

      // Obtenir ou créer le rôle prédéfini
      const role = await this.getOrCreatePredefinedRole(complexeId, employeeType);

      // Déterminer les services autorisés
      const services = servicesAutorises || this.getDefaultServicesForType(employeeType);

      // Mettre à jour l'employé
      const updatedEmployee = await db.query(`
        UPDATE "Employes" 
        SET 
          type_employe = $1,
          role_id = $2,
          services_autorises = $3,
          updated_at = CURRENT_TIMESTAMP
        WHERE employe_id = $4 AND complexe_id = $5
        RETURNING *
      `, [
        employeeType,
        role.role_id,
        JSON.stringify(services),
        employeeId,
        complexeId
      ]);

      if (updatedEmployee.rows.length === 0) {
        throw new Error('Employé non trouvé ou non autorisé pour ce complexe');
      }

      logger.info(`Type d'employé assigné: ${employeeType} pour employé ${employeeId}`);
      return updatedEmployee.rows[0];
    } catch (error) {
      logger.error('Error assigning employee type:', error);
      throw error;
    }
  }

  /**
   * Obtenir les employés par type dans un complexe
   */
  static async getEmployeesByType(complexeId, employeeType = null) {
    try {
      let query = `
        SELECT e.*, r.nom as role_nom, r.permissions
        FROM "Employes" e
        LEFT JOIN "RolesComplexe" r ON e.role_id = r.role_id
        WHERE e.complexe_id = $1 AND e.actif = true
      `;
      const params = [complexeId];

      if (employeeType) {
        if (!this.isValidType(employeeType)) {
          throw new Error(`Type d'employé invalide: ${employeeType}`);
        }
        query += ' AND e.type_employe = $2';
        params.push(employeeType);
      }

      query += ' ORDER BY e.nom, e.prenom';

      const result = await db.query(query, params);
      return result.rows;
    } catch (error) {
      logger.error('Error getting employees by type:', error);
      throw error;
    }
  }

  /**
   * Valider l'accès d'un employé à un service
   */
  static validateServiceAccess(employee, serviceType) {
    if (!employee.services_autorises) {
      return false;
    }

    const servicesAutorises = Array.isArray(employee.services_autorises) 
      ? employee.services_autorises 
      : JSON.parse(employee.services_autorises);

    return servicesAutorises.includes(serviceType);
  }

  /**
   * Obtenir les statistiques des types d'employés dans un complexe
   */
  static async getEmployeeTypeStats(complexeId) {
    try {
      const result = await db.query(`
        SELECT 
          type_employe,
          COUNT(*) as count,
          COUNT(CASE WHEN actif = true THEN 1 END) as active_count
        FROM "Employes" 
        WHERE complexe_id = $1
        GROUP BY type_employe
        ORDER BY type_employe
      `, [complexeId]);

      return result.rows;
    } catch (error) {
      logger.error('Error getting employee type stats:', error);
      throw error;
    }
  }
}

module.exports = EmployeeTypeService;
