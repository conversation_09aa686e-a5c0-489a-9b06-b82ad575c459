// eslint-disable-next-line import/no-extraneous-dependencies
const bcrypt = require('bcryptjs');
// eslint-disable-next-line import/no-extraneous-dependencies
const jwt = require('jsonwebtoken');
const db = require('../db');
const { JWT_SECRET, JWT_EXPIRATION } = require('../config/jwt.config');
const logger = require('../logger');

class AuthService {
  static async login(email, password, userType) {
    try {
      let user;
      let role;

      // Determine which table to query based on userType
      switch (userType) {
        case 'super_admin':
          user = await db.query(
            'SELECT * FROM "UtilisateursSuperAdmin" WHERE email = $1',
            [email],
          );
          role = 'super_admin';
          break;
        case 'admin_chaine':
          user = await db.query(
            'SELECT ac.*, ch.chaine_id FROM "AdminsChaine" ac JOIN "ChainesHotelieres" ch ON ac.chaine_id = ch.chaine_id WHERE ac.email = $1',
            [email],
          );
          role = 'admin_chaine';
          break;
        case 'admin_complexe':
          user = await db.query(
            'SELECT ac.*, ch.complexe_id FROM "AdminsComplexe" ac JOIN "ComplexesHoteliers" ch ON ac.complexe_id = ch.complexe_id WHERE ac.email = $1',
            [email],
          );
          role = 'admin_complexe';
          break;
        case 'employe':
          user = await db.query(
            'SELECT e.*, ch.complexe_id FROM "Employes" e JOIN "ComplexesHoteliers" ch ON e.complexe_id = ch.complexe_id WHERE e.email = $1',
            [email],
          );
          role = 'employe';
          break;
        default:
          throw new Error('Invalid user type');
      }

      if (!user.rows[0]) {
        throw new Error('User not found');
      }

      const userData = user.rows[0];
      const validPassword = await bcrypt.compare(password, userData.mot_de_passe_hash);

      if (!validPassword) {
        throw new Error('Invalid password');
      }

      // Update last login timestamp
      let tableName;
      if (userType === 'super_admin') {
        tableName = '"UtilisateursSuperAdmin"';
      } else if (userType === 'admin_chaine') {
        tableName = '"AdminsChaine"';
      } else if (userType === 'admin_complexe') {
        tableName = '"AdminsComplexe"';
      } else {
        tableName = '"Employes"';
      }

      const updateQuery = `
        UPDATE ${tableName}
        SET derniere_connexion = NOW()
        WHERE email = $1
      `;
      await db.query(updateQuery, [email]);

      // Generate JWT token
      const token = jwt.sign(
        {
          id: userData[`${userType}_id`],
          email: userData.email,
          role,
          chaine_id: userData.chaine_id,
          complexe_id: userData.complexe_id,
        },
        JWT_SECRET,
        { expiresIn: JWT_EXPIRATION },
      );

      // Return user data based on role
      const userResponse = {
        id: userData[`${userType}_id`],
        email: userData.email,
        nom: userData.nom,
        prenom: userData.prenom,
        role,
      };

      // Add chaine_id for admin_chaine
      if (userType === 'admin_chaine') {
        userResponse.chaine_id = userData.chaine_id;
      }

      // Add complexe_id for admin_complexe and employe
      if (userType === 'admin_complexe' || userType === 'employe') {
        userResponse.complexe_id = userData.complexe_id;
      }

      return {
        token,
        user: userResponse,
      };
    } catch (error) {
      logger.error('Login error:', error);
      throw error;
    }
  }

  static async hashPassword(password) {
    const salt = await bcrypt.genSalt(10);
    return bcrypt.hash(password, salt);
  }

  static async verifyPassword(password, hash) {
    return bcrypt.compare(password, hash);
  }
}

module.exports = AuthService;
