const db = require('../db');
const logger = require('../logger');

/**
 * Service pour la gestion avancée des stocks d'ingrédients
 * Gère les mouvements, consommations, alertes et prévisions
 */
class StockManagementService {
  
  /**
   * Réponse de succès standardisée
   */
  static successResponse(data, message = 'Opération réussie') {
    return { success: true, data, message };
  }

  /**
   * Réponse d'erreur standardisée
   */
  static errorResponse(message, code = 500) {
    return { success: false, message, code };
  }

  /**
   * ==================== GESTION DES MOUVEMENTS DE STOCK ====================
   */

  /**
   * Enregistrer un mouvement de stock
   */
  static async enregistrerMouvement(ingredientId, complexeId, typeMouvement, quantite, options = {}) {
    const client = await db.getClient();
    
    try {
      await client.query('BEGIN');

      // Récupérer le stock actuel
      const stockResult = await client.query(
        'SELECT stock_actuel, prix_unitaire_moyen FROM "Ingredients" WHERE ingredient_id = $1',
        [ingredientId]
      );

      if (stockResult.rows.length === 0) {
        throw new Error('Ingrédient non trouvé');
      }

      const stockActuel = parseFloat(stockResult.rows[0].stock_actuel) || 0;
      const prixUnitaire = options.prixUnitaire || parseFloat(stockResult.rows[0].prix_unitaire_moyen) || 0;

      // Calculer le nouveau stock
      let nouveauStock = stockActuel;
      if (typeMouvement === 'ENTREE' || typeMouvement === 'AJUSTEMENT') {
        nouveauStock += parseFloat(quantite);
      } else if (typeMouvement === 'SORTIE' || typeMouvement === 'CONSOMMATION' || typeMouvement === 'PERTE') {
        nouveauStock -= parseFloat(quantite);
      }

      // Vérifier que le stock ne devient pas négatif (sauf pour les ajustements)
      if (nouveauStock < 0 && typeMouvement !== 'AJUSTEMENT') {
        throw new Error(`Stock insuffisant. Stock actuel: ${stockActuel}, quantité demandée: ${quantite}`);
      }

      // Enregistrer le mouvement
      const mouvementResult = await client.query(`
        INSERT INTO "MouvementsStockIngredients" (
          ingredient_id, complexe_id, type_mouvement, quantite, 
          quantite_avant, quantite_apres, prix_unitaire,
          reference_id, reference_type, employe_id, notes
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        RETURNING *
      `, [
        ingredientId, complexeId, typeMouvement, quantite,
        stockActuel, nouveauStock, prixUnitaire,
        options.referenceId || null, options.referenceType || null,
        options.employeId || null, options.notes || null
      ]);

      // Le trigger se charge de mettre à jour le stock_actuel automatiquement

      await client.query('COMMIT');

      // Vérifier s'il faut créer une alerte
      await this.verifierAlertes(ingredientId, complexeId);

      return this.successResponse(mouvementResult.rows[0], 'Mouvement de stock enregistré');

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Erreur lors de l\'enregistrement du mouvement de stock:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Initialiser le stock d'un ingrédient (lors de l'import)
   */
  static async initialiserStock(ingredientId, complexeId, stockInitial, employeId) {
    try {
      // Mettre à jour le stock initial dans la table Ingredients
      await db.query(`
        UPDATE "Ingredients" 
        SET stock_initial = $1, stock_actuel = $1, updated_at = NOW()
        WHERE ingredient_id = $2
      `, [stockInitial, ingredientId]);

      // Enregistrer le mouvement d'initialisation
      return await this.enregistrerMouvement(
        ingredientId, 
        complexeId, 
        'ENTREE', 
        stockInitial,
        {
          referenceType: 'INITIALISATION',
          employeId,
          notes: 'Stock initial lors de l\'import'
        }
      );

    } catch (error) {
      logger.error('Erreur lors de l\'initialisation du stock:', error);
      throw error;
    }
  }

  /**
   * ==================== CONSOMMATION AUTOMATIQUE ====================
   */

  /**
   * Consommer les ingrédients d'un produit lors d'une commande
   */
  static async consommerProduit(produitId, commandeId, quantitePortions = 1) {
    const client = await db.getClient();

    try {
      await client.query('BEGIN');

      // Récupérer les ingrédients du produit
      const ingredientsResult = await client.query(`
        SELECT
          pi.ingredient_id,
          pi.quantite_necessaire,
          pi.cout_unitaire,
          i.nom as ingredient_nom,
          i.stock_actuel,
          i.complexe_id,
          p.nom as produit_nom
        FROM "ProduitsIngredients" pi
        JOIN "Ingredients" i ON pi.ingredient_id = i.ingredient_id
        JOIN "Produits" p ON pi.produit_id = p.produit_id
        WHERE pi.produit_id = $1 AND i.actif = true
      `, [produitId]);

      const consommations = [];
      const alertes = [];

      for (const ingredient of ingredientsResult.rows) {
        const quantiteConsommee = parseFloat(ingredient.quantite_necessaire) * quantitePortions;
        const coutTheorique = parseFloat(ingredient.cout_unitaire) * quantiteConsommee;

        // Vérifier la disponibilité
        if (ingredient.stock_actuel < quantiteConsommee) {
          alertes.push({
            ingredient: ingredient.ingredient_nom,
            stockActuel: ingredient.stock_actuel,
            quantiteNecessaire: quantiteConsommee
          });
        }

        // Enregistrer la consommation théorique
        const consommationResult = await client.query(`
          INSERT INTO "ConsommationsIngredients" (
            ingredient_id, produit_id, commande_id,
            quantite_theorique, cout_theorique
          ) VALUES ($1, $2, $3, $4, $5)
          RETURNING *
        `, [
          ingredient.ingredient_id, produitId, commandeId,
          quantiteConsommee, coutTheorique
        ]);

        consommations.push(consommationResult.rows[0]);

        // Enregistrer le mouvement de stock
        if (ingredient.stock_actuel >= quantiteConsommee) {
          await this.enregistrerMouvement(
            ingredient.ingredient_id,
            ingredient.complexe_id,
            'CONSOMMATION',
            quantiteConsommee,
            {
              referenceId: commandeId,
              referenceType: 'COMMANDE',
              notes: `Consommation pour produit: ${ingredient.produit_nom}`
            }
          );
        }
      }

      await client.query('COMMIT');

      return this.successResponse({
        consommations,
        alertes,
        totalIngredients: ingredientsResult.rows.length,
        ingredientsDisponibles: ingredientsResult.rows.length - alertes.length
      }, 'Consommation de produit enregistrée');

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Erreur lors de la consommation de produit:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * ==================== GESTION DES ALERTES ====================
   */

  /**
   * Vérifier et créer les alertes de stock pour un ingrédient
   */
  static async verifierAlertes(ingredientId, complexeId) {
    try {
      // Récupérer les informations de l'ingrédient
      const ingredientResult = await db.query(`
        SELECT nom, stock_actuel, stock_minimal, stock_maximal, unite_mesure
        FROM "Ingredients"
        WHERE ingredient_id = $1
      `, [ingredientId]);

      if (ingredientResult.rows.length === 0) {
        return;
      }

      const ingredient = ingredientResult.rows[0];
      const stockActuel = parseFloat(ingredient.stock_actuel) || 0;
      const stockMinimal = parseFloat(ingredient.stock_minimal) || 0;

      // Supprimer les anciennes alertes actives pour cet ingrédient
      await db.query(`
        UPDATE "AlertesStock" 
        SET statut = 'RESOLUE', date_resolution = NOW()
        WHERE ingredient_id = $1 AND statut = 'ACTIVE'
      `, [ingredientId]);

      let typeAlerte = null;
      let niveauUrgence = null;
      let message = null;
      let seuil = 0;

      // Déterminer le type d'alerte
      if (stockActuel <= 0) {
        typeAlerte = 'RUPTURE';
        niveauUrgence = 'CRITIQUE';
        message = `Rupture de stock: ${ingredient.nom}`;
        seuil = 0;
      } else if (stockActuel <= stockMinimal * 0.5) {
        typeAlerte = 'STOCK_FAIBLE';
        niveauUrgence = 'ELEVE';
        message = `Stock très faible: ${ingredient.nom} (${stockActuel} ${ingredient.unite_mesure})`;
        seuil = stockMinimal * 0.5;
      } else if (stockActuel <= stockMinimal) {
        typeAlerte = 'STOCK_FAIBLE';
        niveauUrgence = 'MOYEN';
        message = `Stock faible: ${ingredient.nom} (${stockActuel}/${stockMinimal} ${ingredient.unite_mesure})`;
        seuil = stockMinimal;
      }

      // Créer l'alerte si nécessaire
      if (typeAlerte) {
        await db.query(`
          INSERT INTO "AlertesStock" (
            ingredient_id, complexe_id, type_alerte, niveau_urgence,
            message, stock_actuel, seuil_declenche
          ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        `, [
          ingredientId, complexeId, typeAlerte, niveauUrgence,
          message, stockActuel, seuil
        ]);
      }

    } catch (error) {
      logger.error('Erreur lors de la vérification des alertes:', error);
      // Ne pas faire échouer l'opération principale
    }
  }

  /**
   * Récupérer les alertes actives pour un complexe
   */
  static async getAlertes(complexeId, niveauUrgence = null) {
    try {
      let query = 'SELECT * FROM "VueAlertes" WHERE complexe_id = $1';
      const params = [complexeId];

      if (niveauUrgence) {
        query += ' AND niveau_urgence = $2';
        params.push(niveauUrgence);
      }

      query += ' ORDER BY niveau_urgence, date_alerte DESC';

      const result = await db.query(query, params);

      return this.successResponse(result.rows, 'Alertes récupérées');

    } catch (error) {
      logger.error('Erreur lors de la récupération des alertes:', error);
      throw error;
    }
  }

  /**
   * ==================== RAPPORTS ET ANALYSES ====================
   */

  /**
   * Rapport de consommation par période
   */
  static async getRapportConsommation(complexeId, dateDebut, dateFin) {
    try {
      const result = await db.query(`
        SELECT 
          i.ingredient_id,
          i.nom as ingredient_nom,
          i.categorie,
          i.unite_mesure,
          COUNT(ci.consommation_id) as nombre_utilisations,
          SUM(ci.quantite_theorique) as quantite_totale_theorique,
          SUM(ci.quantite_reelle) as quantite_totale_reelle,
          AVG(ci.quantite_theorique) as quantite_moyenne_theorique,
          SUM(ci.cout_theorique) as cout_total_theorique,
          SUM(ci.cout_reel) as cout_total_reel
        FROM "Ingredients" i
        LEFT JOIN "ConsommationsIngredients" ci ON i.ingredient_id = ci.ingredient_id
        WHERE i.complexe_id = $1 
        AND ci.date_consommation BETWEEN $2 AND $3
        GROUP BY i.ingredient_id, i.nom, i.categorie, i.unite_mesure
        ORDER BY quantite_totale_theorique DESC
      `, [complexeId, dateDebut, dateFin]);

      return this.successResponse(result.rows, 'Rapport de consommation généré');

    } catch (error) {
      logger.error('Erreur lors de la génération du rapport de consommation:', error);
      throw error;
    }
  }
}

module.exports = StockManagementService;
