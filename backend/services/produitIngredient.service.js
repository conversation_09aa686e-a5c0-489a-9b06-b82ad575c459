const db = require('../db');
const logger = require('../logger');

/**
 * Service pour gérer les liens entre produits et ingrédients
 * Remplace le système de recettes par une approche directe
 */
class ProduitIngredientService {
  
  /**
   * Réponse de succès standardisée
   */
  static successResponse(data, message = 'Opération réussie') {
    return { success: true, data, message };
  }

  /**
   * Réponse d'erreur standardisée
   */
  static errorResponse(message, code = 500) {
    return { success: false, message, code };
  }

  /**
   * ==================== GESTION DES LIENS PRODUIT-INGRÉDIENT ====================
   */

  /**
   * Ajouter un ingrédient à un produit
   */
  static async ajouterIngredientProduit(produitId, ingredientId, quantiteNecessaire, uniteMesure, options = {}) {
    try {
      // Vérifier que le produit et l'ingrédient existent
      const produitResult = await db.query('SELECT * FROM "Produits" WHERE produit_id = $1', [produitId]);
      const ingredientResult = await db.query('SELECT * FROM "Ingredients" WHERE ingredient_id = $1', [ingredientId]);

      if (produitResult.rows.length === 0) {
        throw new Error('Produit non trouvé');
      }

      if (ingredientResult.rows.length === 0) {
        throw new Error('Ingrédient non trouvé');
      }

      const ingredient = ingredientResult.rows[0];
      const coutUnitaire = options.coutUnitaire || ingredient.prix_unitaire_moyen || 0;

      // Insérer ou mettre à jour le lien
      const result = await db.query(`
        INSERT INTO "ProduitsIngredients" (
          produit_id, ingredient_id, quantite_necessaire, unite_mesure, 
          cout_unitaire, optionnel, notes
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT (produit_id, ingredient_id) 
        DO UPDATE SET 
          quantite_necessaire = EXCLUDED.quantite_necessaire,
          unite_mesure = EXCLUDED.unite_mesure,
          cout_unitaire = EXCLUDED.cout_unitaire,
          optionnel = EXCLUDED.optionnel,
          notes = EXCLUDED.notes,
          updated_at = NOW()
        RETURNING *
      `, [
        produitId, ingredientId, quantiteNecessaire, uniteMesure,
        coutUnitaire, options.optionnel || false, options.notes || null
      ]);

      // Mettre à jour le coût du produit
      await this.calculerCoutProduit(produitId);

      return this.successResponse(result.rows[0], 'Ingrédient ajouté au produit');

    } catch (error) {
      logger.error('Erreur lors de l\'ajout d\'ingrédient au produit:', error);
      throw error;
    }
  }

  /**
   * Supprimer un ingrédient d'un produit
   */
  static async supprimerIngredientProduit(produitId, ingredientId) {
    try {
      const result = await db.query(`
        DELETE FROM "ProduitsIngredients" 
        WHERE produit_id = $1 AND ingredient_id = $2
        RETURNING *
      `, [produitId, ingredientId]);

      if (result.rows.length === 0) {
        throw new Error('Lien produit-ingrédient non trouvé');
      }

      // Mettre à jour le coût du produit
      await this.calculerCoutProduit(produitId);

      return this.successResponse(result.rows[0], 'Ingrédient supprimé du produit');

    } catch (error) {
      logger.error('Erreur lors de la suppression d\'ingrédient du produit:', error);
      throw error;
    }
  }

  /**
   * Récupérer les ingrédients d'un produit
   */
  static async getIngredientsProduit(produitId) {
    try {
      const result = await db.query(`
        SELECT 
          pi.*,
          i.nom as ingredient_nom,
          i.categorie,
          i.unite_mesure as unite_stock,
          i.prix_unitaire_moyen,
          i.stock_actuel,
          i.stock_minimal
        FROM "ProduitsIngredients" pi
        JOIN "Ingredients" i ON pi.ingredient_id = i.ingredient_id
        WHERE pi.produit_id = $1
        ORDER BY i.nom
      `, [produitId]);

      return this.successResponse(result.rows, 'Ingrédients du produit récupérés');

    } catch (error) {
      logger.error('Erreur lors de la récupération des ingrédients du produit:', error);
      throw error;
    }
  }

  /**
   * Récupérer tous les produits avec leurs ingrédients
   */
  static async getProduitsAvecIngredients(complexeId, serviceId = null) {
    try {
      let query = `
        SELECT 
          p.produit_id,
          p.nom as produit_nom,
          p.description,
          p.prix_vente_defaut,
          p.cout_ingredients,
          COUNT(pi.ingredient_id) as nombre_ingredients,
          SUM(pi.cout_total) as cout_total_ingredients
        FROM "Produits" p
        LEFT JOIN "ProduitsIngredients" pi ON p.produit_id = pi.produit_id
        WHERE p.complexe_id = $1 AND p.actif = true
      `;
      const params = [complexeId];

      if (serviceId) {
        query += ' AND p.service_id = $2';
        params.push(serviceId);
      }

      query += `
        GROUP BY p.produit_id, p.nom, p.description, p.prix_vente_defaut, p.cout_ingredients
        ORDER BY p.nom
      `;

      const result = await db.query(query, params);

      return this.successResponse(result.rows, 'Produits avec ingrédients récupérés');

    } catch (error) {
      logger.error('Erreur lors de la récupération des produits avec ingrédients:', error);
      throw error;
    }
  }

  /**
   * Calculer le coût total des ingrédients d'un produit
   */
  static async calculerCoutProduit(produitId) {
    try {
      const result = await db.query(`
        SELECT COALESCE(SUM(cout_total), 0) as cout_total
        FROM "ProduitsIngredients"
        WHERE produit_id = $1
      `, [produitId]);

      const coutTotal = parseFloat(result.rows[0].cout_total) || 0;

      // Mettre à jour le coût dans la table Produits
      await db.query(`
        UPDATE "Produits" 
        SET cout_ingredients = $1, updated_at = NOW()
        WHERE produit_id = $2
      `, [coutTotal, produitId]);

      return coutTotal;

    } catch (error) {
      logger.error('Erreur lors du calcul du coût du produit:', error);
      throw error;
    }
  }

  /**
   * Vérifier la disponibilité des ingrédients pour un produit
   */
  static async verifierDisponibiliteProduit(produitId, quantite = 1) {
    try {
      const result = await db.query(`
        SELECT 
          pi.ingredient_id,
          pi.quantite_necessaire,
          i.nom as ingredient_nom,
          i.stock_actuel,
          i.unite_mesure,
          (pi.quantite_necessaire * $2) as quantite_totale_necessaire,
          CASE 
            WHEN i.stock_actuel >= (pi.quantite_necessaire * $2) THEN true
            ELSE false
          END as disponible
        FROM "ProduitsIngredients" pi
        JOIN "Ingredients" i ON pi.ingredient_id = i.ingredient_id
        WHERE pi.produit_id = $1 AND i.actif = true
      `, [produitId, quantite]);

      const ingredients = result.rows;
      const ingredientsIndisponibles = ingredients.filter(ing => !ing.disponible);

      return this.successResponse({
        produit_id: produitId,
        quantite_demandee: quantite,
        ingredients,
        disponible: ingredientsIndisponibles.length === 0,
        ingredients_indisponibles: ingredientsIndisponibles
      }, 'Vérification de disponibilité effectuée');

    } catch (error) {
      logger.error('Erreur lors de la vérification de disponibilité:', error);
      throw error;
    }
  }

  /**
   * Import en masse des liens produits-ingrédients depuis un fichier Excel
   */
  static async importProduitsIngredients(complexeId, excelData) {
    const client = await db.getClient();
    
    try {
      await client.query('BEGIN');

      const results = {
        success: 0,
        errors: [],
        warnings: []
      };

      for (const [index, row] of excelData.entries()) {
        try {
          const rowNumber = index + 2; // +2 car index commence à 0 et ligne 1 = headers

          // Validation des champs obligatoires
          if (!row.nom_produit || !row.nom_ingredient || !row.quantite_necessaire) {
            results.errors.push(`Ligne ${rowNumber}: nom_produit, nom_ingredient et quantite_necessaire sont obligatoires`);
            continue;
          }

          // Rechercher le produit
          const produitResult = await client.query(`
            SELECT produit_id FROM "Produits" 
            WHERE nom = $1 AND complexe_id = $2 AND actif = true
          `, [row.nom_produit, complexeId]);

          if (produitResult.rows.length === 0) {
            results.errors.push(`Ligne ${rowNumber}: Produit "${row.nom_produit}" non trouvé`);
            continue;
          }

          // Rechercher l'ingrédient
          const ingredientResult = await client.query(`
            SELECT ingredient_id, prix_unitaire_moyen FROM "Ingredients" 
            WHERE nom = $1 AND complexe_id = $2 AND actif = true
          `, [row.nom_ingredient, complexeId]);

          if (ingredientResult.rows.length === 0) {
            results.errors.push(`Ligne ${rowNumber}: Ingrédient "${row.nom_ingredient}" non trouvé`);
            continue;
          }

          const produitId = produitResult.rows[0].produit_id;
          const ingredientId = ingredientResult.rows[0].ingredient_id;
          const prixUnitaire = ingredientResult.rows[0].prix_unitaire_moyen;

          // Ajouter le lien
          await this.ajouterIngredientProduit(
            produitId,
            ingredientId,
            parseFloat(row.quantite_necessaire),
            row.unite_mesure || 'unité',
            {
              coutUnitaire: row.cout_unitaire || prixUnitaire,
              optionnel: row.optionnel === 'true' || row.optionnel === true,
              notes: row.notes || null
            }
          );

          results.success++;

        } catch (error) {
          results.errors.push(`Ligne ${index + 2}: ${error.message}`);
        }
      }

      await client.query('COMMIT');

      return this.successResponse(results, `Import terminé: ${results.success} liens créés, ${results.errors.length} erreurs`);

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Erreur lors de l\'import des liens produits-ingrédients:', error);
      throw error;
    } finally {
      client.release();
    }
  }
}

module.exports = ProduitIngredientService;
