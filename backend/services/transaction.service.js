const db = require('../db');
const logger = require('../logger');

class TransactionService {
  // Méthodes utilitaires
  static successResponse(data) {
    return {
      success: true,
      data
    };
  }

  static rejectResponse(message, code = 500) {
    return {
      success: false,
      message,
      code
    };
  }

  // Création d'une transaction
  static async createTransaction(params) {
    const {
      complexe_id,
      service_id,
      pos_id,
      session_id,
      employe_id,
      client_id,
      chambre_id,
      reference,
      code_promo_id,
      montant_total,
      mode_paiement,
      notes
    } = params;

    try {
      await db.query('BEGIN');

      // Vérifier si la session de caisse est ouverte
      const sessionQuery = `
        SELECT statut 
        FROM "SessionsCaisse" 
        WHERE session_id = $1 AND statut = 'Ouverte'
      `;
      
      const sessionResult = await db.query(sessionQuery, [session_id]);
      
      if (sessionResult.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Session de caisse non trouvée ou fermée', 400);
      }

      // Créer la transaction
      const transactionQuery = `
        INSERT INTO "TransactionsPOS" (
          complexe_id,
          service_id,
          pos_id,
          session_id,
          employe_id,
          client_id,
          chambre_id,
          reference,
          code_promo_id,
          montant_total,
          montant_paye,
          mode_paiement,
          statut,
          notes
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
        RETURNING *
      `;

      const transactionResult = await db.query(transactionQuery, [
        complexe_id,
        service_id,
        pos_id,
        session_id,
        employe_id,
        client_id,
        chambre_id,
        reference,
        code_promo_id,
        montant_total,
        0, // montant_paye initial
        mode_paiement,
        'En cours',
        notes
      ]);

      await db.query('COMMIT');
      logger.info('Transaction créée', { 
        transaction_id: transactionResult.rows[0].transaction_id,
        reference 
      });

      return this.successResponse(transactionResult.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur création transaction:', error);
      return this.rejectResponse('Erreur lors de la création de la transaction');
    }
  }

  // Récupération d'une transaction par ID
  static async getTransactionById(transactionId) {
    try {
      const query = `
        SELECT 
          t.*,
          c.nom as client_nom,
          c.prenom as client_prenom,
          ch.numero as numero_chambre,
          p.reference as reference_pos,
          s.nom as nom_service
        FROM "TransactionsPOS" t
        LEFT JOIN "Clients" c ON t.client_id = c.client_id
        LEFT JOIN "Chambres" ch ON t.chambre_id = ch.chambre_id
        LEFT JOIN "PointsDeVente" p ON t.pos_id = p.pos_id
        LEFT JOIN "ServicesComplexe" s ON t.service_id = s.service_id
        WHERE t.transaction_id = $1
      `;
      
      const result = await db.query(query, [transactionId]);
      
      if (result.rows.length === 0) {
        return this.rejectResponse('Transaction non trouvée', 404);
      }

      // Récupérer les paiements associés
      const paiementsQuery = `
        SELECT * FROM "Paiements"
        WHERE transaction_id = $1
        ORDER BY date_paiement DESC
      `;
      
      const paiementsResult = await db.query(paiementsQuery, [transactionId]);
      
      const transaction = result.rows[0];
      transaction.paiements = paiementsResult.rows;

      return this.successResponse(transaction);
    } catch (error) {
      logger.error('Erreur récupération transaction:', error);
      return this.rejectResponse('Erreur lors de la récupération de la transaction');
    }
  }

  // Mise à jour du statut d'une transaction
  static async updateTransactionStatus(transactionId, nouveauStatut) {
    try {
      await db.query('BEGIN');

      // Vérifier si la transaction existe
      const checkQuery = `
        SELECT * FROM "TransactionsPOS" 
        WHERE transaction_id = $1
        FOR UPDATE
      `;
      
      const checkResult = await db.query(checkQuery, [transactionId]);
      
      if (checkResult.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Transaction non trouvée', 404);
      }

      // Valider le nouveau statut
      const statutsValides = ['En cours', 'Payée', 'Annulée', 'Remboursée'];
      if (!statutsValides.includes(nouveauStatut)) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Statut invalide');
      }

      // Mettre à jour le statut
      const updateQuery = `
        UPDATE "TransactionsPOS" 
        SET statut = $1
        WHERE transaction_id = $2
        RETURNING *
      `;

      const updateResult = await db.query(updateQuery, [nouveauStatut, transactionId]);

      await db.query('COMMIT');
      logger.info('Statut transaction mis à jour', {
        transaction_id: transactionId,
        nouveau_statut: nouveauStatut
      });

      return this.successResponse(updateResult.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur mise à jour statut transaction:', error);
      return this.rejectResponse('Erreur lors de la mise à jour du statut de la transaction');
    }
  }

  // Récupération des transactions d'une session de caisse
  static async getTransactionsBySession(sessionId) {
    try {
      const query = `
        SELECT 
          t.*,
          c.nom as client_nom,
          c.prenom as client_prenom,
          ch.numero as numero_chambre
        FROM "TransactionsPOS" t
        LEFT JOIN "Clients" c ON t.client_id = c.client_id
        LEFT JOIN "Chambres" ch ON t.chambre_id = ch.chambre_id
        WHERE t.session_id = $1
        ORDER BY t.date_transaction DESC
      `;
      
      const result = await db.query(query, [sessionId]);
      
      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération transactions session:', error);
      return this.rejectResponse('Erreur lors de la récupération des transactions de la session');
    }
  }

  // Récupération des transactions d'un client
  static async getTransactionsByClient(clientId) {
    try {
      const query = `
        SELECT 
          t.*,
          ch.numero as numero_chambre,
          p.reference as reference_pos
        FROM "TransactionsPOS" t
        LEFT JOIN "Chambres" ch ON t.chambre_id = ch.chambre_id
        LEFT JOIN "PointsDeVente" p ON t.pos_id = p.pos_id
        WHERE t.client_id = $1
        ORDER BY t.date_transaction DESC
      `;
      
      const result = await db.query(query, [clientId]);
      
      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération transactions client:', error);
      return this.rejectResponse('Erreur lors de la récupération des transactions du client');
    }
  }

  // Vérification du solde d'une transaction
  static async verifierSoldeTransaction(transactionId) {
    try {
      const query = `
        SELECT 
          t.transaction_id,
          t.montant_total,
          COALESCE(SUM(p.montant), 0) as montant_paye
        FROM "TransactionsPOS" t
        LEFT JOIN "Paiements" p ON t.transaction_id = p.transaction_id
        WHERE t.transaction_id = $1
        GROUP BY t.transaction_id, t.montant_total
      `;
      
      const result = await db.query(query, [transactionId]);
      
      if (result.rows.length === 0) {
        return this.rejectResponse('Transaction non trouvée', 404);
      }

      const { montant_total, montant_paye } = result.rows[0];
      const solde = montant_total - montant_paye;

      return this.successResponse({
        transaction_id: transactionId,
        montant_total,
        montant_paye,
        solde_restant: solde,
        est_payee: solde <= 0
      });
    } catch (error) {
      logger.error('Erreur vérification solde transaction:', error);
      return this.rejectResponse('Erreur lors de la vérification du solde de la transaction');
    }
  }
}

module.exports = TransactionService; 