const db = require('../db');
const logger = require('../logger');
const EmployeeTypeService = require('./employeeType.service');

// =====================================================
// PHASE 2 : SYSTÈME DE PERMISSIONS SIMPLIFIÉ
// Plan de Simplification du Système de Permissions
// =====================================================

// Permissions simplifiées - 8 permissions au lieu de 50+
const SIMPLIFIED_PERMISSIONS = {
  // Permissions administratives (Super Admin, Admin Chaîne, Admin Complexe)
  full_access: 'Accès complet au système (super admin)',
  chain_access: 'Accès complet à la chaîne (admin chaîne)',
  complex_access: 'Accès complet au complexe (admin complexe)',

  // Permissions opérationnelles employés (PAS d'accès config/rapports)
  reception_operations: 'Opérations réception (chambres, clients, réservations)',
  piscine_operations: 'Opérations piscine (billetterie, accès)',
  service_operations: 'Opérations service (commandes bar/restaurant)',
  management_operations: 'Opérations gestion (validation, paiements)',
  kitchen_operations: 'Opérations cuisine (voir commandes restaurant)'
};

// Mapping des permissions par type d'utilisateur
const USER_TYPE_PERMISSIONS = {
  // Admins : Accès complet incluant rapports, configuration, gestion
  super_admin: ['full_access'],
  admin_chaine: ['chain_access'],
  admin_complexe: ['complex_access'],

  // Employés : Accès opérationnel uniquement (PAS de rapports/config)
  employe: {
    reception: ['reception_operations'],
    gerant_piscine: ['piscine_operations'],
    serveuse: ['service_operations'],
    gerant_services: ['service_operations', 'management_operations'],
    cuisine: ['kitchen_operations']
  }
};

// Anciennes permissions pour rétrocompatibilité (DEPRECATED)
const LEGACY_PERMISSIONS = {
  // === GESTION DES RÉSERVATIONS ===
  manage_reservations: 'Gérer toutes les réservations',
  view_reservations: 'Consulter les réservations',
  create_reservation: 'Créer des réservations',
  update_reservation: 'Modifier des réservations',
  delete_reservation: 'Supprimer des réservations',
  confirm_reservation: 'Confirmer des réservations',
  reject_reservation: 'Rejeter des réservations',
  view_reservation_history: 'Voir l\'historique des réservations',
  print_reservation_ticket: 'Imprimer les tickets de réservation',

  // === GESTION DES CLIENTS ===
  manage_clients: 'Gérer tous les clients',
  view_clients: 'Consulter les clients',
  create_client: 'Créer des clients',
  update_client: 'Modifier des clients',
  delete_client: 'Supprimer des clients',
  view_client_history: 'Voir l\'historique des clients',

  // === GESTION DES CHAMBRES ===
  manage_rooms: 'Gérer toutes les chambres',
  view_rooms: 'Consulter les chambres',
  create_room: 'Créer des chambres',
  update_room: 'Modifier des chambres',
  delete_room: 'Supprimer des chambres',
  view_room_statistics: 'Voir les statistiques des chambres',
  view_room_calendar: 'Voir le calendrier des chambres',
  manage_room_availability: 'Gérer la disponibilité des chambres',

  // === GESTION DES PAIEMENTS ===
  manage_payments: 'Gérer tous les paiements',
  view_payments: 'Consulter les paiements',
  create_payment: 'Créer des paiements',
  update_payment: 'Modifier des paiements',
  process_payment: 'Traiter des paiements',
  refund_payment: 'Rembourser des paiements',
  view_payment_history: 'Voir l\'historique des paiements',

  // === GESTION DES EMPLOYÉS ===
  manage_employees: 'Gérer tous les employés',
  view_employees: 'Consulter les employés',
  create_employee: 'Créer des employés',
  update_employee: 'Modifier des employés',
  delete_employee: 'Supprimer des employés',
  manage_employee_passwords: 'Gérer les mots de passe des employés',

  // === GESTION DES RÔLES ===
  manage_roles: 'Gérer tous les rôles',
  view_roles: 'Consulter les rôles',
  create_role: 'Créer des rôles',
  update_role: 'Modifier des rôles',
  delete_role: 'Supprimer des rôles',
  assign_roles: 'Attribuer des rôles aux employés',

  // === GESTION DES SERVICES ===
  manage_services: 'Gérer tous les services du complexe',
  view_services: 'Consulter les services',
  create_service: 'Créer des services',
  update_service: 'Modifier des services',
  delete_service: 'Supprimer des services',

  // === ACCÈS AUX INTERFACES DE SERVICES ===
  'access_restaurant_interface': 'Accéder à l\'interface Restaurant',
  'access_bar_interface': 'Accéder à l\'interface Bar',
  'access_piscine_interface': 'Accéder à l\'interface Piscine',
  'operate_restaurant': 'Utiliser les fonctionnalités Restaurant',
  'operate_bar': 'Utiliser les fonctionnalités Bar',
  'operate_piscine': 'Utiliser les fonctionnalités Piscine',
  'manage_restaurant_menu': 'Gérer le menu du restaurant',
  'manage_bar_menu': 'Gérer la carte du bar',
  'manage_piscine_access': 'Gérer l\'accès à la piscine',
  'view_restaurant_orders': 'Voir les commandes restaurant',
  'view_bar_orders': 'Voir les commandes bar',
  'view_piscine_reservations': 'Voir les réservations piscine',

  // === GESTION DES POS ===
  'manage_pos': 'Gérer tous les points de vente',
  'view_pos': 'Consulter les points de vente',
  'create_pos': 'Créer des points de vente',
  'update_pos': 'Modifier des points de vente',
  'delete_pos': 'Supprimer des points de vente',
  'operate_pos': 'Utiliser un point de vente',

  // === GESTION DES SESSIONS CAISSE ===
  'manage_sessions': 'Gérer toutes les sessions de caisse',
  'view_sessions': 'Consulter les sessions de caisse',
  'open_session': 'Ouvrir des sessions de caisse',
  'close_session': 'Fermer des sessions de caisse',
  'view_session_history': 'Voir l\'historique des sessions',

  // === GESTION DES TRANSACTIONS ===
  'manage_transactions': 'Gérer toutes les transactions',
  'view_transactions': 'Consulter les transactions',
  'create_transaction': 'Créer des transactions',
  'update_transaction': 'Modifier des transactions',
  'cancel_transaction': 'Annuler des transactions',

  // === GESTION DES TICKETS ===
  'manage_tickets': 'Gérer tous les tickets',
  'view_tickets': 'Consulter les tickets',
  'generate_tickets': 'Générer des tickets',
  'validate_tickets': 'Valider des tickets',
  'print_tickets': 'Imprimer des tickets',
  'configure_tickets': 'Configurer le système de tickets',

  // === RAPPORTS ET STATISTIQUES ===
  'view_reports': 'Consulter les rapports',
  'generate_reports': 'Générer des rapports',
  'view_statistics': 'Consulter les statistiques',
  'view_financial_reports': 'Voir les rapports financiers',
  'view_occupancy_reports': 'Voir les rapports d\'occupation',
  'export_reports': 'Exporter des rapports',

  // === NOTIFICATIONS ===
  'manage_notifications': 'Gérer toutes les notifications',
  'view_notifications': 'Consulter les notifications',
  'create_notification': 'Créer des notifications',
  'mark_notifications_read': 'Marquer les notifications comme lues',

  // === TRAÇABILITÉ ET AUDIT ===
  'view_audit_logs': 'Consulter les logs d\'audit',
  'view_trace_history': 'Voir l\'historique de traçabilité',
  'generate_audit_reports': 'Générer des rapports d\'audit',

  // === GESTION DES COMPLEXES (pour admin_chaine) ===
  'manage_complexes': 'Gérer tous les complexes',
  'view_complexes': 'Consulter les complexes',
  'create_complex': 'Créer des complexes',
  'update_complex': 'Modifier des complexes',
  'delete_complex': 'Supprimer des complexes',

  // === GESTION DES ADMINS COMPLEXE (pour admin_chaine) ===
  'manage_complex_admins': 'Gérer les administrateurs de complexe',
  'view_complex_admins': 'Consulter les administrateurs de complexe',
  'create_complex_admin': 'Créer des administrateurs de complexe',
  'update_complex_admin': 'Modifier des administrateurs de complexe',
  'delete_complex_admin': 'Supprimer des administrateurs de complexe',

  // === GESTION DES PRODUITS/INVENTAIRE ===
  'manage_products': 'Gérer tous les produits',
  'view_products': 'Consulter les produits',
  'create_product': 'Créer des produits',
  'update_product': 'Modifier des produits',
  'delete_product': 'Supprimer des produits',
  'manage_inventory': 'Gérer l\'inventaire',
  'view_inventory': 'Consulter l\'inventaire',

  // === GESTION DES TABLES ===
  'view_tables': 'Consulter les tables',
  'manage_tables': 'Gérer les tables',
  'manage_table_reservations': 'Gérer les réservations de tables',
  'view_table_reservations': 'Consulter les réservations de tables',

  // === GESTION DES MENUS ===
  'view_menu': 'Consulter les menus',
  'manage_menu': 'Gérer les menus',
  'manage_menu_prices': 'Gérer les prix des menus',

  // === GESTION DES COMMANDES ===
  'view_orders': 'Consulter les commandes',
  'manage_orders': 'Gérer les commandes',
  'operate_restaurant_pos': 'Utiliser le POS restaurant',
  'operate_bar_pos': 'Utiliser le POS bar',
  'view_kitchen_orders': 'Voir les commandes cuisine'
};

// Définition des permissions par rôle
const ROLE_PERMISSIONS = {
  super_admin: {
    permissions: ['*'], // Toutes les permissions
    description: 'Accès total au système'
  },
  admin_chaine: {
    permissions: [
      // Gestion des complexes et admins
      'manage_complexes', 'view_complexes', 'create_complex', 'update_complex', 'delete_complex',
      'manage_complex_admins', 'view_complex_admins', 'create_complex_admin', 'update_complex_admin', 'delete_complex_admin',

      // Gestion des employés et rôles
      'manage_employees', 'view_employees', 'create_employee', 'update_employee', 'delete_employee', 'manage_employee_passwords',
      'manage_roles', 'view_roles', 'create_role', 'update_role', 'delete_role', 'assign_roles',

      // Gestion opérationnelle
      'manage_reservations', 'view_reservations', 'create_reservation', 'update_reservation', 'confirm_reservation', 'reject_reservation',
      'manage_clients', 'view_clients', 'create_client', 'update_client', 'view_client_history',
      'manage_payments', 'view_payments', 'create_payment', 'update_payment', 'process_payment', 'refund_payment',
      'manage_rooms', 'view_rooms', 'create_room', 'update_room', 'delete_room', 'manage_room_availability',
      'manage_services', 'view_services', 'create_service', 'update_service', 'delete_service',
      'manage_products', 'view_products', 'create_product', 'update_product', 'delete_product', 'manage_inventory',

      // Gestion POS Restaurant/Bar
      'view_tables', 'manage_tables', 'manage_table_reservations', 'view_table_reservations',
      'view_menu', 'manage_menu', 'manage_menu_prices',
      'view_orders', 'manage_orders', 'operate_restaurant_pos', 'operate_bar_pos', 'view_kitchen_orders',

      // Rapports et statistiques
      'view_reports', 'generate_reports', 'view_statistics', 'view_financial_reports', 'view_occupancy_reports', 'export_reports',
      'view_audit_logs', 'view_trace_history', 'generate_audit_reports'
    ],
    description: 'Gestionnaire de chaîne hôtelière'
  },
  admin_complexe: {
    permissions: [
      // Gestion des employés et rôles
      'manage_employees', 'view_employees', 'create_employee', 'update_employee', 'delete_employee', 'manage_employee_passwords',
      'manage_roles', 'view_roles', 'create_role', 'update_role', 'delete_role', 'assign_roles',

      // Gestion opérationnelle
      'manage_reservations', 'view_reservations', 'create_reservation', 'update_reservation', 'confirm_reservation', 'reject_reservation',
      'manage_clients', 'view_clients', 'create_client', 'update_client', 'view_client_history',
      'manage_payments', 'view_payments', 'create_payment', 'update_payment', 'process_payment', 'refund_payment',
      'manage_rooms', 'view_rooms', 'create_room', 'update_room', 'delete_room', 'manage_room_availability',
      'manage_services', 'view_services', 'create_service', 'update_service', 'delete_service',
      'manage_products', 'view_products', 'create_product', 'update_product', 'delete_product', 'manage_inventory',
      'manage_pos', 'view_pos', 'create_pos', 'update_pos', 'delete_pos',

      // Gestion POS Restaurant/Bar
      'view_tables', 'manage_tables', 'manage_table_reservations', 'view_table_reservations',
      'view_menu', 'manage_menu', 'manage_menu_prices',
      'view_orders', 'manage_orders', 'operate_restaurant_pos', 'operate_bar_pos', 'view_kitchen_orders',
      'manage_sessions', 'view_sessions', 'open_session', 'close_session', 'view_session_history',
      'manage_transactions', 'view_transactions', 'create_transaction', 'update_transaction',
      'manage_tickets', 'view_tickets', 'generate_tickets', 'validate_tickets', 'print_tickets', 'configure_tickets',

      // Rapports et statistiques
      'view_reports', 'generate_reports', 'view_statistics', 'view_financial_reports', 'view_occupancy_reports', 'export_reports',
      'view_audit_logs', 'view_trace_history',

      // Notifications
      'manage_notifications', 'view_notifications', 'create_notification', 'mark_notifications_read'
    ],
    description: 'Gestionnaire de complexe'
  },
  employe: {
    permissions: [], // Les permissions seront définies dans la base de données via les rôles personnalisés
    description: 'Employé'
  }
};

class PermissionService {
  /**
   * Obtenir les permissions d'un utilisateur selon le nouveau système simplifié
   * Phase 2 : Simplification des permissions
   */
  static async getUserPermissions(userId, userType, complexeId = null, employeeType = null) {
    try {
      let permissions = [];

      // Système simplifié selon le type d'utilisateur
      if (USER_TYPE_PERMISSIONS[userType]) {
        if (userType === 'employe') {
          // Pour les employés, utiliser le type d'employé
          if (employeeType && USER_TYPE_PERMISSIONS.employe[employeeType]) {
            permissions = [...USER_TYPE_PERMISSIONS.employe[employeeType]];
          } else {
            // Fallback : récupérer depuis la base de données si type non spécifié
            const employeeData = await this.getEmployeeTypeFromDB(userId, complexeId);
            if (employeeData && employeeData.type_employe && USER_TYPE_PERMISSIONS.employe[employeeData.type_employe]) {
              permissions = [...USER_TYPE_PERMISSIONS.employe[employeeData.type_employe]];
            } else {
              // Fallback vers l'ancien système pour rétrocompatibilité
              permissions = await this.getLegacyEmployeePermissions(userId, complexeId);
            }
          }
        } else {
          // Pour les admins, permissions directes
          permissions = [...USER_TYPE_PERMISSIONS[userType]];
        }
      }

      return permissions;
    } catch (error) {
      logger.error('Error getting user permissions:', error);
      throw error;
    }
  }

  /**
   * Récupérer le type d'employé depuis la base de données
   */
  static async getEmployeeTypeFromDB(userId, complexeId) {
    try {
      const query = `
        SELECT type_employe, services_autorises
        FROM "Employes"
        WHERE employe_id = $1 AND complexe_id = $2 AND actif = true
      `;
      const result = await db.query(query, [userId, complexeId]);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error getting employee type from DB:', error);
      return null;
    }
  }

  /**
   * Fallback vers l'ancien système pour rétrocompatibilité
   */
  static async getLegacyEmployeePermissions(userId, complexeId) {
    try {
      const query = `
        SELECT r.permissions
        FROM "RolesComplexe" r
        JOIN "Employes" e ON e.role_id = r.role_id
        WHERE e.employe_id = $1 AND e.complexe_id = $2
      `;
      const result = await db.query(query, [userId, complexeId]);

      if (result.rows[0] && result.rows[0].permissions) {
        const permissions = Array.isArray(result.rows[0].permissions)
          ? result.rows[0].permissions
          : JSON.parse(result.rows[0].permissions);
        return permissions;
      }

      return [];
    } catch (error) {
      logger.error('Error getting legacy employee permissions:', error);
      return [];
    }
  }

  /**
   * Vérifier si un utilisateur a une permission spécifique
   * Phase 2 : Système simplifié avec mapping intelligent
   */
  static async hasPermission(userId, userType, permission, complexeId = null, employeeType = null) {
    try {
      // Super admin a toutes les permissions
      if (userType === 'super_admin') {
        return true;
      }

      // Admin chaîne et admin complexe ont accès complet à leur périmètre
      if (userType === 'admin_chaine' || userType === 'admin_complexe') {
        return this.isAdminPermission(permission);
      }

      // Pour les employés, vérifier selon le nouveau système
      if (userType === 'employe') {
        return await this.checkEmployeePermission(userId, permission, complexeId, employeeType);
      }

      return false;
    } catch (error) {
      logger.error('Error checking permission:', error);
      return false;
    }
  }

  /**
   * Vérifier si une permission est accessible aux admins
   */
  static isAdminPermission(permission) {
    // Les admins ont accès à tout sauf aux permissions opérationnelles spécifiques
    const employeeOnlyPermissions = [
      'reception_operations',
      'piscine_operations',
      'service_operations',
      'management_operations',
      'kitchen_operations'
    ];

    // Si c'est une permission employé spécifique, les admins n'en ont pas besoin
    // (ils ont accès via leurs permissions admin)
    return !employeeOnlyPermissions.includes(permission);
  }

  /**
   * Vérifier les permissions d'un employé selon le nouveau système
   */
  static async checkEmployeePermission(userId, permission, complexeId, employeeType = null) {
    try {
      // Récupérer le type d'employé si non fourni
      if (!employeeType) {
        const employeeData = await this.getEmployeeTypeFromDB(userId, complexeId);
        employeeType = employeeData && employeeData.type_employe;
      }

      // Vérifier selon le nouveau système
      if (employeeType && USER_TYPE_PERMISSIONS.employe[employeeType]) {
        const employeePermissions = USER_TYPE_PERMISSIONS.employe[employeeType];
        return employeePermissions.includes(permission);
      }

      // Fallback vers l'ancien système pour rétrocompatibilité
      return await this.checkLegacyEmployeePermission(userId, permission, complexeId);
    } catch (error) {
      logger.error('Error checking employee permission:', error);
      return false;
    }
  }

  /**
   * Vérification legacy pour rétrocompatibilité
   */
  static async checkLegacyEmployeePermission(userId, permission, complexeId) {
    try {
      const permissions = await this.getLegacyEmployeePermissions(userId, complexeId);
      return permissions.includes(permission) || permissions.includes('*');
    } catch (error) {
      logger.error('Error checking legacy employee permission:', error);
      return false;
    }
  }

  static async getRolePermissions(roleId) {
    try {
      const query = 'SELECT permissions FROM "RolesComplexe" WHERE role_id = $1';
      const result = await db.query(query, [roleId]);

      if (result.rows.length === 0) {
        return [];
      }

      const permissions = result.rows[0].permissions;
      // Parser les permissions si elles sont en format string JSON
      if (typeof permissions === 'string') {
        return JSON.parse(permissions);
      }

      return permissions || [];
    } catch (error) {
      logger.error('Error getting role permissions:', error);
      throw error;
    }
  }

  static async updateRolePermissions(roleId, permissions) {
    try {
      const query = `
        UPDATE "RolesComplexe"
        SET permissions = $1
        WHERE role_id = $2
        RETURNING *
      `;
      const result = await db.query(query, [permissions, roleId]);
      return result.rows[0];
    } catch (error) {
      logger.error('Error updating role permissions:', error);
      throw error;
    }
  }

  /**
   * Obtenir toutes les permissions disponibles (nouveau système simplifié)
   */
  static getAllAvailablePermissions() {
    return {
      simplified: SIMPLIFIED_PERMISSIONS,
      legacy: LEGACY_PERMISSIONS
    };
  }

  /**
   * Obtenir les permissions simplifiées uniquement
   */
  static getSimplifiedPermissions() {
    return SIMPLIFIED_PERMISSIONS;
  }

  /**
   * Obtenir les clés des permissions simplifiées
   */
  static getAvailablePermissions() {
    return Object.keys(SIMPLIFIED_PERMISSIONS);
  }

  /**
   * Obtenir les permissions par type d'utilisateur
   */
  static getPermissionsByUserType() {
    return USER_TYPE_PERMISSIONS;
  }

  /**
   * Obtenir les permissions pour un type d'employé spécifique
   */
  static getPermissionsForEmployeeType(employeeType) {
    if (!EmployeeTypeService.isValidType(employeeType)) {
      throw new Error(`Type d'employé invalide: ${employeeType}`);
    }
    return USER_TYPE_PERMISSIONS.employe[employeeType] || [];
  }

  /**
   * Mapper une ancienne permission vers le nouveau système
   */
  static mapLegacyPermissionToSimplified(legacyPermission) {
    // Mapping des anciennes permissions vers les nouvelles
    const mappingTable = {
      // Permissions réception
      'manage_reservations': 'reception_operations',
      'view_reservations': 'reception_operations',
      'create_reservation': 'reception_operations',
      'update_reservation': 'reception_operations',
      'confirm_reservation': 'reception_operations',
      'manage_clients': 'reception_operations',
      'view_clients': 'reception_operations',
      'create_client': 'reception_operations',
      'manage_rooms': 'reception_operations',
      'view_rooms': 'reception_operations',
      'manage_room_availability': 'reception_operations',

      // Permissions piscine
      'access_piscine_interface': 'piscine_operations',
      'operate_piscine': 'piscine_operations',
      'manage_piscine_access': 'piscine_operations',
      'view_piscine_reservations': 'piscine_operations',

      // Permissions service (bar/restaurant)
      'access_restaurant_interface': 'service_operations',
      'access_bar_interface': 'service_operations',
      'operate_restaurant': 'service_operations',
      'operate_bar': 'service_operations',
      'view_restaurant_orders': 'service_operations',
      'view_bar_orders': 'service_operations',
      'view_orders': 'service_operations',
      'view_menu': 'service_operations',
      'view_tables': 'service_operations',

      // Permissions gestion (gérant services)
      'manage_orders': 'management_operations',
      'process_payment': 'management_operations',
      'manage_tables': 'management_operations',
      'operate_restaurant_pos': 'management_operations',
      'operate_bar_pos': 'management_operations',

      // Permissions cuisine
      'view_kitchen_orders': 'kitchen_operations'
    };

    return mappingTable[legacyPermission] || null;
  }

  /**
   * Vérifier si une permission legacy est supportée dans le nouveau système
   */
  static isLegacyPermissionSupported(legacyPermission) {
    return this.mapLegacyPermissionToSimplified(legacyPermission) !== null;
  }
}

module.exports = PermissionService;