const Service = require('./Service');
const db = require('../db');
const logger = require('../logger');

class ClientService  {
  // Création d'un client
  static async createClient(params) {
    const { 
      nom, 
      prenom, 
      email, 
      telephone, 
      adresse, 
      type_piece, 
      numero_piece,
      date_naissance,
      nationalite,
      utilisateur_id 
    } = params;

    try {
      const client = await db.query('BEGIN');

      // Vérifier si le client existe déjà
      const checkQuery = `
        SELECT client_id 
        FROM Clients 
        WHERE email = $1 OR numero_piece = $2
      `;
      
      const checkResult = await db.query(checkQuery, [email, numero_piece]);
      
      if (checkResult.rows.length > 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Un client avec cet email ou ce numéro de pièce existe déjà', 409);
      }

      // Créer le client
      const createQuery = `
        INSERT INTO Clients (
          nom,
          prenom,
          email,
          telephone,
          adresse,
          type_piece,
          numero_piece,
          date_naissance,
          nationalite,
          utilisateur_id,
          created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, CURRENT_TIMESTAMP)
        RETURNING *
      `;

      const createResult = await db.query(createQuery, [
        nom,
        prenom,
        email,
        telephone,
        adresse,
        type_piece,
        numero_piece,
        date_naissance,
        nationalite,
        utilisateur_id
      ]);

      await db.query('COMMIT');
      logger.info('Client créé', { 
        client_id: createResult.rows[0].client_id,
        email 
      });

      return this.successResponse(createResult.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur création client:', error);
      return this.rejectResponse('Erreur lors de la création du client');
    }
  }

  // Récupération des détails d'un client
  static async getClientById(clientId) {
    try {
      const query = `
        SELECT 
          c.*,
          COALESCE(r.nombre_reservations, 0) as nombre_reservations,
          COALESCE(r.derniere_reservation, NULL) as derniere_reservation,
          COALESCE(p.montant_total, 0) as montant_total_paiements,
          COALESCE(p.nombre_paiements, 0) as nombre_paiements
        FROM Clients c
        LEFT JOIN (
          SELECT 
            client_id,
            COUNT(*) as nombre_reservations,
            MAX(created_at) as derniere_reservation
          FROM Reservations
          GROUP BY client_id
        ) r ON c.client_id = r.client_id
        LEFT JOIN (
          SELECT 
            client_id,
            COUNT(*) as nombre_paiements,
            SUM(montant) as montant_total
          FROM Paiements
          GROUP BY client_id
        ) p ON c.client_id = p.client_id
        WHERE c.client_id = $1
      `;

      const result = await db.query(query, [clientId]);

      if (result.rows.length === 0) {
        return this.rejectResponse('Client non trouvé', 404);
      }

      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur récupération détails client:', error);
      return this.rejectResponse('Erreur lors de la récupération des détails du client');
    }
  }

  // Mise à jour d'un client
  static async updateClient(clientId, params) {
    const { 
      nom, 
      prenom, 
      email, 
      telephone, 
      adresse, 
      type_piece, 
      numero_piece,
      date_naissance,
      nationalite,
      utilisateur_id 
    } = params;

    try {
      const client = await db.query('BEGIN');

      // Vérifier si le client existe
      const checkQuery = `
        SELECT * 
        FROM Clients 
        WHERE client_id = $1 
        FOR UPDATE
      `;
      
      const checkResult = await db.query(checkQuery, [clientId]);
      
      if (checkResult.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Client non trouvé', 404);
      }

      // Vérifier les doublons si email ou numéro de pièce changent
      if (email || numero_piece) {
        const checkDuplicateQuery = `
          SELECT client_id 
          FROM Clients 
          WHERE client_id != $1
          AND (email = $2 OR numero_piece = $3)
        `;
        
        const checkDuplicateResult = await db.query(checkDuplicateQuery, [
          clientId,
          email || checkResult.rows[0].email,
          numero_piece || checkResult.rows[0].numero_piece
        ]);
        
        if (checkDuplicateResult.rows.length > 0) {
          await db.query('ROLLBACK');
          return this.rejectResponse('Un client avec cet email ou ce numéro de pièce existe déjà', 409);
        }
      }

      // Mettre à jour le client
      const updateQuery = `
        UPDATE Clients 
        SET 
          nom = COALESCE($1, nom),
          prenom = COALESCE($2, prenom),
          email = COALESCE($3, email),
          telephone = COALESCE($4, telephone),
          adresse = COALESCE($5, adresse),
          type_piece = COALESCE($6, type_piece),
          numero_piece = COALESCE($7, numero_piece),
          date_naissance = COALESCE($8, date_naissance),
          nationalite = COALESCE($9, nationalite),
          utilisateur_id = COALESCE($10, utilisateur_id),
          updated_at = CURRENT_TIMESTAMP
        WHERE client_id = $11
        RETURNING *
      `;

      const updateResult = await db.query(updateQuery, [
        nom,
        prenom,
        email,
        telephone,
        adresse,
        type_piece,
        numero_piece,
        date_naissance,
        nationalite,
        utilisateur_id,
        clientId
      ]);

      await db.query('COMMIT');
      logger.info('Client mis à jour', { 
        client_id: clientId,
        email: updateResult.rows[0].email 
      });

      return this.successResponse(updateResult.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur mise à jour client:', error);
      return this.rejectResponse('Erreur lors de la mise à jour du client');
    }
  }

  // Récupération de l'historique des réservations d'un client
  static async getClientReservations(clientId, params) {
    const { statut, date_debut, date_fin, tri = 'created_at' } = params;

    try {
      let query = `
        WITH ReservationsDetails AS (
          SELECT 
            r.*,
            COUNT(cr.chambre_id) as nombre_chambres,
            STRING_AGG(c.numero, ', ') as numeros_chambres,
            STRING_AGG(c.type_chambre, ', ') as types_chambres,
            COALESCE(p.montant_total, 0) as montant_total_paiements,
            COALESCE(p.nombre_paiements, 0) as nombre_paiements
          FROM Reservations r
          LEFT JOIN ChambresReservees cr ON r.reservation_id = cr.reservation_id
          LEFT JOIN Chambres c ON cr.chambre_id = c.chambre_id
          LEFT JOIN (
            SELECT 
              reservation_id,
              COUNT(*) as nombre_paiements,
              SUM(montant) as montant_total
            FROM Paiements
            GROUP BY reservation_id
          ) p ON r.reservation_id = p.reservation_id
          WHERE r.client_id = $1
      `;

      const values = [clientId];
      let paramIndex = 2;

      if (statut) {
        query += ` AND r.statut = $${paramIndex}`;
        values.push(statut);
        paramIndex++;
      }

      if (date_debut) {
        query += ` AND r.date_arrivee >= $${paramIndex}`;
        values.push(date_debut);
        paramIndex++;
      }

      if (date_fin) {
        query += ` AND r.date_depart <= $${paramIndex}`;
        values.push(date_fin);
        paramIndex++;
      }

      query += `
          GROUP BY r.reservation_id, p.montant_total, p.nombre_paiements
        )
        SELECT 
          *,
          date_depart - date_arrivee as duree_sejour
        FROM ReservationsDetails
        ORDER BY 
          CASE 
            WHEN $${paramIndex} = 'created_at' THEN created_at
            WHEN $${paramIndex} = 'date_arrivee' THEN date_arrivee
            WHEN $${paramIndex} = 'date_depart' THEN date_depart
            ELSE created_at
          END DESC
      `;

      values.push(tri);

      const result = await db.query(query, values);

      return this.successResponse({
        reservations: result.rows,
        total: result.rows.length,
        client_id: clientId
      });
    } catch (error) {
      logger.error('Erreur récupération historique réservations:', error);
      return this.rejectResponse('Erreur lors de la récupération de l\'historique des réservations');
    }
  }
}

module.exports = ClientService; 