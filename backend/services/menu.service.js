const db = require('../db');
const logger = require('../logger');
const BaseService = require('./base.service');
const POSStockIntegration = require('./posStockIntegration.service');

/**
 * Service de gestion des menus pour les restaurants et bars
 */
class MenuService extends BaseService {

  /**
   * Récupérer le menu complet d'un service avec catégories
   */
  static async getMenuByService(serviceId) {
    try {
      const query = `
        SELECT 
          p.*,
          cp.nom as categorie_nom,
          cp.description as categorie_description,
          cp.ordre_affichage as categorie_ordre,
          s.nom as service_nom,
          s.type_service
        FROM "Produits" p
        LEFT JOIN "CategoriesProduits" cp ON p.categorie_id = cp.categorie_id
        LEFT JOIN "ServicesComplexe" s ON p.service_id = s.service_id
        WHERE (p.service_id = $1 OR p.niveau = 'Chaîne')
        AND p.actif = true
        AND cp.actif = true
        ORDER BY cp.ordre_affichage, p.nom
      `;
      
      const result = await db.query(query, [serviceId]);
      
      // Organiser les produits par catégorie
      const categories = {};
      result.rows.forEach(produit => {
        const categorieId = produit.categorie_id;
        if (!categories[categorieId]) {
          categories[categorieId] = {
            categorie_id: categorieId,
            nom: produit.categorie_nom,
            description: produit.categorie_description,
            ordre_affichage: produit.categorie_ordre,
            produits: []
          };
        }
        categories[categorieId].produits.push({
          produit_id: produit.produit_id,
          nom: produit.nom,
          description: produit.description,
          prix: produit.prix,
          image_url: produit.image_url,
          allergenes: produit.allergenes,
          temps_preparation: produit.temps_preparation,
          disponible: produit.disponible,
          stock_actuel: produit.stock_actuel
        });
      });

      return this.successResponse(Object.values(categories));
    } catch (error) {
      logger.error('Erreur récupération menu par service:', error);
      return this.rejectResponse('Erreur lors de la récupération du menu');
    }
  }

  /**
   * Récupérer les produits d'une catégorie spécifique
   */
  static async getProductsByCategory(serviceId, categorieId) {
    try {
      const query = `
        SELECT 
          p.*,
          cp.nom as categorie_nom
        FROM "Produits" p
        LEFT JOIN "CategoriesProduits" cp ON p.categorie_id = cp.categorie_id
        WHERE p.categorie_id = $1
        AND (p.service_id = $2 OR p.niveau = 'Chaîne')
        AND p.actif = true
        ORDER BY p.nom
      `;
      
      const result = await db.query(query, [categorieId, serviceId]);
      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération produits par catégorie:', error);
      return this.rejectResponse('Erreur lors de la récupération des produits');
    }
  }

  /**
   * Mettre à jour le prix d'un produit pour un service spécifique
   */
  static async updateProductPrice(produitId, serviceId, prix) {
    try {
      // Vérifier que le produit existe
      const produitCheck = await db.query(
        'SELECT * FROM "Produits" WHERE produit_id = $1',
        [produitId]
      );

      if (produitCheck.rows.length === 0) {
        return this.rejectResponse('Produit non trouvé', 404);
      }

      const query = `
        UPDATE "Produits" 
        SET prix = $1, updated_at = NOW()
        WHERE produit_id = $2
        AND (service_id = $3 OR niveau = 'Chaîne')
        RETURNING *
      `;

      const result = await db.query(query, [prix, produitId, serviceId]);

      if (result.rows.length === 0) {
        return this.rejectResponse('Impossible de modifier le prix pour ce service', 403);
      }

      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur mise à jour prix produit:', error);
      return this.rejectResponse('Erreur lors de la mise à jour du prix');
    }
  }

  /**
   * Vérifier la disponibilité d'un produit en fonction du stock
   */
  static async checkProductAvailability(produitId, quantite) {
    try {
      // Utiliser le service d'intégration stock pour vérifier la disponibilité
      const stockCheck = await POSStockIntegration.checkIngredientAvailability(produitId, quantite);
      
      if (!stockCheck.success) {
        return this.rejectResponse('Erreur lors de la vérification du stock');
      }

      return this.successResponse({
        produit_id: produitId,
        quantite_demandee: quantite,
        disponible: stockCheck.data.disponible,
        stock_suffisant: stockCheck.data.stock_suffisant,
        ingredients_manquants: stockCheck.data.ingredients_manquants || []
      });
    } catch (error) {
      logger.error('Erreur vérification disponibilité produit:', error);
      return this.rejectResponse('Erreur lors de la vérification de la disponibilité');
    }
  }

  /**
   * Récupérer le menu avec informations de stock en temps réel
   */
  static async getMenuWithStock(serviceId) {
    try {
      // Récupérer le menu de base
      const menuResult = await this.getMenuByService(serviceId);
      
      if (!menuResult.success) {
        return menuResult;
      }

      const categories = menuResult.data;

      // Enrichir chaque produit avec les informations de stock
      for (const categorie of categories) {
        for (const produit of categorie.produits) {
          try {
            const stockInfo = await POSStockIntegration.checkIngredientAvailability(produit.produit_id, 1);
            
            produit.stock_info = {
              disponible: stockInfo.success ? stockInfo.data.disponible : false,
              stock_suffisant: stockInfo.success ? stockInfo.data.stock_suffisant : false,
              ingredients_manquants: stockInfo.success ? stockInfo.data.ingredients_manquants : [],
              derniere_verification: new Date()
            };
          } catch (error) {
            logger.warn(`Erreur vérification stock pour produit ${produit.produit_id}:`, error);
            produit.stock_info = {
              disponible: false,
              stock_suffisant: false,
              ingredients_manquants: [],
              erreur: 'Impossible de vérifier le stock'
            };
          }
        }
      }

      return this.successResponse(categories);
    } catch (error) {
      logger.error('Erreur récupération menu avec stock:', error);
      return this.rejectResponse('Erreur lors de la récupération du menu avec stock');
    }
  }

  /**
   * Mise à jour en lot des prix pour un service
   */
  static async bulkUpdatePrices(serviceId, pricesData) {
    try {
      await db.query('BEGIN');

      const results = [];

      for (const priceUpdate of pricesData) {
        const { produit_id, nouveau_prix } = priceUpdate;

        const updateResult = await db.query(`
          UPDATE "Produits" 
          SET prix = $1, updated_at = NOW()
          WHERE produit_id = $2
          AND (service_id = $3 OR niveau = 'Chaîne')
          RETURNING produit_id, nom, prix
        `, [nouveau_prix, produit_id, serviceId]);

        if (updateResult.rows.length > 0) {
          results.push({
            produit_id,
            nom: updateResult.rows[0].nom,
            ancien_prix: priceUpdate.ancien_prix,
            nouveau_prix: updateResult.rows[0].prix,
            success: true
          });
        } else {
          results.push({
            produit_id,
            success: false,
            erreur: 'Produit non trouvé ou non modifiable'
          });
        }
      }

      await db.query('COMMIT');

      return this.successResponse({
        total_updates: pricesData.length,
        successful_updates: results.filter(r => r.success).length,
        failed_updates: results.filter(r => !r.success).length,
        details: results
      });
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur mise à jour en lot des prix:', error);
      return this.rejectResponse('Erreur lors de la mise à jour des prix');
    }
  }

  /**
   * Rechercher des produits dans le menu
   */
  static async searchProducts(serviceId, searchTerm) {
    try {
      const query = `
        SELECT 
          p.*,
          cp.nom as categorie_nom
        FROM "Produits" p
        LEFT JOIN "CategoriesProduits" cp ON p.categorie_id = cp.categorie_id
        WHERE (p.service_id = $1 OR p.niveau = 'Chaîne')
        AND p.actif = true
        AND (
          LOWER(p.nom) LIKE LOWER($2) OR
          LOWER(p.description) LIKE LOWER($2) OR
          LOWER(cp.nom) LIKE LOWER($2)
        )
        ORDER BY p.nom
      `;
      
      const searchPattern = `%${searchTerm}%`;
      const result = await db.query(query, [serviceId, searchPattern]);
      
      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur recherche produits:', error);
      return this.rejectResponse('Erreur lors de la recherche de produits');
    }
  }
}

module.exports = MenuService;
