const Service = require('./Service');
const db = require('../db');
const logger = require('../logger');

class RapportService extends Service {
  // Rapport des paiements par période
  static async getRapportPaiements(params) {
    const { date_debut, date_fin, complexe_id, mode_paiement, type_utilisateur, statut } = params;

    try {
      let query = `
        SELECT
          pr.paiement_reservation_id,
          pr.montant,
          pr.mode_paiement,
          pr.date_paiement,
          pr.reference_paiement,
          pr.statut,
          pr.notes,
          pr.utilisateur_id,
          pr.type_utilisateur,
          r.numero_reservation,
          r.statut as statut_reservation,
          r.montant_total as montant_reservation,
          c.nom as client_nom,
          c.prenom as client_prenom,
          c.telephone as client_telephone,
          c.email as client_email,
          ch.nom as complexe_nom
        FROM "PaiementsReservations" pr
        JOIN "Reservations" r ON pr.reservation_id = r.reservation_id
        JOIN "Clients" c ON r.client_id = c.client_id
        JOIN "ComplexesHoteliers" ch ON r.complexe_id = ch.complexe_id
        WHERE pr.date_paiement BETWEEN $1 AND $2
      `;

      const queryParams = [date_debut, date_fin];
      let paramIndex = 3;

      if (complexe_id) {
        query += ` AND r.complexe_id = $${paramIndex}`;
        queryParams.push(complexe_id);
        paramIndex++;
      }

      if (mode_paiement) {
        query += ` AND pr.mode_paiement = $${paramIndex}`;
        queryParams.push(mode_paiement);
        paramIndex++;
      }

      if (type_utilisateur) {
        query += ` AND pr.type_utilisateur = $${paramIndex}`;
        queryParams.push(type_utilisateur);
        paramIndex++;
      }

      if (statut) {
        query += ` AND pr.statut = $${paramIndex}`;
        queryParams.push(statut);
        paramIndex++;
      }

      query += ` ORDER BY pr.date_paiement DESC`;

      const result = await db.query(query, queryParams);

      // Calculer les totaux
      const totaux = result.rows.reduce((acc, paiement) => {
        // Ne compter que les paiements validés
        if (paiement.statut === 'Validé') {
          acc.montant_total += parseFloat(paiement.montant);

          // Grouper par mode de paiement
          if (!acc.par_mode[paiement.mode_paiement]) {
            acc.par_mode[paiement.mode_paiement] = 0;
          }
          acc.par_mode[paiement.mode_paiement] += parseFloat(paiement.montant);

          // Grouper par type d'utilisateur
          if (!acc.par_type_utilisateur[paiement.type_utilisateur]) {
            acc.par_type_utilisateur[paiement.type_utilisateur] = 0;
          }
          acc.par_type_utilisateur[paiement.type_utilisateur] += parseFloat(paiement.montant);
        }

        return acc;
      }, {
        montant_total: 0,
        par_mode: {},
        par_type_utilisateur: {}
      });

      return this.successResponse({
        paiements: result.rows,
        totaux,
        nombre_paiements: result.rows.length,
        nombre_paiements_valides: result.rows.filter(p => p.statut === 'Validé').length
      });
    } catch (error) {
      logger.error('Erreur rapport paiements:', error);
      return this.rejectResponse('Erreur lors de la génération du rapport de paiements');
    }
  }

  // Rapport des revenus par période
  static async getRapportRevenus(params) {
    const { date_debut, date_fin, complexe_id, grouper_par } = params;

    try {
      let groupBy = '';
      let selectFields = '';

      switch (grouper_par) {
        case 'jour':
          groupBy = 'DATE(pr.date_paiement)';
          selectFields = 'DATE(pr.date_paiement) as periode';
          break;
        case 'semaine':
          groupBy = 'EXTRACT(YEAR FROM pr.date_paiement), EXTRACT(WEEK FROM pr.date_paiement)';
          selectFields = 'EXTRACT(YEAR FROM pr.date_paiement) as annee, EXTRACT(WEEK FROM pr.date_paiement) as semaine';
          break;
        case 'mois':
          groupBy = 'EXTRACT(YEAR FROM pr.date_paiement), EXTRACT(MONTH FROM pr.date_paiement)';
          selectFields = 'EXTRACT(YEAR FROM pr.date_paiement) as annee, EXTRACT(MONTH FROM pr.date_paiement) as mois';
          break;
        default:
          groupBy = 'DATE(pr.date_paiement)';
          selectFields = 'DATE(pr.date_paiement) as periode';
      }

      let query = `
        SELECT
          ${selectFields},
          SUM(pr.montant) as montant_total,
          COUNT(DISTINCT pr.paiement_reservation_id) as nombre_paiements,
          COUNT(DISTINCT r.reservation_id) as nombre_reservations,
          AVG(pr.montant) as montant_moyen,
          ch.nom as complexe_nom
        FROM "PaiementsReservations" pr
        JOIN "Reservations" r ON pr.reservation_id = r.reservation_id
        JOIN "ComplexesHoteliers" ch ON r.complexe_id = ch.complexe_id
        WHERE pr.date_paiement BETWEEN $1 AND $2
        AND pr.statut = 'Validé'
      `;

      const queryParams = [date_debut, date_fin];
      let paramIndex = 3;

      if (complexe_id) {
        query += ` AND r.complexe_id = $${paramIndex}`;
        queryParams.push(complexe_id);
        paramIndex++;
      }

      query += ` GROUP BY ${groupBy}`;

      // Ajouter le nom du complexe au GROUP BY si on ne filtre pas par complexe
      if (!complexe_id) {
        query += `, ch.nom`;
      }

      const orderBy = groupBy === 'DATE(pr.date_paiement)' ? 'periode' : 'annee, ' + (grouper_par === 'semaine' ? 'semaine' : 'mois');
      query += ` ORDER BY ${orderBy}`;

      const result = await db.query(query, queryParams);

      // Calculer le total général et les statistiques
      const stats = result.rows.reduce((acc, row) => {
        acc.montant_total += parseFloat(row.montant_total);
        acc.nombre_paiements += parseInt(row.nombre_paiements);
        acc.nombre_reservations += parseInt(row.nombre_reservations);
        return acc;
      }, {
        montant_total: 0,
        nombre_paiements: 0,
        nombre_reservations: 0
      });

      return this.successResponse({
        revenus: result.rows,
        statistiques: {
          total_general: stats.montant_total,
          total_paiements: stats.nombre_paiements,
          total_reservations: stats.nombre_reservations,
          montant_moyen_par_paiement: stats.nombre_paiements > 0 ? stats.montant_total / stats.nombre_paiements : 0,
          montant_moyen_par_reservation: stats.nombre_reservations > 0 ? stats.montant_total / stats.nombre_reservations : 0
        },
        periode: {
          date_debut,
          date_fin,
          grouper_par
        }
      });
    } catch (error) {
      logger.error('Erreur rapport revenus:', error);
      return this.rejectResponse('Erreur lors de la génération du rapport de revenus');
    }
  }
}

module.exports = RapportService;