const db = require('../db');
const logger = require('../logger');

class SessionCaisseService {
  static successResponse(data) {
    return {
      success: true,
      data
    };
  }

  static rejectResponse(message, code = 500) {
    return {
      success: false,
      message,
      code
    };
  }

  // Ouvrir une nouvelle session de caisse
  static async ouvrirSession(sessionData) {
    const {
      pos_id,
      employe_id,
      complexe_id,
      service_id,
      fonds_ouverture,
      notes
    } = sessionData;

    try {
      await db.query('BEGIN');

      // Vérifier qu'il n'y a pas déjà une session ouverte pour ce POS
      const sessionExistante = await db.query(
        'SELECT session_id FROM "SessionsCaisse" WHERE pos_id = $1 AND statut = \'Ouverte\'',
        [pos_id]
      );

      if (sessionExistante.rows.length > 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Une session est déjà ouverte pour ce point de vente', 400);
      }

      // Vérifier que le POS existe et est actif
      const posCheck = await db.query(
        'SELECT pos_id, statut FROM "PointsDeVente" WHERE pos_id = $1',
        [pos_id]
      );

      if (posCheck.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Point de vente non trouvé', 404);
      }

      if (posCheck.rows[0].statut !== 'Actif') {
        await db.query('ROLLBACK');
        return this.rejectResponse('Le point de vente n\'est pas actif', 400);
      }

      // Créer la nouvelle session
      const query = `
        INSERT INTO "SessionsCaisse" (
          pos_id,
          employe_id,
          complexe_id,
          service_id,
          fonds_ouverture,
          notes,
          date_ouverture
        ) VALUES ($1, $2, $3, $4, $5, $6, NOW())
        RETURNING *
      `;

      const result = await db.query(query, [
        pos_id,
        employe_id,
        complexe_id,
        service_id,
        fonds_ouverture,
        notes
      ]);

      // Mettre à jour le statut du POS
      await db.query(
        'UPDATE "PointsDeVente" SET caisse_ouverte = true, employe_actuel_id = $1 WHERE pos_id = $2',
        [employe_id, pos_id]
      );

      await db.query('COMMIT');
      return this.successResponse(result.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur ouverture session caisse:', error);
      return this.rejectResponse('Erreur lors de l\'ouverture de la session de caisse');
    }
  }

  // Fermer une session de caisse
  static async fermerSession(sessionId, fermetureData) {
    const {
      fonds_fermeture,
      total_ventes,
      total_especes,
      total_cartes,
      total_autres,
      notes
    } = fermetureData;

    try {
      await db.query('BEGIN');

      // Vérifier que la session existe et est ouverte
      const sessionCheck = await db.query(
        'SELECT * FROM "SessionsCaisse" WHERE session_id = $1 AND statut = \'Ouverte\'',
        [sessionId]
      );

      if (sessionCheck.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Session non trouvée ou déjà fermée', 404);
      }

      const session = sessionCheck.rows[0];

      // Mettre à jour la session
      const updateQuery = `
        UPDATE "SessionsCaisse" 
        SET 
          date_fermeture = NOW(),
          fonds_fermeture = $1,
          total_ventes = $2,
          total_especes = $3,
          total_cartes = $4,
          total_autres = $5,
          notes = COALESCE($6, notes),
          statut = 'Fermée'
        WHERE session_id = $7
        RETURNING *
      `;

      const result = await db.query(updateQuery, [
        fonds_fermeture,
        total_ventes,
        total_especes,
        total_cartes,
        total_autres,
        notes,
        sessionId
      ]);

      // Mettre à jour le statut du POS
      await db.query(
        'UPDATE "PointsDeVente" SET caisse_ouverte = false, employe_actuel_id = NULL WHERE pos_id = $1',
        [session.pos_id]
      );

      await db.query('COMMIT');
      return this.successResponse(result.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur fermeture session caisse:', error);
      return this.rejectResponse('Erreur lors de la fermeture de la session de caisse');
    }
  }

  // Récupérer les sessions d'un point de vente
  static async getSessionsByPOS(posId) {
    try {
      const query = `
        SELECT 
          s.*,
          e.nom as employe_nom,
          e.prenom as employe_prenom,
          p.nom as pos_nom
        FROM "SessionsCaisse" s
        LEFT JOIN "Employes" e ON s.employe_id = e.employe_id
        LEFT JOIN "PointsDeVente" p ON s.pos_id = p.pos_id
        WHERE s.pos_id = $1
        ORDER BY s.date_ouverture DESC
      `;

      const result = await db.query(query, [posId]);
      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération sessions POS:', error);
      return this.rejectResponse('Erreur lors de la récupération des sessions');
    }
  }

  // Récupérer la session active d'un point de vente
  static async getActiveSession(posId) {
    try {
      const query = `
        SELECT 
          s.*,
          e.nom as employe_nom,
          e.prenom as employe_prenom,
          p.nom as pos_nom
        FROM "SessionsCaisse" s
        LEFT JOIN "Employes" e ON s.employe_id = e.employe_id
        LEFT JOIN "PointsDeVente" p ON s.pos_id = p.pos_id
        WHERE s.pos_id = $1 AND s.statut = 'Ouverte'
      `;

      const result = await db.query(query, [posId]);
      
      if (result.rows.length === 0) {
        return this.rejectResponse('Aucune session active trouvée', 404);
      }

      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur récupération session active:', error);
      return this.rejectResponse('Erreur lors de la récupération de la session active');
    }
  }

  // Récupérer une session par ID
  static async getSessionById(sessionId) {
    try {
      const query = `
        SELECT 
          s.*,
          e.nom as employe_nom,
          e.prenom as employe_prenom,
          p.nom as pos_nom,
          srv.nom as service_nom
        FROM "SessionsCaisse" s
        LEFT JOIN "Employes" e ON s.employe_id = e.employe_id
        LEFT JOIN "PointsDeVente" p ON s.pos_id = p.pos_id
        LEFT JOIN "ServicesComplexe" srv ON s.service_id = srv.service_id
        WHERE s.session_id = $1
      `;

      const result = await db.query(query, [sessionId]);
      
      if (result.rows.length === 0) {
        return this.rejectResponse('Session non trouvée', 404);
      }

      return this.successResponse(result.rows[0]);
    } catch (error) {
      logger.error('Erreur récupération session:', error);
      return this.rejectResponse('Erreur lors de la récupération de la session');
    }
  }

  // Récupérer les sessions d'un complexe
  static async getSessionsByComplexe(complexeId, params = {}) {
    try {
      const { dateDebut, dateFin, statut, serviceId } = params;
      
      let whereConditions = ['s.complexe_id = $1'];
      let queryParams = [complexeId];
      let paramIndex = 2;

      if (dateDebut) {
        whereConditions.push(`s.date_ouverture >= $${paramIndex}`);
        queryParams.push(dateDebut);
        paramIndex++;
      }

      if (dateFin) {
        whereConditions.push(`s.date_ouverture <= $${paramIndex}`);
        queryParams.push(dateFin);
        paramIndex++;
      }

      if (statut) {
        whereConditions.push(`s.statut = $${paramIndex}`);
        queryParams.push(statut);
        paramIndex++;
      }

      if (serviceId) {
        whereConditions.push(`s.service_id = $${paramIndex}`);
        queryParams.push(serviceId);
        paramIndex++;
      }

      const query = `
        SELECT 
          s.*,
          e.nom as employe_nom,
          e.prenom as employe_prenom,
          p.nom as pos_nom,
          srv.nom as service_nom,
          srv.type_service
        FROM "SessionsCaisse" s
        LEFT JOIN "Employes" e ON s.employe_id = e.employe_id
        LEFT JOIN "PointsDeVente" p ON s.pos_id = p.pos_id
        LEFT JOIN "ServicesComplexe" srv ON s.service_id = srv.service_id
        WHERE ${whereConditions.join(' AND ')}
        ORDER BY s.date_ouverture DESC
      `;

      const result = await db.query(query, queryParams);
      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération sessions complexe:', error);
      return this.rejectResponse('Erreur lors de la récupération des sessions du complexe');
    }
  }
}

module.exports = SessionCaisseService;
