const db = require('../db');
const logger = require('../logger');
const BaseService = require('./base.service');

class InventaireService extends BaseService {

  /**
   * ==================== GESTION DES INGRÉDIENTS ====================
   */

  /**
   * Création d'un nouvel ingrédient
   */
  static async createIngredient(ingredientData) {
    const {
      chaineId, complexeId, nom, description, uniteMesure, categorie,
      codeBarre, prixUnitaireMoyen, fournisseurPrincipalId, allergenes,
      conservation, dureeConservationJours
    } = ingredientData;

    try {
      // Vérification des doublons
      await this.checkIngredientDuplicate(nom, complexeId, codeBarre);

      const result = await db.query(
        `INSERT INTO "Ingredients" (
          chaine_id, complexe_id, nom, description, unite_mesure, categorie,
          code_barre, prix_unitaire_moyen, fournisseur_principal_id, allergenes,
          conservation, duree_conservation_jours, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW())
        RETURNING *`,
        [
          chaineId, complexeId, nom, description, uniteMesure, categorie,
          codeBarre, prixUnitaireMoyen || 0, fournisseurPrincipalId,
          JSON.stringify(allergenes || []), conservation, dureeConservationJours
        ]
      );

      // Initialisation du stock à zéro
      await this.initializeIngredientStock(result.rows[0].ingredient_id, complexeId);

      logger.info(`Ingredient created: ${nom}`, { ingredientId: result.rows[0].ingredient_id });

      return this.successResponse(result.rows[0], 'Ingrédient créé avec succès');
    } catch (error) {
      logger.error('Error creating ingredient:', error);
      throw error;
    }
  }

  /**
   * Mise à jour d'un ingrédient
   */
  static async updateIngredient(ingredientId, data) {
    try {
      const {
        nom, description, uniteMesure, categorie, codeBarre,
        prixUnitaireMoyen, fournisseurPrincipalId, allergenes,
        conservation, dureeConservationJours, actif
      } = data;

      // Vérification de l'existence
      const existing = await this.getIngredientById(ingredientId);
      if (!existing) {
        this.rejectResponse('Ingrédient non trouvé', 404);
      }

      const result = await db.query(
        `UPDATE "Ingredients" 
         SET nom = $1, description = $2, unite_mesure = $3, categorie = $4,
             code_barre = $5, prix_unitaire_moyen = $6, fournisseur_principal_id = $7,
             allergenes = $8, conservation = $9, duree_conservation_jours = $10,
             actif = $11, updated_at = NOW()
         WHERE ingredient_id = $12
         RETURNING *`,
        [
          nom, description, uniteMesure, categorie, codeBarre,
          prixUnitaireMoyen, fournisseurPrincipalId, JSON.stringify(allergenes || []),
          conservation, dureeConservationJours, actif !== undefined ? actif : true,
          ingredientId
        ]
      );

      // Recalcul des coûts des produits liés si le prix a changé
      if (prixUnitaireMoyen !== existing.prix_unitaire_moyen) {
        await this.updateRelatedProductCosts(ingredientId);
      }

      return this.successResponse(result.rows[0], 'Ingrédient mis à jour avec succès');
    } catch (error) {
      logger.error(`Error updating ingredient ${ingredientId}:`, error);
      throw error;
    }
  }

  /**
   * Récupération des ingrédients avec filtres
   */
  static async getIngredients(complexeId, filters = {}) {
    try {
      const { categorie, actif, search, page = 1, limit = 50 } = filters;
      
      let query = `
        SELECT i.*, f.nom as fournisseur_nom,
               COALESCE(vs.stock_actuel, 0) as stock_actuel,
               COALESCE(vs.valeur_stock, 0) as valeur_stock
        FROM "Ingredients" i
        LEFT JOIN "Fournisseurs" f ON i.fournisseur_principal_id = f.fournisseur_id
        LEFT JOIN "VueStockIngredients" vs ON i.ingredient_id = vs.ingredient_id
        WHERE i.complexe_id = $1
      `;
      
      const params = [complexeId];
      let paramIndex = 2;

      // Filtres
      if (categorie) {
        query += ` AND i.categorie = $${paramIndex}`;
        params.push(categorie);
        paramIndex++;
      }

      if (actif !== undefined) {
        query += ` AND i.actif = $${paramIndex}`;
        params.push(actif);
        paramIndex++;
      }

      if (search) {
        query += ` AND (i.nom ILIKE $${paramIndex} OR i.description ILIKE $${paramIndex})`;
        params.push(`%${search}%`);
        paramIndex++;
      }

      // Pagination
      const offset = (page - 1) * limit;
      query += ` ORDER BY i.nom LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
      params.push(limit, offset);

      const result = await db.query(query, params);

      // Comptage total pour la pagination
      let countQuery = `
        SELECT COUNT(*) as total
        FROM "Ingredients" i
        WHERE i.complexe_id = $1
      `;
      const countParams = [complexeId];
      let countParamIndex = 2;

      if (categorie) {
        countQuery += ` AND i.categorie = $${countParamIndex}`;
        countParams.push(categorie);
        countParamIndex++;
      }

      if (actif !== undefined) {
        countQuery += ` AND i.actif = $${countParamIndex}`;
        countParams.push(actif);
        countParamIndex++;
      }

      if (search) {
        countQuery += ` AND (i.nom ILIKE $${countParamIndex} OR i.description ILIKE $${countParamIndex})`;
        countParams.push(`%${search}%`);
      }

      const countResult = await db.query(countQuery, countParams);
      const total = parseInt(countResult.rows[0].total);

      return this.successResponse({
        ingredients: result.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }, 'Ingrédients récupérés avec succès');

    } catch (error) {
      logger.error(`Error fetching ingredients for complexe ${complexeId}:`, error);
      throw error;
    }
  }

  /**
   * Recherche textuelle avancée d'ingrédients
   */
  static async searchIngredients(query, complexeId) {
    try {
      const searchQuery = `
        SELECT i.*, f.nom as fournisseur_nom,
               COALESCE(vs.stock_actuel, 0) as stock_actuel,
               SIMILARITY(i.nom, $1) as nom_similarity,
               SIMILARITY(i.description, $1) as desc_similarity
        FROM "Ingredients" i
        LEFT JOIN "Fournisseurs" f ON i.fournisseur_principal_id = f.fournisseur_id
        LEFT JOIN "VueStockIngredients" vs ON i.ingredient_id = vs.ingredient_id
        WHERE i.complexe_id = $2 AND i.actif = true
        AND (
          i.nom ILIKE $3 OR 
          i.description ILIKE $3 OR 
          i.code_barre = $4 OR
          SIMILARITY(i.nom, $1) > 0.3
        )
        ORDER BY 
          CASE WHEN i.nom ILIKE $3 THEN 1 ELSE 2 END,
          SIMILARITY(i.nom, $1) DESC,
          i.nom
        LIMIT 20
      `;

      const result = await db.query(searchQuery, [
        query, complexeId, `%${query}%`, query
      ]);

      return this.successResponse(result.rows, 'Recherche effectuée avec succès');
    } catch (error) {
      logger.error(`Error searching ingredients:`, error);
      throw error;
    }
  }

  /**
   * ==================== GESTION DU STOCK ====================
   */

  /**
   * Mise à jour du stock basée sur inventaire physique
   */
  static async updateStockFromInventaire(inventaireId) {
    try {
      // Récupération des lignes d'inventaire
      const inventaireLines = await db.query(
        `SELECT li.*, i.ingredient_id, i.nom as ingredient_nom, i.prix_unitaire_moyen
         FROM "LignesInventaire" li
         LEFT JOIN "Ingredients" i ON li.ingredient_id = i.ingredient_id
         WHERE li.inventaire_id = $1`,
        [inventaireId]
      );

      const mouvements = [];

      for (const ligne of inventaireLines.rows) {
        if (ligne.ecart !== 0) {
          // Création d'un mouvement de stock pour ajustement
          const mouvement = await db.query(
            `INSERT INTO "MouvementsStock" (
              chaine_id, complexe_id, service_id, produit_id, type_mouvement,
              quantite, reference_id, reference_type, employe_id, notes
            ) 
            SELECT inv.chaine_id, inv.complexe_id, inv.service_id, $1, 'Ajustement',
                   $2, $3, 'INVENTAIRE', inv.employe_id, $4
            FROM "Inventaires" inv WHERE inv.inventaire_id = $3
            RETURNING *`,
            [
              ligne.ingredient_id,
              ligne.ecart,
              inventaireId,
              `Ajustement inventaire: ${ligne.ingredient_nom} (écart: ${ligne.ecart})`
            ]
          );

          mouvements.push(mouvement.rows[0]);
        }
      }

      logger.info(`Stock updated from inventory ${inventaireId}`, { mouvements: mouvements.length });

      return this.successResponse({
        mouvements,
        adjustments: mouvements.length
      }, 'Stock mis à jour depuis l\'inventaire');

    } catch (error) {
      logger.error(`Error updating stock from inventory ${inventaireId}:`, error);
      throw error;
    }
  }

  /**
   * Récupération du stock des ingrédients
   */
  static async getStockIngredients(complexeId, serviceId = null) {
    try {
      let query = `
        SELECT * FROM "VueStockIngredients"
        WHERE complexe_id = $1
      `;
      const params = [complexeId];

      if (serviceId) {
        query += ` AND service_id = $2`;
        params.push(serviceId);
      }

      query += ` ORDER BY nom_ingredient`;

      const result = await db.query(query, params);

      // Calcul des statistiques
      const stats = {
        totalIngredients: result.rows.length,
        totalValue: result.rows.reduce((sum, item) => sum + parseFloat(item.valeur_stock || 0), 0),
        lowStockItems: result.rows.filter(item => item.stock_actuel < 10).length,
        outOfStockItems: result.rows.filter(item => item.stock_actuel <= 0).length
      };

      return this.successResponse({
        stock: result.rows,
        statistics: stats
      }, 'Stock des ingrédients récupéré avec succès');

    } catch (error) {
      logger.error(`Error fetching ingredient stock for complexe ${complexeId}:`, error);
      throw error;
    }
  }

  /**
   * Calcul de la valeur totale du stock
   */
  static async calculateStockValue(complexeId) {
    try {
      const result = await db.query(
        `SELECT 
           COUNT(*) as total_ingredients,
           SUM(stock_actuel * prix_unitaire_moyen) as valeur_totale,
           SUM(CASE WHEN stock_actuel > 0 THEN stock_actuel * prix_unitaire_moyen ELSE 0 END) as valeur_disponible,
           categorie,
           SUM(stock_actuel * prix_unitaire_moyen) as valeur_par_categorie
         FROM "VueStockIngredients"
         WHERE complexe_id = $1
         GROUP BY categorie
         ORDER BY valeur_par_categorie DESC`,
        [complexeId]
      );

      const totalValue = await db.query(
        `SELECT 
           COUNT(*) as total_ingredients,
           SUM(stock_actuel * prix_unitaire_moyen) as valeur_totale
         FROM "VueStockIngredients"
         WHERE complexe_id = $1`,
        [complexeId]
      );

      return this.successResponse({
        totalValue: totalValue.rows[0],
        byCategory: result.rows
      }, 'Valeur du stock calculée avec succès');

    } catch (error) {
      logger.error(`Error calculating stock value for complexe ${complexeId}:`, error);
      throw error;
    }
  }

  /**
   * ==================== MÉTHODES UTILITAIRES ====================
   */

  /**
   * Vérification des doublons d'ingrédients
   */
  static async checkIngredientDuplicate(nom, complexeId, codeBarre = null) {
    // Vérification par nom
    const nameCheck = await db.query(
      'SELECT ingredient_id FROM "Ingredients" WHERE nom = $1 AND complexe_id = $2 AND actif = true',
      [nom, complexeId]
    );

    if (nameCheck.rows.length > 0) {
      this.rejectResponse(`Un ingrédient avec le nom "${nom}" existe déjà`, 409);
    }

    // Vérification par code-barres si fourni
    if (codeBarre) {
      const barcodeCheck = await db.query(
        'SELECT ingredient_id FROM "Ingredients" WHERE code_barre = $1 AND actif = true',
        [codeBarre]
      );

      if (barcodeCheck.rows.length > 0) {
        this.rejectResponse(`Un ingrédient avec le code-barres "${codeBarre}" existe déjà`, 409);
      }
    }
  }

  /**
   * Récupération d'un ingrédient par ID
   */
  static async getIngredientById(ingredientId) {
    const result = await db.query(
      'SELECT * FROM "Ingredients" WHERE ingredient_id = $1',
      [ingredientId]
    );
    return result.rows[0] || null;
  }

  /**
   * Initialisation du stock d'un ingrédient
   */
  static async initializeIngredientStock(ingredientId, complexeId) {
    // Le stock sera géré via les mouvements de stock
    // Pas besoin d'initialisation spécifique car la vue calcule automatiquement
    logger.info(`Stock initialized for ingredient ${ingredientId}`);
  }

  /**
   * Mise à jour des coûts des produits liés
   */
  static async updateRelatedProductCosts(ingredientId) {
    try {
      // Récupération des produits utilisant cet ingrédient
      const produits = await db.query(
        `SELECT DISTINCT pi.produit_id
         FROM "ProduitsIngredients" pi
         JOIN "Produits" p ON pi.produit_id = p.produit_id
         WHERE pi.ingredient_id = $1 AND p.actif = true`,
        [ingredientId]
      );

      // Mise à jour du coût unitaire dans ProduitsIngredients
      await db.query(
        `UPDATE "ProduitsIngredients"
         SET cout_unitaire = (
           SELECT prix_unitaire_moyen
           FROM "Ingredients"
           WHERE ingredient_id = $1
         )
         WHERE ingredient_id = $1`,
        [ingredientId]
      );

      // Recalculer le coût total de chaque produit affecté
      const ProduitIngredientService = require('./produitIngredient.service');
      for (const produit of produits.rows) {
        await ProduitIngredientService.calculerCoutProduit(produit.produit_id);
      }

      logger.info(`Updated costs for ${produits.rows.length} products affected by ingredient ${ingredientId}`);

      return produits.rows.length;
    } catch (error) {
      logger.error(`Error updating related product costs for ingredient ${ingredientId}:`, error);
      throw error;
    }
  }

  /**
   * Obtenir les catégories d'ingrédients disponibles
   */
  static async getIngredientCategories(complexeId) {
    try {
      const result = await db.query(
        `SELECT categorie, COUNT(*) as count
         FROM "Ingredients"
         WHERE complexe_id = $1 AND actif = true
         GROUP BY categorie
         ORDER BY categorie`,
        [complexeId]
      );

      return this.successResponse(result.rows, 'Catégories récupérées avec succès');
    } catch (error) {
      logger.error(`Error fetching ingredient categories for complexe ${complexeId}:`, error);
      throw error;
    }
  }

  /**
   * Génération d'alertes de stock faible
   */
  static async generateStockAlerts(complexeId) {
    try {
      const alerts = await db.query(
        `SELECT i.ingredient_id, i.nom, i.categorie, vs.stock_actuel,
                CASE 
                  WHEN vs.stock_actuel <= 0 THEN 'RUPTURE'
                  WHEN vs.stock_actuel <= 5 THEN 'CRITIQUE'
                  WHEN vs.stock_actuel <= 10 THEN 'FAIBLE'
                  ELSE 'NORMAL'
                END as niveau_alerte
         FROM "Ingredients" i
         LEFT JOIN "VueStockIngredients" vs ON i.ingredient_id = vs.ingredient_id
         WHERE i.complexe_id = $1 AND i.actif = true
         AND vs.stock_actuel <= 10
         ORDER BY vs.stock_actuel ASC, i.nom`,
        [complexeId]
      );

      return this.successResponse(alerts.rows, 'Alertes de stock générées');
    } catch (error) {
      logger.error(`Error generating stock alerts for complexe ${complexeId}:`, error);
      throw error;
    }
  }
}

module.exports = InventaireService;
