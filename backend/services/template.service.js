const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs').promises;
const db = require('../db');
const logger = require('../logger');
const BaseService = require('./base.service');

class TemplateService extends BaseService {

  /**
   * ==================== GESTION DES TEMPLATES ====================
   */

  /**
   * Récupération de tous les templates
   */
  static async getTemplates(chaineId, filters = {}) {
    try {
      const { typeService, typeImport, actif } = filters;
      
      let query = `
        SELECT * FROM "TemplatesImport"
        WHERE chaine_id = $1
      `;
      
      const params = [chaineId];
      let paramIndex = 2;

      if (typeService) {
        query += ` AND type_service = $${paramIndex}`;
        params.push(typeService);
        paramIndex++;
      }

      if (typeImport) {
        query += ` AND type_import = $${paramIndex}`;
        params.push(typeImport);
        paramIndex++;
      }

      if (actif !== undefined) {
        query += ` AND actif = $${paramIndex}`;
        params.push(actif);
        paramIndex++;
      }

      query += ` ORDER BY type_service, type_import, nom_template`;

      const result = await db.query(query, params);

      return this.successResponse(result.rows, 'Templates récupérés avec succès');

    } catch (error) {
      logger.error(`Error fetching templates for chaine ${chaineId}:`, error);
      throw error;
    }
  }

  /**
   * Récupération d'un template par ID
   */
  static async getTemplateById(templateId) {
    try {
      const result = await db.query(
        'SELECT * FROM "TemplatesImport" WHERE template_id = $1',
        [templateId]
      );

      if (result.rows.length === 0) {
        this.rejectResponse('Template non trouvé', 404);
      }

      return this.successResponse(result.rows[0], 'Template récupéré avec succès');

    } catch (error) {
      logger.error(`Error fetching template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Création d'un nouveau template
   */
  static async createTemplate(templateData) {
    try {
      const {
        chaineId, typeService, typeImport, nomTemplate, description,
        colonnesRequises, exempleDonnees, reglesValidation, mappingDefaut
      } = templateData;

      // Validation de la structure
      this.validateTemplateStructure(templateData);

      const result = await db.query(
        `INSERT INTO "TemplatesImport" (
          chaine_id, type_service, type_import, nom_template, description,
          colonnes_requises, exemple_donnees, regles_validation, mapping_defaut,
          created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW())
        RETURNING *`,
        [
          chaineId, typeService, typeImport, nomTemplate, description,
          JSON.stringify(colonnesRequises), JSON.stringify(exempleDonnees),
          JSON.stringify(reglesValidation), JSON.stringify(mappingDefaut)
        ]
      );

      logger.info(`Template created: ${nomTemplate}`, { templateId: result.rows[0].template_id });

      return this.successResponse(result.rows[0], 'Template créé avec succès');

    } catch (error) {
      logger.error('Error creating template:', error);
      throw error;
    }
  }

  /**
   * Mise à jour d'un template
   */
  static async updateTemplate(templateId, templateData) {
    try {
      const {
        nomTemplate, description, colonnesRequises, exempleDonnees,
        reglesValidation, mappingDefaut, actif, version
      } = templateData;

      // Vérification de l'existence
      const existing = await this.getTemplateById(templateId);
      if (!existing.success) {
        this.rejectResponse('Template non trouvé', 404);
      }

      const result = await db.query(
        `UPDATE "TemplatesImport" 
         SET nom_template = $1, description = $2, colonnes_requises = $3,
             exemple_donnees = $4, regles_validation = $5, mapping_defaut = $6,
             actif = $7, version = $8, updated_at = NOW()
         WHERE template_id = $9
         RETURNING *`,
        [
          nomTemplate, description, JSON.stringify(colonnesRequises),
          JSON.stringify(exempleDonnees), JSON.stringify(reglesValidation),
          JSON.stringify(mappingDefaut), actif !== undefined ? actif : true,
          version || '1.0', templateId
        ]
      );

      return this.successResponse(result.rows[0], 'Template mis à jour avec succès');

    } catch (error) {
      logger.error(`Error updating template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * ==================== GÉNÉRATION DE FICHIERS TEMPLATE ====================
   */

  /**
   * Génération d'un fichier Excel template
   */
  static async generateTemplate(templateId) {
    try {
      const templateInfo = await this.getTemplateById(templateId);
      const template = templateInfo.data;

      // Création du workbook Excel
      const workbook = XLSX.utils.book_new();

      // Génération de la feuille principale
      const worksheet = this.createTemplateWorksheet(template);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Données');

      // Génération de la feuille d'instructions
      const instructionsSheet = this.createInstructionsWorksheet(template);
      XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Instructions');

      // Génération de la feuille d'exemples
      const examplesSheet = this.createExamplesWorksheet(template);
      XLSX.utils.book_append_sheet(workbook, examplesSheet, 'Exemples');

      // Génération du fichier
      const fileName = this.generateTemplateFileName(template);
      const filePath = path.join('uploads/templates', fileName);

      // Création du répertoire si nécessaire
      await this.ensureDirectoryExists('uploads/templates');

      // Écriture du fichier
      XLSX.writeFile(workbook, filePath);

      logger.info(`Template file generated: ${fileName}`, { templateId });

      return this.successResponse({
        fileName,
        filePath,
        downloadUrl: `/api/templates/download/${templateId}`
      }, 'Fichier template généré avec succès');

    } catch (error) {
      logger.error(`Error generating template file ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Création de la feuille de données principale
   */
  static createTemplateWorksheet(template) {
    const colonnes = template.colonnes_requises?.colonnes || [];
    const mappingDefaut = template.mapping_defaut || {};

    // Création des en-têtes
    const headers = [];
    const descriptions = [];

    for (const colonne of colonnes) {
      // Recherche du nom d'affichage dans le mapping
      const displayName = Object.keys(mappingDefaut).find(key => 
        mappingDefaut[key] === colonne
      ) || colonne;
      
      headers.push(displayName);
      descriptions.push(this.getColumnDescription(colonne, template.type_import));
    }

    // Création des données avec en-têtes et descriptions
    const data = [headers, descriptions];

    // Ajout de quelques lignes vides pour la saisie
    for (let i = 0; i < 10; i++) {
      data.push(new Array(headers.length).fill(''));
    }

    const worksheet = XLSX.utils.aoa_to_sheet(data);

    // Formatage des cellules
    this.formatTemplateWorksheet(worksheet, headers.length);

    return worksheet;
  }

  /**
   * Création de la feuille d'instructions
   */
  static createInstructionsWorksheet(template) {
    const instructions = [
      ['INSTRUCTIONS D\'UTILISATION'],
      [''],
      ['1. Structure du fichier'],
      ['- Utilisez uniquement la feuille "Données" pour saisir vos informations'],
      ['- Ne modifiez pas les en-têtes de colonnes'],
      ['- Respectez les formats de données indiqués'],
      [''],
      ['2. Colonnes requises'],
      ...this.generateColumnInstructions(template),
      [''],
      ['3. Règles de validation'],
      ...this.generateValidationInstructions(template),
      [''],
      ['4. Conseils'],
      ['- Vérifiez vos données avant l\'import'],
      ['- Consultez la feuille "Exemples" pour des modèles'],
      ['- En cas d\'erreur, corrigez et relancez l\'import']
    ];

    return XLSX.utils.aoa_to_sheet(instructions);
  }

  /**
   * Création de la feuille d'exemples
   */
  static createExamplesWorksheet(template) {
    const exempleDonnees = template.exemple_donnees?.exemple || [];
    
    if (exempleDonnees.length === 0) {
      return XLSX.utils.aoa_to_sheet([['Aucun exemple disponible']]);
    }

    // Extraction des en-têtes depuis le premier exemple
    const headers = Object.keys(exempleDonnees[0]);
    
    // Conversion des exemples en tableau
    const data = [headers];
    for (const exemple of exempleDonnees) {
      const row = headers.map(header => exemple[header] || '');
      data.push(row);
    }

    return XLSX.utils.aoa_to_sheet(data);
  }

  /**
   * ==================== MÉTHODES UTILITAIRES ====================
   */

  /**
   * Validation de la structure d'un template
   */
  static validateTemplateStructure(templateData) {
    const { typeService, typeImport, colonnesRequises, reglesValidation } = templateData;

    // Validation du type de service
    const typesServicesValides = ['Restaurant', 'Bar', 'Piscine'];
    if (!typesServicesValides.includes(typeService)) {
      this.rejectResponse(`Type de service non valide: ${typeService}`, 400);
    }

    // Validation du type d'import
    const typesImportsValides = ['MENU', 'CARTE', 'INVENTAIRE'];
    if (!typesImportsValides.includes(typeImport)) {
      this.rejectResponse(`Type d'import non valide: ${typeImport}`, 400);
    }

    // Validation de la structure des colonnes requises
    if (!colonnesRequises || !colonnesRequises.colonnes || !Array.isArray(colonnesRequises.colonnes)) {
      this.rejectResponse('La structure des colonnes requises est invalide', 400);
    }

    // Validation des règles de validation
    if (reglesValidation && typeof reglesValidation !== 'object') {
      this.rejectResponse('Les règles de validation doivent être un objet JSON', 400);
    }
  }

  /**
   * Génération du nom de fichier template
   */
  static generateTemplateFileName(template) {
    const timestamp = new Date().toISOString().slice(0, 10);
    const safeName = template.nom_template
      .replace(/[^a-zA-Z0-9]/g, '_')
      .toLowerCase();
    
    return `template_${template.type_service}_${template.type_import}_${safeName}_${timestamp}.xlsx`;
  }

  /**
   * Description des colonnes selon le type d'import
   */
  static getColumnDescription(colonne, typeImport) {
    const descriptions = {
      'MENU': {
        'nom_produit': 'Nom du plat ou menu (obligatoire)',
        'categorie': 'Catégorie (Entrées, Plats, Desserts...)',
        'prix_vente': 'Prix de vente en euros (obligatoire)',
        'description': 'Description du produit',
        'ingredients': 'Liste des ingrédients séparés par des virgules',
        'quantites': 'Quantités correspondantes séparées par des virgules',
        'unites': 'Unités de mesure séparées par des virgules'
      },
      'CARTE': {
        'nom_boisson': 'Nom de la boisson (obligatoire)',
        'categorie': 'Catégorie (Cocktails, Bières, Vins...)',
        'prix_vente': 'Prix de vente en euros (obligatoire)',
        'degre_alcool': 'Degré d\'alcool (0-100)',
        'ingredients': 'Ingrédients pour cocktails',
        'quantites': 'Quantités en mL ou cl',
        'unites': 'Unités de mesure'
      },
      'INVENTAIRE': {
        'nom_ingredient': 'Nom de l\'ingrédient (obligatoire)',
        'categorie': 'Catégorie (Légumes, Viandes, Boissons...)',
        'unite_mesure': 'Unité (kg, L, unité, pièce...)',
        'prix_unitaire': 'Prix unitaire en euros (obligatoire)',
        'stock_actuel': 'Stock actuel en unités',
        'stock_minimal': 'Stock minimal d\'alerte',
        'fournisseur': 'Nom du fournisseur',
        'conservation': 'Type de conservation (Frais, Congelé, Sec, Ambiant)'
      }
    };

    return descriptions[typeImport]?.[colonne] || 'Description non disponible';
  }

  /**
   * Génération des instructions pour les colonnes
   */
  static generateColumnInstructions(template) {
    const colonnes = template.colonnes_requises?.colonnes || [];
    const instructions = [];

    for (const colonne of colonnes) {
      const description = this.getColumnDescription(colonne, template.type_import);
      instructions.push([`- ${colonne}: ${description}`]);
    }

    return instructions;
  }

  /**
   * Génération des instructions de validation
   */
  static generateValidationInstructions(template) {
    const regles = template.regles_validation?.regles || {};
    const instructions = [];

    for (const [champ, regle] of Object.entries(regles)) {
      let instruction = `- ${champ}: `;
      
      if (regle.required) instruction += 'Obligatoire. ';
      if (regle.type) instruction += `Type: ${regle.type}. `;
      if (regle.min !== undefined) instruction += `Minimum: ${regle.min}. `;
      if (regle.max !== undefined) instruction += `Maximum: ${regle.max}. `;
      if (regle.max_length) instruction += `Longueur max: ${regle.max_length} caractères. `;

      instructions.push([instruction]);
    }

    return instructions;
  }

  /**
   * Formatage de la feuille template
   */
  static formatTemplateWorksheet(worksheet, columnCount) {
    // Définition de la plage
    const range = XLSX.utils.decode_range(worksheet['!ref']);
    
    // Formatage des en-têtes (ligne 1)
    for (let col = 0; col < columnCount; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
      if (!worksheet[cellAddress]) continue;
      
      worksheet[cellAddress].s = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "366092" } },
        alignment: { horizontal: "center" }
      };
    }

    // Formatage des descriptions (ligne 2)
    for (let col = 0; col < columnCount; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: 1, c: col });
      if (!worksheet[cellAddress]) continue;
      
      worksheet[cellAddress].s = {
        font: { italic: true, color: { rgb: "666666" } },
        fill: { fgColor: { rgb: "F0F0F0" } }
      };
    }

    // Largeur des colonnes
    worksheet['!cols'] = new Array(columnCount).fill({ width: 20 });
  }

  /**
   * Création du répertoire s'il n'existe pas
   */
  static async ensureDirectoryExists(dirPath) {
    try {
      await fs.access(dirPath);
    } catch (error) {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  /**
   * Suppression d'un template
   */
  static async deleteTemplate(templateId) {
    try {
      const result = await db.query(
        'DELETE FROM "TemplatesImport" WHERE template_id = $1 RETURNING *',
        [templateId]
      );

      if (result.rows.length === 0) {
        this.rejectResponse('Template non trouvé', 404);
      }

      return this.successResponse(
        { templateId },
        'Template supprimé avec succès'
      );

    } catch (error) {
      logger.error(`Error deleting template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Duplication d'un template
   */
  static async duplicateTemplate(templateId, newName) {
    try {
      const originalTemplate = await this.getTemplateById(templateId);
      const template = originalTemplate.data;

      const duplicatedTemplate = {
        chaineId: template.chaine_id,
        typeService: template.type_service,
        typeImport: template.type_import,
        nomTemplate: newName || `${template.nom_template} (Copie)`,
        description: `${template.description} (Copie)`,
        colonnesRequises: JSON.parse(template.colonnes_requises),
        exempleDonnees: JSON.parse(template.exemple_donnees || '{}'),
        reglesValidation: JSON.parse(template.regles_validation || '{}'),
        mappingDefaut: JSON.parse(template.mapping_defaut || '{}')
      };

      return await this.createTemplate(duplicatedTemplate);

    } catch (error) {
      logger.error(`Error duplicating template ${templateId}:`, error);
      throw error;
    }
  }

  /**
   * Récupération des templates par défaut
   */
  static getDefaultTemplates() {
    return {
      restaurant: {
        typeService: 'Restaurant',
        typeImport: 'MENU',
        colonnesRequises: {
          colonnes: ['nom_produit', 'categorie', 'prix_vente', 'description', 'ingredients', 'quantites', 'unites']
        },
        exempleDonnees: {
          exemple: [{
            nom_produit: 'Salade César',
            categorie: 'Entrées',
            prix_vente: 12.50,
            description: 'Salade avec croûtons et parmesan',
            ingredients: 'Salade,Croûtons,Parmesan',
            quantites: '100,20,30',
            unites: 'g,g,g'
          }]
        },
        reglesValidation: {
          regles: {
            nom_produit: { required: true, type: 'string', max_length: 255 },
            prix_vente: { required: true, type: 'decimal', min: 0 }
          }
        },
        mappingDefaut: {
          'Nom du produit': 'nom_produit',
          'Catégorie': 'categorie',
          'Prix de vente': 'prix_vente',
          'Description': 'description',
          'Ingrédients': 'ingredients',
          'Quantités': 'quantites',
          'Unités': 'unites'
        }
      },
      bar: {
        typeService: 'Bar',
        typeImport: 'CARTE',
        colonnesRequises: {
          colonnes: ['nom_boisson', 'categorie', 'prix_vente', 'degre_alcool', 'ingredients', 'quantites', 'unites']
        },
        exempleDonnees: {
          exemple: [{
            nom_boisson: 'Mojito',
            categorie: 'Cocktails',
            prix_vente: 8.50,
            degre_alcool: 12,
            ingredients: 'Rhum blanc,Menthe,Citron vert',
            quantites: '50,10,20',
            unites: 'mL,g,mL'
          }]
        },
        reglesValidation: {
          regles: {
            nom_boisson: { required: true, type: 'string', max_length: 255 },
            prix_vente: { required: true, type: 'decimal', min: 0 },
            degre_alcool: { type: 'decimal', min: 0, max: 100 }
          }
        },
        mappingDefaut: {
          'Nom de la boisson': 'nom_boisson',
          'Catégorie': 'categorie',
          'Prix de vente': 'prix_vente',
          'Degré d\'alcool': 'degre_alcool',
          'Ingrédients': 'ingredients',
          'Quantités': 'quantites',
          'Unités': 'unites'
        }
      }
    };
  }
}

module.exports = TemplateService;
