// Services existants
const AuthService = require('./auth.service');
const BaseService = require('./base.service');
const ChambreService = require('./chambre.service');
const ClientService = require('./client.service');
const ComplexService = require('./complex.service');
const DisponibiliteService = require('./disponibilite.service');
const EmployeeService = require('./employee.service');
const NotificationService = require('./notification.service');
const PaiementService = require('./paiement.service');
const PermissionService = require('./permission.service');
const PosService = require('./pos.service');
const RapportService = require('./rapport.service');
const ReservationService = require('./reservation.service');
const RoleService = require('./role.service');
const ServiceComplexeService = require('./service.service');
const SessionService = require('./session.service');
const StatistiqueService = require('./statistique.service');
const TicketService = require('./ticket.service');
const TokenService = require('./token.service');
const TracabiliteService = require('./tracabilite.service');
const TransactionService = require('./transaction.service');

// Nouveaux services pour le système d'inventaire
const FileUploadService = require('./fileUpload.service');
const ExcelParserService = require('./excelParser.service');
const InventaireService = require('./inventaire.service');
const RecetteService = require('./recette.service');
const ImportService = require('./import.service');
const TemplateService = require('./template.service');

// Service d'intégration POS-Stock
const POSStockIntegration = require('./posStockIntegration.service');

// Services POS Restaurant/Bar
const TableService = require('./table.service');
const MenuService = require('./menu.service');
const CommandeService = require('./commande.service');
const POSAnalyticsService = require('./posAnalytics.service');

module.exports = {
  // Services existants
  AuthService,
  BaseService,
  ChambreService,
  ClientService,
  ComplexService,
  DisponibiliteService,
  EmployeeService,
  NotificationService,
  PaiementService,
  PermissionService,
  PosService,
  RapportService,
  ReservationService,
  RoleService,
  ServiceComplexeService,
  SessionService,
  StatistiqueService,
  TicketService,
  TokenService,
  TracabiliteService,
  TransactionService,

  // Nouveaux services d'inventaire
  FileUploadService,
  ExcelParserService,
  InventaireService,
  RecetteService,
  ImportService,
  TemplateService,

  // Service d'intégration
  POSStockIntegration,

  // Services POS Restaurant/Bar
  TableService,
  MenuService,
  CommandeService,
  POSAnalyticsService
};
