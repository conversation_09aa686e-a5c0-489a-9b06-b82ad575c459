const bcrypt = require('bcrypt');
const logger = require('../logger');
const db = require('../db');
const EmployeeTypeService = require('./employeeType.service');

class EmployeeService {
  static async createEmployee(employeeData) {
    try {
      const {
        nom,
        prenom,
        email,
        telephone,
        role_id,
        complexe_id,
        password,
        type_employe,
        services_autorises
      } = employeeData;

      // Vérifier si l'email existe déjà
      const existingEmployee = await db.query(
        'SELECT * FROM "Employes" WHERE email = $1',
        [email]
      );

      if (existingEmployee.rows.length > 0) {
        throw new Error('Un employé avec cet email existe déjà');
      }

      // Hasher le mot de passe
      const hashedPassword = await bcrypt.hash(password, 10);

      // Gestion du type d'employé et du rôle
      let finalRoleId = role_id;
      let finalServicesAutorises = services_autorises;

      // Si un type d'employé est spécifié, créer/obtenir le rôle prédéfini
      if (type_employe) {
        if (!EmployeeTypeService.isValidType(type_employe)) {
          throw new Error(`Type d'employé invalide: ${type_employe}`);
        }

        const predefinedRole = await EmployeeTypeService.getOrCreatePredefinedRole(complexe_id, type_employe);
        finalRoleId = predefinedRole.role_id;

        // Utiliser les services par défaut si non spécifiés
        if (!finalServicesAutorises) {
          finalServicesAutorises = EmployeeTypeService.getDefaultServicesForType(type_employe);
        }
      }

      // Créer l'employé
      const query = `
        INSERT INTO "Employes" (
          nom, prenom, email, telephone, role_id,
          complexe_id, mot_de_passe_hash, date_embauche,
          type_employe, services_autorises, created_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_DATE, $8, $9, CURRENT_TIMESTAMP)
        RETURNING *
      `;

      const result = await db.query(query, [
        nom,
        prenom,
        email,
        telephone,
        finalRoleId,
        complexe_id,
        hashedPassword,
        type_employe,
        finalServicesAutorises ? JSON.stringify(finalServicesAutorises) : null
      ]);

      const employee = result.rows[0];

      // Log de création avec type d'employé
      if (type_employe) {
        logger.info(`Employé créé avec type: ${type_employe} pour ${nom} ${prenom}`);
      }

      return employee;
    } catch (error) {
      logger.error('Error creating employee:', error);
      throw error;
    }
  }

  static async getEmployeeById(employeeId) {
    try {
      const query = `
        SELECT e.*, r.nom as role_nom
        FROM "Employes" e
        LEFT JOIN "RolesComplexe" r ON e.role_id = r.role_id
        WHERE e.employe_id = $1
      `;
      const result = await db.query(query, [employeeId]);
      return result.rows[0];
    } catch (error) {
      logger.error('Error getting employee:', error);
      throw error;
    }
  }

  static async getEmployeesByComplexe(complexeId, filters = {}) {
    try {
      let query = `
        SELECT e.*, r.nom as role_nom
        FROM "Employes" e
        LEFT JOIN "RolesComplexe" r ON e.role_id = r.role_id
        WHERE e.complexe_id = $1
      `;
      const params = [complexeId];
      let paramIndex = 2;

      // Ajouter les filtres
      if (filters.role_id) {
        query += ` AND e.role_id = $${paramIndex}`;
        params.push(filters.role_id);
        paramIndex++;
      }

      if (filters.search) {
        query += ` AND (
          e.nom ILIKE $${paramIndex} OR 
          e.prenom ILIKE $${paramIndex} OR 
          e.email ILIKE $${paramIndex}
        )`;
        params.push(`%${filters.search}%`);
        paramIndex++;
      }

      // Ajouter le tri
      query += ' ORDER BY e.nom, e.prenom';

      const result = await db.query(query, params);
      return result.rows;
    } catch (error) {
      logger.error('Error getting employees:', error);
      throw error;
    }
  }

  static async updateEmployee(employeeId, updateData) {
    try {
      const allowedFields = [
        'nom', 'prenom', 'email', 'telephone',
        'role_id', 'actif', 'type_employe', 'services_autorises'
      ];
      
      const updates = [];
      const values = [];
      let paramIndex = 1;

      // Gestion spéciale pour le changement de type d'employé
      if (updateData.type_employe) {
        if (!EmployeeTypeService.isValidType(updateData.type_employe)) {
          throw new Error(`Type d'employé invalide: ${updateData.type_employe}`);
        }

        // Récupérer l'employé actuel pour obtenir le complexe_id
        const currentEmployee = await this.getEmployeeById(employeeId);
        if (!currentEmployee) {
          throw new Error('Employé non trouvé');
        }

        // Créer/obtenir le rôle prédéfini pour le nouveau type
        const predefinedRole = await EmployeeTypeService.getOrCreatePredefinedRole(
          currentEmployee.complexe_id,
          updateData.type_employe
        );

        // Forcer la mise à jour du role_id
        updateData.role_id = predefinedRole.role_id;

        // Si services_autorises n'est pas spécifié, utiliser les services par défaut
        if (!updateData.services_autorises) {
          updateData.services_autorises = EmployeeTypeService.getDefaultServicesForType(updateData.type_employe);
        }
      }

      // Construire la requête dynamiquement
      for (const [key, value] of Object.entries(updateData)) {
        if (allowedFields.includes(key)) {
          if (key === 'services_autorises' && value) {
            // Convertir en JSON si c'est un array
            const jsonValue = Array.isArray(value) ? JSON.stringify(value) : value;
            updates.push(`${key} = $${paramIndex}`);
            values.push(jsonValue);
          } else {
            updates.push(`${key} = $${paramIndex}`);
            values.push(value);
          }
          paramIndex++;
        }
      }

      if (updates.length === 0) {
        throw new Error('Aucun champ valide à mettre à jour');
      }

      const query = `
        UPDATE "Employes"
        SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE employe_id = $${paramIndex}
        RETURNING *
      `;
      values.push(employeeId);

      const result = await db.query(query, values);
      return result.rows[0];
    } catch (error) {
      logger.error('Error updating employee:', error);
      throw error;
    }
  }

  static async updatePassword(employeeId, newPassword) {
    try {
      const hashedPassword = await bcrypt.hash(newPassword, 10);
      
      const query = `
        UPDATE "Employes"
        SET mot_de_passe_hash = $1, updated_at = CURRENT_TIMESTAMP
        WHERE employe_id = $2
        RETURNING employe_id
      `;
      
      const result = await db.query(query, [hashedPassword, employeeId]);
      return result.rows[0];
    } catch (error) {
      logger.error('Error updating password:', error);
      throw error;
    }
  }

  static async deleteEmployee(employeeId) {
    try {
      const query = `
        UPDATE "Employes"
        SET actif = false, updated_at = CURRENT_TIMESTAMP
        WHERE employe_id = $1
        RETURNING employe_id
      `;

      const result = await db.query(query, [employeeId]);
      return result.rows[0];
    } catch (error) {
      logger.error('Error deleting employee:', error);
      throw error;
    }
  }

  /**
   * Vérifier les dépendances d'un employé avant suppression définitive
   */
  static async checkEmployeeDependencies(employeeId) {
    try {
      const dependencies = {};

      // Vérifier les points de vente
      const posResult = await db.query(
        'SELECT COUNT(*) as count FROM "PointsDeVente" WHERE employe_actuel_id = $1',
        [employeeId]
      );
      dependencies.pointsDeVente = parseInt(posResult.rows[0].count);

      // Vérifier les sessions de caisse
      const sessionsResult = await db.query(
        'SELECT COUNT(*) as count FROM "SessionsCaisse" WHERE employe_id = $1',
        [employeeId]
      );
      dependencies.sessionsCaisse = parseInt(sessionsResult.rows[0].count);

      // Vérifier les réservations
      const reservationsResult = await db.query(
        'SELECT COUNT(*) as count FROM "Reservations" WHERE employe_id = $1',
        [employeeId]
      );
      dependencies.reservations = parseInt(reservationsResult.rows[0].count);

      // Vérifier les transactions POS
      const transactionsResult = await db.query(
        'SELECT COUNT(*) as count FROM "TransactionsPOS" WHERE employe_id = $1',
        [employeeId]
      );
      dependencies.transactions = parseInt(transactionsResult.rows[0].count);

      // Vérifier les commandes
      const commandesResult = await db.query(
        'SELECT COUNT(*) as count FROM "Commandes" WHERE employe_id = $1',
        [employeeId]
      );
      dependencies.commandes = parseInt(commandesResult.rows[0].count);

      // Calculer le total des dépendances
      const totalDependencies = Object.values(dependencies).reduce((sum, count) => sum + count, 0);

      return {
        hasDependencies: totalDependencies > 0,
        dependencies,
        totalCount: totalDependencies
      };
    } catch (error) {
      logger.error('Error checking employee dependencies:', error);
      throw error;
    }
  }

  /**
   * Suppression définitive d'un employé (hard delete)
   */
  static async hardDeleteEmployee(employeeId, force = false) {
    try {
      // Vérifier les dépendances si force n'est pas activé
      if (!force) {
        const dependencyCheck = await this.checkEmployeeDependencies(employeeId);
        if (dependencyCheck.hasDependencies) {
          throw new Error(`Impossible de supprimer l'employé : ${dependencyCheck.totalCount} enregistrement(s) dépendant(s) trouvé(s). Utilisez la désactivation ou forcez la suppression.`);
        }
      }

      // Si force = true, on supprime d'abord les dépendances ou on les met à NULL
      if (force) {
        // Mettre à NULL les références dans les points de vente
        await db.query(
          'UPDATE "PointsDeVente" SET employe_actuel_id = NULL WHERE employe_actuel_id = $1',
          [employeeId]
        );

        // Pour les autres tables, on garde les enregistrements mais on met employe_id à NULL
        // Cela préserve l'historique tout en permettant la suppression
        const tablesToUpdate = [
          'SessionsCaisse',
          'Reservations',
          'TransactionsPOS',
          'UtilisationsCodes',
          'CommandesFournisseurs',
          'MouvementsStock',
          'Inventaires',
          'ReservationsTables',
          'Commandes',
          'HistoriqueReservations',
          'Tickets'
        ];

        for (const table of tablesToUpdate) {
          await db.query(
            `UPDATE "${table}" SET employe_id = NULL WHERE employe_id = $1`,
            [employeeId]
          );
        }

        // Cas spécial pour DetailsCommandes (employe_preparation)
        await db.query(
          'UPDATE "DetailsCommandes" SET employe_preparation = NULL WHERE employe_preparation = $1',
          [employeeId]
        );

        // Cas spécial pour UtilisationsTickets (utilisateur_id)
        await db.query(
          'UPDATE "UtilisationsTickets" SET utilisateur_id = NULL WHERE utilisateur_id = $1',
          [employeeId]
        );
      }

      // Supprimer définitivement l'employé
      const deleteQuery = `
        DELETE FROM "Employes"
        WHERE employe_id = $1
        RETURNING employe_id, nom, prenom
      `;

      const result = await db.query(deleteQuery, [employeeId]);

      if (result.rows.length === 0) {
        throw new Error('Employé non trouvé');
      }

      return result.rows[0];
    } catch (error) {
      logger.error('Error hard deleting employee:', error);
      throw error;
    }
  }

  static async getEmployeeRoles(complexeId) {
    try {
      const query = `
        SELECT role_id, nom, description, permissions
        FROM "RolesComplexe"
        WHERE complexe_id = $1
        ORDER BY nom
      `;
      
      const result = await db.query(query, [complexeId]);
      return result.rows;
    } catch (error) {
      logger.error('Error getting employee roles:', error);
      throw error;
    }
  }

  /**
   * Obtenir les employés avec leurs informations de type
   */
  static async getEmployeesWithTypes(complexeId, filters = {}) {
    try {
      let query = `
        SELECT
          e.*,
          r.nom as role_nom,
          r.permissions
        FROM "Employes" e
        LEFT JOIN "RolesComplexe" r ON e.role_id = r.role_id
        WHERE e.complexe_id = $1
      `;
      const params = [complexeId];
      let paramIndex = 2;

      // Ajouter les filtres
      if (filters.type_employe) {
        query += ` AND e.type_employe = $${paramIndex}`;
        params.push(filters.type_employe);
        paramIndex++;
      }

      if (filters.role_id) {
        query += ` AND e.role_id = $${paramIndex}`;
        params.push(filters.role_id);
        paramIndex++;
      }

      if (filters.search) {
        query += ` AND (
          e.nom ILIKE $${paramIndex} OR
          e.prenom ILIKE $${paramIndex} OR
          e.email ILIKE $${paramIndex}
        )`;
        params.push(`%${filters.search}%`);
        paramIndex++;
      }

      // Ajouter le tri
      query += ' ORDER BY e.type_employe, e.nom, e.prenom';

      const result = await db.query(query, params);

      // Enrichir avec les informations de type
      const employees = result.rows.map(employee => {
        let typeInfo = null;
        if (employee.type_employe && EmployeeTypeService.isValidType(employee.type_employe)) {
          typeInfo = EmployeeTypeService.getTypeInfo(employee.type_employe);
        }

        return {
          ...employee,
          type_info: typeInfo
        };
      });

      return employees;
    } catch (error) {
      logger.error('Error getting employees with types:', error);
      throw error;
    }
  }

  /**
   * Assigner un type d'employé (utilise EmployeeTypeService)
   */
  static async assignEmployeeType(employeeId, employeeType, complexeId, servicesAutorises = null) {
    try {
      return await EmployeeTypeService.assignEmployeeType(employeeId, employeeType, complexeId, servicesAutorises);
    } catch (error) {
      logger.error('Error assigning employee type:', error);
      throw error;
    }
  }
}

module.exports = EmployeeService;