const Service = require('./Service');
const db = require('../db');
const logger = require('../logger');
const QRCode = require('qrcode');
const PDFDocument = require('pdfkit');
const { v4: uuidv4 } = require('uuid');

class TicketService extends Service {
  // Génération du QR code
  static async genererQRCode(data) {
    try {
      const qrCodeData = await QRCode.toDataURL(JSON.stringify(data));
      return this.successResponse({ qrCode: qrCodeData });
    } catch (error) {
      logger.error('Erreur génération QR code:', error);
      return this.rejectResponse('Erreur lors de la génération du QR code');
    }
  }

  // Vérification d'un ticket
  static async verifierTicket(numero) {
    try {
      const query = `
        SELECT t.*, r.*, c.nom as client_nom, c.prenom as client_prenom
        FROM Tickets t
        JOIN Reservations r ON t.reservation_id = r.reservation_id
        JOIN Clients c ON r.client_id = c.client_id
        WHERE t.numero_ticket = $1
      `;
      
      const result = await db.query(query, [numero]);
      
      if (result.rows.length === 0) {
        return this.rejectResponse('Ticket non trouvé', 404);
      }

      const ticket = result.rows[0];
      const now = new Date();

      // Vérifier si le ticket est actif
      if (ticket.statut !== 'ACTIF') {
        return this.successResponse({
          valide: false,
          raison: 'Ticket inactif',
          statut: ticket.statut
        });
      }

      // Vérifier si la réservation est en cours
      const dateArrivee = new Date(ticket.date_arrivee);
      const dateDepart = new Date(ticket.date_depart);

      if (now < dateArrivee) {
        return this.successResponse({
          valide: false,
          raison: 'Réservation pas encore commencée',
          date_arrivee: dateArrivee
        });
      }

      if (now > dateDepart) {
        return this.successResponse({
          valide: false,
          raison: 'Réservation terminée',
          date_depart: dateDepart
        });
      }

      return this.successResponse({
        valide: true,
        ticket: {
          numero_ticket: ticket.numero_ticket,
          client: `${ticket.client_nom} ${ticket.client_prenom}`,
          date_arrivee: ticket.date_arrivee,
          date_depart: ticket.date_depart,
          statut: ticket.statut
        }
      });
    } catch (error) {
      logger.error('Erreur vérification ticket:', error);
      return this.rejectResponse('Erreur lors de la vérification du ticket');
    }
  }

  // Création d'un ticket
  static async creerTicket(params) {
    const { reservation_id, utilisateur_id } = params;

    try {
      await db.query('BEGIN');

      // Vérifier si la réservation existe
      const reservationQuery = `
        SELECT r.*, c.nom as client_nom, c.prenom as client_prenom,
               ch.numero as numero_chambre, ch.type_chambre
        FROM Reservations r
        JOIN Clients c ON r.client_id = c.client_id
        JOIN ChambresReservees cr ON r.reservation_id = cr.reservation_id
        JOIN Chambres ch ON cr.chambre_id = ch.chambre_id
        WHERE r.reservation_id = $1
      `;
      
      const reservationResult = await db.query(reservationQuery, [reservation_id]);
      
      if (reservationResult.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Réservation non trouvée', 404);
      }

      const reservation = reservationResult.rows[0];

      // Générer le QR code
      const qrData = {
        numero_reservation: reservation.numero_reservation,
        client: `${reservation.client_nom} ${reservation.client_prenom}`,
        chambre: reservation.numero_chambre,
        date_arrivee: reservation.date_arrivee,
        date_depart: reservation.date_depart
      };

      const qrCode = await QRCode.toDataURL(JSON.stringify(qrData));

      // Générer le numéro de ticket
      const numero_ticket = `TICK-${uuidv4().slice(0, 8).toUpperCase()}`;

      // Créer le ticket
      const ticketQuery = `
        INSERT INTO Tickets (
          numero_ticket,
          reservation_id,
          qr_code_data,
          date_emission,
          employe_emission_id,
          statut
        ) VALUES ($1, $2, $3, NOW(), $4, 'ACTIF')
        RETURNING *
      `;

      const ticketResult = await db.query(ticketQuery, [
        numero_ticket,
        reservation_id,
        qrCode,
        utilisateur_id
      ]);

      await db.query('COMMIT');
      logger.info('Ticket créé', { 
        ticket_id: ticketResult.rows[0].ticket_id,
        numero_ticket
      });

      return this.successResponse({
        ticket: ticketResult.rows[0],
        qr_code: qrCode,
        reservation: {
          numero_reservation: reservation.numero_reservation,
          client: `${reservation.client_nom} ${reservation.client_prenom}`,
          chambre: reservation.numero_chambre,
          date_arrivee: reservation.date_arrivee,
          date_depart: reservation.date_depart
        }
      });
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur création ticket:', error);
      return this.rejectResponse('Erreur lors de la création du ticket');
    }
  }

  // Récupération des détails d'un ticket
  static async getTicketById(ticketId) {
    try {
      const query = `
        SELECT t.*, r.*, c.nom as client_nom, c.prenom as client_prenom,
               ch.numero as numero_chambre, ch.type_chambre,
               e.nom as employe_nom, e.prenom as employe_prenom
        FROM Tickets t
        JOIN Reservations r ON t.reservation_id = r.reservation_id
        JOIN Clients c ON r.client_id = c.client_id
        JOIN ChambresReservees cr ON r.reservation_id = cr.reservation_id
        JOIN Chambres ch ON cr.chambre_id = ch.chambre_id
        LEFT JOIN Employes e ON t.employe_emission_id = e.employe_id
        WHERE t.ticket_id = $1
      `;
      
      const result = await db.query(query, [ticketId]);
      
      if (result.rows.length === 0) {
        return this.rejectResponse('Ticket non trouvé', 404);
      }

      const ticket = result.rows[0];

      return this.successResponse({
        ticket_id: ticket.ticket_id,
        numero_ticket: ticket.numero_ticket,
        qr_code: ticket.qr_code_data,
        date_emission: ticket.date_emission,
        statut: ticket.statut,
        reservation: {
          numero_reservation: ticket.numero_reservation,
          client: `${ticket.client_nom} ${ticket.client_prenom}`,
          chambre: ticket.numero_chambre,
          type_chambre: ticket.type_chambre,
          date_arrivee: ticket.date_arrivee,
          date_depart: ticket.date_depart
        },
        employe: ticket.employe_nom ? {
          nom: ticket.employe_nom,
          prenom: ticket.employe_prenom
        } : null
      });
    } catch (error) {
      logger.error('Erreur récupération détails ticket:', error);
      return this.rejectResponse('Erreur lors de la récupération des détails du ticket');
    }
  }

  // Mise à jour du statut d'un ticket
  static async updateTicketStatus(ticketId, statut) {
    try {
      await db.query('BEGIN');

      // Vérifier si le ticket existe
      const checkQuery = `
        SELECT * FROM Tickets 
        WHERE ticket_id = $1
        FOR UPDATE
      `;
      
      const checkResult = await db.query(checkQuery, [ticketId]);
      
      if (checkResult.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Ticket non trouvé', 404);
      }

      const ticket = checkResult.rows[0];

      // Vérifier la validité du nouveau statut
      const statutsValides = ['ACTIF', 'UTILISE', 'ANNULE', 'EXPIRE'];
      if (!statutsValides.includes(statut)) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Statut invalide', 400);
      }

      // Mettre à jour le statut
      const updateQuery = `
        UPDATE Tickets 
        SET 
          statut = $1,
          updated_at = CURRENT_TIMESTAMP
        WHERE ticket_id = $2
        RETURNING *
      `;

      const updateResult = await db.query(updateQuery, [statut, ticketId]);

      await db.query('COMMIT');
      logger.info('Statut ticket mis à jour', {
        ticket_id: ticketId,
        ancien_statut: ticket.statut,
        nouveau_statut: statut
      });

      return this.successResponse(updateResult.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur mise à jour statut ticket:', error);
      return this.rejectResponse('Erreur lors de la mise à jour du statut du ticket');
    }
  }

  // Impression du ticket
  static async imprimerTicket(ticketId) {
    try {
      // Récupérer les informations du ticket
      const ticketQuery = `
        SELECT t.*, r.*, c.numero as numero_chambre
        FROM Tickets t
        JOIN Reservations r ON t.reservation_id = r.reservation_id
        JOIN ChambresReservees cr ON r.reservation_id = cr.reservation_id
        JOIN Chambres c ON cr.chambre_id = c.chambre_id
        WHERE t.ticket_id = $1
      `;
      
      const ticketResult = await db.query(ticketQuery, [ticketId]);
      
      if (ticketResult.rows.length === 0) {
        return this.rejectResponse('Ticket non trouvé', 404);
      }

      const ticket = ticketResult.rows[0];

      // Créer le PDF
      const doc = new PDFDocument();
      const pdfBuffer = [];

      doc.on('data', chunk => pdfBuffer.push(chunk));
      doc.on('end', () => {
        const pdfData = Buffer.concat(pdfBuffer);
        // Ici, vous pouvez implémenter la logique d'impression réelle
        // ou retourner le PDF pour le téléchargement
      });

      // Générer le contenu du PDF
      doc.fontSize(20).text('Ticket de Réservation', { align: 'center' });
      doc.moveDown();
      doc.fontSize(12).text(`Numéro de Chambre: ${ticket.numero_chambre}`);
      doc.text(`Date d'arrivée: ${new Date(ticket.date_arrivee).toLocaleDateString()}`);
      doc.text(`Date de départ: ${new Date(ticket.date_depart).toLocaleDateString()}`);
      doc.moveDown();
      doc.text(`Type de Ticket: ${ticket.type_ticket}`);
      doc.text(`Statut: ${ticket.statut}`);
      doc.moveDown();
      doc.image(ticket.qr_code, {
        fit: [200, 200],
        align: 'center'
      });

      doc.end();

      return this.successResponse({
        message: 'Ticket généré avec succès',
        ticket_id: ticketId
      });
    } catch (error) {
      logger.error('Erreur impression ticket:', error);
      return this.rejectResponse('Erreur lors de l\'impression du ticket');
    }
  }

  // Génération d'un ticket à partir d'une réservation
  static async genererTicketFromReservation(numero) {
    try {
      // Récupérer les informations de la réservation
      const query = `
        SELECT r.*, c.nom as client_nom, c.prenom as client_prenom,
               ch.numero as numero_chambre, ch.type_chambre
        FROM "Reservations" r
        JOIN "Clients" c ON r.client_id = c.client_id
        JOIN "ChambresReservees" cr ON r.reservation_id = cr.reservation_id
        JOIN "Chambres" ch ON cr.chambre_id = ch.chambre_id
        WHERE r.numero_reservation = $1
      `;

      const result = await db.query(query, [numero]);

      if (result.rows.length === 0) {
        return this.rejectResponse('Réservation non trouvée', 404);
      }

      const reservation = result.rows[0];

      // Générer le QR code
      const qrData = {
        numero_reservation: reservation.numero_reservation,
        client: `${reservation.client_nom} ${reservation.client_prenom}`,
        chambre: reservation.numero_chambre,
        date_arrivee: reservation.date_arrivee,
        date_depart: reservation.date_depart
      };

      const qrCode = await QRCode.toDataURL(JSON.stringify(qrData));

      // Créer le ticket
      const ticket = {
        numero_ticket: `TICK-${uuidv4().slice(0, 8).toUpperCase()}`,
        reservation_id: reservation.reservation_id,
        qr_code: qrCode,
        date_emission: new Date(),
        statut: 'valide'
      };

      // Enregistrer le ticket
      const insertTicket = `
        INSERT INTO "Tickets" (
          numero_ticket,
          reservation_id,
          qr_code_data,
          date_emission,
          statut
        ) VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `;

      const savedTicket = await db.query(insertTicket, [
        ticket.numero_ticket,
        ticket.reservation_id,
        ticket.qr_code,
        ticket.date_emission,
        ticket.statut
      ]);

      logger.info('Ticket généré', { numero_ticket: ticket.numero_ticket });

      return this.successResponse({
        ...savedTicket.rows[0],
        qr_code: ticket.qr_code
      });
    } catch (error) {
      logger.error('Erreur génération ticket:', error);
      return this.rejectResponse('Erreur lors de la génération du ticket');
    }
  }

  // ==================== MÉTHODES POUR TICKETS DE SERVICE ====================

  // Création d'un ticket de service (piscine, bar, restaurant)
  static async creerTicketService(ticketData) {
    const {
      service_id, pos_id, session_id, employe_id,
      nom_client, nombre_personnes, duree_heures,
      mode_paiement, prix_total
    } = ticketData;

    try {
      await db.query('BEGIN');

      // 1. Vérifier que le service existe et récupérer ses informations
      const serviceQuery = `
        SELECT s.*, sc.nom as complexe_nom
        FROM "ServicesComplexe" s
        JOIN "ComplexesHoteliers" sc ON s.complexe_id = sc.complexe_id
        WHERE s.service_id = $1 AND s.actif = true
      `;

      const serviceResult = await db.query(serviceQuery, [service_id]);

      if (serviceResult.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Service non trouvé ou inactif', 404);
      }

      const service = serviceResult.rows[0];

      // 2. Vérifier que la session de caisse est active
      const sessionQuery = `
        SELECT * FROM "SessionsCaisse"
        WHERE session_id = $1 AND statut = 'Ouverte'
      `;

      const sessionResult = await db.query(sessionQuery, [session_id]);

      if (sessionResult.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Session de caisse non trouvée ou fermée', 400);
      }

      // 3. Générer le numéro de ticket
      const numero_ticket = `SRV-${uuidv4().slice(0, 8).toUpperCase()}`;

      // 4. Créer les données QR avec date d'expiration
      const dateExpiration = new Date();
      if (duree_heures) {
        dateExpiration.setHours(dateExpiration.getHours() + duree_heures);
      } else {
        // Par défaut, expiration à la fin de la journée
        dateExpiration.setHours(23, 59, 59, 999);
      }

      const qrData = {
        type: 'SERVICE',
        service_id,
        service_nom: service.nom,
        service_type: service.type_service,
        nom_client,
        nombre_personnes,
        duree_heures,
        date_creation: new Date(),
        date_expiration: dateExpiration,
        numero_ticket
      };

      const qrCode = await QRCode.toDataURL(JSON.stringify(qrData));

      // 5. Insérer le ticket
      const ticketQuery = `
        INSERT INTO "Tickets" (
          type_ticket, service_id, pos_id, session_id,
          numero_ticket, employe_emission_id, nom_client,
          nombre_personnes, duree_heures, prix_total,
          mode_paiement, statut_paiement, qr_code_data,
          date_expiration, statut, date_emission
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, NOW())
        RETURNING *
      `;

      const ticketResult = await db.query(ticketQuery, [
        'SERVICE', service_id, pos_id, session_id,
        numero_ticket, employe_id, nom_client,
        nombre_personnes, duree_heures, prix_total,
        mode_paiement, 'Payé', qrCode,
        dateExpiration, 'ACTIF'
      ]);

      // 6. Mettre à jour la session de caisse
      await this.updateSessionCaisse(session_id, prix_total, mode_paiement);

      await db.query('COMMIT');

      logger.info('Ticket de service créé', {
        ticket_id: ticketResult.rows[0].ticket_id,
        numero_ticket,
        service_type: service.type_service,
        prix_total
      });

      return this.successResponse({
        ticket: ticketResult.rows[0],
        qr_code: qrCode,
        service: {
          nom: service.nom,
          type: service.type_service,
          complexe: service.complexe_nom
        },
        qr_data: qrData
      });
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur création ticket service:', error);
      return this.rejectResponse('Erreur lors de la création du ticket de service');
    }
  }

  // Mise à jour de la session de caisse après vente
  static async updateSessionCaisse(session_id, montant, mode_paiement) {
    try {
      const updateQuery = `
        UPDATE "SessionsCaisse"
        SET
          total_ventes = COALESCE(total_ventes, 0) + $1,
          ${mode_paiement === 'Espèces' ? 'total_especes' : 'total_cartes'} =
          COALESCE(${mode_paiement === 'Espèces' ? 'total_especes' : 'total_cartes'}, 0) + $1
        WHERE session_id = $2
      `;

      await db.query(updateQuery, [montant, session_id]);

      logger.info('Session de caisse mise à jour', {
        session_id,
        montant,
        mode_paiement
      });
    } catch (error) {
      logger.error('Erreur mise à jour session caisse:', error);
      throw error;
    }
  }

  // Récupération des tickets de service pour un service donné
  static async getServiceTickets(service_id, filters = {}) {
    try {
      let query = `
        SELECT t.*, s.nom as service_nom, s.type_service,
               e.nom as employe_nom, e.prenom as employe_prenom,
               p.nom as pos_nom, sc.session_id
        FROM "Tickets" t
        JOIN "ServicesComplexe" s ON t.service_id = s.service_id
        LEFT JOIN "Employes" e ON t.employe_emission_id = e.employe_id
        LEFT JOIN "PointsDeVente" p ON t.pos_id = p.pos_id
        LEFT JOIN "SessionsCaisse" sc ON t.session_id = sc.session_id
        WHERE t.type_ticket = 'SERVICE' AND t.service_id = $1
      `;

      const params = [service_id];

      if (filters.statut) {
        query += ` AND t.statut = $${params.length + 1}`;
        params.push(filters.statut);
      }

      if (filters.date_debut) {
        query += ` AND t.date_emission >= $${params.length + 1}`;
        params.push(filters.date_debut);
      }

      if (filters.date_fin) {
        query += ` AND t.date_emission <= $${params.length + 1}`;
        params.push(filters.date_fin);
      }

      query += ' ORDER BY t.date_emission DESC';

      const result = await db.query(query, params);

      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération tickets service:', error);
      return this.rejectResponse('Erreur lors de la récupération des tickets de service');
    }
  }

  // Marquer un ticket de service comme utilisé
  static async marquerTicketUtilise(ticketId) {
    try {
      await db.query('BEGIN');

      // Vérifier que le ticket existe et est de type SERVICE
      const checkQuery = `
        SELECT * FROM "Tickets"
        WHERE ticket_id = $1 AND type_ticket = 'SERVICE'
        FOR UPDATE
      `;

      const checkResult = await db.query(checkQuery, [ticketId]);

      if (checkResult.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Ticket de service non trouvé', 404);
      }

      const ticket = checkResult.rows[0];

      // Vérifier que le ticket n'est pas déjà utilisé
      if (ticket.statut === 'UTILISE') {
        await db.query('ROLLBACK');
        return this.rejectResponse('Ticket déjà utilisé', 400);
      }

      // Vérifier que le ticket n'est pas expiré
      if (ticket.date_expiration && new Date() > new Date(ticket.date_expiration)) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Ticket expiré', 400);
      }

      // Mettre à jour le statut
      const updateQuery = `
        UPDATE "Tickets"
        SET
          statut = 'UTILISE',
          updated_at = CURRENT_TIMESTAMP
        WHERE ticket_id = $1
        RETURNING *
      `;

      const updateResult = await db.query(updateQuery, [ticketId]);

      await db.query('COMMIT');

      logger.info('Ticket de service marqué comme utilisé', {
        ticket_id: ticketId,
        numero_ticket: ticket.numero_ticket
      });

      return this.successResponse(updateResult.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur marquage ticket utilisé:', error);
      return this.rejectResponse('Erreur lors du marquage du ticket comme utilisé');
    }
  }

  // Vérification d'un ticket de service
  static async verifierTicketService(numero) {
    try {
      const query = `
        SELECT t.*, s.nom as service_nom, s.type_service,
               e.nom as employe_nom, e.prenom as employe_prenom
        FROM "Tickets" t
        JOIN "ServicesComplexe" s ON t.service_id = s.service_id
        LEFT JOIN "Employes" e ON t.employe_emission_id = e.employe_id
        WHERE t.numero_ticket = $1 AND t.type_ticket = 'SERVICE'
      `;

      const result = await db.query(query, [numero]);

      if (result.rows.length === 0) {
        return this.rejectResponse('Ticket de service non trouvé', 404);
      }

      const ticket = result.rows[0];
      const now = new Date();

      // Vérifier si le ticket est actif
      if (ticket.statut !== 'ACTIF') {
        return this.successResponse({
          valide: false,
          raison: 'Ticket inactif',
          statut: ticket.statut
        });
      }

      // Vérifier si le ticket est expiré
      if (ticket.date_expiration && now > new Date(ticket.date_expiration)) {
        return this.successResponse({
          valide: false,
          raison: 'Ticket expiré',
          date_expiration: ticket.date_expiration
        });
      }

      return this.successResponse({
        valide: true,
        ticket: {
          numero_ticket: ticket.numero_ticket,
          nom_client: ticket.nom_client,
          nombre_personnes: ticket.nombre_personnes,
          duree_heures: ticket.duree_heures,
          service_nom: ticket.service_nom,
          service_type: ticket.type_service,
          date_emission: ticket.date_emission,
          date_expiration: ticket.date_expiration,
          statut: ticket.statut
        }
      });
    } catch (error) {
      logger.error('Erreur vérification ticket service:', error);
      return this.rejectResponse('Erreur lors de la vérification du ticket de service');
    }
  }

  // Récupération des tickets d'une session de caisse
  static async getTicketsBySession(sessionId) {
    try {
      const query = `
        SELECT t.*, s.nom as service_nom, s.type_service
        FROM "Tickets" t
        LEFT JOIN "ServicesComplexe" s ON t.service_id = s.service_id
        WHERE t.session_id = $1
        ORDER BY t.date_emission DESC
      `;

      const result = await db.query(query, [sessionId]);

      return this.successResponse(result.rows);
    } catch (error) {
      logger.error('Erreur récupération tickets session:', error);
      return this.rejectResponse('Erreur lors de la récupération des tickets de la session');
    }
  }
}

module.exports = TicketService;