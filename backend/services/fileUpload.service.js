const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const db = require('../db');
const logger = require('../logger');
const BaseService = require('./base.service');

class FileUploadService extends BaseService {
  
  /**
   * Configuration des types de fichiers autorisés
   */
  static ALLOWED_MIME_TYPES = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.ms-excel' // .xls
  ];

  static ALLOWED_EXTENSIONS = ['.xlsx', '.xls'];
  static MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  static UPLOAD_DIR = 'uploads/excel';
  static TEMP_DIR = 'uploads/temp';

  /**
   * Upload et validation initiale du fichier Excel
   */
  static async uploadExcelFile(file, metadata) {
    try {
      const { complexeId, serviceId, employeId, typeImport } = metadata;

      // Validation du fichier
      await this.validateFile(file);

      // Génération d'un nom de fichier sécurisé
      const fileName = this.generateSecureFileName(file.originalname);
      const filePath = path.join(this.UPLOAD_DIR, fileName);

      // Création du répertoire si nécessaire
      await this.ensureDirectoryExists(this.UPLOAD_DIR);

      // Déplacement du fichier vers le répertoire final
      await fs.rename(file.path, filePath);

      // Création de l'enregistrement d'import
      const importRecord = await this.createImportRecord({
        complexeId,
        serviceId,
        employeId,
        typeImport,
        nomFichier: file.originalname,
        cheminFichier: filePath,
        tailleFichier: file.size
      });

      logger.info(`File uploaded successfully: ${fileName}`, {
        importId: importRecord.import_id,
        originalName: file.originalname,
        size: file.size
      });

      return this.successResponse({
        importId: importRecord.import_id,
        fileName: fileName,
        originalName: file.originalname,
        size: file.size,
        status: 'uploaded'
      }, 'Fichier uploadé avec succès');

    } catch (error) {
      logger.error('Error uploading file:', error);
      
      // Nettoyage en cas d'erreur
      if (file.path) {
        try {
          await fs.unlink(file.path);
        } catch (cleanupError) {
          logger.error('Error cleaning up file:', cleanupError);
        }
      }
      
      throw error;
    }
  }

  /**
   * Validation approfondie du fichier
   */
  static async validateFile(file) {
    if (!file) {
      this.rejectResponse('Aucun fichier fourni', 400);
    }

    // Vérification de la taille
    if (file.size > this.MAX_FILE_SIZE) {
      this.rejectResponse(`Fichier trop volumineux. Taille maximale: ${this.MAX_FILE_SIZE / 1024 / 1024}MB`, 400);
    }

    // Vérification du type MIME
    if (!this.ALLOWED_MIME_TYPES.includes(file.mimetype)) {
      this.rejectResponse('Type de fichier non autorisé. Seuls les fichiers Excel (.xlsx, .xls) sont acceptés', 400);
    }

    // Vérification de l'extension
    const fileExtension = path.extname(file.originalname).toLowerCase();
    if (!this.ALLOWED_EXTENSIONS.includes(fileExtension)) {
      this.rejectResponse('Extension de fichier non autorisée', 400);
    }

    // Vérification de l'existence du fichier
    try {
      await fs.access(file.path);
    } catch (error) {
      this.rejectResponse('Fichier inaccessible', 400);
    }

    // Scan de sécurité basique - vérification que le fichier n'est pas vide
    const stats = await fs.stat(file.path);
    if (stats.size === 0) {
      this.rejectResponse('Le fichier est vide', 400);
    }

    return true;
  }

  /**
   * Génération d'un nom de fichier sécurisé
   */
  static generateSecureFileName(originalName) {
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(8).toString('hex');
    const extension = path.extname(originalName);
    const baseName = path.basename(originalName, extension)
      .replace(/[^a-zA-Z0-9]/g, '_')
      .substring(0, 50);
    
    return `${timestamp}_${randomString}_${baseName}${extension}`;
  }

  /**
   * Création du répertoire s'il n'existe pas
   */
  static async ensureDirectoryExists(dirPath) {
    try {
      await fs.access(dirPath);
    } catch (error) {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  /**
   * Création de l'enregistrement d'import en base
   */
  static async createImportRecord(data) {
    const {
      complexeId, serviceId, employeId, typeImport,
      nomFichier, cheminFichier, tailleFichier
    } = data;

    const result = await db.query(
      `INSERT INTO "ImportsExcel" (
        complexe_id, service_id, employe_id, type_import,
        nom_fichier, chemin_fichier, taille_fichier,
        statut, date_import
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, 'EN_COURS', NOW())
      RETURNING *`,
      [complexeId, serviceId, employeId, typeImport, nomFichier, cheminFichier, tailleFichier]
    );

    return result.rows[0];
  }

  /**
   * Nettoyage des fichiers temporaires
   */
  static async cleanupTempFiles(importId = null, olderThanDays = 7) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      let query = `
        SELECT import_id, chemin_fichier, statut, date_import 
        FROM "ImportsExcel" 
        WHERE date_import < $1
      `;
      let params = [cutoffDate];

      if (importId) {
        query += ' AND import_id = $2';
        params.push(importId);
      }

      const result = await db.query(query, params);
      let cleanedCount = 0;

      for (const record of result.rows) {
        try {
          // Supprimer le fichier physique
          await fs.unlink(record.chemin_fichier);
          
          // Marquer l'import comme nettoyé
          await db.query(
            'UPDATE "ImportsExcel" SET notes = $1 WHERE import_id = $2',
            [`Fichier nettoyé le ${new Date().toISOString()}`, record.import_id]
          );
          
          cleanedCount++;
        } catch (fileError) {
          logger.warn(`Could not delete file: ${record.chemin_fichier}`, fileError);
        }
      }

      logger.info(`Cleaned up ${cleanedCount} temporary files`);
      return cleanedCount;

    } catch (error) {
      logger.error('Error during cleanup:', error);
      throw error;
    }
  }

  /**
   * Obtenir les informations d'un import
   */
  static async getImportInfo(importId) {
    try {
      const result = await db.query(
        `SELECT ie.*, e.nom as employe_nom, e.prenom as employe_prenom,
                sc.nom as service_nom, ch.nom as complexe_nom
         FROM "ImportsExcel" ie
         LEFT JOIN "Employes" e ON ie.employe_id = e.employe_id
         LEFT JOIN "ServicesComplexe" sc ON ie.service_id = sc.service_id
         LEFT JOIN "ComplexesHoteliers" ch ON ie.complexe_id = ch.complexe_id
         WHERE ie.import_id = $1`,
        [importId]
      );

      if (result.rows.length === 0) {
        this.rejectResponse('Import non trouvé', 404);
      }

      return result.rows[0];
    } catch (error) {
      logger.error(`Error fetching import info ${importId}:`, error);
      throw error;
    }
  }

  /**
   * Mise à jour du statut d'un import
   */
  static async updateImportStatus(importId, statut, additionalData = {}) {
    try {
      const updateFields = ['statut = $2', 'updated_at = NOW()'];
      const params = [importId, statut];
      let paramIndex = 3;

      // Ajout des champs optionnels
      if (additionalData.nombreLignesTotal !== undefined) {
        updateFields.push(`nombre_lignes_total = $${paramIndex}`);
        params.push(additionalData.nombreLignesTotal);
        paramIndex++;
      }

      if (additionalData.nombreLignesValides !== undefined) {
        updateFields.push(`nombre_lignes_valides = $${paramIndex}`);
        params.push(additionalData.nombreLignesValides);
        paramIndex++;
      }

      if (additionalData.nombreErreurs !== undefined) {
        updateFields.push(`nombre_erreurs = $${paramIndex}`);
        params.push(additionalData.nombreErreurs);
        paramIndex++;
      }

      if (additionalData.donneesParsees) {
        updateFields.push(`donnees_parsees = $${paramIndex}`);
        params.push(JSON.stringify(additionalData.donneesParsees));
        paramIndex++;
      }

      if (additionalData.erreursDetectees) {
        updateFields.push(`erreurs_detectees = $${paramIndex}`);
        params.push(JSON.stringify(additionalData.erreursDetectees));
        paramIndex++;
      }

      if (statut === 'VALIDE') {
        updateFields.push(`date_traitement = NOW()`);
      } else if (statut === 'IMPORTE') {
        updateFields.push(`date_finalisation = NOW()`);
      }

      const query = `
        UPDATE "ImportsExcel" 
        SET ${updateFields.join(', ')}
        WHERE import_id = $1 
        RETURNING *
      `;

      const result = await db.query(query, params);

      if (result.rows.length === 0) {
        this.rejectResponse('Import non trouvé', 404);
      }

      return result.rows[0];
    } catch (error) {
      logger.error(`Error updating import status ${importId}:`, error);
      throw error;
    }
  }

  /**
   * Vérification de l'intégrité d'un fichier
   */
  static async verifyFileIntegrity(filePath) {
    try {
      const stats = await fs.stat(filePath);
      return {
        exists: true,
        size: stats.size,
        lastModified: stats.mtime,
        isReadable: true
      };
    } catch (error) {
      return {
        exists: false,
        error: error.message
      };
    }
  }
}

module.exports = FileUploadService;
