const db = require('../db');
const logger = require('../logger');
const POSStockIntegration = require('./posStockIntegration.service');

/**
 * Service de déduction automatique du stock
 * Gère la déduction automatique du stock lors des ventes avec gestion des erreurs et des rollbacks
 */
class AutoStockDeductionService {
  
  static successResponse(data) {
    return {
      success: true,
      data
    };
  }

  static rejectResponse(message, code = 500) {
    return {
      success: false,
      message,
      code
    };
  }

  /**
   * Déduire automatiquement le stock pour une transaction
   * @param {Object} transactionData - Données de la transaction
   * @returns {Promise<Object>} Résultat de la déduction
   */
  static async deductStockForTransaction(transactionData) {
    const client = await db.getClient();
    
    try {
      await client.query('BEGIN');

      const {
        transaction_id,
        lignes_transaction = [],
        service_id,
        complexe_id
      } = transactionData;

      // Vérifier que la transaction existe
      const transactionResult = await client.query(
        'SELECT * FROM "TransactionsPOS" WHERE transaction_id = $1 FOR UPDATE',
        [transaction_id]
      );

      if (transactionResult.rows.length === 0) {
        await client.query('ROLLBACK');
        return this.rejectResponse('Transaction non trouvée', 404);
      }

      const transaction = transactionResult.rows[0];
      const stockUpdates = [];
      const alertes = [];
      const mouvements = [];

      // Traiter chaque ligne de transaction
      for (const ligne of lignes_transaction) {
        const { produit_id, quantite } = ligne;

        // Vérifier la disponibilité des ingrédients
        const disponibiliteCheck = await POSStockIntegration.checkIngredientAvailability(
          produit_id, 
          quantite, 
          complexe_id
        );

        if (!disponibiliteCheck.success) {
          await client.query('ROLLBACK');
          return disponibiliteCheck;
        }

        // Si des ingrédients sont insuffisants, ajouter aux alertes
        if (disponibiliteCheck.data.alertes.length > 0) {
          alertes.push(...disponibiliteCheck.data.alertes);
        }

        // Déduire le stock pour chaque ingrédient
        for (const ingredient of disponibiliteCheck.data.ingredients_requis) {
          // Mettre à jour le stock
          await client.query(`
            UPDATE "Ingredients" 
            SET stock_actuel = stock_actuel - $1,
                updated_at = NOW()
            WHERE ingredient_id = $2
          `, [ingredient.quantite_necessaire, ingredient.ingredient_id]);

          // Créer un mouvement de stock
          const mouvementResult = await client.query(`
            INSERT INTO "MouvementsStock" (
              complexe_id,
              service_id,
              ingredient_id,
              type_mouvement,
              quantite,
              unite_mesure,
              reference_transaction,
              notes,
              created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
            RETURNING *
          `, [
            complexe_id,
            service_id,
            ingredient.ingredient_id,
            'SORTIE_VENTE',
            -ingredient.quantite_necessaire,
            ingredient.unite_mesure,
            `TRANSACTION_${transaction_id}`,
            `Vente produit: ${quantite} unité(s)`
          ]);

          mouvements.push(mouvementResult.rows[0]);
        }

        stockUpdates.push({
          produit_id,
          quantite,
          ingredients: disponibiliteCheck.data.ingredients_requis
        });
      }

      // Mettre à jour le statut de la transaction
      await client.query(`
        UPDATE "TransactionsPOS" 
        SET statut = $1,
            stock_deduit = true,
            updated_at = NOW()
        WHERE transaction_id = $2
      `, ['Stock_Deduit', transaction_id]);

      await client.query('COMMIT');

      logger.info('Stock déduit pour transaction', {
        transaction_id,
        produits_traites: stockUpdates.length,
        mouvements_crees: mouvements.length,
        alertes_generees: alertes.length
      });

      return this.successResponse({
        transaction_id,
        statut: 'Stock_Deduit',
        stock_updates: stockUpdates,
        mouvements,
        alertes,
        message: 'Stock déduit avec succès'
      });

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Erreur déduction stock transaction:', error);
      return this.rejectResponse('Erreur lors de la déduction du stock');
    } finally {
      client.release();
    }
  }

  /**
   * Restaurer le stock pour une transaction annulée
   * @param {number} transactionId - ID de la transaction
   * @returns {Promise<Object>} Résultat de la restauration
   */
  static async restoreStockForCancelledTransaction(transactionId) {
    const client = await db.getClient();
    
    try {
      await client.query('BEGIN');

      // Récupérer les mouvements de stock de la transaction
      const mouvementsResult = await client.query(`
        SELECT ms.*, i.nom as ingredient_nom
        FROM "MouvementsStock" ms
        JOIN "Ingredients" i ON ms.ingredient_id = i.ingredient_id
        WHERE ms.reference_transaction = $1
        AND ms.type_mouvement = 'SORTIE_VENTE'
      `, [`TRANSACTION_${transactionId}`]);

      if (mouvementsResult.rows.length === 0) {
        await client.query('ROLLBACK');
        return this.rejectResponse('Aucun mouvement de stock trouvé pour cette transaction', 404);
      }

      const mouvementsRestores = [];

      // Restaurer le stock pour chaque mouvement
      for (const mouvement of mouvementsResult.rows) {
        // Restaurer le stock
        await client.query(`
          UPDATE "Ingredients"
          SET stock_actuel = stock_actuel + $1,
              updated_at = NOW()
          WHERE ingredient_id = $2
        `, [Math.abs(mouvement.quantite), mouvement.ingredient_id]);

        // Créer un mouvement de restauration
        const mouvementRestoreResult = await client.query(`
          INSERT INTO "MouvementsStock" (
            complexe_id,
            service_id,
            ingredient_id,
            type_mouvement,
            quantite,
            unite_mesure,
            reference_transaction,
            notes,
            created_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
          RETURNING *
        `, [
          mouvement.complexe_id,
          mouvement.service_id,
          mouvement.ingredient_id,
          'ENTREE_ANNULATION',
          Math.abs(mouvement.quantite),
          mouvement.unite_mesure,
          `ANNULATION_${transactionId}`,
          `Restauration stock pour annulation transaction ${transactionId}`
        ]);

        mouvementsRestores.push(mouvementRestoreResult.rows[0]);
      }

      // Mettre à jour le statut de la transaction
      await client.query(`
        UPDATE "TransactionsPOS"
        SET statut = $1,
            stock_restore = true,
            updated_at = NOW()
        WHERE transaction_id = $2
      `, ['Stock_Restore', transactionId]);

      await client.query('COMMIT');

      logger.info('Stock restauré pour transaction annulée', {
        transaction_id: transactionId,
        mouvements_restores: mouvementsRestores.length
      });

      return this.successResponse({
        transaction_id: transactionId,
        statut: 'Stock_Restore',
        mouvements_restores: mouvementsRestores,
        message: 'Stock restauré avec succès'
      });

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Erreur restauration stock transaction:', error);
      return this.rejectResponse('Erreur lors de la restauration du stock');
    } finally {
      client.release();
    }
  }

  /**
   * Vérifier si le stock a été déduit pour une transaction
   * @param {number} transactionId - ID de la transaction
   * @returns {Promise<Object>} Statut de la déduction
   */
  static async checkTransactionStockStatus(transactionId) {
    try {
      const result = await db.query(`
        SELECT 
          t.transaction_id,
          t.statut,
          t.stock_deduit,
          t.stock_restore,
          COUNT(ms.mouvement_id) as nombre_mouvements,
          SUM(CASE WHEN ms.type_mouvement = 'SORTIE_VENTE' THEN 1 ELSE 0 END) as mouvements_sortie,
          SUM(CASE WHEN ms.type_mouvement = 'ENTREE_ANNULATION' THEN 1 ELSE 0 END) as mouvements_entree
        FROM "TransactionsPOS" t
        LEFT JOIN "MouvementsStock" ms ON ms.reference_transaction IN (
          'TRANSACTION_' || t.transaction_id,
          'ANNULATION_' || t.transaction_id
        )
        WHERE t.transaction_id = $1
        GROUP BY t.transaction_id, t.statut, t.stock_deduit, t.stock_restore
      `, [transactionId]);

      if (result.rows.length === 0) {
        return this.rejectResponse('Transaction non trouvée', 404);
      }

      const status = result.rows[0];

      return this.successResponse({
        transaction_id: transactionId,
        statut: status.statut,
        stock_deduit: status.stock_deduit,
        stock_restore: status.stock_restore,
        mouvements: {
          total: parseInt(status.nombre_mouvements) || 0,
          sorties: parseInt(status.mouvements_sortie) || 0,
          entrees: parseInt(status.mouvements_entree) || 0
        }
      });

    } catch (error) {
      logger.error('Erreur vérification statut stock transaction:', error);
      return this.rejectResponse('Erreur lors de la vérification du statut du stock');
    }
  }
}

module.exports = AutoStockDeductionService; 