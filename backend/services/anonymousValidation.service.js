const logger = require('../logger');

/**
 * Service de validation spécifique aux réservations anonymes
 * Contient toutes les règles de validation pour les données anonymes
 */
class AnonymousValidationService {

  /**
   * Valider les paramètres de création d'une réservation anonyme
   * @param {Object} params - Paramètres à valider
   * @returns {Object} Résultat de la validation
   */
  static validateAnonymousReservationParams(params) {
    const errors = [];
    const {
      date_arrivee,
      date_depart,
      heure_debut,
      heure_fin,
      chambres,
      pseudonyme,
      commentaires,
      prix_total,
      complexe_id
    } = params;

    // Validation des dates
    if (!date_arrivee) {
      errors.push('La date d\'arrivée est requise');
    } else if (!this.isValidDate(date_arrivee)) {
      errors.push('Format de date d\'arrivée invalide (YYYY-MM-DD attendu)');
    }

    if (!date_depart) {
      errors.push('La date de départ est requise');
    } else if (!this.isValidDate(date_depart)) {
      errors.push('Format de date de départ invalide (YYYY-MM-DD attendu)');
    }

    // Validation que la date de départ est après la date d'arrivée
    if (date_arrivee && date_depart) {
      const arrivee = new Date(date_arrivee);
      const depart = new Date(date_depart);
      
      if (depart <= arrivee) {
        errors.push('La date de départ doit être après la date d\'arrivée');
      }

      // Vérifier que la date d'arrivée n'est pas dans le passé
      const aujourd_hui = new Date();
      aujourd_hui.setHours(0, 0, 0, 0);
      
      if (arrivee < aujourd_hui) {
        errors.push('La date d\'arrivée ne peut pas être dans le passé');
      }

      // Vérifier que la réservation n'est pas trop loin dans le futur (1 an max)
      const unAnPlus = new Date();
      unAnPlus.setFullYear(unAnPlus.getFullYear() + 1);
      
      if (arrivee > unAnPlus) {
        errors.push('La date d\'arrivée ne peut pas être à plus d\'un an dans le futur');
      }
    }

    // Validation des heures
    if (!heure_debut) {
      errors.push('L\'heure de début est requise');
    } else if (!this.isValidTime(heure_debut)) {
      errors.push('Format d\'heure de début invalide (HH:MM attendu)');
    }

    if (!heure_fin) {
      errors.push('L\'heure de fin est requise');
    } else if (!this.isValidTime(heure_fin)) {
      errors.push('Format d\'heure de fin invalide (HH:MM attendu)');
    }

    // Validation que l'heure de fin est après l'heure de début
    if (heure_debut && heure_fin && date_arrivee === date_depart) {
      if (this.timeToMinutes(heure_fin) <= this.timeToMinutes(heure_debut)) {
        errors.push('L\'heure de fin doit être après l\'heure de début pour une réservation sur la même journée');
      }
    }

    // Validation des chambres
    if (!chambres || !Array.isArray(chambres) || chambres.length === 0) {
      errors.push('Au moins une chambre doit être sélectionnée');
    } else {
      chambres.forEach((chambre, index) => {
        if (!chambre.chambre_id) {
          errors.push(`Chambre ${index + 1}: ID de chambre manquant`);
        }
        if (!chambre.type_chambre) {
          errors.push(`Chambre ${index + 1}: Type de chambre manquant`);
        }
        if (!chambre.prix_nuit || chambre.prix_nuit <= 0) {
          errors.push(`Chambre ${index + 1}: Prix invalide`);
        }
      });

      // Vérifier qu'il n'y a pas de doublons
      const chambreIds = chambres.map(c => c.chambre_id);
      const uniqueIds = [...new Set(chambreIds)];
      if (chambreIds.length !== uniqueIds.length) {
        errors.push('Des chambres en double ont été sélectionnées');
      }
    }

    // Validation du pseudonyme (optionnel mais si fourni, doit être valide)
    if (pseudonyme !== undefined && pseudonyme !== null) {
      const pseudonymeValidation = this.validatePseudonyme(pseudonyme);
      if (!pseudonymeValidation.valid) {
        errors.push(`Pseudonyme invalide: ${pseudonymeValidation.error}`);
      }
    }

    // Validation des commentaires (optionnel)
    if (commentaires && typeof commentaires !== 'string') {
      errors.push('Les commentaires doivent être une chaîne de caractères');
    } else if (commentaires && commentaires.length > 1000) {
      errors.push('Les commentaires ne peuvent pas dépasser 1000 caractères');
    }

    // Validation du prix total
    if (!prix_total || typeof prix_total !== 'number' || prix_total <= 0) {
      errors.push('Le prix total doit être un nombre positif');
    } else if (prix_total > 10000) {
      errors.push('Le prix total semble anormalement élevé (maximum 10000FCFA)');
    }

    // Validation du complexe ID
    if (!complexe_id || typeof complexe_id !== 'number' || complexe_id <= 0) {
      errors.push('ID de complexe invalide');
    }

    return {
      valid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * Valider un pseudonyme
   * @param {string} pseudonyme - Pseudonyme à valider
   * @returns {Object} Résultat de la validation
   */
  static validatePseudonyme(pseudonyme) {
    if (!pseudonyme || typeof pseudonyme !== 'string') {
      return { valid: false, error: 'Le pseudonyme doit être une chaîne de caractères' };
    }

    // Nettoyer le pseudonyme
    const cleaned = pseudonyme.trim();

    if (cleaned.length === 0) {
      return { valid: false, error: 'Le pseudonyme ne peut pas être vide' };
    }

    if (cleaned.length < 2) {
      return { valid: false, error: 'Le pseudonyme doit contenir au moins 2 caractères' };
    }

    if (cleaned.length > 50) {
      return { valid: false, error: 'Le pseudonyme ne peut pas dépasser 50 caractères' };
    }

    // Vérifier les caractères autorisés (lettres, chiffres, espaces, tirets, underscores)
    const allowedPattern = /^[a-zA-Z0-9\s\-_àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]+$/;
    if (!allowedPattern.test(cleaned)) {
      return { valid: false, error: 'Le pseudonyme contient des caractères non autorisés' };
    }

    // Vérifier qu'il ne contient pas que des espaces ou des caractères spéciaux
    const contentPattern = /[a-zA-Z0-9àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]/;
    if (!contentPattern.test(cleaned)) {
      return { valid: false, error: 'Le pseudonyme doit contenir au moins une lettre ou un chiffre' };
    }

    // Vérifier qu'il ne ressemble pas à un email ou un numéro de téléphone
    if (cleaned.includes('@') || /^\+?[\d\s\-\(\)]{8,}$/.test(cleaned)) {
      return { valid: false, error: 'Le pseudonyme ne peut pas ressembler à un email ou un numéro de téléphone' };
    }

    // Liste de mots interdits (insultes, mots inappropriés, etc.)
    const forbiddenWords = [
      'admin', 'administrator', 'root', 'system', 'null', 'undefined',
      'test', 'demo', 'example', 'sample', 'anonymous', 'guest'
    ];

    const lowerCleaned = cleaned.toLowerCase();
    for (const word of forbiddenWords) {
      if (lowerCleaned.includes(word)) {
        return { valid: false, error: 'Ce pseudonyme n\'est pas autorisé' };
      }
    }

    return { 
      valid: true, 
      cleaned: cleaned,
      original: pseudonyme 
    };
  }

  /**
   * Valider un code d'accès anonyme
   * @param {string} codeAcces - Code d'accès à valider
   * @returns {Object} Résultat de la validation
   */
  static validateAccessCode(codeAcces) {
    if (!codeAcces || typeof codeAcces !== 'string') {
      return { valid: false, error: 'Le code d\'accès doit être une chaîne de caractères' };
    }

    const cleaned = codeAcces.trim().toUpperCase();

    // Vérifier le format général (PREFIXE-XXXXXXXXXX)
    const formatPattern = /^[A-Z]{3,10}-[A-Z0-9]{8,16}$/;
    if (!formatPattern.test(cleaned)) {
      return { valid: false, error: 'Format de code d\'accès invalide' };
    }

    // Vérifier que le préfixe est valide
    const [prefixe, code] = cleaned.split('-');
    const validPrefixes = ['ANON', 'TEST', 'DEMO'];
    
    // En production, on pourrait être plus strict sur les préfixes
    if (prefixe.length < 3 || prefixe.length > 10) {
      return { valid: false, error: 'Préfixe de code d\'accès invalide' };
    }

    // Vérifier que la partie code ne contient que des caractères alphanumériques
    if (!/^[A-Z0-9]+$/.test(code)) {
      return { valid: false, error: 'Le code contient des caractères invalides' };
    }

    return { 
      valid: true, 
      cleaned: cleaned,
      prefixe: prefixe,
      code: code 
    };
  }

  /**
   * Valider les paramètres de modification d'une réservation anonyme
   * @param {Object} params - Paramètres à valider
   * @returns {Object} Résultat de la validation
   */
  static validateUpdateParams(params) {
    const errors = [];
    const allowedFields = ['commentaires'];

    // Vérifier que seuls les champs autorisés sont fournis
    const providedFields = Object.keys(params);
    const invalidFields = providedFields.filter(field => !allowedFields.includes(field));
    
    if (invalidFields.length > 0) {
      errors.push(`Champs non modifiables: ${invalidFields.join(', ')}`);
    }

    // Valider les commentaires si fournis
    if (params.commentaires !== undefined) {
      if (typeof params.commentaires !== 'string') {
        errors.push('Les commentaires doivent être une chaîne de caractères');
      } else if (params.commentaires.length > 1000) {
        errors.push('Les commentaires ne peuvent pas dépasser 1000 caractères');
      }
    }

    return {
      valid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * Vérifier si une date est valide
   * @param {string} dateString - Date au format YYYY-MM-DD
   * @returns {boolean} True si la date est valide
   */
  static isValidDate(dateString) {
    const regex = /^\d{4}-\d{2}-\d{2}$/;
    if (!regex.test(dateString)) return false;

    const date = new Date(dateString);
    const [year, month, day] = dateString.split('-').map(Number);
    
    return date.getFullYear() === year &&
           date.getMonth() === month - 1 &&
           date.getDate() === day;
  }

  /**
   * Vérifier si une heure est valide
   * @param {string} timeString - Heure au format HH:MM
   * @returns {boolean} True si l'heure est valide
   */
  static isValidTime(timeString) {
    const regex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return regex.test(timeString);
  }

  /**
   * Convertir une heure en minutes depuis minuit
   * @param {string} timeString - Heure au format HH:MM
   * @returns {number} Nombre de minutes depuis minuit
   */
  static timeToMinutes(timeString) {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Nettoyer et valider les données d'entrée
   * @param {Object} data - Données à nettoyer
   * @returns {Object} Données nettoyées
   */
  static sanitizeInput(data) {
    const sanitized = {};

    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'string') {
        // Nettoyer les chaînes de caractères
        sanitized[key] = value.trim();
        
        // Supprimer les caractères de contrôle
        sanitized[key] = sanitized[key].replace(/[\x00-\x1F\x7F]/g, '');
        
        // Limiter la longueur pour éviter les attaques
        if (sanitized[key].length > 1000) {
          sanitized[key] = sanitized[key].substring(0, 1000);
        }
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /**
   * Valider les limites de taux pour éviter les abus
   * @param {string} identifier - Identifiant (IP, session, etc.)
   * @param {string} action - Action effectuée
   * @returns {Object} Résultat de la validation
   */
  static validateRateLimit(identifier, action) {
    // Cette méthode pourrait être étendue pour implémenter
    // une logique de rate limiting plus sophistiquée
    
    const limits = {
      'CREATE_RESERVATION': { max: 5, window: 3600 }, // 5 créations par heure
      'CONSULTATION': { max: 50, window: 3600 }, // 50 consultations par heure
      'MODIFICATION': { max: 10, window: 3600 }, // 10 modifications par heure
      'ANNULATION': { max: 3, window: 3600 } // 3 annulations par heure
    };

    const limit = limits[action];
    if (!limit) {
      return { valid: true };
    }

    // Ici, on pourrait implémenter une vérification en base de données
    // ou en cache (Redis) pour compter les actions récentes
    
    return { 
      valid: true, 
      limit: limit,
      message: `Limite: ${limit.max} ${action} par ${limit.window/3600}h`
    };
  }
}

module.exports = AnonymousValidationService;
