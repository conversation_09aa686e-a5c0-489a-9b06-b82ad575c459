const Service = require('./Service');
const db = require('../db');
const logger = require('../logger');

class NotificationService extends Service {
  // Récupération de la liste des notifications
  static async getNotifications(params) {
    const { destinataire_id, type, date_debut, date_fin, page = 1, limit = 10 } = params;
    const offset = (page - 1) * limit;

    try {
      let query = `
        SELECT n.*, 
               e.nom as emetteur_nom, e.prenom as emetteur_prenom,
               d.nom as destinataire_nom, d.prenom as destinataire_prenom
        FROM "Notifications" n
        LEFT JOIN Utilisateurs e ON n.emetteur_id = e.utilisateur_id
        LEFT JOIN Utilisateurs d ON n.destinataire_id = d.utilisateur_id
        WHERE 1=1
      `;
      const queryParams = [];

      if (destinataire_id) {
        queryParams.push(destinataire_id);
        query += ` AND n.destinataire_id = $${queryParams.length}`;
      }

      if (type) {
        queryParams.push(type);
        query += ` AND n.type = $${queryParams.length}`;
      }

      if (date_debut) {
        queryParams.push(date_debut);
        query += ` AND n.created_at >= $${queryParams.length}`;
      }

      if (date_fin) {
        queryParams.push(date_fin);
        query += ` AND n.created_at <= $${queryParams.length}`;
      }

      // Ajouter le tri et la pagination
      query += `
        ORDER BY n.created_at DESC
        LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
      `;
      queryParams.push(limit, offset);

      const result = await db.query(query, queryParams);

      // Récupérer le nombre total de notifications
      const countQuery = `
        SELECT COUNT(*) as total
        FROM "Notifications" n
        WHERE 1=1
        ${destinataire_id ? 'AND n.destinataire_id = $1' : ''}
        ${type ? `AND n.type = $${destinataire_id ? '2' : '1'}` : ''}
        ${date_debut ? `AND n.created_at >= $${destinataire_id && type ? '3' : destinataire_id || type ? '2' : '1'}` : ''}
        ${date_fin ? `AND n.created_at <= $${destinataire_id && type && date_debut ? '4' : (destinataire_id && type) || (destinataire_id && date_debut) || (type && date_debut) ? '3' : (destinataire_id || type || date_debut) ? '2' : '1'}` : ''}
      `;

      const countResult = await db.query(countQuery, 
        [destinataire_id, type, date_debut, date_fin].filter(Boolean)
      );

      return this.successResponse({
        notifications: result.rows,
        pagination: {
          total: parseInt(countResult.rows[0].total),
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(parseInt(countResult.rows[0].total) / limit)
        }
      });
    } catch (error) {
      logger.error('Erreur récupération notifications:', error);
      return this.rejectResponse('Erreur lors de la récupération des notifications');
    }
  }

  // Création d'une notification
  static async createNotification(params) {
    const { titre, message, type, destinataire_id, emetteur_id } = params;

    try {
      await db.query('BEGIN');

      const query = `
        INSERT INTO "Notifications" (
          titre,
          message,
          type,
          destinataire_id,
          emetteur_id,
          created_at,
          lu
        ) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, false)
        RETURNING *
      `;

      const result = await db.query(query, [
        titre,
        message,
        type,
        destinataire_id,
        emetteur_id
      ]);

      await db.query('COMMIT');
      logger.info('Notification créée', { 
        notification_id: result.rows[0].notification_id,
        type,
        destinataire_id
      });

      return this.successResponse(result.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur création notification:', error);
      return this.rejectResponse('Erreur lors de la création de la notification');
    }
  }

  // Marquer une notification comme lue
  static async markAsRead(notificationId) {
    try {
      await db.query('BEGIN');

      // Vérifier si la notification existe
      const checkQuery = `
        SELECT * FROM "Notifications" 
        WHERE notification_id = $1
        FOR UPDATE
      `;
      
      const checkResult = await db.query(checkQuery, [notificationId]);
      
      if (checkResult.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Notification non trouvée', 404);
      }

      // Mettre à jour le statut
      const updateQuery = `
        UPDATE "Notifications" 
        SET 
          lu = true,
          date_lecture = CURRENT_TIMESTAMP
        WHERE notification_id = $1
        RETURNING *
      `;

      const updateResult = await db.query(updateQuery, [notificationId]);

      await db.query('COMMIT');
      logger.info('Notification marquée comme lue', {
        notification_id: notificationId
      });

      return this.successResponse(updateResult.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur marquage notification comme lue:', error);
      return this.rejectResponse('Erreur lors du marquage de la notification comme lue');
    }
  }

  // Récupération des notifications non lues
  static async getUnreadNotifications(params) {
    const { destinataire_id, type, page = 1, limit = 10 } = params;
    const offset = (page - 1) * limit;

    try {
      let query = `
        SELECT n.*, 
               e.nom as emetteur_nom, e.prenom as emetteur_prenom
        FROM "Notifications" n
        LEFT JOIN Utilisateurs e ON n.emetteur_id = e.utilisateur_id
        WHERE n.destinataire_id = $1
        AND n.lu = false
      `;
      const queryParams = [destinataire_id];

      if (type) {
        queryParams.push(type);
        query += ` AND n.type = $${queryParams.length}`;
      }

      // Ajouter le tri et la pagination
      query += `
        ORDER BY n.created_at DESC
        LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
      `;
      queryParams.push(limit, offset);

      const result = await db.query(query, queryParams);

      // Récupérer le nombre total de notifications non lues
      const countQuery = `
        SELECT COUNT(*) as total
        FROM "Notifications" n
        WHERE n.destinataire_id = $1
        AND n.lu = false
        ${type ? 'AND n.type = $2' : ''}
      `;

      const countResult = await db.query(countQuery, 
        [destinataire_id, type].filter(Boolean)
      );

      return this.successResponse({
        notifications: result.rows,
        pagination: {
          total: parseInt(countResult.rows[0].total),
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(parseInt(countResult.rows[0].total) / limit)
        }
      });
    } catch (error) {
      logger.error('Erreur récupération notifications non lues:', error);
      return this.rejectResponse('Erreur lors de la récupération des notifications non lues');
    }
  }
}

module.exports = NotificationService; 