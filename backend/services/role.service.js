const db = require('../db');
const logger = require('../logger');
const PermissionService = require('./permission.service');
const EmployeeTypeService = require('./employeeType.service');

/**
 * Service pour gérer les rôles
 * Phase 2 : Adaptation au système de permissions simplifié
 */
class RoleService {
  /**
   * Créer un rôle avec support du nouveau système de permissions
   */
  static async createRole(roleData) {
    try {
      const { nom, description, permissions, complexe_id, is_predefined = false } = roleData;

      // Vérifier si le rôle existe déjà dans le complexe
      const existingRole = await db.query(
        'SELECT * FROM "RolesComplexe" WHERE nom = $1 AND complexe_id = $2',
        [nom, complexe_id]
      );

      if (existingRole.rows.length > 0) {
        throw new Error('Un rôle avec ce nom existe déjà dans ce complexe');
      }

      // Convertir les permissions en JSON string si c'est un tableau
      const permissionsJson = Array.isArray(permissions)
        ? JSON.stringify(permissions)
        : permissions;

      const query = `
        INSERT INTO "RolesComplexe" (
          nom, description, permissions, complexe_id, created_at
        )
        VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
        RETURNING *
      `;

      const result = await db.query(query, [
        nom,
        description,
        permissionsJson,
        complexe_id
      ]);

      // Parser les permissions pour le retour
      const role = result.rows[0];
      if (role.permissions && typeof role.permissions === 'string') {
        role.permissions = JSON.parse(role.permissions);
      }

      return role;
    } catch (error) {
      logger.error('Error creating role:', error);
      throw error;
    }
  }

  static async getRoleById(roleId) {
    try {
      const query = `
        SELECT * FROM "RolesComplexe"
        WHERE role_id = $1
      `;
      const result = await db.query(query, [roleId]);

      const role = result.rows[0];
      if (role && role.permissions && typeof role.permissions === 'string') {
        role.permissions = JSON.parse(role.permissions);
      }

      return role;
    } catch (error) {
      logger.error('Error getting role:', error);
      throw error;
    }
  }

  static async getRolesByComplexe(complexeId) {
    try {
      const query = `
        SELECT * FROM "RolesComplexe"
        WHERE complexe_id = $1
        ORDER BY nom
      `;
      const result = await db.query(query, [complexeId]);

      // Parser les permissions pour chaque rôle
      const roles = result.rows.map(role => {
        if (role.permissions && typeof role.permissions === 'string') {
          role.permissions = JSON.parse(role.permissions);
        }
        return role;
      });

      return roles;
    } catch (error) {
      logger.error('Error getting roles:', error);
      throw error;
    }
  }

  static async updateRole(roleId, updateData) {
    try {
      const { nom, description, permissions } = updateData;

      // Convertir les permissions en JSON string si c'est un tableau
      const permissionsJson = permissions && Array.isArray(permissions)
        ? JSON.stringify(permissions)
        : permissions;

      const query = `
        UPDATE "RolesComplexe"
        SET
          nom = COALESCE($1, nom),
          description = COALESCE($2, description),
          permissions = COALESCE($3, permissions),
          updated_at = CURRENT_TIMESTAMP
        WHERE role_id = $4
        RETURNING *
      `;

      const result = await db.query(query, [
        nom,
        description,
        permissionsJson,
        roleId
      ]);

      // Parser les permissions pour le retour
      const role = result.rows[0];
      if (role && role.permissions && typeof role.permissions === 'string') {
        role.permissions = JSON.parse(role.permissions);
      }

      return role;
    } catch (error) {
      logger.error('Error updating role:', error);
      throw error;
    }
  }

  static async deleteRole(roleId) {
    try {
      // Vérifier si le rôle est utilisé par des employés
      const employeesWithRole = await db.query(
        'SELECT COUNT(*) FROM "Employes" WHERE role_id = $1',
        [roleId]
      );

      if (employeesWithRole.rows[0].count > 0) {
        throw new Error('Ce rôle est actuellement attribué à des employés');
      }

      const query = `
        DELETE FROM "RolesComplexe"
        WHERE role_id = $1
        RETURNING role_id
      `;

      const result = await db.query(query, [roleId]);
      return result.rows[0];
    } catch (error) {
      logger.error('Error deleting role:', error);
      throw error;
    }
  }

  static async assignRoleToEmployee(employeeId, roleId) {
    try {
      const query = `
        UPDATE "Employes"
        SET role_id = $1, updated_at = CURRENT_TIMESTAMP
        WHERE employe_id = $2
        RETURNING *
      `;

      const result = await db.query(query, [roleId, employeeId]);
      return result.rows[0];
    } catch (error) {
      logger.error('Error assigning role to employee:', error);
      throw error;
    }
  }

  static async getEmployeePermissions(employeeId) {
    try {
      const query = `
        SELECT r.permissions
        FROM "Employes" e
        JOIN "RolesComplexe" r ON e.role_id = r.role_id
        WHERE e.employe_id = $1
      `;
      const result = await db.query(query, [employeeId]);

      if (result.rows.length === 0) {
        return [];
      }

      const permissions = result.rows[0].permissions;
      // Parser les permissions si elles sont en format string JSON
      if (typeof permissions === 'string') {
        return JSON.parse(permissions);
      }

      return permissions || [];
    } catch (error) {
      logger.error('Error getting employee permissions:', error);
      throw error;
    }
  }

  /**
   * Créer les rôles prédéfinis pour un complexe selon le nouveau système
   * Phase 2 : Support du système simplifié
   */
  static async createPredefinedRolesForComplex(complexeId) {
    try {
      const predefinedRoles = [
        {
          nom: 'Employé Réception',
          description: 'Rôle prédéfini pour les employés de réception',
          permissions: ['reception_operations'],
          complexe_id: complexeId,
          is_predefined: true
        },
        {
          nom: 'Gérant Piscine',
          description: 'Rôle prédéfini pour les gérants de piscine',
          permissions: ['piscine_operations'],
          complexe_id: complexeId,
          is_predefined: true
        },
        {
          nom: 'Serveuse',
          description: 'Rôle prédéfini pour les serveuses',
          permissions: ['service_operations'],
          complexe_id: complexeId,
          is_predefined: true
        },
        {
          nom: 'Gérant Services',
          description: 'Rôle prédéfini pour les gérants de services',
          permissions: ['service_operations', 'management_operations'],
          complexe_id: complexeId,
          is_predefined: true
        },
        {
          nom: 'Employé Cuisine',
          description: 'Rôle prédéfini pour les employés de cuisine',
          permissions: ['kitchen_operations'],
          complexe_id: complexeId,
          is_predefined: true
        }
      ];

      const createdRoles = [];
      for (const roleData of predefinedRoles) {
        try {
          // Vérifier si le rôle existe déjà
          const existingRole = await db.query(
            'SELECT * FROM "RolesComplexe" WHERE nom = $1 AND complexe_id = $2',
            [roleData.nom, complexeId]
          );

          if (existingRole.rows.length === 0) {
            const createdRole = await this.createRole(roleData);
            createdRoles.push(createdRole);
            logger.info(`Rôle prédéfini créé: ${roleData.nom} pour complexe ${complexeId}`);
          } else {
            logger.info(`Rôle prédéfini existe déjà: ${roleData.nom} pour complexe ${complexeId}`);
          }
        } catch (error) {
          logger.error(`Erreur lors de la création du rôle ${roleData.nom}:`, error);
        }
      }

      return createdRoles;
    } catch (error) {
      logger.error('Error creating predefined roles:', error);
      throw error;
    }
  }

  /**
   * Obtenir les rôles prédéfinis pour un type d'employé
   */
  static async getPredefinedRoleForEmployeeType(complexeId, employeeType) {
    try {
      const roleNames = {
        reception: 'Employé Réception',
        gerant_piscine: 'Gérant Piscine',
        serveuse: 'Serveuse',
        gerant_services: 'Gérant Services',
        cuisine: 'Employé Cuisine'
      };

      const roleName = roleNames[employeeType];
      if (!roleName) {
        throw new Error(`Type d'employé invalide: ${employeeType}`);
      }

      const result = await db.query(
        'SELECT * FROM "RolesComplexe" WHERE nom = $1 AND complexe_id = $2',
        [roleName, complexeId]
      );

      if (result.rows.length === 0) {
        // Créer le rôle s'il n'existe pas
        return await EmployeeTypeService.getOrCreatePredefinedRole(complexeId, employeeType);
      }

      const role = result.rows[0];
      if (role.permissions && typeof role.permissions === 'string') {
        role.permissions = JSON.parse(role.permissions);
      }

      return role;
    } catch (error) {
      logger.error('Error getting predefined role for employee type:', error);
      throw error;
    }
  }

  /**
   * Migrer les anciennes permissions vers le nouveau système
   */
  static async migrateLegacyPermissions(complexeId) {
    try {
      const roles = await this.getRolesByComplexe(complexeId);
      const migratedRoles = [];

      for (const role of roles) {
        if (!role.permissions || role.permissions.length === 0) {
          continue;
        }

        const newPermissions = [];
        const legacyPermissions = Array.isArray(role.permissions)
          ? role.permissions
          : JSON.parse(role.permissions);

        // Mapper les anciennes permissions vers les nouvelles
        for (const legacyPermission of legacyPermissions) {
          const newPermission = PermissionService.mapLegacyPermissionToSimplified(legacyPermission);
          if (newPermission && !newPermissions.includes(newPermission)) {
            newPermissions.push(newPermission);
          }
        }

        // Mettre à jour le rôle si des permissions ont été mappées
        if (newPermissions.length > 0) {
          await this.updateRole(role.role_id, {
            permissions: newPermissions
          });
          migratedRoles.push({
            role_id: role.role_id,
            nom: role.nom,
            old_permissions: legacyPermissions,
            new_permissions: newPermissions
          });
          logger.info(`Rôle migré: ${role.nom} - ${legacyPermissions.length} -> ${newPermissions.length} permissions`);
        }
      }

      return migratedRoles;
    } catch (error) {
      logger.error('Error migrating legacy permissions:', error);
      throw error;
    }
  }

  /**
   * Valider les permissions d'un rôle selon le nouveau système
   */
  static validateRolePermissions(permissions) {
    const validPermissions = PermissionService.getAvailablePermissions();
    const invalidPermissions = [];

    for (const permission of permissions) {
      if (!validPermissions.includes(permission)) {
        // Vérifier si c'est une ancienne permission qui peut être mappée
        const mappedPermission = PermissionService.mapLegacyPermissionToSimplified(permission);
        if (!mappedPermission) {
          invalidPermissions.push(permission);
        }
      }
    }

    return {
      isValid: invalidPermissions.length === 0,
      invalidPermissions
    };
  }
}

module.exports = RoleService;