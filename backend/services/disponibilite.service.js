const db = require('../db');
const logger = require('../logger');

class DisponibiliteService {
  // Méthodes utilitaires
  static successResponse(data) {
    return {
      success: true,
      data
    };
  }

  static rejectResponse(message, code = 500) {
    return {
      success: false,
      message,
      code
    };
  }

  // Récupération de la liste des disponibilités
  static async getDisponibilites(params) {
    const { date_debut, date_fin, chambre_id } = params;

    try {
      let query = `
        WITH Disponibilites AS (
          SELECT 
            c.chambre_id,
            c.numero,
            c.type_chambre,
            c.capacite,
            c.prix_base,
            c.description,
            c.statut,
            d.heure_debut,
            d.heure_fin,
            d.statut as statut_disponibilite,
            d.reservation_id,
            d.date
          FROM "Chambres" c
          LEFT JOIN "DisponibilitesChambres" d ON d.chambre_id = c.chambre_id 
            AND d.date BETWEEN $1 AND $2
          WHERE c.statut = 'active'
      `;

      const values = [date_debut, date_fin];
      let paramIndex = 3;

      if (chambre_id) {
        query += ` AND c.chambre_id = $${paramIndex}`;
        values.push(chambre_id);
        paramIndex++;
      }

      query += `
        ),
        ChambresAvecPlages AS (
          SELECT 
            chambre_id,
            numero,
            type_chambre,
            statut,
            capacite,
            prix_base,
            description,
            date,
            CASE WHEN statut_disponibilite IS NULL OR statut_disponibilite = 'disponible' THEN true ELSE false END as disponible,
            COALESCE(statut_disponibilite, 'disponible') as statut_disponibilite,
            json_agg(
              CASE 
                WHEN heure_debut IS NOT NULL THEN
                  json_build_object(
                    'heure_debut', heure_debut,
                    'heure_fin', heure_fin,
                    'statut', statut_disponibilite,
                    'reservation_id', reservation_id,
                    'date', date
                  )
                ELSE NULL
              END
            ) FILTER (WHERE heure_debut IS NOT NULL) as plages_occupation
          FROM Disponibilites
          GROUP BY 
            chambre_id,
            numero,
            type_chambre,
            statut,
            capacite,
            prix_base,
            description,
            date,
            statut_disponibilite
        )
        SELECT 
          type_chambre,
          COUNT(DISTINCT chambre_id) as total,
          COUNT(DISTINCT CASE WHEN disponible THEN chambre_id END) as disponibles,
          json_agg(
            json_build_object(
              'chambre_id', chambre_id,
              'numero', numero,
              'type_chambre', type_chambre,
              'statut', statut,
              'capacite', capacite,
              'prix_base', prix_base,
              'description', description,
              'disponible', disponible,
              'statut_disponibilite', statut_disponibilite,
              'date', date,
              'plages_occupation', COALESCE(plages_occupation, '[]'::json)
            )
          ) as chambres
        FROM ChambresAvecPlages
        GROUP BY type_chambre
      `;

      const result = await db.query(query, values);

      return this.successResponse({
        disponibilites: result.rows,
        date_debut,
        date_fin
      });
    } catch (error) {
      logger.error('Erreur récupération disponibilités:', error);
      return this.rejectResponse('Erreur lors de la récupération des disponibilités');
    }
  }

  // Vérification des disponibilités
  static async verifierDisponibilite(params) {
    const { date_debut, date_fin, heure_debut, heure_fin, type_chambre, nombre_chambres = 1 } = params;

    try {
      logger.info('Vérification disponibilité avec params:', params);

      let query = `
        WITH Disponibilites AS (
          SELECT 
            c.chambre_id,
            c.numero,
            c.type_chambre,
            c.capacite,
            c.prix_base,
            c.description,
            c.statut,
            NOT EXISTS (
              SELECT 1 
              FROM "DisponibilitesChambres" d
              WHERE d.chambre_id = c.chambre_id
              AND d.date = $1
              AND d.statut IN ('occupee', 'bloquee', 'en_attente', 'expiree')
              AND (
                (d.heure_debut <= $2 AND d.heure_fin > $2)
                OR (d.heure_debut < $3 AND d.heure_fin >= $3)
                OR (d.heure_debut >= $2 AND d.heure_fin <= $3)
              )
            ) AND NOT EXISTS (
              SELECT 1 
              FROM "ChambresReservees" cr
              JOIN "Reservations" r ON cr.reservation_id = r.reservation_id
              WHERE cr.chambre_id = c.chambre_id
              AND r.statut NOT IN ('annulee', 'terminee')
              AND r.date_arrivee = $1
              AND (
                (r.heure_debut <= $2 AND r.heure_fin > $2)
                OR (r.heure_debut < $3 AND r.heure_fin >= $3)
                OR (r.heure_debut >= $2 AND r.heure_fin <= $3)
              )
            ) as disponible,
            COALESCE(
            json_agg(
              json_build_object(
                'heure_debut', d.heure_debut,
                'heure_fin', d.heure_fin,
                'statut', d.statut,
                'type_reservation', d.type_reservation,
                'reservation_id', d.reservation_id
                )
              ) FILTER (WHERE d.heure_debut IS NOT NULL),
              '[]'::json
            ) as plages_occupation
          FROM "Chambres" c
          LEFT JOIN "DisponibilitesChambres" d ON d.chambre_id = c.chambre_id 
            AND d.date = $1
          WHERE c.statut = 'active'
      `;

      const values = [date_debut, heure_debut, heure_fin];
      let paramIndex = 4;

      if (type_chambre) {
        query += ` AND c.type_chambre = $${paramIndex}`;
        values.push(type_chambre);
        paramIndex++;
      }

      query += `
          GROUP BY c.chambre_id, c.numero, c.type_chambre, c.capacite, c.prix_base, c.description, c.statut
        )
        SELECT 
          type_chambre,
          COUNT(*) as total,
          COUNT(*) FILTER (WHERE disponible) as disponibles,
          json_agg(
            json_build_object(
              'chambre_id', chambre_id,
              'numero', numero,
              'type_chambre', type_chambre,
              'statut', statut,
              'capacite', capacite,
              'prix_base', prix_base,
              'description', description,
              'disponible', disponible,
              'statut_disponibilite', CASE 
                WHEN disponible THEN 'disponible'
                ELSE 'occupee'
              END,
              'plages_occupation', plages_occupation
            )
          ) as chambres
        FROM Disponibilites
        GROUP BY type_chambre
        HAVING COUNT(*) FILTER (WHERE disponible) >= $${paramIndex}
      `;

      values.push(nombre_chambres);

      logger.info('Requête SQL:', { query, values });

      const result = await db.query(query, values);
      
      logger.info('Résultat vérification disponibilité:', {
        date_debut,
        heure_debut,
        heure_fin,
        nombre_chambres,
        result: result.rows
      });

      return this.successResponse({
        disponibilites: result.rows,
        date_debut,
        date_fin,
        heure_debut,
        heure_fin,
        nombre_chambres
      });
    } catch (error) {
      logger.error('Erreur vérification disponibilités:', error);
      return this.rejectResponse('Erreur lors de la vérification des disponibilités');
    }
  }

  // Création d'une disponibilité
  static async createDisponibilite(params) {
    const { chambre_id, date, heure_debut, heure_fin, statut, reservation_id, type_reservation = 'CONFIRMEE' } = params;

    try {
      const client = await db.query('BEGIN');

      // Vérifier si la chambre existe
      const checkChambreQuery = `
        SELECT statut 
        FROM "Chambres" 
        WHERE chambre_id = $1 
        FOR UPDATE
      `;
      
      const checkChambreResult = await db.query(checkChambreQuery, [chambre_id]);
      
      if (checkChambreResult.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Chambre non trouvée', 404);
      }

      // Vérifier les conflits de disponibilité
      const checkConflictQuery = `
        SELECT 1
        FROM "DisponibilitesChambres"
        WHERE chambre_id = $1
        AND date = $2
        AND statut IN ('occupee', 'bloquee', 'expiree')
        AND (
          (heure_debut <= $3 AND heure_fin >= $3)
          OR (heure_debut <= $4 AND heure_fin >= $4)
          OR (heure_debut >= $3 AND heure_fin <= $4)
        )
      `;

      const checkConflictResult = await db.query(checkConflictQuery, [
        chambre_id,
        date,
        heure_debut,
        heure_fin
      ]);

      if (checkConflictResult.rows.length > 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Conflit de disponibilité détecté', 409);
      }

      // Créer la disponibilité
      const createQuery = `
        INSERT INTO "DisponibilitesChambres" (
          chambre_id,
          date,
          heure_debut,
          heure_fin,
          statut,
          reservation_id,
          type_occupation,
          type_reservation,
          created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, 'HEURE', $7, NOW())
        RETURNING *
      `;

      const createResult = await db.query(createQuery, [
        chambre_id,
        date,
        heure_debut,
        heure_fin,
        statut,
        reservation_id,
        type_reservation
      ]);

      await db.query('COMMIT');
      logger.info('Disponibilité créée', { 
        chambre_id, 
        date,
        heure_debut,
        heure_fin,
        statut,
        type_reservation
      });

      return this.successResponse(createResult.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur création disponibilité:', error);
      return this.rejectResponse('Erreur lors de la création de la disponibilité');
    }
  }

  // Mise à jour d'une disponibilité
  static async updateDisponibilite(id, params) {
    const { date, heure_debut, heure_fin, statut, reservation_id } = params;

    try {
      const client = await db.query('BEGIN');

      // Vérifier si la disponibilité existe
      const checkQuery = `
        SELECT * 
        FROM "DisponibilitesChambres" 
        WHERE disponibilite_id = $1 
        FOR UPDATE
      `;
      
      const checkResult = await db.query(checkQuery, [id]);
      
      if (checkResult.rows.length === 0) {
        await db.query('ROLLBACK');
        return this.rejectResponse('Disponibilité non trouvée', 404);
      }

      const ancienneDisponibilite = checkResult.rows[0];

      // Vérifier les conflits si les heures changent
      if (heure_debut !== ancienneDisponibilite.heure_debut || 
          heure_fin !== ancienneDisponibilite.heure_fin ||
          date !== ancienneDisponibilite.date) {
        const checkConflictQuery = `
          SELECT 1
          FROM "DisponibilitesChambres"
          WHERE chambre_id = $1
          AND disponibilite_id != $2
          AND date = $3
          AND statut IN ('occupee', 'bloquee', 'expiree')
          AND (
            (heure_debut <= $4 AND heure_fin >= $4)
            OR (heure_debut <= $5 AND heure_fin >= $5)
            OR (heure_debut >= $4 AND heure_fin <= $5)
          )
        `;

        const checkConflictResult = await db.query(checkConflictQuery, [
          ancienneDisponibilite.chambre_id,
          id,
          date,
          heure_debut,
          heure_fin
        ]);

        if (checkConflictResult.rows.length > 0) {
          await db.query('ROLLBACK');
          return this.rejectResponse('Conflit de disponibilité détecté', 409);
        }
      }

      // Mettre à jour la disponibilité
      const updateQuery = `
        UPDATE "DisponibilitesChambres" 
        SET 
          date = COALESCE($1, date),
          heure_debut = COALESCE($2, heure_debut),
          heure_fin = COALESCE($3, heure_fin),
          statut = COALESCE($4, statut),
          reservation_id = COALESCE($5, reservation_id),
          updated_at = NOW()
        WHERE disponibilite_id = $6
        RETURNING *
      `;

      const updateResult = await db.query(updateQuery, [
        date,
        heure_debut,
        heure_fin,
        statut,
        reservation_id,
        id
      ]);

      await db.query('COMMIT');
      logger.info('Disponibilité mise à jour', { 
        id, 
        ancien_statut: ancienneDisponibilite.statut,
        nouveau_statut: statut 
      });

      return this.successResponse(updateResult.rows[0]);
    } catch (error) {
      await db.query('ROLLBACK');
      logger.error('Erreur mise à jour disponibilité:', error);
      return this.rejectResponse('Erreur lors de la mise à jour de la disponibilité');
    }
  }
}

module.exports = DisponibiliteService; 