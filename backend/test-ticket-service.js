// Script de test pour les tickets de service
// Usage: node test-ticket-service.js

const TicketService = require('./services/ticket.service');

async function testTicketService() {
  console.log('🧪 Test des tickets de service...\n');

  // Test 1: Création d'un ticket de service
  console.log('1️⃣ Test création ticket de service');
  const ticketData = {
    service_id: 1,
    pos_id: 1,
    session_id: 1,
    employe_id: 1,
    nom_client: 'Martin Test',
    nombre_personnes: 3,
    duree_heures: 2,
    mode_paiement: 'Espèces',
    prix_total: 45.00
  };

  try {
    const result = await TicketService.creerTicketService(ticketData);
    if (result.success) {
      console.log('✅ Ticket créé avec succès');
      console.log('📄 Numéro:', result.data.ticket.numero_ticket);
      console.log('🎯 Service:', result.data.service.nom);
      console.log('💰 Prix:', result.data.ticket.prix_total, 'FCFA');
    } else {
      console.log('❌ Erreur:', result.message);
    }
  } catch (error) {
    console.log('❌ Erreur technique:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 2: Vérification d'un ticket
  console.log('2️⃣ Test vérification ticket de service');
  try {
    const result = await TicketService.verifierTicketService('SRV-TEST123');
    if (result.success) {
      console.log('✅ Vérification réussie');
      console.log('🎫 Valide:', result.data.valide);
      if (result.data.valide) {
        console.log('👤 Client:', result.data.ticket.nom_client);
        console.log('👥 Personnes:', result.data.ticket.nombre_personnes);
      } else {
        console.log('❌ Raison:', result.data.raison);
      }
    } else {
      console.log('❌ Erreur:', result.message);
    }
  } catch (error) {
    console.log('❌ Erreur technique:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 3: Récupération des tickets d'un service
  console.log('3️⃣ Test récupération tickets par service');
  try {
    const result = await TicketService.getServiceTickets(1, { statut: 'ACTIF' });
    if (result.success) {
      console.log('✅ Récupération réussie');
      console.log('📊 Nombre de tickets:', result.data.length);
      result.data.forEach((ticket, index) => {
        console.log(`   ${index + 1}. ${ticket.numero_ticket} - ${ticket.nom_client} (${ticket.statut})`);
      });
    } else {
      console.log('❌ Erreur:', result.message);
    }
  } catch (error) {
    console.log('❌ Erreur technique:', error.message);
  }

  console.log('\n🏁 Tests terminés');
}

// Exécuter les tests si le script est appelé directement
if (require.main === module) {
  testTicketService().catch(console.error);
}

module.exports = { testTicketService };
