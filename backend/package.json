{"name": "api-generated-from-input-sql", "version": "1.0.0", "description": "REST API automatically generated from PostgreSQL schema in input.sql", "main": "index.js", "scripts": {"prestart": "npm install", "dev": "nodemon index.js", "start": "node index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "init-data": "node scripts/init-data.js"}, "keywords": ["openapi-generator", "openapi"], "license": "Unlicense", "private": true, "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "body-parser": "^1.19.0", "camelcase": "^5.3.1", "compression": "^1.8.0", "cookie-parser": "^1.4.4", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.5.0", "express": "^4.16.4", "express-openapi-validator": "^4.13.8", "express-rate-limit": "^7.5.0", "express-slow-down": "^2.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "js-yaml": "^3.3.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "node-cache": "^5.1.2", "nodemailer": "^6.9.9", "ono": "^5.0.1", "openapi-sampler": "^1.0.0-beta.15", "pdfkit": "^0.14.0", "pg": "^8.16.0", "qrcode": "^1.5.4", "react-dropzone": "^14.3.8", "stripe": "^14.14.0", "swagger-ui-express": "^4.0.2", "twilio": "^4.21.0", "uuid": "^11.1.0", "winston": "^3.2.1", "xlsx": "^0.18.5"}, "devDependencies": {"chai": "^4.2.0", "chai-as-promised": "^7.1.1", "eslint": "^5.16.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-plugin-import": "^2.17.2", "jest": "^29.7.0", "mocha": "^11.4.0", "nodemon": "^3.1.10", "supertest": "^6.3.4"}, "eslintConfig": {"env": {"node": true, "jest": true}}, "jest": {"testEnvironment": "node", "coverageDirectory": "./coverage", "collectCoverageFrom": ["controllers/**/*.js", "services/**/*.js", "!**/node_modules/**"]}}