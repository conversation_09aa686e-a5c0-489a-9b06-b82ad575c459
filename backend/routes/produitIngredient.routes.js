const express = require('express');
const ProduitIngredientController = require('../controllers/produitIngredient.controller');

// Middlewares
const { checkPermission } = require('../middleware/permission');
const { rateLimiter } = require('../middleware/rateLimiter');
const { autoInvalidateCache, heavyResponseCache } = require('../middleware/cache');
const { logInventaireAction } = require('../middleware/inventaireLogger');

const router = express.Router();

// Rate limiters spécialisés
const produitIngredientLimiter = rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requêtes par fenêtre
  message: 'Trop de requêtes pour les produits-ingrédients'
});

const importLimiter = rateLimiter({
  windowMs: 60 * 60 * 1000, // 1 heure
  max: 5, // 5 imports par heure
  message: 'Trop d\'imports de produits-ingrédients'
});

/**
 * ==================== ROUTES DE GESTION DES LIENS ====================
 */

/**
 * POST /api/produit-ingredient/add
 * Ajouter un ingrédient à un produit
 * 
 * @body {number} produitId - ID du produit
 * @body {number} ingredientId - ID de l'ingrédient
 * @body {number} quantiteNecessaire - Quantité nécessaire
 * @body {string} uniteMesure - Unité de mesure
 * @body {object} options - Options supplémentaires (optionnel)
 */
router.post('/add',
  produitIngredientLimiter,
  checkPermission('manage_inventory'),
  autoInvalidateCache(['products', 'ingredients', 'analytics']),
  logInventaireAction('ADD_INGREDIENT_TO_PRODUCT'),
  ProduitIngredientController.ajouterIngredientProduit
);

/**
 * PUT /api/produit-ingredient/:produitId/:ingredientId
 * Mettre à jour un lien produit-ingrédient
 * 
 * @param {number} produitId - ID du produit
 * @param {number} ingredientId - ID de l'ingrédient
 * @body {object} updateData - Données à mettre à jour
 */
router.put('/:produitId/:ingredientId',
  produitIngredientLimiter,
  checkPermission('manage_inventory'),
  autoInvalidateCache(['products', 'ingredients', 'analytics']),
  logInventaireAction('UPDATE_PRODUCT_INGREDIENT'),
  ProduitIngredientController.updateIngredientProduit
);

/**
 * DELETE /api/produit-ingredient/:produitId/:ingredientId
 * Supprimer un ingrédient d'un produit
 * 
 * @param {number} produitId - ID du produit
 * @param {number} ingredientId - ID de l'ingrédient
 */
router.delete('/:produitId/:ingredientId',
  produitIngredientLimiter,
  checkPermission('manage_inventory'),
  autoInvalidateCache(['products', 'ingredients', 'analytics']),
  logInventaireAction('REMOVE_INGREDIENT_FROM_PRODUCT'),
  ProduitIngredientController.supprimerIngredientProduit
);

/**
 * ==================== ROUTES DE CONSULTATION ====================
 */

/**
 * GET /api/produit-ingredient/produit/:produitId
 * Récupérer les ingrédients d'un produit
 * 
 * @param {number} produitId - ID du produit
 */
router.get('/produit/:produitId',
  produitIngredientLimiter,
  checkPermission('view_inventory'),
  heavyResponseCache('product-ingredients', (req) => `product:${req.params.produitId}:ingredients`, 300),
  ProduitIngredientController.getIngredientsProduit
);

/**
 * GET /api/produit-ingredient/complexe/:complexeId
 * Récupérer tous les produits avec leurs ingrédients pour un complexe
 * 
 * @param {number} complexeId - ID du complexe
 * @query {number} serviceId - ID du service (optionnel)
 */
router.get('/complexe/:complexeId',
  produitIngredientLimiter,
  checkPermission('view_inventory'),
  heavyResponseCache('products-ingredients', (req) => {
    const serviceId = req.query.serviceId;
    return serviceId ? 
      `complexe:${req.params.complexeId}:service:${serviceId}:products-ingredients` :
      `complexe:${req.params.complexeId}:products-ingredients`;
  }, 600),
  ProduitIngredientController.getProduitsAvecIngredients
);

/**
 * ==================== ROUTES D'ANALYSE ET VÉRIFICATION ====================
 */

/**
 * POST /api/produit-ingredient/check-availability
 * Vérifier la disponibilité des ingrédients pour un produit
 * 
 * @body {number} produitId - ID du produit
 * @body {number} quantite - Quantité demandée (défaut: 1)
 */
router.post('/check-availability',
  produitIngredientLimiter,
  checkPermission('view_inventory'),
  ProduitIngredientController.verifierDisponibiliteProduit
);

/**
 * POST /api/produit-ingredient/calculate-cost/:produitId
 * Calculer le coût d'un produit basé sur ses ingrédients
 * 
 * @param {number} produitId - ID du produit
 */
router.post('/calculate-cost/:produitId',
  produitIngredientLimiter,
  checkPermission('view_inventory'),
  autoInvalidateCache(['products', 'analytics']),
  logInventaireAction('CALCULATE_PRODUCT_COST'),
  ProduitIngredientController.calculerCoutProduit
);

/**
 * ==================== ROUTES D'IMPORT ====================
 */

/**
 * POST /api/produit-ingredient/import/:complexeId
 * Import en masse des liens produits-ingrédients depuis Excel
 * 
 * @param {number} complexeId - ID du complexe
 * @body {array} excelData - Données Excel parsées
 */
router.post('/import/:complexeId',
  importLimiter,
  checkPermission('manage_inventory'),
  autoInvalidateCache(['products', 'ingredients', 'analytics']),
  logInventaireAction('IMPORT_PRODUCTS_INGREDIENTS'),
  ProduitIngredientController.importProduitsIngredients
);

/**
 * ==================== ROUTES D'ANALYSE AVANCÉE ====================
 */

/**
 * GET /api/produit-ingredient/analytics/complexe/:complexeId
 * Analyses des coûts et marges par complexe
 * 
 * @param {number} complexeId - ID du complexe
 * @query {number} serviceId - ID du service (optionnel)
 */
router.get('/analytics/complexe/:complexeId',
  produitIngredientLimiter,
  checkPermission('view_analytics'),
  heavyResponseCache('analytics', (req) => {
    const serviceId = req.query.serviceId;
    return serviceId ? 
      `analytics:complexe:${req.params.complexeId}:service:${serviceId}` :
      `analytics:complexe:${req.params.complexeId}`;
  }, 900),
  async (req, res) => {
    try {
      // Cette route sera implémentée plus tard dans les analytics
      res.json({
        success: true,
        message: 'Analytics des produits-ingrédients - À implémenter',
        data: {
          complexeId: req.params.complexeId,
          serviceId: req.query.serviceId || null
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des analytics'
      });
    }
  }
);

/**
 * ==================== ROUTES DE VALIDATION ====================
 */

/**
 * POST /api/produit-ingredient/validate-import
 * Valider des données d'import sans les sauvegarder
 * 
 * @body {array} excelData - Données Excel à valider
 */
router.post('/validate-import',
  produitIngredientLimiter,
  checkPermission('view_inventory'),
  async (req, res) => {
    try {
      const { excelData } = req.body;
      
      if (!excelData || !Array.isArray(excelData)) {
        return res.status(400).json({
          success: false,
          message: 'Données Excel requises sous forme de tableau'
        });
      }

      // Validation basique des données
      const errors = [];
      const warnings = [];
      
      excelData.forEach((row, index) => {
        if (!row.nom_produit) {
          errors.push(`Ligne ${index + 2}: Nom du produit manquant`);
        }
        if (!row.nom_ingredient) {
          errors.push(`Ligne ${index + 2}: Nom de l'ingrédient manquant`);
        }
        if (!row.quantite_necessaire || isNaN(parseFloat(row.quantite_necessaire))) {
          errors.push(`Ligne ${index + 2}: Quantité nécessaire invalide`);
        }
        if (!row.unite_mesure) {
          warnings.push(`Ligne ${index + 2}: Unité de mesure manquante, 'unité' sera utilisée`);
        }
      });

      res.json({
        success: true,
        data: {
          valid: errors.length === 0,
          errors,
          warnings,
          rowsCount: excelData.length
        },
        message: errors.length === 0 ? 'Validation réussie' : 'Erreurs de validation détectées'
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la validation'
      });
    }
  }
);

module.exports = router;
