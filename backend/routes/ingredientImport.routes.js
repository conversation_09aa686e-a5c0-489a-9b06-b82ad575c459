const express = require('express');
const router = express.Router();
const multer = require('multer');
const IngredientImportController = require('../controllers/ingredientImport.controller');
const { verifyToken } = require('../middleware/auth');
const { checkPermission } = require('../middleware/permission');

/**
 * Configuration de multer pour l'upload de fichiers Excel
 */
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB max
  },
  fileFilter: (req, file, cb) => {
    // Accepter seulement les fichiers Excel
    const allowedMimeTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];
    
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Seuls les fichiers Excel (.xlsx, .xls) sont autorisés'), false);
    }
  }
});

/**
 * Middleware d'authentification pour toutes les routes
 */
router.use(verifyToken);

/**
 * ==================== ROUTES D'IMPORT D'INGRÉDIENTS ====================
 */

/**
 * POST /api/ingredient-import/cuisine/:complexeId
 * Importer des ingrédients cuisine depuis un fichier Excel
 * 
 * @param {number} complexeId - ID du complexe
 * @body {file} ingredientFile - Fichier Excel des ingrédients
 * @body {object} options - Options d'import (optionnel)
 */
router.post('/cuisine/:complexeId',
  checkPermission('manage_inventory'),
  upload.single('ingredientFile'),
  IngredientImportController.importCuisineIngredients
);

/**
 * POST /api/ingredient-import/boissons/:complexeId
 * Importer un inventaire boissons depuis un fichier Excel
 * 
 * @param {number} complexeId - ID du complexe
 * @body {file} boissonFile - Fichier Excel de l'inventaire boissons
 * @body {object} options - Options d'import (optionnel)
 */
router.post('/boissons/:complexeId',
  checkPermission('manage_inventory'),
  upload.single('boissonFile'),
  IngredientImportController.importBoissonInventory
);

/**
 * ==================== ROUTES DE VALIDATION ====================
 */

/**
 * POST /api/ingredient-import/validate/cuisine
 * Valider un fichier Excel d'ingrédients cuisine sans l'importer
 * 
 * @body {file} ingredientFile - Fichier Excel à valider
 */
router.post('/validate/cuisine',
  checkPermission('view_inventory'),
  upload.single('ingredientFile'),
  IngredientImportController.validateCuisineIngredients
);

/**
 * POST /api/ingredient-import/validate/boissons
 * Valider un fichier Excel d'inventaire boissons sans l'importer
 * 
 * @body {file} boissonFile - Fichier Excel à valider
 */
router.post('/validate/boissons',
  checkPermission('view_inventory'),
  upload.single('boissonFile'),
  IngredientImportController.validateBoissonInventory
);

/**
 * ==================== ROUTES DE STATUT ====================
 */

/**
 * GET /api/ingredient-import/status/:complexeId
 * Obtenir le statut d'import pour un complexe
 * 
 * @param {number} complexeId - ID du complexe
 */
router.get('/status/:complexeId',
  checkPermission('view_inventory'),
  IngredientImportController.getImportStatus
);

/**
 * ==================== ROUTES DE TÉLÉCHARGEMENT DE TEMPLATES ====================
 */

/**
 * GET /api/ingredient-import/template/cuisine
 * Télécharger le template Excel pour ingrédients cuisine
 */
router.get('/template/cuisine',
  checkPermission('view_inventory'),
  IngredientImportController.downloadCuisineTemplate
);

/**
 * GET /api/ingredient-import/template/boissons
 * Télécharger le template Excel pour inventaire boissons
 */
router.get('/template/boissons',
  checkPermission('view_inventory'),
  IngredientImportController.downloadBoissonTemplate
);

/**
 * ==================== MIDDLEWARE DE GESTION D'ERREURS ====================
 */

/**
 * Middleware de gestion d'erreurs pour multer
 */
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'Le fichier est trop volumineux (maximum 10MB)'
      });
    }
    
    return res.status(400).json({
      success: false,
      message: `Erreur d'upload: ${error.message}`
    });
  }
  
  if (error.message.includes('fichiers Excel')) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
  
  next(error);
});

/**
 * ==================== ROUTES D'INFORMATION ====================
 */

/**
 * GET /api/ingredient-import/info
 * Informations sur les imports d'ingrédients et inventaire
 */
router.get('/info', (req, res) => {
  res.json({
    success: true,
    data: {
      supportedFormats: ['.xlsx', '.xls'],
      maxFileSize: '10MB',
      requiredFields: {
        cuisine: ['nom_ingredient', 'categorie', 'unite_mesure'],
        boissons: ['nom_boisson', 'categorie', 'type_conditionnement', 'volume_unitaire']
      },
      optionalFields: {
        cuisine: [
          'description', 'prix_unitaire', 'fournisseur', 'allergenes',
          'conservation', 'duree_conservation_jours', 'stock_minimal', 'actif'
        ],
        boissons: [
          'description', 'prix_achat_unitaire', 'prix_vente_unitaire', 'fournisseur',
          'degre_alcool', 'code_barre', 'stock_minimal', 'stock_maximal',
          'emplacement_stockage', 'temperature_stockage', 'actif'
        ]
      },
      validValues: {
        unitesMesure: ['kg', 'g', 'L', 'ml', 'pièce', 'sachet', 'boîte'],
        conservation: ['Frais', 'Sec', 'Congelé', 'Ambiante'],
        categoriesBoissons: ['Bière', 'Vin', 'Spiritueux', 'Cocktail', 'Soft', 'Alcool'],
        conditionnements: ['Bouteille', 'Canette', 'Fût', 'Magnum', 'Jéroboam'],
        temperaturesStockage: ['Ambiante', 'Frais', 'Très frais', 'Congelé']
      },
      workflow: [
        '1. Télécharger le template approprié (cuisine ou boissons)',
        '2. Remplir le fichier Excel avec vos données',
        '3. Valider le fichier (optionnel)',
        '4. Importer le fichier',
        '5. Vérifier le rapport d\'import',
        '6. Les stocks sont initialisés à 0'
      ],
      notes: [
        'Les ingrédients existants ne seront pas écrasés',
        'Les stocks sont initialisés à 0 après import',
        'Les prix peuvent être mis à jour via l\'interface de gestion',
        'Les allergènes doivent être séparés par des virgules'
      ]
    },
    message: 'Informations sur l\'import d\'ingrédients récupérées avec succès'
  });
});

module.exports = router;
