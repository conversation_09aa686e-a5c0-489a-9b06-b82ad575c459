const express = require('express');
const router = express.Router();
const POSAnalyticsController = require('../controllers/posAnalytics.controller');
const { verifyToken } = require('../middleware/auth');
const { checkPermission } = require('../middleware/permission');

// Import des middlewares de cache et rate limiting
const { 
  heavyResponseCache,
  autoInvalidateCache 
} = require('../middleware/cache.middleware');
const { 
  analyticsLimiter 
} = require('../middleware/rateLimiting.middleware');

/**
 * Routes pour les analytics des points de vente Restaurant/Bar
 * Toutes les routes nécessitent une authentification et des permissions appropriées
 */

// Middleware d'authentification pour toutes les routes
router.use(verifyToken);

/**
 * ==================== ROUTES DASHBOARD ET VUE D'ENSEMBLE ====================
 */

/**
 * GET /api/pos-analytics/dashboard/:serviceId
 * Dashboard complet des performances d'un service
 * Query params: date_debut, date_fin
 */
router.get('/dashboard/:serviceId', 
  analyticsLimiter,
  checkPermission('view_reports'),
  heavyResponseCache('analytics', (req) => `pos-dashboard:${req.params.serviceId}:${req.query.date_debut || 'default'}:${req.query.date_fin || 'default'}`, 900),
  POSAnalyticsController.getServiceDashboard
);

/**
 * GET /api/pos-analytics/comparison/:complexeId
 * Comparaison entre services d'un complexe (Restaurant vs Bar)
 * Query params: date_debut, date_fin
 */
router.get('/comparison/:complexeId', 
  analyticsLimiter,
  checkPermission('view_reports'),
  heavyResponseCache('analytics', (req) => `pos-comparison:${req.params.complexeId}:${req.query.date_debut || 'default'}:${req.query.date_fin || 'default'}`, 1800),
  POSAnalyticsController.getServiceComparison
);

/**
 * ==================== ROUTES ANALYSES DÉTAILLÉES ====================
 */

/**
 * GET /api/pos-analytics/performance/:serviceId
 * Analyse des performances d'un service
 * Query params: date_debut, date_fin
 */
router.get('/performance/:serviceId', 
  analyticsLimiter,
  checkPermission('view_reports'),
  heavyResponseCache('analytics', (req) => `pos-performance:${req.params.serviceId}:${req.query.date_debut || 'default'}:${req.query.date_fin || 'default'}`, 600),
  POSAnalyticsController.getServicePerformance
);

/**
 * GET /api/pos-analytics/revenue/:serviceId
 * Analyse détaillée des revenus avec tendances horaires
 * Query params: date_debut, date_fin
 */
router.get('/revenue/:serviceId', 
  analyticsLimiter,
  checkPermission('view_financial_reports'),
  heavyResponseCache('analytics', (req) => `pos-revenue:${req.params.serviceId}:${req.query.date_debut || 'default'}:${req.query.date_fin || 'default'}`, 1200),
  POSAnalyticsController.getRevenueAnalysis
);

/**
 * GET /api/pos-analytics/trends/:serviceId
 * Tendances de fréquentation par jour de la semaine
 * Query params: date_debut, date_fin
 */
router.get('/trends/:serviceId', 
  analyticsLimiter,
  checkPermission('view_reports'),
  heavyResponseCache('analytics', (req) => `pos-trends:${req.params.serviceId}:${req.query.date_debut || 'default'}:${req.query.date_fin || 'default'}`, 1800),
  POSAnalyticsController.getWeeklyTrends
);

/**
 * ==================== ROUTES ANALYSES PRODUITS ET MENU ====================
 */

/**
 * GET /api/pos-analytics/products/:serviceId
 * Top des produits les plus vendus
 * Query params: date_debut, date_fin, limit
 */
router.get('/products/:serviceId', 
  analyticsLimiter,
  checkPermission('view_reports'),
  heavyResponseCache('analytics', (req) => `pos-products:${req.params.serviceId}:${req.query.date_debut || 'default'}:${req.query.date_fin || 'default'}:${req.query.limit || '10'}`, 1200),
  POSAnalyticsController.getTopProducts
);

/**
 * GET /api/pos-analytics/preparation-time/:serviceId
 * Analyse des temps de préparation par produit
 * Query params: date_debut, date_fin
 */
router.get('/preparation-time/:serviceId', 
  analyticsLimiter,
  checkPermission('view_reports'),
  heavyResponseCache('analytics', (req) => `pos-prep-time:${req.params.serviceId}:${req.query.date_debut || 'default'}:${req.query.date_fin || 'default'}`, 1800),
  POSAnalyticsController.getPreparationTime
);

/**
 * ==================== ROUTES ANALYSES OPÉRATIONNELLES ====================
 */

/**
 * GET /api/pos-analytics/tables/:serviceId
 * Analyse de l'utilisation des tables
 * Query params: date_debut, date_fin
 */
router.get('/tables/:serviceId', 
  analyticsLimiter,
  checkPermission('view_reports'),
  heavyResponseCache('analytics', (req) => `pos-tables:${req.params.serviceId}:${req.query.date_debut || 'default'}:${req.query.date_fin || 'default'}`, 1200),
  POSAnalyticsController.getTableUtilization
);

/**
 * GET /api/pos-analytics/employees/:serviceId
 * Performance des employés
 * Query params: date_debut, date_fin
 */
router.get('/employees/:serviceId', 
  analyticsLimiter,
  checkPermission('view_reports'),
  heavyResponseCache('analytics', (req) => `pos-employees:${req.params.serviceId}:${req.query.date_debut || 'default'}:${req.query.date_fin || 'default'}`, 1800),
  POSAnalyticsController.getEmployeePerformance
);

/**
 * ==================== ROUTES PRÉVISIONS ET PROJECTIONS ====================
 */

/**
 * GET /api/pos-analytics/forecast/:serviceId
 * Prévisions basées sur les tendances historiques
 * Query params: jours (nombre de jours à prévoir, défaut: 7)
 */
router.get('/forecast/:serviceId', 
  analyticsLimiter,
  checkPermission('view_reports'),
  heavyResponseCache('analytics', (req) => `pos-forecast:${req.params.serviceId}:${req.query.jours || '7'}`, 3600),
  POSAnalyticsController.getForecast
);

/**
 * ==================== ROUTES D'EXPORT ET RAPPORTS ====================
 */

/**
 * GET /api/pos-analytics/export/:serviceId
 * Export des données analytics en format CSV/Excel
 * Query params: format (csv|excel), type (performance|products|tables), date_debut, date_fin
 */
router.get('/export/:serviceId', 
  analyticsLimiter,
  checkPermission('export_reports'),
  async (req, res, next) => {
    // TODO: Implémenter l'export des données
    res.status(501).json({
      success: false,
      message: 'Export non encore implémenté',
      code: 'NOT_IMPLEMENTED'
    });
  }
);

/**
 * ==================== ROUTES DE CONFIGURATION ====================
 */

/**
 * POST /api/pos-analytics/configure/:serviceId
 * Configuration des paramètres d'analytics pour un service
 * Body: { refresh_interval, cache_duration, alert_thresholds }
 */
router.post('/configure/:serviceId', 
  checkPermission('manage_reports'),
  autoInvalidateCache(['analytics']),
  async (req, res, next) => {
    // TODO: Implémenter la configuration des analytics
    res.status(501).json({
      success: false,
      message: 'Configuration non encore implémentée',
      code: 'NOT_IMPLEMENTED'
    });
  }
);

/**
 * ==================== ROUTES DE MONITORING ====================
 */

/**
 * GET /api/pos-analytics/health
 * Statut de santé du système d'analytics
 */
router.get('/health', 
  checkPermission('view_reports'),
  async (req, res) => {
    try {
      res.json({
        success: true,
        data: {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          version: '1.0.0',
          features: {
            dashboard: 'active',
            comparisons: 'active',
            forecasting: 'active',
            exports: 'planned',
            real_time: 'planned'
          }
        },
        message: 'Système d\'analytics opérationnel'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification du statut'
      });
    }
  }
);

module.exports = router;
