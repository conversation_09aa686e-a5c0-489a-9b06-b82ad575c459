const express = require('express');
const router = express.Router();
const ServiceController = require('../controllers/service.controller');
const {
  validateTarificationData,
  logTarificationAction,
  checkTarificationPermissions
} = require('../middleware/tarification.middleware');

// Routes de consultation des services
router.get('/', ServiceController.getAllServices);
router.get('/type/:type', ServiceController.getServicesByType);
router.get('/:id', ServiceController.getServiceById);

// Routes de gestion des services (CRUD)
router.post('/', ServiceController.createService);
router.put('/:id', ServiceController.updateService);
router.delete('/:id', ServiceController.deleteService);

// ==================== ROUTES TARIFICATION ====================

// Récupérer la tarification d'un service
router.get('/:id/tarification',
  logTarificationAction('CONSULTATION'),
  ServiceController.getTarificationByService
);

// Mettre à jour la tarification d'un service
router.put('/:id/tarification',
  checkTarificationPermissions,
  validateTarificationData,
  logTarificationAction('MISE_A_JOUR'),
  ServiceController.updateTarification
);

// Obtenir un modèle de tarification par défaut selon le type de service
router.get('/tarification/template/:type',
  logTarificationAction('CONSULTATION_TEMPLATE'),
  ServiceController.getTarificationTemplate
);

// ==================== ROUTES D'INTÉGRATION INVENTAIRE ====================

// Mise à jour automatique des tarifs depuis les recettes
router.post('/:id/tarification/update-from-recipes',
  checkTarificationPermissions,
  logTarificationAction('UPDATE_FROM_RECIPES'),
  ServiceController.updateTarificationFromRecipes
);

// Synchronisation prix/coûts avec marge cible
router.post('/:id/tarification/sync-prices',
  checkTarificationPermissions,
  logTarificationAction('SYNC_PRICES'),
  ServiceController.syncPricesWithCosts
);

// Génération de tarification depuis import Excel
router.post('/:id/tarification/generate-from-import/:importId',
  checkTarificationPermissions,
  logTarificationAction('GENERATE_FROM_IMPORT'),
  ServiceController.generateTarificationFromImport
);

module.exports = router;
