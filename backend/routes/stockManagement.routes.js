const express = require('express');
const router = express.Router();
const StockManagementController = require('../controllers/stockManagement.controller');

// Middlewares
const { verifyToken } = require('../middleware/auth.middleware');
const { checkPermission } = require('../middleware/permissions.middleware');

// Middlewares spécialisés pour l'inventaire
const {
  checkInventairePermissions,
  logInventaireAction,
} = require('../middleware/inventaire.middleware');

const {
  stockLimiter,
  analyticsLimiter,
} = require('../middleware/rateLimiting.middleware');

const {
  autoInvalidateCache,
  heavyResponseCache,
} = require('../middleware/cache.middleware');

/**
 * Middleware d'authentification pour toutes les routes
 */
router.use(verifyToken);

/**
 * ==================== ROUTES DE GESTION DES MOUVEMENTS ====================
 */

/**
 * POST /api/stock-management/mouvement
 * Enregistrer un mouvement de stock manuel
 * 
 * @body {number} ingredient_id - ID de l'ingrédient
 * @body {number} complexe_id - ID du complexe
 * @body {string} type_mouvement - Type de mouvement (ENTREE, SORTIE, AJUSTEMENT, PERTE)
 * @body {number} quantite - Quantité du mouvement
 * @body {number} [prix_unitaire] - Prix unitaire (optionnel)
 * @body {string} [notes] - Notes sur le mouvement (optionnel)
 */
router.post('/mouvement',
  stockLimiter,
  checkInventairePermissions(['manage_inventory']),
  autoInvalidateCache(['ingredients', 'analytics']),
  logInventaireAction('STOCK_MOVEMENT'),
  StockManagementController.enregistrerMouvement
);

/**
 * POST /api/stock-management/initialiser
 * Initialiser le stock d'un ingrédient
 * 
 * @body {number} ingredient_id - ID de l'ingrédient
 * @body {number} complexe_id - ID du complexe
 * @body {number} stock_initial - Stock initial
 */
router.post('/initialiser',
  stockLimiter,
  checkInventairePermissions(['manage_inventory']),
  autoInvalidateCache(['ingredients', 'analytics']),
  logInventaireAction('INITIALIZE_STOCK'),
  StockManagementController.initialiserStock
);

/**
 * POST /api/stock-management/consommer-produit
 * Consommer les ingrédients d'un produit lors d'une commande
 *
 * @body {number} produit_id - ID du produit
 * @body {number} commande_id - ID de la commande
 * @body {number} [quantite_portions] - Nombre de portions (défaut: 1)
 */
router.post('/consommer-produit',
  stockLimiter,
  checkInventairePermissions(['operate_pos', 'manage_inventory']),
  autoInvalidateCache(['ingredients', 'analytics']),
  logInventaireAction('CONSUME_PRODUCT'),
  StockManagementController.consommerProduit
);

/**
 * ==================== ROUTES DE CONSULTATION ====================
 */

/**
 * GET /api/stock-management/alertes/:complexeId
 * Récupérer les alertes de stock pour un complexe
 * 
 * @param {number} complexeId - ID du complexe
 * @query {string} [niveau_urgence] - Filtrer par niveau d'urgence (FAIBLE, MOYEN, ELEVE, CRITIQUE)
 */
router.get('/alertes/:complexeId',
  analyticsLimiter,
  checkInventairePermissions(['view_inventory']),
  heavyResponseCache('analytics', (req) => `stock-alerts:${req.params.complexeId}:${req.query.niveau_urgence || 'all'}`, 300),
  StockManagementController.getAlertes
);

/**
 * GET /api/stock-management/stock-detaille/:complexeId
 * Vue détaillée du stock avec statuts et alertes
 * 
 * @param {number} complexeId - ID du complexe
 * @query {string} [categorie] - Filtrer par catégorie d'ingrédient
 * @query {string} [statut_stock] - Filtrer par statut (NORMAL, FAIBLE, CRITIQUE, RUPTURE, EXCESSIF)
 */
router.get('/stock-detaille/:complexeId',
  analyticsLimiter,
  checkInventairePermissions(['view_inventory']),
  heavyResponseCache('ingredients', (req) => `stock-detailed:${req.params.complexeId}:${req.query.categorie || 'all'}:${req.query.statut_stock || 'all'}`, 600),
  StockManagementController.getStockDetaille
);

/**
 * GET /api/stock-management/rapport-consommation/:complexeId
 * Rapport de consommation par période
 * 
 * @param {number} complexeId - ID du complexe
 * @query {string} date_debut - Date de début (YYYY-MM-DD)
 * @query {string} date_fin - Date de fin (YYYY-MM-DD)
 */
router.get('/rapport-consommation/:complexeId',
  analyticsLimiter,
  checkInventairePermissions(['view_inventory', 'view_costs']),
  heavyResponseCache('analytics', (req) => `consumption-report:${req.params.complexeId}:${req.query.date_debut}:${req.query.date_fin}`, 1800),
  StockManagementController.getRapportConsommation
);

/**
 * ==================== ROUTES D'ADMINISTRATION ====================
 */

/**
 * PUT /api/stock-management/alerte/:alerteId/resoudre
 * Marquer une alerte comme résolue
 */
router.put('/alerte/:alerteId/resoudre',
  stockLimiter,
  checkInventairePermissions(['manage_inventory']),
  autoInvalidateCache(['analytics']),
  logInventaireAction('RESOLVE_ALERT'),
  async (request, response) => {
    try {
      const { alerteId } = request.params;
      const { notes_resolution } = request.body;
      const employe_id = request.user?.employe_id;

      const db = require('../db');
      await db.query(`
        UPDATE "AlertesStock" 
        SET statut = 'RESOLUE', 
            date_resolution = NOW(),
            employe_id = $1,
            notes_resolution = $2
        WHERE alerte_id = $3
      `, [employe_id, notes_resolution || null, alerteId]);

      response.json({
        success: true,
        message: 'Alerte marquée comme résolue'
      });

    } catch (error) {
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la résolution de l\'alerte'
      });
    }
  }
);

/**
 * GET /api/stock-management/dashboard/:complexeId
 * Dashboard complet de gestion des stocks
 */
router.get('/dashboard/:complexeId',
  analyticsLimiter,
  checkInventairePermissions(['view_inventory']),
  heavyResponseCache('analytics', (req) => `stock-dashboard:${req.params.complexeId}`, 900),
  async (request, response) => {
    try {
      const { complexeId } = request.params;
      const db = require('../db');

      // Récupérer les données du dashboard en parallèle
      const [stockStats, alertes, mouvementsRecents] = await Promise.all([
        // Statistiques générales du stock
        db.query(`
          SELECT 
            COUNT(*) as total_ingredients,
            SUM(valeur_stock_actuel) as valeur_totale,
            COUNT(CASE WHEN statut_stock = 'RUPTURE' THEN 1 END) as ruptures,
            COUNT(CASE WHEN statut_stock = 'CRITIQUE' THEN 1 END) as critiques,
            COUNT(CASE WHEN statut_stock = 'FAIBLE' THEN 1 END) as faibles,
            COUNT(CASE WHEN statut_stock = 'NORMAL' THEN 1 END) as normaux,
            COUNT(CASE WHEN statut_stock = 'EXCESSIF' THEN 1 END) as excessifs
          FROM "VueStockIngredientsDetaille"
          WHERE complexe_id = $1
        `, [complexeId]),

        // Alertes actives
        db.query(`
          SELECT COUNT(*) as total_alertes,
            COUNT(CASE WHEN niveau_urgence = 'CRITIQUE' THEN 1 END) as critiques,
            COUNT(CASE WHEN niveau_urgence = 'ELEVE' THEN 1 END) as elevees,
            COUNT(CASE WHEN niveau_urgence = 'MOYEN' THEN 1 END) as moyennes
          FROM "AlertesStock"
          WHERE complexe_id = $1 AND statut = 'ACTIVE'
        `, [complexeId]),

        // Mouvements récents
        db.query(`
          SELECT 
            msi.*,
            i.nom as ingredient_nom,
            i.unite_mesure
          FROM "MouvementsStockIngredients" msi
          JOIN "Ingredients" i ON msi.ingredient_id = i.ingredient_id
          WHERE msi.complexe_id = $1
          ORDER BY msi.date_mouvement DESC
          LIMIT 10
        `, [complexeId])
      ]);

      const dashboard = {
        statistiques: stockStats.rows[0],
        alertes: alertes.rows[0],
        mouvements_recents: mouvementsRecents.rows,
        derniere_mise_a_jour: new Date().toISOString()
      };

      response.json({
        success: true,
        data: dashboard,
        message: 'Dashboard récupéré avec succès'
      });

    } catch (error) {
      response.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération du dashboard'
      });
    }
  }
);

module.exports = router;
