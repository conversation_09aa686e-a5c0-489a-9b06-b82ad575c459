const express = require('express');
const { verifyToken } = require('../middleware/auth');
const NotificationController = require('../controllers/notification.controller');
const {
  validateNotification,
  checkPermissions,
  logNotificationAction
} = require('../middleware/notification');

const router = express.Router();

// Routes protégées
router.get('/', 
  verifyToken,
  checkPermissions,
  logNotificationAction('CONSULTATION'),
  NotificationController.getNotifications
);

router.post('/', 
  verifyToken,
  checkPermissions,
  validateNotification,
  logNotificationAction('CREATION'),
  NotificationController.createNotification
);

router.put('/:id/lu', 
  verifyToken,
  checkPermissions,
  logNotificationAction('MARQUER_LU'),
  NotificationController.markAsRead
);

router.get('/non-lues', 
  verifyToken,
  checkPermissions,
  logNotificationAction('CONSULTATION_NON_LUES'),
  NotificationController.getUnreadNotifications
);

module.exports = router; 