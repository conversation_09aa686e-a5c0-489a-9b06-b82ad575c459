const express = require('express');
const router = express.Router();
const EmployeeTypeController = require('../controllers/employeeType.controller');
const { verifyToken } = require('../middleware/auth');
const { checkPermission } = require('../middleware/permission');

/**
 * Routes pour la gestion des types d'employés
 * Phase 1 du plan de simplification des permissions
 */

// Middleware d'authentification pour toutes les routes
router.use(verifyToken);

/**
 * @route GET /api/employee-types
 * @desc Obtenir tous les types d'employés disponibles
 * @access Admins uniquement
 */
router.get('/', 
  checkPermission(['manage_employees', 'view_employees']),
  EmployeeTypeController.getAvailableTypes
);

/**
 * @route GET /api/employee-types/:type
 * @desc Obtenir les informations d'un type d'employé spécifique
 * @access Admins uniquement
 */
router.get('/:type',
  checkPermission(['manage_employees', 'view_employees']),
  EmployeeTypeController.getTypeInfo
);

/**
 * @route GET /api/employee-types/:type/permissions
 * @desc Obtenir les permissions pour un type d'employé
 * @access Admins uniquement
 */
router.get('/:type/permissions',
  checkPermission(['manage_employees', 'view_employees']),
  EmployeeTypeController.getTypePermissions
);

/**
 * @route GET /api/employee-types/:type/default-services
 * @desc Obtenir les services par défaut pour un type d'employé
 * @access Admins uniquement
 */
router.get('/:type/default-services',
  checkPermission(['manage_employees', 'view_employees']),
  EmployeeTypeController.getDefaultServicesForType
);

/**
 * @route POST /api/employee-types/employees/:id/change-type
 * @desc Changer le type d'un employé
 * @access Admins uniquement
 */
router.post('/employees/:id/change-type',
  checkPermission(['manage_employees']),
  EmployeeTypeController.changeEmployeeType
);

/**
 * @route GET /api/employee-types/employees
 * @desc Obtenir les employés par type dans un complexe
 * @access Admins uniquement
 */
router.get('/employees/by-type',
  checkPermission(['manage_employees', 'view_employees']),
  EmployeeTypeController.getEmployeesByType
);

/**
 * @route GET /api/employee-types/stats
 * @desc Obtenir les statistiques des types d'employés
 * @access Admins uniquement
 */
router.get('/stats/overview',
  checkPermission(['view_statistics', 'view_employees']),
  EmployeeTypeController.getEmployeeTypeStats
);

/**
 * @route GET /api/employee-types/validate/:employeeId/:serviceType
 * @desc Valider l'accès d'un employé à un service
 * @access Admins et employés concernés
 */
router.get('/validate/:employeeId/:serviceType',
  // Middleware plus permissif car les employés peuvent vérifier leur propre accès
  (req, res, next) => {
    // Si c'est l'employé lui-même qui vérifie son accès
    if (req.user.role === 'employe' && req.user.id == req.params.employeeId) {
      return next();
    }
    // Sinon, vérifier les permissions admin
    return checkPermission(['manage_employees', 'view_employees'])(req, res, next);
  },
  EmployeeTypeController.validateServiceAccess
);

module.exports = router;
