const express = require('express');
const { verifyToken } = require('../middleware/auth');
const TracabiliteController = require('../controllers/tracabilite.controller');

const router = express.Router();

// Routes client
router.get('/client/historique/:entiteId', TracabiliteController.getHistoriqueEntite);
router.get('/client/paiements/:reservationId', TracabiliteController.getHistoriquePaiements);
router.get('/client/tickets/:reservationId', TracabiliteController.getHistoriqueTickets);

// Routes réception
router.get('/reception/actions/:entiteId', verifyToken, TracabiliteController.getHistoriqueActions);
router.get('/reception/modifications/:reservationId', verifyToken, TracabiliteController.getHistoriqueModifications);
router.get('/reception/utilisations/:ticketId', verifyToken, TracabiliteController.getHistoriqueUtilisations);

// Routes admin
router.get('/admin/audit', verifyToken, TracabiliteController.getAuditLog);
router.get('/admin/statistiques', verifyToken, TracabiliteController.getStatistiquesActions);
router.get('/admin/rapports', verifyToken, TracabiliteController.genererRapports);

module.exports = router; 