const express = require('express');
const router = express.Router();
const UploadController = require('../controllers/upload.controller');

// Middlewares spécialisés
const {
  fileUploadConfig,
  validateFileUpload,
  handleMulterError,
  cleanupTempFiles,
  advancedSecurityCheck,
} = require('../middleware/fileValidation.middleware');

const {
  checkImportPermissions,
  logInventaireAction,
} = require('../middleware/inventaire.middleware');

const {
  fileUploadLimiter,
  fileParsingLimiter,
  fileSizeBasedLimiter,
  rateLimitMonitoring,
} = require('../middleware/rateLimiting.middleware');

const {
  cacheTemplates,
  autoInvalidateCache,
} = require('../middleware/cache.middleware');

// Application des middlewares globaux pour toutes les routes
router.use(rateLimitMonitoring);

/**
 * ==================== ROUTES D'UPLOAD ====================
 */

/**
 * POST /api/upload/excel
 * Upload d'un fichier Excel pour import
 */
router.post('/excel',
  fileUploadLimiter,
  fileSizeBasedLimiter,
  checkImportPermissions,
  fileUploadConfig.single('file'),
  handleMulterError,
  validateFileUpload,
  advancedSecurityCheck,
  cleanupTempFiles,
  logInventaireAction('UPLOAD_FILE'),
  UploadController.uploadExcelFile
);

/**
 * POST /api/upload/parse/:importId
 * Parsing d'un fichier Excel uploadé
 */
router.post('/parse/:importId',
  fileParsingLimiter,
  logInventaireAction('PARSE_FILE'),
  UploadController.parseExcelFile
);

/**
 * POST /api/upload/validate/:importId
 * Validation des données importées
 */
router.post('/validate/:importId',
  logInventaireAction('VALIDATE_DATA'),
  UploadController.validateImportData
);

/**
 * GET /api/upload/preview/:importId
 * Prévisualisation des données parsées
 */
router.get('/preview/:importId', UploadController.previewImportData);

/**
 * GET /api/upload/status/:importId
 * Statut de l'import en cours
 */
router.get('/status/:importId', UploadController.getImportStatus);

/**
 * DELETE /api/upload/:importId
 * Suppression d'un import et nettoyage des fichiers
 */
router.delete('/:importId',
  logInventaireAction('DELETE_IMPORT'),
  UploadController.deleteImport
);

/**
 * ==================== ROUTES DE TEMPLATES ====================
 */

/**
 * GET /api/upload/templates/:type
 * Récupération des templates d'import par type
 */
router.get('/templates/:type',
  cacheTemplates,
  UploadController.getImportTemplate
);

/**
 * GET /api/upload/download/template/:templateId
 * Téléchargement direct d'un fichier template
 */
router.get('/download/template/:templateId',
  UploadController.downloadTemplate
);

/**
 * ==================== ROUTES D'ADMINISTRATION ====================
 */

/**
 * POST /api/upload/cleanup
 * Nettoyage manuel des fichiers temporaires
 */
router.post('/cleanup',
  logInventaireAction('CLEANUP_FILES'),
  UploadController.cleanupTempFiles
);

module.exports = router;
