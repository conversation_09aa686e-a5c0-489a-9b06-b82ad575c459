const express = require('express');
const router = express.Router();
const PermissionController = require('../controllers/permission.controller');
const { verifyToken } = require('../middleware/auth');

/**
 * Routes pour les permissions - Système simplifié
 * Phase 6 : Refactoring des routes
 */

// Routes protégées par l'authentification
router.use(verifyToken);

// Routes pour récupérer les permissions disponibles (système simplifié)
router.get('/all', PermissionController.getAllPermissions);
router.get('/list', PermissionController.getPermissionsList);
router.get('/by-user-type', PermissionController.getPermissionsByUserType);

// Routes pour les permissions utilisateur
router.get('/user', PermissionController.getUserPermissions);
router.get('/check/:permission', PermissionController.checkUserPermission);

// Routes pour les types d'employés
router.get('/employee-type/:type', PermissionController.getPermissionsForEmployeeType);

// Routes pour vérifier l'accès aux services
router.get('/service-access/:serviceType', PermissionController.checkServiceAccess);

module.exports = router;
