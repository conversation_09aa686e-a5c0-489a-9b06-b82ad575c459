const express = require('express');
const ReservationController = require('../controllers/reservation.controller');
const {
  checkPermissions,
  logReservationAction
} = require('../middleware/reservation');

const router = express.Router();

// Routes réception
router.post('/', 
  checkPermissions,
  logReservationAction('CREATION'),
  ReservationController.createReservation
);

// Routes publiques
router.post('/demande', 
  logReservationAction('DEMANDE_CREATION'),
  ReservationController.createDemandeReservation
);




router.get('/en-attente', 
  checkPermissions,
  logReservationAction('CONSULTATION_EN_ATTENTE'),
  ReservationController.getPendingReservations
);

  // Route pour récupérer l'historique des réservations pour le calendrier
router.get('/historique-calendrier',
  checkPermissions,
  logReservationAction('CONSULTATION_HISTORIQUE_CALENDRIER'),
  ReservationController.getHistoriqueReservationsCalendrier
);


router.get('/:reservationId',
  checkPermissions,
  logReservationAction('CONSULTATION'),
  ReservationController.getReservationById
);

router.get('/:reservationId/chambres',
  checkPermissions,
  logReservationAction('CONSULTATION_CHAMBRES'),
  ReservationController.getChambresReservation
);

router.get('/:reservationId/paiements',
  checkPermissions,
  logReservationAction('CONSULTATION_PAIEMENTS'),
  ReservationController.getPaiementsReservation
);

router.patch('/:reservationId/statut',
  checkPermissions,
  logReservationAction('MISE_A_JOUR_STATUT'),
  ReservationController.updateReservationStatus
);

router.post('/:reservationId/confirmer', 
  checkPermissions,
  logReservationAction('CONFIRMATION'),
  ReservationController.confirmReservation
);

router.post('/:reservationId/rejeter', 
  checkPermissions,
  logReservationAction('REJET'),
  ReservationController.rejectReservation
);

router.get('/:reservationId/historique',
  checkPermissions,
  logReservationAction('CONSULTATION_HISTORIQUE'),
  ReservationController.getHistoriqueReservation
);

router.get('/:reservationId/ticket-caisse',
  checkPermissions,
  logReservationAction('IMPRESSION_TICKET_CAISSE'),
  ReservationController.genererTicketCaisse
);

module.exports = router;