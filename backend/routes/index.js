const express = require('express');
// Routes existantes
const authRoutes = require('./auth.routes');
const reservationRoutes = require('./reservation.routes');
const paiementRoutes = require('./paiement.routes');
const tracabiliteRoutes = require('./tracabilite.routes');
const ticketRoutes = require('./ticket.routes');
const chambreRoutes = require('./chambre.routes');
const disponibiliteRoutes = require('./disponibilite.routes');
const clientRoutes = require('./client.routes');
const notificationRoutes = require('./notification.routes');
const statistiqueRoutes = require('./statistique.routes');
const complexRoutes = require('./complex.routes');
const transactionRoutes = require('./transaction.routes');
const rapportRoutes = require('./rapport.routes');
const serviceRoutes = require('./service.routes');
const posRoutes = require('./pos.routes');
const employeeRoutes = require('./employee.routes');
const employeeTypeRoutes = require('./employeeType.routes');
const roleRoutes = require('./role.routes');
const sessionRoutes = require('./session.routes');
const permissionRoutes = require('./permission.routes');

// Nouvelles routes pour le système d'inventaire
const uploadRoutes = require('./upload.routes');
const inventaireRoutes = require('./inventaire.routes');

const importRoutes = require('./import.routes');
const templateRoutes = require('./template.routes');
const templateGeneratorRoutes = require('./templateGenerator.routes');
const menuImportRoutes = require('./menuImport.routes');
const ingredientImportRoutes = require('./ingredientImport.routes');
const produitIngredientRoutes = require('./produitIngredient.routes');

// Routes d'intégration POS-Stock
const posStockRoutes = require('./posStock.routes');

// Routes de gestion avancée des stocks
const stockManagementRoutes = require('./stockManagement.routes');

// Routes POS Analytics
const posAnalyticsRoutes = require('./posAnalytics.routes');

// Routes POS Restaurant/Bar
const tableRoutes = require('./table.routes');
const menuRoutes = require('./menu.routes');
const commandeRoutes = require('./commande.routes');

// Routes pour les réservations anonymes
const anonymousReservationRoutes = require('./anonymousReservation.routes');
const anonymousConfigRoutes = require('./anonymousConfig.routes');

const { verifyToken } = require('../middleware/auth');
const requestLogger = require('../middleware/request');
const { errorHandler } = require('../middleware/error');

const router = express.Router();

// Middleware global
router.use(requestLogger);

// Routes de santé
router.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
  });
});

// Routes publiques
router.use('/auth', authRoutes);

// Routes publiques pour les réservations anonymes
router.use('/reservations-anonymes', anonymousReservationRoutes);

// Routes protégées existantes
router.use('/reservations', verifyToken, reservationRoutes);
router.use('/paiements', verifyToken, paiementRoutes);
router.use('/notifications', verifyToken, notificationRoutes);
router.use('/tracabilite', verifyToken, tracabiliteRoutes);
router.use('/tickets', verifyToken, ticketRoutes);
router.use('/chambres', verifyToken, chambreRoutes);
router.use('/disponibilites', verifyToken, disponibiliteRoutes);
router.use('/clients', verifyToken, clientRoutes);
router.use('/statistiques', verifyToken, statistiqueRoutes);
router.use('/complexes', verifyToken, complexRoutes);
router.use('/transactions', verifyToken, transactionRoutes);
router.use('/rapports', verifyToken, rapportRoutes);
router.use('/services', verifyToken, serviceRoutes);
router.use('/pos', verifyToken, posRoutes);
router.use('/employees', verifyToken, employeeRoutes);
router.use('/employee-types', verifyToken, employeeTypeRoutes);
router.use('/roles', verifyToken, roleRoutes);
router.use('/sessions', verifyToken, sessionRoutes);
router.use('/permissions', permissionRoutes);

// Nouvelles routes protégées pour le système d'inventaire
router.use('/upload', verifyToken, uploadRoutes);
router.use('/inventaire', verifyToken, inventaireRoutes);

router.use('/imports', verifyToken, importRoutes);
router.use('/templates', verifyToken, templateRoutes);
router.use('/template-generator', verifyToken, templateGeneratorRoutes);
router.use('/menu-import', menuImportRoutes);
router.use('/ingredient-import', ingredientImportRoutes);
router.use('/produit-ingredient', verifyToken, produitIngredientRoutes);

// Routes d'intégration POS-Stock
router.use('/pos-stock', verifyToken, posStockRoutes);

// Routes de gestion avancée des stocks
router.use('/stock-management', verifyToken, stockManagementRoutes);

// Routes POS Analytics
router.use('/pos-analytics', verifyToken, posAnalyticsRoutes);

// Routes de déduction automatique du stock
const autoStockDeductionRoutes = require('./autoStockDeduction.routes');
router.use('/auto-stock-deduction', autoStockDeductionRoutes);

// Routes POS Restaurant/Bar
router.use('/tables', verifyToken, tableRoutes);
router.use('/menu', verifyToken, menuRoutes);
router.use('/commandes', verifyToken, commandeRoutes);

// Routes protégées pour la configuration des réservations anonymes
router.use('/admin/anonymous-config', verifyToken, anonymousConfigRoutes);

// Routes d'erreur
router.use((req, res, next) => {
  res.status(404).json({
    status: 'error',
    message: 'Route not found',
    code: 'NOT_FOUND',
  });
});

// Error handling middleware
router.use(errorHandler);

module.exports = router;
