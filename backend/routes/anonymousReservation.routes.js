const express = require('express');
const AnonymousReservationController = require('../controllers/anonymousReservation.controller');

// Middlewares spécialisés
const {
  rateLimitCreation,
  rateLimitConsultation,
  rateLimitModification,
  rateLimitAnnulation,
  rateLimitValidation,
  validateAnonymousReservation,
  validateAccessCode,
  validateUpdateParams,
  logAnonymousAccess,
  checkServiceAvailability,
  csrfProtection
} = require('../middleware/anonymousReservation');

const {
  anomalyDetection,
  injectionProtection,
  requestSizeLimit,
  headerValidation
} = require('../middleware/anonymousSecurity');

const {
  cacheConfiguration,
  cacheValidation,
  cacheAvailability,
  invalidateCache
} = require('../middleware/anonymousCache');

const router = express.Router();

// Middlewares globaux pour toutes les routes anonymes
router.use(headerValidation);
router.use(requestSizeLimit);
router.use(injectionProtection);
router.use(anomalyDetection);
router.use(csrfProtection);

/**
 * Routes publiques pour les réservations anonymes
 * Aucune authentification requise
 */

/**
 * POST /api/reservations-anonymes/demande
 * Créer une demande de réservation anonyme
 * Body: {
 *   date_arrivee: string,
 *   date_depart: string,
 *   heure_debut: string,
 *   heure_fin: string,
 *   chambres: Array,
 *   pseudonyme?: string,
 *   commentaires?: string,
 *   prix_total: number,
 *   complexe_id: number
 * }
 */
router.post('/demande',
  rateLimitCreation,
  checkServiceAvailability,
  validateAnonymousReservation,
  logAnonymousAccess('CREATION_RESERVATION'),
  invalidateCache(['stats:', 'availability:']),
  AnonymousReservationController.createDemandeReservationAnonyme
);

/**
 * GET /api/reservations-anonymes/:codeAcces
 * Consulter une réservation anonyme par code d'accès
 * Params: { codeAcces: string }
 */
router.get('/:codeAcces',
  rateLimitConsultation,
  validateAccessCode,
  logAnonymousAccess('CONSULTATION_RESERVATION'),
  AnonymousReservationController.getReservationAnonyme
);

/**
 * PATCH /api/reservations-anonymes/:codeAcces
 * Modifier une réservation anonyme (fonctionnalités limitées)
 * Params: { codeAcces: string }
 * Body: { commentaires?: string }
 */
router.patch('/:codeAcces',
  rateLimitModification,
  validateAccessCode,
  validateUpdateParams,
  logAnonymousAccess('MODIFICATION_RESERVATION'),
  invalidateCache(['stats:']),
  AnonymousReservationController.updateReservationAnonyme
);

/**
 * DELETE /api/reservations-anonymes/:codeAcces
 * Annuler une réservation anonyme
 * Params: { codeAcces: string }
 */
router.delete('/:codeAcces',
  rateLimitAnnulation,
  validateAccessCode,
  logAnonymousAccess('ANNULATION_RESERVATION'),
  invalidateCache(['stats:', 'availability:']),
  AnonymousReservationController.cancelReservationAnonyme
);

/**
 * POST /api/reservations-anonymes/validate-code
 * Valider un code d'accès (utilitaire pour le frontend)
 * Body: { code_acces: string }
 */
router.post('/validate-code',
  rateLimitValidation,
  cacheValidation,
  logAnonymousAccess('VALIDATION_CODE'),
  AnonymousReservationController.validateAccessCode
);

/**
 * GET /api/reservations-anonymes/stats/public
 * Récupérer les statistiques publiques non sensibles
 */
router.get('/stats/public',
  rateLimitConsultation,
  AnonymousReservationController.getPublicStats
);

/**
 * GET /api/reservations-anonymes/availability/:complexeId
 * Vérifier la disponibilité du service pour un complexe
 * Params: { complexeId: number }
 */
router.get('/availability/:complexeId',
  rateLimitConsultation,
  cacheAvailability,
  AnonymousReservationController.checkServiceAvailability
);

module.exports = router;
