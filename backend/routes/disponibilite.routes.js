const express = require('express');
const router = express.Router();
const DisponibiliteController = require('../controllers/disponibilite.controller');
const { verifyToken } = require('../middleware/auth');

// Routes publiques
router.get('/', DisponibiliteController.getDisponibilites);
router.get('/verification', DisponibiliteController.verifierDisponibilite);

// Routes protégées (réception)
router.post('/', 
  verifyToken, 
  DisponibiliteController.createDisponibilite
);

router.put('/:id', 
  verifyToken, 
  DisponibiliteController.updateDisponibilite
);

module.exports = router; 