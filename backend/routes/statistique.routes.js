const express = require('express');
const { verifyToken } = require('../middleware/auth');
const StatistiqueController = require('../controllers/statistique.controller');
const { checkPermissions, validateDateParams, logStatistiqueAction } = require('../middleware/statistique');

const router = express.Router();

// Taux d'occupation
router.get('/occupation',
  verifyToken,
  checkPermissions,
  validateDateParams,
  logStatistiqueAction('CONSULTATION_OCCUPATION'),
  StatistiqueController.getTauxOccupation
);

// Revenus
router.get('/revenus',
  verifyToken,
  checkPermissions,
  validateDateParams,
  logStatistiqueAction('CONSULTATION_REVENUS'),
  StatistiqueController.getRevenus
);

// Chambres les plus populaires
router.get('/chambres-populaires',
  verifyToken,
  checkPermissions,
  validateDateParams,
  logStatistiqueAction('CONSULTATION_CHAMBRES_POPULAIRES'),
  StatistiqueController.getChambresPopulaires
);

// Génération de rapports
router.get('/rapports',
  verifyToken,
  checkPermissions,
  validateDateParams,
  logStatistiqueAction('GENERATION_RAPPORT'),
  StatistiqueController.genererRapport
);

module.exports = router; 