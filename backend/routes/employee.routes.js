const express = require('express');

const router = express.Router();
const EmployeeController = require('../controllers/employee.controller');
const { verifyToken } = require('../middleware/auth');
const { checkPermission } = require('../middleware/permission');

// Middleware pour vérifier les permissions
const checkEmployeePermission = checkPermission('manage_employees');

// Routes protégées par l'authentification et les permissions
router.use(verifyToken);
router.use(checkEmployeePermission);

// Routes pour la gestion des employés
router.post('/', EmployeeController.createEmployee);
router.get('/', EmployeeController.getEmployees);
router.get('/roles', EmployeeController.getRoles);

// Routes pour les types d'employés (Phase 1 - Simplification permissions)
router.get('/types', EmployeeController.getEmployeeTypes);
router.post('/:id/assign-type', EmployeeController.assignEmployeeType);
router.get('/:employeeId/validate-access/:serviceType', EmployeeController.validateServiceAccess);

// Routes individuelles pour les employés
router.get('/:id', EmployeeController.getEmployee);
router.put('/:id', EmployeeController.updateEmployee);
router.put('/:id/password', EmployeeController.updatePassword);
router.delete('/:id', EmployeeController.deleteEmployee); // Soft delete (désactivation)

// Routes pour la suppression définitive
router.get('/:id/dependencies', EmployeeController.checkEmployeeDependencies);
router.delete('/:id/hard', EmployeeController.hardDeleteEmployee); // Hard delete (suppression définitive)

module.exports = router;
