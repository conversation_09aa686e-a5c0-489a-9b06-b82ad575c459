const express = require('express');
const router = express.Router();
const TemplateController = require('../controllers/template.controller');

// Import des middlewares spécialisés
const {
  cacheTemplates,
  autoInvalidateCache,
  heavyResponseCache
} = require('../middleware/cache.middleware');
const {
  checkImportPermissions,
  logInventaireAction
} = require('../middleware/inventaire.middleware');
const {
  templateLimiter
} = require('../middleware/rateLimiting.middleware');

/**
 * ==================== ROUTES CRUD DES TEMPLATES ====================
 */

/**
 * GET /api/templates
 * Liste des templates avec filtres (chaineId requis en query)
 */
router.get('/',
  templateLimiter,
  checkImportPermissions,
  cacheTemplates,
  TemplateController.getTemplates
);

/**
 * GET /api/templates/:id
 * Détails d'un template spécifique
 */
router.get('/:id',
  templateLimiter,
  checkImportPermissions,
  heavyResponseCache('templates', (req) => `template:${req.params.id}`, 3600),
  TemplateController.getTemplateById
);

/**
 * POST /api/templates
 * Création d'un nouveau template
 */
router.post('/',
  templateLimiter,
  checkImportPermissions,
  autoInvalidateCache(['templates']),
  logInventaireAction('CREATE_TEMPLATE'),
  TemplateController.createTemplate
);

/**
 * PUT /api/templates/:id
 * Mise à jour d'un template existant
 */
router.put('/:id',
  templateLimiter,
  checkImportPermissions,
  autoInvalidateCache(['templates']),
  logInventaireAction('UPDATE_TEMPLATE'),
  TemplateController.updateTemplate
);

/**
 * DELETE /api/templates/:id
 * Suppression d'un template
 */
router.delete('/:id',
  templateLimiter,
  checkImportPermissions,
  autoInvalidateCache(['templates']),
  logInventaireAction('DELETE_TEMPLATE'),
  TemplateController.deleteTemplate
);

/**
 * ==================== ROUTES DE GÉNÉRATION DE FICHIERS ====================
 */

/**
 * POST /api/templates/:id/generate
 * Génération d'un fichier Excel template
 */
router.post('/:id/generate', TemplateController.generateTemplateFile);

/**
 * GET /api/templates/:id/download
 * Téléchargement direct d'un fichier template
 */
router.get('/:id/download', TemplateController.downloadTemplate);

/**
 * ==================== ROUTES DE GESTION AVANCÉE ====================
 */

/**
 * POST /api/templates/:id/duplicate
 * Duplication d'un template existant
 */
router.post('/:id/duplicate', TemplateController.duplicateTemplate);

/**
 * GET /api/templates/defaults
 * Récupération des templates par défaut
 */
router.get('/defaults', TemplateController.getDefaultTemplates);

/**
 * POST /api/templates/create-from-default
 * Création d'un template à partir d'un modèle par défaut
 */
router.post('/create-from-default', TemplateController.createFromDefault);

/**
 * ==================== ROUTES DE VALIDATION ET APERÇU ====================
 */

/**
 * POST /api/templates/validate
 * Validation de la structure d'un template
 */
router.post('/validate', TemplateController.validateTemplate);

/**
 * GET /api/templates/types
 * Liste des types de services et d'imports disponibles
 */
router.get('/types', TemplateController.getTemplateTypes);

/**
 * GET /api/templates/preview/:id
 * Aperçu de la structure d'un template
 */
router.get('/preview/:id', TemplateController.previewTemplate);

module.exports = router;
