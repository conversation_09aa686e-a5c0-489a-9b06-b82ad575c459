const express = require('express');
const router = express.Router();
const TableController = require('../controllers/table.controller');
const { verifyToken } = require('../middleware/auth');
const { checkPermission } = require('../middleware/permission');
const { validateTableStatusChange } = require('../middleware/restaurantValidation.middleware');
const { logTableOperation, logValidationResult } = require('../middleware/restaurantLogging.middleware');

/**
 * Routes pour la gestion des tables de restaurant/bar
 * Toutes les routes nécessitent une authentification
 */

// Middleware d'authentification pour toutes les routes
router.use(verifyToken);

/**
 * ==================== ROUTES CRUD DES TABLES ====================
 */

/**
 * GET /api/tables
 * Récupérer toutes les tables d'un complexe
 */
router.get('/', 
  checkPermission('view_tables'),
  TableController.getAllTables
);

/**
 * GET /api/tables/service/:serviceId
 * Récupérer les tables d'un service spécifique
 */
router.get('/service/:serviceId', 
  checkPermission('view_tables'),
  TableController.getTablesByService
);

/**
 * POST /api/tables
 * Créer une nouvelle table
 */
router.post('/', 
  checkPermission('manage_tables'),
  TableController.createTable
);

/**
 * PUT /api/tables/:id
 * Mettre à jour une table (statut, position, etc.)
 */
router.put('/:id', 
  checkPermission('manage_tables'),
  TableController.updateTable
);

/**
 * DELETE /api/tables/:id
 * Supprimer une table (désactivation)
 */
router.delete('/:id', 
  checkPermission('manage_tables'),
  TableController.deleteTable
);

/**
 * ==================== ROUTES GESTION DES STATUTS ====================
 */

/**
 * PUT /api/tables/:id/status
 * Mettre à jour le statut d'une table avec validation
 */
router.put('/:id/status',
  checkPermission('operate_restaurant_pos'),
  logTableOperation('UPDATE_TABLE_STATUS'),
  validateTableStatusChange,
  logValidationResult,
  TableController.updateTableStatus
);

/**
 * ==================== ROUTES LAYOUT DES TABLES ====================
 */

/**
 * GET /api/tables/service/:serviceId/layout
 * Récupérer le layout des tables d'un service
 */
router.get('/service/:serviceId/layout', 
  checkPermission('view_tables'),
  TableController.getTableLayout
);

/**
 * ==================== ROUTES RÉSERVATIONS DE TABLES ====================
 */

/**
 * POST /api/tables/reservations
 * Créer une réservation de table
 */
router.post('/reservations', 
  checkPermission('manage_table_reservations'),
  TableController.createTableReservation
);

/**
 * GET /api/tables/service/:serviceId/reservations
 * Récupérer les réservations d'un service pour une date
 * Query params: date (YYYY-MM-DD)
 */
router.get('/service/:serviceId/reservations', 
  checkPermission('view_table_reservations'),
  TableController.getTableReservations
);

/**
 * DELETE /api/tables/reservations/:id
 * Annuler une réservation de table
 */
router.delete('/reservations/:id', 
  checkPermission('manage_table_reservations'),
  TableController.cancelTableReservation
);

module.exports = router;
