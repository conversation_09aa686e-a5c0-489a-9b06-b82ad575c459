const express = require('express');
const { verifyToken } = require('../middleware/auth');
const PaiementController = require('../controllers/paiement.controller');
const { validatePaiement, checkPermissions, logPaiementAction } = require('../middleware/paiement');

const router = express.Router();

// Routes protégées
router.post('/', 
  verifyToken,
  checkPermissions,
    validatePaiement,
  logPaiementAction('CREATION'),
    PaiementController.creerPaiement
);

router.get('/:id', 
  verifyToken,
  checkPermissions,
  logPaiementAction('CONSULTATION'),
  PaiementController.getPaiementById
);

router.put('/:id/statut', 
    verifyToken,
  checkPermissions,
  logPaiementAction('MISE_A_JOUR_STATUT'),
  PaiementController.updatePaiementStatus
);

router.get('/reservation/:id', 
    verifyToken,
  checkPermissions,
  logPaiementAction('CONSULTATION_RESERVATION'),
  PaiementController.getPaiementsReservation
);

// Routes publiques
router.get('/verification/:id', 
  PaiementController.verifierStatutPaiement
);

module.exports = router; 