const express = require('express');
const router = express.Router();
const ClientController = require('../controllers/client.controller');
const { verifyToken } = require('../middleware/auth');

// Routes protégées (réception)
router.post('/', 
  verifyToken, 
  ClientController.createClient
);

router.get('/:id', 
  verifyToken, 
  ClientController.getClientById
);

router.put('/:id', 
  verifyToken, 
  ClientController.updateClient
);

router.get('/:id/reservations', 
  verifyToken, 
  ClientController.getClientReservations
);

module.exports = router; 