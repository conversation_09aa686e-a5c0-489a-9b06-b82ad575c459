const express = require('express');
const AuthController = require('../controllers/auth.controller');
const { verifyToken, checkRole } = require('../middleware/auth');
const { body } = require('express-validator');
const AuthService = require('../services/auth.service');
const logger = require('../logger');

const router = express.Router();

// Public routes
router.post('/login', AuthController.validateLogin(), AuthController.login);
router.post('/logout', verifyToken, AuthController.logout);

// Super Admin specific routes
router.post('/super-admin/login', 
  [
    body('email').isEmail().withMessage('Invalid email format'),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters long'),
  ],
  async (req, res) => {
    try {
      const result = await AuthService.login(req.body.email, req.body.password, 'super_admin');
      return res.json(result);
    } catch (error) {
      logger.error('Super admin login error:', error);
      return res.status(401).json({ message: error.message });
    }
  }
);

// Patron d'hôtel (Admin Chaine) routes
router.post('/patron/login',
  [
    body('email').isEmail().withMessage('Invalid email format'),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters long'),
  ],
  async (req, res) => {
    try {
      const result = await AuthService.login(req.body.email, req.body.password, 'admin_chaine');
      return res.json(result);
    } catch (error) {
      logger.error('Patron login error:', error);
      return res.status(401).json({ message: error.message });
    }
  }
);

// Admin de complexe routes
router.post('/admin-complexe/login',
  [
    body('email').isEmail().withMessage('Invalid email format'),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters long'),
    // Pas besoin de complexe_id pour admin_complexe - il est récupéré automatiquement depuis la DB
  ],
  async (req, res) => {
    try {
      const result = await AuthService.login(req.body.email, req.body.password, 'admin_complexe');
      return res.json(result);
    } catch (error) {
      logger.error('Admin complexe login error:', error);
      return res.status(401).json({ message: error.message });
    }
  }
);

// Employé routes
router.post('/employe/login',
  [
    body('email').isEmail().withMessage('Invalid email format'),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters long'),
    // Pas besoin de complexe_id pour employe - il est récupéré automatiquement depuis la DB
  ],
  async (req, res) => {
    try {
      const result = await AuthService.login(req.body.email, req.body.password, 'employe');
      return res.json(result);
    } catch (error) {
      logger.error('Employe login error:', error);
      return res.status(401).json({ message: error.message });
    }
  }
);

// Route pour vérifier le statut de la session
router.get('/check-session', verifyToken, (req, res) => {
  try {
    return res.json({
      valid: true,
      user: {
        id: req.user.id,
        email: req.user.email,
        role: req.user.role,
        chaine_id: req.user.chaine_id,
        complexe_id: req.user.complexe_id,
        role_id: req.user.role_id
      }
    });
  } catch (error) {
    logger.error('Session check error:', error);
    return res.status(401).json({ message: 'Invalid session' });
  }
});

module.exports = router;
