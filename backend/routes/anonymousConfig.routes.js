const express = require('express');
const AnonymousConfigController = require('../controllers/anonymousConfig.controller');
const { verifyToken } = require('../middleware/auth');
const { checkPermission } = require('../middleware/permission');

// Middlewares spécialisés
const {
  anomalyDetection,
  injectionProtection,
  requestSizeLimit,
  headerValidation
} = require('../middleware/anonymousSecurity');

const {
  cacheConfiguration,
  invalidateCache,
  exposeCacheStats
} = require('../middleware/anonymousCache');

const router = express.Router();

// Middlewares globaux pour toutes les routes d'administration
router.use(headerValidation);
router.use(requestSizeLimit);
router.use(injectionProtection);
router.use(anomalyDetection);
router.use(exposeCacheStats);

/**
 * Routes protégées pour la configuration des réservations anonymes
 * Authentification et permissions requises
 */

// Middleware d'authentification pour toutes les routes
router.use(verifyToken);

/**
 * GET /api/admin/anonymous-config/:complexeId
 * Récupérer la configuration d'un complexe
 * Permissions: view_anonymous_config
 */
router.get('/:complexeId',
  checkPermission('view_anonymous_config'),
  cacheConfiguration,
  AnonymousConfigController.getConfigurationComplexe
);

/**
 * PUT /api/admin/anonymous-config/:complexeId
 * Mettre à jour la configuration d'un complexe
 * Permissions: manage_anonymous_config
 */
router.put('/:complexeId',
  checkPermission('manage_anonymous_config'),
  invalidateCache(['config:', 'availability:', 'stats:']),
  AnonymousConfigController.updateConfigurationComplexe
);

/**
 * PATCH /api/admin/anonymous-config/:complexeId/toggle
 * Activer/désactiver les réservations anonymes pour un complexe
 * Permissions: manage_anonymous_config
 */
router.patch('/:complexeId/toggle',
  checkPermission('manage_anonymous_config'),
  invalidateCache(['config:', 'availability:']),
  AnonymousConfigController.toggleReservationsAnonymes
);

/**
 * GET /api/admin/anonymous-config
 * Récupérer toutes les configurations (pour les admins de chaîne)
 * Permissions: view_all_anonymous_configs
 */
router.get('/', 
  checkPermission('view_all_anonymous_configs'),
  AnonymousConfigController.getAllConfigurations
);

/**
 * GET /api/admin/anonymous-config/:complexeId/stats
 * Récupérer les statistiques d'utilisation
 * Permissions: view_anonymous_stats
 */
router.get('/:complexeId/stats', 
  checkPermission('view_anonymous_stats'),
  AnonymousConfigController.getUsageStatistics
);

/**
 * DELETE /api/admin/anonymous-config/cleanup
 * Nettoyer les configurations obsolètes
 * Permissions: manage_system (super admin uniquement)
 */
router.delete('/cleanup', 
  checkPermission('manage_system'),
  AnonymousConfigController.cleanupObsoleteConfigurations
);

/**
 * POST /api/admin/anonymous-config/:complexeId/default
 * Créer une configuration par défaut pour un complexe
 * Permissions: manage_anonymous_config
 */
router.post('/:complexeId/default', 
  checkPermission('manage_anonymous_config'),
  AnonymousConfigController.createDefaultConfiguration
);

/**
 * GET /api/admin/anonymous-config/:complexeId/export
 * Exporter la configuration d'un complexe
 * Permissions: view_anonymous_config
 */
router.get('/:complexeId/export', 
  checkPermission('view_anonymous_config'),
  AnonymousConfigController.exportConfiguration
);

/**
 * POST /api/admin/anonymous-config/:complexeId/import
 * Importer une configuration pour un complexe
 * Permissions: manage_anonymous_config
 */
router.post('/:complexeId/import', 
  checkPermission('manage_anonymous_config'),
  AnonymousConfigController.importConfiguration
);

module.exports = router;
