const express = require('express');
const router = express.Router();
const TransactionController = require('../controllers/transaction.controller');

// Créer une nouvelle transaction
router.post('/', TransactionController.createTransaction);

// Récupérer une transaction par ID
router.get('/:transactionId', TransactionController.getTransactionById);

// Mettre à jour le statut d'une transaction
router.patch('/:transactionId/statut', TransactionController.updateTransactionStatus);

// Récupérer les transactions d'une session
router.get('/session/:sessionId', TransactionController.getTransactionsBySession);

// Récupérer les transactions d'un client
router.get('/client/:clientId', TransactionController.getTransactionsByClient);

// Vérifier le solde d'une transaction
router.get('/:transactionId/solde', TransactionController.verifierSoldeTransaction);

module.exports = router; 
