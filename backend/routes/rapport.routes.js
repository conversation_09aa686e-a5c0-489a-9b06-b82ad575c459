const express = require('express');
const { verifyToken } = require('../middleware/auth');
const RapportController = require('../controllers/rapport.controller');
const { checkPermissions } = require('../middleware/rapport');

const router = express.Router();

// Routes protégées
router.get('/paiements', 
  verifyToken,
  checkPermissions,
  RapportController.getRapportPaiements
);

router.get('/revenus', 
  verifyToken,
  checkPermissions,
  RapportController.getRapportRevenus
);

module.exports = router;