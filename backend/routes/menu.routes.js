const express = require('express');
const router = express.Router();
const MenuController = require('../controllers/menu.controller');
const { verifyToken } = require('../middleware/auth');
const { checkPermission } = require('../middleware/permission');

/**
 * Routes pour la gestion des menus de restaurant/bar
 * Toutes les routes nécessitent une authentification
 */

// Middleware d'authentification pour toutes les routes
router.use(verifyToken);

/**
 * ==================== ROUTES CONSULTATION DES MENUS ====================
 */

/**
 * GET /api/menu/service/:serviceId
 * Récupérer le menu complet d'un service avec catégories
 */
router.get('/service/:serviceId', 
  checkPermission('view_menu'),
  MenuController.getMenuByService
);

/**
 * GET /api/menu/service/:serviceId/category/:categoryId
 * Récupérer les produits d'une catégorie spécifique
 */
router.get('/service/:serviceId/category/:categoryId', 
  checkPermission('view_menu'),
  MenuController.getProductsByCategory
);

/**
 * GET /api/menu/service/:serviceId/availability
 * Récupérer le menu avec informations de disponibilité en temps réel
 */
router.get('/service/:serviceId/availability', 
  checkPermission('operate_restaurant_pos'),
  MenuController.getMenuWithAvailability
);

/**
 * ==================== ROUTES GESTION DES PRIX ====================
 */

/**
 * PUT /api/menu/service/:serviceId/product/:productId/price
 * Mettre à jour le prix d'un produit pour un service spécifique
 */
router.put('/service/:serviceId/product/:productId/price', 
  checkPermission('manage_menu_prices'),
  MenuController.updateServicePrice
);

/**
 * PUT /api/menu/service/:serviceId/prices/bulk
 * Mise à jour en lot des prix pour un service
 */
router.put('/service/:serviceId/prices/bulk', 
  checkPermission('manage_menu_prices'),
  MenuController.bulkUpdatePrices
);

/**
 * ==================== ROUTES RECHERCHE ET DISPONIBILITÉ ====================
 */

/**
 * GET /api/menu/service/:serviceId/search
 * Rechercher des produits dans le menu
 * Query params: q (terme de recherche)
 */
router.get('/service/:serviceId/search', 
  checkPermission('view_menu'),
  MenuController.searchProducts
);

/**
 * GET /api/menu/product/:productId/availability
 * Vérifier la disponibilité d'un produit spécifique
 * Query params: quantite (optionnel, défaut: 1)
 */
router.get('/product/:productId/availability', 
  checkPermission('operate_restaurant_pos'),
  MenuController.checkProductAvailability
);

module.exports = router;
