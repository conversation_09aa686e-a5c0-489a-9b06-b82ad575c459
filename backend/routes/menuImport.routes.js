const express = require('express');
const router = express.Router();
const multer = require('multer');
const MenuImportController = require('../controllers/menuImport.controller');
const { verifyToken } = require('../middleware/auth');
const { checkPermission } = require('../middleware/permission');

/**
 * Configuration de multer pour l'upload de fichiers Excel
 */
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB max
  },
  fileFilter: (req, file, cb) => {
    // Accepter seulement les fichiers Excel
    const allowedMimeTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];
    
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Seuls les fichiers Excel (.xlsx, .xls) sont autorisés'), false);
    }
  }
});

/**
 * Middleware d'authentification pour toutes les routes
 */
router.use(verifyToken);

/**
 * ==================== ROUTES D'IMPORT DE MENUS ====================
 */

/**
 * POST /api/menu-import/restaurant/:serviceId
 * Importer un menu restaurant depuis un fichier Excel
 * 
 * @param {number} serviceId - ID du service restaurant
 * @body {file} menuFile - Fichier Excel du menu
 * @body {object} options - Options d'import (optionnel)
 */
router.post('/restaurant/:serviceId',
  checkPermission('manage_services'),
  upload.single('menuFile'),
  MenuImportController.importRestaurantMenu
);

/**
 * POST /api/menu-import/bar/:serviceId
 * Importer une carte bar depuis un fichier Excel
 * 
 * @param {number} serviceId - ID du service bar
 * @body {file} menuFile - Fichier Excel de la carte
 * @body {object} options - Options d'import (optionnel)
 */
router.post('/bar/:serviceId',
  checkPermission('manage_services'),
  upload.single('menuFile'),
  MenuImportController.importBarMenu
);

/**
 * ==================== ROUTES DE VALIDATION ====================
 */

/**
 * POST /api/menu-import/validate/restaurant
 * Valider un fichier Excel de menu restaurant sans l'importer
 * 
 * @body {file} menuFile - Fichier Excel à valider
 */
router.post('/validate/restaurant',
  checkPermission('view_services'),
  upload.single('menuFile'),
  MenuImportController.validateRestaurantMenu
);

/**
 * POST /api/menu-import/validate/bar
 * Valider un fichier Excel de carte bar sans l'importer
 * 
 * @body {file} menuFile - Fichier Excel à valider
 */
router.post('/validate/bar',
  checkPermission('view_services'),
  upload.single('menuFile'),
  MenuImportController.validateBarMenu
);

/**
 * ==================== ROUTES DE STATUT ====================
 */

/**
 * GET /api/menu-import/status/:serviceId
 * Obtenir le statut d'import pour un service
 * 
 * @param {number} serviceId - ID du service
 */
router.get('/status/:serviceId',
  checkPermission('view_services'),
  MenuImportController.getImportStatus
);

/**
 * ==================== ROUTES DE TÉLÉCHARGEMENT DE TEMPLATES ====================
 */

/**
 * GET /api/menu-import/template/restaurant
 * Télécharger le template Excel pour menu restaurant
 */
router.get('/template/restaurant',
  checkPermission('view_services'),
  (req, res) => {
    // Rediriger vers le service de templates
    res.redirect('/api/template-generator/restaurant');
  }
);

/**
 * GET /api/menu-import/template/bar
 * Télécharger le template Excel pour carte bar
 */
router.get('/template/bar',
  checkPermission('view_services'),
  (req, res) => {
    // Rediriger vers le service de templates
    res.redirect('/api/template-generator/bar');
  }
);

/**
 * ==================== MIDDLEWARE DE GESTION D'ERREURS ====================
 */

/**
 * Middleware de gestion d'erreurs pour multer
 */
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'Le fichier est trop volumineux (maximum 10MB)'
      });
    }
    
    return res.status(400).json({
      success: false,
      message: `Erreur d'upload: ${error.message}`
    });
  }
  
  if (error.message.includes('fichiers Excel')) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
  
  next(error);
});

/**
 * ==================== ROUTES D'INFORMATION ====================
 */

/**
 * GET /api/menu-import/info
 * Informations sur les imports de menus
 */
router.get('/info', (req, res) => {
  res.json({
    success: true,
    data: {
      supportedFormats: ['.xlsx', '.xls'],
      maxFileSize: '10MB',
      requiredFields: {
        restaurant: ['nom_plat', 'categorie', 'prix_vente'],
        bar: ['nom_boisson', 'categorie', 'prix_vente']
      },
      optionalFields: {
        restaurant: ['description', 'temps_preparation', 'allergenes', 'image_url', 'actif'],
        bar: ['description', 'degre_alcool', 'volume_ml', 'allergenes', 'image_url', 'actif']
      },
      validCategories: {
        restaurant: ['Entrée', 'Plat Principal', 'Dessert', 'Boisson'],
        bar: ['Cocktail', 'Bière', 'Vin', 'Soft', 'Spiritueux', 'Alcool']
      },
      workflow: [
        '1. Télécharger le template approprié',
        '2. Remplir le fichier Excel avec vos données',
        '3. Valider le fichier (optionnel)',
        '4. Importer le fichier',
        '5. Vérifier le rapport d\'import'
      ]
    },
    message: 'Informations sur l\'import de menus récupérées avec succès'
  });
});

module.exports = router;
