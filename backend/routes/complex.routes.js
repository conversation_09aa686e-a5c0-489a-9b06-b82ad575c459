const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middleware/auth');
const complexController = require('../controllers/complex.controller');

// Routes protégées par authentification
router.use(verifyToken);

// Récupérer tous les complexes
router.get('/', complexController.getAllComplexes);

// Récupérer un complexe par son ID
router.get('/:id', complexController.getComplexById);

// Créer un nouveau complexe
router.post('/', complexController.createComplex);

// Mettre à jour un complexe
router.put('/:id', complexController.updateComplex);

// Supprimer un complexe
router.delete('/:id', complexController.deleteComplex);

module.exports = router; 