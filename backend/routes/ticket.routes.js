const express = require('express');
const TicketController = require('../controllers/ticket.controller');
const {
  validateTicketGeneration,
  validateTicketStatus,
  validateServiceTicketCreation,
  checkPermissions,
  logTicketAction
} = require('../middleware/ticket');

const router = express.Router();

// Routes publiques
router.get('/verification/:numero', TicketController.verifierTicket);

// Routes client
router.get('/client/:reservationId', TicketController.getTicketsReservation);
router.get('/client/qrcode/:ticketId', TicketController.getQRCode);
router.get('/client/pdf/:ticketId', TicketController.getPDFTicket);

// Routes réception
router.post('/reception/generer/:reservationId', 
  checkPermissions,
  validateTicketGeneration,
  logTicketAction('GENERATION'),
  TicketController.genererTickets
);

router.post('/reception/valider/:ticketId', 
  checkPermissions,
  logTicketAction('VALIDATION'),
  TicketController.validerTicket
);

router.post('/reception/imprimer/:ticketId', 
  checkPermissions,
  logTicketAction('IMPRESSION'),
  TicketController.imprimerTicket
);

router.get('/reception/statut/:ticketId', 
  checkPermissions,
  logTicketAction('CONSULTATION_STATUT'),
  TicketController.getStatutTicket
);

// Routes admin
router.get('/admin/statistiques', 
  checkPermissions,
  logTicketAction('CONSULTATION_STATISTIQUES'),
  TicketController.getStatistiquesTickets
);

router.get('/admin/rapports', 
  checkPermissions,
  logTicketAction('GENERATION_RAPPORTS'),
  TicketController.genererRapportsTickets
);

router.post('/admin/config', 
  checkPermissions,
  logTicketAction('CONFIGURATION'),
  TicketController.configurerTickets
);

// Routes génériques
router.post('/reservation/:numero', 
  checkPermissions,
  validateTicketGeneration,
  logTicketAction('GENERATION'),
  TicketController.genererTicketFromReservation
);

router.get('/:ticketId', 
  checkPermissions,
  logTicketAction('CONSULTATION'),
  TicketController.getTicketById
);

router.patch('/:ticketId/statut', 
  checkPermissions,
  validateTicketStatus,
  logTicketAction('MISE_A_JOUR_STATUT'),
  TicketController.updateTicketStatus
);

router.get('/:ticketId/imprimer',
  checkPermissions,
  logTicketAction('IMPRESSION'),
  TicketController.imprimerTicket
);

// ==================== ROUTES POUR TICKETS DE SERVICE ====================

// Créer un ticket de service (piscine, bar, restaurant)
router.post('/service',
  checkPermissions,
  validateServiceTicketCreation,
  logTicketAction('CREATION_SERVICE'),
  TicketController.createServiceTicket
);

// Récupérer les tickets d'un service spécifique
router.get('/service/:serviceId',
  checkPermissions,
  logTicketAction('CONSULTATION_SERVICE'),
  TicketController.getServiceTickets
);

// Marquer un ticket de service comme utilisé
router.put('/service/:ticketId/utiliser',
  checkPermissions,
  logTicketAction('UTILISATION_SERVICE'),
  TicketController.markServiceTicketUsed
);

// Vérifier un ticket de service par son numéro
router.get('/service/verification/:numero',
  TicketController.verifyServiceTicket
);

// Récupérer tous les tickets d'une session de caisse
router.get('/session/:sessionId',
  checkPermissions,
  logTicketAction('CONSULTATION_SESSION'),
  TicketController.getTicketsBySession
);

module.exports = router;