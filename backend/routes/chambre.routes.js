const express = require('express');
const router = express.Router();
const ChambreController = require('../controllers/chambre.controller');
const { verifyToken } = require('../middleware/auth');

// Routes de consultation (nécessitent une authentification)
router.get('/', verifyToken, ChambreController.getChambres);
router.get('/:id', verifyToken, ChambreController.getChambreById);
router.get('/:id/statistiques', verifyToken, ChambreController.getStatistiquesChambre);
router.get('/:id/calendrier', verifyToken, ChambreController.getCalendrierChambre);
router.get('/:id/historique', verifyToken, ChambreController.getHistoriqueChambre);

// Routes de gestion des chambres (protégées)
router.post('/', verifyToken, ChambreController.createChambre);
router.put('/:id', verifyToken, ChambreController.updateChambre);
router.delete('/:id', verifyToken, ChambreController.deleteChambre);

// Route de mise à jour du statut (protégée)
router.put('/:chambreId/statut', verifyToken, ChambreController.updateStatutChambre);

module.exports = router; 