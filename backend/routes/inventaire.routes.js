const express = require('express');
const router = express.Router();
const InventaireController = require('../controllers/inventaire.controller');

// Import des middlewares spécialisés
const {
  cacheIngredients,
  cacheAnalytics,
  autoInvalidateCache,
  heavyResponseCache
} = require('../middleware/cache.middleware');
const {
  checkInventairePermissions,
  logInventaireAction
} = require('../middleware/inventaire.middleware');
const {
  ingredientLimiter,
  stockLimiter,
  analyticsLimiter
} = require('../middleware/rateLimiting.middleware');

/**
 * ==================== ROUTES DES INGRÉDIENTS ====================
 */

/**
 * GET /api/inventaire/ingredients
 * Liste paginée des ingrédients avec filtres
 */
router.get('/ingredients',
  ingredientLimiter,
  checkInventairePermissions(['view_inventory']),
  cacheIngredients,
  InventaireController.getIngredients
);

/**
 * POST /api/inventaire/ingredients
 * Création d'un nouvel ingrédient
 */
router.post('/ingredients',
  ingredientLimiter,
  checkInventairePermissions(['manage_inventory']),
  autoInvalidateCache(['ingredients', 'analytics']),
  logInventaireAction('CREATE_INGREDIENT'),
  InventaireController.createIngredient
);

/**
 * PUT /api/inventaire/ingredients/:id
 * Mise à jour d'un ingrédient
 */
router.put('/ingredients/:id',
  ingredientLimiter,
  checkInventairePermissions(['manage_inventory']),
  autoInvalidateCache(['ingredients', 'recipes', 'analytics']),
  logInventaireAction('UPDATE_INGREDIENT'),
  InventaireController.updateIngredient
);

/**
 * DELETE /api/inventaire/ingredients/:id
 * Suppression logique d'un ingrédient (désactivation)
 */
router.delete('/ingredients/:id', InventaireController.deleteIngredient);

/**
 * GET /api/inventaire/ingredients/search
 * Recherche textuelle avancée d'ingrédients
 */
router.get('/ingredients/search', InventaireController.searchIngredients);

/**
 * GET /api/inventaire/ingredients/categories/:complexeId
 * Récupération des catégories d'ingrédients disponibles
 */
router.get('/ingredients/categories/:complexeId', InventaireController.getIngredientCategories);

/**
 * ==================== ROUTES DU STOCK ====================
 */

/**
 * GET /api/inventaire/stock/:complexeId
 * État du stock par complexe avec filtres optionnels
 */
router.get('/stock/:complexeId',
  stockLimiter,
  checkInventairePermissions(['view_inventory']),
  heavyResponseCache('analytics', (req) => `stock:${req.params.complexeId}:${JSON.stringify(req.query)}`, 600),
  InventaireController.getStock
);

/**
 * PUT /api/inventaire/stock/:ingredientId
 * Mise à jour manuelle du stock d'un ingrédient
 */
router.put('/stock/:ingredientId',
  stockLimiter,
  checkInventairePermissions(['manage_inventory']),
  autoInvalidateCache(['ingredients', 'analytics']),
  logInventaireAction('UPDATE_STOCK'),
  InventaireController.updateStock
);

/**
 * GET /api/inventaire/alerts/:complexeId
 * Alertes de stock faible et rupture
 */
router.get('/alerts/:complexeId',
  stockLimiter,
  checkInventairePermissions(['view_inventory']),
  heavyResponseCache('analytics', (req) => `alerts:${req.params.complexeId}`, 300),
  InventaireController.getStockAlerts
);

/**
 * POST /api/inventaire/stock/from-inventory/:inventaireId
 * Mise à jour du stock depuis un inventaire physique
 */
router.post('/stock/from-inventory/:inventaireId',
  stockLimiter,
  checkInventairePermissions(['manage_inventory']),
  autoInvalidateCache(['ingredients', 'analytics']),
  logInventaireAction('UPDATE_STOCK_FROM_INVENTORY'),
  InventaireController.updateStockFromInventaire
);

/**
 * ==================== ROUTES D'ANALYSES ====================
 */

/**
 * GET /api/inventaire/analytics/:complexeId
 * Analyses de consommation et valeur du stock
 */
router.get('/analytics/:complexeId',
  analyticsLimiter,
  checkInventairePermissions(['view_inventory', 'view_costs']),
  cacheAnalytics,
  InventaireController.getInventaireAnalytics
);

/**
 * GET /api/inventaire/value/:complexeId
 * Calcul de la valeur totale du stock
 */
router.get('/value/:complexeId',
  analyticsLimiter,
  checkInventairePermissions(['view_costs']),
  heavyResponseCache('analytics', (req) => `value:${req.params.complexeId}`, 900),
  InventaireController.getStockValue
);

/**
 * GET /api/inventaire/dashboard/:complexeId
 * Dashboard avec métriques clés de l'inventaire
 */
router.get('/dashboard/:complexeId',
  analyticsLimiter,
  checkInventairePermissions(['view_inventory']),
  heavyResponseCache('analytics', (req) => `dashboard:${req.params.complexeId}`, 600),
  InventaireController.getInventaireDashboard
);

module.exports = router;
