const express = require('express');
const router = express.Router();
const POSStockController = require('../controllers/posStock.controller');
const { verifyToken } = require('../middleware/auth');

// Import des middlewares spécialisés
const { 
  heavyResponseCache,
  autoInvalidateCache 
} = require('../middleware/cache.middleware');
const { 
  checkInventairePermissions,
  logInventaireAction 
} = require('../middleware/inventaire.middleware');
const { 
  stockLimiter,
  analyticsLimiter 
} = require('../middleware/rateLimiting.middleware');

/**
 * ==================== ROUTES DE TRAITEMENT DES TRANSACTIONS ====================
 */

/**
 * POST /api/pos-stock/process-transaction
 * Traitement d'une transaction POS avec déduction automatique du stock
 */
router.post('/process-transaction',
  stockLimiter,
  checkInventairePermissions(['manage_inventory', 'operate_pos']),
  autoInvalidateCache(['ingredients', 'analytics']),
  logInventaireAction('PROCESS_TRANSACTION_WITH_STOCK'),
  POSStockController.processTransactionWithStock
);

/**
 * POST /api/pos-stock/check-availability
 * Vérification de la disponibilité des ingrédients pour un produit
 */
router.post('/check-availability',
  stockLimiter,
  checkInventairePermissions(['view_inventory', 'operate_pos']),
  POSStockController.checkIngredientAvailability
);

/**
 * ==================== ROUTES D'ALERTES ET MONITORING ====================
 */

/**
 * GET /api/pos-stock/alerts/:complexeId
 * Génération des alertes de stock faible
 */
router.get('/alerts/:complexeId',
  analyticsLimiter,
  checkInventairePermissions(['view_inventory']),
  heavyResponseCache('analytics', (req) => `pos-stock:alerts:${req.params.complexeId}`, 300),
  POSStockController.generateStockAlerts
);

/**
 * GET /api/pos-stock/consumption/:complexeId
 * Calcul de la consommation réelle d'ingrédients
 */
router.get('/consumption/:complexeId',
  analyticsLimiter,
  checkInventairePermissions(['view_inventory', 'view_costs']),
  heavyResponseCache('analytics', (req) => `pos-stock:consumption:${req.params.complexeId}:${JSON.stringify(req.query)}`, 600),
  POSStockController.calculateIngredientConsumption
);

/**
 * GET /api/pos-stock/predictions/:complexeId
 * Prévisions de rupture de stock basées sur les ventes
 */
router.get('/predictions/:complexeId',
  analyticsLimiter,
  checkInventairePermissions(['view_inventory']),
  heavyResponseCache('analytics', (req) => `pos-stock:predictions:${req.params.complexeId}:${req.query.days || 7}`, 1800),
  POSStockController.predictStockBreakdown
);

/**
 * GET /api/pos-stock/order-suggestions/:complexeId
 * Suggestions de commandes optimales basées sur la consommation
 */
router.get('/order-suggestions/:complexeId',
  analyticsLimiter,
  checkInventairePermissions(['view_inventory', 'manage_inventory']),
  heavyResponseCache('analytics', (req) => `pos-stock:orders:${req.params.complexeId}:${req.query.days || 14}`, 3600),
  POSStockController.suggestOptimalOrders
);

/**
 * ==================== ROUTES DE RAPPORTS ====================
 */

/**
 * GET /api/pos-stock/dashboard/:complexeId
 * Dashboard complet de l'intégration POS-Stock
 */
router.get('/dashboard/:complexeId',
  analyticsLimiter,
  checkInventairePermissions(['view_inventory']),
  heavyResponseCache('analytics', (req) => `pos-stock:dashboard:${req.params.complexeId}`, 900),
  POSStockController.getDashboard
);

/**
 * GET /api/pos-stock/reports/stock-impact/:complexeId
 * Rapport d'impact des ventes sur le stock
 */
router.get('/reports/stock-impact/:complexeId',
  analyticsLimiter,
  checkInventairePermissions(['view_inventory', 'view_reports']),
  heavyResponseCache('analytics', (req) => `pos-stock:impact:${req.params.complexeId}:${JSON.stringify(req.query)}`, 1800),
  POSStockController.getStockImpactReport
);

/**
 * ==================== ROUTES DE CONFIGURATION ====================
 */

/**
 * POST /api/pos-stock/configure-auto-deduction/:serviceId
 * Configuration de la déduction automatique pour un service
 */
router.post('/configure-auto-deduction/:serviceId',
  stockLimiter,
  checkInventairePermissions(['manage_inventory']),
  logInventaireAction('CONFIGURE_AUTO_DEDUCTION'),
  POSStockController.configureAutoDeduction
);

/**
 * GET /api/pos-stock/configuration/:serviceId
 * Récupération de la configuration d'un service
 */
router.get('/configuration/:serviceId',
  checkInventairePermissions(['view_inventory']),
  POSStockController.getServiceConfiguration
);

// Routes de vérification de disponibilité
router.get('/check/:productId', verifyToken, POSStockController.checkProductAvailability);
router.get('/status/:productId', verifyToken, POSStockController.getProductStockStatus);
router.post('/validate-order', verifyToken, POSStockController.validateOrderStock);

module.exports = router;
