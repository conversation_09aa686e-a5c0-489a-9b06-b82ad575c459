const express = require('express');
const router = express.Router();
const TemplateGeneratorController = require('../controllers/templateGenerator.controller');
const { verifyToken } = require('../middleware/auth');
const { checkPermission } = require('../middleware/permission');

/**
 * Routes pour la génération et téléchargement de templates Excel
 * Toutes les routes nécessitent une authentification
 */

// Middleware d'authentification pour toutes les routes
router.use(verifyToken);

/**
 * ==================== ROUTES DE TÉLÉCHARGEMENT DES TEMPLATES ====================
 */

/**
 * GET /api/templates/restaurant
 * Télécharger le template Excel pour menu restaurant
 */
router.get('/restaurant',
  checkPermission('manage_services'),
  TemplateGeneratorController.downloadRestaurantTemplate
);

/**
 * GET /api/templates/bar
 * Télécharger le template Excel pour carte bar
 */
router.get('/bar',
  checkPermission('manage_services'),
  TemplateGeneratorController.downloadBarTemplate
);

/**
 * GET /api/templates/cuisine-ingredients
 * Télécharger le template Excel pour ingrédients cuisine
 */
router.get('/cuisine-ingredients',
  checkPermission('manage_inventory'),
  TemplateGeneratorController.downloadCuisineIngredientTemplate
);

/**
 * GET /api/templates/boisson-inventory
 * Télécharger le template Excel pour inventaire boissons
 */
router.get('/boisson-inventory',
  checkPermission('manage_inventory'),
  TemplateGeneratorController.downloadBoissonInventoryTemplate
);

/**
 * ==================== ROUTES DE GESTION DES TEMPLATES ====================
 */

/**
 * GET /api/templates/list
 * Lister tous les templates disponibles avec leur statut
 */
router.get('/list',
  checkPermission('view_services'),
  TemplateGeneratorController.listTemplates
);

/**
 * POST /api/templates/generate-all
 * Générer tous les templates Excel (pour l'administration)
 */
router.post('/generate-all',
  checkPermission('manage_system'),
  TemplateGeneratorController.generateAllTemplates
);

/**
 * ==================== ROUTES PUBLIQUES (SANS PERMISSIONS) ====================
 */

/**
 * GET /api/templates/info
 * Informations sur les templates disponibles (sans téléchargement)
 */
router.get('/info', (req, res) => {
  res.json({
    success: true,
    data: {
      templates: [
        {
          name: 'Menu Restaurant',
          description: 'Template pour importer un menu de restaurant complet',
          columns: [
            'nom_plat', 'description', 'categorie', 'prix_vente', 
            'temps_preparation', 'allergenes', 'image_url', 'actif'
          ],
          examples: 'Salade César, Steak Frites, Tarte Tatin...'
        },
        {
          name: 'Carte Bar',
          description: 'Template pour importer une carte de bar avec boissons',
          columns: [
            'nom_boisson', 'description', 'categorie', 'prix_vente',
            'degre_alcool', 'volume_ml', 'allergenes', 'image_url', 'actif'
          ],
          examples: 'Mojito, Heineken, Château Margaux...'
        },
        {
          name: 'Ingrédients Cuisine',
          description: 'Template pour importer les ingrédients de cuisine',
          columns: [
            'nom_ingredient', 'description', 'categorie', 'unite_mesure',
            'prix_unitaire', 'fournisseur', 'allergenes', 'conservation',
            'duree_conservation_jours', 'stock_minimal', 'actif'
          ],
          examples: 'Tomates fraîches, Farine de blé, Bœuf entrecôte...'
        },
        {
          name: 'Inventaire Boissons',
          description: 'Template pour importer l\'inventaire des boissons',
          columns: [
            'nom_boisson', 'description', 'categorie', 'type_conditionnement',
            'volume_unitaire', 'prix_achat_unitaire', 'prix_vente_unitaire',
            'fournisseur', 'degre_alcool', 'code_barre', 'stock_minimal',
            'stock_maximal', 'emplacement_stockage', 'temperature_stockage', 'actif'
          ],
          examples: 'Heineken bouteille, Château Margaux, Rhum Havana Club...'
        }
      ],
      usage: {
        step1: 'Télécharger le template correspondant à votre besoin',
        step2: 'Remplir le fichier Excel avec vos données',
        step3: 'Uploader le fichier via l\'interface d\'import',
        step4: 'Vérifier les données importées et valider'
      }
    },
    message: 'Informations sur les templates récupérées avec succès'
  });
});

module.exports = router;
