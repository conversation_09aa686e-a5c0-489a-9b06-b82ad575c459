const express = require('express');
const POSController = require('../controllers/pos.controller');

const router = express.Router();

// Routes de consultation des points de vente
router.get('/', POSController.getAllPOS);
router.get('/service/:serviceId', POSController.getPOSByService);
router.get('/:id', POSController.getPOSById);

// Routes de gestion des points de vente (CRUD)
router.post('/', POSController.createPOS);
router.put('/:id', POSController.updatePOS);
router.delete('/:id', POSController.deletePOS);

// Route pour créer automatiquement un POS pour un service
router.post('/auto/:serviceId', POSController.createPOSForService);

// Routes pour la gestion des fonds
router.post('/transfer', POSController.transferFunds);
router.post('/:id/withdraw', POSController.withdrawFunds);
router.post('/:id/add-funds', POSController.addFunds);

module.exports = router;
