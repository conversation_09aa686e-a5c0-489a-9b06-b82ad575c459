const express = require('express');
const SessionController = require('../controllers/session.controller');

const router = express.Router();

// Routes de gestion des sessions de caisse
router.post('/ouvrir', SessionController.ouvrirSession);
router.put('/fermer/:id', SessionController.fermerSession);

// Routes de consultation des sessions
router.get('/', SessionController.getSessionsByComplexe);
router.get('/pos/:posId', SessionController.getSessionsByPOS);
router.get('/pos/:posId/active', SessionController.getActiveSession);
router.get('/:id', SessionController.getSessionById);

module.exports = router;
