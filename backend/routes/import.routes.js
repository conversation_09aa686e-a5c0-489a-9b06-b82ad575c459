const express = require('express');
const router = express.Router();
const ImportController = require('../controllers/import.controller');

// Import des middlewares spécialisés
const {
  autoInvalidateCache,
  heavyResponseCache
} = require('../middleware/cache.middleware');
const {
  checkImportPermissions,
  logInventaireAction
} = require('../middleware/inventaire.middleware');
const {
  importLimiter,
  heavyImportLimiter
} = require('../middleware/rateLimiting.middleware');

/**
 * ==================== ROUTES DE PROCESSUS D'IMPORT ====================
 */

/**
 * POST /api/imports/:importId/process
 * Traitement complet d'un import validé
 */
router.post('/:importId/process',
  heavyImportLimiter,
  checkImportPermissions,
  autoInvalidateCache(['ingredients', 'recipes', 'analytics']),
  logInventaireAction('PROCESS_IMPORT'),
  ImportController.processImport
);

/**
 * POST /api/imports/:importId/validate
 * Validation complète des données d'import
 */
router.post('/:importId/validate',
  importLimiter,
  checkImportPermissions,
  logInventaireAction('VALIDATE_IMPORT'),
  ImportController.validateImportData
);

/**
 * POST /api/imports/:importId/rollback
 * Annulation complète d'un import avec suppression des données créées
 */
router.post('/:importId/rollback',
  importLimiter,
  checkImportPermissions,
  autoInvalidateCache(['ingredients', 'recipes', 'analytics']),
  logInventaireAction('ROLLBACK_IMPORT'),
  ImportController.rollbackImport
);

/**
 * ==================== ROUTES DE RAPPORTS ET HISTORIQUE ====================
 */

/**
 * GET /api/imports/:importId/report
 * Génération du rapport détaillé d'import
 */
router.get('/:importId/report',
  importLimiter,
  checkImportPermissions,
  heavyResponseCache('analytics', (req) => `import:report:${req.params.importId}`, 1800),
  ImportController.generateImportReport
);

/**
 * GET /api/imports/history/:complexeId
 * Historique des imports par complexe avec filtres
 */
router.get('/history/:complexeId',
  importLimiter,
  checkImportPermissions,
  heavyResponseCache('analytics', (req) => `import:history:${req.params.complexeId}:${JSON.stringify(req.query)}`, 600),
  ImportController.getImportHistory
);

/**
 * GET /api/imports/statistics/:complexeId
 * Statistiques des imports par complexe
 */
router.get('/statistics/:complexeId',
  importLimiter,
  checkImportPermissions,
  heavyResponseCache('analytics', (req) => `import:stats:${req.params.complexeId}`, 900),
  ImportController.getImportStatistics
);

/**
 * ==================== ROUTES DE GESTION DES ERREURS ====================
 */

/**
 * GET /api/imports/:importId/errors
 * Récupération détaillée des erreurs d'import
 */
router.get('/:importId/errors', ImportController.getImportErrors);

/**
 * POST /api/imports/:importId/fix-errors
 * Correction automatique des erreurs simples
 */
router.post('/:importId/fix-errors', ImportController.fixImportErrors);

/**
 * ==================== ROUTES D'IMPORTS SPÉCIALISÉS ====================
 */

/**
 * POST /api/imports/menu-restaurant/:importId
 * Import spécialisé pour menu restaurant
 */
router.post('/menu-restaurant/:importId', ImportController.importMenuRestaurant);

/**
 * POST /api/imports/carte-bar/:importId
 * Import spécialisé pour carte bar
 */
router.post('/carte-bar/:importId', ImportController.importCarteBar);

/**
 * POST /api/imports/inventaire/:importId
 * Import spécialisé pour inventaire ingrédients
 */
router.post('/inventaire/:importId', ImportController.importInventaireIngredients);

/**
 * ==================== ROUTES UTILITAIRES ====================
 */

/**
 * GET /api/imports/types
 * Liste des types d'imports disponibles avec leurs spécifications
 */
router.get('/types', ImportController.getImportTypes);

/**
 * GET /api/imports/status-options
 * Liste des statuts d'imports possibles
 */
router.get('/status-options', ImportController.getImportStatusOptions);

module.exports = router;
