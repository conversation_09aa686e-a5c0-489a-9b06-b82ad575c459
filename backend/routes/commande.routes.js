const express = require('express');
const router = express.Router();
const CommandeController = require('../controllers/commande.controller');
const { verifyToken } = require('../middleware/auth');
const { checkPermission } = require('../middleware/permission');
const {
  validateRestaurantCommande,
  validateAddItemToCommande,
  validateUpdateItemQuantity
} = require('../middleware/restaurantValidation.middleware');
const {
  logCommandeOperation,
  logStockAlert,
  logValidationResult,
  auditLog
} = require('../middleware/restaurantLogging.middleware');

/**
 * Routes pour la gestion des commandes de restaurant/bar
 * Toutes les routes nécessitent une authentification
 */

// Middleware d'authentification pour toutes les routes
router.use(verifyToken);

/**
 * ==================== ROUTES CRUD DES COMMANDES ====================
 */

/**
 * POST /api/commandes
 * Créer une nouvelle commande avec validation automatique
 */
router.post('/',
  checkPermission('operate_restaurant_pos'),
  logCommandeOperation('CREATE_COMMANDE'),
  validateRestaurantCommande,
  logValidationResult,
  logStockAlert,
  auditLog('CREATE_COMMANDE'),
  CommandeController.createCommande
);

/**
 * GET /api/commandes/:id
 * Récupérer une commande par ID avec ses détails
 */
router.get('/:id', 
  checkPermission('view_orders'),
  CommandeController.getCommande
);

/**
 * PUT /api/commandes/:id
 * Mettre à jour une commande
 */
router.put('/:id', 
  checkPermission('operate_restaurant_pos'),
  CommandeController.updateCommande
);

/**
 * DELETE /api/commandes/:id
 * Annuler une commande avec restauration du stock
 */
router.delete('/:id', 
  checkPermission('operate_restaurant_pos'),
  CommandeController.deleteCommande
);

/**
 * ==================== ROUTES GESTION DES ITEMS ====================
 */

/**
 * POST /api/commandes/:id/items
 * Ajouter un item à une commande avec validation stock
 */
router.post('/:id/items',
  checkPermission('operate_restaurant_pos'),
  logCommandeOperation('ADD_ITEM'),
  validateAddItemToCommande,
  logValidationResult,
  CommandeController.addItemToCommande
);

/**
 * PUT /api/commandes/:id/items/:itemId
 * Mettre à jour la quantité d'un item avec validation
 */
router.put('/:id/items/:itemId',
  checkPermission('operate_restaurant_pos'),
  logCommandeOperation('UPDATE_ITEM'),
  validateUpdateItemQuantity,
  CommandeController.updateItemQuantity
);

/**
 * DELETE /api/commandes/:id/items/:itemId
 * Supprimer un item d'une commande
 */
router.delete('/:id/items/:itemId', 
  checkPermission('operate_restaurant_pos'),
  CommandeController.removeItemFromCommande
);

/**
 * ==================== ROUTES WORKFLOW ET STATUTS ====================
 */

/**
 * PUT /api/commandes/:id/status
 * Mettre à jour le statut d'une commande
 */
router.put('/:id/status', 
  checkPermission('operate_restaurant_pos'),
  CommandeController.updateCommandeStatus
);

/**
 * GET /api/commandes/service/:serviceId
 * Récupérer les commandes d'un service
 * Query params: statut (optionnel)
 */
router.get('/service/:serviceId', 
  checkPermission('view_orders'),
  CommandeController.getCommandesByService
);

/**
 * GET /api/commandes/table/:tableId
 * Récupérer les commandes d'une table
 */
router.get('/table/:tableId', 
  checkPermission('view_orders'),
  CommandeController.getCommandesByTable
);

/**
 * ==================== ROUTES PAIEMENT ET FINALISATION ====================
 */

/**
 * POST /api/commandes/:id/payment
 * Traiter le paiement d'une commande
 */
router.post('/:id/payment', 
  checkPermission('operate_restaurant_pos'),
  CommandeController.processPayment
);

/**
 * POST /api/commandes/:id/receipt
 * Générer et imprimer le ticket de caisse
 */
router.post('/:id/receipt', 
  checkPermission('operate_restaurant_pos'),
  CommandeController.printReceipt
);

module.exports = router;
