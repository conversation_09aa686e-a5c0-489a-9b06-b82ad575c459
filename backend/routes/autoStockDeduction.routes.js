const express = require('express');
const router = express.Router();
const AutoStockDeductionController = require('../controllers/autoStockDeduction.controller');
const { verifyToken } = require('../middleware/auth');

// Import des middlewares spécialisés
const { 
  checkInventairePermissions,
  logInventaireAction 
} = require('../middleware/inventaire.middleware');
const { 
  stockLimiter
} = require('../middleware/rateLimiting.middleware');

/**
 * POST /api/auto-stock-deduction/deduct
 * Déduire automatiquement le stock pour une transaction
 */
router.post('/deduct',
  verifyToken,
  stockLimiter,
  checkInventairePermissions(['manage_inventory', 'operate_pos']),
  logInventaireAction('AUTO_DEDUCT_STOCK'),
  AutoStockDeductionController.deductStockForTransaction
);

/**
 * POST /api/auto-stock-deduction/restore/:transactionId
 * Restaurer le stock pour une transaction annulée
 */
router.post('/restore/:transactionId',
  verifyToken,
  stockLimiter,
  checkInventairePermissions(['manage_inventory', 'operate_pos']),
  logInventaireAction('RESTORE_STOCK'),
  AutoStockDeductionController.restoreStockForCancelledTransaction
);

/**
 * GET /api/auto-stock-deduction/status/:transactionId
 * Vérifier le statut de déduction du stock pour une transaction
 */
router.get('/status/:transactionId',
  verifyToken,
  checkInventairePermissions(['view_inventory', 'operate_pos']),
  AutoStockDeductionController.checkTransactionStockStatus
);

module.exports = router; 