const express = require('express');
const router = express.Router();
const RoleController = require('../controllers/role.controller');
const { verifyToken } = require('../middleware/auth');
const { checkPermission } = require('../middleware/permission');

/**
 * Routes pour la gestion des rôles - Système simplifié
 * Phase 6 : Refactoring des routes
 */

// Routes protégées par l'authentification
router.use(verifyToken);

// Middleware pour vérifier les permissions (simplifié)
const checkAdminPermission = (req, res, next) => {
  if (['super_admin', 'admin_chaine', 'admin_complexe'].includes(req.user.role)) {
    return next();
  }
  return res.status(403).json({
    success: false,
    message: 'Seuls les administrateurs peuvent gérer les rôles'
  });
};

// Routes de base pour la gestion des rôles (avec permissions admin)
router.post('/', checkAdminPermission, RoleController.createRole);
router.get('/', checkAdminPermission, RoleController.getRoles);
router.get('/:id', checkAdminPermission, RoleController.getRole);
router.put('/:id', checkAdminPermission, RoleController.updateRole);
router.delete('/:id', checkAdminPermission, RoleController.deleteRole);

// Routes pour l'assignation de rôles (avec permissions admin)
router.post('/assign', checkAdminPermission, RoleController.assignRoleToEmployee);
router.get('/employee/:employeeId/permissions', checkAdminPermission, RoleController.getEmployeePermissions);

// Routes pour le système simplifié (nouvelles)
router.post('/predefined/create', checkAdminPermission, RoleController.createPredefinedRoles);
router.get('/predefined/type/:type', checkAdminPermission, RoleController.getPredefinedRoleForType);
router.post('/validate-permissions', checkAdminPermission, RoleController.validatePermissions);

module.exports = router;