const { Pool } = require('pg');
const dbConfig = require('./db.config');
const logger = require('./logger');

// Create a new pool using the configuration
const pool = new Pool(dbConfig);

// Test the connection
pool.on('connect', () => {
  logger.info('Connected to the database');
});

pool.on('error', (err) => {
  logger.error('Unexpected error on idle client', err);
  process.exit(-1);
});

// Export a function to execute queries
const query = async (text, params) => {
  const start = Date.now();
  try {
    const res = await pool.query(text, params);
    const duration = Date.now() - start;
    logger.debug('Executed query', { text, duration, rows: res.rowCount });
    return res;
  } catch (error) {
    logger.error('Error executing query', { text, error });
    throw error;
  }
};

// Export a function to get a client from the pool
const getClient = async () => {
  const client = await pool.connect();
  const { query: clientQuery, release: clientRelease } = client;

  // Set a timeout of 5 seconds, after which we will log this client's last query
  const timeout = setTimeout(() => {
    logger.error('A client has been checked out for more than 5 seconds!');
    logger.error(`The last executed query on this client was: ${client.lastQuery}`);
  }, 5000);

  // Monkey patch the query method to keep track of the last query executed
  client.query = (...args) => {
    client.lastQuery = args;
    return clientQuery.apply(client, args);
  };

  client.release = () => {
    clearTimeout(timeout);
    client.query = clientQuery;
    client.release = clientRelease;
    return clientRelease.apply(client);
  };

  return client;
};

module.exports = {
  query,
  getClient,
  pool,
};
