-- Migration pour ajouter le support des réservations anonymes
-- Phase 1 : Modifications de la Base de Données

-- =====================================================
-- 1.1 Modifications de la table Clients
-- =====================================================

-- Ajouter les colonnes pour supporter l anonymat
ALTER TABLE "Clients" ADD COLUMN "est_anonyme" boolean DEFAULT false;
ALTER TABLE "Clients" ADD COLUMN "pseudonyme" varchar(100);
ALTER TABLE "Clients" ADD COLUMN "code_acces_anonyme" varchar(50) UNIQUE;

-- Rendre certains champs optionnels pour les clients anonymes
-- Note: Ces modifications peuvent nécessiter une vérification des données existantes
ALTER TABLE "Clients" ALTER COLUMN "nom" DROP NOT NULL;
ALTER TABLE "Clients" ALTER COLUMN "prenom" DROP NOT NULL;
ALTER TABLE "Clients" ALTER COLUMN "telephone" DROP NOT NULL;

-- Ajouter des contraintes pour assurer la cohérence des données
ALTER TABLE "Clients" ADD CONSTRAINT "check_client_anonyme" 
CHECK (
  (est_anonyme = false AND nom IS NOT NULL AND prenom IS NOT NULL) OR
  (est_anonyme = true AND code_acces_anonyme IS NOT NULL)
);

-- Ajouter une contrainte pour s'assurer que les clients anonymes ont un pseudonyme ou un code
ALTER TABLE "Clients" ADD CONSTRAINT "check_client_anonyme_identifiant"
CHECK (
  (est_anonyme = false) OR 
  (est_anonyme = true AND (pseudonyme IS NOT NULL OR code_acces_anonyme IS NOT NULL))
);

-- Index pour optimiser les recherches
CREATE INDEX "idx_clients_code_acces_anonyme" ON "Clients" ("code_acces_anonyme");
CREATE INDEX "idx_clients_est_anonyme" ON "Clients" ("est_anonyme");
CREATE INDEX "idx_clients_pseudonyme" ON "Clients" ("pseudonyme") WHERE "est_anonyme" = true;

-- =====================================================
-- 1.2 Modifications de la table Reservations
-- =====================================================

-- Ajouter une colonne pour identifier les réservations anonymes
ALTER TABLE "Reservations" ADD COLUMN "est_anonyme" boolean DEFAULT false;

-- Ajouter une colonne pour stocker le code d acces direct (optionnel, pour performance)
ALTER TABLE "Reservations" ADD COLUMN "code_acces_direct" varchar(50);

-- Index pour optimiser les recherches
CREATE INDEX "idx_reservations_est_anonyme" ON "Reservations" ("est_anonyme");
CREATE INDEX "idx_reservations_code_acces_direct" ON "Reservations" ("code_acces_direct") WHERE "est_anonyme" = true;

-- Contrainte pour s assurer que les reservations anonymes ont un code d acces
ALTER TABLE "Reservations" ADD CONSTRAINT "check_reservation_anonyme_code"
CHECK (
  (est_anonyme = false) OR 
  (est_anonyme = true AND code_acces_direct IS NOT NULL)
);

-- =====================================================
-- 1.3 Table de logs pour les acces anonymes
-- =====================================================

CREATE TABLE "LogsAccesAnonymes" (
  "log_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "code_acces_anonyme" varchar(50) NOT NULL,
  "adresse_ip" varchar(45),
  "user_agent" text,
  "action" varchar(50) NOT NULL,
  "timestamp" timestamp DEFAULT (now()),
  "details" json,
  "success" boolean DEFAULT true,
  "error_message" text,
  "session_id" varchar(100),
  "referer" varchar(500)
);

-- Index pour optimiser les recherches et le monitoring
CREATE INDEX "idx_logs_acces_code" ON "LogsAccesAnonymes" ("code_acces_anonyme");
CREATE INDEX "idx_logs_acces_timestamp" ON "LogsAccesAnonymes" ("timestamp");
CREATE INDEX "idx_logs_acces_action" ON "LogsAccesAnonymes" ("action");
CREATE INDEX "idx_logs_acces_ip" ON "LogsAccesAnonymes" ("adresse_ip");
CREATE INDEX "idx_logs_acces_success" ON "LogsAccesAnonymes" ("success", "timestamp");

-- =====================================================
-- 1.4 Table de configuration pour les réservations anonymes
-- =====================================================

CREATE TABLE "ConfigurationReservationsAnonymes" (
  "config_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "complexe_id" integer NOT NULL,
  "actif" boolean DEFAULT true,
  "duree_validite_code_heures" integer DEFAULT 72, -- 72h par défaut
  "max_tentatives_acces" integer DEFAULT 5,
  "delai_blocage_minutes" integer DEFAULT 30,
  "autoriser_modification" boolean DEFAULT true,
  "autoriser_annulation" boolean DEFAULT true,
  "delai_annulation_heures" integer DEFAULT 24,
  "pseudonyme_obligatoire" boolean DEFAULT false,
  "longueur_code_acces" integer DEFAULT 12,
  "prefixe_code" varchar(10) DEFAULT 'ANON',
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp,
  FOREIGN KEY (complexe_id) REFERENCES "ComplexesHoteliers" (complexe_id)
);

-- Index pour la configuration
CREATE INDEX "idx_config_anonyme_complexe" ON "ConfigurationReservationsAnonymes" ("complexe_id");
CREATE UNIQUE INDEX "idx_config_anonyme_complexe_unique" ON "ConfigurationReservationsAnonymes" ("complexe_id");

-- =====================================================
-- 1.5 Table de securite pour les tentatives d acces
-- =====================================================

CREATE TABLE "SecuriteAccesAnonymes" (
  "securite_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "adresse_ip" varchar(45) NOT NULL,
  "code_acces_anonyme" varchar(50),
  "tentatives_echec" integer DEFAULT 0,
  "derniere_tentative" timestamp DEFAULT (now()),
  "bloque_jusqu" timestamp,
  "raison_blocage" varchar(100),
  "created_at" timestamp DEFAULT (now()),
  "updated_at" timestamp
);

-- Index pour la sécurité
CREATE INDEX "idx_securite_ip" ON "SecuriteAccesAnonymes" ("adresse_ip");
CREATE INDEX "idx_securite_code" ON "SecuriteAccesAnonymes" ("code_acces_anonyme");
CREATE INDEX "idx_securite_bloque" ON "SecuriteAccesAnonymes" ("bloque_jusqu") WHERE "bloque_jusqu" IS NOT NULL;

-- =====================================================
-- 1.6 Vues pour faciliter les requêtes
-- =====================================================

-- Vue pour les réservations anonymes avec détails client
CREATE VIEW "VueReservationsAnonymes" AS
SELECT 
  r.reservation_id,
  r.numero_reservation,
  r.code_acces_direct,
  r.date_arrivee,
  r.date_depart,
  r.heure_debut,
  r.heure_fin,
  r.statut,
  r.montant_total,
  r.commentaires,
  r.created_at,
  c.pseudonyme,
  c.code_acces_anonyme,
  co.nom as complexe_nom
FROM "Reservations" r
JOIN "Clients" c ON r.client_id = c.client_id
JOIN "ComplexesHoteliers" co ON r.complexe_id = co.complexe_id
WHERE r.est_anonyme = true;

-- Vue pour les statistiques des acces anonymes
CREATE VIEW "VueStatistiquesAccesAnonymes" AS
SELECT 
  DATE(timestamp) as date_acces,
  action,
  COUNT(*) as nombre_acces,
  COUNT(DISTINCT code_acces_anonyme) as codes_uniques,
  COUNT(DISTINCT adresse_ip) as ips_uniques,
  SUM(CASE WHEN success = true THEN 1 ELSE 0 END) as acces_reussis,
  SUM(CASE WHEN success = false THEN 1 ELSE 0 END) as acces_echecs
FROM "LogsAccesAnonymes"
GROUP BY DATE(timestamp), action
ORDER BY date_acces DESC, action;

-- =====================================================
-- 1.7 Fonctions utilitaires
-- =====================================================

-- Fonction pour generer un code d acces unique
CREATE OR REPLACE FUNCTION generer_code_acces_anonyme(prefixe varchar DEFAULT 'ANON', longueur integer DEFAULT 12)
RETURNS varchar AS $$
DECLARE
  code varchar;
  existe boolean;
BEGIN
  LOOP
    -- Générer un code aléatoire
    code := prefixe || '-' || upper(substring(md5(random()::text) from 1 for longueur));
    
    -- Vérifier s'il existe déjà
    SELECT EXISTS(SELECT 1 FROM "Clients" WHERE code_acces_anonyme = code) INTO existe;
    
    -- Si le code n existe pas, on peut l utiliser
    IF NOT existe THEN
      EXIT;
    END IF;
  END LOOP;
  
  RETURN code;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour nettoyer les logs anciens
CREATE OR REPLACE FUNCTION nettoyer_logs_acces_anonymes(jours_retention integer DEFAULT 90)
RETURNS integer AS $$
DECLARE
  lignes_supprimees integer;
BEGIN
  DELETE FROM "LogsAccesAnonymes" 
  WHERE timestamp < (CURRENT_TIMESTAMP - INTERVAL '1 day' * jours_retention);
  
  GET DIAGNOSTICS lignes_supprimees = ROW_COUNT;
  RETURN lignes_supprimees;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 1.8 Triggers pour l audit et la securite
-- =====================================================

-- Trigger pour logger automatiquement les modifications de réservations anonymes
CREATE OR REPLACE FUNCTION trigger_audit_reservation_anonyme()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.est_anonyme = true THEN
    INSERT INTO "LogsAccesAnonymes" (
      code_acces_anonyme,
      action,
      details
    ) VALUES (
      COALESCE(NEW.code_acces_direct, 'UNKNOWN'),
      CASE 
        WHEN TG_OP = 'INSERT' THEN 'CREATION_RESERVATION'
        WHEN TG_OP = 'UPDATE' THEN 'MODIFICATION_RESERVATION'
        WHEN TG_OP = 'DELETE' THEN 'SUPPRESSION_RESERVATION'
      END,
      json_build_object(
        'reservation_id', COALESCE(NEW.reservation_id, OLD.reservation_id),
        'numero_reservation', COALESCE(NEW.numero_reservation, OLD.numero_reservation),
        'ancien_statut', CASE WHEN TG_OP = 'UPDATE' THEN OLD.statut ELSE NULL END,
        'nouveau_statut', CASE WHEN TG_OP != 'DELETE' THEN NEW.statut ELSE NULL END
      )
    );
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Créer le trigger
CREATE TRIGGER trigger_audit_reservations_anonymes
  AFTER INSERT OR UPDATE OR DELETE ON "Reservations"
  FOR EACH ROW
  EXECUTE FUNCTION trigger_audit_reservation_anonyme();

-- =====================================================
-- 1.9 Données de configuration par défaut
-- =====================================================

-- Insérer la configuration par défaut pour tous les complexes existants
INSERT INTO "ConfigurationReservationsAnonymes" (complexe_id)
SELECT complexe_id FROM "ComplexesHoteliers"
ON CONFLICT (complexe_id) DO NOTHING;

-- =====================================================
-- Commentaires et documentation
-- =====================================================

COMMENT ON TABLE "LogsAccesAnonymes" IS 'Table de logs pour tracer tous les acces aux reservations anonymes';
COMMENT ON TABLE "ConfigurationReservationsAnonymes" IS 'Configuration des parametres pour les reservations anonymes par complexe';
COMMENT ON TABLE "SecuriteAccesAnonymes" IS 'Table de securite pour gerer les tentatives d''acces et blocages';

COMMENT ON COLUMN "Clients"."est_anonyme" IS 'Indique si le client est anonyme';
COMMENT ON COLUMN "Clients"."pseudonyme" IS 'Pseudonyme choisi par le client anonyme';
COMMENT ON COLUMN "Clients"."code_acces_anonyme" IS 'Code d''acces unique pour les clients anonymes';

COMMENT ON COLUMN "Reservations"."est_anonyme" IS 'Indique si la reservation est anonyme';
COMMENT ON COLUMN "Reservations"."code_acces_direct" IS 'Code d''acces direct pour la reservation (performance)';
