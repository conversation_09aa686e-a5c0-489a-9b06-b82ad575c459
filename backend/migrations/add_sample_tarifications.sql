-- Migration pour ajouter des exemples de tarifications aux services existants
-- À exécuter après avoir créé des services dans la base de données

-- Mise à jour des tarifications pour les restaurants
UPDATE "ServicesComplexe" 
SET tarification = '{
  "menus": {
    "Menu du jour": 25.00,
    "Menu enfant": 15.00,
    "Menu dégustation": 45.00,
    "Menu végétarien": 22.00,
    "Menu sans gluten": 28.00
  },
  "boissons": {
    "Eau plate": 3.00,
    "Eau gazeuse": 3.50,
    "Soda": 4.00,
    "Jus de fruits": 5.00,
    "Café": 2.50,
    "Thé": 2.00
  },
  "service_table": 2.00,
  "couvert_par_personne": 1.50
}'::json
WHERE type_service = 'Restaurant' AND tarification IS NULL;

-- Mise à jour des tarifications pour les bars
UPDATE "ServicesComplexe" 
SET tarification = '{
  "alcools": {
    "Bière pression": 5.00,
    "Bière bouteille": 6.00,
    "Vin rouge (verre)": 7.00,
    "Vin blanc (verre)": 7.00,
    "Champagne (verre)": 12.00,
    "Whisky": 8.00,
    "Vodka": 7.00,
    "Rhum": 7.50,
    "Gin": 7.50
  },
  "soft_drinks": {
    "Coca-Cola": 4.00,
    "Sprite": 4.00,
    "Jus d''orange": 5.00,
    "Jus de pomme": 5.00,
    "Eau minérale": 3.00,
    "Café": 2.50
  },
  "cocktails": {
    "Mojito": 12.00,
    "Piña Colada": 14.00,
    "Cosmopolitan": 13.00,
    "Margarita": 11.00,
    "Bloody Mary": 10.00,
    "Caipirinha": 9.00
  },
  "happy_hour": {
    "reduction_pourcentage": 20,
    "heures": ["17:00-19:00", "22:00-24:00"]
  }
}'::json
WHERE type_service = 'Bar' AND tarification IS NULL;

-- Mise à jour des tarifications pour les piscines
UPDATE "ServicesComplexe" 
SET tarification = '{
  "prix_par_personne": 10.00,
  "prix_par_heure": 5.00,
  "prix_forfaitaire": 25.00,
  "tarifs_age": {
    "Enfant (0-12 ans)": 5.00,
    "Adolescent (13-17 ans)": 8.00,
    "Adulte (18-64 ans)": 10.00,
    "Senior (65+ ans)": 7.00,
    "Accompagnateur gratuit": 0.00
  },
  "tarifs_duree": {
    "1 heure": 10.00,
    "2 heures": 18.00,
    "3 heures": 25.00,
    "Demi-journée (4h)": 30.00,
    "Journée complète (8h)": 40.00
  },
  "services_additionnels": {
    "Location serviette": 3.00,
    "Location parasol": 5.00,
    "Casier": 2.00,
    "Cours de natation": 25.00
  }
}'::json
WHERE type_service = 'Piscine' AND tarification IS NULL;

-- Ajouter des commentaires pour documenter les tarifications
COMMENT ON COLUMN "ServicesComplexe"."tarification" IS 'Tarification JSON du service. Structure varie selon le type_service: Restaurant (menus, boissons, service_table), Bar (alcools, soft_drinks, cocktails, happy_hour), Piscine (prix_par_personne, prix_par_heure, tarifs_age, tarifs_duree)';

-- Créer un index pour améliorer les performances des requêtes sur la tarification
CREATE INDEX IF NOT EXISTS idx_services_tarification_gin ON "ServicesComplexe" USING gin (tarification);

-- Créer un index pour les requêtes par type de service
CREATE INDEX IF NOT EXISTS idx_services_type_service ON "ServicesComplexe" (type_service);

-- Vérification des données après migration
-- Cette requête peut être utilisée pour vérifier que les tarifications ont été correctement ajoutées
/*
SELECT 
  service_id,
  nom,
  type_service,
  CASE 
    WHEN tarification IS NOT NULL THEN 'Tarification définie'
    ELSE 'Pas de tarification'
  END as statut_tarification,
  jsonb_pretty(tarification::jsonb) as tarification_formatee
FROM "ServicesComplexe"
ORDER BY type_service, nom;
*/
