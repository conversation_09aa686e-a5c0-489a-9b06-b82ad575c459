# Migrations pour les Réservations Anonymes

## Vue d'ensemble

Ce dossier contient les scripts de migration pour implémenter le système de réservations anonymes dans la base de données.

## Fichiers de migration

### 1. `001_add_anonymous_reservations.sql`
Script principal de migration qui ajoute toutes les fonctionnalités nécessaires pour les réservations anonymes.

**Modifications incluses :**
- Ajout de colonnes à la table `Clients` pour supporter l'anonymat
- Ajout de colonnes à la table `Reservations` pour identifier les réservations anonymes
- Création de la table `LogsAccesAnonymes` pour tracer les accès
- Création de la table `ConfigurationReservationsAnonymes` pour la configuration
- Création de la table `SecuriteAccesAnonymes` pour la sécurité
- Ajout de vues, fonctions et triggers utilitaires

### 2. `001_rollback_anonymous_reservations.sql`
Script de rollback pour annuler toutes les modifications en cas de problème.

## Instructions d'exécution

### Avant la migration

1. **Sauvegarde de la base de données**
   ```bash
   pg_dump -h localhost -U username -d database_name > backup_before_anonymous.sql
   ```

2. **Vérification des données existantes**
   ```sql
   -- Vérifier s'il y a des clients avec des champs NULL
   SELECT COUNT(*) FROM "Clients" WHERE nom IS NULL OR prenom IS NULL OR telephone IS NULL;
   ```

### Exécution de la migration

1. **Exécuter le script de migration**
   ```bash
   psql -h localhost -U username -d database_name -f 001_add_anonymous_reservations.sql
   ```

2. **Vérifier que la migration s'est bien passée**
   ```sql
   -- Vérifier les nouvelles colonnes
   \d "Clients"
   \d "Reservations"
   
   -- Vérifier les nouvelles tables
   \dt *Anonyme*
   
   -- Vérifier les index
   \di idx_clients_*
   \di idx_reservations_*
   ```

### En cas de problème

1. **Exécuter le rollback**
   ```bash
   psql -h localhost -U username -d database_name -f 001_rollback_anonymous_reservations.sql
   ```

2. **Restaurer la sauvegarde si nécessaire**
   ```bash
   psql -h localhost -U username -d database_name < backup_before_anonymous.sql
   ```

## Vérifications post-migration

### 1. Vérifier les contraintes
```sql
-- Vérifier que les contraintes sont bien créées
SELECT conname, contype, pg_get_constraintdef(oid) 
FROM pg_constraint 
WHERE conrelid = 'Clients'::regclass 
AND conname LIKE '%anonyme%';
```

### 2. Tester la génération de codes
```sql
-- Tester la fonction de génération de codes
SELECT generer_code_acces_anonyme();
SELECT generer_code_acces_anonyme('TEST', 8);
```

### 3. Vérifier les index
```sql
-- Vérifier que les index sont créés et utilisés
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM "Clients" WHERE code_acces_anonyme = 'TEST-12345678';
```

### 4. Tester les vues
```sql
-- Tester les vues créées
SELECT * FROM "VueReservationsAnonymes" LIMIT 5;
SELECT * FROM "VueStatistiquesAccesAnonymes" LIMIT 5;
```

## Configuration post-migration

### 1. Configurer les paramètres par complexe
```sql
-- Modifier la configuration pour un complexe spécifique
UPDATE "ConfigurationReservationsAnonymes" 
SET 
  duree_validite_code_heures = 48,
  max_tentatives_acces = 3,
  autoriser_modification = false
WHERE complexe_id = 1;
```

### 2. Programmer le nettoyage automatique
```sql
-- Créer une tâche cron pour nettoyer les logs anciens
-- À ajouter dans le crontab du serveur :
-- 0 2 * * * psql -d database_name -c "SELECT nettoyer_logs_acces_anonymes(90);"
```

## Monitoring et maintenance

### 1. Surveiller les performances
```sql
-- Vérifier l'utilisation des index
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE indexname LIKE '%anonyme%'
ORDER BY idx_scan DESC;
```

### 2. Surveiller la sécurité
```sql
-- Vérifier les tentatives d'accès suspectes
SELECT adresse_ip, COUNT(*) as tentatives, MAX(derniere_tentative)
FROM "SecuriteAccesAnonymes"
WHERE tentatives_echec > 3
GROUP BY adresse_ip
ORDER BY tentatives DESC;
```

### 3. Statistiques d'utilisation
```sql
-- Statistiques générales
SELECT 
  COUNT(*) as total_reservations_anonymes,
  COUNT(DISTINCT code_acces_direct) as codes_uniques,
  MIN(created_at) as premiere_reservation,
  MAX(created_at) as derniere_reservation
FROM "Reservations" 
WHERE est_anonyme = true;
```

## Dépannage

### Problèmes courants

1. **Erreur de contrainte sur les clients existants**
   ```sql
   -- Identifier les clients problématiques
   SELECT client_id, nom, prenom, telephone 
   FROM "Clients" 
   WHERE nom IS NULL OR prenom IS NULL;
   
   -- Corriger les données avant la migration
   UPDATE "Clients" SET nom = 'Inconnu' WHERE nom IS NULL;
   UPDATE "Clients" SET prenom = 'Inconnu' WHERE prenom IS NULL;
   ```

2. **Performance lente sur les recherches**
   ```sql
   -- Analyser les statistiques des tables
   ANALYZE "Clients";
   ANALYZE "Reservations";
   ANALYZE "LogsAccesAnonymes";
   ```

3. **Espace disque insuffisant**
   ```sql
   -- Vérifier l'espace utilisé par les nouvelles tables
   SELECT 
     schemaname,
     tablename,
     pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
   FROM pg_tables 
   WHERE tablename LIKE '%Anonyme%';
   ```

## Support

Pour toute question ou problème avec cette migration, consultez :
- La documentation technique du projet
- Les logs de l'application
- L'équipe de développement

## Changelog

- **v1.0** : Migration initiale pour les réservations anonymes
- Ajout de toutes les tables, colonnes et fonctionnalités de base
- Implémentation de la sécurité et du logging
- Création des vues et fonctions utilitaires
