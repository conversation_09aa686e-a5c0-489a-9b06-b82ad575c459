-- Script de rollback pour les réservations anonymes
-- À utiliser en cas de problème avec la migration

-- =====================================================
-- Rollback Phase 1 : Suppression des modifications
-- =====================================================

-- Supprimer les triggers
DROP TRIGGER IF EXISTS trigger_audit_reservations_anonymes ON "Reservations";
DROP FUNCTION IF EXISTS trigger_audit_reservation_anonyme();

-- Supprimer les fonctions utilitaires
DROP FUNCTION IF EXISTS generer_code_acces_anonyme(varchar, integer);
DROP FUNCTION IF EXISTS nettoyer_logs_acces_anonymes(integer);

-- Supprimer les vues
DROP VIEW IF EXISTS "VueStatistiquesAccesAnonymes";
DROP VIEW IF EXISTS "VueReservationsAnonymes";

-- Supprimer les tables créées
DROP TABLE IF EXISTS "SecuriteAccesAnonymes";
DROP TABLE IF EXISTS "ConfigurationReservationsAnonymes";
DROP TABLE IF EXISTS "LogsAccesAnonymes";

-- Supprimer les colonnes ajoutées à la table Reservations
ALTER TABLE "Reservations" DROP CONSTRAINT IF EXISTS "check_reservation_anonyme_code";
DROP INDEX IF EXISTS "idx_reservations_code_acces_direct";
DROP INDEX IF EXISTS "idx_reservations_est_anonyme";
ALTER TABLE "Reservations" DROP COLUMN IF EXISTS "code_acces_direct";
ALTER TABLE "Reservations" DROP COLUMN IF EXISTS "est_anonyme";

-- Supprimer les modifications de la table Clients
ALTER TABLE "Clients" DROP CONSTRAINT IF EXISTS "check_client_anonyme_identifiant";
ALTER TABLE "Clients" DROP CONSTRAINT IF EXISTS "check_client_anonyme";
DROP INDEX IF EXISTS "idx_clients_pseudonyme";
DROP INDEX IF EXISTS "idx_clients_est_anonyme";
DROP INDEX IF EXISTS "idx_clients_code_acces_anonyme";
ALTER TABLE "Clients" DROP COLUMN IF EXISTS "code_acces_anonyme";
ALTER TABLE "Clients" DROP COLUMN IF EXISTS "pseudonyme";
ALTER TABLE "Clients" DROP COLUMN IF EXISTS "est_anonyme";

-- Remettre les contraintes NOT NULL sur les colonnes Clients
-- ATTENTION: Cette opération peut échouer s'il y a des données NULL
-- Vérifier d'abord les données avant d'exécuter ces commandes

-- ALTER TABLE "Clients" ALTER COLUMN "telephone" SET NOT NULL;
-- ALTER TABLE "Clients" ALTER COLUMN "prenom" SET NOT NULL;
-- ALTER TABLE "Clients" ALTER COLUMN "nom" SET NOT NULL;

-- Message de fin
SELECT 'Rollback des réservations anonymes terminé. Vérifiez les contraintes NOT NULL manuellement.' as message;
