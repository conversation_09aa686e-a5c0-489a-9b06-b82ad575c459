-- =====================================================
-- PHASE 1 : Modification du Schéma de Base de Données
-- Plan de Simplification du Système de Permissions
-- =====================================================

-- 1.1 Ajout du champ type_employe dans la table Employes
-- (Si la table existe déjà et n'a pas encore le champ)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'Employes' 
        AND column_name = 'type_employe'
    ) THEN
        ALTER TABLE "Employes" ADD COLUMN "type_employe" varchar;
        
        -- Ajouter le commentaire pour documenter les valeurs possibles
        COMMENT ON COLUMN "Employes"."type_employe" IS 'reception, gerant_piscine, serveuse, gerant_services, cuisine';
    END IF;
END $$;

-- 1.2 Mise à jour du commentaire pour services_autorises
-- (Le champ existe déjà mais on change son utilisation)
COMMENT ON COLUMN "Employes"."services_autorises" IS 'Array JSON des services autorisés: ["bar", "restaurant", "piscine"]';

-- 1.3 Création des rôles prédéfinis par type d'employé
-- Ces rôles seront créés pour chaque complexe existant

-- Fonction pour créer les rôles prédéfinis pour un complexe donné
CREATE OR REPLACE FUNCTION create_predefined_roles_for_complex(complexe_id_param INTEGER)
RETURNS VOID AS $$
DECLARE
    role_reception_id INTEGER;
    role_gerant_piscine_id INTEGER;
    role_serveuse_id INTEGER;
    role_gerant_services_id INTEGER;
    role_cuisine_id INTEGER;
BEGIN
    -- Rôle Réception
    INSERT INTO "RolesComplexe" (
        complexe_id, nom, description, permissions, created_at
    ) VALUES (
        complexe_id_param,
        'Employé Réception',
        'Rôle prédéfini pour les employés de réception',
        '["reception_operations"]'::json,
        CURRENT_TIMESTAMP
    ) ON CONFLICT DO NOTHING
    RETURNING role_id INTO role_reception_id;

    -- Rôle Gérant Piscine
    INSERT INTO "RolesComplexe" (
        complexe_id, nom, description, permissions, created_at
    ) VALUES (
        complexe_id_param,
        'Gérant Piscine',
        'Rôle prédéfini pour les gérants de piscine',
        '["piscine_operations"]'::json,
        CURRENT_TIMESTAMP
    ) ON CONFLICT DO NOTHING
    RETURNING role_id INTO role_gerant_piscine_id;

    -- Rôle Serveuse
    INSERT INTO "RolesComplexe" (
        complexe_id, nom, description, permissions, created_at
    ) VALUES (
        complexe_id_param,
        'Serveuse',
        'Rôle prédéfini pour les serveuses',
        '["service_operations"]'::json,
        CURRENT_TIMESTAMP
    ) ON CONFLICT DO NOTHING
    RETURNING role_id INTO role_serveuse_id;

    -- Rôle Gérant Services
    INSERT INTO "RolesComplexe" (
        complexe_id, nom, description, permissions, created_at
    ) VALUES (
        complexe_id_param,
        'Gérant Services',
        'Rôle prédéfini pour les gérants de services (bar/restaurant)',
        '["service_operations", "management_operations"]'::json,
        CURRENT_TIMESTAMP
    ) ON CONFLICT DO NOTHING
    RETURNING role_id INTO role_gerant_services_id;

    -- Rôle Cuisine
    INSERT INTO "RolesComplexe" (
        complexe_id, nom, description, permissions, created_at
    ) VALUES (
        complexe_id_param,
        'Employé Cuisine',
        'Rôle prédéfini pour les employés de cuisine',
        '["kitchen_operations"]'::json,
        CURRENT_TIMESTAMP
    ) ON CONFLICT DO NOTHING
    RETURNING role_id INTO role_cuisine_id;

    RAISE NOTICE 'Rôles prédéfinis créés pour le complexe %', complexe_id_param;
END;
$$ LANGUAGE plpgsql;

-- Créer les rôles prédéfinis pour tous les complexes existants
DO $$
DECLARE
    complexe_record RECORD;
BEGIN
    FOR complexe_record IN 
        SELECT complexe_id FROM "ComplexesHoteliers" WHERE actif = true
    LOOP
        PERFORM create_predefined_roles_for_complex(complexe_record.complexe_id);
    END LOOP;
    
    RAISE NOTICE 'Rôles prédéfinis créés pour tous les complexes actifs';
END $$;

-- 1.4 Contrainte pour valider les types d'employés
ALTER TABLE "Employes" 
ADD CONSTRAINT check_type_employe 
CHECK (type_employe IS NULL OR type_employe IN ('reception', 'gerant_piscine', 'serveuse', 'gerant_services', 'cuisine'));

-- 1.5 Index pour améliorer les performances des requêtes sur type_employe
CREATE INDEX IF NOT EXISTS idx_employes_type_employe ON "Employes" (type_employe);
CREATE INDEX IF NOT EXISTS idx_employes_complexe_type ON "Employes" (complexe_id, type_employe);

-- 1.6 Fonction utilitaire pour obtenir les services autorisés par défaut selon le type
CREATE OR REPLACE FUNCTION get_default_services_for_type(type_employe_param VARCHAR)
RETURNS JSON AS $$
BEGIN
    CASE type_employe_param
        WHEN 'reception' THEN
            RETURN '["hebergement"]'::json;
        WHEN 'gerant_piscine' THEN
            RETURN '["piscine"]'::json;
        WHEN 'serveuse' THEN
            RETURN '["bar", "restaurant"]'::json;
        WHEN 'gerant_services' THEN
            RETURN '["bar", "restaurant"]'::json;
        WHEN 'cuisine' THEN
            RETURN '["restaurant"]'::json;
        ELSE
            RETURN '[]'::json;
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- 1.7 Trigger pour assigner automatiquement les services par défaut
CREATE OR REPLACE FUNCTION assign_default_services_on_type_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Si le type_employe est défini et services_autorises est null ou vide
    IF NEW.type_employe IS NOT NULL AND (
        NEW.services_autorises IS NULL OR
        NEW.services_autorises::text = '[]' OR
        NEW.services_autorises::text = 'null'
    ) THEN
        NEW.services_autorises := get_default_services_for_type(NEW.type_employe);
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Créer le trigger
DROP TRIGGER IF EXISTS trigger_assign_default_services ON "Employes";
CREATE TRIGGER trigger_assign_default_services
    BEFORE INSERT OR UPDATE OF type_employe ON "Employes"
    FOR EACH ROW
    EXECUTE FUNCTION assign_default_services_on_type_change();

-- =====================================================
-- Fin de la Phase 1
-- =====================================================

-- Vérification des modifications
SELECT 
    'Phase 1 terminée' as status,
    COUNT(*) as total_complexes,
    (SELECT COUNT(*) FROM "RolesComplexe" WHERE nom LIKE '%Employé%' OR nom LIKE '%Gérant%' OR nom LIKE '%Serveuse%') as roles_predefined_created
FROM "ComplexesHoteliers" 
WHERE actif = true;
