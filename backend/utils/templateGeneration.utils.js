const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');
const logger = require('../logger');

/**
 * Styles Excel pour les templates
 */
const EXCEL_STYLES = {
  header: {
    font: { bold: true, color: { rgb: "FFFFFF" }, size: 12 },
    fill: { fgColor: { rgb: "366092" } },
    alignment: { horizontal: "center", vertical: "center" },
    border: {
      top: { style: "thin", color: { rgb: "000000" } },
      bottom: { style: "thin", color: { rgb: "000000" } },
      left: { style: "thin", color: { rgb: "000000" } },
      right: { style: "thin", color: { rgb: "000000" } }
    }
  },
  description: {
    font: { italic: true, color: { rgb: "666666" }, size: 10 },
    fill: { fgColor: { rgb: "F0F0F0" } },
    alignment: { horizontal: "left", vertical: "center", wrapText: true },
    border: {
      top: { style: "thin", color: { rgb: "CCCCCC" } },
      bottom: { style: "thin", color: { rgb: "CCCCCC" } },
      left: { style: "thin", color: { rgb: "CCCCCC" } },
      right: { style: "thin", color: { rgb: "CCCCCC" } }
    }
  },
  data: {
    font: { size: 11 },
    alignment: { horizontal: "left", vertical: "center" },
    border: {
      top: { style: "thin", color: { rgb: "DDDDDD" } },
      bottom: { style: "thin", color: { rgb: "DDDDDD" } },
      left: { style: "thin", color: { rgb: "DDDDDD" } },
      right: { style: "thin", color: { rgb: "DDDDDD" } }
    }
  },
  title: {
    font: { bold: true, size: 16, color: { rgb: "2F4F4F" } },
    alignment: { horizontal: "center", vertical: "center" }
  },
  instruction: {
    font: { size: 11, color: { rgb: "333333" } },
    alignment: { horizontal: "left", vertical: "top", wrapText: true }
  }
};

/**
 * Configuration des colonnes par type d'import
 */
const COLUMN_CONFIGS = {
  MENU_RESTAURANT: {
    columns: [
      { key: 'nom_produit', header: 'Nom du produit', description: 'Nom du plat ou menu (obligatoire)', width: 25, required: true },
      { key: 'categorie', header: 'Catégorie', description: 'Catégorie (Entrées, Plats, Desserts...)', width: 15, required: false },
      { key: 'prix_vente', header: 'Prix de vente', description: 'Prix de vente en euros (obligatoire)', width: 12, required: true },
      { key: 'description', header: 'Description', description: 'Description du produit', width: 30, required: false },
      { key: 'ingredients', header: 'Ingrédients', description: 'Liste des ingrédients séparés par des virgules', width: 25, required: false },
      { key: 'quantites', header: 'Quantités', description: 'Quantités correspondantes séparées par des virgules', width: 15, required: false },
      { key: 'unites', header: 'Unités', description: 'Unités de mesure séparées par des virgules', width: 15, required: false }
    ],
    examples: [
      {
        nom_produit: 'Salade César',
        categorie: 'Entrées',
        prix_vente: 12.50,
        description: 'Salade avec croûtons et parmesan',
        ingredients: 'Salade,Croûtons,Parmesan',
        quantites: '100,20,30',
        unites: 'g,g,g'
      },
      {
        nom_produit: 'Steak frites',
        categorie: 'Plats',
        prix_vente: 18.90,
        description: 'Steak de bœuf avec frites maison',
        ingredients: 'Steak de bœuf,Pommes de terre,Huile',
        quantites: '200,300,50',
        unites: 'g,g,mL'
      }
    ]
  },

  CARTE_BAR: {
    columns: [
      { key: 'nom_boisson', header: 'Nom de la boisson', description: 'Nom de la boisson (obligatoire)', width: 25, required: true },
      { key: 'categorie', header: 'Catégorie', description: 'Catégorie (Cocktails, Bières, Vins...)', width: 15, required: false },
      { key: 'prix_vente', header: 'Prix de vente', description: 'Prix de vente en euros (obligatoire)', width: 12, required: true },
      { key: 'degre_alcool', header: 'Degré d\'alcool', description: 'Degré d\'alcool (0-100)', width: 12, required: false },
      { key: 'description', header: 'Description', description: 'Description de la boisson', width: 30, required: false },
      { key: 'ingredients', header: 'Ingrédients', description: 'Ingrédients pour cocktails', width: 25, required: false },
      { key: 'quantites', header: 'Quantités', description: 'Quantités en mL ou cl', width: 15, required: false },
      { key: 'unites', header: 'Unités', description: 'Unités de mesure', width: 15, required: false }
    ],
    examples: [
      {
        nom_boisson: 'Mojito',
        categorie: 'Cocktails',
        prix_vente: 8.50,
        degre_alcool: 12,
        description: 'Cocktail à base de rhum et menthe',
        ingredients: 'Rhum blanc,Menthe,Citron vert,Sucre',
        quantites: '50,10,20,10',
        unites: 'mL,g,mL,g'
      },
      {
        nom_boisson: 'Bière blonde',
        categorie: 'Bières',
        prix_vente: 4.50,
        degre_alcool: 5.2,
        description: 'Bière blonde artisanale',
        ingredients: '',
        quantites: '',
        unites: ''
      }
    ]
  },

  INVENTAIRE_INGREDIENTS: {
    columns: [
      { key: 'nom_ingredient', header: 'Nom de l\'ingrédient', description: 'Nom de l\'ingrédient (obligatoire)', width: 25, required: true },
      { key: 'categorie', header: 'Catégorie', description: 'Catégorie (Légumes, Viandes, Boissons...)', width: 15, required: false },
      { key: 'unite_mesure', header: 'Unité de mesure', description: 'Unité (kg, L, unité, pièce...)', width: 12, required: true },
      { key: 'prix_unitaire', header: 'Prix unitaire', description: 'Prix unitaire en euros (obligatoire)', width: 12, required: true },
      { key: 'stock_actuel', header: 'Stock actuel', description: 'Stock actuel en unités', width: 12, required: false },
      { key: 'stock_minimal', header: 'Stock minimal', description: 'Stock minimal d\'alerte', width: 12, required: false },
      { key: 'fournisseur', header: 'Fournisseur', description: 'Nom du fournisseur', width: 20, required: false },
      { key: 'conservation', header: 'Conservation', description: 'Type de conservation (Frais, Congelé, Sec, Ambiant)', width: 15, required: false },
      { key: 'code_barre', header: 'Code-barres', description: 'Code-barres du produit', width: 15, required: false }
    ],
    examples: [
      {
        nom_ingredient: 'Tomates',
        categorie: 'Légumes',
        unite_mesure: 'kg',
        prix_unitaire: 3.50,
        stock_actuel: 25,
        stock_minimal: 5,
        fournisseur: 'Maraîcher Local',
        conservation: 'Frais',
        code_barre: '1234567890123'
      },
      {
        nom_ingredient: 'Farine',
        categorie: 'Céréales',
        unite_mesure: 'kg',
        prix_unitaire: 1.20,
        stock_actuel: 50,
        stock_minimal: 10,
        fournisseur: 'Minoterie Dupont',
        conservation: 'Sec',
        code_barre: '9876543210987'
      }
    ]
  }
};

/**
 * Génération d'un template Excel complet
 */
function generateExcelTemplate(templateConfig, outputPath) {
  try {
    const { type_import, nom_template, description } = templateConfig;
    const config = COLUMN_CONFIGS[type_import];

    if (!config) {
      throw new Error(`Type d'import non supporté: ${type_import}`);
    }

    // Création du workbook
    const workbook = XLSX.utils.book_new();

    // Génération des feuilles
    const dataSheet = createDataSheet(config, nom_template);
    const instructionsSheet = createInstructionsSheet(config, templateConfig);
    const examplesSheet = createExamplesSheet(config);

    // Ajout des feuilles au workbook
    XLSX.utils.book_append_sheet(workbook, dataSheet, 'Données');
    XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Instructions');
    XLSX.utils.book_append_sheet(workbook, examplesSheet, 'Exemples');

    // Création du répertoire de sortie si nécessaire
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Écriture du fichier
    XLSX.writeFile(workbook, outputPath);

    logger.info('Excel template generated successfully', {
      templateType: type_import,
      outputPath,
      columns: config.columns.length
    });

    return {
      success: true,
      filePath: outputPath,
      fileName: path.basename(outputPath),
      columns: config.columns.length,
      examples: config.examples.length
    };

  } catch (error) {
    logger.error('Error generating Excel template:', error);
    throw error;
  }
}

/**
 * Création de la feuille de données principale
 */
function createDataSheet(config, templateName) {
  const { columns, examples } = config;

  // Préparation des données
  const data = [];

  // Titre
  data.push([templateName]);
  data.push([]); // Ligne vide

  // En-têtes
  const headers = columns.map(col => col.header);
  data.push(headers);

  // Descriptions
  const descriptions = columns.map(col => col.description);
  data.push(descriptions);

  // Ligne de séparation
  data.push([]);

  // Lignes d'exemple (optionnel)
  if (examples && examples.length > 0) {
    for (const example of examples.slice(0, 2)) { // Maximum 2 exemples
      const row = columns.map(col => example[col.key] || '');
      data.push(row);
    }
  }

  // Lignes vides pour la saisie
  for (let i = 0; i < 20; i++) {
    data.push(new Array(columns.length).fill(''));
  }

  // Création de la feuille
  const worksheet = XLSX.utils.aoa_to_sheet(data);

  // Application des styles et largeurs de colonnes
  applyDataSheetFormatting(worksheet, columns, data.length);

  return worksheet;
}

/**
 * Création de la feuille d'instructions
 */
function createInstructionsSheet(config, templateConfig) {
  const { columns } = config;
  const { type_import, description } = templateConfig;

  const instructions = [
    [`INSTRUCTIONS D'UTILISATION - ${type_import}`],
    [''],
    [description || 'Template d\'import de données'],
    [''],
    ['1. STRUCTURE DU FICHIER'],
    ['• Utilisez uniquement la feuille "Données" pour saisir vos informations'],
    ['• Ne modifiez pas les en-têtes de colonnes'],
    ['• Respectez les formats de données indiqués'],
    ['• Les champs marqués comme obligatoires doivent être remplis'],
    [''],
    ['2. COLONNES DISPONIBLES'],
    ['']
  ];

  // Ajout des descriptions de colonnes
  for (const column of columns) {
    const required = column.required ? ' (OBLIGATOIRE)' : ' (optionnel)';
    instructions.push([`• ${column.header}${required}: ${column.description}`]);
  }

  instructions.push(
    [''],
    ['3. RÈGLES DE VALIDATION'],
    ['• Les champs obligatoires ne peuvent pas être vides'],
    ['• Les prix doivent être des nombres positifs'],
    ['• Les quantités doivent être des nombres positifs'],
    ['• Les unités de mesure doivent être valides (kg, L, unité, etc.)'],
    [''],
    ['4. FORMAT DES INGRÉDIENTS (si applicable)'],
    ['• Séparez les ingrédients par des virgules'],
    ['• Utilisez le même nombre d\'éléments pour ingrédients, quantités et unités'],
    ['• Exemple: "Tomate,Oignon,Huile" / "200,50,30" / "g,g,mL"'],
    [''],
    ['5. CONSEILS'],
    ['• Vérifiez vos données avant l\'import'],
    ['• Consultez la feuille "Exemples" pour des modèles'],
    ['• En cas d\'erreur, corrigez et relancez l\'import'],
    ['• Sauvegardez votre fichier au format Excel (.xlsx)'],
    [''],
    ['6. SUPPORT'],
    ['• En cas de problème, contactez l\'administrateur système'],
    ['• Conservez une copie de sauvegarde de vos données']
  );

  const worksheet = XLSX.utils.aoa_to_sheet(instructions);

  // Formatage de la feuille d'instructions
  applyInstructionsFormatting(worksheet, instructions.length);

  return worksheet;
}

/**
 * Création de la feuille d'exemples
 */
function createExamplesSheet(config) {
  const { columns, examples } = config;

  if (!examples || examples.length === 0) {
    const noExamples = [['Aucun exemple disponible pour ce type d\'import']];
    return XLSX.utils.aoa_to_sheet(noExamples);
  }

  const data = [];

  // Titre
  data.push(['EXEMPLES DE DONNÉES']);
  data.push([]);

  // En-têtes
  const headers = columns.map(col => col.header);
  data.push(headers);

  // Exemples
  for (const example of examples) {
    const row = columns.map(col => example[col.key] || '');
    data.push(row);
  }

  const worksheet = XLSX.utils.aoa_to_sheet(data);

  // Formatage de la feuille d'exemples
  applyExamplesFormatting(worksheet, columns, data.length);

  return worksheet;
}

/**
 * Application du formatage pour la feuille de données
 */
function applyDataSheetFormatting(worksheet, columns, rowCount) {
  // Largeurs de colonnes
  worksheet['!cols'] = columns.map(col => ({ width: col.width || 20 }));

  // Plage de données
  const range = XLSX.utils.decode_range(worksheet['!ref']);

  // Application des styles (simplifié pour compatibilité)
  // Note: Les styles avancés nécessitent des bibliothèques supplémentaires

  // Fusion de cellules pour le titre
  if (!worksheet['!merges']) worksheet['!merges'] = [];
  worksheet['!merges'].push({
    s: { r: 0, c: 0 },
    e: { r: 0, c: columns.length - 1 }
  });

  // Protection de la feuille (optionnel)
  worksheet['!protect'] = {
    password: '',
    selectLockedCells: false,
    selectUnlockedCells: true,
    formatCells: false,
    formatColumns: false,
    formatRows: false,
    insertColumns: false,
    insertRows: false,
    insertHyperlinks: false,
    deleteColumns: false,
    deleteRows: false,
    sort: false,
    autoFilter: false,
    pivotTables: false
  };
}

/**
 * Application du formatage pour la feuille d'instructions
 */
function applyInstructionsFormatting(worksheet, rowCount) {
  // Largeur de colonne unique
  worksheet['!cols'] = [{ width: 80 }];

  // Hauteur des lignes pour le texte long
  if (!worksheet['!rows']) worksheet['!rows'] = [];
  for (let i = 0; i < rowCount; i++) {
    worksheet['!rows'][i] = { hpt: 20 }; // Hauteur en points
  }
}

/**
 * Application du formatage pour la feuille d'exemples
 */
function applyExamplesFormatting(worksheet, columns, rowCount) {
  // Largeurs de colonnes
  worksheet['!cols'] = columns.map(col => ({ width: col.width || 20 }));

  // Fusion pour le titre
  if (!worksheet['!merges']) worksheet['!merges'] = [];
  worksheet['!merges'].push({
    s: { r: 0, c: 0 },
    e: { r: 0, c: columns.length - 1 }
  });
}

/**
 * Génération d'un nom de fichier sécurisé
 */
function generateSafeFileName(templateName, type) {
  const timestamp = new Date().toISOString().slice(0, 10);
  const safeName = templateName
    .replace(/[^a-zA-Z0-9\s]/g, '')
    .replace(/\s+/g, '_')
    .toLowerCase();
  
  return `template_${type}_${safeName}_${timestamp}.xlsx`;
}

/**
 * Validation de la configuration de template
 */
function validateTemplateConfig(templateConfig) {
  const errors = [];

  if (!templateConfig.type_import) {
    errors.push('Le type d\'import est requis');
  }

  if (!templateConfig.nom_template) {
    errors.push('Le nom du template est requis');
  }

  if (!COLUMN_CONFIGS[templateConfig.type_import]) {
    errors.push(`Type d'import non supporté: ${templateConfig.type_import}`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

module.exports = {
  generateExcelTemplate,
  generateSafeFileName,
  validateTemplateConfig,
  COLUMN_CONFIGS,
  EXCEL_STYLES
};
