const logger = require('../logger');
const { isValidUnit, areUnitsCompatible } = require('./unitConversion.utils');

/**
 * Règles de validation par type d'import
 */
const VALIDATION_RULES = {
  MENU_RESTAURANT: {
    required: ['nom_produit', 'prix_vente'],
    optional: ['categorie', 'description', 'ingredients', 'quantites', 'unites'],
    types: {
      nom_produit: 'string',
      prix_vente: 'number',
      categorie: 'string',
      description: 'string',
      ingredients: 'string',
      quantites: 'string',
      unites: 'string'
    },
    constraints: {
      nom_produit: { maxLength: 255, minLength: 2 },
      prix_vente: { min: 0.01, max: 1000 },
      categorie: { maxLength: 100 },
      description: { maxLength: 1000 }
    }
  },
  
  CARTE_BAR: {
    required: ['nom_boisson', 'prix_vente'],
    optional: ['categorie', 'degre_alcool', 'description', 'ingredients', 'quantites', 'unites'],
    types: {
      nom_boisson: 'string',
      prix_vente: 'number',
      categorie: 'string',
      degre_alcool: 'number',
      description: 'string',
      ingredients: 'string',
      quantites: 'string',
      unites: 'string'
    },
    constraints: {
      nom_boisson: { maxLength: 255, minLength: 2 },
      prix_vente: { min: 0.01, max: 500 },
      degre_alcool: { min: 0, max: 100 },
      categorie: { maxLength: 100 },
      description: { maxLength: 1000 }
    }
  },
  
  INVENTAIRE_INGREDIENTS: {
    required: ['nom_ingredient', 'unite_mesure', 'prix_unitaire'],
    optional: ['categorie', 'stock_actuel', 'stock_minimal', 'fournisseur', 'conservation', 'code_barre'],
    types: {
      nom_ingredient: 'string',
      unite_mesure: 'string',
      prix_unitaire: 'number',
      categorie: 'string',
      stock_actuel: 'number',
      stock_minimal: 'number',
      fournisseur: 'string',
      conservation: 'string',
      code_barre: 'string'
    },
    constraints: {
      nom_ingredient: { maxLength: 255, minLength: 2 },
      prix_unitaire: { min: 0, max: 10000 },
      stock_actuel: { min: 0 },
      stock_minimal: { min: 0 },
      categorie: { maxLength: 100 },
      fournisseur: { maxLength: 255 },
      conservation: { enum: ['Frais', 'Congelé', 'Sec', 'Ambiant'] },
      code_barre: { maxLength: 50 }
    }
  }
};

/**
 * Validation d'une ligne de données selon le type d'import
 */
function validateImportRow(rowData, importType, rowIndex) {
  const errors = [];
  const warnings = [];
  
  try {
    const rules = VALIDATION_RULES[importType];
    
    if (!rules) {
      throw new Error(`Type d'import non supporté: ${importType}`);
    }

    // Validation des champs requis
    for (const requiredField of rules.required) {
      if (!rowData[requiredField] || rowData[requiredField].toString().trim() === '') {
        errors.push({
          row: rowIndex,
          field: requiredField,
          message: `Le champ '${requiredField}' est requis`,
          severity: 'error',
          code: 'REQUIRED_FIELD_MISSING'
        });
      }
    }

    // Validation des types et contraintes
    for (const [field, value] of Object.entries(rowData)) {
      if (value === null || value === undefined || value === '') {
        continue; // Champ vide, déjà vérifié dans les requis
      }

      const expectedType = rules.types[field];
      const constraints = rules.constraints[field];

      if (expectedType) {
        const typeValidation = validateFieldType(field, value, expectedType, rowIndex);
        if (typeValidation.error) {
          errors.push(typeValidation.error);
        }
      }

      if (constraints) {
        const constraintValidation = validateFieldConstraints(field, value, constraints, rowIndex);
        errors.push(...constraintValidation.errors);
        warnings.push(...constraintValidation.warnings);
      }
    }

    // Validations spécifiques par type d'import
    const specificValidation = validateSpecificRules(rowData, importType, rowIndex);
    errors.push(...specificValidation.errors);
    warnings.push(...specificValidation.warnings);

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };

  } catch (error) {
    logger.error('Error validating import row:', error);
    return {
      isValid: false,
      errors: [{
        row: rowIndex,
        field: 'general',
        message: `Erreur de validation: ${error.message}`,
        severity: 'error',
        code: 'VALIDATION_ERROR'
      }],
      warnings: []
    };
  }
}

/**
 * Validation du type d'un champ
 */
function validateFieldType(field, value, expectedType, rowIndex) {
  try {
    switch (expectedType) {
      case 'string':
        if (typeof value !== 'string') {
          return {
            error: {
              row: rowIndex,
              field,
              message: `Le champ '${field}' doit être une chaîne de caractères`,
              severity: 'error',
              code: 'INVALID_TYPE'
            }
          };
        }
        break;

      case 'number':
        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
          return {
            error: {
              row: rowIndex,
              field,
              message: `Le champ '${field}' doit être un nombre`,
              severity: 'error',
              code: 'INVALID_TYPE'
            }
          };
        }
        break;

      case 'integer':
        const intValue = parseInt(value);
        if (isNaN(intValue) || !Number.isInteger(parseFloat(value))) {
          return {
            error: {
              row: rowIndex,
              field,
              message: `Le champ '${field}' doit être un nombre entier`,
              severity: 'error',
              code: 'INVALID_TYPE'
            }
          };
        }
        break;
    }

    return { error: null };

  } catch (error) {
    return {
      error: {
        row: rowIndex,
        field,
        message: `Erreur de validation du type: ${error.message}`,
        severity: 'error',
        code: 'TYPE_VALIDATION_ERROR'
      }
    };
  }
}

/**
 * Validation des contraintes d'un champ
 */
function validateFieldConstraints(field, value, constraints, rowIndex) {
  const errors = [];
  const warnings = [];

  try {
    // Contrainte de longueur minimale
    if (constraints.minLength && value.toString().length < constraints.minLength) {
      errors.push({
        row: rowIndex,
        field,
        message: `Le champ '${field}' doit contenir au moins ${constraints.minLength} caractères`,
        severity: 'error',
        code: 'MIN_LENGTH_VIOLATION'
      });
    }

    // Contrainte de longueur maximale
    if (constraints.maxLength && value.toString().length > constraints.maxLength) {
      errors.push({
        row: rowIndex,
        field,
        message: `Le champ '${field}' ne peut pas dépasser ${constraints.maxLength} caractères`,
        severity: 'error',
        code: 'MAX_LENGTH_VIOLATION'
      });
    }

    // Contrainte de valeur minimale
    if (constraints.min !== undefined) {
      const numValue = parseFloat(value);
      if (!isNaN(numValue) && numValue < constraints.min) {
        errors.push({
          row: rowIndex,
          field,
          message: `Le champ '${field}' doit être supérieur ou égal à ${constraints.min}`,
          severity: 'error',
          code: 'MIN_VALUE_VIOLATION'
        });
      }
    }

    // Contrainte de valeur maximale
    if (constraints.max !== undefined) {
      const numValue = parseFloat(value);
      if (!isNaN(numValue) && numValue > constraints.max) {
        errors.push({
          row: rowIndex,
          field,
          message: `Le champ '${field}' doit être inférieur ou égal à ${constraints.max}`,
          severity: 'error',
          code: 'MAX_VALUE_VIOLATION'
        });
      }
    }

    // Contrainte d'énumération
    if (constraints.enum && !constraints.enum.includes(value)) {
      errors.push({
        row: rowIndex,
        field,
        message: `Le champ '${field}' doit être l'une des valeurs suivantes: ${constraints.enum.join(', ')}`,
        severity: 'error',
        code: 'ENUM_VIOLATION'
      });
    }

    // Contrainte de format (regex)
    if (constraints.pattern) {
      const regex = new RegExp(constraints.pattern);
      if (!regex.test(value.toString())) {
        errors.push({
          row: rowIndex,
          field,
          message: `Le champ '${field}' ne respecte pas le format attendu`,
          severity: 'error',
          code: 'PATTERN_VIOLATION'
        });
      }
    }

    return { errors, warnings };

  } catch (error) {
    return {
      errors: [{
        row: rowIndex,
        field,
        message: `Erreur de validation des contraintes: ${error.message}`,
        severity: 'error',
        code: 'CONSTRAINT_VALIDATION_ERROR'
      }],
      warnings: []
    };
  }
}

/**
 * Validations spécifiques par type d'import
 */
function validateSpecificRules(rowData, importType, rowIndex) {
  const errors = [];
  const warnings = [];

  try {
    switch (importType) {
      case 'MENU_RESTAURANT':
      case 'CARTE_BAR':
        // Validation de la cohérence ingrédients/quantités/unités
        if (rowData.ingredients || rowData.quantites || rowData.unites) {
          const ingredientsValidation = validateIngredientsConsistency(
            rowData.ingredients,
            rowData.quantites,
            rowData.unites,
            rowIndex
          );
          errors.push(...ingredientsValidation.errors);
          warnings.push(...ingredientsValidation.warnings);
        }
        break;

      case 'INVENTAIRE_INGREDIENTS':
        // Validation de l'unité de mesure
        if (rowData.unite_mesure && !isValidUnit(rowData.unite_mesure)) {
          errors.push({
            row: rowIndex,
            field: 'unite_mesure',
            message: `Unité de mesure non supportée: ${rowData.unite_mesure}`,
            severity: 'error',
            code: 'INVALID_UNIT'
          });
        }

        // Validation de la cohérence stock actuel/minimal
        if (rowData.stock_actuel && rowData.stock_minimal) {
          const stockActuel = parseFloat(rowData.stock_actuel);
          const stockMinimal = parseFloat(rowData.stock_minimal);
          
          if (!isNaN(stockActuel) && !isNaN(stockMinimal) && stockActuel < stockMinimal) {
            warnings.push({
              row: rowIndex,
              field: 'stock_actuel',
              message: 'Le stock actuel est inférieur au stock minimal',
              severity: 'warning',
              code: 'LOW_STOCK_WARNING'
            });
          }
        }
        break;
    }

    return { errors, warnings };

  } catch (error) {
    return {
      errors: [{
        row: rowIndex,
        field: 'general',
        message: `Erreur de validation spécifique: ${error.message}`,
        severity: 'error',
        code: 'SPECIFIC_VALIDATION_ERROR'
      }],
      warnings: []
    };
  }
}

/**
 * Validation de la cohérence entre ingrédients, quantités et unités
 */
function validateIngredientsConsistency(ingredients, quantites, unites, rowIndex) {
  const errors = [];
  const warnings = [];

  try {
    if (!ingredients && !quantites && !unites) {
      return { errors, warnings }; // Tous vides, pas de problème
    }

    if (!ingredients || !quantites || !unites) {
      errors.push({
        row: rowIndex,
        field: 'ingredients',
        message: 'Si des ingrédients sont spécifiés, les quantités et unités doivent aussi être fournies',
        severity: 'error',
        code: 'INCOMPLETE_INGREDIENTS'
      });
      return { errors, warnings };
    }

    const ingredientsList = ingredients.split(',').map(i => i.trim()).filter(i => i);
    const quantitesList = quantites.split(',').map(q => q.trim()).filter(q => q);
    const unitesList = unites.split(',').map(u => u.trim()).filter(u => u);

    // Vérification du nombre d'éléments
    if (ingredientsList.length !== quantitesList.length || 
        ingredientsList.length !== unitesList.length) {
      errors.push({
        row: rowIndex,
        field: 'ingredients',
        message: 'Le nombre d\'ingrédients, quantités et unités doit être identique',
        severity: 'error',
        code: 'INGREDIENTS_COUNT_MISMATCH'
      });
      return { errors, warnings };
    }

    // Validation de chaque élément
    for (let i = 0; i < ingredientsList.length; i++) {
      const ingredient = ingredientsList[i];
      const quantite = quantitesList[i];
      const unite = unitesList[i];

      // Validation de l'ingrédient
      if (!ingredient || ingredient.length < 2) {
        errors.push({
          row: rowIndex,
          field: 'ingredients',
          message: `Nom d'ingrédient invalide à la position ${i + 1}: "${ingredient}"`,
          severity: 'error',
          code: 'INVALID_INGREDIENT_NAME'
        });
      }

      // Validation de la quantité
      const quantiteNum = parseFloat(quantite);
      if (isNaN(quantiteNum) || quantiteNum <= 0) {
        errors.push({
          row: rowIndex,
          field: 'quantites',
          message: `Quantité invalide à la position ${i + 1}: "${quantite}"`,
          severity: 'error',
          code: 'INVALID_QUANTITY'
        });
      }

      // Validation de l'unité
      if (!isValidUnit(unite)) {
        warnings.push({
          row: rowIndex,
          field: 'unites',
          message: `Unité non reconnue à la position ${i + 1}: "${unite}"`,
          severity: 'warning',
          code: 'UNKNOWN_UNIT'
        });
      }
    }

    return { errors, warnings };

  } catch (error) {
    return {
      errors: [{
        row: rowIndex,
        field: 'ingredients',
        message: `Erreur de validation des ingrédients: ${error.message}`,
        severity: 'error',
        code: 'INGREDIENTS_VALIDATION_ERROR'
      }],
      warnings: []
    };
  }
}

/**
 * Validation d'un fichier complet
 */
function validateImportFile(data, importType) {
  try {
    const results = {
      totalRows: data.length,
      validRows: 0,
      errorRows: 0,
      warningRows: 0,
      errors: [],
      warnings: [],
      summary: {
        criticalErrors: 0,
        minorErrors: 0,
        warnings: 0
      }
    };

    for (let i = 0; i < data.length; i++) {
      const rowValidation = validateImportRow(data[i], importType, i + 1);
      
      if (rowValidation.isValid) {
        results.validRows++;
      } else {
        results.errorRows++;
      }

      if (rowValidation.warnings.length > 0) {
        results.warningRows++;
      }

      results.errors.push(...rowValidation.errors);
      results.warnings.push(...rowValidation.warnings);

      // Classification des erreurs
      for (const error of rowValidation.errors) {
        if (['REQUIRED_FIELD_MISSING', 'INVALID_TYPE'].includes(error.code)) {
          results.summary.criticalErrors++;
        } else {
          results.summary.minorErrors++;
        }
      }

      results.summary.warnings += rowValidation.warnings.length;
    }

    return results;

  } catch (error) {
    logger.error('Error validating import file:', error);
    throw error;
  }
}

/**
 * Génération de suggestions de correction
 */
function generateCorrectionSuggestions(errors) {
  const suggestions = [];

  for (const error of errors) {
    let suggestion = null;

    switch (error.code) {
      case 'REQUIRED_FIELD_MISSING':
        suggestion = `Ajoutez une valeur pour le champ '${error.field}' à la ligne ${error.row}`;
        break;
      case 'INVALID_TYPE':
        suggestion = `Vérifiez le format de la valeur du champ '${error.field}' à la ligne ${error.row}`;
        break;
      case 'INVALID_UNIT':
        suggestion = `Utilisez une unité valide pour '${error.field}' à la ligne ${error.row} (ex: kg, L, unité)`;
        break;
      case 'INGREDIENTS_COUNT_MISMATCH':
        suggestion = `Vérifiez que le nombre d'ingrédients, quantités et unités correspond à la ligne ${error.row}`;
        break;
      default:
        suggestion = `Corrigez l'erreur dans le champ '${error.field}' à la ligne ${error.row}`;
    }

    if (suggestion) {
      suggestions.push({
        row: error.row,
        field: error.field,
        error: error.message,
        suggestion
      });
    }
  }

  return suggestions;
}

module.exports = {
  validateImportRow,
  validateImportFile,
  validateIngredientsConsistency,
  generateCorrectionSuggestions,
  VALIDATION_RULES
};
