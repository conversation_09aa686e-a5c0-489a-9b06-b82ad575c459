const logger = require('../logger');
const { convertUnit, areUnitsCompatible } = require('./unitConversion.utils');

/**
 * Calcul du coût total d'une recette
 */
function calculateRecipeTotalCost(ingredients) {
  try {
    if (!Array.isArray(ingredients)) {
      throw new Error('Les ingrédients doivent être fournis sous forme de tableau');
    }

    let totalCost = 0;
    const costBreakdown = [];

    for (const ingredient of ingredients) {
      const {
        nom,
        quantiteNecessaire,
        uniteMesure,
        prixUnitaire,
        uniteAchat = uniteMesure
      } = ingredient;

      // Validation des données
      if (!quantiteNecessaire || !prixUnitaire) {
        logger.warn('Ingredient missing required data for cost calculation', {
          nom,
          quantiteNecessaire,
          prixUnitaire
        });
        continue;
      }

      let ingredientCost = 0;

      // Conversion d'unité si nécessaire
      if (uniteMesure !== uniteAchat && areUnitsCompatible(uniteMesure, uniteAchat)) {
        try {
          const conversion = convertUnit(quantiteNecessaire, uniteMesure, uniteAchat);
          ingredientCost = conversion.value * prixUnitaire;
        } catch (conversionError) {
          logger.warn('Unit conversion failed, using direct calculation', {
            nom,
            error: conversionError.message
          });
          ingredientCost = quantiteNecessaire * prixUnitaire;
        }
      } else {
        ingredientCost = quantiteNecessaire * prixUnitaire;
      }

      totalCost += ingredientCost;
      
      costBreakdown.push({
        nom,
        quantiteNecessaire,
        uniteMesure,
        prixUnitaire,
        coutTotal: Math.round(ingredientCost * 100) / 100
      });
    }

    return {
      totalCost: Math.round(totalCost * 100) / 100,
      costBreakdown,
      ingredientCount: ingredients.length
    };

  } catch (error) {
    logger.error('Error calculating recipe total cost:', error);
    throw error;
  }
}

/**
 * Calcul du prix de vente suggéré basé sur le coût et la marge
 */
function calculateSuggestedPrice(cost, targetMarginPercent, roundingStrategy = 'nearest') {
  try {
    if (cost <= 0) {
      throw new Error('Le coût doit être supérieur à zéro');
    }

    if (targetMarginPercent < 0 || targetMarginPercent >= 100) {
      throw new Error('La marge doit être entre 0 et 99%');
    }

    // Calcul du prix de base
    const basePrice = cost / (1 - (targetMarginPercent / 100));

    // Application de la stratégie d'arrondi
    let suggestedPrice;
    
    switch (roundingStrategy) {
      case 'up':
        suggestedPrice = Math.ceil(basePrice);
        break;
      case 'down':
        suggestedPrice = Math.floor(basePrice);
        break;
      case 'nearest_50':
        suggestedPrice = Math.round(basePrice * 2) / 2;
        break;
      case 'nearest_euro':
        suggestedPrice = Math.round(basePrice);
        break;
      case 'psychological':
        // Prix psychologique (ex: 9.99, 19.90)
        const rounded = Math.ceil(basePrice);
        suggestedPrice = rounded - 0.01;
        break;
      default: // 'nearest'
        suggestedPrice = Math.round(basePrice * 100) / 100;
    }

    // Calcul de la marge réelle
    const actualMargin = ((suggestedPrice - cost) / suggestedPrice) * 100;

    return {
      suggestedPrice: Math.round(suggestedPrice * 100) / 100,
      actualMargin: Math.round(actualMargin * 100) / 100,
      targetMargin: targetMarginPercent,
      cost,
      profit: Math.round((suggestedPrice - cost) * 100) / 100
    };

  } catch (error) {
    logger.error('Error calculating suggested price:', error);
    throw error;
  }
}

/**
 * Analyse de rentabilité d'un produit
 */
function analyzeProfitability(cost, sellingPrice, targetMargin = null) {
  try {
    if (cost < 0 || sellingPrice < 0) {
      throw new Error('Le coût et le prix de vente doivent être positifs');
    }

    const profit = sellingPrice - cost;
    const marginPercent = sellingPrice > 0 ? (profit / sellingPrice) * 100 : 0;
    const markupPercent = cost > 0 ? (profit / cost) * 100 : 0;

    // Classification de la rentabilité
    let profitabilityStatus;
    if (profit <= 0) {
      profitabilityStatus = 'LOSS';
    } else if (targetMargin && marginPercent < targetMargin * 0.8) {
      profitabilityStatus = 'LOW';
    } else if (targetMargin && marginPercent >= targetMargin) {
      profitabilityStatus = 'OPTIMAL';
    } else {
      profitabilityStatus = 'ACCEPTABLE';
    }

    // Recommandations
    const recommendations = [];
    if (profit <= 0) {
      recommendations.push('Augmenter le prix de vente ou réduire les coûts');
    } else if (targetMargin && marginPercent < targetMargin) {
      const suggestedPrice = calculateSuggestedPrice(cost, targetMargin);
      recommendations.push(`Augmenter le prix à ${suggestedPrice.suggestedPrice}FCFA pour atteindre la marge cible`);
    }

    return {
      cost,
      sellingPrice,
      profit: Math.round(profit * 100) / 100,
      marginPercent: Math.round(marginPercent * 100) / 100,
      markupPercent: Math.round(markupPercent * 100) / 100,
      profitabilityStatus,
      recommendations,
      targetMargin
    };

  } catch (error) {
    logger.error('Error analyzing profitability:', error);
    throw error;
  }
}

/**
 * Calcul du coût par portion
 */
function calculateCostPerPortion(totalCost, numberOfPortions) {
  try {
    if (totalCost < 0) {
      throw new Error('Le coût total doit être positif');
    }

    if (numberOfPortions <= 0) {
      throw new Error('Le nombre de portions doit être supérieur à zéro');
    }

    const costPerPortion = totalCost / numberOfPortions;

    return {
      totalCost,
      numberOfPortions,
      costPerPortion: Math.round(costPerPortion * 100) / 100
    };

  } catch (error) {
    logger.error('Error calculating cost per portion:', error);
    throw error;
  }
}

/**
 * Optimisation des prix pour un ensemble de produits
 */
function optimizePricing(products, constraints = {}) {
  try {
    const {
      minMargin = 20,
      maxMargin = 80,
      competitorPrices = {},
      priceRoundingStrategy = 'nearest'
    } = constraints;

    const optimizedProducts = [];

    for (const product of products) {
      const { id, name, cost, currentPrice, targetMargin = minMargin } = product;

      // Calcul du prix optimal
      const suggested = calculateSuggestedPrice(cost, targetMargin, priceRoundingStrategy);
      
      // Prise en compte des prix concurrents
      let finalPrice = suggested.suggestedPrice;
      if (competitorPrices[id]) {
        const competitorPrice = competitorPrices[id];
        const maxAcceptablePrice = competitorPrice * 1.1; // 10% au-dessus du concurrent
        
        if (finalPrice > maxAcceptablePrice) {
          finalPrice = maxAcceptablePrice;
        }
      }

      // Analyse de la rentabilité avec le prix final
      const profitability = analyzeProfitability(cost, finalPrice, targetMargin);

      optimizedProducts.push({
        id,
        name,
        cost,
        currentPrice,
        suggestedPrice: finalPrice,
        priceChange: finalPrice - currentPrice,
        priceChangePercent: currentPrice > 0 ? ((finalPrice - currentPrice) / currentPrice) * 100 : 0,
        profitability,
        recommendations: profitability.recommendations
      });
    }

    return {
      optimizedProducts,
      summary: {
        totalProducts: products.length,
        averagePriceIncrease: optimizedProducts.reduce((sum, p) => sum + p.priceChange, 0) / products.length,
        profitableProducts: optimizedProducts.filter(p => p.profitability.profitabilityStatus !== 'LOSS').length
      }
    };

  } catch (error) {
    logger.error('Error optimizing pricing:', error);
    throw error;
  }
}

/**
 * Calcul de l'impact d'un changement de prix d'ingrédient
 */
function calculateIngredientPriceImpact(recipes, ingredientId, oldPrice, newPrice) {
  try {
    const impactedRecipes = [];
    let totalImpact = 0;

    for (const recipe of recipes) {
      const ingredient = recipe.ingredients.find(ing => ing.id === ingredientId);
      
      if (ingredient) {
        const oldCost = ingredient.quantiteNecessaire * oldPrice;
        const newCost = ingredient.quantiteNecessaire * newPrice;
        const costDifference = newCost - oldCost;
        
        const newTotalCost = recipe.totalCost + costDifference;
        const currentMargin = recipe.sellingPrice > 0 ? 
          ((recipe.sellingPrice - recipe.totalCost) / recipe.sellingPrice) * 100 : 0;
        const newMargin = recipe.sellingPrice > 0 ? 
          ((recipe.sellingPrice - newTotalCost) / recipe.sellingPrice) * 100 : 0;

        impactedRecipes.push({
          recipeId: recipe.id,
          recipeName: recipe.name,
          oldCost: recipe.totalCost,
          newCost: newTotalCost,
          costIncrease: costDifference,
          currentMargin,
          newMargin,
          marginImpact: newMargin - currentMargin,
          sellingPrice: recipe.sellingPrice
        });

        totalImpact += costDifference;
      }
    }

    return {
      ingredientId,
      oldPrice,
      newPrice,
      priceIncrease: newPrice - oldPrice,
      priceIncreasePercent: oldPrice > 0 ? ((newPrice - oldPrice) / oldPrice) * 100 : 0,
      impactedRecipes,
      totalCostImpact: Math.round(totalImpact * 100) / 100,
      averageCostImpact: impactedRecipes.length > 0 ? 
        Math.round((totalImpact / impactedRecipes.length) * 100) / 100 : 0
    };

  } catch (error) {
    logger.error('Error calculating ingredient price impact:', error);
    throw error;
  }
}

/**
 * Calcul des économies potentielles par optimisation des recettes
 */
function calculateOptimizationSavings(recipe, alternativeIngredients) {
  try {
    const currentCost = calculateRecipeTotalCost(recipe.ingredients);
    const optimizedIngredients = [...recipe.ingredients];
    let totalSavings = 0;

    for (const alternative of alternativeIngredients) {
      const { originalIngredientId, newIngredient, conversionFactor = 1 } = alternative;
      
      const originalIndex = optimizedIngredients.findIndex(ing => ing.id === originalIngredientId);
      
      if (originalIndex !== -1) {
        const original = optimizedIngredients[originalIndex];
        const newQuantity = original.quantiteNecessaire * conversionFactor;
        const newCost = newQuantity * newIngredient.prixUnitaire;
        const originalCost = original.quantiteNecessaire * original.prixUnitaire;
        const savings = originalCost - newCost;

        if (savings > 0) {
          optimizedIngredients[originalIndex] = {
            ...newIngredient,
            quantiteNecessaire: newQuantity,
            coutTotal: newCost
          };
          totalSavings += savings;
        }
      }
    }

    const optimizedCost = calculateRecipeTotalCost(optimizedIngredients);

    return {
      originalCost: currentCost.totalCost,
      optimizedCost: optimizedCost.totalCost,
      totalSavings: Math.round(totalSavings * 100) / 100,
      savingsPercent: currentCost.totalCost > 0 ? 
        (totalSavings / currentCost.totalCost) * 100 : 0,
      optimizedIngredients
    };

  } catch (error) {
    logger.error('Error calculating optimization savings:', error);
    throw error;
  }
}

module.exports = {
  calculateRecipeTotalCost,
  calculateSuggestedPrice,
  analyzeProfitability,
  calculateCostPerPortion,
  optimizePricing,
  calculateIngredientPriceImpact,
  calculateOptimizationSavings
};
