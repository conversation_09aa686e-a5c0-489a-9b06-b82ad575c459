const logger = require('../logger');

/**
 * Unités de mesure supportées avec leurs conversions vers l'unité de base
 */
const UNIT_CONVERSIONS = {
  // Unités de poids (base: grammes)
  weight: {
    'g': 1,
    'kg': 1000,
    'mg': 0.001,
    'lb': 453.592,
    'oz': 28.3495
  },
  
  // Unités de volume (base: millilitres)
  volume: {
    'mL': 1,
    'L': 1000,
    'cL': 10,
    'dL': 100,
    'fl oz': 29.5735,
    'cup': 236.588,
    'pint': 473.176,
    'quart': 946.353,
    'gallon': 3785.41
  },
  
  // Unités de quantité (base: unité)
  quantity: {
    'unité': 1,
    'pièce': 1,
    'portion': 1,
    'douzaine': 12,
    'centaine': 100
  }
};

/**
 * Mapping des unités vers leurs catégories
 */
const UNIT_CATEGORIES = {
  // Poids
  'g': 'weight',
  'kg': 'weight',
  'mg': 'weight',
  'lb': 'weight',
  'oz': 'weight',
  
  // Volume
  'mL': 'volume',
  'L': 'volume',
  'cL': 'volume',
  'dL': 'volume',
  'fl oz': 'volume',
  'cup': 'volume',
  'pint': 'volume',
  'quart': 'volume',
  'gallon': 'volume',
  
  // Quantité
  'unité': 'quantity',
  'pièce': 'quantity',
  'portion': 'quantity',
  'douzaine': 'quantity',
  'centaine': 'quantity'
};

/**
 * Unités recommandées par catégorie d'ingrédient
 */
const RECOMMENDED_UNITS = {
  'Légumes': ['kg', 'g', 'unité'],
  'Fruits': ['kg', 'g', 'unité'],
  'Viandes': ['kg', 'g'],
  'Poissons': ['kg', 'g'],
  'Produits laitiers': ['L', 'mL', 'kg', 'g'],
  'Céréales': ['kg', 'g'],
  'Légumineuses': ['kg', 'g'],
  'Épices': ['g', 'mg'],
  'Huiles': ['L', 'mL'],
  'Boissons': ['L', 'mL'],
  'Alcools': ['L', 'mL'],
  'Condiments': ['mL', 'g'],
  'Produits de boulangerie': ['kg', 'g', 'unité']
};

/**
 * Conversion entre deux unités
 */
function convertUnit(value, fromUnit, toUnit) {
  try {
    // Validation des paramètres
    if (typeof value !== 'number' || value < 0) {
      throw new Error('La valeur doit être un nombre positif');
    }
    
    if (!fromUnit || !toUnit) {
      throw new Error('Les unités source et destination sont requises');
    }
    
    // Normalisation des unités (suppression des espaces, casse)
    const normalizedFromUnit = normalizeUnit(fromUnit);
    const normalizedToUnit = normalizeUnit(toUnit);
    
    // Si les unités sont identiques, pas de conversion
    if (normalizedFromUnit === normalizedToUnit) {
      return {
        value: value,
        fromUnit: normalizedFromUnit,
        toUnit: normalizedToUnit,
        conversionFactor: 1
      };
    }
    
    // Vérification que les unités existent
    const fromCategory = UNIT_CATEGORIES[normalizedFromUnit];
    const toCategory = UNIT_CATEGORIES[normalizedToUnit];
    
    if (!fromCategory || !toCategory) {
      throw new Error(`Unité non supportée: ${!fromCategory ? normalizedFromUnit : normalizedToUnit}`);
    }
    
    // Vérification que les unités sont de la même catégorie
    if (fromCategory !== toCategory) {
      throw new Error(`Impossible de convertir entre ${fromCategory} et ${toCategory}`);
    }
    
    // Conversion via l'unité de base
    const fromFactor = UNIT_CONVERSIONS[fromCategory][normalizedFromUnit];
    const toFactor = UNIT_CONVERSIONS[toCategory][normalizedToUnit];
    
    const baseValue = value * fromFactor;
    const convertedValue = baseValue / toFactor;
    const conversionFactor = fromFactor / toFactor;
    
    return {
      value: Math.round(convertedValue * 1000000) / 1000000, // Arrondi à 6 décimales
      fromUnit: normalizedFromUnit,
      toUnit: normalizedToUnit,
      conversionFactor: conversionFactor
    };
    
  } catch (error) {
    logger.error('Error in unit conversion:', {
      value,
      fromUnit,
      toUnit,
      error: error.message
    });
    throw error;
  }
}

/**
 * Normalisation d'une unité
 */
function normalizeUnit(unit) {
  if (!unit) return null;
  
  // Suppression des espaces et conversion en minuscules
  let normalized = unit.toString().trim();
  
  // Mapping des variantes communes
  const unitMappings = {
    'gramme': 'g',
    'grammes': 'g',
    'kilogramme': 'kg',
    'kilogrammes': 'kg',
    'litre': 'L',
    'litres': 'L',
    'millilitre': 'mL',
    'millilitres': 'mL',
    'centilitre': 'cL',
    'centilitres': 'cL',
    'décilitre': 'dL',
    'décilitres': 'dL',
    'unite': 'unité',
    'unites': 'unité',
    'piece': 'pièce',
    'pieces': 'pièce',
    'ml': 'mL',
    'cl': 'cL',
    'dl': 'dL',
    'l': 'L'
  };
  
  return unitMappings[normalized.toLowerCase()] || normalized;
}

/**
 * Validation d'une unité
 */
function isValidUnit(unit) {
  const normalizedUnit = normalizeUnit(unit);
  return normalizedUnit && UNIT_CATEGORIES.hasOwnProperty(normalizedUnit);
}

/**
 * Obtenir la catégorie d'une unité
 */
function getUnitCategory(unit) {
  const normalizedUnit = normalizeUnit(unit);
  return UNIT_CATEGORIES[normalizedUnit] || null;
}

/**
 * Obtenir les unités disponibles pour une catégorie
 */
function getUnitsForCategory(category) {
  if (!UNIT_CONVERSIONS[category]) {
    return [];
  }
  
  return Object.keys(UNIT_CONVERSIONS[category]);
}

/**
 * Obtenir les unités recommandées pour une catégorie d'ingrédient
 */
function getRecommendedUnits(ingredientCategory) {
  return RECOMMENDED_UNITS[ingredientCategory] || ['kg', 'g', 'L', 'mL', 'unité'];
}

/**
 * Conversion automatique vers l'unité la plus appropriée
 */
function autoConvertToOptimalUnit(value, unit, ingredientCategory = null) {
  try {
    const normalizedUnit = normalizeUnit(unit);
    const category = getUnitCategory(normalizedUnit);
    
    if (!category) {
      throw new Error(`Unité non supportée: ${unit}`);
    }
    
    // Règles de conversion automatique
    let targetUnit = normalizedUnit;
    
    if (category === 'weight') {
      if (value >= 1000 && normalizedUnit === 'g') {
        targetUnit = 'kg';
      } else if (value < 1 && normalizedUnit === 'kg') {
        targetUnit = 'g';
      }
    } else if (category === 'volume') {
      if (value >= 1000 && normalizedUnit === 'mL') {
        targetUnit = 'L';
      } else if (value < 1 && normalizedUnit === 'L') {
        targetUnit = 'mL';
      }
    }
    
    // Utilisation des unités recommandées si disponibles
    if (ingredientCategory) {
      const recommended = getRecommendedUnits(ingredientCategory);
      const currentCategory = getUnitCategory(normalizedUnit);
      const recommendedForCategory = recommended.filter(u => getUnitCategory(u) === currentCategory);
      
      if (recommendedForCategory.length > 0) {
        // Choix de l'unité recommandée la plus appropriée
        if (currentCategory === 'weight') {
          targetUnit = value >= 1000 ? 'kg' : 'g';
        } else if (currentCategory === 'volume') {
          targetUnit = value >= 1000 ? 'L' : 'mL';
        }
        
        // Vérification que l'unité cible est dans les recommandations
        if (!recommendedForCategory.includes(targetUnit)) {
          targetUnit = recommendedForCategory[0];
        }
      }
    }
    
    return convertUnit(value, normalizedUnit, targetUnit);
    
  } catch (error) {
    logger.error('Error in auto conversion:', error);
    throw error;
  }
}

/**
 * Calcul du coût unitaire après conversion
 */
function calculateConvertedCost(originalCost, originalQuantity, originalUnit, targetQuantity, targetUnit) {
  try {
    // Conversion de la quantité vers l'unité cible
    const conversion = convertUnit(originalQuantity, originalUnit, targetUnit);
    
    // Calcul du coût par unité cible
    const costPerTargetUnit = (originalCost * originalQuantity) / (conversion.value * targetQuantity);
    
    return {
      costPerUnit: Math.round(costPerTargetUnit * 100) / 100, // Arrondi à 2 décimales
      totalCost: Math.round(costPerTargetUnit * targetQuantity * 100) / 100,
      conversion: conversion
    };
    
  } catch (error) {
    logger.error('Error calculating converted cost:', error);
    throw error;
  }
}

/**
 * Validation de compatibilité entre unités
 */
function areUnitsCompatible(unit1, unit2) {
  try {
    const category1 = getUnitCategory(unit1);
    const category2 = getUnitCategory(unit2);
    
    return category1 && category2 && category1 === category2;
  } catch (error) {
    return false;
  }
}

/**
 * Obtenir toutes les unités supportées
 */
function getAllSupportedUnits() {
  const allUnits = {};
  
  for (const [category, units] of Object.entries(UNIT_CONVERSIONS)) {
    allUnits[category] = Object.keys(units);
  }
  
  return allUnits;
}

module.exports = {
  convertUnit,
  normalizeUnit,
  isValidUnit,
  getUnitCategory,
  getUnitsForCategory,
  getRecommendedUnits,
  autoConvertToOptimalUnit,
  calculateConvertedCost,
  areUnitsCompatible,
  getAllSupportedUnits,
  UNIT_CATEGORIES,
  UNIT_CONVERSIONS,
  RECOMMENDED_UNITS
};
