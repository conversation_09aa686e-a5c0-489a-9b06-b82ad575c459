-- Script de test pour vérifier la migration des réservations anonymes
-- À exécuter après la migration pour s'assurer que tout fonctionne

-- =====================================================
-- Tests de base - Structure de la base de données
-- =====================================================

\echo '=== TESTS DE STRUCTURE ==='

-- Test 1: Vérifier que les colonnes ont été ajoutées à la table Clients
\echo 'Test 1: Colonnes table Clients'
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'Clients' 
AND column_name IN ('est_anonyme', 'pseudonyme', 'code_acces_anonyme')
ORDER BY column_name;

-- Test 2: Vérifier que les colonnes ont été ajoutées à la table Reservations
\echo 'Test 2: Colonnes table Reservations'
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'Reservations' 
AND column_name IN ('est_anonyme', 'code_acces_direct')
ORDER BY column_name;

-- Test 3: Vérifier que les nouvelles tables existent
\echo 'Test 3: Nouvelles tables'
SELECT table_name, table_type
FROM information_schema.tables 
WHERE table_name IN ('LogsAccesAnonymes', 'ConfigurationReservationsAnonymes', 'SecuriteAccesAnonymes')
ORDER BY table_name;

-- Test 4: Vérifier les contraintes
\echo 'Test 4: Contraintes'
SELECT conname, contype, pg_get_constraintdef(oid) as definition
FROM pg_constraint 
WHERE conrelid IN ('Clients'::regclass, 'Reservations'::regclass)
AND conname LIKE '%anonyme%'
ORDER BY conname;

-- Test 5: Vérifier les index
\echo 'Test 5: Index'
SELECT indexname, tablename, indexdef
FROM pg_indexes 
WHERE indexname LIKE '%anonyme%' OR indexname LIKE '%acces%'
ORDER BY tablename, indexname;

-- =====================================================
-- Tests fonctionnels - Fonctions et vues
-- =====================================================

\echo '=== TESTS FONCTIONNELS ==='

-- Test 6: Tester la fonction de génération de codes
\echo 'Test 6: Génération de codes d\'accès'
SELECT 
  generer_code_acces_anonyme() as code_defaut,
  generer_code_acces_anonyme('TEST', 8) as code_personnalise,
  length(generer_code_acces_anonyme()) as longueur_defaut,
  length(generer_code_acces_anonyme('CUSTOM', 16)) as longueur_personnalisee;

-- Test 7: Vérifier les vues
\echo 'Test 7: Vues créées'
SELECT viewname, definition 
FROM pg_views 
WHERE viewname LIKE '%Anonyme%'
ORDER BY viewname;

-- Test 8: Tester la fonction de nettoyage (sans supprimer de données)
\echo 'Test 8: Fonction de nettoyage'
SELECT 'Fonction nettoyer_logs_acces_anonymes existe' as test,
       EXISTS(SELECT 1 FROM pg_proc WHERE proname = 'nettoyer_logs_acces_anonymes') as resultat;

-- =====================================================
-- Tests de données - Insertion et validation
-- =====================================================

\echo '=== TESTS DE DONNÉES ==='

-- Test 9: Créer un client anonyme de test
\echo 'Test 9: Création client anonyme'
DO $$
DECLARE
  code_test varchar;
  client_id_test integer;
BEGIN
  -- Générer un code unique
  code_test := generer_code_acces_anonyme('TEST', 10);
  
  -- Insérer un client anonyme
  INSERT INTO "Clients" (
    chaine_id, 
    complexe_creation_id, 
    est_anonyme, 
    pseudonyme, 
    code_acces_anonyme
  ) VALUES (
    1, 1, true, 'TestUser', code_test
  ) RETURNING client_id INTO client_id_test;
  
  RAISE NOTICE 'Client anonyme créé avec ID: % et code: %', client_id_test, code_test;
  
  -- Vérifier que le client a été créé
  IF EXISTS(SELECT 1 FROM "Clients" WHERE client_id = client_id_test AND est_anonyme = true) THEN
    RAISE NOTICE 'Test 9: SUCCÈS - Client anonyme créé correctement';
  ELSE
    RAISE NOTICE 'Test 9: ÉCHEC - Problème lors de la création du client anonyme';
  END IF;
  
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Test 9: ERREUR - %', SQLERRM;
END $$;

-- Test 10: Tester les contraintes de validation
\echo 'Test 10: Contraintes de validation'
DO $$
BEGIN
  -- Tenter de créer un client anonyme sans code d'accès (doit échouer)
  BEGIN
    INSERT INTO "Clients" (chaine_id, complexe_creation_id, est_anonyme, pseudonyme) 
    VALUES (1, 1, true, 'TestSansCode');
    RAISE NOTICE 'Test 10a: ÉCHEC - Contrainte non respectée (client anonyme sans code)';
  EXCEPTION
    WHEN check_violation THEN
      RAISE NOTICE 'Test 10a: SUCCÈS - Contrainte respectée (client anonyme sans code rejeté)';
    WHEN OTHERS THEN
      RAISE NOTICE 'Test 10a: ERREUR - %', SQLERRM;
  END;
  
  -- Tenter de créer un client normal sans nom (doit échouer)
  BEGIN
    INSERT INTO "Clients" (chaine_id, complexe_creation_id, est_anonyme, prenom) 
    VALUES (1, 1, false, 'TestSansNom');
    RAISE NOTICE 'Test 10b: ÉCHEC - Contrainte non respectée (client normal sans nom)';
  EXCEPTION
    WHEN check_violation THEN
      RAISE NOTICE 'Test 10b: SUCCÈS - Contrainte respectée (client normal sans nom rejeté)';
    WHEN OTHERS THEN
      RAISE NOTICE 'Test 10b: ERREUR - %', SQLERRM;
  END;
END $$;

-- Test 11: Tester l'insertion dans les logs
\echo 'Test 11: Logs d\'accès'
DO $$
DECLARE
  code_test varchar := 'TEST-LOGACCESS';
BEGIN
  -- Insérer un log de test
  INSERT INTO "LogsAccesAnonymes" (
    code_acces_anonyme,
    adresse_ip,
    action,
    details
  ) VALUES (
    code_test,
    '*************',
    'TEST_ACCESS',
    '{"test": true, "timestamp": "' || now() || '"}'::json
  );
  
  -- Vérifier que le log a été inséré
  IF EXISTS(SELECT 1 FROM "LogsAccesAnonymes" WHERE code_acces_anonyme = code_test) THEN
    RAISE NOTICE 'Test 11: SUCCÈS - Log d''accès créé correctement';
  ELSE
    RAISE NOTICE 'Test 11: ÉCHEC - Problème lors de la création du log';
  END IF;
  
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Test 11: ERREUR - %', SQLERRM;
END $$;

-- Test 12: Vérifier la configuration par défaut
\echo 'Test 12: Configuration par défaut'
SELECT 
  complexe_id,
  actif,
  duree_validite_code_heures,
  max_tentatives_acces,
  autoriser_modification,
  autoriser_annulation
FROM "ConfigurationReservationsAnonymes"
LIMIT 5;

-- =====================================================
-- Tests de performance - Index et requêtes
-- =====================================================

\echo '=== TESTS DE PERFORMANCE ==='

-- Test 13: Vérifier l'utilisation des index
\echo 'Test 13: Utilisation des index'
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM "Clients" WHERE code_acces_anonyme = 'TEST-NONEXISTANT';

-- Test 14: Performance des vues
\echo 'Test 14: Performance des vues'
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM "VueReservationsAnonymes" LIMIT 10;

-- =====================================================
-- Nettoyage des données de test
-- =====================================================

\echo '=== NETTOYAGE ==='

-- Supprimer les données de test créées
DELETE FROM "LogsAccesAnonymes" WHERE code_acces_anonyme LIKE 'TEST-%';
DELETE FROM "Clients" WHERE code_acces_anonyme LIKE 'TEST-%';

\echo 'Données de test supprimées'

-- =====================================================
-- Résumé des tests
-- =====================================================

\echo '=== RÉSUMÉ ==='

SELECT 
  'Migration des réservations anonymes' as test_suite,
  'Terminé' as statut,
  now() as timestamp_test;

\echo 'Tests terminés. Vérifiez les messages ci-dessus pour détecter d''éventuels problèmes.'
\echo 'Si tous les tests sont en SUCCÈS, la migration s''est déroulée correctement.'
