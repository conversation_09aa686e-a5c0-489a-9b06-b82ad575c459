-- Création de la table Reservations si elle n'existe pas
CREATE TABLE IF NOT EXISTS Reservations (
    reservation_id SERIAL PRIMARY KEY,
    numero_reservation VARCHAR(20) UNIQUE NOT NULL,
    date DATE NOT NULL,
    heure_debut TIME NOT NULL,
    heure_fin TIME NOT NULL,
    type_chambre VARCHAR(50) NOT NULL,
    nombre_personnes INTEGER NOT NULL,
    client_info JSONB NOT NULL,
    commentaires TEXT,
    statut VARCHAR(20) NOT NULL DEFAULT 'en_attente',
    date_demande TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_confirmation TIMESTAMP,
    date_modification TIMESTAMP,
    reception_id INTEGER,
    utilisateur_id INTEGER
);

-- Création de la table ChambresReservees si elle n'existe pas
CREATE TABLE IF NOT EXISTS ChambresReservees (
    chambre_reservee_id SERIAL PRIMARY KEY,
    reservation_id INTEGER REFERENCES Reservations(reservation_id),
    chambre_id INTEGER NOT NULL,
    date_creation TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Création de la table Chambres si elle n'existe pas
CREATE TABLE IF NOT EXISTS Chambres (
    chambre_id SERIAL PRIMARY KEY,
    numero VARCHAR(10) UNIQUE NOT NULL,
    type_chambre VARCHAR(50) NOT NULL,
    statut VARCHAR(20) NOT NULL DEFAULT 'active'
);

-- Création de la table PaiementsReservations si elle n'existe pas
CREATE TABLE IF NOT EXISTS PaiementsReservations (
    paiement_id SERIAL PRIMARY KEY,
    reservation_id INTEGER REFERENCES Reservations(reservation_id),
    montant DECIMAL(10,2) NOT NULL,
    mode_paiement VARCHAR(50) NOT NULL,
    reference_paiement VARCHAR(100) NOT NULL,
    date_paiement TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Création de la table HistoriqueReservations si elle n'existe pas
CREATE TABLE IF NOT EXISTS HistoriqueReservations (
    historique_id SERIAL PRIMARY KEY,
    numero_reservation VARCHAR(20) NOT NULL,
    action VARCHAR(50) NOT NULL,
    utilisateur_id INTEGER,
    details JSONB,
    date_action TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Ajout d'index pour optimiser les recherches
CREATE INDEX IF NOT EXISTS idx_reservations_date ON Reservations(date);
CREATE INDEX IF NOT EXISTS idx_reservations_heures ON Reservations(heure_debut, heure_fin);
CREATE INDEX IF NOT EXISTS idx_reservations_statut ON Reservations(statut);
CREATE INDEX IF NOT EXISTS idx_reservations_numero ON Reservations(numero_reservation);
CREATE INDEX IF NOT EXISTS idx_chambres_type ON Chambres(type_chambre);
CREATE INDEX IF NOT EXISTS idx_chambres_statut ON Chambres(statut);
CREATE INDEX IF NOT EXISTS idx_paiements_reservation ON PaiementsReservations(reservation_id);
CREATE INDEX IF NOT EXISTS idx_historique_reservation ON HistoriqueReservations(numero_reservation);

-- Insertion de quelques types de chambres de base
INSERT INTO Chambres (numero, type_chambre, statut)
VALUES 
    ('101', 'STANDARD', 'active'),
    ('102', 'STANDARD', 'active'),
    ('201', 'SUITE', 'active'),
    ('202', 'SUITE', 'active')
ON CONFLICT (numero) DO NOTHING;

-- Ajout des colonnes pour les réservations horaires
ALTER TABLE Reservations
ADD COLUMN date DATE,
ADD COLUMN heure_debut TIME,
ADD COLUMN heure_fin TIME,
ADD COLUMN client_info JSONB;

-- Migration des données existantes
UPDATE Reservations
SET 
  date = date_arrivee,
  heure_debut = '14:00',
  heure_fin = '12:00',
  client_info = jsonb_build_object(
    'client_id', client_id,
    'date_creation', date_creation
  );

-- Suppression des anciennes colonnes
ALTER TABLE Reservations
DROP COLUMN date_arrivee,
DROP COLUMN date_depart,
DROP COLUMN client_id;

-- Ajout d'index pour optimiser les recherches
CREATE INDEX idx_reservations_date ON Reservations(date);
CREATE INDEX idx_reservations_heures ON Reservations(heure_debut, heure_fin);
CREATE INDEX idx_reservations_statut ON Reservations(statut); 