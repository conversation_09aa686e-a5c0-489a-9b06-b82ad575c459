const fs = require('fs');
const path = require('path');
const db = require('../db');
const logger = require('../logger');

/**
 * Script pour exécuter la migration de la Phase 1
 * Plan de Simplification du Système de Permissions
 */

async function runPhase1Migration() {
  try {
    logger.info('🚀 Début de la migration Phase 1 - Simplification des permissions');

    // Lire le fichier de migration SQL
    const migrationPath = path.join(__dirname, '../migrations/phase1-simplify-permissions.sql');
    
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`Fichier de migration non trouvé: ${migrationPath}`);
    }

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    logger.info('📄 Fichier de migration chargé');

    // Exécuter la migration dans une transaction
    await db.query('BEGIN');
    
    try {
      // Exécuter le script SQL
      await db.query(migrationSQL);
      
      // Vérifier que les modifications ont été appliquées
      const verificationResults = await verifyMigration();
      
      if (verificationResults.success) {
        await db.query('COMMIT');
        logger.info('✅ Migration Phase 1 terminée avec succès');
        
        // Afficher les résultats
        console.log('\n📊 Résultats de la migration:');
        console.log(`- Complexes traités: ${verificationResults.complexes_count}`);
        console.log(`- Rôles prédéfinis créés: ${verificationResults.predefined_roles_count}`);
        console.log(`- Champ type_employe ajouté: ${verificationResults.type_employe_column_exists ? '✅' : '❌'}`);
        console.log(`- Contraintes ajoutées: ${verificationResults.constraints_added ? '✅' : '❌'}`);
        console.log(`- Index créés: ${verificationResults.indexes_created ? '✅' : '❌'}`);
        console.log(`- Fonctions utilitaires créées: ${verificationResults.functions_created ? '✅' : '❌'}`);
        
      } else {
        throw new Error('Échec de la vérification de la migration');
      }
      
    } catch (error) {
      await db.query('ROLLBACK');
      throw error;
    }

  } catch (error) {
    logger.error('❌ Erreur lors de la migration Phase 1:', error);
    throw error;
  }
}

async function verifyMigration() {
  try {
    const results = {
      success: false,
      complexes_count: 0,
      predefined_roles_count: 0,
      type_employe_column_exists: false,
      constraints_added: false,
      indexes_created: false,
      functions_created: false
    };

    // Vérifier que la colonne type_employe existe
    const columnCheck = await db.query(`
      SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'Employes' 
        AND column_name = 'type_employe'
      ) as exists
    `);
    results.type_employe_column_exists = columnCheck.rows[0].exists;

    // Compter les complexes
    const complexesCount = await db.query(`
      SELECT COUNT(*) as count FROM "ComplexesHoteliers" WHERE actif = true
    `);
    results.complexes_count = parseInt(complexesCount.rows[0].count);

    // Compter les rôles prédéfinis créés
    const rolesCount = await db.query(`
      SELECT COUNT(*) as count 
      FROM "RolesComplexe" 
      WHERE nom IN ('Employé Réception', 'Gérant Piscine', 'Serveuse', 'Gérant Services', 'Employé Cuisine')
    `);
    results.predefined_roles_count = parseInt(rolesCount.rows[0].count);

    // Vérifier les contraintes
    const constraintCheck = await db.query(`
      SELECT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'check_type_employe'
      ) as exists
    `);
    results.constraints_added = constraintCheck.rows[0].exists;

    // Vérifier les index
    const indexCheck = await db.query(`
      SELECT COUNT(*) as count
      FROM pg_indexes 
      WHERE indexname IN ('idx_employes_type_employe', 'idx_employes_complexe_type')
    `);
    results.indexes_created = parseInt(indexCheck.rows[0].count) >= 2;

    // Vérifier les fonctions
    const functionCheck = await db.query(`
      SELECT COUNT(*) as count
      FROM pg_proc p
      JOIN pg_namespace n ON p.pronamespace = n.oid
      WHERE n.nspname = 'public' 
      AND p.proname IN ('get_default_services_for_type', 'assign_default_services_on_type_change', 'create_predefined_roles_for_complex')
    `);
    results.functions_created = parseInt(functionCheck.rows[0].count) >= 3;

    // Déterminer le succès global
    results.success = results.type_employe_column_exists && 
                     results.constraints_added && 
                     results.indexes_created && 
                     results.functions_created &&
                     results.predefined_roles_count > 0;

    return results;
  } catch (error) {
    logger.error('Erreur lors de la vérification:', error);
    return { success: false };
  }
}

// Fonction pour nettoyer la migration en cas de problème
async function rollbackPhase1Migration() {
  try {
    logger.info('🔄 Début du rollback de la migration Phase 1');

    await db.query('BEGIN');

    try {
      // Supprimer les rôles prédéfinis créés
      await db.query(`
        DELETE FROM "RolesComplexe" 
        WHERE nom IN ('Employé Réception', 'Gérant Piscine', 'Serveuse', 'Gérant Services', 'Employé Cuisine')
      `);

      // Supprimer les fonctions créées
      await db.query('DROP FUNCTION IF EXISTS get_default_services_for_type(VARCHAR)');
      await db.query('DROP FUNCTION IF EXISTS assign_default_services_on_type_change()');
      await db.query('DROP FUNCTION IF EXISTS create_predefined_roles_for_complex(INTEGER)');

      // Supprimer le trigger
      await db.query('DROP TRIGGER IF EXISTS trigger_assign_default_services ON "Employes"');

      // Supprimer les index
      await db.query('DROP INDEX IF EXISTS idx_employes_type_employe');
      await db.query('DROP INDEX IF EXISTS idx_employes_complexe_type');

      // Supprimer la contrainte
      await db.query('ALTER TABLE "Employes" DROP CONSTRAINT IF EXISTS check_type_employe');

      // Supprimer la colonne type_employe (optionnel - commenté pour éviter la perte de données)
      // await db.query('ALTER TABLE "Employes" DROP COLUMN IF EXISTS type_employe');

      await db.query('COMMIT');
      logger.info('✅ Rollback Phase 1 terminé avec succès');

    } catch (error) {
      await db.query('ROLLBACK');
      throw error;
    }

  } catch (error) {
    logger.error('❌ Erreur lors du rollback Phase 1:', error);
    throw error;
  }
}

// Exporter les fonctions pour utilisation externe
module.exports = {
  runPhase1Migration,
  rollbackPhase1Migration,
  verifyMigration
};

// Exécuter la migration si le script est appelé directement
if (require.main === module) {
  const command = process.argv[2];
  
  if (command === 'rollback') {
    rollbackPhase1Migration()
      .then(() => {
        console.log('Rollback terminé');
        process.exit(0);
      })
      .catch((error) => {
        console.error('Erreur lors du rollback:', error);
        process.exit(1);
      });
  } else {
    runPhase1Migration()
      .then(() => {
        console.log('Migration terminée');
        process.exit(0);
      })
      .catch((error) => {
        console.error('Erreur lors de la migration:', error);
        process.exit(1);
      });
  }
}
