#!/usr/bin/env node

/**
 * Script de test complet pour la Phase 5
 * Initialise les permissions puis exécute les tests d'intégration
 */

const { spawn } = require('child_process');
const path = require('path');

/**
 * Exécuter une commande et retourner une promesse
 */
function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`🔧 Exécution: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      cwd: options.cwd || process.cwd(),
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * Fonction principale
 */
async function main() {
  console.log('🚀 Tests Complets Phase 5 - Système d\'Inventaire Excel\n');

  try {
    // Étape 1: Initialiser les permissions de test
    console.log('📋 Étape 1: Initialisation des permissions de test');
    await runCommand('node', [
      path.join(__dirname, 'init-test-permissions.js'),
      'init',
      '1' // complexe_id
    ]);
    console.log('✅ Permissions initialisées\n');

    // Étape 2: Exécuter les tests d'intégration
    console.log('📋 Étape 2: Tests d\'intégration Phase 5');
    await runCommand('node', [
      path.join(__dirname, 'test-phase5-integration.js')
    ]);
    console.log('✅ Tests d\'intégration terminés\n');

    // Étape 3: Tests avec le serveur réel (optionnel)
    console.log('📋 Étape 3: Tests avec serveur réel (optionnel)');
    console.log('💡 Pour tester avec le serveur réel:');
    console.log('   1. Démarrez le serveur: npm run dev');
    console.log('   2. Exécutez: node backend/scripts/test-real-server.js');
    console.log('');

    // Étape 4: Nettoyage (optionnel)
    const args = process.argv.slice(2);
    if (args.includes('--cleanup')) {
      console.log('📋 Étape 4: Nettoyage des données de test');
      await runCommand('node', [
        path.join(__dirname, 'init-test-permissions.js'),
        'cleanup'
      ]);
      console.log('✅ Nettoyage terminé\n');
    } else {
      console.log('💡 Pour nettoyer les données de test: --cleanup');
      console.log('   Exemple: node run-phase5-tests.js --cleanup\n');
    }

    console.log('🎉 Tous les tests de la Phase 5 sont terminés avec succès!');
    console.log('\n📊 Résumé:');
    console.log('  ✅ Permissions configurées');
    console.log('  ✅ Services d\'intégration testés');
    console.log('  ✅ Routes API validées');
    console.log('  ✅ Logique métier vérifiée');
    console.log('  ✅ Performance mesurée');
    
    console.log('\n🚀 Prochaines étapes recommandées:');
    console.log('  1. Tester avec de vraies données de production');
    console.log('  2. Intégrer avec le frontend');
    console.log('  3. Effectuer des tests de charge');
    console.log('  4. Déployer en environnement de test');

  } catch (error) {
    console.error('\n❌ Erreur lors des tests:', error.message);
    console.log('\n🔧 Dépannage:');
    console.log('  1. Vérifiez que la base de données est accessible');
    console.log('  2. Vérifiez que les tables existent (schema.sql)');
    console.log('  3. Vérifiez les permissions de la base de données');
    console.log('  4. Consultez les logs pour plus de détails');
    
    process.exit(1);
  }
}

// Gestion des signaux pour nettoyage
process.on('SIGINT', async () => {
  console.log('\n🛑 Interruption détectée. Nettoyage en cours...');
  try {
    await runCommand('node', [
      path.join(__dirname, 'init-test-permissions.js'),
      'cleanup'
    ]);
    console.log('✅ Nettoyage terminé');
  } catch (error) {
    console.error('❌ Erreur lors du nettoyage:', error.message);
  }
  process.exit(0);
});

// Exécution si appelé directement
if (require.main === module) {
  main();
}

module.exports = { runCommand };
