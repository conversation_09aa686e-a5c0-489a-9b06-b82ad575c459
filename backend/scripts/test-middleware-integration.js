#!/usr/bin/env node

/**
 * Script de test pour vérifier l'intégration des middlewares
 * dans les routes du système d'inventaire Excel
 */

const express = require('express');
const request = require('supertest');
const logger = require('../logger');

// Import des routes
const inventaireRoutes = require('../routes/inventaire.routes');
const recetteRoutes = require('../routes/recette.routes');
const importRoutes = require('../routes/import.routes');
const templateRoutes = require('../routes/template.routes');
const uploadRoutes = require('../routes/upload.routes');

// Mock des middlewares pour les tests
const mockAuth = (req, res, next) => {
  req.user = {
    employe_id: 1,
    role: 'admin_complexe',
    complexe_id: 1
  };
  next();
};

/**
 * Création d'une app de test
 */
function createTestApp() {
  const app = express();
  
  app.use(express.json());
  app.use(mockAuth);
  
  // Routes
  app.use('/api/inventaire', inventaireRoutes);
  app.use('/api/recettes', recetteRoutes);
  app.use('/api/imports', importRoutes);
  app.use('/api/templates', templateRoutes);
  app.use('/api/upload', uploadRoutes);
  
  return app;
}

/**
 * Tests des middlewares
 */
async function testMiddlewareIntegration() {
  const app = createTestApp();
  
  console.log('🧪 Démarrage des tests d\'intégration des middlewares...\n');
  
  try {
    // Test 1: Vérification que les routes sont accessibles
    console.log('📋 Test 1: Accessibilité des routes');
    
    const routes = [
      { method: 'get', path: '/api/inventaire/ingredients', name: 'Liste ingrédients' },
      { method: 'get', path: '/api/recettes/service/1', name: 'Recettes par service' },
      { method: 'get', path: '/api/imports/types', name: 'Types d\'imports' },
      { method: 'get', path: '/api/templates', name: 'Liste templates' },
      { method: 'get', path: '/api/upload/templates/restaurant', name: 'Templates upload' }
    ];
    
    for (const route of routes) {
      try {
        const response = await request(app)[route.method](route.path);
        console.log(`  ✅ ${route.name}: ${response.status}`);
      } catch (error) {
        console.log(`  ❌ ${route.name}: Erreur - ${error.message}`);
      }
    }
    
    // Test 2: Vérification des headers de rate limiting
    console.log('\n🚦 Test 2: Headers de rate limiting');
    
    const response = await request(app).get('/api/inventaire/ingredients');
    const rateLimitHeaders = {
      'X-RateLimit-Limit': response.headers['x-ratelimit-limit'],
      'X-RateLimit-Remaining': response.headers['x-ratelimit-remaining'],
      'X-RateLimit-Reset': response.headers['x-ratelimit-reset']
    };
    
    console.log('  Headers de rate limiting:', rateLimitHeaders);
    
    // Test 3: Vérification du cache
    console.log('\n💾 Test 3: Fonctionnement du cache');
    
    const firstResponse = await request(app).get('/api/inventaire/ingredients');
    const secondResponse = await request(app).get('/api/inventaire/ingredients');
    
    console.log(`  Première requête: ${firstResponse.status}`);
    console.log(`  Seconde requête: ${secondResponse.status}`);
    console.log(`  Cache utilisé: ${secondResponse.body?.cached ? 'Oui' : 'Non'}`);
    
    // Test 4: Test des permissions (simulation)
    console.log('\n🔐 Test 4: Simulation des permissions');
    
    // Simulation d'un utilisateur sans permissions
    const appNoPerms = express();
    appNoPerms.use(express.json());
    appNoPerms.use((req, res, next) => {
      req.user = {
        employe_id: 2,
        role: 'employe_service',
        complexe_id: 1
      };
      next();
    });
    appNoPerms.use('/api/inventaire', inventaireRoutes);
    
    try {
      const restrictedResponse = await request(appNoPerms)
        .post('/api/inventaire/ingredients')
        .send({ nom: 'Test', uniteMesure: 'kg', categorie: 'Test' });
      
      console.log(`  Accès restreint: ${restrictedResponse.status} - ${restrictedResponse.body?.message || 'OK'}`);
    } catch (error) {
      console.log(`  Accès restreint: Erreur - ${error.message}`);
    }
    
    console.log('\n✅ Tests d\'intégration terminés avec succès!');
    
  } catch (error) {
    console.error('\n❌ Erreur lors des tests:', error.message);
    process.exit(1);
  }
}

/**
 * Vérification de la configuration des middlewares
 */
function checkMiddlewareConfiguration() {
  console.log('🔧 Vérification de la configuration des middlewares...\n');
  
  try {
    // Vérification des imports
    const cacheMiddleware = require('../middleware/cache.middleware');
    const inventaireMiddleware = require('../middleware/inventaire.middleware');
    const rateLimitingMiddleware = require('../middleware/rateLimiting.middleware');
    
    console.log('📦 Middlewares importés avec succès:');
    console.log(`  ✅ Cache: ${Object.keys(cacheMiddleware).length} exports`);
    console.log(`  ✅ Inventaire: ${Object.keys(inventaireMiddleware).length} exports`);
    console.log(`  ✅ Rate Limiting: ${Object.keys(rateLimitingMiddleware).length} exports`);
    
    // Vérification des fonctions clés
    const requiredCacheFunctions = ['cacheIngredients', 'cacheRecipes', 'cacheAnalytics', 'autoInvalidateCache'];
    const requiredInventaireFunctions = ['checkInventairePermissions', 'checkImportPermissions', 'logInventaireAction'];
    const requiredRateLimitFunctions = ['ingredientLimiter', 'recipeLimiter', 'importLimiter'];
    
    console.log('\n🔍 Vérification des fonctions requises:');
    
    requiredCacheFunctions.forEach(func => {
      console.log(`  ${cacheMiddleware[func] ? '✅' : '❌'} Cache.${func}`);
    });
    
    requiredInventaireFunctions.forEach(func => {
      console.log(`  ${inventaireMiddleware[func] ? '✅' : '❌'} Inventaire.${func}`);
    });
    
    requiredRateLimitFunctions.forEach(func => {
      console.log(`  ${rateLimitingMiddleware[func] ? '✅' : '❌'} RateLimit.${func}`);
    });
    
    console.log('\n✅ Configuration des middlewares vérifiée!');
    
  } catch (error) {
    console.error('\n❌ Erreur lors de la vérification:', error.message);
    process.exit(1);
  }
}

/**
 * Fonction principale
 */
async function main() {
  console.log('🚀 Test d\'intégration des middlewares - Système d\'inventaire Excel\n');
  
  // Vérification de la configuration
  checkMiddlewareConfiguration();
  
  // Tests d'intégration
  await testMiddlewareIntegration();
  
  console.log('\n🎉 Tous les tests sont terminés!');
}

// Exécution si appelé directement
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Erreur fatale:', error);
    process.exit(1);
  });
}

module.exports = {
  createTestApp,
  testMiddlewareIntegration,
  checkMiddlewareConfiguration
};
