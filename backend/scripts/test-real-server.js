#!/usr/bin/env node

/**
 * Script de test avec le serveur réel
 * Teste les endpoints de la Phase 5 avec un serveur en cours d'exécution
 */

const axios = require('axios');

// Configuration
const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000/api';
const TEST_TOKEN = process.env.TEST_TOKEN || 'your-test-jwt-token-here';

/**
 * Client API configuré
 */
const apiClient = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  },
  timeout: 10000
});

/**
 * Tests des endpoints de services
 */
async function testServiceEndpoints() {
  console.log('🔧 Test des endpoints de services');
  
  const tests = [
    {
      name: 'Mise à jour tarification depuis recettes',
      method: 'POST',
      url: '/services/1/tarification/update-from-recipes',
      data: {}
    },
    {
      name: 'Synchronisation prix avec marge 25%',
      method: 'POST',
      url: '/services/1/tarification/sync-prices',
      data: { target_margin: 25 }
    },
    {
      name: 'Génération depuis import',
      method: 'POST',
      url: '/services/1/tarification/generate-from-import/123',
      data: {}
    }
  ];

  for (const test of tests) {
    try {
      console.log(`  📋 ${test.name}...`);
      
      const response = await apiClient({
        method: test.method.toLowerCase(),
        url: test.url,
        data: test.data
      });
      
      console.log(`    ✅ ${response.status} - ${response.data?.message || 'OK'}`);
      
      if (response.data?.data) {
        const data = response.data.data;
        if (data.prix_mis_a_jour !== undefined) {
          console.log(`    📊 Prix mis à jour: ${data.prix_mis_a_jour}`);
        }
        if (data.produits_ajoutes !== undefined) {
          console.log(`    📊 Produits ajoutés: ${data.produits_ajoutes}`);
        }
      }
      
    } catch (error) {
      const status = error.response?.status || 'ERR';
      const message = error.response?.data?.message || error.message;
      console.log(`    ❌ ${status} - ${message}`);
    }
  }
}

/**
 * Tests des endpoints POS-Stock
 */
async function testPOSStockEndpoints() {
  console.log('\n🛣️ Test des endpoints POS-Stock');
  
  const tests = [
    {
      name: 'Dashboard POS-Stock',
      method: 'GET',
      url: '/pos-stock/dashboard/1'
    },
    {
      name: 'Alertes de stock',
      method: 'GET',
      url: '/pos-stock/alerts/1'
    },
    {
      name: 'Consommation d\'ingrédients',
      method: 'GET',
      url: '/pos-stock/consumption/1?date_debut=2024-01-01&date_fin=2024-12-31'
    },
    {
      name: 'Prévisions de rupture',
      method: 'GET',
      url: '/pos-stock/predictions/1?days=7'
    },
    {
      name: 'Suggestions de commandes',
      method: 'GET',
      url: '/pos-stock/order-suggestions/1?days=14'
    },
    {
      name: 'Vérification disponibilité',
      method: 'POST',
      url: '/pos-stock/check-availability',
      data: {
        produit_id: 1,
        quantite: 2,
        complexe_id: 1
      }
    },
    {
      name: 'Configuration service',
      method: 'GET',
      url: '/pos-stock/configuration/1'
    }
  ];

  for (const test of tests) {
    try {
      console.log(`  📋 ${test.name}...`);
      
      const response = await apiClient({
        method: test.method.toLowerCase(),
        url: test.url,
        data: test.data
      });
      
      console.log(`    ✅ ${response.status} - ${response.data?.message || 'OK'}`);
      
      if (response.data?.data) {
        const data = response.data.data;
        
        // Affichage spécialisé selon le type de réponse
        if (data.alertes) {
          console.log(`    📊 Alertes: ${data.alertes.length || data.total || 0}`);
        }
        if (data.ingredients_a_risque) {
          console.log(`    📊 Ingrédients à risque: ${data.ingredients_a_risque.length}`);
        }
        if (data.ingredients_a_commander) {
          console.log(`    📊 Suggestions commandes: ${data.ingredients_a_commander.length}`);
        }
        if (data.cout_total_estime !== undefined) {
          console.log(`    💰 Coût estimé: ${data.cout_total_estime}FCFA`);
        }
        if (data.consommation) {
          console.log(`    📊 Ingrédients consommés: ${data.total_ingredients || data.consommation.length}`);
        }
      }
      
    } catch (error) {
      const status = error.response?.status || 'ERR';
      const message = error.response?.data?.message || error.message;
      console.log(`    ❌ ${status} - ${message}`);
    }
  }
}

/**
 * Test de performance
 */
async function testPerformance() {
  console.log('\n⚡ Test de performance');
  
  const performanceTests = [
    {
      name: 'Dashboard (temps de réponse)',
      url: '/pos-stock/dashboard/1',
      maxTime: 1000
    },
    {
      name: 'Alertes (temps de réponse)',
      url: '/pos-stock/alerts/1',
      maxTime: 500
    }
  ];

  for (const test of performanceTests) {
    try {
      console.log(`  📋 ${test.name}...`);
      
      const startTime = Date.now();
      const response = await apiClient.get(test.url);
      const responseTime = Date.now() - startTime;
      
      const withinLimit = responseTime <= test.maxTime;
      console.log(`    ${withinLimit ? '✅' : '⚠️'} ${responseTime}ms ${withinLimit ? '' : `(> ${test.maxTime}ms)`}`);
      
    } catch (error) {
      const status = error.response?.status || 'ERR';
      console.log(`    ❌ ${status} - ${error.message}`);
    }
  }
}

/**
 * Test de santé du serveur
 */
async function testServerHealth() {
  console.log('🏥 Test de santé du serveur');
  
  try {
    const response = await apiClient.get('/health');
    console.log(`  ✅ Serveur accessible - ${response.status}`);
    
    if (response.data) {
      console.log(`  📊 Uptime: ${Math.round(response.data.uptime)}s`);
      console.log(`  📊 Environment: ${response.data.environment || 'N/A'}`);
    }
    
    return true;
  } catch (error) {
    console.log(`  ❌ Serveur inaccessible - ${error.message}`);
    console.log(`  💡 Assurez-vous que le serveur est démarré sur ${BASE_URL}`);
    return false;
  }
}

/**
 * Fonction principale
 */
async function main() {
  console.log('🚀 Tests avec Serveur Réel - Phase 5\n');
  console.log(`🔗 URL de base: ${BASE_URL}`);
  console.log(`🔑 Token: ${TEST_TOKEN ? 'Configuré' : 'Non configuré'}\n`);

  try {
    // Test de santé
    const serverHealthy = await testServerHealth();
    if (!serverHealthy) {
      console.log('\n❌ Impossible de continuer sans serveur accessible');
      process.exit(1);
    }

    // Tests des endpoints
    await testServiceEndpoints();
    await testPOSStockEndpoints();
    await testPerformance();

    console.log('\n🎉 Tests avec serveur réel terminés!');
    console.log('\n📊 Résumé:');
    console.log('  ✅ Serveur accessible');
    console.log('  ✅ Endpoints de services testés');
    console.log('  ✅ Endpoints POS-Stock testés');
    console.log('  ✅ Performance mesurée');

  } catch (error) {
    console.error('\n❌ Erreur lors des tests:', error.message);
    console.log('\n🔧 Vérifications:');
    console.log('  1. Le serveur est-il démarré ?');
    console.log('  2. L\'URL de base est-elle correcte ?');
    console.log('  3. Le token JWT est-il valide ?');
    console.log('  4. Les permissions sont-elles configurées ?');
    
    process.exit(1);
  }
}

// Exécution si appelé directement
if (require.main === module) {
  main();
}

module.exports = {
  testServiceEndpoints,
  testPOSStockEndpoints,
  testPerformance,
  testServerHealth
};
