#!/usr/bin/env node

/**
 * Script d'initialisation des permissions de test
 * Crée les rôles et permissions nécessaires pour les tests de la Phase 5
 */

const db = require('../db');
const logger = require('../logger');

/**
 * Permissions d'inventaire requises
 */
const INVENTORY_PERMISSIONS = [
  'view_inventory',
  'manage_inventory',
  'view_recipes',
  'manage_recipes',
  'view_costs',
  'manage_pricing',
  'import_data',
  'operate_pos',
  'view_reports'
];

/**
 * Créer un rôle de test avec toutes les permissions
 */
async function createTestRole(complexeId = 1) {
  try {
    await db.query('BEGIN');

    // Vérifier si le rôle existe déjà
    const existingRole = await db.query(`
      SELECT role_id FROM "RolesComplexe" 
      WHERE complexe_id = $1 AND nom = 'Test Admin Inventaire'
    `, [complexeId]);

    let roleId;

    if (existingRole.rows.length > 0) {
      roleId = existingRole.rows[0].role_id;
      console.log(`✅ Rôle existant trouvé: ${roleId}`);
      
      // Mettre à jour les permissions
      await db.query(`
        UPDATE "RolesComplexe"
        SET permissions = $1
        WHERE role_id = $2
      `, [JSON.stringify(INVENTORY_PERMISSIONS), roleId]);
      
      console.log(`✅ Permissions mises à jour pour le rôle ${roleId}`);
    } else {
      // Créer le nouveau rôle
      const roleResult = await db.query(`
        INSERT INTO "RolesComplexe" (
          complexe_id,
          nom,
          permissions,
          description,
          created_at
        ) VALUES ($1, $2, $3, $4, NOW())
        RETURNING role_id
      `, [
        complexeId,
        'Test Admin Inventaire',
        JSON.stringify(INVENTORY_PERMISSIONS),
        'Rôle de test avec toutes les permissions d\'inventaire'
      ]);

      roleId = roleResult.rows[0].role_id;
      console.log(`✅ Nouveau rôle créé: ${roleId}`);
    }

    await db.query('COMMIT');
    return roleId;

  } catch (error) {
    await db.query('ROLLBACK');
    logger.error('Erreur création rôle de test:', error);
    throw error;
  }
}

/**
 * Créer ou mettre à jour un employé de test
 */
async function createTestEmployee(complexeId = 1, roleId) {
  try {
    await db.query('BEGIN');

    // Vérifier si l'employé de test existe
    const existingEmployee = await db.query(`
      SELECT employe_id FROM "Employes" 
      WHERE complexe_id = $1 AND nom = 'Test' AND prenom = 'Admin'
    `, [complexeId]);

    let employeId;

    if (existingEmployee.rows.length > 0) {
      employeId = existingEmployee.rows[0].employe_id;
      console.log(`✅ Employé existant trouvé: ${employeId}`);
      
      // Mettre à jour le rôle
      await db.query(`
        UPDATE "Employes"
        SET role_id = $1
        WHERE employe_id = $2
      `, [roleId, employeId]);
      
      console.log(`✅ Rôle mis à jour pour l'employé ${employeId}`);
    } else {
      // Créer le nouvel employé
      const employeResult = await db.query(`
        INSERT INTO "Employes" (
          complexe_id,
          nom,
          prenom,
          email,
          telephone,
          mot_de_passe_hash,
          date_embauche,
          role_id,
          actif,
          created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW())
        RETURNING employe_id
      `, [
        complexeId,
        'Test',
        'Admin',
        '<EMAIL>',
        '+33123456789',
        'test-hash-not-used', // mot_de_passe_hash requis
        new Date().toISOString().split('T')[0], // date_embauche (aujourd'hui)
        roleId,
        true // actif
      ]);

      employeId = employeResult.rows[0].employe_id;
      console.log(`✅ Nouvel employé créé: ${employeId}`);
    }

    await db.query('COMMIT');
    return employeId;

  } catch (error) {
    await db.query('ROLLBACK');
    logger.error('Erreur création employé de test:', error);
    throw error;
  }
}

/**
 * Vérifier les permissions d'un employé
 */
async function verifyEmployeePermissions(employeId) {
  try {
    const result = await db.query(`
      SELECT e.nom, e.prenom, rc.nom as role_nom, rc.permissions
      FROM "Employes" e
      LEFT JOIN "RolesComplexe" rc ON e.role_id = rc.role_id
      WHERE e.employe_id = $1
    `, [employeId]);

    if (result.rows.length === 0) {
      console.log(`❌ Employé ${employeId} non trouvé`);
      return false;
    }

    const employe = result.rows[0];
    console.log(`\n📋 Vérification des permissions pour ${employe.prenom} ${employe.nom}:`);
    console.log(`   Rôle: ${employe.role_nom}`);
    
    if (employe.permissions) {
      let permissions = [];
      try {
        // Essayer de parser comme JSON
        if (typeof employe.permissions === 'string') {
          permissions = JSON.parse(employe.permissions);
        } else {
          permissions = employe.permissions;
        }
      } catch (parseError) {
        console.log(`   ❌ Erreur parsing JSON permissions: ${parseError.message}`);
        console.log(`   📋 Contenu brut: ${employe.permissions}`);
        return false;
      }

      if (Array.isArray(permissions)) {
        console.log(`   Permissions (${permissions.length}):`);
        permissions.forEach(perm => {
          console.log(`     ✅ ${perm}`);
        });

        // Vérifier que toutes les permissions requises sont présentes
        const missingPermissions = INVENTORY_PERMISSIONS.filter(perm => !permissions.includes(perm));
        if (missingPermissions.length === 0) {
          console.log(`   ✅ Toutes les permissions requises sont présentes`);
          return true;
        } else {
          console.log(`   ❌ Permissions manquantes: ${missingPermissions.join(', ')}`);
          return false;
        }
      } else {
        console.log(`   ❌ Les permissions ne sont pas un tableau: ${typeof permissions}`);
        return false;
      }
    } else {
      console.log(`   ❌ Aucune permission définie`);
      return false;
    }

  } catch (error) {
    logger.error('Erreur vérification permissions:', error);
    return false;
  }
}

/**
 * Nettoyer les données de test
 */
async function cleanupTestData() {
  try {
    await db.query('BEGIN');

    // Supprimer l'employé de test
    const deleteEmployeeResult = await db.query(`
      DELETE FROM "Employes" 
      WHERE nom = 'Test' AND prenom = 'Admin'
      RETURNING employe_id
    `);

    if (deleteEmployeeResult.rows.length > 0) {
      console.log(`✅ Employé de test supprimé: ${deleteEmployeeResult.rows[0].employe_id}`);
    }

    // Supprimer le rôle de test
    const deleteRoleResult = await db.query(`
      DELETE FROM "RolesComplexe" 
      WHERE nom = 'Test Admin Inventaire'
      RETURNING role_id
    `);

    if (deleteRoleResult.rows.length > 0) {
      console.log(`✅ Rôle de test supprimé: ${deleteRoleResult.rows[0].role_id}`);
    }

    await db.query('COMMIT');
    console.log(`✅ Nettoyage terminé`);

  } catch (error) {
    await db.query('ROLLBACK');
    logger.error('Erreur nettoyage données de test:', error);
    throw error;
  }
}

/**
 * Fonction principale
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'init';
  const complexeId = parseInt(args[1]) || 1;

  console.log('🔧 Initialisation des permissions de test pour la Phase 5\n');

  try {
    switch (command) {
      case 'init':
        console.log(`📋 Initialisation pour le complexe ${complexeId}...`);
        
        const roleId = await createTestRole(complexeId);
        const employeId = await createTestEmployee(complexeId, roleId);
        const isValid = await verifyEmployeePermissions(employeId);
        
        if (isValid) {
          console.log(`\n🎉 Initialisation réussie !`);
          console.log(`   Complexe ID: ${complexeId}`);
          console.log(`   Rôle ID: ${roleId}`);
          console.log(`   Employé ID: ${employeId}`);
          console.log(`\n💡 Utilisez cet employé ID (${employeId}) dans vos tests`);
        } else {
          console.log(`\n❌ Problème lors de l'initialisation`);
          process.exit(1);
        }
        break;

      case 'verify':
        const employeIdToVerify = parseInt(args[2]);
        if (!employeIdToVerify) {
          console.log('❌ Usage: node init-test-permissions.js verify <complexeId> <employeId>');
          process.exit(1);
        }
        
        const isEmployeeValid = await verifyEmployeePermissions(employeIdToVerify);
        console.log(`\n${isEmployeeValid ? '✅' : '❌'} Vérification ${isEmployeeValid ? 'réussie' : 'échouée'}`);
        break;

      case 'cleanup':
        console.log(`🧹 Nettoyage des données de test...`);
        await cleanupTestData();
        break;

      default:
        console.log('❌ Commande inconnue. Utilisez: init, verify, ou cleanup');
        console.log('   Exemples:');
        console.log('     node init-test-permissions.js init 1');
        console.log('     node init-test-permissions.js verify 1 123');
        console.log('     node init-test-permissions.js cleanup');
        process.exit(1);
    }

  } catch (error) {
    console.error('❌ Erreur:', error.message);
    process.exit(1);
  } finally {
    // Note: db.end() n'est pas disponible avec notre configuration de pool
    // La connexion sera fermée automatiquement
  }
}

// Exécution si appelé directement
if (require.main === module) {
  main();
}

module.exports = {
  createTestRole,
  createTestEmployee,
  verifyEmployeePermissions,
  cleanupTestData,
  INVENTORY_PERMISSIONS
};
