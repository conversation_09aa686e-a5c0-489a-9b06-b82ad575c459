#!/usr/bin/env node

/**
 * Script de test pour la Phase 5 : Intégration avec l'Existant
 * Teste l'intégration entre le système d'inventaire et les services/POS existants
 */

const express = require('express');
const request = require('supertest');
const logger = require('../logger');

// Import des services
const ServiceComplexeService = require('../services/service.service');
const POSStockIntegration = require('../services/posStockIntegration.service');

// Import des routes
const serviceRoutes = require('../routes/service.routes');
const posStockRoutes = require('../routes/posStock.routes');

// Mock des middlewares pour les tests
const mockAuth = (req, res, next) => {
  req.user = {
    employe_id: 6, // Employé de test avec toutes les permissions
    role: 'admin_complexe',
    complexe_id: 1
  };
  next();
};

// Mock du middleware de permissions pour les tests
const mockPermissions = (req, res, next) => {
  // Bypass des vérifications de permissions pour les tests
  next();
};

/**
 * Création d'une app de test
 */
function createTestApp() {
  const app = express();
  
  app.use(express.json());
  app.use(mockAuth);
  
  // Routes
  app.use('/api/services', serviceRoutes);
  app.use('/api/pos-stock', posStockRoutes);
  
  return app;
}

/**
 * Tests des services d'intégration
 */
async function testServiceIntegration() {
  console.log('🔧 Test 1: Intégration des Services');
  
  try {
    // Test 1.1: Méthodes du ServiceComplexeService
    console.log('  📋 Test 1.1: Méthodes ServiceComplexeService');
    
    const methods = [
      'updateTarificationFromRecipes',
      'syncPricesWithCosts',
      'generateTarificationFromImport',
      'determineCategorieProduit'
    ];
    
    methods.forEach(method => {
      const exists = typeof ServiceComplexeService[method] === 'function';
      console.log(`    ${exists ? '✅' : '❌'} ${method}: ${exists ? 'Disponible' : 'Manquant'}`);
    });
    
    // Test 1.2: Méthodes du POSStockIntegration
    console.log('  📋 Test 1.2: Méthodes POSStockIntegration');
    
    const posStockMethods = [
      'processTransactionWithStock',
      'checkIngredientAvailability',
      'generateStockAlerts',
      'calculateIngredientConsumption',
      'predictStockBreakdown',
      'suggestOptimalOrders'
    ];
    
    posStockMethods.forEach(method => {
      const exists = typeof POSStockIntegration[method] === 'function';
      console.log(`    ${exists ? '✅' : '❌'} ${method}: ${exists ? 'Disponible' : 'Manquant'}`);
    });
    
    console.log('  ✅ Tests des services terminés\n');
    
  } catch (error) {
    console.error('  ❌ Erreur tests services:', error.message);
  }
}

/**
 * Tests des routes d'intégration
 */
async function testIntegrationRoutes() {
  console.log('🛣️ Test 2: Routes d\'Intégration');
  
  const app = createTestApp();
  
  try {
    // Test 2.1: Routes de tarification des services
    console.log('  📋 Test 2.1: Routes Tarification Services');
    
    const serviceRoutes = [
      { method: 'post', path: '/api/services/1/tarification/update-from-recipes', name: 'Mise à jour depuis recettes' },
      { method: 'post', path: '/api/services/1/tarification/sync-prices', name: 'Synchronisation prix' },
      { method: 'post', path: '/api/services/1/tarification/generate-from-import/123', name: 'Génération depuis import' }
    ];
    
    for (const route of serviceRoutes) {
      try {
        const response = await request(app)[route.method](route.path)
          .send({ target_margin: 30 });
        console.log(`    ${response.status < 500 ? '✅' : '❌'} ${route.name}: ${response.status}`);
      } catch (error) {
        console.log(`    ❌ ${route.name}: Erreur - ${error.message}`);
      }
    }
    
    // Test 2.2: Routes POS-Stock
    console.log('  📋 Test 2.2: Routes POS-Stock');
    
    const posStockRoutes = [
      { method: 'post', path: '/api/pos-stock/process-transaction', name: 'Traitement transaction' },
      { method: 'post', path: '/api/pos-stock/check-availability', name: 'Vérification disponibilité' },
      { method: 'get', path: '/api/pos-stock/alerts/1', name: 'Alertes stock' },
      { method: 'get', path: '/api/pos-stock/dashboard/1', name: 'Dashboard POS-Stock' }
    ];
    
    for (const route of posStockRoutes) {
      try {
        let response;
        if (route.method === 'post') {
          const testData = route.path.includes('process-transaction') 
            ? { transaction_id: 1, lignes_transaction: [{ produit_id: 1, quantite: 2 }] }
            : { produit_id: 1, quantite: 2, complexe_id: 1 };
          response = await request(app)[route.method](route.path).send(testData);
        } else {
          response = await request(app)[route.method](route.path);
        }
        console.log(`    ${response.status < 500 ? '✅' : '❌'} ${route.name}: ${response.status}`);
      } catch (error) {
        console.log(`    ❌ ${route.name}: Erreur - ${error.message}`);
      }
    }
    
    console.log('  ✅ Tests des routes terminés\n');
    
  } catch (error) {
    console.error('  ❌ Erreur tests routes:', error.message);
  }
}

/**
 * Tests de logique métier
 */
async function testBusinessLogic() {
  console.log('💼 Test 3: Logique Métier');
  
  try {
    // Test 3.1: Catégorisation des produits
    console.log('  📋 Test 3.1: Catégorisation des Produits');
    
    const testCases = [
      { service: 'Restaurant', produit: { nom: 'Burger Menu', type_produit: 'menu' }, expected: 'menus' },
      { service: 'Restaurant', produit: { nom: 'Coca Cola', type_produit: 'boisson' }, expected: 'boissons' },
      { service: 'Bar', produit: { nom: 'Mojito', type_produit: 'cocktail' }, expected: 'cocktails' },
      { service: 'Bar', produit: { nom: 'Whisky', type_produit: 'alcool' }, expected: 'alcools' },
      { service: 'Piscine', produit: { nom: 'Entrée Enfant', type_produit: 'enfant' }, expected: 'tarifs_age' }
    ];
    
    testCases.forEach(testCase => {
      try {
        const result = ServiceComplexeService.determineCategorieProduit(testCase.service, testCase.produit);
        const success = result === testCase.expected;
        console.log(`    ${success ? '✅' : '❌'} ${testCase.service} - ${testCase.produit.nom}: ${result} ${success ? '' : `(attendu: ${testCase.expected})`}`);
      } catch (error) {
        console.log(`    ❌ ${testCase.service} - ${testCase.produit.nom}: Erreur - ${error.message}`);
      }
    });
    
    // Test 3.2: Validation des données
    console.log('  📋 Test 3.2: Validation des Données');
    
    const validationTests = [
      { name: 'Marge négative', data: { target_margin: -10 }, shouldFail: true },
      { name: 'Marge > 100%', data: { target_margin: 150 }, shouldFail: true },
      { name: 'Marge valide', data: { target_margin: 30 }, shouldFail: false },
      { name: 'Quantité zéro', data: { quantite: 0 }, shouldFail: true },
      { name: 'Quantité négative', data: { quantite: -5 }, shouldFail: true },
      { name: 'Quantité valide', data: { quantite: 2 }, shouldFail: false }
    ];
    
    validationTests.forEach(test => {
      // Simulation de validation (logique simplifiée)
      let isValid = true;
      if (test.data.target_margin !== undefined) {
        isValid = test.data.target_margin >= 0 && test.data.target_margin <= 100;
      }
      if (test.data.quantite !== undefined) {
        isValid = test.data.quantite > 0;
      }
      
      const result = test.shouldFail ? !isValid : isValid;
      console.log(`    ${result ? '✅' : '❌'} ${test.name}: ${isValid ? 'Valide' : 'Invalide'}`);
    });
    
    console.log('  ✅ Tests de logique métier terminés\n');
    
  } catch (error) {
    console.error('  ❌ Erreur tests logique métier:', error.message);
  }
}

/**
 * Tests de performance
 */
async function testPerformance() {
  console.log('⚡ Test 4: Performance');
  
  try {
    const app = createTestApp();
    
    // Test 4.1: Temps de réponse des endpoints
    console.log('  📋 Test 4.1: Temps de Réponse');
    
    const performanceTests = [
      { method: 'get', path: '/api/pos-stock/dashboard/1', name: 'Dashboard', maxTime: 1000 },
      { method: 'get', path: '/api/pos-stock/alerts/1', name: 'Alertes', maxTime: 500 },
      { method: 'post', path: '/api/pos-stock/check-availability', name: 'Vérification', maxTime: 300 }
    ];
    
    for (const test of performanceTests) {
      try {
        const startTime = Date.now();
        
        let response;
        if (test.method === 'post') {
          response = await request(app)[test.method](test.path)
            .send({ produit_id: 1, quantite: 2, complexe_id: 1 });
        } else {
          response = await request(app)[test.method](test.path);
        }
        
        const responseTime = Date.now() - startTime;
        const withinLimit = responseTime <= test.maxTime;
        
        console.log(`    ${withinLimit ? '✅' : '⚠️'} ${test.name}: ${responseTime}ms ${withinLimit ? '' : `(> ${test.maxTime}ms)`}`);
      } catch (error) {
        console.log(`    ❌ ${test.name}: Erreur - ${error.message}`);
      }
    }
    
    console.log('  ✅ Tests de performance terminés\n');
    
  } catch (error) {
    console.error('  ❌ Erreur tests performance:', error.message);
  }
}

/**
 * Fonction principale
 */
async function main() {
  console.log('🚀 Tests d\'Intégration Phase 5 - Système d\'Inventaire Excel\n');
  
  // Tests séquentiels
  await testServiceIntegration();
  await testIntegrationRoutes();
  await testBusinessLogic();
  await testPerformance();
  
  console.log('🎉 Tous les tests de la Phase 5 sont terminés!');
  console.log('\n📋 Prochaines étapes:');
  console.log('  1. Tester avec de vraies données de la base');
  console.log('  2. Valider les calculs de coûts et marges');
  console.log('  3. Tester l\'intégration complète avec le frontend');
  console.log('  4. Effectuer des tests de charge');
}

// Exécution si appelé directement
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Erreur fatale:', error);
    process.exit(1);
  });
}

module.exports = {
  createTestApp,
  testServiceIntegration,
  testIntegrationRoutes,
  testBusinessLogic,
  testPerformance
};
