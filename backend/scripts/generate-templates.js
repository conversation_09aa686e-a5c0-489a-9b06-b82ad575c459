#!/usr/bin/env node

/**
 * Script pour générer tous les templates Excel
 * Usage: node scripts/generate-templates.js
 */

const path = require('path');
const fs = require('fs');

// Ajouter le répertoire parent au path pour pouvoir importer les services
const parentDir = path.join(__dirname, '..');
process.chdir(parentDir);

const TemplateGeneratorService = require('../services/templateGenerator.service');
const logger = require('../logger');

async function generateTemplates() {
  try {
    console.log('🚀 Génération des templates Excel...\n');

    // Créer le dossier templates s'il n'existe pas
    const templatesDir = path.join(__dirname, '../templates');
    if (!fs.existsSync(templatesDir)) {
      fs.mkdirSync(templatesDir, { recursive: true });
      console.log('📁 Dossier templates créé');
    }

    // Générer tous les templates
    const result = await TemplateGeneratorService.generateAllTemplates();

    if (result.success) {
      console.log('✅ Tous les templates ont été générés avec succès!\n');
      
      console.log('📋 Templates générés:');
      result.data.forEach((template, index) => {
        console.log(`${index + 1}. ${template.filename}`);
        console.log(`   📍 ${template.filepath}\n`);
      });

      console.log('🎯 Les templates sont prêts à être utilisés!');
      console.log('   • Téléchargement via API: /api/template-generator/{type}');
      console.log('   • Fichiers disponibles dans: ./templates/\n');

      // Afficher les informations sur chaque template
      console.log('📖 Informations des templates:');
      console.log('   • restaurant-menu-template.xlsx: Menu restaurant avec exemples');
      console.log('   • bar-menu-template.xlsx: Carte bar avec boissons');
      console.log('   • cuisine-ingredients-template.xlsx: Ingrédients pour cuisine');
      console.log('   • boisson-inventory-template.xlsx: Inventaire boissons complet');

    } else {
      console.error('❌ Erreur lors de la génération des templates');
      process.exit(1);
    }

  } catch (error) {
    console.error('💥 Erreur fatale:', error.message);
    logger.error('Error in generate-templates script:', error);
    process.exit(1);
  }
}

// Exécuter le script
if (require.main === module) {
  generateTemplates();
}

module.exports = { generateTemplates };
