const bcrypt = require('bcryptjs');
const db = require('../db');
const logger = require('../logger');

async function initializeData() {
  try {
    // Début de la transaction
    await db.query('BEGIN');

    // 1. <PERSON><PERSON><PERSON> le super admin
    const superAdminPassword = await bcrypt.hash('admin123', 10);
    const superAdminQuery = `
      INSERT INTO "UtilisateursSuperAdmin" (
        email, mot_de_passe_hash, nom, prenom, actif
      ) VALUES (
        $1, $2, $3, $4, $5
      ) RETURNING superadmin_id
    `;
    const superAdminResult = await db.query(superAdminQuery, [
      '<EMAIL>',
      superAdminPassword,
      'Admin',
      'Super',
      true
    ]);
    const superAdminId = superAdminResult.rows[0].superadmin_id;
    logger.info('Super admin créé avec succès');

    // 2. <PERSON><PERSON>er la chaîne hôtelière
    const chaineQuery = `
      INSERT INTO "ChainesHotelieres" (
        nom, slug, description, email_contact, telephone_contact,
        adresse_siege, pays_origine, superadmin_createur_id
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8
      ) RETURNING chaine_id
    `;
    const chaineResult = await db.query(chaineQuery, [
      'Cacaveli Hotels',
      'cacaveli-hotels',
      'Chaîne hôtelière de luxe en Afrique',
      '<EMAIL>',
      '+225 0700000000',
      'Abidjan, Côte d\'Ivoire',
      'Côte d\'Ivoire',
      superAdminId
    ]);
    const chaineId = chaineResult.rows[0].chaine_id;
    logger.info('Chaîne hôtelière créée avec succès');

    // 3. Créer l'admin de la chaîne
    const adminChainePassword = await bcrypt.hash('patron123', 10);
    const adminChaineQuery = `
      INSERT INTO "AdminsChaine" (
        chaine_id, email, mot_de_passe_hash, nom, prenom, telephone, actif
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7
      ) RETURNING admin_chaine_id
    `;
    const adminChaineResult = await db.query(adminChaineQuery, [
      chaineId,
      '<EMAIL>',
      adminChainePassword,
      'Patron',
      'Cacaveli',
      '+225 0700000001',
      true
    ]);
    logger.info('Admin de chaîne créé avec succès');

    // 4. Créer les trois complexes hôteliers
    const complexes = [
      {
        nom: 'Cacaveli Resort Abidjan',
        slug: 'cacaveli-resort-abidjan',
        type: 'Hôtel',
        ville: 'Abidjan',
        pays: 'Côte d\'Ivoire'
      },
      {
        nom: 'Cacaveli Beach Grand-Bassam',
        slug: 'cacaveli-beach-grand-bassam',
        type: 'Hôtel',
        ville: 'Grand-Bassam',
        pays: 'Côte d\'Ivoire'
      },
      {
        nom: 'Cacaveli Lodge Yamoussoukro',
        slug: 'cacaveli-lodge-yamoussoukro',
        type: 'Hôtel',
        ville: 'Yamoussoukro',
        pays: 'Côte d\'Ivoire'
      }
    ];

    for (const complexe of complexes) {
      // Créer le complexe
      const complexeQuery = `
        INSERT INTO "ComplexesHoteliers" (
          chaine_id, nom, slug, type_etablissement,
          ville, pays, actif
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7
        ) RETURNING complexe_id
      `;
      const complexeResult = await db.query(complexeQuery, [
        chaineId,
        complexe.nom,
        complexe.slug,
        complexe.type,
        complexe.ville,
        complexe.pays,
        true
      ]);
      const complexeId = complexeResult.rows[0].complexe_id;

      // Créer l'admin du complexe
      const adminComplexePassword = await bcrypt.hash('admin123', 10);
      const adminComplexeQuery = `
        INSERT INTO "AdminsComplexe" (
          complexe_id, email, mot_de_passe_hash, nom, prenom, telephone, actif
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7
        )
      `;
      await db.query(adminComplexeQuery, [
        complexeId,
        `admin.${complexe.slug}@cacaveli.com`,
        adminComplexePassword,
        'Admin',
        'Complexe',
        '+225 0700000002',
        true
      ]);

      logger.info(`Complexe ${complexe.nom} et son admin créés avec succès`);
    }

    // Valider la transaction
    await db.query('COMMIT');
    logger.info('Initialisation des données terminée avec succès');

  } catch (error) {
    // En cas d'erreur, annuler la transaction
    await db.query('ROLLBACK');
    logger.error('Erreur lors de l\'initialisation des données:', error);
    throw error;
  }
}

// Exécuter le script
initializeData()
  .then(() => {
    logger.info('Script d\'initialisation terminé avec succès');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Erreur lors de l\'exécution du script:', error);
    process.exit(1);
  }); 