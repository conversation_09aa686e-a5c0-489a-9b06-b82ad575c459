// eslint-disable-next-line import/no-extraneous-dependencies
const bcrypt = require('bcryptjs');
const { Pool } = require('pg');
const config = require('../config');

const pool = new Pool(config.db);

async function createSuperAdmin() {
  const client = await pool.connect();

  try {
    // Start transaction
    await client.query('BEGIN');

    // Check if super admin already exists
    const checkQuery = 'SELECT * FROM "UtilisateursSuperAdmin" WHERE email = $1';
    const checkResult = await client.query(checkQuery, ['<EMAIL>']);

    if (checkResult.rows.length > 0) {
      console.log('Super admin already exists. Skipping creation.');
      return;
    }

    // Create super admin user
    const email = '<EMAIL>';
    const password = 'Admin@123'; // Default password
    const hashedPassword = await bcrypt.hash(password, 10);

    const insertQuery = `
      INSERT INTO "UtilisateursSuperAdmin" (
        email, 
        mot_de_passe_hash, 
        nom, 
        prenom, 
        actif, 
        created_at, 
        updated_at
      )
      VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING superadmin_id, email;
    `;

    const values = [
      email,
      hashedPassword,
      'Admin',
      'Super',
      true,
    ];

    const result = await client.query(insertQuery, values);

    // Commit transaction
    await client.query('COMMIT');

    console.log('Super admin created successfully:');
    console.log('Email:', result.rows[0].email);
    console.log('ID:', result.rows[0].superadmin_id);
    console.log('Default password:', password);
    console.log('\nPlease change the default password after first login!');
  } catch (error) {
    // Rollback transaction on error
    await client.query('ROLLBACK');
    console.error('Error creating super admin:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Run the script
createSuperAdmin()
  .then(() => {
    console.log('Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
