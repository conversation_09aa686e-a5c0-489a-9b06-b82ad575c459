require('dotenv').config();
const path = require('path');

const config = {
  ROOT_DIR: __dirname,
  URL_PORT: process.env.PORT || 8080,
  URL_PATH: 'http://localhost',
  BASE_VERSION: '',
  CONTROLLER_DIRECTORY: path.join(__dirname, 'controllers'),
  PROJECT_DIR: __dirname,
  NODE_ENV: process.env.NODE_ENV || 'development',
  LOG_LEVEL: process.env.LOG_LEVEL || 'debug',
  UPLOAD_PATH: process.env.UPLOAD_PATH || path.join(__dirname, 'uploaded_files'),
  MAX_FILE_SIZE: process.env.MAX_FILE_SIZE || '14MB',
  CORS_ORIGIN: process.env.CORS_ORIGIN || 'http://localhost:5173',
};

config.OPENAPI_YAML = path.join(config.ROOT_DIR, 'api', 'openapi.yaml');
config.FULL_PATH = `${config.URL_PATH}:${config.URL_PORT}/${config.BASE_VERSION}`;
config.FILE_UPLOAD_PATH = config.UPLOAD_PATH;

module.exports = config;
