# Plan de Test Complet - Application de Gestion Hôtelière

## 📋 Vue d'ensemble

Ce plan de test est conçu pour un utilisateur découvrant l'application pour la première fois. Il couvre tous les types d'utilisateurs et fonctionnalités principales dans un ordre chronologique logique.

## 🔧 Phase 1 : Configuration Initiale

### 1.1 Vérification de l'environnement
- [ ] Vérifier que les serveurs backend et frontend sont démarrés
- [ ] Vérifier l'accès aux bases de données
- [ ] Confirmer que les ports sont disponibles (3000 pour frontend, 5000 pour backend)

### 1.2 Données de test initiales
- [ ] Exécuter le script d'initialisation : `node backend/scripts/init-data.js`
- [ ] Vérifier la création des données de base :
  - Super Admin : `<EMAIL>` / `SuperAdmin123!`
  - Chaîne hôtelière : "Cacaveli Hotels"
  - Complexe de test : "Cacaveli Resort Abidjan"
  - Admin de chaîne : `<EMAIL>` / `AdminChaine123!`
  - Admin de complexe : `<EMAIL>` / `AdminComplexe123!`

## 🏢 Phase 2 : Test Super Admin

### 2.1 Connexion Super Admin
1. Aller sur `http://localhost:3000/super-admin`
2. Se connecter avec : `<EMAIL>` / `SuperAdmin123!`
3. Vérifier l'accès au dashboard super admin

### 2.2 Gestion des chaînes hôtelières
- [ ] Consulter la liste des chaînes existantes
- [ ] Créer une nouvelle chaîne de test
- [ ] Modifier les informations d'une chaîne
- [ ] Vérifier les statistiques globales

### 2.3 Gestion des complexes
- [ ] Consulter la liste des complexes par chaîne
- [ ] Créer un nouveau complexe
- [ ] Modifier les informations d'un complexe
- [ ] Désactiver/réactiver un complexe

## 🏨 Phase 3 : Test Admin de Chaîne

### 3.1 Connexion Admin de Chaîne
1. Aller sur `http://localhost:3000/login`
2. Sélectionner "Admin Chaîne"
3. Se connecter avec : `<EMAIL>` / `AdminChaine123!`
4. Sélectionner le complexe "Cacaveli Resort Abidjan"

### 3.2 Vue d'ensemble des complexes
- [ ] Consulter le dashboard avec les statistiques de tous les complexes
- [ ] Naviguer entre les différents complexes de la chaîne
- [ ] Consulter les rapports consolidés

### 3.3 Gestion des employés (niveau chaîne)
- [ ] Accéder à la gestion des employés
- [ ] Créer des employés de test pour différents services :
  - Employé réception : `<EMAIL>` / `Reception123!`
  - Gérant piscine : `<EMAIL>` / `Piscine123!`
  - Serveuse bar/restaurant : `<EMAIL>` / `Serveuse123!`
  - Gérant services : `<EMAIL>` / `Gerant123!`
  - Employé cuisine : `<EMAIL>` / `Cuisine123!`

## 🏢 Phase 4 : Test Admin de Complexe

### 4.1 Connexion Admin de Complexe
1. Se déconnecter et retourner sur `http://localhost:3000/login`
2. Sélectionner "Admin Complexe"
3. Se connecter avec : `<EMAIL>` / `AdminComplexe123!`

### 4.2 Configuration des services du complexe
- [ ] Accéder à la page Services
- [ ] Configurer les services disponibles :
  - Restaurant (avec tarification)
  - Bar (avec tarification)
  - Piscine (avec tarification horaire/personne)
- [ ] Uploader les menus Excel pour restaurant/bar
- [ ] Configurer les horaires d'ouverture

### 4.3 Gestion des chambres
- [ ] Accéder à la page Chambres
- [ ] Créer différents types de chambres :
  - Chambre Standard (2 personnes) - 50FCFA/nuit
  - Chambre Deluxe (4 personnes) - 80FCFA/nuit
  - Suite (6 personnes) - 120FCFA/nuit
- [ ] Configurer les caractéristiques et équipements
- [ ] Vérifier les statuts des chambres

### 4.4 Configuration des points de vente (POS)
- [ ] Accéder à la page POS Management
- [ ] Créer des caisses pour chaque service :
  - Caisse Restaurant Principal
  - Caisse Bar Terrasse
  - Caisse Piscine
- [ ] Attribuer les caisses aux employés créés
- [ ] Configurer les fonds de caisse initiaux

## 🎯 Phase 5 : Test Employé Réception

### 5.1 Connexion Employé Réception
1. Se déconnecter et retourner sur `http://localhost:3000/login`
2. Sélectionner "Employé"
3. Se connecter avec : `<EMAIL>` / `Reception123!`
4. Vérifier la redirection automatique vers l'interface réception

### 5.2 Gestion des réservations
- [ ] Consulter le calendrier des réservations
- [ ] Créer une nouvelle réservation client :
  - Client : Jean Dupont, <EMAIL>, +33123456789
  - Dates : Aujourd'hui + 2 jours pour 3 nuits
  - Chambre Deluxe
- [ ] Créer une réservation anonyme avec code d'accès
- [ ] Consulter les réservations en attente

### 5.3 Gestion des clients
- [ ] Consulter la liste des clients
- [ ] Créer un nouveau client
- [ ] Modifier les informations d'un client existant
- [ ] Consulter l'historique des réservations d'un client

### 5.4 Validation et paiement des réservations
- [ ] Valider une réservation en attente
- [ ] Effectuer le paiement (espèces/carte)
- [ ] Imprimer le ticket de caisse avec QR code
- [ ] Vérifier la mise à jour du statut

## 🏊 Phase 6 : Test Employé Piscine

### 6.1 Connexion Gérant Piscine
1. Se déconnecter et se connecter avec : `<EMAIL>` / `Piscine123!`
2. Vérifier la redirection vers l'interface piscine

### 6.2 Billetterie piscine
- [ ] Ouvrir une session de caisse
- [ ] Créer un ticket piscine :
  - Client : Marie Martin
  - 2 personnes pour 3 heures
  - Calculer le prix automatiquement
- [ ] Effectuer le paiement
- [ ] Imprimer le ticket avec QR code d'accès
- [ ] Vérifier le ticket dans la liste des tickets actifs

### 6.3 Contrôle d'accès
- [ ] Scanner un QR code de ticket pour vérification
- [ ] Marquer une sortie/re-entrée
- [ ] Consulter les statistiques de fréquentation

## 🍽️ Phase 7 : Test Employé Restaurant/Bar

### 7.1 Connexion Serveuse
1. Se déconnecter et se connecter avec : `<EMAIL>` / `Serveuse123!`
2. Vérifier l'accès aux interfaces Restaurant et Bar

### 7.2 Service Restaurant
- [ ] Ouvrir une session de caisse restaurant
- [ ] Consulter la carte des menus
- [ ] Gérer les tables (statuts libre/occupé/réservé)
- [ ] Prendre une commande :
  - Table 5 : 2 menus du jour, 1 bouteille de vin
  - Envoyer en cuisine
- [ ] Effectuer l'encaissement
- [ ] Imprimer le ticket de caisse

### 7.3 Service Bar
- [ ] Basculer vers l'interface Bar
- [ ] Consulter la carte des boissons
- [ ] Prendre une commande bar :
  - 3 bières, 2 cocktails
- [ ] Effectuer l'encaissement immédiat
- [ ] Vérifier la mise à jour automatique du stock

## 👨‍🍳 Phase 8 : Test Employé Cuisine

### 8.1 Connexion Cuisine
1. Se déconnecter et se connecter avec : `<EMAIL>` / `Cuisine123!`
2. Vérifier l'accès à l'interface cuisine

### 8.2 Gestion des commandes
- [ ] Consulter les commandes en attente
- [ ] Marquer une commande "en préparation"
- [ ] Marquer une commande "prête"
- [ ] Consulter l'historique des commandes du jour

## 📊 Phase 9 : Test Rapports et Inventaire

### 9.1 Retour Admin de Complexe
1. Se reconnecter en tant qu'admin de complexe
2. Accéder aux fonctionnalités avancées

### 9.2 Gestion de l'inventaire
- [ ] Accéder à la page Inventaire
- [ ] Uploader un fichier Excel d'inventaire
- [ ] Consulter les stocks actuels
- [ ] Créer un mouvement de stock manuel
- [ ] Générer un rapport d'inventaire

### 9.3 Rapports financiers
- [ ] Accéder à la page Rapports
- [ ] Générer un rapport de ventes par service
- [ ] Consulter les statistiques de réservations
- [ ] Exporter un rapport en PDF/Excel
- [ ] Consulter les rapports de sessions de caisse

## 🔄 Phase 10 : Tests de Workflow Complets

### 10.1 Workflow réservation complète
1. **Client** : Faire une demande de réservation anonyme
2. **Réception** : Valider et encaisser la réservation
3. **Client** : Arriver à l'hôtel avec le QR code
4. **Réception** : Scanner le QR code et confirmer l'arrivée

### 10.2 Workflow service restaurant
1. **Client** : Arriver au restaurant
2. **Serveuse** : Attribuer une table et prendre la commande
3. **Cuisine** : Préparer la commande
4. **Serveuse** : Servir et encaisser

### 10.3 Workflow piscine
1. **Client** : Acheter un ticket piscine
2. **Piscine** : Contrôler l'accès avec QR code
3. **Client** : Sortir et re-entrer (vérification)
4. **Piscine** : Clôturer la session

## ✅ Phase 11 : Vérifications Finales

### 11.1 Cohérence des données
- [ ] Vérifier que tous les paiements sont enregistrés
- [ ] Contrôler les stocks après les ventes
- [ ] Vérifier les sessions de caisse fermées
- [ ] Contrôler les QR codes générés

### 11.2 Rapports de synthèse
- [ ] Générer un rapport global de la journée de test
- [ ] Vérifier les totaux par service
- [ ] Contrôler les mouvements de caisse
- [ ] Exporter les données de test

## 🚨 Points d'attention

### Erreurs courantes à surveiller
- Problèmes de permissions entre types d'utilisateurs
- QR codes non générés ou illisibles
- Calculs de prix incorrects
- Sessions de caisse non fermées
- Stocks non mis à jour

### Performance
- Temps de réponse des pages
- Fluidité des transitions entre interfaces
- Rapidité des calculs de disponibilité

### Sécurité
- Vérification des accès par rôle
- Protection des données sensibles
- Validation des inputs utilisateur

---

**Durée estimée du test complet : 4-6 heures**

**Prérequis : Environnement de développement configuré avec données de test**
