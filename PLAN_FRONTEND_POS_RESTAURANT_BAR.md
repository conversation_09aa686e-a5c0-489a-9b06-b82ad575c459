# Plan d'Implémentation Frontend - Système POS Restaurant/Bar

## 🎯 **Objectif**
Créer une interface POS moderne et intuitive pour les restaurants et bars avec gestion des tables, menus, commandes et paiements en temps réel.

## 📋 **Analyse de l'Existant**

### ✅ **Services Frontend Disponibles**
- `serviceComplexeService` - Gestion des services Restaurant/Bar
- `posService` - Gestion des points de vente et sessions
- `sessionCaisseService` - Sessions de caisse
- `recetteService` - Recettes et ingrédients
- `inventaireService` - Gestion des stocks

### ✅ **Composants Existants à Réutiliser**
- `QuickLinks` - Navigation rapide
- `ServiceModal` - Modals de services
- `TarificationManager` - Gestion des tarifs
- Système d'authentification et permissions

## 🏗️ **Architecture Frontend à Implémenter**

### **Phase 1: Services Frontend**

#### **1.1 TableService** (`hotel-frontend/src/services/table.service.ts`)
```typescript
export interface Table {
  table_id: number;
  service_id: number;
  numero: string;
  capacite: number;
  zone: string;
  statut: 'Libre' | 'Occupée' | 'Réservée' | 'Maintenance';
  position_x?: number;
  position_y?: number;
  commande_active?: Commande;
}

export interface TableReservation {
  reservation_id: number;
  table_id: number;
  client_id?: number;
  date_debut: string;
  date_fin: string;
  nb_personnes: number;
  statut: string;
}

class TableService {
  async getTablesByService(serviceId: number): Promise<Table[]>
  async updateTableStatus(tableId: number, statut: string): Promise<void>
  async createTableReservation(data: CreateTableReservationData): Promise<TableReservation>
  async getTableLayout(serviceId: number): Promise<Table[]>
}
```

#### **1.2 MenuService** (`hotel-frontend/src/services/menu.service.ts`)
```typescript
export interface MenuItem {
  produit_id: number;
  nom: string;
  description?: string;
  prix_vente: number;
  categorie: string;
  image_url?: string;
  stock_disponible: number;
  ingredients?: RecetteIngredient[];
  allergenes?: string[];
}

export interface MenuCategory {
  categorie_id: number;
  nom: string;
  description?: string;
  items: MenuItem[];
}

class MenuService {
  async getMenuByService(serviceId: number): Promise<MenuCategory[]>
  async getMenuWithAvailability(serviceId: number): Promise<MenuCategory[]>
  async checkItemAvailability(produitId: number, quantite: number): Promise<boolean>
}
```

#### **1.3 CommandeService** (`hotel-frontend/src/services/commande.service.ts`)
```typescript
export interface CommandeItem {
  produit_id: number;
  nom: string;
  quantite: number;
  prix_unitaire: number;
  montant_ligne: number;
  commentaires?: string;
  statut: 'En attente' | 'En préparation' | 'Prêt' | 'Servi';
}

export interface Commande {
  commande_id: number;
  service_id: number;
  table_id?: number;
  employe_id: number;
  date_commande: string;
  statut: 'En cours' | 'Servie' | 'Payée' | 'Annulée';
  montant_total: number;
  items: CommandeItem[];
  notes?: string;
}

class CommandeService {
  async createCommande(data: CreateCommandeData): Promise<Commande>
  async addItemToCommande(commandeId: number, item: CommandeItem): Promise<void>
  async updateCommandeStatus(commandeId: number, statut: string): Promise<void>
  async getCommandesByService(serviceId: number): Promise<Commande[]>
  async processPayment(commandeId: number, paymentData: PaymentData): Promise<void>
}
```

### **Phase 2: Composants UI Principaux**

#### **2.1 Sélecteur de Service** (`hotel-frontend/src/components/pos/ServiceSelector.tsx`)
```typescript
interface ServiceSelectorProps {
  services: ServiceComplexe[];
  selectedService: ServiceComplexe | null;
  onServiceChange: (service: ServiceComplexe) => void;
}

export const ServiceSelector: React.FC<ServiceSelectorProps> = ({
  services, selectedService, onServiceChange
}) => {
  // Interface pour sélectionner Restaurant ou Bar
  // Affichage des services disponibles avec icônes
  // Indication du statut (ouvert/fermé, session active)
}
```

#### **2.2 Gestionnaire de Tables** (`hotel-frontend/src/components/pos/TableManager.tsx`)
```typescript
interface TableManagerProps {
  serviceId: number;
  tables: Table[];
  onTableSelect: (table: Table) => void;
  selectedTable?: Table;
}

export const TableManager: React.FC<TableManagerProps> = ({
  serviceId, tables, onTableSelect, selectedTable
}) => {
  // Grille des tables avec statuts visuels
  // Codes couleur: Libre (vert), Occupée (rouge), Réservée (orange)
  // Informations: numéro, capacité, temps d'occupation
  // Actions: sélectionner, réserver, libérer
}
```

#### **2.3 Menu Interactif** (`hotel-frontend/src/components/pos/MenuDisplay.tsx`)
```typescript
interface MenuDisplayProps {
  serviceId: number;
  categories: MenuCategory[];
  onItemAdd: (item: MenuItem, quantity: number) => void;
  searchTerm?: string;
}

export const MenuDisplay: React.FC<MenuDisplayProps> = ({
  serviceId, categories, onItemAdd, searchTerm
}) => {
  // Navigation par catégories (Entrées, Plats, Desserts, Boissons)
  // Grille de produits avec images, prix, disponibilité
  // Recherche rapide par nom
  // Indicateurs de stock (disponible/rupture)
  // Boutons d'ajout rapide avec quantité
}
```

#### **2.4 Panier de Commande** (`hotel-frontend/src/components/pos/OrderCart.tsx`)
```typescript
interface OrderCartProps {
  commande: Commande | null;
  onItemUpdate: (itemId: number, quantity: number) => void;
  onItemRemove: (itemId: number) => void;
  onOrderValidate: () => void;
  onPayment: () => void;
}

export const OrderCart: React.FC<OrderCartProps> = ({
  commande, onItemUpdate, onItemRemove, onOrderValidate, onPayment
}) => {
  // Liste des items avec quantités et prix
  // Boutons +/- pour modifier quantités
  // Calcul automatique du total
  // Zone de notes/commentaires
  // Boutons: Valider commande, Paiement, Annuler
}
```

### **Phase 3: Interface POS Principale**

#### **3.1 Composant POS Principal** (`hotel-frontend/src/components/POS.tsx`)
```typescript
interface POSState {
  selectedService: ServiceComplexe | null;
  selectedTable: Table | null;
  currentCommande: Commande | null;
  menuCategories: MenuCategory[];
  tables: Table[];
  sessionActive: SessionCaisse | null;
  loading: boolean;
}

export const POS: React.FC<POSProps> = ({ onClose }) => {
  // État global du POS
  const [state, setState] = useState<POSState>(initialState);
  
  // Layout responsive en 4 zones:
  // 1. Header: Service + Session + Actions
  // 2. Left: Tables (30%)
  // 3. Center: Menu (40%) 
  // 4. Right: Commande (30%)
}
```

#### **3.2 Layout Responsive**
```typescript
// Structure de l'interface POS
<div className="pos-container h-screen flex flex-col">
  {/* Header */}
  <POSHeader 
    selectedService={selectedService}
    sessionActive={sessionActive}
    onServiceChange={handleServiceChange}
  />
  
  {/* Main Content */}
  <div className="flex-1 flex">
    {/* Tables */}
    <div className="w-1/3 border-r">
      <TableManager 
        serviceId={selectedService?.service_id}
        tables={tables}
        onTableSelect={handleTableSelect}
        selectedTable={selectedTable}
      />
    </div>
    
    {/* Menu */}
    <div className="w-2/5 border-r">
      <MenuDisplay 
        serviceId={selectedService?.service_id}
        categories={menuCategories}
        onItemAdd={handleItemAdd}
      />
    </div>
    
    {/* Commande */}
    <div className="w-1/3">
      <OrderCart 
        commande={currentCommande}
        onItemUpdate={handleItemUpdate}
        onPayment={handlePayment}
      />
    </div>
  </div>
</div>
```

### **Phase 4: Fonctionnalités Avancées**

#### **4.1 Gestion des Sessions** (`hotel-frontend/src/components/pos/SessionManager.tsx`)
```typescript
export const SessionManager: React.FC = () => {
  // Modal d'ouverture/fermeture de session
  // Saisie des fonds d'ouverture
  // Récapitulatif des ventes
  // Calcul des écarts de caisse
}
```

#### **4.2 Système de Paiement** (`hotel-frontend/src/components/pos/PaymentModal.tsx`)
```typescript
export const PaymentModal: React.FC<PaymentModalProps> = ({
  commande, onPaymentComplete, onClose
}) => {
  // Récapitulatif de la commande
  // Sélection du mode de paiement (Espèces, Carte)
  // Calcul de la monnaie à rendre
  // Génération du ticket de caisse
  // Impression automatique
}
```

#### **4.3 Notifications Temps Réel** (`hotel-frontend/src/components/pos/NotificationCenter.tsx`)
```typescript
export const NotificationCenter: React.FC = () => {
  // Notifications de cuisine (commandes prêtes)
  // Alertes de stock faible
  // Réservations de tables
  // Messages entre services
}
```

### **Phase 5: Intégration et Optimisations**

#### **5.1 Hooks Personnalisés**
```typescript
// Gestion de l'état POS
export const usePOSState = (serviceId: number) => {
  // État centralisé du POS
  // Synchronisation temps réel
  // Gestion des erreurs
}

// Gestion des commandes
export const useCommande = (tableId?: number) => {
  // CRUD commandes
  // Validation automatique
  // Intégration stock
}

// Gestion des tables
export const useTables = (serviceId: number) => {
  // État des tables en temps réel
  // Réservations
  // Historique d'occupation
}
```

#### **5.2 Optimisations Performance**
```typescript
// Mise en cache des menus
const menuCache = useMemo(() => 
  menuCategories.reduce((acc, cat) => ({
    ...acc,
    [cat.categorie_id]: cat.items
  }), {})
, [menuCategories]);

// Debounce pour la recherche
const debouncedSearch = useDebounce(searchTerm, 300);

// Virtualisation pour les grandes listes
import { FixedSizeList as List } from 'react-window';
```

### **Phase 6: Tests et Validation**

#### **6.1 Tests Unitaires**
- Services (table, menu, commande)
- Composants UI
- Hooks personnalisés
- Calculs de prix et totaux

#### **6.2 Tests d'Intégration**
- Workflow complet de commande
- Intégration avec l'inventaire
- Gestion des sessions de caisse
- Synchronisation temps réel

## 🎨 **Design System**

### **Codes Couleur**
- **Tables Libres**: `bg-green-100 text-green-700`
- **Tables Occupées**: `bg-red-100 text-red-700`
- **Tables Réservées**: `bg-orange-100 text-orange-700`
- **Items Disponibles**: `bg-white border-green-200`
- **Items Rupture**: `bg-gray-100 text-gray-400`

### **Icônes Lucide React**
- `UtensilsCrossed` - Restaurant
- `Beer` - Bar
- `Users` - Tables/Capacité
- `ShoppingCart` - Commandes
- `CreditCard` - Paiements
- `Clock` - Temps de préparation
- `AlertTriangle` - Alertes stock

## 🚀 **Ordre d'Implémentation**

1. **✅ Services de base** (table, menu, commande) - **EN COURS**
2. **Composants UI principaux** (sélecteurs, gestionnaires)
3. **Interface POS complète** (layout et intégration)
4. **Fonctionnalités avancées** (paiements, notifications)
5. **Optimisations** (performance, UX)
6. **Tests et validation** (unitaires, intégration)

## 🔧 **Détails Techniques d'Implémentation**

### **Intégration avec l'Architecture Existante**

#### **Extension des Services Existants**
```typescript
// Extension du serviceComplexeService
export const posRestaurantService = {
  ...serviceComplexeService,

  async getRestaurantServices(): Promise<ServiceComplexe[]> {
    return serviceComplexeService.getServicesByType('Restaurant');
  },

  async getBarServices(): Promise<ServiceComplexe[]> {
    return serviceComplexeService.getServicesByType('Bar');
  },

  async getServiceWithMenuAndTables(serviceId: number): Promise<ServiceWithDetails> {
    const [service, menu, tables] = await Promise.all([
      serviceComplexeService.getServiceById(serviceId),
      menuService.getMenuByService(serviceId),
      tableService.getTablesByService(serviceId)
    ]);

    return { service, menu, tables };
  }
};
```

#### **Types TypeScript Étendus**
```typescript
// Extension des types existants
export interface ServiceWithPOSConfig extends ServiceComplexe {
  pos_configuration: {
    table_management: boolean;
    kitchen_display: boolean;
    auto_print_kitchen: boolean;
    split_bills: boolean;
    table_service: boolean;
    takeaway: boolean;
    delivery: boolean;
  };
  active_session?: SessionCaisse;
  tables_count?: number;
  current_orders?: number;
}

export interface POSWorkspace {
  service: ServiceWithPOSConfig;
  tables: Table[];
  menu: MenuCategory[];
  activeCommandes: Commande[];
  session: SessionCaisse | null;
  notifications: POSNotification[];
}
```

### **Gestion d'État Avancée avec Zustand**

#### **Store POS Global**
```typescript
// hotel-frontend/src/stores/posStore.ts
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

interface POSStore {
  // État
  workspace: POSWorkspace | null;
  selectedTable: Table | null;
  currentCommande: Commande | null;
  cartItems: CommandeItem[];

  // Actions
  setWorkspace: (workspace: POSWorkspace) => void;
  selectTable: (table: Table) => void;
  addItemToCart: (item: MenuItem, quantity: number) => void;
  updateCartItem: (itemId: number, quantity: number) => void;
  removeCartItem: (itemId: number) => void;
  clearCart: () => void;

  // Commandes
  createCommande: () => Promise<Commande>;
  validateCommande: () => Promise<void>;
  processPayment: (paymentData: PaymentData) => Promise<void>;

  // Tables
  updateTableStatus: (tableId: number, statut: string) => Promise<void>;

  // Notifications
  addNotification: (notification: POSNotification) => void;
  removeNotification: (id: string) => void;
}

export const usePOSStore = create<POSStore>()(
  devtools(
    persist(
      (set, get) => ({
        workspace: null,
        selectedTable: null,
        currentCommande: null,
        cartItems: [],

        setWorkspace: (workspace) => set({ workspace }),

        selectTable: (table) => set({ selectedTable: table }),

        addItemToCart: (item, quantity) => {
          const { cartItems } = get();
          const existingItem = cartItems.find(ci => ci.produit_id === item.produit_id);

          if (existingItem) {
            set({
              cartItems: cartItems.map(ci =>
                ci.produit_id === item.produit_id
                  ? { ...ci, quantite: ci.quantite + quantity }
                  : ci
              )
            });
          } else {
            set({
              cartItems: [...cartItems, {
                produit_id: item.produit_id,
                nom: item.nom,
                quantite: quantity,
                prix_unitaire: item.prix_vente,
                montant_ligne: item.prix_vente * quantity,
                statut: 'En attente'
              }]
            });
          }
        },

        // ... autres actions
      }),
      { name: 'pos-store' }
    )
  )
);
```

### **Composants UI Avancés**

#### **Table Interactive avec Drag & Drop**
```typescript
// hotel-frontend/src/components/pos/InteractiveTableLayout.tsx
import { DndProvider, useDrag, useDrop } from 'react-dnd';

interface DraggableTableProps {
  table: Table;
  onTableMove: (tableId: number, position: { x: number; y: number }) => void;
  onTableSelect: (table: Table) => void;
}

const DraggableTable: React.FC<DraggableTableProps> = ({
  table, onTableMove, onTableSelect
}) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'table',
    item: { id: table.table_id, x: table.position_x, y: table.position_y },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const getTableStatusColor = (statut: string) => {
    switch (statut) {
      case 'Libre': return 'bg-green-100 border-green-300 text-green-700';
      case 'Occupée': return 'bg-red-100 border-red-300 text-red-700';
      case 'Réservée': return 'bg-orange-100 border-orange-300 text-orange-700';
      case 'Maintenance': return 'bg-gray-100 border-gray-300 text-gray-500';
      default: return 'bg-gray-100 border-gray-300';
    }
  };

  return (
    <div
      ref={drag}
      className={`
        absolute cursor-pointer rounded-lg border-2 p-3 min-w-[80px] min-h-[80px]
        flex flex-col items-center justify-center text-sm font-medium
        transition-all duration-200 hover:shadow-lg
        ${getTableStatusColor(table.statut)}
        ${isDragging ? 'opacity-50' : ''}
      `}
      style={{
        left: table.position_x || 0,
        top: table.position_y || 0,
      }}
      onClick={() => onTableSelect(table)}
    >
      <div className="text-xs font-bold">{table.numero}</div>
      <div className="text-xs">{table.capacite}p</div>
      {table.commande_active && (
        <div className="absolute -top-2 -right-2 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
          <span className="text-white text-xs">!</span>
        </div>
      )}
    </div>
  );
};
```

#### **Menu avec Recherche et Filtres**
```typescript
// hotel-frontend/src/components/pos/AdvancedMenuDisplay.tsx
export const AdvancedMenuDisplay: React.FC<MenuDisplayProps> = ({
  categories, onItemAdd, serviceId
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showOnlyAvailable, setShowOnlyAvailable] = useState(true);
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'popularity'>('name');

  const filteredItems = useMemo(() => {
    let items = categories.flatMap(cat => cat.items);

    // Filtrage par recherche
    if (searchTerm) {
      items = items.filter(item =>
        item.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtrage par catégorie
    if (selectedCategory !== 'all') {
      items = items.filter(item => item.categorie === selectedCategory);
    }

    // Filtrage par disponibilité
    if (showOnlyAvailable) {
      items = items.filter(item => item.stock_disponible > 0);
    }

    // Tri
    items.sort((a, b) => {
      switch (sortBy) {
        case 'price': return a.prix_vente - b.prix_vente;
        case 'popularity': return (b.popularity || 0) - (a.popularity || 0);
        default: return a.nom.localeCompare(b.nom);
      }
    });

    return items;
  }, [categories, searchTerm, selectedCategory, showOnlyAvailable, sortBy]);

  return (
    <div className="h-full flex flex-col">
      {/* Barre de recherche et filtres */}
      <div className="p-4 border-b space-y-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Rechercher un produit..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div className="flex gap-2 flex-wrap">
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="all">Toutes catégories</option>
            {categories.map(cat => (
              <option key={cat.categorie_id} value={cat.nom}>
                {cat.nom}
              </option>
            ))}
          </select>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="name">Nom</option>
            <option value="price">Prix</option>
            <option value="popularity">Popularité</option>
          </select>

          <label className="flex items-center text-sm">
            <input
              type="checkbox"
              checked={showOnlyAvailable}
              onChange={(e) => setShowOnlyAvailable(e.target.checked)}
              className="mr-2"
            />
            Disponibles uniquement
          </label>
        </div>
      </div>

      {/* Grille des produits */}
      <div className="flex-1 overflow-auto p-4">
        <div className="grid grid-cols-2 lg:grid-cols-3 gap-3">
          {filteredItems.map(item => (
            <MenuItemCard
              key={item.produit_id}
              item={item}
              onAdd={(quantity) => onItemAdd(item, quantity)}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
```

### **WebSocket pour Temps Réel**

#### **Hook WebSocket POS**
```typescript
// hotel-frontend/src/hooks/usePOSWebSocket.ts
export const usePOSWebSocket = (serviceId: number) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const { addNotification } = usePOSStore();

  useEffect(() => {
    const newSocket = io(process.env.REACT_APP_WS_URL || 'http://localhost:3001');

    newSocket.emit('join_service', serviceId);

    // Écouter les notifications
    newSocket.on('new_order', (data) => {
      addNotification({
        id: Date.now().toString(),
        type: 'info',
        title: 'Nouvelle commande',
        message: `Table ${data.table} - ${data.items.length} items`,
        timestamp: new Date()
      });
    });

    newSocket.on('order_ready', (data) => {
      addNotification({
        id: Date.now().toString(),
        type: 'success',
        title: 'Commande prête',
        message: `Commande #${data.commande_id} prête`,
        timestamp: new Date()
      });
    });

    newSocket.on('stock_alert', (data) => {
      addNotification({
        id: Date.now().toString(),
        type: 'warning',
        title: 'Alerte stock',
        message: `Stock faible: ${data.nom}`,
        timestamp: new Date()
      });
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [serviceId, addNotification]);

  return socket;
};
```

Cette architecture frontend offrira une expérience utilisateur moderne et efficace pour la gestion des restaurants et bars avec intégration complète du système d'inventaire, gestion d'état avancée, interface drag & drop et notifications temps réel.
