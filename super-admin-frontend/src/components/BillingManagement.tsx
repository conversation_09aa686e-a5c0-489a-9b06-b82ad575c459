import React, { useState } from 'react';
import { CreditCard, Download, AlertCircle, X, ArrowUpCircle, BanIcon, FileText } from 'lucide-react';
import { format } from 'date-fns';

interface Subscription {
  id: string;
  patronId: string;
  patronName: string;
  plan: 'basic' | 'premium' | 'enterprise';
  status: 'active' | 'suspended' | 'cancelled';
  price: number;
  renewalDate: string;
  licenseCount: number;
  paymentMethod: {
    type: 'credit_card' | 'bank_transfer';
    last4: string;
    expiry?: string;
  };
}

interface Payment {
  id: string;
  date: string;
  amount: number;
  status: 'paid' | 'pending' | 'failed';
  invoiceNumber: string;
  description: string;
}

const mockSubscriptions: Subscription[] = [
  {
    id: '1',
    patronId: '1',
    patronName: '<PERSON>',
    plan: 'enterprise',
    status: 'active',
    price: 999.99,
    renewalDate: '2024-04-15',
    licenseCount: 12,
    paymentMethod: {
      type: 'credit_card',
      last4: '4242',
      expiry: '12/25'
    }
  },
  {
    id: '2',
    patronId: '2',
    patronName: '<PERSON>',
    plan: 'premium',
    status: 'active',
    price: 499.99,
    renewalDate: '2024-04-01',
    licenseCount: 5,
    paymentMethod: {
      type: 'bank_transfer',
      last4: '9876'
    }
  },
  {
    id: '3',
    patronId: '3',
    patronName: 'Michael Chen',
    plan: 'basic',
    status: 'suspended',
    price: 99.99,
    renewalDate: '2024-03-28',
    licenseCount: 1,
    paymentMethod: {
      type: 'credit_card',
      last4: '1234',
      expiry: '09/24'
    }
  }
];

const mockPayments: Payment[] = [
  {
    id: '1',
    date: '2024-03-15',
    amount: 999.99,
    status: 'paid',
    invoiceNumber: 'INV-2024-001',
    description: 'Enterprise Plan - March 2024'
  },
  {
    id: '2',
    date: '2024-03-01',
    amount: 499.99,
    status: 'paid',
    invoiceNumber: 'INV-2024-002',
    description: 'Premium Plan - March 2024'
  },
  {
    id: '3',
    date: '2024-02-28',
    amount: 99.99,
    status: 'failed',
    invoiceNumber: 'INV-2024-003',
    description: 'Basic Plan - March 2024'
  }
];

const pricingTiers = [
  {
    name: 'Basic',
    price: 99.99,
    features: [
      'Up to 3 hotels',
      'Basic analytics',
      'Email support',
      'Standard features'
    ]
  },
  {
    name: 'Premium',
    price: 499.99,
    features: [
      'Up to 10 hotels',
      'Advanced analytics',
      'Priority support',
      'Premium features',
      'API access'
    ]
  },
  {
    name: 'Enterprise',
    price: 999.99,
    features: [
      'Unlimited hotels',
      'Custom analytics',
      '24/7 dedicated support',
      'All features',
      'Custom integrations',
      'SLA guarantee'
    ]
  }
];

interface UpgradeModalProps {
  subscription: Subscription;
  onClose: () => void;
  onUpgrade: (plan: string) => void;
}

function UpgradeModal({ subscription, onClose, onUpgrade }: UpgradeModalProps) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold">Upgrade Subscription</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" />
          </button>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {pricingTiers.map((tier) => (
              <div
                key={tier.name}
                className={`border rounded-lg p-6 ${
                  subscription.plan.toLowerCase() === tier.name.toLowerCase()
                    ? 'border-blue-500 bg-blue-50'
                    : 'hover:border-blue-300'
                }`}
              >
                <h3 className="text-lg font-semibold mb-2">{tier.name}</h3>
                <p className="text-2xl font-bold mb-4">${tier.price}/mo</p>
                <ul className="space-y-2 mb-6">
                  {tier.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm">
                      <span className="mr-2">✓</span>
                      {feature}
                    </li>
                  ))}
                </ul>
                <button
                  onClick={() => onUpgrade(tier.name.toLowerCase())}
                  disabled={subscription.plan.toLowerCase() === tier.name.toLowerCase()}
                  className={`w-full py-2 px-4 rounded-lg ${
                    subscription.plan.toLowerCase() === tier.name.toLowerCase()
                      ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {subscription.plan.toLowerCase() === tier.name.toLowerCase()
                    ? 'Current Plan'
                    : 'Upgrade'}
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function BillingManagement() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>(mockSubscriptions);
  const [payments] = useState<Payment[]>(mockPayments);
  const [showUpgradeModal, setShowUpgradeModal] = useState<Subscription | null>(null);
  const [showSuspendConfirm, setShowSuspendConfirm] = useState<string | null>(null);

  const handleUpgrade = (plan: string) => {
    if (showUpgradeModal) {
      setSubscriptions(subscriptions.map(sub =>
        sub.id === showUpgradeModal.id
          ? { ...sub, plan: plan as 'basic' | 'premium' | 'enterprise' }
          : sub
      ));
      setShowUpgradeModal(null);
    }
  };

  const handleSuspend = (id: string) => {
    setSubscriptions(subscriptions.map(sub =>
      sub.id === id
        ? { ...sub, status: sub.status === 'suspended' ? 'active' : 'suspended' }
        : sub
    ));
    setShowSuspendConfirm(null);
  };

  const getStatusColor = (status: Subscription['status'] | Payment['status']) => {
    switch (status) {
      case 'active':
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'suspended':
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-6">
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Active Subscriptions</h2>
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patron</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Licenses</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Method</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Renewal</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {subscriptions.map((subscription) => (
                <tr key={subscription.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900">{subscription.patronName}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <CreditCard className="w-4 h-4 mr-2 text-blue-600" />
                      <span className="text-sm text-gray-900 capitalize">{subscription.plan}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(subscription.status)}`}>
                      {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    {subscription.licenseCount} active
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {subscription.paymentMethod.type === 'credit_card' ? (
                        <>
                          •••• {subscription.paymentMethod.last4}
                          <span className="text-gray-500 ml-2">
                            exp. {subscription.paymentMethod.expiry}
                          </span>
                        </>
                      ) : (
                        <>Bank transfer •••• {subscription.paymentMethod.last4}</>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {format(new Date(subscription.renewalDate), 'MMM d, yyyy')}
                    </div>
                  </td>
                  <td className="px-6 py-4 text-right space-x-2">
                    <button
                      onClick={() => setShowUpgradeModal(subscription)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      <ArrowUpCircle className="w-5 h-5 inline" />
                    </button>
                    <button
                      onClick={() => setShowSuspendConfirm(subscription.id)}
                      className="text-yellow-600 hover:text-yellow-900"
                    >
                      <BanIcon className="w-5 h-5 inline" />
                    </button>
                    <button
                      onClick={() => {}}
                      className="text-gray-600 hover:text-gray-900"
                    >
                      <FileText className="w-5 h-5 inline" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div>
        <h2 className="text-xl font-semibold mb-4">Payment History</h2>
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {payments.map((payment) => (
                <tr key={payment.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {format(new Date(payment.date), 'MMM d, yyyy')}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {payment.invoiceNumber}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    {payment.description}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    ${payment.amount.toFixed(2)}
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
                      {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-right">
                    <button
                      onClick={() => {}}
                      className="text-gray-600 hover:text-gray-900"
                    >
                      <Download className="w-5 h-5 inline" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {showUpgradeModal && (
        <UpgradeModal
          subscription={showUpgradeModal}
          onClose={() => setShowUpgradeModal(null)}
          onUpgrade={handleUpgrade}
        />
      )}

      {showSuspendConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full">
            <div className="flex items-center mb-4">
              <AlertCircle className="w-6 h-6 text-yellow-600 mr-2" />
              <h3 className="text-lg font-medium">Confirm Action</h3>
            </div>
            <p className="text-gray-500 mb-6">
              Are you sure you want to {
                subscriptions.find(s => s.id === showSuspendConfirm)?.status === 'suspended'
                  ? 'reactivate'
                  : 'suspend'
              } this subscription?
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowSuspendConfirm(null)}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => handleSuspend(showSuspendConfirm)}
                className="px-4 py-2 bg-yellow-600 text-white rounded-md text-sm font-medium hover:bg-yellow-700"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}