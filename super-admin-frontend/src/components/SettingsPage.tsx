import React, { useState } from 'react';
import { Save, Mail, Users, Building2, Palette, AlertCircle } from 'lucide-react';

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  content: string;
}

interface UserRole {
  id: string;
  name: string;
  permissions: string[];
}

interface PlanLimits {
  basic: {
    hotels: number;
    users: number;
    storage: number;
  };
  premium: {
    hotels: number;
    users: number;
    storage: number;
  };
  enterprise: {
    hotels: number;
    users: number;
    storage: number;
  };
}

const defaultEmailTemplates: EmailTemplate[] = [
  {
    id: '1',
    name: 'Welcome Email',
    subject: 'Welcome to HotelHub!',
    content: 'Dear {{name}},\n\nWelcome to HotelHub! We\'re excited to have you on board...'
  },
  {
    id: '2',
    name: 'Password Reset',
    subject: 'Password Reset Request',
    content: 'Hello {{name}},\n\nWe received a request to reset your password...'
  }
];

const defaultUserRoles: UserRole[] = [
  {
    id: '1',
    name: 'Super Admin',
    permissions: ['all']
  },
  {
    id: '2',
    name: 'Hotel Manager',
    permissions: ['manage_hotels', 'view_reports', 'manage_staff']
  },
  {
    id: '3',
    name: 'Staff',
    permissions: ['view_hotels', 'basic_operations']
  }
];

const defaultPlanLimits: PlanLimits = {
  basic: {
    hotels: 3,
    users: 5,
    storage: 5
  },
  premium: {
    hotels: 10,
    users: 20,
    storage: 20
  },
  enterprise: {
    hotels: 999999,
    users: 999999,
    storage: 999999
  }
};

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('email');
  const [emailTemplates, setEmailTemplates] = useState<EmailTemplate[]>(defaultEmailTemplates);
  const [userRoles, setUserRoles] = useState<UserRole[]>(defaultUserRoles);
  const [planLimits, setPlanLimits] = useState<PlanLimits>(defaultPlanLimits);
  const [brandSettings, setBrandSettings] = useState({
    primaryColor: '#2563eb',
    logoUrl: 'https://example.com/logo.png',
    companyName: 'HotelHub',
    supportEmail: '<EMAIL>'
  });
  const [showSaveConfirmation, setShowSaveConfirmation] = useState(false);

  const handleSave = () => {
    setShowSaveConfirmation(true);
    setTimeout(() => setShowSaveConfirmation(false), 3000);
  };

  const tabs = [
    { id: 'email', name: 'Email Templates', icon: <Mail className="w-5 h-5" /> },
    { id: 'roles', name: 'User Roles', icon: <Users className="w-5 h-5" /> },
    { id: 'limits', name: 'Plan Limits', icon: <Building2 className="w-5 h-5" /> },
    { id: 'brand', name: 'Branding', icon: <Palette className="w-5 h-5" /> }
  ];

  return (
    <div className="p-6">
      <div className="mb-6 flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-gray-900">System Settings</h1>
        <button
          onClick={handleSave}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          <Save className="w-5 h-5 mr-2" />
          Save Changes
        </button>
      </div>

      {showSaveConfirmation && (
        <div className="fixed bottom-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-lg flex items-center">
          <CheckCircle className="w-5 h-5 mr-2" />
          Settings saved successfully!
        </div>
      )}

      <div className="bg-white rounded-lg shadow">
        <div className="border-b">
          <nav className="flex space-x-4 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center px-3 py-4 text-sm font-medium border-b-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.icon}
                <span className="ml-2">{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'email' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-medium">Email Templates</h2>
                <button className="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200">
                  Add Template
                </button>
              </div>
              {emailTemplates.map((template) => (
                <div key={template.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="font-medium">{template.name}</h3>
                      <p className="text-sm text-gray-500">Subject: {template.subject}</p>
                    </div>
                    <button className="text-blue-600 hover:text-blue-800">Edit</button>
                  </div>
                  <div className="bg-gray-50 rounded p-3">
                    <pre className="text-sm whitespace-pre-wrap">{template.content}</pre>
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'roles' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-medium">User Roles & Permissions</h2>
                <button className="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200">
                  Add Role
                </button>
              </div>
              {userRoles.map((role) => (
                <div key={role.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-4">
                    <h3 className="font-medium">{role.name}</h3>
                    <button className="text-blue-600 hover:text-blue-800">Edit</button>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    {role.permissions.map((permission) => (
                      <span
                        key={permission}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {permission.split('_').join(' ')}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'limits' && (
            <div className="space-y-6">
              <h2 className="text-lg font-medium mb-4">Plan Limits & Quotas</h2>
              {Object.entries(planLimits).map(([plan, limits]) => (
                <div key={plan} className="border rounded-lg p-4">
                  <h3 className="font-medium capitalize mb-4">{plan} Plan</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Max Hotels
                      </label>
                      <input
                        type="number"
                        value={limits.hotels}
                        onChange={(e) =>
                          setPlanLimits({
                            ...planLimits,
                            [plan]: { ...limits, hotels: parseInt(e.target.value) }
                          })
                        }
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Max Users
                      </label>
                      <input
                        type="number"
                        value={limits.users}
                        onChange={(e) =>
                          setPlanLimits({
                            ...planLimits,
                            [plan]: { ...limits, users: parseInt(e.target.value) }
                          })
                        }
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Storage (GB)
                      </label>
                      <input
                        type="number"
                        value={limits.storage}
                        onChange={(e) =>
                          setPlanLimits({
                            ...planLimits,
                            [plan]: { ...limits, storage: parseInt(e.target.value) }
                          })
                        }
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'brand' && (
            <div className="space-y-6">
              <h2 className="text-lg font-medium mb-4">Branding & Customization</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Company Name
                  </label>
                  <input
                    type="text"
                    value={brandSettings.companyName}
                    onChange={(e) =>
                      setBrandSettings({ ...brandSettings, companyName: e.target.value })
                    }
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Support Email
                  </label>
                  <input
                    type="email"
                    value={brandSettings.supportEmail}
                    onChange={(e) =>
                      setBrandSettings({ ...brandSettings, supportEmail: e.target.value })
                    }
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Primary Color
                  </label>
                  <div className="mt-1 flex items-center space-x-2">
                    <input
                      type="color"
                      value={brandSettings.primaryColor}
                      onChange={(e) =>
                        setBrandSettings({ ...brandSettings, primaryColor: e.target.value })
                      }
                      className="h-10 w-20"
                    />
                    <input
                      type="text"
                      value={brandSettings.primaryColor}
                      onChange={(e) =>
                        setBrandSettings({ ...brandSettings, primaryColor: e.target.value })
                      }
                      className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Logo URL
                  </label>
                  <input
                    type="url"
                    value={brandSettings.logoUrl}
                    onChange={(e) =>
                      setBrandSettings({ ...brandSettings, logoUrl: e.target.value })
                    }
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}