import React, { useState } from 'react';
import { Edit2, Trash2, UserPlus, X, AlertCircle } from 'lucide-react';

interface Patron {
  id: string;
  name: string;
  email: string;
  phone: string;
  status: 'active' | 'inactive';
  plan: 'basic' | 'premium' | 'enterprise';
  hotelCount: number;
  lastActive: string;
}

const mockPatrons: Patron[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    status: 'active',
    plan: 'enterprise',
    hotelCount: 12,
    lastActive: '2024-03-15T10:30:00Z',
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    status: 'active',
    plan: 'premium',
    hotelCount: 5,
    lastActive: '2024-03-14T15:45:00Z',
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    status: 'inactive',
    plan: 'basic',
    hotelCount: 1,
    lastActive: '2024-02-28T09:15:00Z',
  },
];

interface PatronModalProps {
  patron?: Patron;
  onClose: () => void;
  onSave: (patron: Omit<Patron, 'id' | 'lastActive'>) => void;
}

function PatronModal({ patron, onClose, onSave }: PatronModalProps) {
  const [formData, setFormData] = useState({
    name: patron?.name || '',
    email: patron?.email || '',
    phone: patron?.phone || '',
    status: patron?.status || 'active',
    plan: patron?.plan || 'basic',
    hotelCount: patron?.hotelCount || 0,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg w-full max-w-md">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold">
            {patron ? 'Edit Patron' : 'Add New Patron'}
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" />
          </button>
        </div>
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Email</label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Phone</label>
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Status</label>
            <select
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value as 'active' | 'inactive' })}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Subscription Plan</label>
            <select
              value={formData.plan}
              onChange={(e) => setFormData({ ...formData, plan: e.target.value as 'basic' | 'premium' | 'enterprise' })}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="basic">Basic</option>
              <option value="premium">Premium</option>
              <option value="enterprise">Enterprise</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Number of Hotels</label>
            <input
              type="number"
              value={formData.hotelCount}
              onChange={(e) => setFormData({ ...formData, hotelCount: parseInt(e.target.value) })}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
              min="0"
              required
            />
          </div>
          <div className="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default function PatronManagement() {
  const [patrons, setPatrons] = useState<Patron[]>(mockPatrons);
  const [showModal, setShowModal] = useState(false);
  const [editingPatron, setEditingPatron] = useState<Patron | undefined>();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  const handleSave = (patronData: Omit<Patron, 'id' | 'lastActive'>) => {
    if (editingPatron) {
      setPatrons(patrons.map(p => 
        p.id === editingPatron.id 
          ? { ...p, ...patronData }
          : p
      ));
    } else {
      const newPatron: Patron = {
        ...patronData,
        id: Math.random().toString(36).substr(2, 9),
        lastActive: new Date().toISOString(),
      };
      setPatrons([...patrons, newPatron]);
    }
    setShowModal(false);
    setEditingPatron(undefined);
  };

  const handleEdit = (patron: Patron) => {
    setEditingPatron(patron);
    setShowModal(true);
  };

  const handleDelete = (id: string) => {
    setPatrons(patrons.filter(p => p.id !== id));
    setShowDeleteConfirm(null);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Patron Management</h1>
        <button
          onClick={() => setShowModal(true)}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          <UserPlus className="w-5 h-5 mr-2" />
          Add Patron
        </button>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patron</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hotels</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Active</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {patrons.map((patron) => (
              <tr key={patron.id} className="hover:bg-gray-50">
                <td className="px-6 py-4">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{patron.name}</div>
                    <div className="text-sm text-gray-500">{patron.email}</div>
                    <div className="text-sm text-gray-500">{patron.phone}</div>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    patron.status === 'active' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {patron.status.charAt(0).toUpperCase() + patron.status.slice(1)}
                  </span>
                </td>
                <td className="px-6 py-4">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    patron.plan === 'enterprise' 
                      ? 'bg-purple-100 text-purple-800'
                      : patron.plan === 'premium'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                  }`}>
                    {patron.plan.charAt(0).toUpperCase() + patron.plan.slice(1)}
                  </span>
                </td>
                <td className="px-6 py-4 text-sm text-gray-500">{patron.hotelCount}</td>
                <td className="px-6 py-4 text-sm text-gray-500">
                  {new Date(patron.lastActive).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 text-right text-sm font-medium space-x-2">
                  <button
                    onClick={() => handleEdit(patron)}
                    className="text-blue-600 hover:text-blue-900"
                  >
                    <Edit2 className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => setShowDeleteConfirm(patron.id)}
                    className="text-red-600 hover:text-red-900"
                  >
                    <Trash2 className="w-5 h-5" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {showModal && (
        <PatronModal
          patron={editingPatron}
          onClose={() => {
            setShowModal(false);
            setEditingPatron(undefined);
          }}
          onSave={handleSave}
        />
      )}

      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full">
            <div className="flex items-center mb-4">
              <AlertCircle className="w-6 h-6 text-red-600 mr-2" />
              <h3 className="text-lg font-medium">Confirm Deletion</h3>
            </div>
            <p className="text-gray-500 mb-6">Are you sure you want to delete this patron? This action cannot be undone.</p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(null)}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => handleDelete(showDeleteConfirm)}
                className="px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}