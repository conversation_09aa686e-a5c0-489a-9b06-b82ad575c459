import React, { useState } from 'react';
import { Building2, Edit2, Trash2, PlusCircle, AlertCircle, X, Search } from 'lucide-react';
import { format } from 'date-fns';

interface Hotel {
  id: string;
  name: string;
  location: string;
  patronId: string;
  patronName: string;
  createdAt: string;
  status: 'active' | 'maintenance' | 'inactive';
  rooms: number;
  rating: number;
}

interface HotelModalProps {
  hotel?: Hotel;
  patrons: Array<{ id: string; name: string }>;
  onClose: () => void;
  onSave: (hotel: Omit<Hotel, 'id' | 'createdAt'>) => void;
}

const mockHotels: Hotel[] = [
  {
    id: '1',
    name: 'Grand Plaza Hotel',
    location: 'New York, NY',
    patronId: '1',
    patronName: '<PERSON>',
    createdAt: '2024-01-15T10:00:00Z',
    status: 'active',
    rooms: 200,
    rating: 4.5
  },
  {
    id: '2',
    name: 'Seaside Resort',
    location: 'Miami Beach, FL',
    patronId: '2',
    patronName: '<PERSON>',
    createdAt: '2024-02-01T15:30:00Z',
    status: 'active',
    rooms: 150,
    rating: 4.8
  },
  {
    id: '3',
    name: 'Mountain View Inn',
    location: 'Denver, CO',
    patronId: '1',
    patronName: '<PERSON>',
    createdAt: '2024-02-15T09:00:00Z',
    status: 'maintenance',
    rooms: 75,
    rating: 4.2
  }
];

const mockPatrons = [
  { id: '1', name: 'John Smith' },
  { id: '2', name: 'Sarah <PERSON>' },
  { id: '3', name: 'Michael Chen' }
];

function HotelModal({ hotel, patrons, onClose, onSave }: HotelModalProps) {
  const [formData, setFormData] = useState({
    name: hotel?.name || '',
    location: hotel?.location || '',
    patronId: hotel?.patronId || patrons[0]?.id || '',
    status: hotel?.status || 'active',
    rooms: hotel?.rooms || 0,
    rating: hotel?.rating || 0,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const patronName = patrons.find(p => p.id === formData.patronId)?.name || '';
    onSave({ ...formData, patronName });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg w-full max-w-md">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold">
            {hotel ? 'Edit Hotel' : 'Add New Hotel'}
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" />
          </button>
        </div>
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Hotel Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Location</label>
            <input
              type="text"
              value={formData.location}
              onChange={(e) => setFormData({ ...formData, location: e.target.value })}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Patron</label>
            <select
              value={formData.patronId}
              onChange={(e) => setFormData({ ...formData, patronId: e.target.value })}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
            >
              {patrons.map((patron) => (
                <option key={patron.id} value={patron.id}>
                  {patron.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Status</label>
            <select
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value as Hotel['status'] })}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="active">Active</option>
              <option value="maintenance">Maintenance</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Number of Rooms</label>
            <input
              type="number"
              value={formData.rooms}
              onChange={(e) => setFormData({ ...formData, rooms: parseInt(e.target.value) })}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
              min="0"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Rating</label>
            <input
              type="number"
              value={formData.rating}
              onChange={(e) => setFormData({ ...formData, rating: parseFloat(e.target.value) })}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
              min="0"
              max="5"
              step="0.1"
              required
            />
          </div>
          <div className="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default function HotelManagement() {
  const [hotels, setHotels] = useState<Hotel[]>(mockHotels);
  const [showModal, setShowModal] = useState(false);
  const [editingHotel, setEditingHotel] = useState<Hotel | undefined>();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [filterPatron, setFilterPatron] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<Hotel['status'] | ''>('');
  const [searchTerm, setSearchTerm] = useState<string>('');

  const filteredHotels = hotels.filter(hotel => {
    const matchesPatron = !filterPatron || hotel.patronId === filterPatron;
    const matchesStatus = !filterStatus || hotel.status === filterStatus;
    const matchesSearch = !searchTerm || 
      hotel.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      hotel.location.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesPatron && matchesStatus && matchesSearch;
  });

  const handleSave = (hotelData: Omit<Hotel, 'id' | 'createdAt'>) => {
    if (editingHotel) {
      setHotels(hotels.map(h => 
        h.id === editingHotel.id 
          ? { ...h, ...hotelData }
          : h
      ));
    } else {
      const newHotel: Hotel = {
        ...hotelData,
        id: Math.random().toString(36).substr(2, 9),
        createdAt: new Date().toISOString(),
      };
      setHotels([...hotels, newHotel]);
    }
    setShowModal(false);
    setEditingHotel(undefined);
  };

  const handleDelete = (id: string) => {
    setHotels(hotels.filter(h => h.id !== id));
    setShowDeleteConfirm(null);
  };

  const getStatusColor = (status: Hotel['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800';
      case 'inactive':
        return 'bg-red-100 text-red-800';
    }
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-900">Hotel Management</h1>
        <button
          onClick={() => setShowModal(true)}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          <PlusCircle className="w-5 h-5 mr-2" />
          Add Hotel
        </button>
      </div>

      <div className="bg-white rounded-lg shadow mb-6">
        <div className="p-4 border-b">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search hotels..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
            <select
              value={filterPatron}
              onChange={(e) => setFilterPatron(e.target.value)}
              className="rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="">All Patrons</option>
              {mockPatrons.map((patron) => (
                <option key={patron.id} value={patron.id}>
                  {patron.name}
                </option>
              ))}
            </select>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as Hotel['status'] | '')}
              className="rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="">All Statuses</option>
              <option value="active">Active</option>
              <option value="maintenance">Maintenance</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hotel</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patron</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rooms</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rating</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredHotels.map((hotel) => (
              <tr key={hotel.id} className="hover:bg-gray-50">
                <td className="px-6 py-4">
                  <div>
                    <div className="text-sm font-medium text-gray-900">{hotel.name}</div>
                    <div className="text-sm text-gray-500">{hotel.location}</div>
                  </div>
                </td>
                <td className="px-6 py-4 text-sm text-gray-500">{hotel.patronName}</td>
                <td className="px-6 py-4">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(hotel.status)}`}>
                    {hotel.status.charAt(0).toUpperCase() + hotel.status.slice(1)}
                  </span>
                </td>
                <td className="px-6 py-4 text-sm text-gray-500">{hotel.rooms}</td>
                <td className="px-6 py-4 text-sm text-gray-500">{hotel.rating}/5</td>
                <td className="px-6 py-4 text-sm text-gray-500">
                  {format(new Date(hotel.createdAt), 'MMM d, yyyy')}
                </td>
                <td className="px-6 py-4 text-right text-sm font-medium space-x-2">
                  <button
                    onClick={() => {
                      setEditingHotel(hotel);
                      setShowModal(true);
                    }}
                    className="text-blue-600 hover:text-blue-900"
                  >
                    <Edit2 className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => setShowDeleteConfirm(hotel.id)}
                    className="text-red-600 hover:text-red-900"
                  >
                    <Trash2 className="w-5 h-5" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {showModal && (
        <HotelModal
          hotel={editingHotel}
          patrons={mockPatrons}
          onClose={() => {
            setShowModal(false);
            setEditingHotel(undefined);
          }}
          onSave={handleSave}
        />
      )}

      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full">
            <div className="flex items-center mb-4">
              <AlertCircle className="w-6 h-6 text-red-600 mr-2" />
              <h3 className="text-lg font-medium">Confirm Deletion</h3>
            </div>
            <p className="text-gray-500 mb-6">Are you sure you want to delete this hotel? This action cannot be undone.</p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(null)}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => handleDelete(showDeleteConfirm)}
                className="px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}