import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Hotel, Users, CreditCard, Settings, Activity, Building2, BarChart3, LogOut, Bell } from 'lucide-react';

const Dashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const [activeComponent, setActiveComponent] = useState('dashboard');
  const [showNotifications, setShowNotifications] = useState(false);

  const stats = [
    {
      title: 'Hôtels Actifs',
      value: '156',
      change: '+12.5%',
      icon: <Hotel className="w-6 h-6 text-blue-600" />,
    },
    {
      title: 'Total Clients',
      value: '8,549',
      change: '+23.1%',
      icon: <Users className="w-6 h-6 text-green-600" />,
    },
    {
      title: 'Plans Premium',
      value: '89%',
      change: '****%',
      icon: <CreditCard className="w-6 h-6 text-purple-600" />,
    },
    {
      title: 'Disponibilité Système',
      value: '99.9%',
      change: '0.1%',
      icon: <Activity className="w-6 h-6 text-red-600" />,
    },
  ];

  const navigation = [
    { name: 'Tableau de Bord', icon: <BarChart3 className="w-5 h-5" />, component: 'dashboard' },
    { name: 'Comptes Hôtels', icon: <Building2 className="w-5 h-5" />, component: 'hotels' },
    { name: 'Gestion Propriétaires', icon: <Users className="w-5 h-5" />, component: 'owners' },
    { name: 'Facturation & Licences', icon: <CreditCard className="w-5 h-5" />, component: 'billing' },
    { name: 'Journal Activité', icon: <Activity className="w-5 h-5" />, component: 'activity' },
    { name: 'Paramètres', icon: <Settings className="w-5 h-5" />, component: 'settings' },
  ];

  const renderContent = () => {
    switch (activeComponent) {
      case 'dashboard':
        return (
          <div className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {stats.map((stat) => (
                <div
                  key={stat.title}
                  className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow"
                >
                  <div className="flex items-center justify-between mb-4">
                    <div>{stat.icon}</div>
                    <span className={`text-sm font-medium ${
                      stat.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.change}
                    </span>
                  </div>
                  <h3 className="text-gray-500 text-sm font-medium">{stat.title}</h3>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{stat.value}</p>
                </div>
              ))}
            </div>

            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b">
                <h2 className="text-lg font-medium text-gray-900">Activité Récente</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="flex items-center justify-between py-3 border-b last:border-0">
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                          <Building2 className="w-5 h-5 text-blue-600" />
                        </div>
                        <div className="ml-4">
                          <p className="text-sm font-medium text-gray-900">Nouvelle inscription hôtel</p>
                          <p className="text-sm text-gray-500">Hilton Garden Inn - New York</p>
                        </div>
                      </div>
                      <span className="text-sm text-gray-500">Il y a 2 heures</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return (
          <div className="p-8">
            <h2 className="text-2xl font-semibold text-gray-900">
              {navigation.find(nav => nav.component === activeComponent)?.name}
            </h2>
            <p className="mt-4 text-gray-600">Cette section est en cours de développement.</p>
          </div>
        );
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-lg">
        <div className="flex items-center justify-center h-16 border-b">
          <Hotel className="w-8 h-8 text-blue-600" />
          <span className="ml-2 text-xl font-semibold">HotelHub</span>
        </div>
        <nav className="mt-6">
          {navigation.map((item) => (
            <a
              key={item.name}
              href="#"
              onClick={() => setActiveComponent(item.component)}
              className={`flex items-center px-6 py-3 text-gray-600 hover:bg-blue-50 hover:text-blue-600 transition-colors ${
                activeComponent === item.component ? 'bg-blue-50 text-blue-600' : ''
              }`}
            >
              {item.icon}
              <span className="mx-3">{item.name}</span>
            </a>
          ))}
        </nav>
        <div className="absolute bottom-0 w-64 border-t">
          <a
            href="#"
            className="flex items-center px-6 py-3 text-gray-600 hover:bg-blue-50 hover:text-blue-600 transition-colors"
            onClick={logout}
          >
            <LogOut className="w-5 h-5" />
            <span className="mx-3">Déconnexion</span>
          </a>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <header className="bg-white shadow">
          <div className="px-8 py-6 flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">
                {navigation.find(nav => nav.component === activeComponent)?.name}
              </h1>
              <p className="text-sm text-gray-500">Bienvenue, {user?.email}</p>
            </div>
            <div className="relative">
              <button
                onClick={() => setShowNotifications(!showNotifications)}
                className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-full relative"
              >
                <Bell className="w-6 h-6" />
                <span className="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
              </button>
              {showNotifications && (
                <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg z-50">
                  <div className="p-4 border-b">
                    <h3 className="font-medium">Notifications Récentes</h3>
                  </div>
                  <div className="max-h-96 overflow-y-auto">
                    {/* Add notification items here */}
                  </div>
                </div>
              )}
            </div>
          </div>
        </header>

        <main>
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

export default Dashboard; 