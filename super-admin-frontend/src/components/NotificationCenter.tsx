import React, { useState } from 'react';
import { format } from 'date-fns';
import { Alert<PERSON>ircle, CheckCircle, XCircle, Clock, Filter, Search } from 'lucide-react';

interface AuditLog {
  id: string;
  timestamp: string;
  eventType: 'hotel_created' | 'subscription_changed' | 'login_attempt' | 'payment_failed' | 'system_alert';
  status: 'success' | 'warning' | 'error' | 'info';
  description: string;
  user: {
    name: string;
    role: string;
  };
  metadata?: Record<string, any>;
}

const mockAuditLogs: AuditLog[] = [
  {
    id: '1',
    timestamp: '2024-03-15T10:30:00Z',
    eventType: 'hotel_created',
    status: 'success',
    description: 'New hotel "Grand Plaza" registered successfully',
    user: {
      name: '<PERSON>',
      role: 'hotel_manager'
    },
    metadata: {
      hotelName: 'Grand Plaza',
      location: 'New York'
    }
  },
  {
    id: '2',
    timestamp: '2024-03-15T09:45:00Z',
    eventType: 'subscription_changed',
    status: 'info',
    description: 'Subscription upgraded to Premium plan',
    user: {
      name: '<PERSON>',
      role: 'super_admin'
    },
    metadata: {
      oldPlan: 'basic',
      newPlan: 'premium'
    }
  },
  {
    id: '3',
    timestamp: '2024-03-15T08:15:00Z',
    eventType: 'login_attempt',
    status: 'error',
    description: 'Failed login attempt detected',
    user: {
      name: 'Unknown',
      role: 'unknown'
    },
    metadata: {
      ipAddress: '***********',
      attempts: 3
    }
  },
  {
    id: '4',
    timestamp: '2024-03-14T23:30:00Z',
    eventType: 'payment_failed',
    status: 'warning',
    description: 'Payment processing failed for invoice #INV-2024-003',
    user: {
      name: 'Michael Chen',
      role: 'hotel_manager'
    },
    metadata: {
      invoiceNumber: 'INV-2024-003',
      amount: 99.99
    }
  },
  {
    id: '5',
    timestamp: '2024-03-14T22:00:00Z',
    eventType: 'system_alert',
    status: 'warning',
    description: 'High CPU usage detected on main server',
    user: {
      name: 'System',
      role: 'system'
    },
    metadata: {
      cpuUsage: '92%',
      duration: '5 minutes'
    }
  }
];

export default function NotificationCenter() {
  const [logs, setLogs] = useState<AuditLog[]>(mockAuditLogs);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterEventType, setFilterEventType] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('');
  const [filterRole, setFilterRole] = useState<string>('');

  const getStatusIcon = (status: AuditLog['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-blue-500" />;
    }
  };

  const getStatusColor = (status: AuditLog['status']) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  const filteredLogs = logs.filter(log => {
    const matchesSearch = !searchTerm || 
      log.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.user.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesEventType = !filterEventType || log.eventType === filterEventType;
    const matchesStatus = !filterStatus || log.status === filterStatus;
    const matchesRole = !filterRole || log.user.role === filterRole;
    return matchesSearch && matchesEventType && matchesStatus && matchesRole;
  });

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="bg-white rounded-lg shadow p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="relative col-span-2">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search audit logs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
            <select
              value={filterEventType}
              onChange={(e) => setFilterEventType(e.target.value)}
              className="rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="">All Event Types</option>
              <option value="hotel_created">Hotel Created</option>
              <option value="subscription_changed">Subscription Changed</option>
              <option value="login_attempt">Login Attempt</option>
              <option value="payment_failed">Payment Failed</option>
              <option value="system_alert">System Alert</option>
            </select>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="">All Statuses</option>
              <option value="success">Success</option>
              <option value="warning">Warning</option>
              <option value="error">Error</option>
              <option value="info">Info</option>
            </select>
            <select
              value={filterRole}
              onChange={(e) => setFilterRole(e.target.value)}
              className="rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="">All Roles</option>
              <option value="super_admin">Super Admin</option>
              <option value="hotel_manager">Hotel Manager</option>
              <option value="staff">Staff</option>
              <option value="system">System</option>
            </select>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b flex items-center justify-between">
          <h2 className="text-lg font-medium text-gray-900">Audit Log</h2>
          <Filter className="w-5 h-5 text-gray-500" />
        </div>
        <div className="divide-y divide-gray-200">
          {filteredLogs.map((log) => (
            <div key={log.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  {getStatusIcon(log.status)}
                </div>
                <div className="ml-4 flex-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900">{log.description}</p>
                    <span className="text-sm text-gray-500">
                      {format(new Date(log.timestamp), 'MMM d, yyyy HH:mm')}
                    </span>
                  </div>
                  <div className="mt-2 flex items-center space-x-4">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(log.status)}`}>
                      {log.status.charAt(0).toUpperCase() + log.status.slice(1)}
                    </span>
                    <span className="text-sm text-gray-500">
                      {log.user.name} ({log.user.role})
                    </span>
                  </div>
                  {log.metadata && (
                    <div className="mt-2 text-sm text-gray-500">
                      <details className="cursor-pointer">
                        <summary className="text-blue-600 hover:text-blue-800">
                          View Details
                        </summary>
                        <div className="mt-2 pl-4 border-l-2 border-gray-200">
                          {Object.entries(log.metadata).map(([key, value]) => (
                            <div key={key} className="flex items-center justify-between py-1">
                              <span className="font-medium">{key}:</span>
                              <span>{value}</span>
                            </div>
                          ))}
                        </div>
                      </details>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}