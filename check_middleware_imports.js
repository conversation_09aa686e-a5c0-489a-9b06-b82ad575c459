#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Fonction pour vérifier si un fichier existe
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

// Fonction pour extraire les imports de middleware d'un fichier de route
function extractMiddlewareImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const imports = [];
    
    // Regex pour capturer les imports de middleware
    const requireRegex = /require\(['"`]([^'"`]+middleware[^'"`]*?)['"`]\)/g;
    let match;
    
    while ((match = requireRegex.exec(content)) !== null) {
      imports.push({
        import: match[1],
        line: content.substring(0, match.index).split('\n').length
      });
    }
    
    return imports;
  } catch (error) {
    console.error(`Erreur lecture fichier ${filePath}:`, error.message);
    return [];
  }
}

// Fonction pour vérifier les imports destructurés
function extractDestructuredImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const imports = [];
    
    // Regex pour capturer les imports destructurés
    const destructureRegex = /const\s*\{\s*([^}]+)\s*\}\s*=\s*require\(['"`]([^'"`]+middleware[^'"`]*?)['"`]\)/g;
    let match;
    
    while ((match = destructureRegex.exec(content)) !== null) {
      const middlewarePath = match[2];
      const destructuredItems = match[1].split(',').map(item => item.trim());
      
      imports.push({
        middlewarePath,
        items: destructuredItems,
        line: content.substring(0, match.index).split('\n').length
      });
    }
    
    return imports;
  } catch (error) {
    console.error(`Erreur lecture fichier ${filePath}:`, error.message);
    return [];
  }
}

// Fonction principale
function checkMiddlewareImports() {
  const routesDir = path.join(__dirname, 'backend', 'routes');
  const middlewareDir = path.join(__dirname, 'backend', 'middleware');
  
  console.log('🔍 Vérification des imports de middleware...\n');
  
  if (!fs.existsSync(routesDir)) {
    console.error('❌ Dossier routes non trouvé:', routesDir);
    return;
  }
  
  if (!fs.existsSync(middlewareDir)) {
    console.error('❌ Dossier middleware non trouvé:', middlewareDir);
    return;
  }
  
  const routeFiles = fs.readdirSync(routesDir).filter(file => file.endsWith('.js'));
  let hasErrors = false;
  
  routeFiles.forEach(routeFile => {
    const routePath = path.join(routesDir, routeFile);
    console.log(`📁 Vérification de ${routeFile}:`);
    
    // Vérifier les imports simples
    const simpleImports = extractMiddlewareImports(routePath);
    simpleImports.forEach(({ import: importPath, line }) => {
      const fullPath = path.resolve(path.dirname(routePath), importPath + '.js');
      if (!fileExists(fullPath)) {
        console.error(`  ❌ Ligne ${line}: Middleware non trouvé: ${importPath}`);
        hasErrors = true;
      } else {
        console.log(`  ✅ Ligne ${line}: ${importPath}`);
      }
    });
    
    // Vérifier les imports destructurés
    const destructuredImports = extractDestructuredImports(routePath);
    destructuredImports.forEach(({ middlewarePath, items, line }) => {
      const fullPath = path.resolve(path.dirname(routePath), middlewarePath + '.js');
      
      if (!fileExists(fullPath)) {
        console.error(`  ❌ Ligne ${line}: Fichier middleware non trouvé: ${middlewarePath}`);
        hasErrors = true;
      } else {
        console.log(`  ✅ Ligne ${line}: ${middlewarePath}`);
        
        // Vérifier si les fonctions exportées existent
        try {
          const middlewareContent = fs.readFileSync(fullPath, 'utf8');
          
          items.forEach(item => {
            const cleanItem = item.replace(/\s+/g, '');
            if (!middlewareContent.includes(`${cleanItem}`) && !middlewareContent.includes(`"${cleanItem}"`)) {
              console.warn(`  ⚠️  Fonction potentiellement manquante: ${cleanItem}`);
            }
          });
        } catch (error) {
          console.error(`  ❌ Erreur lecture middleware ${middlewarePath}:`, error.message);
          hasErrors = true;
        }
      }
    });
    
    console.log('');
  });
  
  if (hasErrors) {
    console.log('❌ Des erreurs ont été détectées dans les imports de middleware.');
    process.exit(1);
  } else {
    console.log('✅ Tous les imports de middleware sont valides.');
  }
}

// Exécuter la vérification
checkMiddlewareImports();
