openapi: 3.0.0
info:
  title: API Generated from input.sql
  description: REST API automatically generated from PostgreSQL schema in input.sql
  version: 1.0.0
  contact:
    name: API Generator
  license:
    name: MIT
paths:
  /utilisateurssuperadmin:
    get:
      summary: List all UtilisateursSuperAdmin
      operationId: listUtilisateursSuperAdmin
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of UtilisateursSuperAdmin items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UtilisateursSuperAdmin'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new UtilisateursSuperAdmin
      operationId: createUtilisateursSuperAdmin
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UtilisateursSuperAdmin'
      responses:
        '201':
          description: Created UtilisateursSuperAdmin
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UtilisateursSuperAdmin'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /utilisateurssuperadmin/{id}:
    get:
      summary: Get a specific UtilisateursSuperAdmin by ID
      operationId: getUtilisateursSuperAdmin
      parameters:
      - &id001
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: UtilisateursSuperAdmin item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UtilisateursSuperAdmin'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a UtilisateursSuperAdmin
      operationId: replaceUtilisateursSuperAdmin
      parameters:
      - *id001
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UtilisateursSuperAdmin'
      responses:
        '200':
          description: Updated UtilisateursSuperAdmin
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UtilisateursSuperAdmin'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a UtilisateursSuperAdmin partially
      operationId: updateUtilisateursSuperAdmin
      parameters:
      - *id001
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UtilisateursSuperAdmin'
      responses:
        '200':
          description: Updated UtilisateursSuperAdmin
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UtilisateursSuperAdmin'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a UtilisateursSuperAdmin
      operationId: deleteUtilisateursSuperAdmin
      parameters:
      - *id001
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /utilisateurssuperadmin/{id}/chaineshotelieres:
    get:
      summary: Get ChainesHotelieres items related to UtilisateursSuperAdmin
      operationId: getUtilisateursSuperAdminChainesHotelieres
      parameters:
      - *id001
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related ChainesHotelieres items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ChainesHotelieres'
        '404':
          $ref: '#/components/responses/Error'
  /chaineshotelieres:
    get:
      summary: List all ChainesHotelieres
      operationId: listChainesHotelieres
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of ChainesHotelieres items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ChainesHotelieres'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new ChainesHotelieres
      operationId: createChainesHotelieres
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChainesHotelieres'
      responses:
        '201':
          description: Created ChainesHotelieres
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChainesHotelieres'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /chaineshotelieres/{id}:
    get:
      summary: Get a specific ChainesHotelieres by ID
      operationId: getChainesHotelieres
      parameters:
      - &id002
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: ChainesHotelieres item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChainesHotelieres'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a ChainesHotelieres
      operationId: replaceChainesHotelieres
      parameters:
      - *id002
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChainesHotelieres'
      responses:
        '200':
          description: Updated ChainesHotelieres
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChainesHotelieres'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a ChainesHotelieres partially
      operationId: updateChainesHotelieres
      parameters:
      - *id002
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChainesHotelieres'
      responses:
        '200':
          description: Updated ChainesHotelieres
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChainesHotelieres'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a ChainesHotelieres
      operationId: deleteChainesHotelieres
      parameters:
      - *id002
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /chaineshotelieres/{id}/adminschaine:
    get:
      summary: Get AdminsChaine items related to ChainesHotelieres
      operationId: getChainesHotelieresAdminsChaine
      parameters:
      - *id002
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related AdminsChaine items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminsChaine'
        '404':
          $ref: '#/components/responses/Error'
  /chaineshotelieres/{id}/complexeshoteliers:
    get:
      summary: Get ComplexesHoteliers items related to ChainesHotelieres
      operationId: getChainesHotelieresComplexesHoteliers
      parameters:
      - *id002
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related ComplexesHoteliers items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ComplexesHoteliers'
        '404':
          $ref: '#/components/responses/Error'
  /chaineshotelieres/{id}/categoriesproduits:
    get:
      summary: Get CategoriesProduits items related to ChainesHotelieres
      operationId: getChainesHotelieresCategoriesProduits
      parameters:
      - *id002
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related CategoriesProduits items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CategoriesProduits'
        '404':
          $ref: '#/components/responses/Error'
  /chaineshotelieres/{id}/produits:
    get:
      summary: Get Produits items related to ChainesHotelieres
      operationId: getChainesHotelieresProduits
      parameters:
      - *id002
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Produits items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Produits'
        '404':
          $ref: '#/components/responses/Error'
  /chaineshotelieres/{id}/clients:
    get:
      summary: Get Clients items related to ChainesHotelieres
      operationId: getChainesHotelieresClients
      parameters:
      - *id002
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Clients items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Clients'
        '404':
          $ref: '#/components/responses/Error'
  /chaineshotelieres/{id}/fidelite:
    get:
      summary: Get Fidelite items related to ChainesHotelieres
      operationId: getChainesHotelieresFidelite
      parameters:
      - *id002
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Fidelite items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Fidelite'
        '404':
          $ref: '#/components/responses/Error'
  /chaineshotelieres/{id}/codespromo:
    get:
      summary: Get CodesPromo items related to ChainesHotelieres
      operationId: getChainesHotelieresCodesPromo
      parameters:
      - *id002
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related CodesPromo items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CodesPromo'
        '404':
          $ref: '#/components/responses/Error'
  /chaineshotelieres/{id}/utilisationscodes:
    get:
      summary: Get UtilisationsCodes items related to ChainesHotelieres
      operationId: getChainesHotelieresUtilisationsCodes
      parameters:
      - *id002
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related UtilisationsCodes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UtilisationsCodes'
        '404':
          $ref: '#/components/responses/Error'
  /chaineshotelieres/{id}/fournisseurs:
    get:
      summary: Get Fournisseurs items related to ChainesHotelieres
      operationId: getChainesHotelieresFournisseurs
      parameters:
      - *id002
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Fournisseurs items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Fournisseurs'
        '404':
          $ref: '#/components/responses/Error'
  /chaineshotelieres/{id}/commandesfournisseurs:
    get:
      summary: Get CommandesFournisseurs items related to ChainesHotelieres
      operationId: getChainesHotelieresCommandesFournisseurs
      parameters:
      - *id002
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related CommandesFournisseurs items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CommandesFournisseurs'
        '404':
          $ref: '#/components/responses/Error'
  /chaineshotelieres/{id}/mouvementsstock:
    get:
      summary: Get MouvementsStock items related to ChainesHotelieres
      operationId: getChainesHotelieresMouvementsStock
      parameters:
      - *id002
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related MouvementsStock items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MouvementsStock'
        '404':
          $ref: '#/components/responses/Error'
  /chaineshotelieres/{id}/inventaires:
    get:
      summary: Get Inventaires items related to ChainesHotelieres
      operationId: getChainesHotelieresInventaires
      parameters:
      - *id002
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Inventaires items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Inventaires'
        '404':
          $ref: '#/components/responses/Error'
  /adminschaine:
    get:
      summary: List all AdminsChaine
      operationId: listAdminsChaine
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of AdminsChaine items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminsChaine'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new AdminsChaine
      operationId: createAdminsChaine
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminsChaine'
      responses:
        '201':
          description: Created AdminsChaine
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminsChaine'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /adminschaine/{id}:
    get:
      summary: Get a specific AdminsChaine by ID
      operationId: getAdminsChaine
      parameters:
      - &id003
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: AdminsChaine item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminsChaine'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a AdminsChaine
      operationId: replaceAdminsChaine
      parameters:
      - *id003
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminsChaine'
      responses:
        '200':
          description: Updated AdminsChaine
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminsChaine'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a AdminsChaine partially
      operationId: updateAdminsChaine
      parameters:
      - *id003
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminsChaine'
      responses:
        '200':
          description: Updated AdminsChaine
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminsChaine'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a AdminsChaine
      operationId: deleteAdminsChaine
      parameters:
      - *id003
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers:
    get:
      summary: List all ComplexesHoteliers
      operationId: listComplexesHoteliers
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of ComplexesHoteliers items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ComplexesHoteliers'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new ComplexesHoteliers
      operationId: createComplexesHoteliers
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ComplexesHoteliers'
      responses:
        '201':
          description: Created ComplexesHoteliers
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComplexesHoteliers'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}:
    get:
      summary: Get a specific ComplexesHoteliers by ID
      operationId: getComplexesHoteliers
      parameters:
      - &id004
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: ComplexesHoteliers item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComplexesHoteliers'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a ComplexesHoteliers
      operationId: replaceComplexesHoteliers
      parameters:
      - *id004
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ComplexesHoteliers'
      responses:
        '200':
          description: Updated ComplexesHoteliers
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComplexesHoteliers'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a ComplexesHoteliers partially
      operationId: updateComplexesHoteliers
      parameters:
      - *id004
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ComplexesHoteliers'
      responses:
        '200':
          description: Updated ComplexesHoteliers
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComplexesHoteliers'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a ComplexesHoteliers
      operationId: deleteComplexesHoteliers
      parameters:
      - *id004
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/servicescomplexe:
    get:
      summary: Get ServicesComplexe items related to ComplexesHoteliers
      operationId: getComplexesHoteliersServicesComplexe
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related ServicesComplexe items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ServicesComplexe'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/adminscomplexe:
    get:
      summary: Get AdminsComplexe items related to ComplexesHoteliers
      operationId: getComplexesHoteliersAdminsComplexe
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related AdminsComplexe items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminsComplexe'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/rolescomplexe:
    get:
      summary: Get RolesComplexe items related to ComplexesHoteliers
      operationId: getComplexesHoteliersRolesComplexe
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related RolesComplexe items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RolesComplexe'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/employes:
    get:
      summary: Get Employes items related to ComplexesHoteliers
      operationId: getComplexesHoteliersEmployes
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Employes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Employes'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/pointsdevente:
    get:
      summary: Get PointsDeVente items related to ComplexesHoteliers
      operationId: getComplexesHoteliersPointsDeVente
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related PointsDeVente items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PointsDeVente'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/sessionscaisse:
    get:
      summary: Get SessionsCaisse items related to ComplexesHoteliers
      operationId: getComplexesHoteliersSessionsCaisse
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related SessionsCaisse items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SessionsCaisse'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/categoriesproduits:
    get:
      summary: Get CategoriesProduits items related to ComplexesHoteliers
      operationId: getComplexesHoteliersCategoriesProduits
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related CategoriesProduits items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CategoriesProduits'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/produits:
    get:
      summary: Get Produits items related to ComplexesHoteliers
      operationId: getComplexesHoteliersProduits
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Produits items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Produits'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/prixproduits:
    get:
      summary: Get PrixProduits items related to ComplexesHoteliers
      operationId: getComplexesHoteliersPrixProduits
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related PrixProduits items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PrixProduits'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/chambres:
    get:
      summary: Get Chambres items related to ComplexesHoteliers
      operationId: getComplexesHoteliersChambres
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Chambres items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Chambres'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/clients:
    get:
      summary: Get Clients items related to ComplexesHoteliers
      operationId: getComplexesHoteliersClients
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Clients items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Clients'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/reservations:
    get:
      summary: Get Reservations items related to ComplexesHoteliers
      operationId: getComplexesHoteliersReservations
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Reservations items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Reservations'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/transactionspos:
    get:
      summary: Get TransactionsPOS items related to ComplexesHoteliers
      operationId: getComplexesHoteliersTransactionsPOS
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related TransactionsPOS items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TransactionsPOS'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/codespromo:
    get:
      summary: Get CodesPromo items related to ComplexesHoteliers
      operationId: getComplexesHoteliersCodesPromo
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related CodesPromo items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CodesPromo'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/utilisationscodes:
    get:
      summary: Get UtilisationsCodes items related to ComplexesHoteliers
      operationId: getComplexesHoteliersUtilisationsCodes
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related UtilisationsCodes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UtilisationsCodes'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/fournisseurs:
    get:
      summary: Get Fournisseurs items related to ComplexesHoteliers
      operationId: getComplexesHoteliersFournisseurs
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Fournisseurs items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Fournisseurs'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/commandesfournisseurs:
    get:
      summary: Get CommandesFournisseurs items related to ComplexesHoteliers
      operationId: getComplexesHoteliersCommandesFournisseurs
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related CommandesFournisseurs items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CommandesFournisseurs'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/mouvementsstock:
    get:
      summary: Get MouvementsStock items related to ComplexesHoteliers
      operationId: getComplexesHoteliersMouvementsStock
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related MouvementsStock items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MouvementsStock'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/inventaires:
    get:
      summary: Get Inventaires items related to ComplexesHoteliers
      operationId: getComplexesHoteliersInventaires
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Inventaires items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Inventaires'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/tables:
    get:
      summary: Get Tables items related to ComplexesHoteliers
      operationId: getComplexesHoteliersTables
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Tables items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Tables'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/reservationstables:
    get:
      summary: Get ReservationsTables items related to ComplexesHoteliers
      operationId: getComplexesHoteliersReservationsTables
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related ReservationsTables items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ReservationsTables'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/commandes:
    get:
      summary: Get Commandes items related to ComplexesHoteliers
      operationId: getComplexesHoteliersCommandes
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Commandes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Commandes'
        '404':
          $ref: '#/components/responses/Error'
  /complexeshoteliers/{id}/detailscommandes:
    get:
      summary: Get DetailsCommandes items related to ComplexesHoteliers
      operationId: getComplexesHoteliersDetailsCommandes
      parameters:
      - *id004
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related DetailsCommandes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DetailsCommandes'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe:
    get:
      summary: List all ServicesComplexe
      operationId: listServicesComplexe
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of ServicesComplexe items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ServicesComplexe'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new ServicesComplexe
      operationId: createServicesComplexe
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServicesComplexe'
      responses:
        '201':
          description: Created ServicesComplexe
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServicesComplexe'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}:
    get:
      summary: Get a specific ServicesComplexe by ID
      operationId: getServicesComplexe
      parameters:
      - &id005
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: ServicesComplexe item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServicesComplexe'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a ServicesComplexe
      operationId: replaceServicesComplexe
      parameters:
      - *id005
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServicesComplexe'
      responses:
        '200':
          description: Updated ServicesComplexe
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServicesComplexe'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a ServicesComplexe partially
      operationId: updateServicesComplexe
      parameters:
      - *id005
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServicesComplexe'
      responses:
        '200':
          description: Updated ServicesComplexe
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServicesComplexe'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a ServicesComplexe
      operationId: deleteServicesComplexe
      parameters:
      - *id005
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/rolescomplexe:
    get:
      summary: Get RolesComplexe items related to ServicesComplexe
      operationId: getServicesComplexeRolesComplexe
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related RolesComplexe items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RolesComplexe'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/employes:
    get:
      summary: Get Employes items related to ServicesComplexe
      operationId: getServicesComplexeEmployes
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Employes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Employes'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/pointsdevente:
    get:
      summary: Get PointsDeVente items related to ServicesComplexe
      operationId: getServicesComplexePointsDeVente
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related PointsDeVente items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PointsDeVente'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/sessionscaisse:
    get:
      summary: Get SessionsCaisse items related to ServicesComplexe
      operationId: getServicesComplexeSessionsCaisse
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related SessionsCaisse items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SessionsCaisse'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/categoriesproduits:
    get:
      summary: Get CategoriesProduits items related to ServicesComplexe
      operationId: getServicesComplexeCategoriesProduits
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related CategoriesProduits items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CategoriesProduits'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/produits:
    get:
      summary: Get Produits items related to ServicesComplexe
      operationId: getServicesComplexeProduits
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Produits items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Produits'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/prixproduits:
    get:
      summary: Get PrixProduits items related to ServicesComplexe
      operationId: getServicesComplexePrixProduits
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related PrixProduits items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PrixProduits'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/chambres:
    get:
      summary: Get Chambres items related to ServicesComplexe
      operationId: getServicesComplexeChambres
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Chambres items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Chambres'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/reservations:
    get:
      summary: Get Reservations items related to ServicesComplexe
      operationId: getServicesComplexeReservations
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Reservations items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Reservations'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/transactionspos:
    get:
      summary: Get TransactionsPOS items related to ServicesComplexe
      operationId: getServicesComplexeTransactionsPOS
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related TransactionsPOS items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TransactionsPOS'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/codespromo:
    get:
      summary: Get CodesPromo items related to ServicesComplexe
      operationId: getServicesComplexeCodesPromo
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related CodesPromo items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CodesPromo'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/utilisationscodes:
    get:
      summary: Get UtilisationsCodes items related to ServicesComplexe
      operationId: getServicesComplexeUtilisationsCodes
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related UtilisationsCodes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UtilisationsCodes'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/commandesfournisseurs:
    get:
      summary: Get CommandesFournisseurs items related to ServicesComplexe
      operationId: getServicesComplexeCommandesFournisseurs
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related CommandesFournisseurs items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CommandesFournisseurs'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/mouvementsstock:
    get:
      summary: Get MouvementsStock items related to ServicesComplexe
      operationId: getServicesComplexeMouvementsStock
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related MouvementsStock items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MouvementsStock'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/inventaires:
    get:
      summary: Get Inventaires items related to ServicesComplexe
      operationId: getServicesComplexeInventaires
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Inventaires items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Inventaires'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/tables:
    get:
      summary: Get Tables items related to ServicesComplexe
      operationId: getServicesComplexeTables
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Tables items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Tables'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/reservationstables:
    get:
      summary: Get ReservationsTables items related to ServicesComplexe
      operationId: getServicesComplexeReservationsTables
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related ReservationsTables items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ReservationsTables'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/commandes:
    get:
      summary: Get Commandes items related to ServicesComplexe
      operationId: getServicesComplexeCommandes
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Commandes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Commandes'
        '404':
          $ref: '#/components/responses/Error'
  /servicescomplexe/{id}/detailscommandes:
    get:
      summary: Get DetailsCommandes items related to ServicesComplexe
      operationId: getServicesComplexeDetailsCommandes
      parameters:
      - *id005
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related DetailsCommandes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DetailsCommandes'
        '404':
          $ref: '#/components/responses/Error'
  /adminscomplexe:
    get:
      summary: List all AdminsComplexe
      operationId: listAdminsComplexe
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of AdminsComplexe items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminsComplexe'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new AdminsComplexe
      operationId: createAdminsComplexe
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminsComplexe'
      responses:
        '201':
          description: Created AdminsComplexe
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminsComplexe'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /adminscomplexe/{id}:
    get:
      summary: Get a specific AdminsComplexe by ID
      operationId: getAdminsComplexe
      parameters:
      - &id006
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: AdminsComplexe item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminsComplexe'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a AdminsComplexe
      operationId: replaceAdminsComplexe
      parameters:
      - *id006
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminsComplexe'
      responses:
        '200':
          description: Updated AdminsComplexe
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminsComplexe'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a AdminsComplexe partially
      operationId: updateAdminsComplexe
      parameters:
      - *id006
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminsComplexe'
      responses:
        '200':
          description: Updated AdminsComplexe
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminsComplexe'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a AdminsComplexe
      operationId: deleteAdminsComplexe
      parameters:
      - *id006
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /rolescomplexe:
    get:
      summary: List all RolesComplexe
      operationId: listRolesComplexe
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of RolesComplexe items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RolesComplexe'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new RolesComplexe
      operationId: createRolesComplexe
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RolesComplexe'
      responses:
        '201':
          description: Created RolesComplexe
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RolesComplexe'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /rolescomplexe/{id}:
    get:
      summary: Get a specific RolesComplexe by ID
      operationId: getRolesComplexe
      parameters:
      - &id007
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: RolesComplexe item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RolesComplexe'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a RolesComplexe
      operationId: replaceRolesComplexe
      parameters:
      - *id007
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RolesComplexe'
      responses:
        '200':
          description: Updated RolesComplexe
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RolesComplexe'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a RolesComplexe partially
      operationId: updateRolesComplexe
      parameters:
      - *id007
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RolesComplexe'
      responses:
        '200':
          description: Updated RolesComplexe
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RolesComplexe'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a RolesComplexe
      operationId: deleteRolesComplexe
      parameters:
      - *id007
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /rolescomplexe/{id}/employes:
    get:
      summary: Get Employes items related to RolesComplexe
      operationId: getRolesComplexeEmployes
      parameters:
      - *id007
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Employes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Employes'
        '404':
          $ref: '#/components/responses/Error'
  /employes:
    get:
      summary: List all Employes
      operationId: listEmployes
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of Employes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Employes'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new Employes
      operationId: createEmployes
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Employes'
      responses:
        '201':
          description: Created Employes
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Employes'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /employes/{id}:
    get:
      summary: Get a specific Employes by ID
      operationId: getEmployes
      parameters:
      - &id008
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Employes item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Employes'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a Employes
      operationId: replaceEmployes
      parameters:
      - *id008
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Employes'
      responses:
        '200':
          description: Updated Employes
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Employes'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a Employes partially
      operationId: updateEmployes
      parameters:
      - *id008
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Employes'
      responses:
        '200':
          description: Updated Employes
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Employes'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a Employes
      operationId: deleteEmployes
      parameters:
      - *id008
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /employes/{id}/pointsdevente:
    get:
      summary: Get PointsDeVente items related to Employes
      operationId: getEmployesPointsDeVente
      parameters:
      - *id008
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related PointsDeVente items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PointsDeVente'
        '404':
          $ref: '#/components/responses/Error'
  /employes/{id}/sessionscaisse:
    get:
      summary: Get SessionsCaisse items related to Employes
      operationId: getEmployesSessionsCaisse
      parameters:
      - *id008
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related SessionsCaisse items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SessionsCaisse'
        '404':
          $ref: '#/components/responses/Error'
  /employes/{id}/reservations:
    get:
      summary: Get Reservations items related to Employes
      operationId: getEmployesReservations
      parameters:
      - *id008
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Reservations items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Reservations'
        '404':
          $ref: '#/components/responses/Error'
  /employes/{id}/transactionspos:
    get:
      summary: Get TransactionsPOS items related to Employes
      operationId: getEmployesTransactionsPOS
      parameters:
      - *id008
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related TransactionsPOS items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TransactionsPOS'
        '404':
          $ref: '#/components/responses/Error'
  /employes/{id}/utilisationscodes:
    get:
      summary: Get UtilisationsCodes items related to Employes
      operationId: getEmployesUtilisationsCodes
      parameters:
      - *id008
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related UtilisationsCodes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UtilisationsCodes'
        '404':
          $ref: '#/components/responses/Error'
  /employes/{id}/commandesfournisseurs:
    get:
      summary: Get CommandesFournisseurs items related to Employes
      operationId: getEmployesCommandesFournisseurs
      parameters:
      - *id008
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related CommandesFournisseurs items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CommandesFournisseurs'
        '404':
          $ref: '#/components/responses/Error'
  /employes/{id}/mouvementsstock:
    get:
      summary: Get MouvementsStock items related to Employes
      operationId: getEmployesMouvementsStock
      parameters:
      - *id008
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related MouvementsStock items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MouvementsStock'
        '404':
          $ref: '#/components/responses/Error'
  /employes/{id}/inventaires:
    get:
      summary: Get Inventaires items related to Employes
      operationId: getEmployesInventaires
      parameters:
      - *id008
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Inventaires items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Inventaires'
        '404':
          $ref: '#/components/responses/Error'
  /employes/{id}/reservationstables:
    get:
      summary: Get ReservationsTables items related to Employes
      operationId: getEmployesReservationsTables
      parameters:
      - *id008
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related ReservationsTables items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ReservationsTables'
        '404':
          $ref: '#/components/responses/Error'
  /employes/{id}/commandes:
    get:
      summary: Get Commandes items related to Employes
      operationId: getEmployesCommandes
      parameters:
      - *id008
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Commandes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Commandes'
        '404':
          $ref: '#/components/responses/Error'
  /employes/{id}/detailscommandes:
    get:
      summary: Get DetailsCommandes items related to Employes
      operationId: getEmployesDetailsCommandes
      parameters:
      - *id008
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related DetailsCommandes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DetailsCommandes'
        '404':
          $ref: '#/components/responses/Error'
  /pointsdevente:
    get:
      summary: List all PointsDeVente
      operationId: listPointsDeVente
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of PointsDeVente items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PointsDeVente'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new PointsDeVente
      operationId: createPointsDeVente
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PointsDeVente'
      responses:
        '201':
          description: Created PointsDeVente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PointsDeVente'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /pointsdevente/{id}:
    get:
      summary: Get a specific PointsDeVente by ID
      operationId: getPointsDeVente
      parameters:
      - &id009
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: PointsDeVente item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PointsDeVente'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a PointsDeVente
      operationId: replacePointsDeVente
      parameters:
      - *id009
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PointsDeVente'
      responses:
        '200':
          description: Updated PointsDeVente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PointsDeVente'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a PointsDeVente partially
      operationId: updatePointsDeVente
      parameters:
      - *id009
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PointsDeVente'
      responses:
        '200':
          description: Updated PointsDeVente
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PointsDeVente'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a PointsDeVente
      operationId: deletePointsDeVente
      parameters:
      - *id009
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /pointsdevente/{id}/sessionscaisse:
    get:
      summary: Get SessionsCaisse items related to PointsDeVente
      operationId: getPointsDeVenteSessionsCaisse
      parameters:
      - *id009
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related SessionsCaisse items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SessionsCaisse'
        '404':
          $ref: '#/components/responses/Error'
  /pointsdevente/{id}/transactionspos:
    get:
      summary: Get TransactionsPOS items related to PointsDeVente
      operationId: getPointsDeVenteTransactionsPOS
      parameters:
      - *id009
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related TransactionsPOS items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TransactionsPOS'
        '404':
          $ref: '#/components/responses/Error'
  /sessionscaisse:
    get:
      summary: List all SessionsCaisse
      operationId: listSessionsCaisse
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of SessionsCaisse items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SessionsCaisse'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new SessionsCaisse
      operationId: createSessionsCaisse
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SessionsCaisse'
      responses:
        '201':
          description: Created SessionsCaisse
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionsCaisse'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /sessionscaisse/{id}:
    get:
      summary: Get a specific SessionsCaisse by ID
      operationId: getSessionsCaisse
      parameters:
      - &id010
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: SessionsCaisse item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionsCaisse'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a SessionsCaisse
      operationId: replaceSessionsCaisse
      parameters:
      - *id010
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SessionsCaisse'
      responses:
        '200':
          description: Updated SessionsCaisse
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionsCaisse'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a SessionsCaisse partially
      operationId: updateSessionsCaisse
      parameters:
      - *id010
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SessionsCaisse'
      responses:
        '200':
          description: Updated SessionsCaisse
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionsCaisse'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a SessionsCaisse
      operationId: deleteSessionsCaisse
      parameters:
      - *id010
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /sessionscaisse/{id}/transactionspos:
    get:
      summary: Get TransactionsPOS items related to SessionsCaisse
      operationId: getSessionsCaisseTransactionsPOS
      parameters:
      - *id010
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related TransactionsPOS items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TransactionsPOS'
        '404':
          $ref: '#/components/responses/Error'
  /categoriesproduits:
    get:
      summary: List all CategoriesProduits
      operationId: listCategoriesProduits
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of CategoriesProduits items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CategoriesProduits'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new CategoriesProduits
      operationId: createCategoriesProduits
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategoriesProduits'
      responses:
        '201':
          description: Created CategoriesProduits
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategoriesProduits'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /categoriesproduits/{id}:
    get:
      summary: Get a specific CategoriesProduits by ID
      operationId: getCategoriesProduits
      parameters:
      - &id011
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: CategoriesProduits item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategoriesProduits'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a CategoriesProduits
      operationId: replaceCategoriesProduits
      parameters:
      - *id011
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategoriesProduits'
      responses:
        '200':
          description: Updated CategoriesProduits
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategoriesProduits'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a CategoriesProduits partially
      operationId: updateCategoriesProduits
      parameters:
      - *id011
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategoriesProduits'
      responses:
        '200':
          description: Updated CategoriesProduits
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategoriesProduits'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a CategoriesProduits
      operationId: deleteCategoriesProduits
      parameters:
      - *id011
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /categoriesproduits/{id}/categoriesproduits:
    get:
      summary: Get CategoriesProduits items related to CategoriesProduits
      operationId: getCategoriesProduitsCategoriesProduits
      parameters:
      - *id011
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related CategoriesProduits items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CategoriesProduits'
        '404':
          $ref: '#/components/responses/Error'
  /categoriesproduits/{id}/produits:
    get:
      summary: Get Produits items related to CategoriesProduits
      operationId: getCategoriesProduitsProduits
      parameters:
      - *id011
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Produits items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Produits'
        '404':
          $ref: '#/components/responses/Error'
  /produits:
    get:
      summary: List all Produits
      operationId: listProduits
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of Produits items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Produits'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new Produits
      operationId: createProduits
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Produits'
      responses:
        '201':
          description: Created Produits
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Produits'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /produits/{id}:
    get:
      summary: Get a specific Produits by ID
      operationId: getProduits
      parameters:
      - &id012
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Produits item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Produits'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a Produits
      operationId: replaceProduits
      parameters:
      - *id012
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Produits'
      responses:
        '200':
          description: Updated Produits
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Produits'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a Produits partially
      operationId: updateProduits
      parameters:
      - *id012
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Produits'
      responses:
        '200':
          description: Updated Produits
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Produits'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a Produits
      operationId: deleteProduits
      parameters:
      - *id012
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /produits/{id}/prixproduits:
    get:
      summary: Get PrixProduits items related to Produits
      operationId: getProduitsPrixProduits
      parameters:
      - *id012
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related PrixProduits items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PrixProduits'
        '404':
          $ref: '#/components/responses/Error'
  /produits/{id}/lignestransactionpos:
    get:
      summary: Get LignesTransactionPOS items related to Produits
      operationId: getProduitsLignesTransactionPOS
      parameters:
      - *id012
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related LignesTransactionPOS items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LignesTransactionPOS'
        '404':
          $ref: '#/components/responses/Error'
  /produits/{id}/detailscommandesfournisseurs:
    get:
      summary: Get DetailsCommandesFournisseurs items related to Produits
      operationId: getProduitsDetailsCommandesFournisseurs
      parameters:
      - *id012
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related DetailsCommandesFournisseurs items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DetailsCommandesFournisseurs'
        '404':
          $ref: '#/components/responses/Error'
  /produits/{id}/mouvementsstock:
    get:
      summary: Get MouvementsStock items related to Produits
      operationId: getProduitsMouvementsStock
      parameters:
      - *id012
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related MouvementsStock items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MouvementsStock'
        '404':
          $ref: '#/components/responses/Error'
  /produits/{id}/lignesinventaire:
    get:
      summary: Get LignesInventaire items related to Produits
      operationId: getProduitsLignesInventaire
      parameters:
      - *id012
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related LignesInventaire items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LignesInventaire'
        '404':
          $ref: '#/components/responses/Error'
  /produits/{id}/detailscommandes:
    get:
      summary: Get DetailsCommandes items related to Produits
      operationId: getProduitsDetailsCommandes
      parameters:
      - *id012
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related DetailsCommandes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DetailsCommandes'
        '404':
          $ref: '#/components/responses/Error'
  /prixproduits:
    get:
      summary: List all PrixProduits
      operationId: listPrixProduits
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of PrixProduits items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PrixProduits'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new PrixProduits
      operationId: createPrixProduits
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrixProduits'
      responses:
        '201':
          description: Created PrixProduits
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrixProduits'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /prixproduits/{id}:
    get:
      summary: Get a specific PrixProduits by ID
      operationId: getPrixProduits
      parameters:
      - &id013
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: PrixProduits item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrixProduits'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a PrixProduits
      operationId: replacePrixProduits
      parameters:
      - *id013
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrixProduits'
      responses:
        '200':
          description: Updated PrixProduits
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrixProduits'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a PrixProduits partially
      operationId: updatePrixProduits
      parameters:
      - *id013
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrixProduits'
      responses:
        '200':
          description: Updated PrixProduits
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrixProduits'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a PrixProduits
      operationId: deletePrixProduits
      parameters:
      - *id013
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /chambres:
    get:
      summary: List all Chambres
      operationId: listChambres
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of Chambres items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Chambres'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new Chambres
      operationId: createChambres
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Chambres'
      responses:
        '201':
          description: Created Chambres
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Chambres'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /chambres/{id}:
    get:
      summary: Get a specific Chambres by ID
      operationId: getChambres
      parameters:
      - &id014
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Chambres item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Chambres'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a Chambres
      operationId: replaceChambres
      parameters:
      - *id014
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Chambres'
      responses:
        '200':
          description: Updated Chambres
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Chambres'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a Chambres partially
      operationId: updateChambres
      parameters:
      - *id014
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Chambres'
      responses:
        '200':
          description: Updated Chambres
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Chambres'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a Chambres
      operationId: deleteChambres
      parameters:
      - *id014
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /chambres/{id}/chambresreservees:
    get:
      summary: Get ChambresReservees items related to Chambres
      operationId: getChambresChambresReservees
      parameters:
      - *id014
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related ChambresReservees items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ChambresReservees'
        '404':
          $ref: '#/components/responses/Error'
  /chambres/{id}/transactionspos:
    get:
      summary: Get TransactionsPOS items related to Chambres
      operationId: getChambresTransactionsPOS
      parameters:
      - *id014
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related TransactionsPOS items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TransactionsPOS'
        '404':
          $ref: '#/components/responses/Error'
  /chambres/{id}/commandes:
    get:
      summary: Get Commandes items related to Chambres
      operationId: getChambresCommandes
      parameters:
      - *id014
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Commandes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Commandes'
        '404':
          $ref: '#/components/responses/Error'
  /clients:
    get:
      summary: List all Clients
      operationId: listClients
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of Clients items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Clients'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new Clients
      operationId: createClients
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Clients'
      responses:
        '201':
          description: Created Clients
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Clients'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /clients/{id}:
    get:
      summary: Get a specific Clients by ID
      operationId: getClients
      parameters:
      - &id015
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Clients item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Clients'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a Clients
      operationId: replaceClients
      parameters:
      - *id015
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Clients'
      responses:
        '200':
          description: Updated Clients
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Clients'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a Clients partially
      operationId: updateClients
      parameters:
      - *id015
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Clients'
      responses:
        '200':
          description: Updated Clients
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Clients'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a Clients
      operationId: deleteClients
      parameters:
      - *id015
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /clients/{id}/reservations:
    get:
      summary: Get Reservations items related to Clients
      operationId: getClientsReservations
      parameters:
      - *id015
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Reservations items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Reservations'
        '404':
          $ref: '#/components/responses/Error'
  /clients/{id}/transactionspos:
    get:
      summary: Get TransactionsPOS items related to Clients
      operationId: getClientsTransactionsPOS
      parameters:
      - *id015
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related TransactionsPOS items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TransactionsPOS'
        '404':
          $ref: '#/components/responses/Error'
  /clients/{id}/fidelite:
    get:
      summary: Get Fidelite items related to Clients
      operationId: getClientsFidelite
      parameters:
      - *id015
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Fidelite items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Fidelite'
        '404':
          $ref: '#/components/responses/Error'
  /clients/{id}/utilisationscodes:
    get:
      summary: Get UtilisationsCodes items related to Clients
      operationId: getClientsUtilisationsCodes
      parameters:
      - *id015
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related UtilisationsCodes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UtilisationsCodes'
        '404':
          $ref: '#/components/responses/Error'
  /clients/{id}/reservationstables:
    get:
      summary: Get ReservationsTables items related to Clients
      operationId: getClientsReservationsTables
      parameters:
      - *id015
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related ReservationsTables items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ReservationsTables'
        '404':
          $ref: '#/components/responses/Error'
  /clients/{id}/commandes:
    get:
      summary: Get Commandes items related to Clients
      operationId: getClientsCommandes
      parameters:
      - *id015
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Commandes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Commandes'
        '404':
          $ref: '#/components/responses/Error'
  /reservations:
    get:
      summary: List all Reservations
      operationId: listReservations
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of Reservations items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Reservations'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new Reservations
      operationId: createReservations
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Reservations'
      responses:
        '201':
          description: Created Reservations
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Reservations'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /reservations/{id}:
    get:
      summary: Get a specific Reservations by ID
      operationId: getReservations
      parameters:
      - &id016
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Reservations item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Reservations'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a Reservations
      operationId: replaceReservations
      parameters:
      - *id016
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Reservations'
      responses:
        '200':
          description: Updated Reservations
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Reservations'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a Reservations partially
      operationId: updateReservations
      parameters:
      - *id016
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Reservations'
      responses:
        '200':
          description: Updated Reservations
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Reservations'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a Reservations
      operationId: deleteReservations
      parameters:
      - *id016
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /reservations/{id}/chambresreservees:
    get:
      summary: Get ChambresReservees items related to Reservations
      operationId: getReservationsChambresReservees
      parameters:
      - *id016
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related ChambresReservees items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ChambresReservees'
        '404':
          $ref: '#/components/responses/Error'
  /reservations/{id}/utilisationscodes:
    get:
      summary: Get UtilisationsCodes items related to Reservations
      operationId: getReservationsUtilisationsCodes
      parameters:
      - *id016
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related UtilisationsCodes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UtilisationsCodes'
        '404':
          $ref: '#/components/responses/Error'
  /chambresreservees:
    get:
      summary: List all ChambresReservees
      operationId: listChambresReservees
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of ChambresReservees items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ChambresReservees'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new ChambresReservees
      operationId: createChambresReservees
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChambresReservees'
      responses:
        '201':
          description: Created ChambresReservees
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChambresReservees'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /chambresreservees/{id}:
    get:
      summary: Get a specific ChambresReservees by ID
      operationId: getChambresReservees
      parameters:
      - &id017
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: ChambresReservees item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChambresReservees'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a ChambresReservees
      operationId: replaceChambresReservees
      parameters:
      - *id017
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChambresReservees'
      responses:
        '200':
          description: Updated ChambresReservees
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChambresReservees'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a ChambresReservees partially
      operationId: updateChambresReservees
      parameters:
      - *id017
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChambresReservees'
      responses:
        '200':
          description: Updated ChambresReservees
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChambresReservees'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a ChambresReservees
      operationId: deleteChambresReservees
      parameters:
      - *id017
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /transactionspos:
    get:
      summary: List all TransactionsPOS
      operationId: listTransactionsPOS
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of TransactionsPOS items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TransactionsPOS'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new TransactionsPOS
      operationId: createTransactionsPOS
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionsPOS'
      responses:
        '201':
          description: Created TransactionsPOS
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionsPOS'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /transactionspos/{id}:
    get:
      summary: Get a specific TransactionsPOS by ID
      operationId: getTransactionsPOS
      parameters:
      - &id018
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: TransactionsPOS item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionsPOS'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a TransactionsPOS
      operationId: replaceTransactionsPOS
      parameters:
      - *id018
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionsPOS'
      responses:
        '200':
          description: Updated TransactionsPOS
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionsPOS'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a TransactionsPOS partially
      operationId: updateTransactionsPOS
      parameters:
      - *id018
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionsPOS'
      responses:
        '200':
          description: Updated TransactionsPOS
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionsPOS'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a TransactionsPOS
      operationId: deleteTransactionsPOS
      parameters:
      - *id018
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /transactionspos/{id}/lignestransactionpos:
    get:
      summary: Get LignesTransactionPOS items related to TransactionsPOS
      operationId: getTransactionsPOSLignesTransactionPOS
      parameters:
      - *id018
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related LignesTransactionPOS items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LignesTransactionPOS'
        '404':
          $ref: '#/components/responses/Error'
  /transactionspos/{id}/paiements:
    get:
      summary: Get Paiements items related to TransactionsPOS
      operationId: getTransactionsPOSPaiements
      parameters:
      - *id018
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Paiements items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Paiements'
        '404':
          $ref: '#/components/responses/Error'
  /transactionspos/{id}/utilisationscodes:
    get:
      summary: Get UtilisationsCodes items related to TransactionsPOS
      operationId: getTransactionsPOSUtilisationsCodes
      parameters:
      - *id018
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related UtilisationsCodes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UtilisationsCodes'
        '404':
          $ref: '#/components/responses/Error'
  /lignestransactionpos:
    get:
      summary: List all LignesTransactionPOS
      operationId: listLignesTransactionPOS
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of LignesTransactionPOS items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LignesTransactionPOS'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new LignesTransactionPOS
      operationId: createLignesTransactionPOS
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LignesTransactionPOS'
      responses:
        '201':
          description: Created LignesTransactionPOS
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LignesTransactionPOS'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /lignestransactionpos/{id}:
    get:
      summary: Get a specific LignesTransactionPOS by ID
      operationId: getLignesTransactionPOS
      parameters:
      - &id019
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: LignesTransactionPOS item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LignesTransactionPOS'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a LignesTransactionPOS
      operationId: replaceLignesTransactionPOS
      parameters:
      - *id019
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LignesTransactionPOS'
      responses:
        '200':
          description: Updated LignesTransactionPOS
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LignesTransactionPOS'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a LignesTransactionPOS partially
      operationId: updateLignesTransactionPOS
      parameters:
      - *id019
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LignesTransactionPOS'
      responses:
        '200':
          description: Updated LignesTransactionPOS
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LignesTransactionPOS'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a LignesTransactionPOS
      operationId: deleteLignesTransactionPOS
      parameters:
      - *id019
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /paiements:
    get:
      summary: List all Paiements
      operationId: listPaiements
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of Paiements items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Paiements'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new Paiements
      operationId: createPaiements
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Paiements'
      responses:
        '201':
          description: Created Paiements
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Paiements'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /paiements/{id}:
    get:
      summary: Get a specific Paiements by ID
      operationId: getPaiements
      parameters:
      - &id020
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Paiements item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Paiements'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a Paiements
      operationId: replacePaiements
      parameters:
      - *id020
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Paiements'
      responses:
        '200':
          description: Updated Paiements
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Paiements'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a Paiements partially
      operationId: updatePaiements
      parameters:
      - *id020
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Paiements'
      responses:
        '200':
          description: Updated Paiements
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Paiements'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a Paiements
      operationId: deletePaiements
      parameters:
      - *id020
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /fidelite:
    get:
      summary: List all Fidelite
      operationId: listFidelite
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of Fidelite items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Fidelite'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new Fidelite
      operationId: createFidelite
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Fidelite'
      responses:
        '201':
          description: Created Fidelite
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Fidelite'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /fidelite/{id}:
    get:
      summary: Get a specific Fidelite by ID
      operationId: getFidelite
      parameters:
      - &id021
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Fidelite item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Fidelite'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a Fidelite
      operationId: replaceFidelite
      parameters:
      - *id021
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Fidelite'
      responses:
        '200':
          description: Updated Fidelite
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Fidelite'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a Fidelite partially
      operationId: updateFidelite
      parameters:
      - *id021
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Fidelite'
      responses:
        '200':
          description: Updated Fidelite
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Fidelite'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a Fidelite
      operationId: deleteFidelite
      parameters:
      - *id021
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /codespromo:
    get:
      summary: List all CodesPromo
      operationId: listCodesPromo
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of CodesPromo items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CodesPromo'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new CodesPromo
      operationId: createCodesPromo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CodesPromo'
      responses:
        '201':
          description: Created CodesPromo
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CodesPromo'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /codespromo/{id}:
    get:
      summary: Get a specific CodesPromo by ID
      operationId: getCodesPromo
      parameters:
      - &id022
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: CodesPromo item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CodesPromo'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a CodesPromo
      operationId: replaceCodesPromo
      parameters:
      - *id022
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CodesPromo'
      responses:
        '200':
          description: Updated CodesPromo
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CodesPromo'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a CodesPromo partially
      operationId: updateCodesPromo
      parameters:
      - *id022
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CodesPromo'
      responses:
        '200':
          description: Updated CodesPromo
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CodesPromo'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a CodesPromo
      operationId: deleteCodesPromo
      parameters:
      - *id022
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /codespromo/{id}/reservations:
    get:
      summary: Get Reservations items related to CodesPromo
      operationId: getCodesPromoReservations
      parameters:
      - *id022
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Reservations items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Reservations'
        '404':
          $ref: '#/components/responses/Error'
  /codespromo/{id}/transactionspos:
    get:
      summary: Get TransactionsPOS items related to CodesPromo
      operationId: getCodesPromoTransactionsPOS
      parameters:
      - *id022
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related TransactionsPOS items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TransactionsPOS'
        '404':
          $ref: '#/components/responses/Error'
  /codespromo/{id}/lignestransactionpos:
    get:
      summary: Get LignesTransactionPOS items related to CodesPromo
      operationId: getCodesPromoLignesTransactionPOS
      parameters:
      - *id022
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related LignesTransactionPOS items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LignesTransactionPOS'
        '404':
          $ref: '#/components/responses/Error'
  /codespromo/{id}/utilisationscodes:
    get:
      summary: Get UtilisationsCodes items related to CodesPromo
      operationId: getCodesPromoUtilisationsCodes
      parameters:
      - *id022
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related UtilisationsCodes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UtilisationsCodes'
        '404':
          $ref: '#/components/responses/Error'
  /codespromo/{id}/commandes:
    get:
      summary: Get Commandes items related to CodesPromo
      operationId: getCodesPromoCommandes
      parameters:
      - *id022
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Commandes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Commandes'
        '404':
          $ref: '#/components/responses/Error'
  /codespromo/{id}/detailscommandes:
    get:
      summary: Get DetailsCommandes items related to CodesPromo
      operationId: getCodesPromoDetailsCommandes
      parameters:
      - *id022
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related DetailsCommandes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DetailsCommandes'
        '404':
          $ref: '#/components/responses/Error'
  /utilisationscodes:
    get:
      summary: List all UtilisationsCodes
      operationId: listUtilisationsCodes
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of UtilisationsCodes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UtilisationsCodes'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new UtilisationsCodes
      operationId: createUtilisationsCodes
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UtilisationsCodes'
      responses:
        '201':
          description: Created UtilisationsCodes
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UtilisationsCodes'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /utilisationscodes/{id}:
    get:
      summary: Get a specific UtilisationsCodes by ID
      operationId: getUtilisationsCodes
      parameters:
      - &id023
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: UtilisationsCodes item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UtilisationsCodes'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a UtilisationsCodes
      operationId: replaceUtilisationsCodes
      parameters:
      - *id023
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UtilisationsCodes'
      responses:
        '200':
          description: Updated UtilisationsCodes
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UtilisationsCodes'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a UtilisationsCodes partially
      operationId: updateUtilisationsCodes
      parameters:
      - *id023
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UtilisationsCodes'
      responses:
        '200':
          description: Updated UtilisationsCodes
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UtilisationsCodes'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a UtilisationsCodes
      operationId: deleteUtilisationsCodes
      parameters:
      - *id023
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /fournisseurs:
    get:
      summary: List all Fournisseurs
      operationId: listFournisseurs
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of Fournisseurs items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Fournisseurs'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new Fournisseurs
      operationId: createFournisseurs
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Fournisseurs'
      responses:
        '201':
          description: Created Fournisseurs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Fournisseurs'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /fournisseurs/{id}:
    get:
      summary: Get a specific Fournisseurs by ID
      operationId: getFournisseurs
      parameters:
      - &id024
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Fournisseurs item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Fournisseurs'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a Fournisseurs
      operationId: replaceFournisseurs
      parameters:
      - *id024
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Fournisseurs'
      responses:
        '200':
          description: Updated Fournisseurs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Fournisseurs'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a Fournisseurs partially
      operationId: updateFournisseurs
      parameters:
      - *id024
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Fournisseurs'
      responses:
        '200':
          description: Updated Fournisseurs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Fournisseurs'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a Fournisseurs
      operationId: deleteFournisseurs
      parameters:
      - *id024
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /fournisseurs/{id}/commandesfournisseurs:
    get:
      summary: Get CommandesFournisseurs items related to Fournisseurs
      operationId: getFournisseursCommandesFournisseurs
      parameters:
      - *id024
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related CommandesFournisseurs items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CommandesFournisseurs'
        '404':
          $ref: '#/components/responses/Error'
  /commandesfournisseurs:
    get:
      summary: List all CommandesFournisseurs
      operationId: listCommandesFournisseurs
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of CommandesFournisseurs items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CommandesFournisseurs'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new CommandesFournisseurs
      operationId: createCommandesFournisseurs
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommandesFournisseurs'
      responses:
        '201':
          description: Created CommandesFournisseurs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommandesFournisseurs'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /commandesfournisseurs/{id}:
    get:
      summary: Get a specific CommandesFournisseurs by ID
      operationId: getCommandesFournisseurs
      parameters:
      - &id025
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: CommandesFournisseurs item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommandesFournisseurs'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a CommandesFournisseurs
      operationId: replaceCommandesFournisseurs
      parameters:
      - *id025
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommandesFournisseurs'
      responses:
        '200':
          description: Updated CommandesFournisseurs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommandesFournisseurs'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a CommandesFournisseurs partially
      operationId: updateCommandesFournisseurs
      parameters:
      - *id025
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommandesFournisseurs'
      responses:
        '200':
          description: Updated CommandesFournisseurs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommandesFournisseurs'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a CommandesFournisseurs
      operationId: deleteCommandesFournisseurs
      parameters:
      - *id025
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /commandesfournisseurs/{id}/detailscommandesfournisseurs:
    get:
      summary: Get DetailsCommandesFournisseurs items related to CommandesFournisseurs
      operationId: getCommandesFournisseursDetailsCommandesFournisseurs
      parameters:
      - *id025
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related DetailsCommandesFournisseurs items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DetailsCommandesFournisseurs'
        '404':
          $ref: '#/components/responses/Error'
  /detailscommandesfournisseurs:
    get:
      summary: List all DetailsCommandesFournisseurs
      operationId: listDetailsCommandesFournisseurs
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of DetailsCommandesFournisseurs items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DetailsCommandesFournisseurs'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new DetailsCommandesFournisseurs
      operationId: createDetailsCommandesFournisseurs
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DetailsCommandesFournisseurs'
      responses:
        '201':
          description: Created DetailsCommandesFournisseurs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailsCommandesFournisseurs'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /detailscommandesfournisseurs/{id}:
    get:
      summary: Get a specific DetailsCommandesFournisseurs by ID
      operationId: getDetailsCommandesFournisseurs
      parameters:
      - &id026
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: DetailsCommandesFournisseurs item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailsCommandesFournisseurs'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a DetailsCommandesFournisseurs
      operationId: replaceDetailsCommandesFournisseurs
      parameters:
      - *id026
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DetailsCommandesFournisseurs'
      responses:
        '200':
          description: Updated DetailsCommandesFournisseurs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailsCommandesFournisseurs'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a DetailsCommandesFournisseurs partially
      operationId: updateDetailsCommandesFournisseurs
      parameters:
      - *id026
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DetailsCommandesFournisseurs'
      responses:
        '200':
          description: Updated DetailsCommandesFournisseurs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailsCommandesFournisseurs'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a DetailsCommandesFournisseurs
      operationId: deleteDetailsCommandesFournisseurs
      parameters:
      - *id026
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /mouvementsstock:
    get:
      summary: List all MouvementsStock
      operationId: listMouvementsStock
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of MouvementsStock items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MouvementsStock'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new MouvementsStock
      operationId: createMouvementsStock
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MouvementsStock'
      responses:
        '201':
          description: Created MouvementsStock
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MouvementsStock'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /mouvementsstock/{id}:
    get:
      summary: Get a specific MouvementsStock by ID
      operationId: getMouvementsStock
      parameters:
      - &id027
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: MouvementsStock item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MouvementsStock'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a MouvementsStock
      operationId: replaceMouvementsStock
      parameters:
      - *id027
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MouvementsStock'
      responses:
        '200':
          description: Updated MouvementsStock
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MouvementsStock'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a MouvementsStock partially
      operationId: updateMouvementsStock
      parameters:
      - *id027
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MouvementsStock'
      responses:
        '200':
          description: Updated MouvementsStock
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MouvementsStock'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a MouvementsStock
      operationId: deleteMouvementsStock
      parameters:
      - *id027
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /inventaires:
    get:
      summary: List all Inventaires
      operationId: listInventaires
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of Inventaires items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Inventaires'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new Inventaires
      operationId: createInventaires
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Inventaires'
      responses:
        '201':
          description: Created Inventaires
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Inventaires'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /inventaires/{id}:
    get:
      summary: Get a specific Inventaires by ID
      operationId: getInventaires
      parameters:
      - &id028
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Inventaires item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Inventaires'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a Inventaires
      operationId: replaceInventaires
      parameters:
      - *id028
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Inventaires'
      responses:
        '200':
          description: Updated Inventaires
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Inventaires'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a Inventaires partially
      operationId: updateInventaires
      parameters:
      - *id028
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Inventaires'
      responses:
        '200':
          description: Updated Inventaires
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Inventaires'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a Inventaires
      operationId: deleteInventaires
      parameters:
      - *id028
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /inventaires/{id}/lignesinventaire:
    get:
      summary: Get LignesInventaire items related to Inventaires
      operationId: getInventairesLignesInventaire
      parameters:
      - *id028
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related LignesInventaire items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LignesInventaire'
        '404':
          $ref: '#/components/responses/Error'
  /lignesinventaire:
    get:
      summary: List all LignesInventaire
      operationId: listLignesInventaire
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of LignesInventaire items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LignesInventaire'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new LignesInventaire
      operationId: createLignesInventaire
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LignesInventaire'
      responses:
        '201':
          description: Created LignesInventaire
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LignesInventaire'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /lignesinventaire/{id}:
    get:
      summary: Get a specific LignesInventaire by ID
      operationId: getLignesInventaire
      parameters:
      - &id029
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: LignesInventaire item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LignesInventaire'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a LignesInventaire
      operationId: replaceLignesInventaire
      parameters:
      - *id029
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LignesInventaire'
      responses:
        '200':
          description: Updated LignesInventaire
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LignesInventaire'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a LignesInventaire partially
      operationId: updateLignesInventaire
      parameters:
      - *id029
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LignesInventaire'
      responses:
        '200':
          description: Updated LignesInventaire
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LignesInventaire'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a LignesInventaire
      operationId: deleteLignesInventaire
      parameters:
      - *id029
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /tables:
    get:
      summary: List all Tables
      operationId: listTables
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of Tables items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Tables'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new Tables
      operationId: createTables
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Tables'
      responses:
        '201':
          description: Created Tables
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tables'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /tables/{id}:
    get:
      summary: Get a specific Tables by ID
      operationId: getTables
      parameters:
      - &id030
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Tables item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tables'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a Tables
      operationId: replaceTables
      parameters:
      - *id030
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Tables'
      responses:
        '200':
          description: Updated Tables
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tables'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a Tables partially
      operationId: updateTables
      parameters:
      - *id030
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Tables'
      responses:
        '200':
          description: Updated Tables
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tables'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a Tables
      operationId: deleteTables
      parameters:
      - *id030
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /tables/{id}/reservationstables:
    get:
      summary: Get ReservationsTables items related to Tables
      operationId: getTablesReservationsTables
      parameters:
      - *id030
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related ReservationsTables items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ReservationsTables'
        '404':
          $ref: '#/components/responses/Error'
  /tables/{id}/commandes:
    get:
      summary: Get Commandes items related to Tables
      operationId: getTablesCommandes
      parameters:
      - *id030
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Commandes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Commandes'
        '404':
          $ref: '#/components/responses/Error'
  /reservationstables:
    get:
      summary: List all ReservationsTables
      operationId: listReservationsTables
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of ReservationsTables items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ReservationsTables'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new ReservationsTables
      operationId: createReservationsTables
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReservationsTables'
      responses:
        '201':
          description: Created ReservationsTables
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReservationsTables'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /reservationstables/{id}:
    get:
      summary: Get a specific ReservationsTables by ID
      operationId: getReservationsTables
      parameters:
      - &id031
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: ReservationsTables item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReservationsTables'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a ReservationsTables
      operationId: replaceReservationsTables
      parameters:
      - *id031
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReservationsTables'
      responses:
        '200':
          description: Updated ReservationsTables
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReservationsTables'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a ReservationsTables partially
      operationId: updateReservationsTables
      parameters:
      - *id031
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReservationsTables'
      responses:
        '200':
          description: Updated ReservationsTables
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReservationsTables'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a ReservationsTables
      operationId: deleteReservationsTables
      parameters:
      - *id031
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /reservationstables/{id}/commandes:
    get:
      summary: Get Commandes items related to ReservationsTables
      operationId: getReservationsTablesCommandes
      parameters:
      - *id031
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related Commandes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Commandes'
        '404':
          $ref: '#/components/responses/Error'
  /commandes:
    get:
      summary: List all Commandes
      operationId: listCommandes
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of Commandes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Commandes'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new Commandes
      operationId: createCommandes
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Commandes'
      responses:
        '201':
          description: Created Commandes
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Commandes'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /commandes/{id}:
    get:
      summary: Get a specific Commandes by ID
      operationId: getCommandes
      parameters:
      - &id032
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Commandes item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Commandes'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a Commandes
      operationId: replaceCommandes
      parameters:
      - *id032
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Commandes'
      responses:
        '200':
          description: Updated Commandes
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Commandes'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a Commandes partially
      operationId: updateCommandes
      parameters:
      - *id032
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Commandes'
      responses:
        '200':
          description: Updated Commandes
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Commandes'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a Commandes
      operationId: deleteCommandes
      parameters:
      - *id032
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
  /commandes/{id}/detailscommandes:
    get:
      summary: Get DetailsCommandes items related to Commandes
      operationId: getCommandesDetailsCommandes
      parameters:
      - *id032
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of related DetailsCommandes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DetailsCommandes'
        '404':
          $ref: '#/components/responses/Error'
  /detailscommandes:
    get:
      summary: List all DetailsCommandes
      operationId: listDetailsCommandes
      parameters:
      - name: limit
        in: query
        description: Maximum number of items to return
        schema:
          type: integer
          default: 20
      - name: offset
        in: query
        description: Number of items to skip
        schema:
          type: integer
          default: 0
      responses:
        '200':
          description: Array of DetailsCommandes items
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DetailsCommandes'
        '400':
          $ref: '#/components/responses/Error'
    post:
      summary: Create a new DetailsCommandes
      operationId: createDetailsCommandes
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DetailsCommandes'
      responses:
        '201':
          description: Created DetailsCommandes
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailsCommandes'
        '400':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
  /detailscommandes/{id}:
    get:
      summary: Get a specific DetailsCommandes by ID
      operationId: getDetailsCommandes
      parameters:
      - &id033
        name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: DetailsCommandes item
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailsCommandes'
        '404':
          $ref: '#/components/responses/Error'
    put:
      summary: Replace a DetailsCommandes
      operationId: replaceDetailsCommandes
      parameters:
      - *id033
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DetailsCommandes'
      responses:
        '200':
          description: Updated DetailsCommandes
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailsCommandes'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    patch:
      summary: Update a DetailsCommandes partially
      operationId: updateDetailsCommandes
      parameters:
      - *id033
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DetailsCommandes'
      responses:
        '200':
          description: Updated DetailsCommandes
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailsCommandes'
        '400':
          $ref: '#/components/responses/Error'
        '404':
          $ref: '#/components/responses/Error'
        '422':
          $ref: '#/components/responses/Error'
    delete:
      summary: Delete a DetailsCommandes
      operationId: deleteDetailsCommandes
      parameters:
      - *id033
      responses:
        '204':
          description: Deletion successful
        '404':
          $ref: '#/components/responses/Error'
components:
  schemas:
    UtilisateursSuperAdmin:
      type: object
      properties:
        superadmin_id:
          type: string
        email:
          type: string
        mot_de_passe_hash:
          type: string
        nom:
          type: string
        prenom:
          type: string
        actif:
          type: string
        derniere_connexion:
          type: string
          format: date-time
        created_at:
          type: string
        updated_at:
          type: string
          format: date-time
    ChainesHotelieres:
      type: object
      properties:
        chaine_id:
          type: string
        nom:
          type: string
        slug:
          type: string
        description:
          type: string
        logo_url:
          type: string
        site_web:
          type: string
        email_contact:
          type: string
        telephone_contact:
          type: string
        adresse_siege:
          type: string
        pays_origine:
          type: string
        devise_principale:
          type: string
        fuseau_horaire_defaut:
          type: string
        actif:
          type: string
        superadmin_createur_id:
          type: integer
          format: int32
        date_creation:
          type: string
        date_mise_a_jour:
          type: string
          format: date-time
    AdminsChaine:
      type: object
      properties:
        admin_chaine_id:
          type: string
        chaine_id:
          type: string
        email:
          type: string
        mot_de_passe_hash:
          type: string
        nom:
          type: string
        prenom:
          type: string
        telephone:
          type: string
        actif:
          type: string
        derniere_connexion:
          type: string
          format: date-time
        created_at:
          type: string
        updated_at:
          type: string
          format: date-time
    ComplexesHoteliers:
      type: object
      properties:
        complexe_id:
          type: string
        chaine_id:
          type: string
        nom:
          type: string
        slug:
          type: string
        type_etablissement:
          type: string
        adresse:
          type: string
        ville:
          type: string
        code_postal:
          type: string
        pays:
          type: string
        telephone:
          type: string
        email:
          type: string
        site_web:
          type: string
        logo_url:
          type: string
        devise:
          type: string
        fuseau_horaire:
          type: string
        actif:
          type: string
        date_creation:
          type: string
        date_mise_a_jour:
          type: string
          format: date-time
    ServicesComplexe:
      type: object
      properties:
        service_id:
          type: string
        complexe_id:
          type: string
        type_service:
          type: string
        nom:
          type: string
        description:
          type: string
        emplacement:
          type: string
        horaires_ouverture:
          type: object
        capacite:
          type: integer
          format: int32
        image_url:
          type: string
        contact_email:
          type: string
        contact_telephone:
          type: string
        actif:
          type: string
        configuration:
          type: object
        tarification:
          type: object
        created_at:
          type: string
        updated_at:
          type: string
          format: date-time
    AdminsComplexe:
      type: object
      properties:
        admin_complexe_id:
          type: string
        complexe_id:
          type: string
        email:
          type: string
        mot_de_passe_hash:
          type: string
        nom:
          type: string
        prenom:
          type: string
        telephone:
          type: string
        actif:
          type: string
        derniere_connexion:
          type: string
          format: date-time
        created_at:
          type: string
        updated_at:
          type: string
          format: date-time
    RolesComplexe:
      type: object
      properties:
        role_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: integer
          format: int32
        nom:
          type: string
        permissions:
          type: string
        description:
          type: string
        created_at:
          type: string
    Employes:
      type: object
      properties:
        employe_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: integer
          format: int32
        role_id:
          type: integer
          format: int32
        nom:
          type: string
        prenom:
          type: string
        email:
          type: string
        telephone:
          type: string
        mot_de_passe_hash:
          type: string
        date_embauche:
          type: string
        actif:
          type: string
        services_autorises:
          type: object
        derniere_connexion:
          type: string
          format: date-time
        created_at:
          type: string
        updated_at:
          type: string
          format: date-time
    PointsDeVente:
      type: object
      properties:
        pos_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: string
        nom:
          type: string
        emplacement:
          type: string
        caisse_ouverte:
          type: string
        fonds_caisse:
          type: string
        employe_actuel_id:
          type: integer
          format: int32
        statut:
          type: string
        configuration:
          type: object
        created_at:
          type: string
        updated_at:
          type: string
          format: date-time
    SessionsCaisse:
      type: object
      properties:
        session_id:
          type: string
        pos_id:
          type: string
        employe_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: string
        date_ouverture:
          type: string
        date_fermeture:
          type: string
          format: date-time
        fonds_ouverture:
          type: string
        fonds_fermeture:
          type: number
          format: double
        total_ventes:
          type: number
          format: double
        total_especes:
          type: number
          format: double
        total_cartes:
          type: number
          format: double
        total_autres:
          type: number
          format: double
        notes:
          type: string
        statut:
          type: string
    CategoriesProduits:
      type: object
      properties:
        categorie_id:
          type: string
        chaine_id:
          type: string
        parent_id:
          type: integer
          format: int32
        nom:
          type: string
        description:
          type: string
        niveau:
          type: string
        complexe_id:
          type: integer
          format: int32
        service_id:
          type: integer
          format: int32
        actif:
          type: string
        created_at:
          type: string
    Produits:
      type: object
      properties:
        produit_id:
          type: string
        categorie_id:
          type: string
        chaine_id:
          type: string
        niveau:
          type: string
        complexe_id:
          type: integer
          format: int32
        service_id:
          type: integer
          format: int32
        nom:
          type: string
        code_barre:
          type: string
        sku:
          type: string
        type_produit:
          type: string
        prix_achat:
          type: number
          format: double
        prix_vente_defaut:
          type: string
        description:
          type: string
        stock_actuel:
          type: string
        stock_minimal:
          type: string
        stock_maximal:
          type: integer
          format: int32
        actif:
          type: string
        tva:
          type: string
        image_url:
          type: string
        created_at:
          type: string
        updated_at:
          type: string
          format: date-time
    PrixProduits:
      type: object
      properties:
        prix_id:
          type: string
        produit_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: integer
          format: int32
        prix_vente:
          type: string
        date_debut:
          type: string
        date_fin:
          type: string
          format: date-time
        actif:
          type: string
        created_at:
          type: string
    Chambres:
      type: object
      properties:
        chambre_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: string
        numero:
          type: string
        type_chambre:
          type: string
        prix_base:
          type: string
        capacite:
          type: string
        statut:
          type: string
        description:
          type: string
        etage:
          type: integer
          format: int32
        caracteristiques:
          type: object
        created_at:
          type: string
        updated_at:
          type: string
          format: date-time
    Clients:
      type: object
      properties:
        client_id:
          type: string
        chaine_id:
          type: string
        complexe_creation_id:
          type: string
        nom:
          type: string
        prenom:
          type: string
        email:
          type: string
        telephone:
          type: string
        adresse:
          type: string
        date_naissance:
          type: string
          format: date
        nationalite:
          type: string
        numero_piece_identite:
          type: string
        type_piece_identite:
          type: string
        preferences:
          type: object
        created_at:
          type: string
        updated_at:
          type: string
          format: date-time
    Reservations:
      type: object
      properties:
        reservation_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: string
        client_id:
          type: string
        date_creation:
          type: string
        date_arrivee:
          type: string
        date_depart:
          type: string
        statut:
          type: string
        nb_adultes:
          type: string
        nb_enfants:
          type: string
        montant_total:
          type: number
          format: double
        acompte_verse:
          type: string
        code_promo_id:
          type: integer
          format: int32
        reduction_appliquee:
          type: string
        employe_id:
          type: integer
          format: int32
        commentaires:
          type: string
        source_reservation:
          type: string
        created_at:
          type: string
        updated_at:
          type: string
          format: date-time
    ChambresReservees:
      type: object
      properties:
        chambre_reservee_id:
          type: string
        reservation_id:
          type: string
        chambre_id:
          type: string
        date_debut:
          type: string
        date_fin:
          type: string
        prix_nuit:
          type: string
        statut:
          type: string
        commentaires:
          type: string
        created_at:
          type: string
    TransactionsPOS:
      type: object
      properties:
        transaction_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: string
        pos_id:
          type: string
        session_id:
          type: string
        employe_id:
          type: string
        client_id:
          type: integer
          format: int32
        chambre_id:
          type: integer
          format: int32
        reference:
          type: string
        code_promo_id:
          type: integer
          format: int32
        date_transaction:
          type: string
        montant_total:
          type: string
        montant_paye:
          type: string
        monnaie_rendue:
          type: number
          format: double
        mode_paiement:
          type: string
        statut:
          type: string
        notes:
          type: string
        created_at:
          type: string
    LignesTransactionPOS:
      type: object
      properties:
        ligne_id:
          type: string
        transaction_id:
          type: string
        produit_id:
          type: string
        quantite:
          type: string
        code_promo_id:
          type: integer
          format: int32
        remise_promo:
          type: string
        prix_unitaire:
          type: string
        remise:
          type: string
        montant_ligne:
          type: string
        tva:
          type: string
        notes:
          type: string
    Paiements:
      type: object
      properties:
        paiement_id:
          type: string
        transaction_id:
          type: string
        montant:
          type: string
        mode_paiement:
          type: string
        reference_paiement:
          type: string
        date_paiement:
          type: string
        notes:
          type: string
    Fidelite:
      type: object
      properties:
        fidelite_id:
          type: string
        chaine_id:
          type: string
        client_id:
          type: string
        points_disponibles:
          type: string
        points_cumules:
          type: string
        niveau:
          type: string
        date_derniere_activite:
          type: string
          format: date-time
        date_inscription:
          type: string
    CodesPromo:
      type: object
      properties:
        code_promo_id:
          type: string
        niveau:
          type: string
        chaine_id:
          type: string
        complexe_id:
          type: integer
          format: int32
        service_id:
          type: integer
          format: int32
        code:
          type: string
        type_remise:
          type: string
        valeur:
          type: string
        date_debut:
          type: string
        date_expiration:
          type: string
        utilisation_max:
          type: integer
          format: int32
        utilisation_actuelle:
          type: string
        actif:
          type: string
        description:
          type: string
        conditions:
          type: string
        categorie:
          type: string
        applicable_sur:
          type: string
        produits_associes:
          type: object
        categories_associees:
          type: object
        chambres_associees:
          type: object
        created_at:
          type: string
        updated_at:
          type: string
          format: date-time
    UtilisationsCodes:
      type: object
      properties:
        utilisation_id:
          type: string
        chaine_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: integer
          format: int32
        code_promo_id:
          type: string
        client_id:
          type: integer
          format: int32
        date_utilisation:
          type: string
        reservation_id:
          type: integer
          format: int32
        transaction_pos_id:
          type: integer
          format: int32
        montant_avant:
          type: string
        montant_remise:
          type: string
        montant_apres:
          type: string
        employe_id:
          type: integer
          format: int32
    Fournisseurs:
      type: object
      properties:
        fournisseur_id:
          type: string
        chaine_id:
          type: string
        niveau:
          type: string
        complexe_id:
          type: integer
          format: int32
        nom:
          type: string
        contact:
          type: string
        email:
          type: string
        telephone:
          type: string
        adresse:
          type: string
        informations_paiement:
          type: string
        notes:
          type: string
        actif:
          type: string
        created_at:
          type: string
        updated_at:
          type: string
          format: date-time
    CommandesFournisseurs:
      type: object
      properties:
        commande_id:
          type: string
        chaine_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: integer
          format: int32
        fournisseur_id:
          type: string
        reference:
          type: string
        date_commande:
          type: string
        date_livraison_prevue:
          type: string
          format: date
        statut:
          type: string
        montant_total:
          type: number
          format: double
        employe_id:
          type: string
        notes:
          type: string
        created_at:
          type: string
        updated_at:
          type: string
          format: date-time
    DetailsCommandesFournisseurs:
      type: object
      properties:
        detail_id:
          type: string
        commande_id:
          type: string
        produit_id:
          type: string
        quantite_commandee:
          type: string
        quantite_recue:
          type: string
        prix_unitaire:
          type: string
        montant_ligne:
          type: string
        date_peremption:
          type: string
          format: date
        numero_lot:
          type: string
        created_at:
          type: string
    MouvementsStock:
      type: object
      properties:
        mouvement_id:
          type: string
        chaine_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: integer
          format: int32
        produit_id:
          type: string
        type_mouvement:
          type: string
        quantite:
          type: string
        date_mouvement:
          type: string
        reference_id:
          type: integer
          format: int32
        reference_type:
          type: string
        employe_id:
          type: string
        notes:
          type: string
        created_at:
          type: string
    Inventaires:
      type: object
      properties:
        inventaire_id:
          type: string
        chaine_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: integer
          format: int32
        reference:
          type: string
        date_debut:
          type: string
        date_fin:
          type: string
          format: date-time
        statut:
          type: string
        employe_id:
          type: string
        notes:
          type: string
        created_at:
          type: string
    LignesInventaire:
      type: object
      properties:
        ligne_id:
          type: string
        inventaire_id:
          type: string
        produit_id:
          type: string
        quantite_theorique:
          type: string
        quantite_reelle:
          type: integer
          format: int32
        ecart:
          type: integer
          format: int32
        notes:
          type: string
        created_at:
          type: string
    Tables:
      type: object
      properties:
        table_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: string
        numero:
          type: string
        capacite:
          type: string
        zone:
          type: string
        statut:
          type: string
        position_x:
          type: integer
          format: int32
        position_y:
          type: integer
          format: int32
        rotation:
          type: string
        shape:
          type: string
        dimensions:
          type: string
        created_at:
          type: string
        updated_at:
          type: string
          format: date-time
    ReservationsTables:
      type: object
      properties:
        reservation_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: string
        table_id:
          type: string
        client_id:
          type: integer
          format: int32
        employe_id:
          type: string
        date_debut:
          type: string
        date_fin:
          type: string
        nb_personnes:
          type: string
        statut:
          type: string
        commentaires:
          type: string
        created_at:
          type: string
    Commandes:
      type: object
      properties:
        commande_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: string
        table_id:
          type: integer
          format: int32
        client_id:
          type: integer
          format: int32
        employe_id:
          type: string
        date_commande:
          type: string
        type:
          type: string
        statut:
          type: string
        montant_total:
          type: number
          format: double
        montant_tva:
          type: number
          format: double
        note:
          type: string
        code_promo_id:
          type: integer
          format: int32
        reduction_appliquee:
          type: string
        reservation_id:
          type: integer
          format: int32
        chambre_id:
          type: integer
          format: int32
        statut_paiement:
          type: string
        mode_paiement:
          type: string
        created_at:
          type: string
        updated_at:
          type: string
          format: date-time
    DetailsCommandes:
      type: object
      properties:
        detail_id:
          type: string
        complexe_id:
          type: string
        service_id:
          type: string
        commande_id:
          type: string
        produit_id:
          type: string
        quantite:
          type: string
        prix_unitaire:
          type: string
        remise:
          type: string
        montant_ligne:
          type: string
        tva:
          type: string
        commentaires:
          type: string
        statut:
          type: string
        code_promo_id:
          type: integer
          format: int32
        remise_promo:
          type: string
        heure_demande:
          type: string
        heure_preparation:
          type: string
          format: date-time
        heure_servi:
          type: string
          format: date-time
        employe_preparation:
          type: integer
          format: int32
  responses:
    Error:
      description: Error response
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
              details:
                type: string
