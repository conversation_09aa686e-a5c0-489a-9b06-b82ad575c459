# Plan d'Implémentation - Upload Simple de Menu et Inventaire

## 🎯 **Objectif**

Permettre aux utilisateurs d'uploader simplement :
- **Menu Restaurant** → Fichier Excel avec tous les plats
- **Carte Bar** → Fichier Excel avec toutes les boissons
- **Ingrédients Cuisine** → Fichier Excel pour l'inventaire restaurant
- **Inventaire Boissons** → Fichier Excel pour l'inventaire bar

**Résultat :** Menu opérationnel en 3 clics, sans configuration manuelle.

---

## 📋 **Phase 1 : Templates Excel Standardisés**

### **1.1 Template Menu Restaurant**
```
Colonnes requises :
- nom_plat (string) - Nom du plat
- description (text) - Description du plat
- categorie (string) - Entrée, Plat, Dessert, Boisson
- prix_vente (decimal) - Prix de vente en FCFA
- temps_preparation (int) - Temps en minutes
- allergenes (string) - Liste séparée par virgules
- image_url (string) - URL optionnelle de l'image
- actif (boolean) - true/false
```

### **1.2 Template Carte Bar**
```
Colonnes requises :
- nom_boisson (string) - Nom de la boisson
- description (text) - Description
- categorie (string) - Alcool, Cocktail, Soft, Vin, Bière
- prix_vente (decimal) - Prix de vente en FCFA
- degre_alcool (decimal) - Degré d'alcool (optionnel)
- volume_ml (int) - Volume en ml
- allergenes (string) - Liste séparée par virgules
- image_url (string) - URL optionnelle
- actif (boolean) - true/false
```

### **1.3 Template Ingrédients Cuisine**
```
Colonnes requises :
- nom_ingredient (string) - Nom de l'ingrédient
- description (text) - Description
- categorie (string) - Viande, Légume, Épice, etc.
- unite_mesure (string) - kg, L, pièce, etc.
- prix_unitaire (decimal) - Prix d'achat unitaire
- fournisseur (string) - Nom du fournisseur
- allergenes (string) - Liste des allergènes
- conservation (string) - Frais, Sec, Congelé
- duree_conservation_jours (int) - Durée de conservation
- stock_minimal (int) - Stock d'alerte
- actif (boolean) - true/false
```

### **1.4 Template Inventaire Boissons**
```
Colonnes requises :
- nom_boisson (string) - Nom de la boisson
- description (text) - Description
- categorie (string) - Alcool, Vin, Bière, Soft, Spiritueux
- type_conditionnement (string) - Bouteille, Fût, Canette, etc.
- volume_unitaire (decimal) - Volume d'une unité (L)
- prix_achat_unitaire (decimal) - Prix d'achat par unité
- prix_vente_unitaire (decimal) - Prix de vente par unité
- fournisseur (string) - Nom du fournisseur
- degre_alcool (decimal) - Degré d'alcool (optionnel)
- code_barre (string) - Code barre (optionnel)
- stock_minimal (int) - Stock d'alerte
- stock_maximal (int) - Stock maximum
- emplacement_stockage (string) - Cave, Frigo, Bar, etc.
- temperature_stockage (string) - Ambiante, Frais, Très frais
- actif (boolean) - true/false
```

---

## 📂 **Phase 2 : Services Backend d'Import**

### **2.1 Service MenuImport**
```javascript
// backend/services/menuImport.service.js
class MenuImportService extends BaseService {
  
  // Import menu restaurant depuis Excel
  static async importRestaurantMenu(serviceId, excelFile) {
    // 1. Parser le fichier Excel
    // 2. Valider les données
    // 3. Créer les catégories automatiquement
    // 4. Créer les produits
    // 5. Associer au service
    // 6. Retourner rapport d'import
  }
  
  // Import carte bar depuis Excel
  static async importBarMenu(serviceId, excelFile) {
    // Même logique adaptée pour les boissons
  }
  
  // Validation des données menu
  static validateMenuData(data, type) {
    // Validation spécifique restaurant/bar
  }
}
```

### **2.2 Service IngredientImport**
```javascript
// backend/services/ingredientImport.service.js
class IngredientImportService extends BaseService {

  // Import ingrédients cuisine depuis Excel
  static async importCuisineIngredients(complexeId, excelFile) {
    // 1. Parser le fichier Excel
    // 2. Valider les données
    // 3. Créer les ingrédients cuisine
    // 4. Initialiser les stocks
    // 5. Retourner rapport d'import
  }

  // Import inventaire boissons depuis Excel
  static async importBoissonInventory(complexeId, excelFile) {
    // 1. Parser le fichier Excel
    // 2. Valider les données spécifiques boissons
    // 3. Créer les produits boissons dans l'inventaire
    // 4. Initialiser les stocks boissons
    // 5. Gérer les conditionnements et volumes
    // 6. Retourner rapport d'import
  }

  // Validation des données ingrédients cuisine
  static validateIngredientData(data) {
    // Validation des champs requis pour cuisine
  }

  // Validation des données inventaire boissons
  static validateBoissonInventoryData(data) {
    // Validation spécifique pour boissons
    // - Vérification degré alcool
    // - Validation volumes et conditionnements
    // - Contrôle prix cohérents
  }
}
```

### **2.3 Service TemplateGenerator**
```javascript
// backend/services/templateGenerator.service.js
class TemplateGeneratorService extends BaseService {

  // Générer template Excel restaurant
  static generateRestaurantTemplate() {
    // Créer fichier Excel avec colonnes et exemples
  }

  // Générer template Excel bar
  static generateBarTemplate() {
    // Créer fichier Excel avec colonnes et exemples
  }

  // Générer template Excel ingrédients cuisine
  static generateCuisineIngredientTemplate() {
    // Créer fichier Excel avec colonnes et exemples pour cuisine
  }

  // Générer template Excel inventaire boissons
  static generateBoissonInventoryTemplate() {
    // Créer fichier Excel avec colonnes spécifiques boissons
    // - Conditionnements, volumes, degrés alcool
    // - Emplacements de stockage, températures
    // - Exemples de différents types de boissons
  }
}
```

---

## 🔌 **Phase 3 : Routes API**

### **3.1 Routes Menu Import**
```javascript
// backend/routes/menuImport.routes.js

// POST /api/menu-import/restaurant/:serviceId
router.post('/restaurant/:serviceId', 
  upload.single('menuFile'),
  MenuImportController.importRestaurantMenu
);

// POST /api/menu-import/bar/:serviceId
router.post('/bar/:serviceId',
  upload.single('menuFile'), 
  MenuImportController.importBarMenu
);

// GET /api/menu-import/template/restaurant
router.get('/template/restaurant',
  MenuImportController.downloadRestaurantTemplate
);

// GET /api/menu-import/template/bar
router.get('/template/bar',
  MenuImportController.downloadBarTemplate
);
```

### **3.2 Routes Ingredient Import**
```javascript
// backend/routes/ingredientImport.routes.js

// POST /api/ingredient-import/cuisine/:complexeId
router.post('/cuisine/:complexeId',
  upload.single('ingredientFile'),
  IngredientImportController.importCuisineIngredients
);

// POST /api/ingredient-import/boissons/:complexeId
router.post('/boissons/:complexeId',
  upload.single('boissonFile'),
  IngredientImportController.importBoissonInventory
);

// GET /api/ingredient-import/template/cuisine
router.get('/template/cuisine',
  IngredientImportController.downloadCuisineTemplate
);

// GET /api/ingredient-import/template/boissons
router.get('/template/boissons',
  IngredientImportController.downloadBoissonTemplate
);
```

---

## 🎨 **Phase 4 : Interface Frontend**

### **4.1 Composant MenuUploader**
```typescript
// hotel-frontend/src/components/upload/MenuUploader.tsx
interface MenuUploaderProps {
  serviceId: number;
  serviceType: 'Restaurant' | 'Bar';
  onUploadComplete: () => void;
}

const MenuUploader: React.FC<MenuUploaderProps> = ({
  serviceId, serviceType, onUploadComplete
}) => {
  // 1. Zone de drag & drop pour fichier Excel
  // 2. Bouton télécharger template
  // 3. Validation en temps réel
  // 4. Barre de progression
  // 5. Rapport d'import avec erreurs/succès
  // 6. Aperçu des données importées
};
```

### **4.2 Composant IngredientUploader**
```typescript
// hotel-frontend/src/components/upload/IngredientUploader.tsx
interface IngredientUploaderProps {
  complexeId: number;
  type: 'cuisine' | 'boissons';
  onUploadComplete: () => void;
}

const IngredientUploader: React.FC<IngredientUploaderProps> = ({
  complexeId, type, onUploadComplete
}) => {
  // 1. Zone de drag & drop spécifique au type
  // 2. Bouton télécharger template (cuisine ou boissons)
  // 3. Validation selon le type d'inventaire
  // 4. Aperçu des données avec colonnes spécifiques
  // 5. Rapport d'import différencié
};
```

### **4.3 Composant BoissonInventoryUploader**
```typescript
// hotel-frontend/src/components/upload/BoissonInventoryUploader.tsx
interface BoissonInventoryUploaderProps {
  complexeId: number;
  onUploadComplete: () => void;
}

const BoissonInventoryUploader: React.FC<BoissonInventoryUploaderProps> = ({
  complexeId, onUploadComplete
}) => {
  // Interface spécialisée pour l'inventaire boissons
  // - Validation des volumes et conditionnements
  // - Vérification des degrés d'alcool
  // - Gestion des emplacements de stockage
  // - Calculs automatiques (prix au litre, etc.)
};
```

### **4.4 Page MenuSetup**
```typescript
// hotel-frontend/src/pages/MenuSetup.tsx
const MenuSetup: React.FC = () => {
  const [serviceType, setServiceType] = useState<'Restaurant' | 'Bar'>('Restaurant');

  return (
    <div className="space-y-8">
      {/* Sélection du type de service */}
      <ServiceTypeSelector
        value={serviceType}
        onChange={setServiceType}
      />

      {serviceType === 'Restaurant' ? (
        <>
          {/* Étape 1: Upload Ingrédients Cuisine */}
          <SetupStep
            title="1. Importer les Ingrédients Cuisine"
            description="Uploadez votre liste d'ingrédients pour la cuisine"
          >
            <IngredientUploader type="cuisine" />
          </SetupStep>

          {/* Étape 2: Upload Menu Restaurant */}
          <SetupStep
            title="2. Importer le Menu Restaurant"
            description="Uploadez votre menu avec tous les plats"
          >
            <MenuUploader serviceType="Restaurant" />
          </SetupStep>
        </>
      ) : (
        <>
          {/* Étape 1: Upload Inventaire Boissons */}
          <SetupStep
            title="1. Importer l'Inventaire Boissons"
            description="Uploadez votre inventaire de boissons"
          >
            <BoissonInventoryUploader />
          </SetupStep>

          {/* Étape 2: Upload Carte Bar */}
          <SetupStep
            title="2. Importer la Carte du Bar"
            description="Uploadez votre carte des boissons"
          >
            <MenuUploader serviceType="Bar" />
          </SetupStep>
        </>
      )}

      {/* Étape 3: Vérification */}
      <SetupStep
        title="3. Vérification"
        description="Vérifiez votre menu/carte"
      >
        <MenuPreview serviceType={serviceType} />
      </SetupStep>
    </div>
  );
};
```

---

## ⚡ **Phase 5 : Intégration dans Services**

### **5.1 Modification ServiceModal**
```typescript
// Ajouter onglet "Import Menu" dans ServiceModal
const importMenuSection = (
  <div className="space-y-6">
    {!service ? (
      <div className="text-center py-8">
        <p className="text-gray-500">
          Créez d'abord le service pour importer le menu
        </p>
      </div>
    ) : (
      <div className="space-y-6">
        {/* Upload selon le type de service */}
        {service.type_service === 'Restaurant' ? (
          <div className="border rounded-lg p-6">
            <h3 className="text-lg font-medium mb-4">
              1. Importer les Ingrédients Cuisine
            </h3>
            <IngredientUploader
              complexeId={authService.getComplexeId()}
              type="cuisine"
              onUploadComplete={() => toast.success('Ingrédients importés')}
            />
          </div>
        ) : (
          <div className="border rounded-lg p-6">
            <h3 className="text-lg font-medium mb-4">
              1. Importer l'Inventaire Boissons
            </h3>
            <BoissonInventoryUploader
              complexeId={authService.getComplexeId()}
              onUploadComplete={() => toast.success('Inventaire boissons importé')}
            />
          </div>
        )}
        
        {/* Upload Menu/Carte */}
        <div className="border rounded-lg p-6">
          <h3 className="text-lg font-medium mb-4">
            2. Importer {service.type_service === 'Restaurant' ? 'le Menu' : 'la Carte'}
          </h3>
          <MenuUploader
            serviceId={service.service_id}
            serviceType={service.type_service}
            onUploadComplete={() => toast.success(
              service.type_service === 'Restaurant' ? 'Menu importé' : 'Carte importée'
            )}
          />
        </div>
      </div>
    )}
  </div>
);
```

---

## 🔄 **Phase 6 : Workflow Utilisateur Final**

### **Workflow Restaurant :**
1. **Créer le service** → Page Services > Nouveau Restaurant
2. **Onglet "Import Menu"** → Interface d'upload
3. **Télécharger templates** → Excel pré-formatés
4. **Remplir les templates** → Avec ses données
5. **Upload Ingrédients** → Drag & drop du fichier Excel
6. **Upload Menu** → Drag & drop du fichier Excel
7. **Vérification automatique** → Rapport d'import
8. **Menu opérationnel** → Disponible dans POS

### **Workflow Bar :**
1. **Créer le service** → Page Services > Nouveau Bar
2. **Onglet "Import Menu"** → Interface d'upload
3. **Télécharger templates** → Excel inventaire boissons + carte bar
4. **Remplir les templates** → Inventaire des boissons + carte des prix
5. **Upload Inventaire Boissons** → Drag & drop du fichier Excel inventaire
6. **Upload Carte Bar** → Drag & drop du fichier Excel carte
7. **Carte opérationnelle** → Disponible dans POS avec gestion stock

---

## 📊 **Phase 7 : Fonctionnalités Avancées**

### **7.1 Validation Intelligente**
- Détection automatique du type de fichier
- Validation des prix (cohérence)
- Vérification des catégories
- Détection des doublons
- Suggestions de corrections

### **7.2 Import Progressif**
- Import par lots pour gros fichiers
- Possibilité d'annuler l'import
- Reprise d'import en cas d'erreur
- Sauvegarde des données partielles

### **7.3 Mise à Jour**
- Re-import pour mise à jour
- Comparaison avec données existantes
- Options de fusion/remplacement
- Historique des imports

---

## 🛠️ **Phase 8 : Implémentation Technique**

### **8.1 Ordre de Développement**
```
1. Backend Templates (1 jour)
   - TemplateGeneratorService
   - Routes de téléchargement

2. Backend Import (2 jours)
   - MenuImportService
   - IngredientImportService
   - Validation et parsing Excel

3. Frontend Composants (2 jours)
   - MenuUploader
   - IngredientUploader
   - Interfaces de progression

4. Intégration (1 jour)
   - Modification ServiceModal
   - Tests d'intégration

5. Tests et Optimisation (1 jour)
   - Tests avec vrais fichiers Excel
   - Optimisation performances
```

### **8.2 Dépendances Techniques**
```javascript
// Backend
- xlsx (parsing Excel)
- multer (upload fichiers)
- joi (validation)

// Frontend
- react-dropzone (drag & drop)
- xlsx (preview Excel côté client)
- react-query (gestion état async)
```

### **8.3 Structure Fichiers**
```
backend/
├── services/
│   ├── menuImport.service.js
│   ├── ingredientImport.service.js
│   └── templateGenerator.service.js
├── controllers/
│   ├── menuImport.controller.js
│   └── ingredientImport.controller.js
├── routes/
│   ├── menuImport.routes.js
│   └── ingredientImport.routes.js
└── templates/
    ├── restaurant-menu-template.xlsx
    ├── bar-menu-template.xlsx
    ├── cuisine-ingredients-template.xlsx
    └── boisson-inventory-template.xlsx

frontend/
├── components/upload/
│   ├── MenuUploader.tsx
│   ├── IngredientUploader.tsx
│   ├── BoissonInventoryUploader.tsx
│   ├── FileDropzone.tsx
│   ├── ImportProgress.tsx
│   └── ImportReport.tsx
├── services/
│   ├── menuImport.service.ts
│   └── ingredientImport.service.ts
└── pages/
    └── MenuSetup.tsx
```

---

## 🎯 **Résultat Final**

**Avant :** Configuration manuelle complexe (30+ étapes)
**Après :** Upload simple (3 étapes)

1. **Télécharger template** → 1 clic
2. **Remplir Excel** → 10 minutes
3. **Upload fichier** → 1 clic

**Menu opérationnel en moins de 15 minutes !**

---

## 📋 **Checklist d'Implémentation**

### **Backend**
- [x] Service TemplateGenerator ✅ PHASE 1
- [x] Templates Excel ✅ PHASE 1
- [x] Service MenuImport (Restaurant) ✅ PHASE 2
- [x] Service MenuImport (Bar) ✅ PHASE 2
- [x] Service IngredientImport ✅ PHASE 2
- [x] Controllers d'import ✅ PHASE 2
- [x] Routes API ✅ PHASE 2
- [x] Validation des données ✅ PHASE 2
- [x] Gestion des erreurs ✅ PHASE 2

### **Frontend**
- [ ] Composant MenuUploader
- [ ] Composant IngredientUploader (cuisine)
- [ ] Composant BoissonInventoryUploader
- [ ] Service menuImport
- [ ] Service ingredientImport
- [ ] Service boissonInventoryImport
- [ ] Interface de progression
- [ ] Rapport d'import différencié
- [ ] Intégration ServiceModal
- [ ] Page MenuSetup (optionnelle)

### **Tests**
- [ ] Tests unitaires services
- [ ] Tests d'intégration API
- [ ] Tests composants React
- [ ] Tests avec vrais fichiers Excel
- [ ] Tests de performance
- [ ] Tests d'erreurs

### **Documentation**
- [ ] Guide utilisateur
- [ ] Format des templates
- [ ] API documentation
- [ ] Exemples de fichiers
