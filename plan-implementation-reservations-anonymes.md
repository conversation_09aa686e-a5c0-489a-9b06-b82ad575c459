# Plan d'Implémentation - Réservations Anonymes

## Vue d'ensemble

Ce plan détaille l'implémentation complète du système de réservations anonymes pour permettre aux clients de faire des réservations sans fournir d'informations personnelles détaillées.

## Phase 1 : Modifications de la Base de Données ✅ IMPLÉMENTÉE

### 1.1 Modifications de la table Clients ✅
```sql
-- Ajouter les colonnes pour supporter l'anonymat
ALTER TABLE "Clients" ADD COLUMN "est_anonyme" boolean DEFAULT false;
ALTER TABLE "Clients" ADD COLUMN "pseudonyme" varchar(100);
ALTER TABLE "Clients" ADD COLUMN "code_acces_anonyme" varchar(50) UNIQUE;

-- Rendre certains champs optionnels pour les clients anonymes
ALTER TABLE "Clients" ALTER COLUMN "nom" DROP NOT NULL;
ALTER TABLE "Clients" ALTER COLUMN "prenom" DROP NOT NULL;
ALTER TABLE "Clients" ALTER COLUMN "telephone" DROP NOT NULL;

-- Ajouter des contraintes
ALTER TABLE "Clients" ADD CONSTRAINT "check_client_anonyme"
CHECK (
  (est_anonyme = false AND nom IS NOT NULL AND prenom IS NOT NULL) OR
  (est_anonyme = true AND code_acces_anonyme IS NOT NULL)
);

-- Index pour optimiser les recherches
CREATE INDEX "idx_clients_code_acces_anonyme" ON "Clients" ("code_acces_anonyme");
CREATE INDEX "idx_clients_est_anonyme" ON "Clients" ("est_anonyme");
```

### 1.2 Modifications de la table Reservations ✅
```sql
-- Ajouter une colonne pour identifier les réservations anonymes
ALTER TABLE "Reservations" ADD COLUMN "est_anonyme" boolean DEFAULT false;

-- Index pour optimiser les recherches
CREATE INDEX "idx_reservations_est_anonyme" ON "Reservations" ("est_anonyme");
```

### 1.3 Table de logs pour les accès anonymes ✅
```sql
CREATE TABLE "LogsAccesAnonymes" (
  "log_id" INTEGER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  "code_acces_anonyme" varchar(50) NOT NULL,
  "adresse_ip" varchar(45),
  "user_agent" text,
  "action" varchar(50) NOT NULL,
  "timestamp" timestamp DEFAULT (now()),
  "details" json
);

CREATE INDEX "idx_logs_acces_code" ON "LogsAccesAnonymes" ("code_acces_anonyme");
CREATE INDEX "idx_logs_acces_timestamp" ON "LogsAccesAnonymes" ("timestamp");
```

## Phase 2 : Backend - Services ✅ IMPLÉMENTÉE

### 2.1 Nouveau service AnonymousReservationService ✅
**Fichier :** `backend/services/anonymousReservation.service.js`

Service principal pour gérer toutes les opérations liées aux réservations anonymes.

### 2.2 Modifications du ReservationService existant ✅
**Fichier :** `backend/services/reservation.service.js`

- ✅ Ajout de la méthode `createDemandeReservationAnonyme` (délégation)
- ✅ Modification des méthodes existantes pour gérer les clients anonymes
- ✅ Protection contre la confirmation directe des réservations anonymes

### 2.3 Nouveau service de validation ✅
**Fichier :** `backend/services/anonymousValidation.service.js`

Service complet de validation avec toutes les règles métier pour les réservations anonymes.

### 2.4 Nouveau service de configuration ✅
**Fichier :** `backend/services/anonymousConfig.service.js`

Service pour gérer la configuration des réservations anonymes par complexe.

## Phase 3 : Backend - Contrôleurs et Routes ✅ IMPLÉMENTÉE

### 3.1 Nouveau contrôleur AnonymousReservationController ✅
**Fichier :** `backend/controllers/anonymousReservation.controller.js`

Contrôleur principal pour gérer toutes les opérations des réservations anonymes.

### 3.2 Routes publiques pour réservations anonymes ✅
**Fichier :** `backend/routes/anonymousReservation.routes.js`

Routes publiques sans authentification pour les clients anonymes.

### 3.3 Routes d'administration ✅
**Fichier :** `backend/routes/anonymousConfig.routes.js`

Routes protégées pour la configuration et gestion des réservations anonymes.

### 3.4 Contrôleur de configuration ✅
**Fichier :** `backend/controllers/anonymousConfig.controller.js`

Contrôleur pour gérer la configuration des réservations anonymes par complexe.

### 3.5 Intégration dans le routeur principal ✅
**Fichier :** `backend/routes/index.js`

- ✅ Routes publiques : `/api/reservations-anonymes`
- ✅ Routes d'administration : `/api/admin/anonymous-config`
- ✅ Intégration avec le système d'authentification existant

### 3.6 Modifications du contrôleur existant ✅
**Fichier :** `backend/controllers/reservation.controller.js`

- ✅ Redirection automatique vers le contrôleur anonyme si demandé
- ✅ Compatibilité avec l'API existante

## Phase 4 : Backend - Middlewares ✅ IMPLÉMENTÉE

### 4.1 Middleware de validation et sécurité ✅
**Fichier :** `backend/middleware/anonymousReservation.js`

Middleware complet pour la validation, sécurité et logging des réservations anonymes.

### 4.2 Middleware de sécurité avancée ✅
**Fichier :** `backend/middleware/anonymousSecurity.js`

Protection contre les attaques, détection d'anomalies et prévention des abus.

### 4.3 Middleware de cache ✅
**Fichier :** `backend/middleware/anonymousCache.js`

Système de cache en mémoire pour optimiser les performances.

### 4.4 Middleware de monitoring ✅
**Fichier :** `backend/middleware/anonymousMonitoring.js`

Collecte de métriques, monitoring de performance et alertes en temps réel.

### 4.5 Intégration dans les routes ✅
- ✅ Routes publiques avec tous les middlewares de sécurité
- ✅ Routes d'administration avec cache et monitoring
- ✅ Rate limiting adaptatif par type d'action
- ✅ Logging complet de toutes les activités

## Phase 5 : Frontend - Services

### 5.1 Nouveau service anonyme
**Fichier :** `hotel-frontend/src/services/anonymousReservation.service.ts`

```typescript
import api from './api.config';

export interface CreateAnonymousReservationParams {
  complexe_id: number;
  date_arrivee: string;
  date_depart: string;
  heure_debut: string;
  heure_fin: string;
  chambres: Array<{
    chambre_id: string;
    type_chambre: string;
    prix_nuit: number;
  }>;
  prix_total: number;
  pseudonyme?: string;
  commentaires?: string;
}

export interface AnonymousReservationResponse {
  reservation: Reservation;
  code_acces_anonyme: string;
}

class AnonymousReservationService {
  // Créer une demande de réservation anonyme
  async createDemandeReservationAnonyme(params: CreateAnonymousReservationParams): Promise<ApiResponse<AnonymousReservationResponse>> {
    // Implémentation complète
  }

  // Consulter une réservation anonyme
  async getReservationAnonyme(codeAcces: string): Promise<ApiResponse<ReservationDetails>> {
    // Implémentation complète
  }

  // Modifier une réservation anonyme
  async updateReservationAnonyme(codeAcces: string, params: Partial<CreateAnonymousReservationParams>): Promise<ApiResponse<Reservation>> {
    // Implémentation complète
  }

  // Annuler une réservation anonyme
  async cancelReservationAnonyme(codeAcces: string): Promise<ApiResponse<void>> {
    // Implémentation complète
  }

  // Valider un code d'accès
  async validateAccessCode(codeAcces: string): Promise<boolean> {
    // Implémentation complète
  }
}

export const anonymousReservationService = new AnonymousReservationService();
```

### 5.2 Modifications du service de réservation existant
**Fichier :** `hotel-frontend/src/services/reservation.service.ts`

- Ajouter les méthodes pour les réservations anonymes
- Modifier les interfaces existantes pour supporter l'anonymat

## Phase 6 : Frontend - Composants

### 6.1 Modification du composant ClientReservation
**Fichier :** `hotel-frontend/src/components/ClientReservation.tsx`

```typescript
// Ajouter les états pour le mode anonyme
const [isAnonymousMode, setIsAnonymousMode] = useState(false);
const [pseudonyme, setPseudonyme] = useState('');

// Modifier le formulaire pour inclure l'option anonyme
const renderAnonymousToggle = () => (
  <div className="flex items-center space-x-2 mb-4">
    <input
      type="checkbox"
      id="anonymous-mode"
      checked={isAnonymousMode}
      onChange={(e) => setIsAnonymousMode(e.target.checked)}
      className="rounded border-gray-300"
    />
    <label htmlFor="anonymous-mode" className="text-sm font-medium">
      Réservation anonyme
    </label>
  </div>
);

// Modifier la logique de soumission
const handleSubmit = async () => {
  if (isAnonymousMode) {
    // Logique pour réservation anonyme
  } else {
    // Logique existante
  }
};
```

### 6.2 Nouveau composant AnonymousReservationLookup
**Fichier :** `hotel-frontend/src/components/AnonymousReservationLookup.tsx`

```typescript
import { useState } from 'react';
import { toast } from 'react-hot-toast';
import { anonymousReservationService } from '../services/anonymousReservation.service';

export function AnonymousReservationLookup() {
  const [codeAcces, setCodeAcces] = useState('');
  const [reservation, setReservation] = useState<ReservationDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleLookup = async () => {
    if (!codeAcces.trim()) {
      toast.error('Veuillez entrer votre code d\'accès');
      return;
    }

    setIsLoading(true);
    try {
      const response = await anonymousReservationService.getReservationAnonyme(codeAcces);
      if (response.success) {
        setReservation(response.data);
        toast.success('Réservation trouvée !');
      }
    } catch (error) {
      toast.error('Réservation non trouvée ou code invalide');
      setReservation(null);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <h2 className="text-2xl font-bold mb-6">Consulter ma réservation anonyme</h2>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Code d'accès
            </label>
            <input
              type="text"
              value={codeAcces}
              onChange={(e) => setCodeAcces(e.target.value.toUpperCase())}
              placeholder="ANON-XXXXXXXXXXXX"
              className="w-full px-3 py-2 border rounded-md"
              maxLength={17}
            />
          </div>

          <button
            onClick={handleLookup}
            disabled={isLoading}
            className="w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {isLoading ? 'Recherche...' : 'Consulter ma réservation'}
          </button>
        </div>
      </div>

      {reservation && (
        <AnonymousReservationDetails reservation={reservation} />
      )}
    </div>
  );
}
```

### 6.3 Composant de détails de réservation anonyme
**Fichier :** `hotel-frontend/src/components/AnonymousReservationDetails.tsx`

```typescript
import { useState } from 'react';
import { ReservationDetails } from '../services/reservation.service';
import { anonymousReservationService } from '../services/anonymousReservation.service';
import { toast } from 'react-hot-toast';

interface Props {
  reservation: ReservationDetails;
}

export function AnonymousReservationDetails({ reservation }: Props) {
  const [isModifying, setIsModifying] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);

  const handleCancel = async () => {
    if (!confirm('Êtes-vous sûr de vouloir annuler cette réservation ?')) {
      return;
    }

    setIsCancelling(true);
    try {
      await anonymousReservationService.cancelReservationAnonyme(reservation.code_acces_anonyme);
      toast.success('Réservation annulée avec succès');
      // Rediriger ou rafraîchir
    } catch (error) {
      toast.error('Erreur lors de l\'annulation');
    } finally {
      setIsCancelling(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-xl font-bold mb-4">Détails de votre réservation</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
          <p className="text-sm text-gray-600">Numéro de réservation</p>
          <p className="font-semibold">{reservation.numero_reservation}</p>
        </div>

        <div>
          <p className="text-sm text-gray-600">Statut</p>
          <span className={`px-2 py-1 rounded text-sm ${
            reservation.statut === 'en_attente' ? 'bg-yellow-100 text-yellow-800' :
            reservation.statut === 'confirmee' ? 'bg-green-100 text-green-800' :
            'bg-red-100 text-red-800'
          }`}>
            {reservation.statut_libelle}
          </span>
        </div>

        <div>
          <p className="text-sm text-gray-600">Date d'arrivée</p>
          <p className="font-semibold">{new Date(reservation.date_arrivee).toLocaleDateString()}</p>
        </div>

        <div>
          <p className="text-sm text-gray-600">Date de départ</p>
          <p className="font-semibold">{new Date(reservation.date_depart).toLocaleDateString()}</p>
        </div>

        <div>
          <p className="text-sm text-gray-600">Heure d'arrivée</p>
          <p className="font-semibold">{reservation.heure_debut}</p>
        </div>

        <div>
          <p className="text-sm text-gray-600">Heure de départ</p>
          <p className="font-semibold">{reservation.heure_fin}</p>
        </div>

        <div>
          <p className="text-sm text-gray-600">Montant total</p>
          <p className="font-semibold">{reservation.montant_total}FCFA</p>
        </div>
      </div>

      {reservation.chambres && reservation.chambres.length > 0 && (
        <div className="mb-6">
          <h4 className="font-semibold mb-2">Chambres réservées</h4>
          <div className="space-y-2">
            {reservation.chambres.map((chambre, index) => (
              <div key={index} className="bg-gray-50 p-3 rounded">
                <p className="font-medium">Chambre {chambre.numero}</p>
                <p className="text-sm text-gray-600">Type: {chambre.type_chambre}</p>
                <p className="text-sm text-gray-600">Prix: {chambre.prix_nuit}FCFA</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {reservation.statut === 'en_attente' && (
        <div className="flex space-x-4">
          <button
            onClick={() => setIsModifying(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Modifier
          </button>

          <button
            onClick={handleCancel}
            disabled={isCancelling}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
          >
            {isCancelling ? 'Annulation...' : 'Annuler'}
          </button>
        </div>
      )}
    </div>
  );
}
```

## Phase 7 : Frontend - Pages et Navigation

### 7.1 Nouvelle page de consultation anonyme
**Fichier :** `hotel-frontend/src/pages/AnonymousReservation.tsx`

```typescript
import { AnonymousReservationLookup } from '../components/AnonymousReservationLookup';

export function AnonymousReservationPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <AnonymousReservationLookup />
      </div>
    </div>
  );
}
```

### 7.2 Modification de la navigation
**Fichier :** `hotel-frontend/src/components/Navigation.tsx`

```typescript
// Ajouter un lien vers la consultation anonyme
<Link
  to="/reservation-anonyme"
  className="text-gray-600 hover:text-gray-900"
>
  Consulter ma réservation
</Link>
```

### 7.3 Modification du routeur
**Fichier :** `hotel-frontend/src/App.tsx`

```typescript
// Ajouter la route pour les réservations anonymes
<Route path="/reservation-anonyme" element={<AnonymousReservationPage />} />
```

## Phase 8 : Sécurité et Validation

### 8.1 Validation côté serveur
- Validation stricte des codes d'accès
- Rate limiting pour éviter les attaques par force brute
- Logging de tous les accès anonymes
- Expiration automatique des codes non utilisés

### 8.2 Validation côté client
- Validation du format des codes d'accès
- Sanitisation des entrées utilisateur
- Gestion des erreurs et timeouts

### 8.3 Sécurité des données
- Chiffrement des codes d'accès en base
- Anonymisation complète des données sensibles
- Purge automatique des données expirées

## Phase 9 : Tests

### 9.1 Tests unitaires backend
- Tests des services anonymes
- Tests des contrôleurs
- Tests des middlewares de validation

### 9.2 Tests unitaires frontend
- Tests des composants anonymes
- Tests des services frontend
- Tests d'intégration

### 9.3 Tests d'intégration
- Tests end-to-end du flux complet
- Tests de sécurité
- Tests de performance

## Phase 10 : Documentation et Déploiement

### 10.1 Documentation technique
- Documentation des nouvelles APIs
- Guide d'utilisation pour les développeurs
- Documentation de la base de données

### 10.2 Documentation utilisateur
- Guide d'utilisation pour les clients
- FAQ sur les réservations anonymes
- Politique de confidentialité mise à jour

### 10.3 Migration et déploiement
- Scripts de migration de base de données
- Plan de déploiement progressif
- Monitoring et alertes

## Phase 11 : Maintenance et Optimisation

### 11.1 Tâches de maintenance
- Nettoyage automatique des réservations expirées
- Archivage des logs d'accès
- Monitoring des performances

### 11.2 Optimisations futures
- Cache des codes d'accès fréquemment utilisés
- Amélioration de l'UX
- Intégration avec d'autres services

## Estimation des délais

- **Phase 1-2 (Backend base)** : 3-4 jours
- **Phase 3-4 (API et middlewares)** : 2-3 jours
- **Phase 5-7 (Frontend)** : 4-5 jours
- **Phase 8-9 (Sécurité et tests)** : 2-3 jours
- **Phase 10-11 (Documentation et déploiement)** : 1-2 jours

**Total estimé : 12-17 jours**

## Prérequis techniques

- Node.js et npm/yarn installés
- Base de données PostgreSQL
- Accès aux environnements de développement et production
- Outils de test configurés

## Risques et mitigation

1. **Sécurité** : Validation stricte et logging complet
2. **Performance** : Indexation appropriée et cache
3. **UX** : Tests utilisateur et feedback continu
4. **Maintenance** : Automatisation des tâches récurrentes
```
