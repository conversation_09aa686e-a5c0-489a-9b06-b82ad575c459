// Mock Role Service
// Replace with actual implementation

export interface Role {
  role_id: number;
  nom: string;
  description: string;
  permissions: string[];
  complexe_id: number;
  created_at: string;
  updated_at: string;
}

export interface CreateRoleData {
  nom: string;
  description: string;
  permissions: string[];
}

export interface UpdateRoleData {
  nom?: string;
  description?: string;
  permissions?: string[];
}

class RoleService {
  async getRoles(): Promise<Role[]> {
    // Mock implementation
    return this.getMockRoles();
  }

  async getRole(id: number): Promise<Role> {
    const role = this.getMockRoles().find(role => role.role_id === id);
    if (!role) {
      throw new Error('Role not found');
    }
    return role;
  }

  async createRole(data: CreateRoleData): Promise<Role> {
    // Mock implementation
    console.log('Creating role:', data);
    return {
      role_id: Math.floor(Math.random() * 1000),
      nom: data.nom,
      description: data.description,
      permissions: data.permissions,
      complexe_id: 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  async updateRole(id: number, data: UpdateRoleData): Promise<boolean> {
    // Mock implementation
    console.log(`Updating role ${id}:`, data);
    return true;
  }

  async deleteRole(id: number): Promise<boolean> {
    // Mock implementation
    console.log(`Deleting role ${id}`);
    return true;
  }

  async assignRoleToEmployee(employeeId: number, roleId: number): Promise<boolean> {
    // Mock implementation
    console.log(`Assigning role ${roleId} to employee ${employeeId}`);
    return true;
  }

  async getEmployeePermissions(employeeId: number): Promise<string[]> {
    // Mock implementation
    return ['manage_employees', 'view_clients', 'view_reservations'];
  }

  async getAvailablePermissions(): Promise<string[]> {
    // Mock implementation
    return [
      'manage_employees', 'manage_roles', 'manage_products', 'manage_reservations',
      'view_reservations', 'create_reservation', 'update_reservation',
      'manage_clients', 'view_clients', 'create_client', 'update_client',
      'manage_payments', 'view_reports', 'view_statistics'
    ];
  }

  // Mock data for testing
  private getMockRoles(): Role[] {
    return [
      {
        role_id: 1,
        nom: 'Administrateur',
        description: 'Accès complet à toutes les fonctionnalités du système',
        permissions: [
          'manage_employees', 'manage_roles', 'manage_products', 'manage_reservations',
          'view_reservations', 'create_reservation', 'update_reservation',
          'manage_clients', 'view_clients', 'create_client', 'update_client',
          'manage_payments', 'view_reports', 'view_statistics'
        ],
        complexe_id: 1,
        created_at: '2020-01-01T00:00:00Z',
        updated_at: '2020-01-01T00:00:00Z'
      },
      {
        role_id: 2,
        nom: 'Réceptionniste',
        description: 'Gestion des réservations et des clients',
        permissions: [
          'view_reservations', 'create_reservation', 'update_reservation',
          'view_clients', 'create_client', 'update_client'
        ],
        complexe_id: 1,
        created_at: '2020-01-01T00:00:00Z',
        updated_at: '2020-01-01T00:00:00Z'
      },
      {
        role_id: 3,
        nom: 'Manager',
        description: 'Supervision des opérations et accès aux rapports',
        permissions: [
          'view_reservations', 'view_clients', 'view_reports', 'view_statistics'
        ],
        complexe_id: 1,
        created_at: '2020-01-01T00:00:00Z',
        updated_at: '2020-01-01T00:00:00Z'
      },
      {
        role_id: 4,
        nom: 'Femme de chambre',
        description: 'Accès limité pour la gestion des chambres',
        permissions: [],
        complexe_id: 1,
        created_at: '2020-01-01T00:00:00Z',
        updated_at: '2020-01-01T00:00:00Z'
      }
    ];
  }
}

export default new RoleService();