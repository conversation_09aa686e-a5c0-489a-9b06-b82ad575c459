// Mock Employee Service
// Replace with actual implementation

export interface Employee {
  employe_id: number;
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  role_id: number;
  role_nom: string;
  complexe_id: number;
  actif: boolean;
  date_embauche: string;
  created_at: string;
  updated_at: string;
}

export interface CreateEmployeeData {
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  role_id: number;
  password: string;
}

export interface UpdateEmployeeData {
  nom?: string;
  prenom?: string;
  email?: string;
  telephone?: string;
  role_id?: number;
  actif?: boolean;
}

class EmployeeService {
  async getEmployees(filters?: { role_id?: number; search?: string }): Promise<Employee[]> {
    // This is a mock implementation
    return this.getMockEmployees().filter(emp => {
      let match = true;
      
      if (filters?.role_id) {
        match = match && emp.role_id === filters.role_id;
      }
      
      if (filters?.search) {
        const search = filters.search.toLowerCase();
        match = match && (
          emp.nom.toLowerCase().includes(search) ||
          emp.prenom.toLowerCase().includes(search) ||
          emp.email.toLowerCase().includes(search)
        );
      }
      
      return match;
    });
  }

  async getEmployee(id: number): Promise<Employee> {
    const employee = this.getMockEmployees().find(emp => emp.employe_id === id);
    if (!employee) {
      throw new Error('Employee not found');
    }
    return employee;
  }

  async createEmployee(data: CreateEmployeeData): Promise<Employee> {
    // Mock implementation
    console.log('Creating employee:', data);
    return {
      employe_id: Math.floor(Math.random() * 1000),
      nom: data.nom,
      prenom: data.prenom,
      email: data.email,
      telephone: data.telephone,
      role_id: data.role_id,
      role_nom: 'Mock Role',
      complexe_id: 1,
      actif: true,
      date_embauche: new Date().toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  async updateEmployee(id: number, data: UpdateEmployeeData): Promise<boolean> {
    // Mock implementation
    console.log(`Updating employee ${id}:`, data);
    return true;
  }

  async updatePassword(id: number, currentPassword: string, newPassword: string): Promise<boolean> {
    // Mock implementation
    console.log(`Updating password for employee ${id}`);
    return true;
  }

  async deleteEmployee(id: number): Promise<boolean> {
    // Mock implementation
    console.log(`Deleting employee ${id}`);
    return true;
  }

  async getRoles(): Promise<any[]> {
    // Mock implementation
    return [
      { role_id: 1, nom: 'Administrateur' },
      { role_id: 2, nom: 'Réceptionniste' },
      { role_id: 3, nom: 'Manager' },
      { role_id: 4, nom: 'Femme de chambre' }
    ];
  }

  // Mock data for testing
  private getMockEmployees(): Employee[] {
    return [
      {
        employe_id: 1,
        nom: 'Dupont',
        prenom: 'Jean',
        email: '<EMAIL>',
        telephone: '+33123456789',
        role_id: 1,
        role_nom: 'Administrateur',
        complexe_id: 1,
        actif: true,
        date_embauche: '2020-01-15',
        created_at: '2020-01-15T10:00:00Z',
        updated_at: '2023-05-20T14:30:00Z'
      },
      {
        employe_id: 2,
        nom: 'Martin',
        prenom: 'Sophie',
        email: '<EMAIL>',
        telephone: '+33678901234',
        role_id: 2,
        role_nom: 'Réceptionniste',
        complexe_id: 1,
        actif: true,
        date_embauche: '2021-03-10',
        created_at: '2021-03-10T09:15:00Z',
        updated_at: '2023-04-12T11:20:00Z'
      },
      {
        employe_id: 3,
        nom: 'Petit',
        prenom: 'Michel',
        email: '<EMAIL>',
        telephone: '+33612345678',
        role_id: 3,
        role_nom: 'Manager',
        complexe_id: 1,
        actif: false,
        date_embauche: '2019-06-22',
        created_at: '2019-06-22T08:30:00Z',
        updated_at: '2023-01-05T16:45:00Z'
      },
      {
        employe_id: 4,
        nom: 'Leroy',
        prenom: 'Marie',
        email: '<EMAIL>',
        telephone: '+33698765432',
        role_id: 4,
        role_nom: 'Femme de chambre',
        complexe_id: 1,
        actif: true,
        date_embauche: '2022-02-05',
        created_at: '2022-02-05T13:45:00Z',
        updated_at: '2023-03-18T10:10:00Z'
      }
    ];
  }
}

export default new EmployeeService();