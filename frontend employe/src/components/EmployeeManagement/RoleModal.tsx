import React, { useState, useEffect } from 'react';
import { X, Loader, Info, ShieldCheck } from 'lucide-react';
import { Role } from '../../services/role.service';

// Mock service (replace with actual import)
const roleService = {
  createRole: async (data: any) => {
    // Mock implementation
    return { role_id: 1 } as Role;
  },
  updateRole: async (id: number, data: any) => {
    // Mock implementation
    return true;
  },
  getAvailablePermissions: async () => {
    // Mock implementation
    return [
      'manage_employees', 'manage_roles', 'manage_products', 'manage_reservations',
      'view_reservations', 'create_reservation', 'update_reservation',
      'manage_clients', 'view_clients', 'create_client', 'update_client',
      'manage_payments', 'view_reports', 'view_statistics'
    ];
  }
};

interface RoleModalProps {
  role: Role | null;
  onClose: () => void;
  onSuccess: (isNew: boolean) => void;
  showToast: (type: 'success' | 'error' | 'info', message: string) => void;
}

interface FormData {
  nom: string;
  description: string;
  permissions: string[];
}

interface PermissionGroup {
  name: string;
  permissions: {
    id: string;
    label: string;
    description: string;
  }[];
}

const RoleModal: React.FC<RoleModalProps> = ({
  role,
  onClose,
  onSuccess,
  showToast
}) => {
  const isNewRole = !role;
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    nom: '',
    description: '',
    permissions: [],
  });
  const [availablePermissions, setAvailablePermissions] = useState<string[]>([]);
  const [errors, setErrors] = useState<Partial<Record<keyof FormData, string>>>({});
  const [isLoading, setIsLoading] = useState(true);

  // Load permissions and initialize form
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        const permissions = await roleService.getAvailablePermissions();
        setAvailablePermissions(permissions);
        
        if (role) {
          setFormData({
            nom: role.nom,
            description: role.description,
            permissions: role.permissions,
          });
        }
      } catch (error) {
        console.error('Error loading permissions:', error);
        showToast('error', 'Erreur lors du chargement des permissions');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadData();
  }, [role, showToast]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error for this field
    if (errors[name as keyof FormData]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name as keyof FormData];
        return newErrors;
      });
    }
  };

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      permissions: checked
        ? [...prev.permissions, permissionId]
        : prev.permissions.filter(id => id !== permissionId)
    }));
  };

  const handleSelectAll = (groupPermissions: string[], checked: boolean) => {
    setFormData(prev => {
      const updatedPermissions = [...prev.permissions];
      
      if (checked) {
        // Add all permissions from this group that aren't already selected
        groupPermissions.forEach(perm => {
          if (!updatedPermissions.includes(perm)) {
            updatedPermissions.push(perm);
          }
        });
      } else {
        // Remove all permissions from this group
        return {
          ...prev,
          permissions: updatedPermissions.filter(p => !groupPermissions.includes(p))
        };
      }
      
      return { ...prev, permissions: updatedPermissions };
    });
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof FormData, string>> = {};
    
    if (!formData.nom.trim()) newErrors.nom = 'Le nom est requis';
    if (!formData.description.trim()) newErrors.description = 'La description est requise';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    try {
      if (isNewRole) {
        // Create new role
        await roleService.createRole(formData);
      } else if (role) {
        // Update existing role
        await roleService.updateRole(role.role_id, formData);
      }
      
      onSuccess(isNewRole);
    } catch (error) {
      console.error('Error saving role:', error);
      showToast('error', `Erreur lors de ${isNewRole ? 'la création' : 'la modification'} du rôle`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Group permissions by category
  const permissionGroups: PermissionGroup[] = [
    {
      name: 'Employés et Rôles',
      permissions: [
        { id: 'manage_employees', label: 'Gérer les employés', description: 'Créer, modifier et supprimer des employés' },
        { id: 'manage_roles', label: 'Gérer les rôles', description: 'Créer, modifier et supprimer des rôles et permissions' },
      ]
    },
    {
      name: 'Réservations',
      permissions: [
        { id: 'manage_reservations', label: 'Gérer les réservations', description: 'Accès complet aux réservations' },
        { id: 'view_reservations', label: 'Voir les réservations', description: 'Consulter les réservations' },
        { id: 'create_reservation', label: 'Créer des réservations', description: 'Ajouter de nouvelles réservations' },
        { id: 'update_reservation', label: 'Modifier des réservations', description: 'Mettre à jour les réservations existantes' },
      ]
    },
    {
      name: 'Clients',
      permissions: [
        { id: 'manage_clients', label: 'Gérer les clients', description: 'Accès complet aux clients' },
        { id: 'view_clients', label: 'Voir les clients', description: 'Consulter les clients' },
        { id: 'create_client', label: 'Créer des clients', description: 'Ajouter de nouveaux clients' },
        { id: 'update_client', label: 'Modifier des clients', description: 'Mettre à jour les clients existants' },
      ]
    },
    {
      name: 'Produits',
      permissions: [
        { id: 'manage_products', label: 'Gérer les produits', description: 'Créer, modifier et supprimer des produits' },
      ]
    },
    {
      name: 'Finances',
      permissions: [
        { id: 'manage_payments', label: 'Gérer les paiements', description: 'Gérer les transactions financières' },
      ]
    },
    {
      name: 'Rapports',
      permissions: [
        { id: 'view_reports', label: 'Voir les rapports', description: 'Accéder aux rapports' },
        { id: 'view_statistics', label: 'Voir les statistiques', description: 'Accéder aux statistiques et analyses' },
      ]
    },
    {
      name: 'Tables',
      permissions: [
        { id: 'view_tables', label: 'Consulter les tables', description: 'Voir la liste des tables' },
        { id: 'manage_tables', label: 'Gérer les tables', description: 'Créer, modifier et supprimer des tables' },
        { id: 'view_table_reservations', label: 'Voir les réservations', description: 'Consulter les réservations de tables' },
        { id: 'manage_table_reservations', label: 'Gérer les réservations', description: 'Créer et modifier les réservations de tables' },
      ]
    },
    {
      name: 'Menus',
      permissions: [
        { id: 'view_menu', label: 'Consulter les menus', description: 'Voir les menus et produits' },
        { id: 'manage_menu', label: 'Gérer les menus', description: 'Modifier les menus et produits' },
        { id: 'manage_menu_prices', label: 'Gérer les prix', description: 'Modifier les prix des produits' },
      ]
    },
    {
      name: 'Commandes',
      permissions: [
        { id: 'view_orders', label: 'Consulter les commandes', description: 'Voir les commandes' },
        { id: 'manage_orders', label: 'Gérer les commandes', description: 'Créer et modifier les commandes' },
        { id: 'view_kitchen_orders', label: 'Voir commandes cuisine', description: 'Accéder aux commandes pour la cuisine' },
      ]
    },
    {
      name: 'POS',
      permissions: [
        { id: 'operate_restaurant_pos', label: 'POS Restaurant', description: 'Utiliser le point de vente restaurant' },
        { id: 'operate_bar_pos', label: 'POS Bar', description: 'Utiliser le point de vente bar' },
      ]
    },
  ].filter(group => 
    // Only show groups that have permissions available in the system
    group.permissions.some(p => availablePermissions.includes(p.id))
  );

  // Filter the permissions in each group to only include available ones
  const filteredGroups = permissionGroups.map(group => ({
    ...group,
    permissions: group.permissions.filter(p => availablePermissions.includes(p.id))
  }));

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-purple-50">
          <h2 className="text-xl font-semibold text-purple-900 flex items-center">
            <ShieldCheck size={20} className="mr-2 text-purple-700" />
            {isNewRole ? 'Créer un rôle' : 'Modifier un rôle'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
            disabled={isSubmitting || isLoading}
          >
            <X size={24} />
          </button>
        </div>
        
        {isLoading ? (
          <div className="p-6 flex flex-col items-center justify-center">
            <Loader size={40} className="text-purple-600 animate-spin mb-4" />
            <p className="text-gray-500">Chargement des données...</p>
          </div>
        ) : (
          <>
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-8rem)]">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="nom" className="block text-sm font-medium text-gray-700 mb-1">
                    Nom du rôle *
                  </label>
                  <input
                    type="text"
                    id="nom"
                    name="nom"
                    value={formData.nom}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 ${
                      errors.nom 
                        ? 'border-red-300 focus:ring-red-500' 
                        : 'border-gray-300 focus:ring-purple-500'
                    }`}
                    disabled={isSubmitting}
                  />
                  {errors.nom && (
                    <p className="mt-1 text-sm text-red-600">{errors.nom}</p>
                  )}
                </div>
                
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description *
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    rows={3}
                    value={formData.description}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 ${
                      errors.description 
                        ? 'border-red-300 focus:ring-red-500' 
                        : 'border-gray-300 focus:ring-purple-500'
                    }`}
                    disabled={isSubmitting}
                  />
                  {errors.description && (
                    <p className="mt-1 text-sm text-red-600">{errors.description}</p>
                  )}
                </div>
                
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-medium text-gray-900">Permissions</h3>
                    <div className="flex items-center text-sm text-gray-500">
                      <Info size={16} className="mr-1" />
                      <span>Sélectionnez les permissions pour ce rôle</span>
                    </div>
                  </div>

                  <div className="border border-gray-200 rounded-md divide-y divide-gray-200">
                    {filteredGroups.map((group, groupIndex) => {
                      const groupPermissionIds = group.permissions.map(p => p.id);
                      const allSelected = groupPermissionIds.every(id => 
                        formData.permissions.includes(id)
                      );
                      const someSelected = groupPermissionIds.some(id => 
                        formData.permissions.includes(id)
                      );
                      
                      return (
                        <div key={groupIndex} className="p-4">
                          <div className="flex items-center mb-3">
                            <input
                              type="checkbox"
                              id={`group-${groupIndex}`}
                              checked={allSelected}
                              onChange={(e) => handleSelectAll(groupPermissionIds, e.target.checked)}
                              className="h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                              disabled={isSubmitting}
                            />
                            <label 
                              htmlFor={`group-${groupIndex}`}
                              className="ml-2 block text-sm font-medium text-gray-900"
                            >
                              {group.name}
                            </label>
                            <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
                              allSelected 
                                ? 'bg-purple-100 text-purple-800' 
                                : someSelected 
                                  ? 'bg-purple-50 text-purple-600' 
                                  : 'bg-gray-100 text-gray-600'
                            }`}>
                              {groupPermissionIds.filter(id => 
                                formData.permissions.includes(id)
                              ).length} / {groupPermissionIds.length}
                            </span>
                          </div>
                          
                          <div className="ml-6 space-y-2">
                            {group.permissions.map((permission, permIndex) => (
                              <div key={permIndex} className="flex items-start">
                                <div className="flex items-center h-5">
                                  <input
                                    type="checkbox"
                                    id={`perm-${permission.id}`}
                                    checked={formData.permissions.includes(permission.id)}
                                    onChange={(e) => handlePermissionChange(permission.id, e.target.checked)}
                                    className="h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                                    disabled={isSubmitting}
                                  />
                                </div>
                                <div className="ml-3 text-sm">
                                  <label 
                                    htmlFor={`perm-${permission.id}`}
                                    className="font-medium text-gray-700"
                                  >
                                    {permission.label}
                                  </label>
                                  <p className="text-gray-500">{permission.description}</p>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </form>
            </div>
            
            <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                disabled={isSubmitting}
              >
                Annuler
              </button>
              <button
                type="button"
                onClick={handleSubmit}
                className="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <div className="flex items-center">
                    <Loader size={16} className="animate-spin mr-2" />
                    <span>En cours...</span>
                  </div>
                ) : (
                  isNewRole ? 'Créer' : 'Enregistrer'
                )}
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default RoleModal;