import React, { useState } from 'react';
import { 
  Edit, Lock, Trash2, CheckCircle, XCircle, AlertCircle 
} from 'lucide-react';
import { Employee } from '../../services/employee.service';
import { Role } from '../../services/role.service';
import ConfirmationModal from './ConfirmationModal';

// Mock service (replace with actual import)
const employeeService = {
  deleteEmployee: async (id: number) => {
    // Mock implementation
    return true;
  },
  updateEmployee: async (id: number, data: any) => {
    // Mock implementation
    return true;
  }
};

interface EmployeesListProps {
  employees: Employee[];
  roles: Role[];
  onEditEmployee: (employee: Employee) => void;
  onChangePassword: (employee: Employee) => void;
  onDeleteSuccess: () => void;
  onToggleActiveSuccess: () => void;
  showToast: (type: 'success' | 'error' | 'info', message: string) => void;
}

const EmployeesList: React.FC<EmployeesListProps> = ({
  employees,
  roles,
  onEditEmployee,
  onChangePassword,
  onDeleteSuccess,
  onToggleActiveSuccess,
  showToast
}) => {
  const [employeeToDelete, setEmployeeToDelete] = useState<Employee | null>(null);
  const [employeeToToggle, setEmployeeToToggle] = useState<Employee | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isToggling, setIsToggling] = useState(false);

  const handleDeleteEmployee = async () => {
    if (!employeeToDelete) return;
    
    setIsDeleting(true);
    try {
      await employeeService.deleteEmployee(employeeToDelete.employe_id);
      onDeleteSuccess();
    } catch (error) {
      console.error('Error deleting employee:', error);
      showToast('error', 'Erreur lors de la suppression de l\'employé');
    } finally {
      setIsDeleting(false);
      setEmployeeToDelete(null);
    }
  };

  const handleToggleActive = async () => {
    if (!employeeToToggle) return;
    
    setIsToggling(true);
    try {
      await employeeService.updateEmployee(employeeToToggle.employe_id, {
        actif: !employeeToToggle.actif
      });
      onToggleActiveSuccess();
    } catch (error) {
      console.error('Error toggling employee status:', error);
      showToast('error', 'Erreur lors de la modification du statut');
    } finally {
      setIsToggling(false);
      setEmployeeToToggle(null);
    }
  };

  // Helper to get role name
  const getRoleName = (roleId: number) => {
    const role = roles.find(r => r.role_id === roleId);
    return role ? role.nom : 'Rôle inconnu';
  };

  if (employees.length === 0) {
    return null; // Empty state is handled by parent component
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Employé
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Contact
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Rôle
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Statut
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Date d'embauche
            </th>
            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {employees.map((employee) => (
            <tr key={employee.employe_id} className="hover:bg-gray-50 transition-colors">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                    <span className="text-blue-600 font-medium">
                      {employee.prenom.charAt(0)}{employee.nom.charAt(0)}
                    </span>
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-gray-900">
                      {employee.prenom} {employee.nom}
                    </div>
                    <div className="text-sm text-gray-500">
                      ID: {employee.employe_id}
                    </div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900">{employee.email}</div>
                <div className="text-sm text-gray-500">{employee.telephone}</div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                  {getRoleName(employee.role_id)}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {employee.actif ? (
                  <span className="flex items-center text-sm text-green-600">
                    <CheckCircle size={16} className="mr-1" />
                    Actif
                  </span>
                ) : (
                  <span className="flex items-center text-sm text-red-600">
                    <XCircle size={16} className="mr-1" />
                    Inactif
                  </span>
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {new Date(employee.date_embauche).toLocaleDateString('fr-FR')}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex items-center justify-end space-x-2">
                  <button
                    onClick={() => onEditEmployee(employee)}
                    className="text-blue-600 hover:text-blue-800 transition-colors p-1"
                    title="Modifier"
                  >
                    <Edit size={18} />
                  </button>
                  <button
                    onClick={() => onChangePassword(employee)}
                    className="text-yellow-600 hover:text-yellow-800 transition-colors p-1"
                    title="Changer le mot de passe"
                  >
                    <Lock size={18} />
                  </button>
                  <button
                    onClick={() => setEmployeeToToggle(employee)}
                    className={`${
                      employee.actif 
                        ? 'text-orange-600 hover:text-orange-800' 
                        : 'text-green-600 hover:text-green-800'
                    } transition-colors p-1`}
                    title={employee.actif ? 'Désactiver' : 'Activer'}
                  >
                    {employee.actif ? <XCircle size={18} /> : <CheckCircle size={18} />}
                  </button>
                  <button
                    onClick={() => setEmployeeToDelete(employee)}
                    className="text-red-600 hover:text-red-800 transition-colors p-1"
                    title="Supprimer"
                  >
                    <Trash2 size={18} />
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      {/* Confirmation Modals */}
      {employeeToDelete && (
        <ConfirmationModal
          title="Supprimer l'employé"
          message={`Êtes-vous sûr de vouloir supprimer l'employé ${employeeToDelete.prenom} ${employeeToDelete.nom} ? Cette action est irréversible.`}
          confirmText="Supprimer"
          confirmButtonClass="bg-red-600 hover:bg-red-700"
          cancelText="Annuler"
          isLoading={isDeleting}
          icon={<AlertCircle className="h-6 w-6 text-red-600" />}
          onConfirm={handleDeleteEmployee}
          onCancel={() => setEmployeeToDelete(null)}
        />
      )}

      {employeeToToggle && (
        <ConfirmationModal
          title={employeeToToggle.actif ? "Désactiver l'employé" : "Activer l'employé"}
          message={`Êtes-vous sûr de vouloir ${employeeToToggle.actif ? 'désactiver' : 'activer'} l'employé ${employeeToToggle.prenom} ${employeeToToggle.nom} ?`}
          confirmText={employeeToToggle.actif ? "Désactiver" : "Activer"}
          confirmButtonClass={employeeToToggle.actif ? "bg-orange-600 hover:bg-orange-700" : "bg-green-600 hover:bg-green-700"}
          cancelText="Annuler"
          isLoading={isToggling}
          icon={employeeToToggle.actif ? 
            <XCircle className="h-6 w-6 text-orange-600" /> : 
            <CheckCircle className="h-6 w-6 text-green-600" />
          }
          onConfirm={handleToggleActive}
          onCancel={() => setEmployeeToToggle(null)}
        />
      )}
    </div>
  );
};

export default EmployeesList;