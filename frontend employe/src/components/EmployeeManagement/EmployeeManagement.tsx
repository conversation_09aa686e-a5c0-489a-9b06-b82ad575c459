import React, { useState, useEffect } from 'react';
import { 
  Search, Plus, Filter, User, Shield, Loader, RefreshCw, 
  ChevronLeft, ChevronRight 
} from 'lucide-react';
import EmployeesList from './EmployeesList';
import RolesList from './RolesList';
import EmployeeModal from './EmployeeModal';
import RoleModal from './RoleModal';
import PasswordChangeModal from './PasswordChangeModal';
import { Employee } from '../../services/employee.service';
import { Role } from '../../services/role.service';
import ToastNotification from './ToastNotification';

// Mock services (replace with actual imports)
const employeeService = {
  getEmployees: async (filters?: {role_id?: number; search?: string}) => {
    // Mock implementation
    return [] as Employee[];
  },
  getRoles: async () => {
    // Mock implementation
    return [] as Role[];
  }
};

const roleService = {
  getRoles: async () => {
    // Mock implementation
    return [] as Role[];
  }
};

type TabType = 'employees' | 'roles';
type ToastType = 'success' | 'error' | 'info';

interface ToastMessage {
  type: ToastType;
  message: string;
  id: number;
}

const EmployeeManagement: React.FC = () => {
  // Tab state
  const [activeTab, setActiveTab] = useState<TabType>('employees');
  
  // Data states
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [filteredEmployees, setFilteredEmployees] = useState<Employee[]>([]);
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRoleFilter, setSelectedRoleFilter] = useState<number | null>(null);
  
  // Loading states
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Modal states
  const [isEmployeeModalOpen, setIsEmployeeModalOpen] = useState(false);
  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  
  // Toast notifications
  const [toasts, setToasts] = useState<ToastMessage[]>([]);

  // Load initial data
  useEffect(() => {
    loadData();
  }, []);

  // Filter employees when search term or role filter changes
  useEffect(() => {
    filterEmployees();
  }, [searchTerm, selectedRoleFilter, employees]);

  const loadData = async () => {
    setIsLoading(true);
    try {
      const [employeesData, rolesData] = await Promise.all([
        employeeService.getEmployees(),
        roleService.getRoles()
      ]);
      
      setEmployees(employeesData);
      setRoles(rolesData);
      setFilteredEmployees(employeesData);
    } catch (error) {
      showToast('error', 'Erreur lors du chargement des données');
      console.error('Error loading data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshData = async () => {
    setIsRefreshing(true);
    try {
      if (activeTab === 'employees') {
        const employeesData = await employeeService.getEmployees({
          role_id: selectedRoleFilter || undefined,
          search: searchTerm || undefined
        });
        setEmployees(employeesData);
        filterEmployees();
      } else {
        const rolesData = await roleService.getRoles();
        setRoles(rolesData);
      }
      showToast('success', 'Données actualisées avec succès');
    } catch (error) {
      showToast('error', 'Erreur lors de l\'actualisation des données');
      console.error('Error refreshing data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const filterEmployees = () => {
    let filtered = [...employees];
    
    // Apply role filter
    if (selectedRoleFilter) {
      filtered = filtered.filter(emp => emp.role_id === selectedRoleFilter);
    }
    
    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(
        emp => 
          emp.nom.toLowerCase().includes(searchLower) ||
          emp.prenom.toLowerCase().includes(searchLower) ||
          emp.email.toLowerCase().includes(searchLower)
      );
    }
    
    setFilteredEmployees(filtered);
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Handle employee actions
  const handleAddEmployee = () => {
    setSelectedEmployee(null);
    setIsEmployeeModalOpen(true);
  };

  const handleEditEmployee = (employee: Employee) => {
    setSelectedEmployee(employee);
    setIsEmployeeModalOpen(true);
  };

  const handleChangePassword = (employee: Employee) => {
    setSelectedEmployee(employee);
    setIsPasswordModalOpen(true);
  };

  // Handle role actions
  const handleAddRole = () => {
    setSelectedRole(null);
    setIsRoleModalOpen(true);
  };

  const handleEditRole = (role: Role) => {
    setSelectedRole(role);
    setIsRoleModalOpen(true);
  };

  // Toast management
  const showToast = (type: ToastType, message: string) => {
    const id = Date.now();
    setToasts(prev => [...prev, { type, message, id }]);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
      setToasts(prev => prev.filter(toast => toast.id !== id));
    }, 5000);
  };

  // Pagination logic
  const totalPages = Math.ceil(filteredEmployees.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentEmployees = filteredEmployees.slice(indexOfFirstItem, indexOfLastItem);

  return (
    <div className="bg-gray-50 min-h-screen p-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h1 className="text-3xl font-bold text-gray-800">
          Gestion des {activeTab === 'employees' ? 'Employés' : 'Rôles'}
        </h1>
        
        <div className="flex items-center space-x-2">
          <button 
            onClick={refreshData} 
            className="bg-white text-blue-600 hover:bg-blue-50 transition-colors p-2 rounded-full"
            disabled={isRefreshing}
          >
            <RefreshCw size={20} className={`${isRefreshing ? 'animate-spin' : ''}`} />
          </button>
          
          <button 
            onClick={activeTab === 'employees' ? handleAddEmployee : handleAddRole}
            className="bg-blue-600 hover:bg-blue-700 text-white transition-colors px-4 py-2 rounded-md flex items-center"
          >
            <Plus size={18} className="mr-1" />
            {activeTab === 'employees' ? 'Ajouter un employé' : 'Créer un rôle'}
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-t-lg shadow-sm mb-0 border-b">
        <div className="flex">
          <button
            className={`px-6 py-3 font-medium text-sm flex items-center border-b-2 ${
              activeTab === 'employees'
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('employees')}
          >
            <User size={18} className="mr-2" />
            Employés
          </button>
          <button
            className={`px-6 py-3 font-medium text-sm flex items-center border-b-2 ${
              activeTab === 'roles'
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('roles')}
          >
            <Shield size={18} className="mr-2" />
            Rôles
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="bg-white rounded-b-lg shadow-sm p-6">
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <Loader size={40} className="text-blue-600 animate-spin mb-4" />
            <p className="text-gray-500">Chargement des données...</p>
          </div>
        ) : (
          <>
            {/* Filter and search section (for employees tab) */}
            {activeTab === 'employees' && (
              <div className="flex flex-col md:flex-row gap-4 mb-6">
                <div className="relative flex-grow">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search size={18} className="text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Rechercher par nom, prénom ou email..."
                    className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                
                <div className="relative w-full md:w-64">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Filter size={18} className="text-gray-400" />
                  </div>
                  <select
                    className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    value={selectedRoleFilter || ''}
                    onChange={(e) => setSelectedRoleFilter(e.target.value ? Number(e.target.value) : null)}
                  >
                    <option value="">Tous les rôles</option>
                    {roles.map(role => (
                      <option key={role.role_id} value={role.role_id}>
                        {role.nom}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            )}

            {/* Content based on active tab */}
            {activeTab === 'employees' ? (
              <EmployeesList 
                employees={currentEmployees}
                roles={roles}
                onEditEmployee={handleEditEmployee}
                onChangePassword={handleChangePassword}
                onDeleteSuccess={() => {
                  refreshData();
                  showToast('success', 'Employé supprimé avec succès');
                }}
                onToggleActiveSuccess={() => {
                  refreshData();
                  showToast('success', 'Statut mis à jour avec succès');
                }}
                showToast={showToast}
              />
            ) : (
              <RolesList 
                roles={roles}
                onEditRole={handleEditRole}
                onDeleteSuccess={() => {
                  refreshData();
                  showToast('success', 'Rôle supprimé avec succès');
                }}
                showToast={showToast}
              />
            )}

            {/* Pagination (for employees tab) */}
            {activeTab === 'employees' && filteredEmployees.length > 0 && (
              <div className="flex items-center justify-between mt-6">
                <div className="text-sm text-gray-500">
                  Affichage de {Math.min(filteredEmployees.length, indexOfFirstItem + 1)} à {Math.min(filteredEmployees.length, indexOfLastItem)} sur {filteredEmployees.length} employés
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className={`p-2 rounded ${
                      currentPage === 1 
                        ? 'text-gray-300 cursor-not-allowed' 
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <ChevronLeft size={20} />
                  </button>
                  
                  <span className="text-sm font-medium">
                    Page {currentPage} sur {totalPages || 1}
                  </span>
                  
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages || totalPages === 0}
                    className={`p-2 rounded ${
                      currentPage === totalPages || totalPages === 0
                        ? 'text-gray-300 cursor-not-allowed' 
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <ChevronRight size={20} />
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Empty state */}
      {!isLoading && 
        ((activeTab === 'employees' && filteredEmployees.length === 0) || 
        (activeTab === 'roles' && roles.length === 0)) && (
        <div className="bg-white rounded-lg shadow-sm p-12 mt-6 text-center">
          <div className="inline-flex items-center justify-center p-4 bg-blue-50 rounded-full mb-4">
            {activeTab === 'employees' ? 
              <User size={32} className="text-blue-600" /> : 
              <Shield size={32} className="text-blue-600" />
            }
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Aucun {activeTab === 'employees' ? 'employé' : 'rôle'} trouvé
          </h3>
          <p className="text-gray-500 mb-4">
            {activeTab === 'employees' 
              ? searchTerm || selectedRoleFilter 
                ? 'Aucun employé ne correspond à vos critères de recherche.' 
                : 'Commencez par ajouter un employé.'
              : 'Commencez par créer un rôle.'
            }
          </p>
          <button
            onClick={activeTab === 'employees' ? handleAddEmployee : handleAddRole}
            className="bg-blue-600 hover:bg-blue-700 text-white transition-colors px-4 py-2 rounded-md inline-flex items-center"
          >
            <Plus size={18} className="mr-1" />
            {activeTab === 'employees' ? 'Ajouter un employé' : 'Créer un rôle'}
          </button>
        </div>
      )}

      {/* Modals */}
      {isEmployeeModalOpen && (
        <EmployeeModal
          employee={selectedEmployee}
          roles={roles}
          onClose={() => setIsEmployeeModalOpen(false)}
          onSuccess={(isNew) => {
            setIsEmployeeModalOpen(false);
            refreshData();
            showToast('success', isNew 
              ? 'Employé créé avec succès' 
              : 'Employé mis à jour avec succès'
            );
          }}
          showToast={showToast}
        />
      )}

      {isRoleModalOpen && (
        <RoleModal
          role={selectedRole}
          onClose={() => setIsRoleModalOpen(false)}
          onSuccess={(isNew) => {
            setIsRoleModalOpen(false);
            refreshData();
            showToast('success', isNew 
              ? 'Rôle créé avec succès' 
              : 'Rôle mis à jour avec succès'
            );
          }}
          showToast={showToast}
        />
      )}

      {isPasswordModalOpen && selectedEmployee && (
        <PasswordChangeModal
          employee={selectedEmployee}
          onClose={() => setIsPasswordModalOpen(false)}
          onSuccess={() => {
            setIsPasswordModalOpen(false);
            showToast('success', 'Mot de passe modifié avec succès');
          }}
          showToast={showToast}
        />
      )}

      {/* Toast notifications */}
      <div className="fixed bottom-4 right-4 flex flex-col space-y-2 z-50">
        {toasts.map(toast => (
          <ToastNotification
            key={toast.id}
            type={toast.type}
            message={toast.message}
            onClose={() => setToasts(prev => prev.filter(t => t.id !== toast.id))}
          />
        ))}
      </div>
    </div>
  );
};

export default EmployeeManagement;