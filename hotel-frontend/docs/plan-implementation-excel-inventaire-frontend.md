# Plan d'Implémentation Frontend - Upload Excel et Inventaire

## Vue d'ensemble

Développement de l'interface utilisateur pour le système d'upload de fichiers Excel et de gestion d'inventaire, intégré aux fonctionnalités existantes de gestion des services du complexe hôtelier.

## État d'avancement

- ✅ **Phase 1** : Types et Interfaces TypeScript - **TERMINÉE**
- ✅ **Phase 2** : Services Frontend - **TERMINÉE**
- ✅ **Phase 3** : Composants UI Réutilisables - **TERMINÉE**
- ✅ **Phase 7** : Composants Manquants - **TERMINÉE**
- ✅ **Phase 4** : Pages Principales - **TERMINÉE**
- ✅ **Phase 5** : Intégration avec l'Existant - **TERMINÉE**
- 🔄 **Phase 6** : Routes et Navigation - **EN COURS** (Routes ajoutées, permissions à intégrer)
- ❌ **Phase 8** : Tests et Validation - **À FAIRE**
- ❌ **Phase 9** : Déploiement et Documentation - **À FAIRE**

## Phase 1 : Types et Interfaces TypeScript ✅ **TERMINÉE**

### 1.1 Types pour l'Inventaire

```typescript
// Types pour les ingrédients
export interface Ingredient {
  ingredient_id: number;
  chaine_id: number;
  complexe_id?: number;
  nom: string;
  description?: string;
  unite_mesure: UniteMesure;
  categorie: CategorieIngredient;
  code_barre?: string;
  prix_unitaire_moyen: number;
  fournisseur_principal_id?: number;
  allergenes?: string[];
  conservation: TypeConservation;
  duree_conservation_jours?: number;
  actif: boolean;
  created_at: string;
  updated_at?: string;
}

export type UniteMesure = 'kg' | 'g' | 'L' | 'mL' | 'unité' | 'pièce' | 'portion';
export type CategorieIngredient = 'Légumes' | 'Viandes' | 'Poissons' | 'Boissons' | 'Épices' | 'Produits laitiers' | 'Céréales' | 'Fruits';
export type TypeConservation = 'Frais' | 'Congelé' | 'Sec' | 'Ambiant';

// Types pour les recettes
export interface Recette {
  recette_id: number;
  produit_id: number;
  service_id: number;
  nom_recette: string;
  description?: string;
  instructions?: string;
  temps_preparation?: number;
  nombre_portions: number;
  cout_total_calcule: number;
  marge_beneficiaire_cible: number;
  prix_vente_suggere: number;
  actif: boolean;
  created_at: string;
  updated_at?: string;
  ingredients?: RecetteIngredient[];
}

export interface RecetteIngredient {
  recette_ingredient_id: number;
  recette_id: number;
  ingredient_id: number;
  ingredient?: Ingredient;
  quantite_necessaire: number;
  unite_mesure: UniteMesure;
  cout_unitaire: number;
  cout_total: number;
  optionnel: boolean;
  notes?: string;
  ordre_ajout?: number;
}

// Types pour le stock
export interface StockIngredient {
  stock_id: number;
  ingredient_id: number;
  ingredient?: Ingredient;
  complexe_id: number;
  service_id?: number;
  quantite_actuelle: number;
  quantite_minimale: number;
  quantite_maximale?: number;
  emplacement_stockage?: string;
  date_derniere_maj: string;
  valeur_stock: number;
  prix_unitaire_actuel: number;
}
```

### 1.2 Types pour l'Import Excel

```typescript
// Types pour les imports
export interface ImportExcel {
  import_id: number;
  complexe_id: number;
  service_id?: number;
  employe_id: number;
  type_import: TypeImport;
  nom_fichier: string;
  chemin_fichier: string;
  taille_fichier: number;
  statut: StatutImport;
  nombre_lignes_total: number;
  nombre_lignes_valides: number;
  nombre_erreurs: number;
  donnees_parsees?: any;
  erreurs_detectees?: ImportError[];
  mapping_colonnes?: ColumnMapping;
  parametres_import?: any;
  date_import: string;
  date_traitement?: string;
  date_finalisation?: string;
  notes?: string;
}

export type TypeImport = 'MENU_RESTAURANT' | 'CARTE_BAR' | 'INVENTAIRE_INGREDIENTS';
export type StatutImport = 'EN_COURS' | 'VALIDE' | 'ERREUR' | 'IMPORTE' | 'ANNULE';

export interface ImportError {
  ligne: number;
  colonne?: string;
  erreur: string;
  valeur_problematique?: any;
  suggestion?: string;
}

export interface ColumnMapping {
  [excelColumn: string]: string; // Mapping colonne Excel -> champ DB
}

// Types pour les templates
export interface TemplateImport {
  template_id: number;
  chaine_id: number;
  type_service: ServiceType;
  type_import: TypeImport;
  nom_template: string;
  description?: string;
  colonnes_requises: TemplateColumn[];
  exemple_donnees: any[];
  regles_validation: ValidationRule[];
  mapping_defaut: ColumnMapping;
  actif: boolean;
  version: string;
  created_at: string;
  updated_at?: string;
}

export interface TemplateColumn {
  nom: string;
  type: 'string' | 'number' | 'date' | 'boolean';
  obligatoire: boolean;
  description: string;
  exemple?: any;
}

export interface ValidationRule {
  champ: string;
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom';
  valeur?: any;
  message: string;
}
```

### 1.3 Types pour les Réponses API

```typescript
// Réponses API spécialisées
export interface IngredientResponse {
  success: boolean;
  data: Ingredient | Ingredient[];
  message?: string;
  pagination?: PaginationInfo;
}

export interface RecetteResponse {
  success: boolean;
  data: Recette | Recette[];
  message?: string;
  analytics?: RecetteAnalytics;
}

export interface ImportResponse {
  success: boolean;
  data: ImportExcel;
  message?: string;
  preview?: ImportPreview;
}

export interface StockResponse {
  success: boolean;
  data: StockIngredient | StockIngredient[];
  message?: string;
  alerts?: StockAlert[];
}

// Types pour les analyses
export interface RecetteAnalytics {
  cout_total: number;
  marge_actuelle: number;
  prix_suggere: number;
  rentabilite: 'Faible' | 'Moyenne' | 'Bonne' | 'Excellente';
  ingredients_couteux: RecetteIngredient[];
}

export interface StockAlert {
  type: 'STOCK_FAIBLE' | 'EXPIRATION_PROCHE' | 'RUPTURE';
  ingredient: Ingredient;
  stock_actuel: number;
  stock_minimal: number;
  jours_restants?: number;
  urgence: 'Faible' | 'Moyenne' | 'Haute' | 'Critique';
}

export interface ImportPreview {
  donnees_valides: any[];
  erreurs: ImportError[];
  statistiques: {
    total_lignes: number;
    lignes_valides: number;
    lignes_erreur: number;
    taux_succes: number;
  };
  suggestions: string[];
}
```

## Phase 2 : Services Frontend ✅ **TERMINÉE**

### 2.1 Service d'Upload (`uploadService.ts`)

```typescript
class UploadService {
  private baseURL = '/api/upload';

  /**
   * Upload d'un fichier Excel
   */
  async uploadExcelFile(
    file: File, 
    typeImport: TypeImport, 
    complexeId: number, 
    serviceId?: number
  ): Promise<ImportExcel> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type_import', typeImport);
    formData.append('complexe_id', complexeId.toString());
    if (serviceId) formData.append('service_id', serviceId.toString());

    const response = await api.post<ImportResponse>(`${this.baseURL}/excel`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });

    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de l\'upload du fichier');
  }

  /**
   * Téléchargement d'un template
   */
  async downloadTemplate(typeImport: TypeImport): Promise<Blob> {
    const response = await api.get(`${this.baseURL}/templates/${typeImport}`, {
      responseType: 'blob'
    });
    return response.data;
  }

  /**
   * Prévisualisation des données importées
   */
  async getImportPreview(importId: number): Promise<ImportPreview> {
    const response = await api.get<ImportResponse>(`${this.baseURL}/preview/${importId}`);
    
    if (response.data.success && response.data.preview) {
      return response.data.preview;
    }
    throw new Error('Erreur lors de la récupération de la prévisualisation');
  }

  /**
   * Validation des données d'import
   */
  async validateImport(importId: number): Promise<ImportExcel> {
    const response = await api.post<ImportResponse>(`${this.baseURL}/validate/${importId}`);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la validation');
  }

  /**
   * Statut de l'import
   */
  async getImportStatus(importId: number): Promise<ImportExcel> {
    const response = await api.get<ImportResponse>(`${this.baseURL}/status/${importId}`);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la récupération du statut');
  }

  /**
   * Polling pour suivre le statut
   */
  async pollImportStatus(
    importId: number, 
    onUpdate: (status: ImportExcel) => void,
    intervalMs: number = 2000
  ): Promise<ImportExcel> {
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          const status = await this.getImportStatus(importId);
          onUpdate(status);

          if (['IMPORTE', 'ERREUR', 'ANNULE'].includes(status.statut)) {
            resolve(status);
          } else {
            setTimeout(poll, intervalMs);
          }
        } catch (error) {
          reject(error);
        }
      };
      poll();
    });
  }
}

export const uploadService = new UploadService();
```

### 2.2 Service d'Inventaire (`inventaireService.ts`)

```typescript
class InventaireService {
  private baseURL = '/api/inventaire';

  /**
   * Gestion des ingrédients
   */
  async getIngredients(
    complexeId: number, 
    filters: IngredientFilters = {}
  ): Promise<{ ingredients: Ingredient[]; pagination: PaginationInfo }> {
    const params = new URLSearchParams({
      complexe_id: complexeId.toString(),
      ...this.buildFilterParams(filters)
    });

    const response = await api.get<IngredientResponse>(`${this.baseURL}/ingredients?${params}`);
    
    if (response.data.success) {
      return {
        ingredients: Array.isArray(response.data.data) ? response.data.data : [response.data.data],
        pagination: response.data.pagination || { page: 1, limit: 50, total: 0, totalPages: 0 }
      };
    }
    throw new Error('Erreur lors de la récupération des ingrédients');
  }

  async createIngredient(ingredientData: Partial<Ingredient>): Promise<Ingredient> {
    const response = await api.post<IngredientResponse>(`${this.baseURL}/ingredients`, ingredientData);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la création de l\'ingrédient');
  }

  async updateIngredient(ingredientId: number, data: Partial<Ingredient>): Promise<Ingredient> {
    const response = await api.put<IngredientResponse>(`${this.baseURL}/ingredients/${ingredientId}`, data);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la mise à jour de l\'ingrédient');
  }

  async deleteIngredient(ingredientId: number): Promise<void> {
    const response = await api.delete(`${this.baseURL}/ingredients/${ingredientId}`);
    
    if (!response.data.success) {
      throw new Error('Erreur lors de la suppression de l\'ingrédient');
    }
  }

  async searchIngredients(query: string, complexeId: number): Promise<Ingredient[]> {
    const response = await api.get<IngredientResponse>(
      `${this.baseURL}/ingredients/search?q=${encodeURIComponent(query)}&complexe_id=${complexeId}`
    );
    
    if (response.data.success) {
      return Array.isArray(response.data.data) ? response.data.data : [response.data.data];
    }
    throw new Error('Erreur lors de la recherche d\'ingrédients');
  }

  /**
   * Gestion du stock
   */
  async getStock(complexeId: number, serviceId?: number): Promise<StockIngredient[]> {
    const params = new URLSearchParams({ complexe_id: complexeId.toString() });
    if (serviceId) params.append('service_id', serviceId.toString());

    const response = await api.get<StockResponse>(`${this.baseURL}/stock?${params}`);
    
    if (response.data.success) {
      return Array.isArray(response.data.data) ? response.data.data : [response.data.data];
    }
    throw new Error('Erreur lors de la récupération du stock');
  }

  async updateStock(
    ingredientId: number, 
    complexeId: number, 
    quantite: number, 
    typeOperation: 'ENTREE' | 'SORTIE' | 'AJUSTEMENT'
  ): Promise<StockIngredient> {
    const response = await api.put<StockResponse>(`${this.baseURL}/stock/${ingredientId}`, {
      complexe_id: complexeId,
      quantite,
      type_operation: typeOperation
    });
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la mise à jour du stock');
  }

  async getStockAlerts(complexeId: number): Promise<StockAlert[]> {
    const response = await api.get<StockResponse>(`${this.baseURL}/alerts/${complexeId}`);
    
    if (response.data.success && response.data.alerts) {
      return response.data.alerts;
    }
    return [];
  }

  /**
   * Analyses et rapports
   */
  async getInventaireAnalytics(complexeId: number, period?: string): Promise<any> {
    const params = new URLSearchParams({ complexe_id: complexeId.toString() });
    if (period) params.append('period', period);

    const response = await api.get(`${this.baseURL}/analytics?${params}`);
    
    if (response.data.success) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la récupération des analyses');
  }

  private buildFilterParams(filters: IngredientFilters): Record<string, string> {
    const params: Record<string, string> = {};
    
    if (filters.categorie) params.categorie = filters.categorie;
    if (filters.conservation) params.conservation = filters.conservation;
    if (filters.actif !== undefined) params.actif = filters.actif.toString();
    if (filters.search) params.search = filters.search;
    if (filters.page) params.page = filters.page.toString();
    if (filters.limit) params.limit = filters.limit.toString();
    
    return params;
  }
}

export interface IngredientFilters {
  categorie?: CategorieIngredient;
  conservation?: TypeConservation;
  actif?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}

export const inventaireService = new InventaireService();
```

### 2.3 Service de Recettes (`recetteService.ts`)

```typescript
class RecetteService {
  private baseURL = '/api/recettes';

  /**
   * CRUD des recettes
   */
  async getRecettesByService(serviceId: number): Promise<Recette[]> {
    const response = await api.get<RecetteResponse>(`${this.baseURL}/service/${serviceId}`);

    if (response.data.success) {
      return Array.isArray(response.data.data) ? response.data.data : [response.data.data];
    }
    throw new Error('Erreur lors de la récupération des recettes');
  }

  async createRecette(recetteData: Partial<Recette>, ingredients: Partial<RecetteIngredient>[] = []): Promise<Recette> {
    const response = await api.post<RecetteResponse>(`${this.baseURL}`, {
      ...recetteData,
      ingredients
    });

    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la création de la recette');
  }

  async updateRecette(recetteId: number, data: Partial<Recette>): Promise<Recette> {
    const response = await api.put<RecetteResponse>(`${this.baseURL}/${recetteId}`, data);

    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la mise à jour de la recette');
  }

  async deleteRecette(recetteId: number): Promise<void> {
    const response = await api.delete(`${this.baseURL}/${recetteId}`);

    if (!response.data.success) {
      throw new Error('Erreur lors de la suppression de la recette');
    }
  }

  async getRecetteDetails(recetteId: number): Promise<Recette> {
    const response = await api.get<RecetteResponse>(`${this.baseURL}/${recetteId}`);

    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la récupération des détails de la recette');
  }

  /**
   * Gestion des ingrédients de recettes
   */
  async addIngredientToRecette(recetteId: number, ingredientData: Partial<RecetteIngredient>): Promise<RecetteIngredient> {
    const response = await api.post(`${this.baseURL}/${recetteId}/ingredients`, ingredientData);

    if (response.data.success) {
      return response.data.data;
    }
    throw new Error('Erreur lors de l\'ajout de l\'ingrédient à la recette');
  }

  async updateRecetteIngredient(recetteId: number, ingredientId: number, data: Partial<RecetteIngredient>): Promise<RecetteIngredient> {
    const response = await api.put(`${this.baseURL}/${recetteId}/ingredients/${ingredientId}`, data);

    if (response.data.success) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la mise à jour de l\'ingrédient');
  }

  async removeIngredientFromRecette(recetteId: number, ingredientId: number): Promise<void> {
    const response = await api.delete(`${this.baseURL}/${recetteId}/ingredients/${ingredientId}`);

    if (!response.data.success) {
      throw new Error('Erreur lors de la suppression de l\'ingrédient de la recette');
    }
  }

  /**
   * Calculs et analyses
   */
  async calculateRecetteCost(recetteId: number): Promise<number> {
    const response = await api.post(`${this.baseURL}/${recetteId}/calculate-cost`);

    if (response.data.success) {
      return response.data.data.cout_total;
    }
    throw new Error('Erreur lors du calcul du coût de la recette');
  }

  async analyzeRecetteProfitability(recetteId: number): Promise<RecetteAnalytics> {
    const response = await api.get<RecetteResponse>(`${this.baseURL}/${recetteId}/profitability`);

    if (response.data.success && response.data.analytics) {
      return response.data.analytics;
    }
    throw new Error('Erreur lors de l\'analyse de rentabilité');
  }

  async optimizeRecettePrice(recetteId: number, targetMargin: number): Promise<number> {
    const response = await api.post(`${this.baseURL}/${recetteId}/optimize-price`, {
      target_margin: targetMargin
    });

    if (response.data.success) {
      return response.data.data.prix_suggere;
    }
    throw new Error('Erreur lors de l\'optimisation du prix');
  }

  /**
   * Utilitaires
   */
  async findRecettesByIngredient(ingredientId: number): Promise<Recette[]> {
    const response = await api.get<RecetteResponse>(`${this.baseURL}/by-ingredient/${ingredientId}`);

    if (response.data.success) {
      return Array.isArray(response.data.data) ? response.data.data : [response.data.data];
    }
    throw new Error('Erreur lors de la recherche de recettes par ingrédient');
  }

  async duplicateRecette(recetteId: number, newName: string): Promise<Recette> {
    const response = await api.post<RecetteResponse>(`${this.baseURL}/${recetteId}/duplicate`, {
      nom_recette: newName
    });

    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la duplication de la recette');
  }
}

export const recetteService = new RecetteService();
```

### 2.4 Service d'Import (`importService.ts`)

```typescript
class ImportService {
  private baseURL = '/api/imports';

  /**
   * Gestion du processus d'import
   */
  async processImport(importId: number): Promise<ImportExcel> {
    const response = await api.post<ImportResponse>(`${this.baseURL}/${importId}/process`);

    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors du traitement de l\'import');
  }

  async getImportHistory(complexeId: number, filters: ImportFilters = {}): Promise<ImportExcel[]> {
    const params = new URLSearchParams({
      complexe_id: complexeId.toString(),
      ...this.buildImportFilterParams(filters)
    });

    const response = await api.get<ImportResponse>(`${this.baseURL}/history?${params}`);

    if (response.data.success) {
      return Array.isArray(response.data.data) ? response.data.data : [response.data.data];
    }
    throw new Error('Erreur lors de la récupération de l\'historique');
  }

  async rollbackImport(importId: number): Promise<void> {
    const response = await api.post(`${this.baseURL}/${importId}/rollback`);

    if (!response.data.success) {
      throw new Error('Erreur lors de l\'annulation de l\'import');
    }
  }

  async getImportReport(importId: number, format: 'json' | 'pdf' | 'excel' = 'json'): Promise<any> {
    const response = await api.get(`${this.baseURL}/${importId}/report?format=${format}`, {
      responseType: format === 'json' ? 'json' : 'blob'
    });

    return response.data;
  }

  /**
   * Gestion des templates
   */
  async getTemplates(typeService?: ServiceType): Promise<TemplateImport[]> {
    const params = typeService ? `?type_service=${typeService}` : '';
    const response = await api.get(`${this.baseURL}/templates${params}`);

    if (response.data.success) {
      return Array.isArray(response.data.data) ? response.data.data : [response.data.data];
    }
    throw new Error('Erreur lors de la récupération des templates');
  }

  async createTemplate(templateData: Partial<TemplateImport>): Promise<TemplateImport> {
    const response = await api.post(`${this.baseURL}/templates`, templateData);

    if (response.data.success) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la création du template');
  }

  private buildImportFilterParams(filters: ImportFilters): Record<string, string> {
    const params: Record<string, string> = {};

    if (filters.type_import) params.type_import = filters.type_import;
    if (filters.statut) params.statut = filters.statut;
    if (filters.date_debut) params.date_debut = filters.date_debut;
    if (filters.date_fin) params.date_fin = filters.date_fin;
    if (filters.page) params.page = filters.page.toString();
    if (filters.limit) params.limit = filters.limit.toString();

    return params;
  }
}

export interface ImportFilters {
  type_import?: TypeImport;
  statut?: StatutImport;
  date_debut?: string;
  date_fin?: string;
  page?: number;
  limit?: number;
}

export const importService = new ImportService();
```

## Phase 3 : Composants UI Réutilisables ✅ **TERMINÉE**

### 3.1 Composant d'Upload de Fichier (`FileUploadZone.tsx`)

```typescript
interface FileUploadZoneProps {
  onFileSelect: (file: File) => void;
  acceptedTypes: string[];
  maxSize: number; // en MB
  disabled?: boolean;
  loading?: boolean;
  error?: string;
}

export const FileUploadZone: React.FC<FileUploadZoneProps> = ({
  onFileSelect,
  acceptedTypes,
  maxSize,
  disabled = false,
  loading = false,
  error
}) => {
  const [dragActive, setDragActive] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (disabled || loading) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      validateAndSelectFile(files[0]);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      validateAndSelectFile(e.target.files[0]);
    }
  };

  const validateAndSelectFile = (file: File) => {
    // Validation du type
    if (!acceptedTypes.includes(file.type)) {
      toast.error(`Type de fichier non supporté. Types acceptés: ${acceptedTypes.join(', ')}`);
      return;
    }

    // Validation de la taille
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > maxSize) {
      toast.error(`Fichier trop volumineux. Taille maximum: ${maxSize}MB`);
      return;
    }

    onFileSelect(file);
  };

  return (
    <div className="w-full">
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center transition-colors
          ${dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}
          ${disabled || loading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:border-gray-400'}
          ${error ? 'border-red-300 bg-red-50' : ''}
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => !disabled && !loading && inputRef.current?.click()}
      >
        <input
          ref={inputRef}
          type="file"
          className="hidden"
          accept={acceptedTypes.join(',')}
          onChange={handleFileInput}
          disabled={disabled || loading}
        />

        {loading ? (
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
            <p className="text-gray-600">Traitement en cours...</p>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <Upload className="h-12 w-12 text-gray-400 mb-4" />
            <p className="text-lg font-medium text-gray-700 mb-2">
              Glissez votre fichier Excel ici ou cliquez pour sélectionner
            </p>
            <p className="text-sm text-gray-500 mb-4">
              Formats acceptés: Excel (.xlsx, .xls) - Taille max: {maxSize}MB
            </p>
            <button
              type="button"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              disabled={disabled || loading}
            >
              Sélectionner un fichier
            </button>
          </div>
        )}
      </div>

      {error && (
        <div className="mt-2 text-sm text-red-600 flex items-center">
          <AlertCircle className="h-4 w-4 mr-1" />
          {error}
        </div>
      )}
    </div>
  );
};
```

### 3.2 Composant de Prévisualisation (`ImportPreview.tsx`)

```typescript
interface ImportPreviewProps {
  preview: ImportPreview;
  onConfirm: () => void;
  onCancel: () => void;
  loading?: boolean;
}

export const ImportPreview: React.FC<ImportPreviewProps> = ({
  preview,
  onConfirm,
  onCancel,
  loading = false
}) => {
  const [activeTab, setActiveTab] = useState<'donnees' | 'erreurs' | 'statistiques'>('donnees');

  const getStatutColor = (taux: number) => {
    if (taux >= 90) return 'text-green-600 bg-green-100';
    if (taux >= 70) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          Prévisualisation de l'import
        </h3>
        <div className={`px-3 py-1 rounded-full text-sm font-medium ${getStatutColor(preview.statistiques.taux_succes)}`}>
          {preview.statistiques.taux_succes.toFixed(1)}% de réussite
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-4 gap-4 mb-6">
        <div className="text-center p-4 bg-gray-50 rounded-lg">
          <div className="text-2xl font-bold text-gray-900">{preview.statistiques.total_lignes}</div>
          <div className="text-sm text-gray-600">Total lignes</div>
        </div>
        <div className="text-center p-4 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">{preview.statistiques.lignes_valides}</div>
          <div className="text-sm text-gray-600">Lignes valides</div>
        </div>
        <div className="text-center p-4 bg-red-50 rounded-lg">
          <div className="text-2xl font-bold text-red-600">{preview.statistiques.lignes_erreur}</div>
          <div className="text-sm text-gray-600">Lignes avec erreurs</div>
        </div>
        <div className="text-center p-4 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">{preview.suggestions.length}</div>
          <div className="text-sm text-gray-600">Suggestions</div>
        </div>
      </div>

      {/* Onglets */}
      <div className="border-b border-gray-200 mb-4">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'donnees', label: 'Données valides', count: preview.donnees_valides.length },
            { id: 'erreurs', label: 'Erreurs', count: preview.erreurs.length },
            { id: 'statistiques', label: 'Suggestions', count: preview.suggestions.length }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </nav>
      </div>

      {/* Contenu des onglets */}
      <div className="min-h-64 max-h-96 overflow-auto">
        {activeTab === 'donnees' && (
          <DataTable data={preview.donnees_valides.slice(0, 10)} />
        )}

        {activeTab === 'erreurs' && (
          <ErrorList errors={preview.erreurs} />
        )}

        {activeTab === 'statistiques' && (
          <SuggestionList suggestions={preview.suggestions} />
        )}
      </div>

      {/* Actions */}
      <div className="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
        <div className="text-sm text-gray-600">
          {preview.statistiques.lignes_erreur > 0 && (
            <span className="text-yellow-600">
              ⚠️ Des erreurs ont été détectées. L'import ne traitera que les lignes valides.
            </span>
          )}
        </div>

        <div className="flex space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            disabled={loading}
          >
            Annuler
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            disabled={loading || preview.statistiques.lignes_valides === 0}
          >
            {loading ? 'Import en cours...' : `Confirmer l'import (${preview.statistiques.lignes_valides} lignes)`}
          </button>
        </div>
      </div>
    </div>
  );
};
```

### 3.3 Composant de Gestion des Ingrédients (`IngredientManager.tsx`)

```typescript
interface IngredientManagerProps {
  complexeId: number;
  serviceId?: number;
  mode: 'selection' | 'management';
  onIngredientSelect?: (ingredient: Ingredient) => void;
  selectedIngredients?: number[];
}

export const IngredientManager: React.FC<IngredientManagerProps> = ({
  complexeId,
  serviceId,
  mode,
  onIngredientSelect,
  selectedIngredients = []
}) => {
  const [ingredients, setIngredients] = useState<Ingredient[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState<IngredientFilters>({});
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingIngredient, setEditingIngredient] = useState<Ingredient | null>(null);

  useEffect(() => {
    loadIngredients();
  }, [complexeId, filters]);

  const loadIngredients = async () => {
    try {
      setLoading(true);
      const result = await inventaireService.getIngredients(complexeId, filters);
      setIngredients(result.ingredients);
    } catch (error) {
      toast.error('Erreur lors du chargement des ingrédients');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateIngredient = async (data: Partial<Ingredient>) => {
    try {
      const newIngredient = await inventaireService.createIngredient({
        ...data,
        complexe_id: complexeId
      });
      setIngredients(prev => [newIngredient, ...prev]);
      setShowCreateModal(false);
      toast.success('Ingrédient créé avec succès');
    } catch (error) {
      toast.error('Erreur lors de la création de l\'ingrédient');
    }
  };

  const handleUpdateIngredient = async (id: number, data: Partial<Ingredient>) => {
    try {
      const updatedIngredient = await inventaireService.updateIngredient(id, data);
      setIngredients(prev => prev.map(ing =>
        ing.ingredient_id === id ? updatedIngredient : ing
      ));
      setEditingIngredient(null);
      toast.success('Ingrédient mis à jour avec succès');
    } catch (error) {
      toast.error('Erreur lors de la mise à jour de l\'ingrédient');
    }
  };

  const handleDeleteIngredient = async (id: number) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet ingrédient ?')) return;

    try {
      await inventaireService.deleteIngredient(id);
      setIngredients(prev => prev.filter(ing => ing.ingredient_id !== id));
      toast.success('Ingrédient supprimé avec succès');
    } catch (error) {
      toast.error('Erreur lors de la suppression de l\'ingrédient');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header avec filtres */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">
          Gestion des Ingrédients
        </h2>
        {mode === 'management' && (
          <button
            onClick={() => setShowCreateModal(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-2 inline" />
            Nouvel ingrédient
          </button>
        )}
      </div>

      {/* Filtres */}
      <IngredientFilters
        filters={filters}
        onFiltersChange={setFilters}
        onReset={() => setFilters({})}
      />

      {/* Liste des ingrédients */}
      {loading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <IngredientList
          ingredients={ingredients}
          mode={mode}
          selectedIngredients={selectedIngredients}
          onSelect={onIngredientSelect}
          onEdit={setEditingIngredient}
          onDelete={handleDeleteIngredient}
        />
      )}

      {/* Modals */}
      {showCreateModal && (
        <IngredientModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSave={handleCreateIngredient}
          title="Créer un ingrédient"
        />
      )}

      {editingIngredient && (
        <IngredientModal
          isOpen={!!editingIngredient}
          onClose={() => setEditingIngredient(null)}
          onSave={(data) => handleUpdateIngredient(editingIngredient.ingredient_id, data)}
          ingredient={editingIngredient}
          title="Modifier l'ingrédient"
        />
      )}
    </div>
  );
};
```

### 3.4 Composant de Gestion des Recettes (`RecetteManager.tsx`)

```typescript
interface RecetteManagerProps {
  serviceId: number;
  complexeId: number;
}

export const RecetteManager: React.FC<RecetteManagerProps> = ({
  serviceId,
  complexeId
}) => {
  const [recettes, setRecettes] = useState<Recette[]>([]);
  const [loading, setLoading] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingRecette, setEditingRecette] = useState<Recette | null>(null);
  const [selectedRecette, setSelectedRecette] = useState<Recette | null>(null);

  useEffect(() => {
    loadRecettes();
  }, [serviceId]);

  const loadRecettes = async () => {
    try {
      setLoading(true);
      const result = await recetteService.getRecettesByService(serviceId);
      setRecettes(result);
    } catch (error) {
      toast.error('Erreur lors du chargement des recettes');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateRecette = async (data: Partial<Recette>, ingredients: Partial<RecetteIngredient>[]) => {
    try {
      const newRecette = await recetteService.createRecette({
        ...data,
        service_id: serviceId
      }, ingredients);
      setRecettes(prev => [newRecette, ...prev]);
      setShowCreateModal(false);
      toast.success('Recette créée avec succès');
    } catch (error) {
      toast.error('Erreur lors de la création de la recette');
    }
  };

  const handleUpdateRecette = async (id: number, data: Partial<Recette>) => {
    try {
      const updatedRecette = await recetteService.updateRecette(id, data);
      setRecettes(prev => prev.map(rec =>
        rec.recette_id === id ? updatedRecette : rec
      ));
      setEditingRecette(null);
      toast.success('Recette mise à jour avec succès');
    } catch (error) {
      toast.error('Erreur lors de la mise à jour de la recette');
    }
  };

  const handleDeleteRecette = async (id: number) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette recette ?')) return;

    try {
      await recetteService.deleteRecette(id);
      setRecettes(prev => prev.filter(rec => rec.recette_id !== id));
      toast.success('Recette supprimée avec succès');
    } catch (error) {
      toast.error('Erreur lors de la suppression de la recette');
    }
  };

  const handleCalculateCost = async (recetteId: number) => {
    try {
      await recetteService.calculateRecetteCost(recetteId);
      await loadRecettes(); // Recharger pour avoir les nouveaux coûts
      toast.success('Coût recalculé avec succès');
    } catch (error) {
      toast.error('Erreur lors du calcul du coût');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">
          Gestion des Recettes
        </h2>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowCreateModal(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-2 inline" />
            Nouvelle recette
          </button>
        </div>
      </div>

      {/* Liste des recettes */}
      {loading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <RecetteList
          recettes={recettes}
          onEdit={setEditingRecette}
          onDelete={handleDeleteRecette}
          onViewDetails={setSelectedRecette}
          onCalculateCost={handleCalculateCost}
        />
      )}

      {/* Modals */}
      {showCreateModal && (
        <RecetteModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSave={handleCreateRecette}
          complexeId={complexeId}
          title="Créer une recette"
        />
      )}

      {editingRecette && (
        <RecetteModal
          isOpen={!!editingRecette}
          onClose={() => setEditingRecette(null)}
          onSave={(data, ingredients) => handleUpdateRecette(editingRecette.recette_id, data)}
          recette={editingRecette}
          complexeId={complexeId}
          title="Modifier la recette"
        />
      )}

      {selectedRecette && (
        <RecetteDetailsModal
          isOpen={!!selectedRecette}
          onClose={() => setSelectedRecette(null)}
          recette={selectedRecette}
        />
      )}
    </div>
  );
};
```

## Phase 7 : Composants Manquants ✅ **TERMINÉE**

Cette phase a été réalisée pour créer les composants de support et d'analyse qui manquaient pour compléter le système d'inventaire Excel.

### 7.1 Composants d'Analyse

#### InventaireAnalytics.tsx ✅
Composant complet d'analyses d'inventaire avec :
- Métriques principales (valeur stock, nombre d'ingrédients, stock faible, évolution)
- Graphiques des ingrédients les plus coûteux
- Répartition par catégories
- Alertes d'expiration avec tableau détaillé
- Sélecteur de période (7j, 30j, 90j)

#### ServiceRecettesManager.tsx ✅
Gestionnaire de recettes par service avec :
- Sélection de service avec recherche
- Filtrage par type (Restaurant/Bar uniquement)
- Intégration avec RecetteManager existant
- Interface intuitive avec cartes de services

### 7.2 Composants Utilitaires

#### DataTable.tsx ✅
Tableau de données générique avec :
- Affichage formaté des données
- Limitation du nombre de lignes affichées
- Formatage automatique des colonnes
- Tooltips pour contenu tronqué

#### ErrorList.tsx ✅
Liste d'erreurs avancée avec :
- Groupement par type d'erreur
- Interface expandable/collapsible
- Codes couleur par sévérité
- Suggestions de correction
- Conseils d'amélioration

#### SuggestionList.tsx ✅
Liste de suggestions catégorisées avec :
- Catégorisation automatique (optimisation, avertissement, info)
- Icônes et couleurs par type
- Statistiques des suggestions
- Actions recommandées

### 7.3 Pages de Support

#### ImportHistoryPage.tsx ✅
Page complète d'historique des imports avec :
- Filtres avancés (type, statut, dates)
- Tableau des imports avec statuts colorés
- Actions (voir détails, télécharger rapport, annuler import)
- Statistiques de réussite
- Gestion des erreurs

#### RecetteDetailsModal.tsx ✅
Modal détaillé pour les recettes avec :
- Informations générales (temps, portions, coût)
- Analyses de rentabilité en temps réel
- Liste détaillée des ingrédients
- Instructions de préparation
- Métriques de performance

### 7.4 Optimisations

#### ImportPreview.tsx - Optimisé ✅
- Remplacement des composants intégrés par les nouveaux composants réutilisables
- Amélioration de la maintenabilité
- Réduction de la duplication de code

#### Exports mis à jour ✅
- Tous les nouveaux composants exportés dans `index.ts`
- Structure modulaire maintenue
- Imports optimisés

## Phase 4 : Pages Principales

### 4.1 Page d'Import Excel (`ImportExcelPage.tsx`)

```typescript
export const ImportExcelPage: React.FC = () => {
  const { complexeId } = useAuth();
  const [currentStep, setCurrentStep] = useState<'upload' | 'preview' | 'processing' | 'complete'>('upload');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [typeImport, setTypeImport] = useState<TypeImport>('MENU_RESTAURANT');
  const [serviceId, setServiceId] = useState<number | undefined>();
  const [currentImport, setCurrentImport] = useState<ImportExcel | null>(null);
  const [preview, setPreview] = useState<ImportPreview | null>(null);
  const [loading, setLoading] = useState(false);

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
  };

  const handleUpload = async () => {
    if (!selectedFile || !complexeId) return;

    try {
      setLoading(true);
      const importData = await uploadService.uploadExcelFile(
        selectedFile,
        typeImport,
        complexeId,
        serviceId
      );
      setCurrentImport(importData);

      // Attendre le parsing et récupérer la preview
      await new Promise(resolve => setTimeout(resolve, 2000)); // Délai pour le parsing
      const previewData = await uploadService.getImportPreview(importData.import_id);
      setPreview(previewData);
      setCurrentStep('preview');
    } catch (error) {
      toast.error('Erreur lors de l\'upload du fichier');
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmImport = async () => {
    if (!currentImport) return;

    try {
      setLoading(true);
      setCurrentStep('processing');

      // Démarrer l'import et suivre le statut
      await importService.processImport(currentImport.import_id);

      // Polling du statut
      await uploadService.pollImportStatus(
        currentImport.import_id,
        (status) => {
          setCurrentImport(status);
          if (status.statut === 'IMPORTE') {
            setCurrentStep('complete');
          } else if (status.statut === 'ERREUR') {
            toast.error('Erreur lors de l\'import');
            setCurrentStep('upload');
          }
        }
      );
    } catch (error) {
      toast.error('Erreur lors du traitement de l\'import');
      setCurrentStep('upload');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setCurrentStep('upload');
    setSelectedFile(null);
    setCurrentImport(null);
    setPreview(null);
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Import de Fichiers Excel
        </h1>
        <p className="text-gray-600">
          Importez vos menus, cartes de bar ou inventaires depuis des fichiers Excel
        </p>
      </div>

      {/* Stepper */}
      <ImportStepper currentStep={currentStep} />

      {/* Contenu selon l'étape */}
      <div className="mt-8">
        {currentStep === 'upload' && (
          <UploadStep
            typeImport={typeImport}
            onTypeChange={setTypeImport}
            serviceId={serviceId}
            onServiceChange={setServiceId}
            selectedFile={selectedFile}
            onFileSelect={handleFileSelect}
            onUpload={handleUpload}
            loading={loading}
          />
        )}

        {currentStep === 'preview' && preview && (
          <ImportPreview
            preview={preview}
            onConfirm={handleConfirmImport}
            onCancel={handleReset}
            loading={loading}
          />
        )}

        {currentStep === 'processing' && currentImport && (
          <ProcessingStep import={currentImport} />
        )}

        {currentStep === 'complete' && currentImport && (
          <CompleteStep
            import={currentImport}
            onNewImport={handleReset}
          />
        )}
      </div>
    </div>
  );
};
```

### 4.2 Page de Gestion d'Inventaire (`InventairePage.tsx`)

```typescript
export const InventairePage: React.FC = () => {
  const { complexeId } = useAuth();
  const [activeTab, setActiveTab] = useState<'ingredients' | 'stock' | 'recettes' | 'analytics'>('ingredients');
  const [stockAlerts, setStockAlerts] = useState<StockAlert[]>([]);

  useEffect(() => {
    loadStockAlerts();
  }, [complexeId]);

  const loadStockAlerts = async () => {
    try {
      const alerts = await inventaireService.getStockAlerts(complexeId);
      setStockAlerts(alerts);
    } catch (error) {
      console.error('Erreur lors du chargement des alertes:', error);
    }
  };

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Gestion d'Inventaire
            </h1>
            <p className="text-gray-600">
              Gérez vos ingrédients, stocks et recettes
            </p>
          </div>

          {/* Alertes rapides */}
          {stockAlerts.length > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
                <span className="text-sm font-medium text-yellow-800">
                  {stockAlerts.length} alerte(s) de stock
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Navigation par onglets */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'ingredients', label: 'Ingrédients', icon: Package },
            { id: 'stock', label: 'Stock', icon: Warehouse },
            { id: 'recettes', label: 'Recettes', icon: ChefHat },
            { id: 'analytics', label: 'Analyses', icon: BarChart3 }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Contenu des onglets */}
      <div>
        {activeTab === 'ingredients' && (
          <IngredientManager
            complexeId={complexeId}
            mode="management"
          />
        )}

        {activeTab === 'stock' && (
          <StockManager
            complexeId={complexeId}
            alerts={stockAlerts}
            onAlertsUpdate={loadStockAlerts}
          />
        )}

        {activeTab === 'recettes' && (
          <ServiceRecettesManager complexeId={complexeId} />
        )}

        {activeTab === 'analytics' && (
          <InventaireAnalytics complexeId={complexeId} />
        )}
      </div>
    </div>
  );
};
```

## Phase 5 : Intégration avec l'Existant

### 5.1 Extension du Modal de Service

```typescript
// Ajout d'un onglet "Recettes" dans ServiceModal.tsx
const ServiceModal: React.FC<ServiceModalProps> = ({ ... }) => {
  // ... code existant ...

  const [activeTab, setActiveTab] = useState<'general' | 'horaires' | 'configuration' | 'tarification' | 'recettes'>('general');

  // Nouveau contenu pour l'onglet recettes
  const recettesSection = (
    <div className="space-y-4">
      {service && (
        <RecetteManager
          serviceId={service.service_id}
          complexeId={service.complexe_id}
        />
      )}
    </div>
  );

  return (
    <Modal isOpen={isOpen} onClose={handleCancel} size="xl">
      {/* ... header existant ... */}

      {/* Navigation avec nouvel onglet */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {/* ... onglets existants ... */}
          <button
            className={`px-4 py-2 text-sm font-medium transition-colors duration-200 ${
              activeTab === 'recettes'
                ? 'border-b-2 border-blue-500 text-blue-700'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('recettes')}
          >
            Recettes
          </button>
        </nav>
      </div>

      {/* ... contenu existant ... */}
      {activeTab === 'recettes' && recettesSection}
    </Modal>
  );
};
```

### 5.2 Extension de la Page Services

```typescript
// Ajout d'un bouton d'import dans la page de gestion des services
const ServicesPage: React.FC = () => {
  // ... code existant ...

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          Gestion des Services
        </h1>
        <div className="flex space-x-3">
          <Link
            to="/services/import"
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            <Upload className="h-4 w-4 mr-2 inline" />
            Importer Excel
          </Link>
          <button
            onClick={() => setShowCreateModal(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-2 inline" />
            Nouveau service
          </button>
        </div>
      </div>

      {/* ... reste du contenu existant ... */}
    </div>
  );
};
```

## Phase 6 : Routes et Navigation

### 6.1 Nouvelles Routes

```typescript
// Ajout dans App.tsx ou le routeur principal
const routes = [
  // ... routes existantes ...

  // Routes d'inventaire
  {
    path: '/inventaire',
    element: <InventairePage />,
    permissions: ['VIEW_INVENTORY']
  },
  {
    path: '/inventaire/ingredients',
    element: <IngredientManager complexeId={complexeId} mode="management" />,
    permissions: ['MANAGE_INVENTORY']
  },

  // Routes d'import
  {
    path: '/services/import',
    element: <ImportExcelPage />,
    permissions: ['IMPORT_DATA']
  },
  {
    path: '/import/history',
    element: <ImportHistoryPage />,
    permissions: ['VIEW_IMPORTS']
  },

  // Routes de recettes
  {
    path: '/recettes/:serviceId',
    element: <RecetteManager serviceId={serviceId} complexeId={complexeId} />,
    permissions: ['MANAGE_RECIPES']
  }
];
```

### 6.2 Navigation Menu

```typescript
// Extension du menu de navigation
const navigationItems = [
  // ... items existants ...

  {
    name: 'Inventaire',
    href: '/inventaire',
    icon: Package,
    permissions: ['VIEW_INVENTORY'],
    children: [
      {
        name: 'Ingrédients',
        href: '/inventaire/ingredients',
        permissions: ['MANAGE_INVENTORY']
      },
      {
        name: 'Stock',
        href: '/inventaire/stock',
        permissions: ['VIEW_INVENTORY']
      },
      {
        name: 'Analyses',
        href: '/inventaire/analytics',
        permissions: ['VIEW_INVENTORY']
      }
    ]
  },

  {
    name: 'Import Excel',
    href: '/services/import',
    icon: Upload,
    permissions: ['IMPORT_DATA']
  }
];
```

## Phase 8 : Tests et Validation

### 7.1 Tests des Services

```typescript
// Tests pour uploadService
describe('UploadService', () => {
  test('should upload Excel file successfully', async () => {
    const mockFile = new File(['test'], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const result = await uploadService.uploadExcelFile(mockFile, 'MENU_RESTAURANT', 1);
    expect(result.import_id).toBeDefined();
  });

  test('should handle upload errors', async () => {
    const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    await expect(uploadService.uploadExcelFile(invalidFile, 'MENU_RESTAURANT', 1))
      .rejects.toThrow();
  });
});

// Tests pour inventaireService
describe('InventaireService', () => {
  test('should fetch ingredients with filters', async () => {
    const result = await inventaireService.getIngredients(1, { categorie: 'Légumes' });
    expect(Array.isArray(result.ingredients)).toBe(true);
  });

  test('should create ingredient successfully', async () => {
    const ingredientData = {
      nom: 'Test Ingredient',
      unite_mesure: 'kg' as UniteMesure,
      categorie: 'Légumes' as CategorieIngredient,
      conservation: 'Frais' as TypeConservation
    };
    const result = await inventaireService.createIngredient(ingredientData);
    expect(result.nom).toBe('Test Ingredient');
  });
});
```

### 7.2 Tests des Composants

```typescript
// Tests pour FileUploadZone
describe('FileUploadZone', () => {
  test('should render upload zone correctly', () => {
    render(
      <FileUploadZone
        onFileSelect={jest.fn()}
        acceptedTypes={['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']}
        maxSize={10}
      />
    );
    expect(screen.getByText(/Glissez votre fichier Excel/)).toBeInTheDocument();
  });

  test('should handle file selection', () => {
    const onFileSelect = jest.fn();
    render(
      <FileUploadZone
        onFileSelect={onFileSelect}
        acceptedTypes={['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']}
        maxSize={10}
      />
    );

    const file = new File(['test'], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const input = screen.getByRole('button');
    fireEvent.click(input);
    // Simulation de sélection de fichier...
  });
});
```

## Phase 9 : Déploiement et Documentation

### 8.1 Configuration Build

```typescript
// Ajout dans vite.config.ts ou webpack.config.js
export default defineConfig({
  // ... configuration existante ...

  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'excel-import': [
            './src/services/uploadService.ts',
            './src/services/inventaireService.ts',
            './src/components/import'
          ]
        }
      }
    }
  }
});
```

### 8.2 Variables d'Environnement

```typescript
// Ajout dans .env
VITE_MAX_FILE_SIZE=10485760  # 10MB
VITE_SUPPORTED_EXCEL_TYPES=application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel
VITE_IMPORT_POLLING_INTERVAL=2000
```

## Ordre d'Implémentation Recommandé

### Sprint 1 (2-3 semaines) - Types et Services de Base
1. Définition des types TypeScript
2. Services uploadService et inventaireService
3. Tests unitaires des services

### Sprint 2 (3-4 semaines) - Composants UI de Base
1. FileUploadZone
2. ImportPreview
3. IngredientManager basique
4. Tests des composants

### Sprint 3 (3-4 semaines) - Pages Principales
1. ImportExcelPage
2. InventairePage
3. Intégration avec navigation
4. Tests d'intégration

### Sprint 4 (2-3 semaines) - Gestion des Recettes
1. RecetteService
2. RecetteManager
3. Intégration avec ServiceModal
4. Calculs de coûts

### Sprint 5 (2-3 semaines) - Fonctionnalités Avancées
1. Analytics et rapports
2. Optimisations de performance
3. Gestion d'erreurs avancée
4. Tests de bout en bout

### Sprint 6 (1-2 semaines) - Finalisation
1. Documentation utilisateur
2. Tests de performance
3. Optimisations finales
4. Déploiement

Ce plan frontend détaillé s'intègre parfaitement avec le plan backend et fournit une expérience utilisateur complète pour la gestion d'inventaire et l'import Excel.
```
