# Plan d'Implémentation - Phase 6 & Composants Manquants

## Vue d'ensemble

Ce plan détaille l'implémentation de la **Phase 6 : Routes et Navigation** avec système de permissions complet, ainsi que la création des **composants UI manquants** pour finaliser le système d'inventaire Excel.

## Analyse du Backend

### Système d'Authentification
- **4 types d'utilisateurs** : `super_admin`, `admin_chaine`, `admin_complexe`, `employe`
- **JWT avec rôles** : Token contient `id`, `email`, `role`, `chaine_id`, `complexe_id`
- **Permissions granulaires** : 150+ permissions définies dans `permission.service.js`

### Middlewares de Protection
- `verifyToken` : Vérification JWT
- `checkRole` : Contrôle par rôle
- `checkComplexeAccess` : Accès par complexe
- `checkPermission` : Permissions spécifiques
- `checkInventairePermissions` : Permissions inventaire
- `checkImportPermissions` : Permissions import Excel

### Routes Protégées Backend
```javascript
// Routes inventaire avec middlewares
router.use('/inventaire', verifyToken, inventaireRoutes);
router.use('/upload', verifyToken, uploadRoutes);
router.use('/imports', verifyToken, importRoutes);
router.use('/recettes', verifyToken, recetteRoutes);
```

## Phase 6 : Routes et Navigation

### 6.1 Système de Permissions Frontend ✅

#### Types de Permissions
```typescript
// Types basés sur le backend
export type UserRole = 'super_admin' | 'admin_chaine' | 'admin_complexe' | 'employe';

export interface Permission {
  id: string;
  name: string;
  category: string;
  description: string;
}

export interface UserPermissions {
  role: UserRole;
  permissions: string[];
  complexeId?: number;
  chaineId?: number;
}
```

#### Service de Permissions
```typescript
// services/permission.service.ts
class PermissionService {
  async getUserPermissions(): Promise<UserPermissions>
  async hasPermission(permission: string): Promise<boolean>
  async hasAnyPermission(permissions: string[]): Promise<boolean>
  async hasAllPermissions(permissions: string[]): Promise<boolean>
  async checkInventaireAccess(): Promise<boolean>
  async checkImportAccess(typeImport: string): Promise<boolean>
}
```

### 6.2 Guards de Routes ✅

#### ProtectedRoute Component
```typescript
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  requiredRole?: UserRole[];
  fallback?: React.ReactNode;
}
```

#### RouteGuard Hook
```typescript
const useRouteGuard = (permissions: string[]) => {
  const { hasPermissions, loading, error } = usePermissions();
  return { canAccess, loading, error };
}
```

### 6.3 Navigation avec Permissions ✅

#### Navigation Conditionnelle
- Masquer les liens selon les permissions
- Indicateurs visuels des accès
- Navigation contextuelle par rôle

#### Breadcrumbs Intelligents
- Chemin de navigation dynamique
- Liens conditionnels selon permissions
- Contexte utilisateur (complexe/chaîne)

## Composants UI Manquants

### Priorité HAUTE (Bloquants)

#### 1. UploadSteps Components ✅ PARTIELLEMENT
**Statut** : `UploadStep` existe, manque `ProcessingStep` et `CompleteStep`

**À créer :**
- `ProcessingStep.tsx` - Affichage progression import
- `CompleteStep.tsx` - Résultats et actions post-import

#### 2. IngredientManager Sub-Components ✅ PARTIELLEMENT  
**Statut** : `IngredientFilters` et `IngredientList` intégrés dans le fichier principal

**À optimiser :**
- Extraire `IngredientFilters` en composant séparé
- Extraire `IngredientList` en composant séparé
- Améliorer la réutilisabilité

#### 3. RecetteManager Sub-Components ✅ PARTIELLEMENT
**Statut** : `RecetteList` intégré dans le fichier principal

**À optimiser :**
- Extraire `RecetteList` en composant séparé
- Créer `RecetteFilters` pour filtrage avancé

#### 4. Pages Manquantes ❌
**À créer :**
- `ImportDetailsPage.tsx` - Détails d'un import spécifique
- `TemplatesPage.tsx` - Gestion des templates Excel

### Priorité MOYENNE (Améliorations)

#### 5. Composants de Permissions ❌
**À créer :**
- `PermissionGuard.tsx` - Wrapper conditionnel
- `RoleIndicator.tsx` - Indicateur de rôle utilisateur
- `AccessDenied.tsx` - Page d'accès refusé

#### 6. Navigation Avancée ❌
**À créer :**
- `Breadcrumbs.tsx` - Navigation hiérarchique
- `UserMenu.tsx` - Menu utilisateur avec permissions
- `SidebarNavigation.tsx` - Navigation latérale complète

## Plan d'Implémentation Détaillé

### Étape 1 : Système de Permissions (2-3h)

#### 1.1 Service de Permissions
```typescript
// services/permission.service.ts
- Intégration avec API backend permissions
- Cache des permissions utilisateur
- Méthodes de vérification
```

#### 1.2 Hook usePermissions
```typescript
// hooks/usePermissions.ts
- État global des permissions
- Méthodes de vérification
- Loading et error states
```

#### 1.3 Types et Interfaces
```typescript
// types/permission.types.ts
- Types basés sur backend
- Interfaces pour composants
```

### Étape 2 : Guards et Protection (1-2h)

#### 2.1 ProtectedRoute Component
```typescript
// components/guards/ProtectedRoute.tsx
- Vérification permissions
- Redirection si non autorisé
- Loading states
```

#### 2.2 Route Guards
```typescript
// guards/routeGuards.ts
- Guards spécialisés par section
- Validation complexe/chaîne
```

### Étape 3 : Composants UI Manquants (2-3h)

#### 3.1 ProcessingStep & CompleteStep
```typescript
// components/ui/UploadSteps.tsx (compléter)
- ProcessingStep avec progression
- CompleteStep avec actions
```

#### 3.2 Pages Manquantes
```typescript
// pages/ImportDetailsPage.tsx
// pages/TemplatesPage.tsx
```

#### 3.3 Composants de Permissions
```typescript
// components/guards/PermissionGuard.tsx
// components/ui/RoleIndicator.tsx
// components/ui/AccessDenied.tsx
```

### Étape 4 : Navigation Avancée (1-2h)

#### 4.1 Breadcrumbs
```typescript
// components/navigation/Breadcrumbs.tsx
- Navigation hiérarchique
- Liens conditionnels
```

#### 4.2 Navigation Conditionnelle
```typescript
// Mise à jour MainNavigation.tsx
- Masquage selon permissions
- Indicateurs d'accès
```

### Étape 5 : Intégration et Tests (1h)

#### 5.1 Mise à jour App.tsx
```typescript
// Intégration ProtectedRoute
// Configuration guards
```

#### 5.2 Tests d'Intégration
```typescript
// Test des permissions
// Test des redirections
// Test des composants conditionnels
```

## Structure des Fichiers

```
src/
├── components/
│   ├── guards/
│   │   ├── ProtectedRoute.tsx          # NEW
│   │   ├── PermissionGuard.tsx         # NEW
│   │   └── index.ts                    # NEW
│   ├── navigation/
│   │   ├── Breadcrumbs.tsx             # NEW
│   │   ├── UserMenu.tsx                # NEW
│   │   └── SidebarNavigation.tsx       # NEW
│   └── ui/
│       ├── AccessDenied.tsx            # NEW
│       ├── RoleIndicator.tsx           # NEW
│       └── UploadSteps.tsx             # COMPLÉTER
├── hooks/
│   ├── usePermissions.ts               # NEW
│   └── useRouteGuard.ts                # NEW
├── services/
│   └── permission.service.ts           # NEW
├── types/
│   └── permission.types.ts             # NEW
├── guards/
│   └── routeGuards.ts                  # NEW
└── pages/
    ├── ImportDetailsPage.tsx           # NEW
    ├── TemplatesPage.tsx               # NEW
    └── AccessDenied.tsx                # NEW
```

## Priorités d'Implémentation

### 🔥 **PRIORITÉ 1** (Critique - 3h)
1. Service de permissions + Hook usePermissions
2. ProtectedRoute component
3. ProcessingStep & CompleteStep

### 🔶 **PRIORITÉ 2** (Important - 2h)  
4. Pages manquantes (ImportDetails, Templates)
5. Guards de routes spécialisés
6. Navigation conditionnelle

### 🔵 **PRIORITÉ 3** (Nice to have - 2h)
7. Breadcrumbs et navigation avancée
8. Composants de permissions UI
9. Tests et optimisations

## Estimation Totale : 7-8 heures

Cette implémentation créera un système complet de permissions et navigation, avec tous les composants manquants pour finaliser le système d'inventaire Excel.

## Détails Techniques d'Implémentation

### Permissions Backend Mappées

#### Permissions Inventaire (du backend)
```javascript
// Permissions extraites du backend/services/permission.service.js
const INVENTAIRE_PERMISSIONS = {
  'view_inventory': 'Consulter l\'inventaire',
  'manage_inventory': 'Gérer l\'inventaire',
  'import_data': 'Importer des données',
  'export_data': 'Exporter des données',
  'view_costs': 'Voir les coûts',
  'manage_recipes': 'Gérer les recettes',
  'view_analytics': 'Voir les analyses'
};
```

#### Permissions par Rôle (du backend)
```javascript
const ROLE_PERMISSIONS_MAP = {
  'super_admin': ['*'], // Toutes permissions
  'admin_chaine': [
    'view_inventory', 'manage_inventory', 'import_data',
    'export_data', 'view_costs', 'manage_recipes', 'view_analytics'
  ],
  'admin_complexe': [
    'view_inventory', 'manage_inventory', 'import_data',
    'view_costs', 'manage_recipes', 'view_analytics'
  ],
  'employe': [] // Permissions définies par RolesComplexe
};
```

### Exemples de Code Détaillés

#### Service de Permissions Complet
```typescript
// services/permission.service.ts
import { authService } from './auth.service';
import { apiClient } from './api.client';

interface PermissionResponse {
  permissions: string[];
  role: UserRole;
  complexeId?: number;
  chaineId?: number;
}

class PermissionService {
  private permissionsCache: Map<string, PermissionResponse> = new Map();
  private cacheExpiry: number = 5 * 60 * 1000; // 5 minutes

  async getUserPermissions(): Promise<PermissionResponse> {
    const user = authService.getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const cacheKey = `permissions_${user.id}`;
    const cached = this.permissionsCache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached;
    }

    try {
      const response = await apiClient.get('/permissions/user');
      const permissions = {
        ...response.data,
        timestamp: Date.now()
      };

      this.permissionsCache.set(cacheKey, permissions);
      return permissions;
    } catch (error) {
      console.error('Error fetching permissions:', error);
      throw error;
    }
  }

  async hasPermission(permission: string): Promise<boolean> {
    try {
      const userPerms = await this.getUserPermissions();

      // Super admin a toutes les permissions
      if (userPerms.permissions.includes('*')) return true;

      return userPerms.permissions.includes(permission);
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }

  async checkInventaireAccess(): Promise<boolean> {
    return this.hasAnyPermission([
      'view_inventory', 'manage_inventory', 'import_data'
    ]);
  }

  async checkImportAccess(typeImport: string): Promise<boolean> {
    const hasImportPerm = await this.hasPermission('import_data');
    if (!hasImportPerm) return false;

    // Vérification spécifique par type d'import
    const userPerms = await this.getUserPermissions();
    const importTypePerms = {
      'MENU_RESTAURANT': ['manage_inventory', 'manage_recipes'],
      'CARTE_BAR': ['manage_inventory', 'manage_recipes'],
      'INVENTAIRE_INGREDIENTS': ['manage_inventory']
    };

    const requiredPerms = importTypePerms[typeImport] || [];
    return this.hasAnyPermission(requiredPerms);
  }

  clearCache(): void {
    this.permissionsCache.clear();
  }
}

export const permissionService = new PermissionService();
```

#### Hook usePermissions Avancé
```typescript
// hooks/usePermissions.ts
import { useState, useEffect, useCallback } from 'react';
import { permissionService } from '../services/permission.service';
import { authService } from '../services/auth.service';

interface UsePermissionsReturn {
  permissions: string[];
  role: UserRole;
  loading: boolean;
  error: string | null;
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
  canAccessInventaire: boolean;
  canAccessImport: boolean;
  refresh: () => Promise<void>;
}

export const usePermissions = (): UsePermissionsReturn => {
  const [permissions, setPermissions] = useState<string[]>([]);
  const [role, setRole] = useState<UserRole>('employe');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadPermissions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const userPerms = await permissionService.getUserPermissions();
      setPermissions(userPerms.permissions);
      setRole(userPerms.role);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur de permissions');
      console.error('Error loading permissions:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    const user = authService.getCurrentUser();
    if (user) {
      loadPermissions();
    } else {
      setLoading(false);
    }
  }, [loadPermissions]);

  const hasPermission = useCallback((permission: string): boolean => {
    return permissions.includes('*') || permissions.includes(permission);
  }, [permissions]);

  const hasAnyPermission = useCallback((perms: string[]): boolean => {
    if (permissions.includes('*')) return true;
    return perms.some(perm => permissions.includes(perm));
  }, [permissions]);

  const hasAllPermissions = useCallback((perms: string[]): boolean => {
    if (permissions.includes('*')) return true;
    return perms.every(perm => permissions.includes(perm));
  }, [permissions]);

  const canAccessInventaire = hasAnyPermission([
    'view_inventory', 'manage_inventory', 'import_data'
  ]);

  const canAccessImport = hasPermission('import_data');

  return {
    permissions,
    role,
    loading,
    error,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canAccessInventaire,
    canAccessImport,
    refresh: loadPermissions
  };
};
```

#### ProtectedRoute Component Complet
```typescript
// components/guards/ProtectedRoute.tsx
import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { usePermissions } from '../../hooks/usePermissions';
import { authService } from '../../services/auth.service';
import { Loader } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  requiredRole?: UserRole[];
  requireAuth?: boolean;
  fallback?: React.ReactNode;
  redirectTo?: string;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermissions = [],
  requiredRole = [],
  requireAuth = true,
  fallback,
  redirectTo = '/login'
}) => {
  const location = useLocation();
  const { hasAnyPermission, role, loading, error } = usePermissions();
  const isAuthenticated = authService.isAuthenticated();

  // Vérification d'authentification
  if (requireAuth && !isAuthenticated) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader className="animate-spin h-8 w-8 mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Vérification des permissions...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Erreur de permissions
          </h3>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  // Vérification des rôles
  if (requiredRole.length > 0 && !requiredRole.includes(role)) {
    return fallback || (
      <Navigate to="/access-denied" state={{
        reason: 'role',
        required: requiredRole,
        current: role
      }} replace />
    );
  }

  // Vérification des permissions
  if (requiredPermissions.length > 0 && !hasAnyPermission(requiredPermissions)) {
    return fallback || (
      <Navigate to="/access-denied" state={{
        reason: 'permission',
        required: requiredPermissions
      }} replace />
    );
  }

  return <>{children}</>;
};
```

### Configuration des Routes avec Protection

#### App.tsx avec ProtectedRoute
```typescript
// App.tsx (mise à jour)
import { ProtectedRoute } from './components/guards/ProtectedRoute';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/login" element={<Login />} />

        {/* Routes publiques */}
        <Route path="/patron/complexes" element={<ComplexesList />} />

        {/* Routes protégées basiques */}
        <Route path="/dashboard" element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        } />

        {/* Routes avec permissions spécifiques */}
        <Route path="/inventaire" element={
          <ProtectedRoute requiredPermissions={['view_inventory']}>
            <InventairePage />
          </ProtectedRoute>
        } />

        <Route path="/import-excel" element={
          <ProtectedRoute requiredPermissions={['import_data']}>
            <ImportExcelPage />
          </ProtectedRoute>
        } />

        {/* Routes admin uniquement */}
        <Route path="/employee-management" element={
          <ProtectedRoute
            requiredRole={['super_admin', 'admin_chaine', 'admin_complexe']}
          >
            <EmployeeManagement />
          </ProtectedRoute>
        } />

        {/* Route d'accès refusé */}
        <Route path="/access-denied" element={<AccessDenied />} />

        <Route path="/" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </Router>
  );
}
```

## Tests d'Intégration Recommandés

### Tests de Permissions
```typescript
// __tests__/permissions.test.ts
describe('Permission System', () => {
  test('Super admin has all permissions', async () => {
    // Mock super admin user
    const hasPermission = await permissionService.hasPermission('any_permission');
    expect(hasPermission).toBe(true);
  });

  test('Employee has limited permissions', async () => {
    // Mock employee user
    const canManageInventory = await permissionService.hasPermission('manage_inventory');
    expect(canManageInventory).toBe(false);
  });

  test('Admin complexe can access inventaire', async () => {
    // Mock admin complexe
    const canAccess = await permissionService.checkInventaireAccess();
    expect(canAccess).toBe(true);
  });
});
```

### Tests de Routes Protégées
```typescript
// __tests__/protectedRoutes.test.tsx
describe('Protected Routes', () => {
  test('Redirects to login when not authenticated', () => {
    render(
      <MemoryRouter initialEntries={['/inventaire']}>
        <App />
      </MemoryRouter>
    );

    expect(screen.getByText(/login/i)).toBeInTheDocument();
  });

  test('Shows access denied for insufficient permissions', () => {
    // Mock user without inventory permissions
    render(
      <MemoryRouter initialEntries={['/inventaire']}>
        <App />
      </MemoryRouter>
    );

    expect(screen.getByText(/accès refusé/i)).toBeInTheDocument();
  });
});
```

## Prochaines Étapes

1. **Commencer par l'implémentation du service de permissions** (Priorité 1)
2. **Créer le hook usePermissions** (Priorité 1)
3. **Implémenter ProtectedRoute** (Priorité 1)
4. **Compléter les composants UploadSteps manquants** (Priorité 1)
5. **Créer les pages manquantes** (Priorité 2)
6. **Ajouter la navigation conditionnelle** (Priorité 2)

Cette approche garantit un système robuste et sécurisé, aligné avec votre backend existant.
