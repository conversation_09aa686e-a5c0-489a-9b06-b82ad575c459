{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "clean": "rm -rf node_modules/.vite && rm -rf .vite", "dev:clean": "npm run clean && npm run dev"}, "dependencies": {"@fullcalendar/core": "^6.1.11", "@fullcalendar/daygrid": "^6.1.11", "@fullcalendar/interaction": "^6.1.11", "@fullcalendar/react": "^6.1.11", "@fullcalendar/timegrid": "^6.1.11", "@headlessui/react": "^1.7.18", "@types/react-router-dom": "^5.3.3", "axios": "^1.6.7", "framer-motion": "^11.0.8", "lucide-react": "^0.344.0", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.22.3", "react-toastify": "^11.0.5", "recharts": "^2.12.2", "tailwind-merge": "^3.3.0", "zustand": "^5.0.5"}, "devDependencies": {"@types/node": "^20.11.24", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^6.3.5"}}