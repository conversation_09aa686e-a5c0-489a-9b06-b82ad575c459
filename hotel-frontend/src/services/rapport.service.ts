import api from './api.config';

export interface RapportParams {
  date_debut: string;
  date_fin: string;
  complexe_id?: string;
  mode_paiement?: 'especes' | 'carte' | 'virement' | 'cheque' | 'mobile';
  type_utilisateur?: 'employe' | 'admin_complexe' | 'admin_chaine';
  statut?: 'Validé' | 'Annulé' | 'Remboursé';
  grouper_par?: 'jour' | 'semaine' | 'mois';
}

export interface PaiementRapport {
  paiement_reservation_id: number;
  montant: number;
  mode_paiement: 'especes' | 'carte' | 'virement' | 'cheque' | 'mobile';
  date_paiement: string;
  reference_paiement: string;
  statut: 'Validé' | 'Annulé' | 'Remboursé';
  notes?: string;
  utilisateur_id?: number;
  type_utilisateur: 'employe' | 'admin_complexe' | 'admin_chaine';
  numero_reservation: string;
  statut_reservation: string;
  montant_reservation: number;
  client_nom: string;
  client_prenom: string;
  client_telephone?: string;
  client_email?: string;
  complexe_nom: string;
}

export interface RevenuRapport {
  periode?: string;
  annee?: number;
  mois?: number;
  semaine?: number;
  montant_total: number;
  nombre_paiements: number;
  nombre_reservations: number;
  montant_moyen: number;
  complexe_nom?: string;
}

export interface StatistiquesRapport {
  total_general: number;
  total_paiements: number;
  total_reservations: number;
  montant_moyen_par_paiement: number;
  montant_moyen_par_reservation: number;
}

export interface PeriodeRapport {
  date_debut: string;
  date_fin: string;
  grouper_par?: string;
}

class RapportService {
  // Rapport des paiements de réservations
  async getRapportPaiements(params: RapportParams): Promise<{
    success: boolean;
    data: {
      paiements: PaiementRapport[];
      totaux: {
        montant_total: number;
        par_mode: Record<string, number>;
        par_type_utilisateur: Record<string, number>;
      };
      nombre_paiements: number;
      nombre_paiements_valides: number;
    };
    message?: string;
  }> {
    try {
      const response = await api.get('/rapports/paiements', { params });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération du rapport de paiements:', error);
      throw error;
    }
  }

  // Rapport des revenus de réservations
  async getRapportRevenus(params: RapportParams): Promise<{
    success: boolean;
    data: {
      revenus: RevenuRapport[];
      statistiques: StatistiquesRapport;
      periode: PeriodeRapport;
    };
    message?: string;
  }> {
    try {
      const response = await api.get('/rapports/revenus', { params });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération du rapport de revenus:', error);
      throw error;
    }
  }

  // Rapport détaillé d'un paiement spécifique
  async getDetailsPaiement(paiementId: number): Promise<{
    success: boolean;
    data: PaiementRapport;
    message?: string;
  }> {
    try {
      const response = await api.get(`/rapports/paiements/${paiementId}`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des détails du paiement:', error);
      throw error;
    }
  }

  // Exporter le rapport de paiements en CSV
  async exporterRapportPaiements(params: RapportParams, format: 'csv' | 'excel' = 'csv'): Promise<Blob> {
    try {
      const response = await api.get('/rapports/paiements/export', {
        params: { ...params, format },
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de l\'export du rapport de paiements:', error);
      throw error;
    }
  }

  // Exporter le rapport de revenus en CSV
  async exporterRapportRevenus(params: RapportParams, format: 'csv' | 'excel' = 'csv'): Promise<Blob> {
    try {
      const response = await api.get('/rapports/revenus/export', {
        params: { ...params, format },
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de l\'export du rapport de revenus:', error);
      throw error;
    }
  }
}

export const rapportService = new RapportService();