// Tests pour le service ticket étendu
import { ticketService, CreateServiceTicketData } from '../ticket.service';

// Mock des données de test
const mockServiceTicketData: CreateServiceTicketData = {
  service_id: 1,
  pos_id: 1,
  session_id: 1,
  nom_client: 'Martin Test',
  nombre_personnes: 3,
  duree_heures: 2,
  mode_paiement: 'Espèces',
  prix_total: 45.00
};

const mockTarification = {
  prix_par_personne: 10,
  prix_par_heure: 15,
  prix_forfaitaire: 50
};

describe('TicketService - Service Tickets', () => {
  
  describe('calculateServiceTicketPrice', () => {
    it('should calculate price correctly with per-person and per-hour rates', () => {
      const price = ticketService.calculateServiceTicketPrice(
        mockTarification,
        3, // 3 personnes
        2  // 2 heures
      );
      
      // (3 * 10) + (2 * 15) = 30 + 30 = 60
      // Mais le forfaitaire est de 50, donc max(60, 50) = 60
      expect(price).toBe(60);
    });

    it('should use forfaitaire price when higher', () => {
      const price = ticketService.calculateServiceTicketPrice(
        { ...mockTarification, prix_forfaitaire: 100 },
        1, // 1 personne
        1  // 1 heure
      );
      
      // (1 * 10) + (1 * 15) = 25
      // Forfaitaire = 100, donc max(25, 100) = 100
      expect(price).toBe(100);
    });

    it('should handle missing duration', () => {
      const price = ticketService.calculateServiceTicketPrice(
        mockTarification,
        2 // 2 personnes, pas de durée
      );
      
      // 2 * 10 = 20, pas de prix par heure
      // max(20, 50) = 50 (forfaitaire)
      expect(price).toBe(50);
    });
  });

  describe('validateServiceTicketData', () => {
    it('should return no errors for valid data', () => {
      const errors = ticketService.validateServiceTicketData(mockServiceTicketData);
      expect(errors).toHaveLength(0);
    });

    it('should return errors for missing required fields', () => {
      const invalidData = {
        ...mockServiceTicketData,
        service_id: 0,
        nom_client: '',
        nombre_personnes: 0,
        prix_total: 0
      };

      const errors = ticketService.validateServiceTicketData(invalidData);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors).toContain('L\'ID du service est requis');
      expect(errors).toContain('Le nom du client est requis');
      expect(errors).toContain('Le nombre de personnes doit être supérieur à 0');
      expect(errors).toContain('Le prix total doit être supérieur à 0');
    });

    it('should validate duration when provided', () => {
      const invalidData = {
        ...mockServiceTicketData,
        duree_heures: -1
      };

      const errors = ticketService.validateServiceTicketData(invalidData);
      expect(errors).toContain('La durée doit être supérieure à 0');
    });
  });

  describe('formatServiceTicketForDisplay', () => {
    it('should format ticket data correctly', () => {
      const mockTicket = {
        ticket_id: '1',
        numero_ticket: 'SRV-TEST123',
        type_ticket: 'SERVICE' as const,
        service_id: 1,
        pos_id: 1,
        session_id: 1,
        nom_client: 'Martin Test',
        nombre_personnes: 3,
        duree_heures: 2,
        prix_total: 45.00,
        mode_paiement: 'Espèces' as const,
        statut_paiement: 'Payé' as const,
        statut: 'ACTIF' as const,
        date_emission: '2025-06-04T17:00:00Z',
        date_expiration: '2025-06-04T19:00:00Z',
        qr_code_data: 'mock-qr-data'
      };

      const formatted = ticketService.formatServiceTicketForDisplay(mockTicket);

      expect(formatted.numero).toBe('SRV-TEST123');
      expect(formatted.client).toBe('Martin Test');
      expect(formatted.personnes).toBe(3);
      expect(formatted.duree).toBe('2h');
      expect(formatted.prix).toBe('45FCFA');
      expect(formatted.paiement).toBe('Espèces');
      expect(formatted.statut).toBe('ACTIF');
    });

    it('should handle missing duration', () => {
      const mockTicket = {
        ticket_id: '1',
        numero_ticket: 'SRV-TEST123',
        type_ticket: 'SERVICE' as const,
        service_id: 1,
        pos_id: 1,
        session_id: 1,
        nom_client: 'Martin Test',
        nombre_personnes: 3,
        prix_total: 30.00,
        mode_paiement: 'Carte' as const,
        statut_paiement: 'Payé' as const,
        statut: 'ACTIF' as const,
        date_emission: '2025-06-04T17:00:00Z',
        qr_code_data: 'mock-qr-data'
      };

      const formatted = ticketService.formatServiceTicketForDisplay(mockTicket);
      expect(formatted.duree).toBe('N/A');
      expect(formatted.expiration).toBe('Aucune');
    });
  });
});

// Exemple d'utilisation pour les développeurs
export const exampleUsage = {
  // Créer un ticket de piscine
  createPoolTicket: async () => {
    const ticketData: CreateServiceTicketData = {
      service_id: 1, // ID de la piscine
      pos_id: 1,     // ID du POS
      session_id: 1, // ID de la session de caisse
      nom_client: 'Martin',
      nombre_personnes: 3,
      duree_heures: 2,
      mode_paiement: 'Espèces',
      prix_total: 45.00
    };

    try {
      const result = await ticketService.createServiceTicket(ticketData);
      console.log('Ticket créé:', result.ticket.numero_ticket);
      console.log('QR Code:', result.qr_code);
      return result;
    } catch (error) {
      console.error('Erreur création ticket:', error);
      throw error;
    }
  },

  // Vérifier un ticket
  verifyTicket: async (numero: string) => {
    try {
      const result = await ticketService.verifyServiceTicket(numero);
      if (result.valide) {
        console.log('Ticket valide pour:', result.ticket?.nom_client);
      } else {
        console.log('Ticket invalide:', result.raison);
      }
      return result;
    } catch (error) {
      console.error('Erreur vérification:', error);
      throw error;
    }
  }
};
