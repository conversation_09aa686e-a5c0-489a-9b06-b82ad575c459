import { describe, it, expect, beforeEach, vi } from 'vitest';
import { tableService } from '../table.service';
import { menuService } from '../menu.service';
import { commandeService } from '../commande.service';
import api from '../api.config';

// Mock de l'API
vi.mock('../api.config');
const mockedApi = vi.mocked(api);

// Mock du service d'authentification
vi.mock('../auth.service', () => ({
  authService: {
    isAuthenticated: vi.fn(() => true),
    getCurrentUser: vi.fn(() => ({
      employe_id: 1,
      complexe_id: 1,
      role: 'employe'
    }))
  }
}));

describe('POS Services - Phase 1', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('TableService', () => {
    it('devrait récupérer les tables par service', async () => {
      const mockTables = [
        {
          table_id: 1,
          service_id: 1,
          numero: 'T01',
          capacite: 4,
          zone: 'Terrasse',
          statut: 'Libre',
          created_at: '2024-01-01T00:00:00Z'
        }
      ];

      mockedApi.get.mockResolvedValueOnce({
        data: { success: true, data: mockTables }
      });

      const result = await tableService.getTablesByService(1);
      
      expect(mockedApi.get).toHaveBeenCalledWith('/tables/service/1');
      expect(result).toEqual(mockTables);
    });

    it('devrait créer une nouvelle table', async () => {
      const newTable = {
        service_id: 1,
        numero: 'T02',
        capacite: 2,
        zone: 'Intérieur'
      };

      const mockResponse = {
        table_id: 2,
        ...newTable,
        statut: 'Libre',
        created_at: '2024-01-01T00:00:00Z'
      };

      mockedApi.post.mockResolvedValueOnce({
        data: { success: true, data: mockResponse }
      });

      const result = await tableService.createTable(newTable);
      
      expect(mockedApi.post).toHaveBeenCalledWith('/tables', newTable);
      expect(result).toEqual(mockResponse);
    });

    it('devrait mettre à jour le statut d\'une table', async () => {
      mockedApi.put.mockResolvedValueOnce({
        data: { success: true }
      });

      await tableService.updateTableStatus(1, 'Occupée');
      
      expect(mockedApi.put).toHaveBeenCalledWith('/tables/1/statut', { statut: 'Occupée' });
    });
  });

  describe('MenuService', () => {
    it('devrait récupérer le menu par service', async () => {
      const mockMenu = [
        {
          categorie_id: 1,
          service_id: 1,
          nom: 'Entrées',
          description: 'Nos délicieuses entrées',
          ordre_affichage: 1,
          actif: true,
          items: [
            {
              produit_id: 1,
              service_id: 1,
              nom: 'Salade César',
              prix_vente: 12.50,
              categorie: 'Entrées',
              stock_disponible: 10,
              actif: true,
              created_at: '2024-01-01T00:00:00Z'
            }
          ]
        }
      ];

      mockedApi.get.mockResolvedValueOnce({
        data: { success: true, data: mockMenu }
      });

      const result = await menuService.getMenuByService(1);
      
      expect(mockedApi.get).toHaveBeenCalledWith('/menu/service/1', { params: {} });
      expect(result).toEqual(mockMenu);
    });

    it('devrait vérifier la disponibilité d\'un item', async () => {
      mockedApi.get.mockResolvedValueOnce({
        data: { 
          success: true, 
          data: { 
            disponible: true,
            stock_restant: 5
          } 
        }
      });

      const result = await menuService.checkItemAvailability(1, 2);
      
      expect(mockedApi.get).toHaveBeenCalledWith('/menu/items/1/availability', {
        params: { quantite: 2 }
      });
      expect(result).toBe(true);
    });

    it('devrait créer un nouvel item de menu', async () => {
      const newItem = {
        service_id: 1,
        nom: 'Pizza Margherita',
        description: 'Pizza classique',
        prix_vente: 15.00,
        categorie: 'Plats'
      };

      const mockResponse = {
        produit_id: 2,
        ...newItem,
        stock_disponible: 0,
        actif: true,
        created_at: '2024-01-01T00:00:00Z'
      };

      mockedApi.post.mockResolvedValueOnce({
        data: { success: true, data: mockResponse }
      });

      const result = await menuService.createMenuItem(newItem);
      
      expect(mockedApi.post).toHaveBeenCalledWith('/menu/items', newItem);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('CommandeService', () => {
    it('devrait créer une nouvelle commande', async () => {
      const newCommande = {
        service_id: 1,
        table_id: 1,
        items: [
          {
            produit_id: 1,
            nom: 'Salade César',
            quantite: 2,
            prix_unitaire: 12.50,
            montant_ligne: 25.00
          }
        ],
        type_commande: 'Sur place' as const
      };

      const mockResponse = {
        commande_id: 1,
        ...newCommande,
        employe_id: 1,
        date_commande: '2024-01-01T12:00:00Z',
        statut: 'En cours',
        montant_total: 25.00
      };

      mockedApi.post.mockResolvedValueOnce({
        data: { success: true, data: mockResponse }
      });

      const result = await commandeService.createCommande(newCommande);
      
      expect(mockedApi.post).toHaveBeenCalledWith('/commandes', {
        ...newCommande,
        employe_id: 1
      });
      expect(result).toEqual(mockResponse);
    });

    it('devrait récupérer les commandes par service', async () => {
      const mockCommandes = [
        {
          commande_id: 1,
          service_id: 1,
          table_id: 1,
          employe_id: 1,
          date_commande: '2024-01-01T12:00:00Z',
          statut: 'En cours',
          montant_total: 25.00,
          items: [],
          type_commande: 'Sur place'
        }
      ];

      mockedApi.get.mockResolvedValueOnce({
        data: { success: true, data: mockCommandes }
      });

      const result = await commandeService.getCommandesByService(1);
      
      expect(mockedApi.get).toHaveBeenCalledWith('/commandes/service/1', { params: {} });
      expect(result).toEqual(mockCommandes);
    });

    it('devrait traiter un paiement', async () => {
      const paymentData = {
        montant: 25.00,
        mode_paiement: 'CARTE' as const,
        reference: 'REF123'
      };

      mockedApi.post.mockResolvedValueOnce({
        data: { success: true }
      });

      await commandeService.processPayment(1, paymentData);
      
      expect(mockedApi.post).toHaveBeenCalledWith('/commandes/1/payment', paymentData);
    });

    it('devrait mettre à jour le statut d\'une commande', async () => {
      mockedApi.put.mockResolvedValueOnce({
        data: { success: true }
      });

      await commandeService.updateCommandeStatus(1, 'Servie');
      
      expect(mockedApi.put).toHaveBeenCalledWith('/commandes/1/statut', { statut: 'Servie' });
    });
  });
});
