import { describe, it, expect, beforeEach, vi } from 'vitest';
import axios from 'axios';
import { anonymousReservationService } from '../anonymousReservation.service';
import type { CreateAnonymousReservationParams } from '../anonymousReservation.service';

// Mock axios
vi.mock('axios');
const mockedAxios = vi.mocked(axios);

describe('AnonymousReservationService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('createDemandeReservationAnonyme', () => {
    it('should create an anonymous reservation successfully', async () => {
      const mockParams: CreateAnonymousReservationParams = {
        complexe_id: 1,
        date_arrivee: '2024-01-15',
        date_depart: '2024-01-16',
        heure_debut: '14:00',
        heure_fin: '11:00',
        chambres: [
          {
            chambre_id: '1',
            type_chambre: 'Standard',
            prix_nuit: 100
          }
        ],
        prix_total: 100,
        pseudonyme: 'TestClient',
        commentaires: 'Test reservation'
      };

      const mockResponse = {
        data: {
          success: true,
          data: {
            reservation: {
              reservation_id: '1',
              numero_reservation: 'RES-001',
              complexe_id: 1,
              date_arrivee: '2024-01-15',
              date_depart: '2024-01-16',
              heure_debut: '14:00',
              heure_fin: '11:00',
              statut: 'en_attente',
              type_reservation: 'HEURE',
              montant_total: 100,
              est_anonyme: true,
              created_at: '2024-01-01T00:00:00Z',
              pseudonyme: 'TestClient'
            },
            code_acces_anonyme: 'ANON-123456789012',
            message: 'Réservation créée avec succès'
          }
        }
      };

      mockedAxios.post.mockResolvedValue(mockResponse);

      const result = await anonymousReservationService.createDemandeReservationAnonyme(mockParams);

      expect(result.success).toBe(true);
      expect(result.data.code_acces_anonyme).toBe('ANON-123456789012');
      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.stringContaining('reservations-anonymes/demande'),
        mockParams,
        expect.objectContaining({
          headers: { 'Content-Type': 'application/json' },
          timeout: 10000
        })
      );
    });

    it('should throw error for invalid parameters', async () => {
      const invalidParams = {
        complexe_id: 0, // Invalid
        date_arrivee: '',
        date_depart: '',
        heure_debut: '',
        heure_fin: '',
        chambres: [],
        prix_total: 0
      } as CreateAnonymousReservationParams;

      await expect(
        anonymousReservationService.createDemandeReservationAnonyme(invalidParams)
      ).rejects.toThrow('Paramètres invalides');
    });
  });

  describe('validateAccessCode', () => {
    it('should validate a correct access code format', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            valid: true,
            exists: true,
            expired: false
          }
        }
      };

      mockedAxios.post.mockResolvedValue(mockResponse);

      const result = await anonymousReservationService.validateAccessCode('ANON-123456789012');

      expect(result.valid).toBe(true);
      expect(result.exists).toBe(true);
      expect(result.expired).toBe(false);
    });

    it('should return invalid for incorrect format', async () => {
      const result = await anonymousReservationService.validateAccessCode('INVALID-CODE');

      expect(result.valid).toBe(false);
      expect(result.message).toContain('Format de code d\'accès invalide');
    });
  });

  describe('Static utility methods', () => {
    it('should format access code for display', () => {
      const formatted = anonymousReservationService.constructor.formatAccessCodeForDisplay('ANON-123456789012');
      expect(formatted).toBe('ANON-****-9012');
    });

    it('should generate default pseudonym', () => {
      const pseudonym = anonymousReservationService.constructor.generateDefaultPseudonym();
      expect(pseudonym).toMatch(/^Client-\d{6}$/);
    });

    it('should calculate stay duration', () => {
      const duration = anonymousReservationService.constructor.calculateStayDuration('2024-01-15', '2024-01-17');
      expect(duration).toBe(2);
    });

    it('should get status label', () => {
      expect(anonymousReservationService.constructor.getStatusLabel('en_attente')).toBe('En attente');
      expect(anonymousReservationService.constructor.getStatusLabel('confirmee')).toBe('Confirmée');
      expect(anonymousReservationService.constructor.getStatusLabel('annulee')).toBe('Annulée');
      expect(anonymousReservationService.constructor.getStatusLabel('expiree')).toBe('Expirée');
    });

    it('should get status color', () => {
      expect(anonymousReservationService.constructor.getStatusColor('en_attente')).toContain('yellow');
      expect(anonymousReservationService.constructor.getStatusColor('confirmee')).toContain('green');
      expect(anonymousReservationService.constructor.getStatusColor('annulee')).toContain('red');
      expect(anonymousReservationService.constructor.getStatusColor('expiree')).toContain('gray');
    });
  });
});
