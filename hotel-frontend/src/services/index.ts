export { authService } from './auth.service';
export { reservationService } from './reservation.service';
export { anonymousReservationService } from './anonymousReservation.service';
export { chambreService } from './chambre.service';
export { paiementService } from './paiement.service';
export { notificationService } from './notification.service';
export { statistiqueService } from './statistique.service';
export { nettoyageService } from './nettoyage.service';
export { transactionService } from './transaction.service';
export { tracabiliteService } from './tracabilite.service';
export { ticketService } from './ticket.service';
export { clientService } from './client.service';
export { disponibiliteService } from './disponibilite.service';
export { complexeService } from './complexe.service';
export { employeeService } from './employee.service';
export { roleService } from './role.service';
export { serviceComplexeService } from './service.service';
export { posService } from './pos.service';
export { sessionCaisseService } from './session.service';
export { servicePermissionService } from './servicePermission.service';
export { employeePermissionService } from './employeePermission.service';
export { rapportService } from './rapport.service';

// Services pour le système d'inventaire et import Excel
export { uploadService } from './upload.service';
export { inventaireService } from './inventaire.service';

export { importService } from './import.service';

// Services pour l'import de menus et ingrédients - Phase 4
export { menuImportService } from './menuImport.service';
export { ingredientImportService } from './ingredientImport.service';
export { produitIngredientService } from './produitIngredient.service';
export { menuIntegreService } from './menuIntegre.service';
export { templateGeneratorService } from './templateGenerator.service';

// Services POS Restaurant/Bar - Phase 1
export { tableService } from './table.service';
export { menuService } from './menu.service';
export { commandeService } from './commande.service';

// Ré-exporter les types
export type { LoginCredentials, AuthResponse } from './auth.service';
export type { Reservation, ReservationDetails, CreateReservationParams } from './reservation.service';
export type {
  CreateAnonymousReservationParams,
  AnonymousReservationResponse,
  AnonymousReservation,
  AnonymousReservationDetails,
  UpdateAnonymousReservationParams,
  AccessCodeValidation,
  PublicStats,
  ServiceAvailability
} from './anonymousReservation.service';
// Chambre types exportés plus bas avec les autres types de chambre
export type { PaiementRequest, PaiementResponse } from './paiement.service';
export type {
  TauxOccupation,
  TauxOccupationMoyen,
  TauxOccupationResponse,
  Revenu,
  RevenuTotal,
  RevenusResponse,
  ChambrePopulaire,
  ChambresPopulairesResponse,
  RapportParams as StatistiqueRapportParams,
  RapportResponse
} from './statistique.service';
export type { ChambreEnNettoyage } from './nettoyage.service';
export type {
  TransactionRequest,
  TransactionResponse,
  TransactionDetails,
  SoldeResponse
} from './transaction.service';
export type {
  HistoriqueAction,
  HistoriquePaiement,
  HistoriqueTicket,
  HistoriqueParams,
  StatistiquesActionsResponse,
  RapportParams as TracabiliteRapportParams
} from './tracabilite.service';
export type {
  Notification,
  NotificationResponse,
  NotificationParams,
  CreateNotificationRequest
} from './notification.service';
export type {
  Employee,
  CreateEmployeeData,
  UpdateEmployeeData
} from './employee.service';
export type {
  Role,
  CreateRoleData,
  UpdateRoleData
} from './role.service';
export type {
  EmployeePermissions,
  ServiceAccess,
  EmployeeTypeInfo
} from './employeePermission.service';
export type {
  Ticket,
  TicketDetails,
  TicketVerification,
  TicketGenerationParams,
  TicketStatistiques,
  TicketConfiguration
} from './ticket.service';
export type {
  Chambre,
  ChambreDetails,
  ChambreStatistiques,
  DisponibiliteChambre,
  ChambreParams,
  CreateChambreParams,
  UpdateChambreParams,
  UpdateStatutParams,
  ChambreResponse
} from './chambre.service';
export type {
  Disponibilite,
  DisponibiliteDetails,
  DisponibiliteResponse,
  DisponibiliteParams,
  VerifierDisponibiliteParams,
  CreateDisponibiliteParams,
  UpdateDisponibiliteParams,
  DisponibiliteVerificationResponse
} from './disponibilite.service';
export type {
  Client,
  ClientDetails,
  ClientReservation,
  ClientReservationsResponse,
  ClientParams,
  CreateClientParams,
  UpdateClientParams
} from './client.service';
export type {
  Complexe,
  CreateComplexeParams,
  UpdateComplexeParams,
  ComplexeDetails
} from './complexe.service';
export type {
  ServiceComplexe,
  ServiceType,
  CreateServiceParams,
  UpdateServiceParams,
  ServiceParams,
  ServiceResponse,
  TarificationResponse,
  TarificationTemplateResponse,
  RestaurantTarification,
  BarTarification,
  PiscineTarification,
  TarificationData
} from './service.service';

export type {
  PointDeVente,
  CreatePOSParams,
  UpdatePOSParams,
  POSResponse
} from './pos.service';

export type {
  SessionCaisse,
  CreateSessionParams,
  CloseSessionParams,
  SessionParams,
  SessionResponse
} from './session.service';

// Types pour le système d'inventaire et import Excel
export type {
  // Types d'inventaire
  UniteMesure,
  CategorieIngredient,
  TypeConservation,
  Ingredient,

  StockIngredient,
  IngredientFilters,

  StockAlert,
  InventaireConfig,
  StockThreshold,
  AlertConfig,

  // Types d'import
  TypeImport,
  StatutImport,
  ImportExcel,
  ImportError,
  ColumnMapping,
  TemplateImport,
  TemplateColumn,
  ValidationRule,
  ImportPreview,
  ImportFilters,
  PaginationInfo,

  // Types de réponses API
  BaseApiResponse,
  IngredientResponse,

  ImportResponse,
  StockResponse,
  TemplateResponse,
  InventaireAnalyticsResponse,
  CostCalculationResponse,
  PriceOptimizationResponse,
  ImportReportResponse,
  SearchResponse,
  StockOperationResponse
} from '../types';

// Types pour les services POS Restaurant/Bar - Phase 1
export type {
  Table,
  TableReservation,
  CreateTableParams,
  UpdateTableParams,
  CreateTableReservationParams,
  TableResponse,
  TableReservationResponse,
  TableLayoutData
} from './table.service';

export type {
  MenuItem,
  MenuCategory,
  MenuWithAvailability,
  CreateMenuItemParams,
  UpdateMenuItemParams,
  CreateCategoryParams,
  UpdateCategoryParams,
  MenuResponse,
  AvailabilityResponse,
  MenuFilters
} from './menu.service';

export type {
  CommandeItem,
  Commande,
  CreateCommandeParams,
  UpdateCommandeParams,
  AddItemParams,
  UpdateItemParams,
  PaymentData,
  CommandeResponse,
  CommandeFilters,
  CommandeStats
} from './commande.service';

// Types pour l'import de menus et ingrédients - Phase 4
export type {
  MenuImportResponse,
  MenuValidationResponse,
  ImportStatus,
  ImportError as MenuImportError,
  ImportWarning as MenuImportWarning
} from './menuImport.service';

export type {
  IngredientImportResponse,
  IngredientValidationResponse,
  IngredientImportStatus,
  ImportError as IngredientImportError,
  ImportWarning as IngredientImportWarning
} from './ingredientImport.service';

export type {
  TemplateInfo,
  TemplateColumn,
  ValidationRule as TemplateValidationRule
} from './templateGenerator.service';