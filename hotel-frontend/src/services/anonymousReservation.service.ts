import api from './api.config';
import axios from 'axios';

// Interface pour les paramètres de création d'une réservation anonyme
export interface CreateAnonymousReservationParams {
  complexe_id: number;
  date_arrivee: string;
  date_depart: string;
  heure_debut: string;
  heure_fin: string;
  chambres: Array<{
    chambre_id: string;
    type_chambre: string;
    prix_nuit: number;
  }>;
  prix_total: number;
  pseudonyme?: string;
  commentaires?: string;
}

// Interface pour la réponse de création d'une réservation anonyme
export interface AnonymousReservationResponse {
  reservation: AnonymousReservation;
  code_acces_anonyme: string;
  message: string;
}

// Interface pour une réservation anonyme
export interface AnonymousReservation {
  reservation_id: string;
  numero_reservation: string;
  complexe_id: number;
  date_arrivee: string;
  date_depart: string;
  heure_debut: string;
  heure_fin: string;
  statut: 'en_attente' | 'confirmee' | 'annulee' | 'expiree';
  type_reservation: 'HEURE';
  montant_total: number;
  commentaires?: string;
  qr_code?: string;
  est_anonyme: boolean;
  created_at: string;
  updated_at?: string;
  pseudonyme?: string;
}

// Interface pour les détails d'une réservation anonyme
export interface AnonymousReservationDetails extends AnonymousReservation {
  chambres: Array<{
    chambre_id: string;
    numero: string;
    type_chambre: string;
    prix_nuit: number;
  }>;
  duree_sejour: number;
  statut_libelle: string;
  code_acces_anonyme?: string;
  complexe_nom?: string;
  complexe_adresse?: string;
}

// Interface pour les paramètres de modification d'une réservation anonyme
export interface UpdateAnonymousReservationParams {
  commentaires?: string;
  // Note: Les modifications sont limitées pour les réservations anonymes
}

// Interface pour la validation d'un code d'accès
export interface AccessCodeValidation {
  valid: boolean;
  exists: boolean;
  expired: boolean;
  message?: string;
}

// Interface pour les statistiques publiques
export interface PublicStats {
  total_reservations_anonymes: number;
  reservations_actives: number;
  taux_confirmation: number;
  delai_moyen_confirmation: number;
}

// Interface pour la disponibilité du service
export interface ServiceAvailability {
  available: boolean;
  message?: string;
  configuration?: {
    actif: boolean;
    duree_validite_code_heures: number;
    max_reservations_par_ip: number;
    prefixe_code: string;
  };
}

// Interface générique pour les réponses API
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
  errors?: string[];
}

/**
 * Service pour gérer les réservations anonymes côté frontend
 * Permet aux clients de créer, consulter, modifier et annuler des réservations sans authentification
 */
class AnonymousReservationService {

  /**
   * Créer une demande de réservation anonyme
   * @param params Paramètres de la réservation
   * @returns Promesse avec la réponse de création
   */
  async createDemandeReservationAnonyme(params: CreateAnonymousReservationParams): Promise<ApiResponse<AnonymousReservationResponse>> {
    try {
      // Validation côté client
      this.validateReservationParams(params);

      const response = await axios.post<ApiResponse<AnonymousReservationResponse>>(
        `${api.defaults.baseURL}reservations-anonymes/demande`,
        params,
        {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 10000, // 10 secondes pour la création
        }
      );

      return response.data;
    } catch (error) {
      console.error('Erreur lors de la création de la réservation anonyme:', error);
      
      if (axios.isAxiosError(error)) {
        if (error.response) {
          // Erreur de réponse du serveur
          throw new Error(error.response.data.message || 'Erreur lors de la création de la réservation');
        } else if (error.request) {
          // Erreur de réseau
          throw new Error('Aucune réponse du serveur. Veuillez vérifier votre connexion internet.');
        }
      }
      
      throw new Error('Erreur lors de la configuration de la requête');
    }
  }

  /**
   * Consulter une réservation anonyme par code d'accès
   * @param codeAcces Code d'accès de la réservation
   * @returns Promesse avec les détails de la réservation
   */
  async getReservationAnonyme(codeAcces: string): Promise<ApiResponse<AnonymousReservationDetails>> {
    try {
      // Validation du format du code d'accès
      this.validateAccessCodeFormat(codeAcces);

      const response = await axios.get<ApiResponse<AnonymousReservationDetails>>(
        `${api.defaults.baseURL}reservations-anonymes/${encodeURIComponent(codeAcces)}`,
        {
          timeout: 5000,
        }
      );

      return response.data;
    } catch (error) {
      console.error('Erreur lors de la consultation de la réservation anonyme:', error);
      
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 404) {
          throw new Error('Réservation non trouvée ou code d\'accès invalide');
        } else if (error.response?.status === 410) {
          throw new Error('Cette réservation a expiré');
        } else if (error.response) {
          throw new Error(error.response.data.message || 'Erreur lors de la consultation de la réservation');
        } else if (error.request) {
          throw new Error('Aucune réponse du serveur. Veuillez vérifier votre connexion internet.');
        }
      }
      
      throw new Error('Erreur lors de la configuration de la requête');
    }
  }

  /**
   * Modifier une réservation anonyme (fonctionnalités limitées)
   * @param codeAcces Code d'accès de la réservation
   * @param params Paramètres de modification
   * @returns Promesse avec la réservation modifiée
   */
  async updateReservationAnonyme(codeAcces: string, params: UpdateAnonymousReservationParams): Promise<ApiResponse<AnonymousReservation>> {
    try {
      // Validation du format du code d'accès
      this.validateAccessCodeFormat(codeAcces);

      const response = await axios.patch<ApiResponse<AnonymousReservation>>(
        `${api.defaults.baseURL}reservations-anonymes/${encodeURIComponent(codeAcces)}`,
        params,
        {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 5000,
        }
      );

      return response.data;
    } catch (error) {
      console.error('Erreur lors de la modification de la réservation anonyme:', error);
      
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 404) {
          throw new Error('Réservation non trouvée ou code d\'accès invalide');
        } else if (error.response?.status === 403) {
          throw new Error('Cette réservation ne peut plus être modifiée');
        } else if (error.response) {
          throw new Error(error.response.data.message || 'Erreur lors de la modification de la réservation');
        } else if (error.request) {
          throw new Error('Aucune réponse du serveur. Veuillez vérifier votre connexion internet.');
        }
      }
      
      throw new Error('Erreur lors de la configuration de la requête');
    }
  }

  /**
   * Annuler une réservation anonyme
   * @param codeAcces Code d'accès de la réservation
   * @returns Promesse avec la confirmation d'annulation
   */
  async cancelReservationAnonyme(codeAcces: string): Promise<ApiResponse<void>> {
    try {
      // Validation du format du code d'accès
      this.validateAccessCodeFormat(codeAcces);

      const response = await axios.delete<ApiResponse<void>>(
        `${api.defaults.baseURL}reservations-anonymes/${encodeURIComponent(codeAcces)}`,
        {
          timeout: 5000,
        }
      );

      return response.data;
    } catch (error) {
      console.error('Erreur lors de l\'annulation de la réservation anonyme:', error);
      
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 404) {
          throw new Error('Réservation non trouvée ou code d\'accès invalide');
        } else if (error.response?.status === 403) {
          throw new Error('Cette réservation ne peut plus être annulée');
        } else if (error.response) {
          throw new Error(error.response.data.message || 'Erreur lors de l\'annulation de la réservation');
        } else if (error.request) {
          throw new Error('Aucune réponse du serveur. Veuillez vérifier votre connexion internet.');
        }
      }
      
      throw new Error('Erreur lors de la configuration de la requête');
    }
  }

  /**
   * Valider un code d'accès (utilitaire pour le frontend)
   * @param codeAcces Code d'accès à valider
   * @returns Promesse avec le résultat de la validation
   */
  async validateAccessCode(codeAcces: string): Promise<AccessCodeValidation> {
    try {
      // Validation du format côté client
      if (!this.isValidAccessCodeFormat(codeAcces)) {
        return {
          valid: false,
          exists: false,
          expired: false,
          message: 'Format de code d\'accès invalide'
        };
      }

      const response = await axios.post<ApiResponse<AccessCodeValidation>>(
        `${api.defaults.baseURL}reservations-anonymes/validate-code`,
        { code_acces: codeAcces },
        {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 3000,
        }
      );

      return response.data.data;
    } catch (error) {
      console.error('Erreur lors de la validation du code d\'accès:', error);

      return {
        valid: false,
        exists: false,
        expired: false,
        message: 'Erreur lors de la validation du code'
      };
    }
  }

  /**
   * Récupérer les statistiques publiques des réservations anonymes
   * @returns Promesse avec les statistiques publiques
   */
  async getPublicStats(): Promise<ApiResponse<PublicStats>> {
    try {
      const response = await axios.get<ApiResponse<PublicStats>>(
        `${api.defaults.baseURL}reservations-anonymes/stats/public`,
        {
          timeout: 5000,
        }
      );

      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);

      if (axios.isAxiosError(error)) {
        if (error.response) {
          throw new Error(error.response.data.message || 'Erreur lors de la récupération des statistiques');
        } else if (error.request) {
          throw new Error('Aucune réponse du serveur. Veuillez vérifier votre connexion internet.');
        }
      }

      throw new Error('Erreur lors de la configuration de la requête');
    }
  }

  /**
   * Vérifier la disponibilité du service pour un complexe
   * @param complexeId ID du complexe
   * @returns Promesse avec la disponibilité du service
   */
  async checkServiceAvailability(complexeId: number): Promise<ApiResponse<ServiceAvailability>> {
    try {
      const response = await axios.get<ApiResponse<ServiceAvailability>>(
        `${api.defaults.baseURL}reservations-anonymes/availability/${complexeId}`,
        {
          timeout: 5000,
        }
      );

      return response.data;
    } catch (error) {
      console.error('Erreur lors de la vérification de disponibilité:', error);

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 403) {
          throw new Error('Les réservations anonymes ne sont pas disponibles pour ce complexe');
        } else if (error.response) {
          throw new Error(error.response.data.message || 'Erreur lors de la vérification de disponibilité');
        } else if (error.request) {
          throw new Error('Aucune réponse du serveur. Veuillez vérifier votre connexion internet.');
        }
      }

      throw new Error('Erreur lors de la configuration de la requête');
    }
  }

  // ===== MÉTHODES DE VALIDATION CÔTÉ CLIENT =====

  /**
   * Valider les paramètres de création d'une réservation anonyme
   * @param params Paramètres à valider
   * @throws Error si les paramètres sont invalides
   */
  private validateReservationParams(params: CreateAnonymousReservationParams): void {
    const errors: string[] = [];

    // Validation des dates
    if (!params.date_arrivee || !this.isValidDate(params.date_arrivee)) {
      errors.push('Date d\'arrivée invalide');
    }

    if (!params.date_depart || !this.isValidDate(params.date_depart)) {
      errors.push('Date de départ invalide');
    }

    if (params.date_arrivee && params.date_depart) {
      const dateArrivee = new Date(params.date_arrivee);
      const dateDepart = new Date(params.date_depart);

      if (dateArrivee >= dateDepart) {
        errors.push('La date de départ doit être postérieure à la date d\'arrivée');
      }

      if (dateArrivee < new Date()) {
        errors.push('La date d\'arrivée ne peut pas être dans le passé');
      }
    }

    // Validation des heures
    if (!params.heure_debut || !this.isValidTime(params.heure_debut)) {
      errors.push('Heure de début invalide');
    }

    if (!params.heure_fin || !this.isValidTime(params.heure_fin)) {
      errors.push('Heure de fin invalide');
    }

    // Validation du complexe
    if (!params.complexe_id || params.complexe_id <= 0) {
      errors.push('ID du complexe invalide');
    }

    // Validation des chambres
    if (!params.chambres || !Array.isArray(params.chambres) || params.chambres.length === 0) {
      errors.push('Au moins une chambre doit être sélectionnée');
    }

    if (errors.length > 0) {
      throw new Error(`Paramètres invalides: ${errors.join(', ')}`);
    }
  }

  /**
   * Valider le format d'un code d'accès (méthode privée)
   * @param codeAcces Code d'accès à valider
   * @throws Error si le format est invalide
   */
  private validateAccessCodeFormat(codeAcces: string): void {
    if (!this.isValidAccessCodeFormat(codeAcces)) {
      throw new Error('Format de code d\'accès invalide. Le format attendu est: ANON-XXXXXXXXXXXX');
    }
  }

  /**
   * Vérifier si le format d'un code d'accès est valide
   * @param codeAcces Code d'accès à vérifier
   * @returns true si le format est valide
   */
  private isValidAccessCodeFormat(codeAcces: string): boolean {
    if (!codeAcces || typeof codeAcces !== 'string') {
      return false;
    }

    // Format attendu: ANON-XXXXXXXXXXXX (4 lettres + tiret + 12 caractères alphanumériques)
    const regex = /^[A-Z]{4}-[A-Z0-9]{12}$/;
    return regex.test(codeAcces.toUpperCase());
  }

  /**
   * Vérifier si une date est valide
   * @param dateString Date au format string
   * @returns true si la date est valide
   */
  private isValidDate(dateString: string): boolean {
    if (!dateString) return false;

    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime());
  }

  /**
   * Vérifier si une heure est valide
   * @param timeString Heure au format HH:MM
   * @returns true si l'heure est valide
   */
  private isValidTime(timeString: string): boolean {
    if (!timeString) return false;

    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timeRegex.test(timeString);
  }

  /**
   * Formater un code d'accès pour l'affichage (masquer une partie)
   * @param codeAcces Code d'accès complet
   * @returns Code d'accès partiellement masqué
   */
  static formatAccessCodeForDisplay(codeAcces: string): string {
    if (!codeAcces || codeAcces.length < 8) {
      return '****-****';
    }

    const prefix = codeAcces.substring(0, 4);
    const suffix = codeAcces.substring(codeAcces.length - 4);
    return `${prefix}-****-${suffix}`;
  }

  /**
   * Générer un pseudonyme par défaut basé sur la date
   * @returns Pseudonyme par défaut
   */
  static generateDefaultPseudonym(): string {
    const now = new Date();
    const timestamp = now.getTime().toString().slice(-6);
    return `Client-${timestamp}`;
  }

  /**
   * Calculer la durée d'un séjour en jours
   * @param dateArrivee Date d'arrivée
   * @param dateDepart Date de départ
   * @returns Nombre de jours
   */
  static calculateStayDuration(dateArrivee: string, dateDepart: string): number {
    const arrivee = new Date(dateArrivee);
    const depart = new Date(dateDepart);

    if (isNaN(arrivee.getTime()) || isNaN(depart.getTime())) {
      return 0;
    }

    const diffTime = Math.abs(depart.getTime() - arrivee.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays;
  }

  /**
   * Vérifier si une réservation peut être modifiée
   * @param reservation Réservation à vérifier
   * @returns true si la réservation peut être modifiée
   */
  static canModifyReservation(reservation: AnonymousReservation): boolean {
    return reservation.statut === 'en_attente';
  }

  /**
   * Vérifier si une réservation peut être annulée
   * @param reservation Réservation à vérifier
   * @returns true si la réservation peut être annulée
   */
  static canCancelReservation(reservation: AnonymousReservation): boolean {
    return reservation.statut === 'en_attente' || reservation.statut === 'confirmee';
  }

  /**
   * Obtenir le libellé du statut d'une réservation
   * @param statut Statut de la réservation
   * @returns Libellé du statut
   */
  static getStatusLabel(statut: AnonymousReservation['statut']): string {
    const labels = {
      'en_attente': 'En attente',
      'confirmee': 'Confirmée',
      'annulee': 'Annulée',
      'expiree': 'Expirée'
    };

    return labels[statut] || statut;
  }

  /**
   * Obtenir la couleur associée à un statut
   * @param statut Statut de la réservation
   * @returns Classe CSS pour la couleur
   */
  static getStatusColor(statut: AnonymousReservation['statut']): string {
    const colors = {
      'en_attente': 'text-yellow-600 bg-yellow-100',
      'confirmee': 'text-green-600 bg-green-100',
      'annulee': 'text-red-600 bg-red-100',
      'expiree': 'text-gray-600 bg-gray-100'
    };

    return colors[statut] || 'text-gray-600 bg-gray-100';
  }
}

// Instance singleton du service
export const anonymousReservationService = new AnonymousReservationService();

// Export par défaut
export default anonymousReservationService;
