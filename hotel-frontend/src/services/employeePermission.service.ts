import api from './api.config';
import { authService } from './auth.service';

// ===== INTERFACES POUR LE SYSTÈME SIMPLIFIÉ =====

export interface EmployeePermissions {
  reception_operations: boolean;
  piscine_operations: boolean;
  service_operations: boolean;
  management_operations: boolean;
  kitchen_operations: boolean;
}

export interface ServiceAccess {
  bar: boolean;
  restaurant: boolean;
  piscine: boolean;
  hebergement: boolean;
}

export interface EmployeeTypeInfo {
  type: string;
  label: string;
  permissions: string[];
  defaultServices: string[];
  description: string;
}

// ===== CONSTANTES =====

export const EMPLOYEE_TYPES: Record<string, EmployeeTypeInfo> = {
  reception: {
    type: 'reception',
    label: 'Employé Réception',
    permissions: ['reception_operations'],
    defaultServices: ['hebergement'],
    description: 'Gestion des réservations, clients et chambres'
  },
  gerant_piscine: {
    type: 'gerant_piscine',
    label: 'Gérant Piscine',
    permissions: ['piscine_operations'],
    defaultServices: ['piscine'],
    description: 'Gestion de la piscine et billetterie'
  },
  serveuse: {
    type: 'serveuse',
    label: 'Serveuse',
    permissions: ['service_operations'],
    defaultServices: ['bar', 'restaurant'],
    description: 'Service en salle, prise de commandes'
  },
  gerant_services: {
    type: 'gerant_services',
    label: 'Gérant Services',
    permissions: ['service_operations', 'management_operations'],
    defaultServices: ['bar', 'restaurant'],
    description: 'Gestion des services bar/restaurant et équipe'
  },
  cuisine: {
    type: 'cuisine',
    label: 'Employé Cuisine',
    permissions: ['kitchen_operations'],
    defaultServices: ['restaurant'],
    description: 'Préparation des commandes restaurant'
  }
};

export const SIMPLIFIED_PERMISSIONS = {
  reception_operations: 'Opérations réception',
  piscine_operations: 'Opérations piscine',
  service_operations: 'Opérations service',
  management_operations: 'Opérations gestion',
  kitchen_operations: 'Opérations cuisine'
};

// ===== SERVICE PRINCIPAL =====

class EmployeePermissionService {
  private checkAuth(): void {
    if (!authService.isAuthenticated()) {
      throw new Error('Utilisateur non authentifié');
    }
  }

  /**
   * Récupère les permissions de l'employé connecté
   */
  public async getEmployeePermissions(): Promise<EmployeePermissions> {
    try {
      this.checkAuth();

      // Si admin, toutes les permissions
      if (authService.isAdmin()) {
        return {
          reception_operations: true,
          piscine_operations: true,
          service_operations: true,
          management_operations: true,
          kitchen_operations: true
        };
      }

      // Si employé, permissions selon le type
      const employeeType = authService.getEmployeeType();
      if (!employeeType) {
        throw new Error('Type d\'employé non défini');
      }

      const typeInfo = EMPLOYEE_TYPES[employeeType];
      if (!typeInfo) {
        throw new Error(`Type d'employé inconnu: ${employeeType}`);
      }

      // Construire l'objet permissions
      const permissions: EmployeePermissions = {
        reception_operations: false,
        piscine_operations: false,
        service_operations: false,
        management_operations: false,
        kitchen_operations: false
      };

      // Activer les permissions selon le type
      typeInfo.permissions.forEach(permission => {
        if (permission in permissions) {
          (permissions as any)[permission] = true;
        }
      });

      return permissions;
    } catch (error) {
      console.error('Error getting employee permissions:', error);
      throw error;
    }
  }

  /**
   * Récupère l'accès aux services pour l'employé connecté
   */
  public async getServiceAccess(): Promise<ServiceAccess> {
    try {
      this.checkAuth();

      // Si admin, accès à tous les services
      if (authService.isAdmin()) {
        return {
          bar: true,
          restaurant: true,
          piscine: true,
          hebergement: true
        };
      }

      // Si employé, accès selon les services autorisés
      const authorizedServices = authService.getAuthorizedServices();
      
      return {
        bar: authorizedServices.includes('bar'),
        restaurant: authorizedServices.includes('restaurant'),
        piscine: authorizedServices.includes('piscine'),
        hebergement: authorizedServices.includes('hebergement')
      };
    } catch (error) {
      console.error('Error getting service access:', error);
      throw error;
    }
  }

  /**
   * Vérifie si l'utilisateur a une permission spécifique
   */
  public async hasPermission(permission: string): Promise<boolean> {
    try {
      this.checkAuth();

      // Si admin, toutes les permissions
      if (authService.isAdmin()) {
        return true;
      }

      const permissions = await this.getEmployeePermissions();
      return (permissions as any)[permission] === true;
    } catch (error) {
      console.error(`Error checking permission ${permission}:`, error);
      return false;
    }
  }

  /**
   * Vérifie si l'utilisateur peut accéder à un service spécifique
   */
  public async canAccessService(serviceType: string): Promise<boolean> {
    try {
      this.checkAuth();

      // Si admin, accès à tous les services
      if (authService.isAdmin()) {
        return true;
      }

      const serviceAccess = await this.getServiceAccess();
      const normalizedService = serviceType.toLowerCase();
      
      return (serviceAccess as any)[normalizedService] === true;
    } catch (error) {
      console.error(`Error checking service access for ${serviceType}:`, error);
      return false;
    }
  }

  /**
   * Récupère les informations du type d'employé
   */
  public getEmployeeTypeInfo(type?: string): EmployeeTypeInfo | null {
    const employeeType = type || authService.getEmployeeType();
    if (!employeeType) return null;

    return EMPLOYEE_TYPES[employeeType] || null;
  }

  /**
   * Récupère tous les types d'employés disponibles
   */
  public getAvailableEmployeeTypes(): EmployeeTypeInfo[] {
    return Object.values(EMPLOYEE_TYPES);
  }

  /**
   * Vérifie si l'utilisateur peut gérer les employés
   */
  public canManageEmployees(): boolean {
    return authService.isAdmin();
  }

  /**
   * Vérifie si l'utilisateur peut voir les rapports
   */
  public canViewReports(): boolean {
    return authService.isAdmin();
  }

  /**
   * Vérifie si l'utilisateur peut configurer le système
   */
  public canConfigureSystem(): boolean {
    return authService.isAdmin();
  }

  /**
   * Récupère les services accessibles pour l'utilisateur
   */
  public async getAccessibleServices(): Promise<string[]> {
    try {
      const serviceAccess = await this.getServiceAccess();
      const accessibleServices: string[] = [];

      Object.entries(serviceAccess).forEach(([service, hasAccess]) => {
        if (hasAccess) {
          accessibleServices.push(service);
        }
      });

      return accessibleServices;
    } catch (error) {
      console.error('Error getting accessible services:', error);
      return [];
    }
  }

  /**
   * Vérifie si l'utilisateur peut effectuer des opérations de gestion
   */
  public async canManage(): Promise<boolean> {
    if (authService.isAdmin()) {
      return true;
    }

    return await this.hasPermission('management_operations');
  }
}

export const employeePermissionService = new EmployeePermissionService();
