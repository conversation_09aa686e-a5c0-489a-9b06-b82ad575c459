import api from './api.config';
import { authService } from './auth.service';

// Types pour les imports de menus
export interface MenuImportResponse {
  success: boolean;
  message: string;
  data: {
    import_id: number;
    service_id: number;
    total_items: number;
    imported_items: number;
    errors: ImportError[];
    warnings: ImportWarning[];
    categories_created: number;
    processing_time: number;
  };
}

export interface ImportError {
  ligne: number;
  colonne?: string;
  erreur: string;
  valeur_problematique?: any;
  suggestion?: string;
}

export interface ImportWarning {
  ligne: number;
  colonne?: string;
  message: string;
  valeur: any;
}

export interface MenuValidationResponse {
  success: boolean;
  message: string;
  data: {
    is_valid: boolean;
    total_rows: number;
    valid_rows: number;
    errors: ImportError[];
    warnings: ImportWarning[];
    preview_data: any[];
    suggested_categories: string[];
  };
}

export interface ImportStatus {
  service_id: number;
  last_import_date?: string;
  total_imports: number;
  total_items_imported: number;
  has_menu: boolean;
  categories_count: number;
  items_count: number;
}

/**
 * Service pour la gestion des imports de menus
 */
class MenuImportService {
  private baseURL = 'menu-import';

  /**
   * Vérification de l'authentification
   */
  private checkAuth(): void {
    if (!authService.isAuthenticated()) {
      throw new Error('Utilisateur non authentifié');
    }
  }

  /**
   * Import d'un menu restaurant depuis un fichier Excel
   */
  async importRestaurantMenu(
    serviceId: number, 
    file: File, 
    options?: { overwrite?: boolean; validate_only?: boolean }
  ): Promise<MenuImportResponse> {
    this.checkAuth();

    const formData = new FormData();
    formData.append('menuFile', file);
    
    if (options) {
      formData.append('options', JSON.stringify(options));
    }

    try {
      const response = await api.post<MenuImportResponse>(
        `${this.baseURL}/restaurant/${serviceId}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          timeout: 60000, // 1 minute pour les gros fichiers
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de l\'import du menu restaurant');
      }

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de l\'import du menu restaurant'
      );
    }
  }

  /**
   * Import d'une carte bar depuis un fichier Excel
   */
  async importBarMenu(
    serviceId: number, 
    file: File, 
    options?: { overwrite?: boolean; validate_only?: boolean }
  ): Promise<MenuImportResponse> {
    this.checkAuth();

    const formData = new FormData();
    formData.append('menuFile', file);
    
    if (options) {
      formData.append('options', JSON.stringify(options));
    }

    try {
      const response = await api.post<MenuImportResponse>(
        `${this.baseURL}/bar/${serviceId}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          timeout: 60000,
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de l\'import de la carte bar');
      }

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de l\'import de la carte bar'
      );
    }
  }

  /**
   * Validation d'un fichier Excel de menu restaurant sans l'importer
   */
  async validateRestaurantMenu(file: File): Promise<MenuValidationResponse> {
    this.checkAuth();

    const formData = new FormData();
    formData.append('menuFile', file);

    try {
      const response = await api.post<MenuValidationResponse>(
        `${this.baseURL}/validate/restaurant`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          timeout: 30000,
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la validation du menu restaurant');
      }

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de la validation du menu restaurant'
      );
    }
  }

  /**
   * Validation d'un fichier Excel de carte bar sans l'importer
   */
  async validateBarMenu(file: File): Promise<MenuValidationResponse> {
    this.checkAuth();

    const formData = new FormData();
    formData.append('menuFile', file);

    try {
      const response = await api.post<MenuValidationResponse>(
        `${this.baseURL}/validate/bar`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          timeout: 30000,
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la validation de la carte bar');
      }

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de la validation de la carte bar'
      );
    }
  }

  /**
   * Récupération du statut d'import pour un service
   */
  async getImportStatus(serviceId: number): Promise<ImportStatus> {
    this.checkAuth();

    try {
      const response = await api.get<{
        success: boolean;
        message: string;
        data: ImportStatus;
      }>(`${this.baseURL}/status/${serviceId}`);

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la récupération du statut');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de la récupération du statut d\'import'
      );
    }
  }

  /**
   * Récupération des informations sur les imports de menus
   */
  async getImportInfo(): Promise<{
    supportedFormats: string[];
    maxFileSize: string;
    requiredFields: {
      restaurant: string[];
      bar: string[];
    };
    optionalFields: {
      restaurant: string[];
      bar: string[];
    };
    validCategories: {
      restaurant: string[];
      bar: string[];
    };
  }> {
    try {
      const response = await api.get<{
        success: boolean;
        data: any;
      }>(`${this.baseURL}/info`);

      if (!response.data.success) {
        throw new Error('Erreur lors de la récupération des informations');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de la récupération des informations d\'import'
      );
    }
  }
}

export const menuImportService = new MenuImportService();
export default menuImportService;
