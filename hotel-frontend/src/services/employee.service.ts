import api from './api.config';
import { authService } from './auth.service';

export interface Employee {
  employe_id: number;
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  role_id: number;
  role_nom: string;
  complexe_id: number;
  actif: boolean;
  date_embauche: string;
  created_at: string;
  updated_at: string;
}

export interface CreateEmployeeData {
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  role_id: number;
  password: string;
  complexe_id?: number;
}

export interface UpdateEmployeeData {
  nom?: string;
  prenom?: string;
  email?: string;
  telephone?: string;
  role_id?: number;
  actif?: boolean;
}

export interface EmployeeRole {
  role_id: number;
  nom: string;
  description: string;
  permissions: string[];
}

class EmployeeService {
  private static instance: EmployeeService;

  private constructor() {}

  public static getInstance(): EmployeeService {
    if (!EmployeeService.instance) {
      EmployeeService.instance = new EmployeeService();
    }
    return EmployeeService.instance;
  }

  private checkAuth() {
    if (!authService.isAuthenticated()) {
      throw new Error('Non authentifié');
    }
  }

  private getComplexeId(): number {
    const user = authService.getCurrentUser();
    if (!user) throw new Error('Utilisateur non authentifié');

    // Si l'utilisateur est un admin de chaîne, on utilise le complexe_id sélectionné
    if (user.role === 'admin_chaine') {
      const selectedComplexeId = localStorage.getItem('selectedComplexeId');
      if (selectedComplexeId) {
        return parseInt(selectedComplexeId, 10);
      }
      throw new Error('Aucun complexe sélectionné');
    }

    // Pour les autres types d'utilisateurs, on utilise leur complexe_id
    if (!user.complexe_id) {
      throw new Error('Complexe ID manquant');
    }
    return user.complexe_id;
  }

  public async getEmployees(filters?: {
    role_id?: number;
    search?: string;
    complexe_id?: number;
  }): Promise<Employee[]> {
    try {
      this.checkAuth();

      // Déterminer si on doit inclure le complexe_id en query parameter
      const user = authService.getCurrentUser();
      let url = '/employees';
      let params = { ...filters };

      // Pour les super_admin et admin_chaine, inclure le complexe_id en query
      if (user?.role === 'super_admin' || user?.role === 'admin_chaine') {
        const complexeId = this.getComplexeId();
        params = { ...filters, complexe_id: complexeId };
      }

      const response = await api.get<Employee[]>(url, {
        params
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching employees:', error);
      throw error;
    }
  }

  public async getEmployee(id: number): Promise<Employee> {
    try {
      const response = await api.get<Employee>(`/employees/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching employee:', error);
      throw error;
    }
  }

  public async createEmployee(data: CreateEmployeeData): Promise<Employee> {
    try {
      this.checkAuth();

      // Déterminer si on doit inclure le complexe_id
      const user = authService.getCurrentUser();
      let requestData = { ...data };

      // Pour les super_admin et admin_chaine, inclure le complexe_id
      if (user?.role === 'super_admin' || user?.role === 'admin_chaine') {
        const complexeId = this.getComplexeId();
        requestData = { ...data, complexe_id: complexeId };
      }

      const response = await api.post<Employee>('/employees', requestData);
      return response.data;
    } catch (error) {
      console.error('Error creating employee:', error);
      throw error;
    }
  }

  public async updateEmployee(id: number, data: UpdateEmployeeData): Promise<Employee> {
    try {
      const response = await api.put<Employee>(`/employees/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating employee:', error);
      throw error;
    }
  }

  public async updatePassword(id: number, currentPassword: string, newPassword: string): Promise<void> {
    try {
      await api.put(`/employees/${id}/password`, { currentPassword, newPassword });
    } catch (error) {
      console.error('Error updating password:', error);
      throw error;
    }
  }

  public async deleteEmployee(id: number): Promise<void> {
    try {
      await api.delete(`/employees/${id}`);
    } catch (error) {
      console.error('Error deleting employee:', error);
      throw error;
    }
  }

  public async checkEmployeeDependencies(id: number): Promise<{
    hasDependencies: boolean;
    dependencies: {
      pointsDeVente: number;
      sessionsCaisse: number;
      reservations: number;
      transactions: number;
      commandes: number;
    };
    totalCount: number;
  }> {
    try {
      const response = await api.get(`/employees/${id}/dependencies`);
      return response.data;
    } catch (error) {
      console.error('Error checking employee dependencies:', error);
      throw error;
    }
  }

  public async hardDeleteEmployee(id: number, force: boolean = false): Promise<{
    message: string;
    employee: {
      employe_id: number;
      nom: string;
      prenom: string;
    };
  }> {
    try {
      const response = await api.delete(`/employees/${id}/hard`, {
        params: { force: force.toString() }
      });
      return response.data;
    } catch (error) {
      console.error('Error hard deleting employee:', error);
      throw error;
    }
  }

  public async getRoles(): Promise<EmployeeRole[]> {
    try {
      this.checkAuth();

      // Déterminer si on doit inclure le complexe_id en query parameter
      const user = authService.getCurrentUser();
      let url = '/employees/roles';

      // Pour les super_admin et admin_chaine, inclure le complexe_id en query
      if (user?.role === 'super_admin' || user?.role === 'admin_chaine') {
        const complexeId = this.getComplexeId();
        url = `/employees/roles?complexe_id=${complexeId}`;
      }

      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching roles:', error);
      throw error;
    }
  }
}

export const employeeService = EmployeeService.getInstance();