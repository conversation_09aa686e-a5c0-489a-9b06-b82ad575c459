import api from './api.config';
import { authService } from './auth.service';

export interface Chambre {
  chambre_id: string;
  numero: string;
  type_chambre: string;
  statut: 'active' | 'inactive' | 'maintenance';
  complexe_id: number;
  prix_base: number;
  capacite: number;
  description?: string;
  caracteristiques?: string[];
  images?: string[];
}

export interface ChambreDetails extends Chambre {
  nombre_reservations: number;
  derniere_reservation?: string;
  prochaine_reservation?: string;
  nombre_historique: number;
}

export interface ChambreStatistiques {
  chambre_id: string;
  numero: string;
  type_chambre: string;
  nombre_reservations: number;
  derniere_reservation?: string;
  taux_occupation: number;
  revenus_total: number;
  nombre_nuits: number;
}

export interface DisponibiliteChambre extends Chambre {
  disponible: boolean;
  statut_disponibilite: 'disponible' | 'maintenance' | 'occupee';
}

export interface DisponibilitesResponse {
  disponibilites: {
    type: string;
    total: number;
    disponibles: number;
    chambres: DisponibiliteChambre[];
  }[];
  date_debut: string;
  date_fin: string;
}

export interface ChambreParams {
  type_chambre?: string;
  statut?: string;
  capacite_min?: number;
  capacite_max?: number;
}

export interface CreateChambreParams {
  numero: string;
  type_chambre: string;
  capacite: number;
  prix_base: number;
  description?: string;
  etage?: number;
  caracteristiques?: Record<string, any>;
}

export interface UpdateChambreParams extends Partial<CreateChambreParams> {}

export interface DisponibiliteParams {
  date_debut: string;
  date_fin: string;
  type_chambre?: string;
}

export interface VerrouChambreParams {
  reservation_id: string;
  duree_minutes?: number;
}

export interface UpdateStatutParams {
  statut: Chambre['statut'];
  raison: string;
  utilisateur_id: string;
}

export interface ChambreResponse {
  success: boolean;
  data: {
    chambres: Chambre[];
    total: number;
  };
  message?: string;
}

export class ChambreService {
  private checkAuth() {
    if (!authService.isAuthenticated()) {
      throw new Error('Non authentifié');
    }
  }

  private getComplexeId(): number {
    const user = authService.getCurrentUser();
    if (!user) throw new Error('Utilisateur non authentifié');

    // Si l'utilisateur est un admin de chaîne, on utilise le complexe_id sélectionné
    if (user.role === 'admin_chaine') {
      const selectedComplexeId = localStorage.getItem('selectedComplexeId');
      if (!selectedComplexeId) {
        throw new Error('Aucun complexe sélectionné');
      }
      return parseInt(selectedComplexeId, 10);
    }

    // Pour les autres rôles, on utilise le complexe_id de l'utilisateur
    if (!user.complexe_id) {
      throw new Error('Complexe ID non disponible');
    }
    return user.complexe_id;
  }

  // Récupération de la liste des chambres
  async getChambres(params: ChambreParams): Promise<ChambreResponse> {
    this.checkAuth();
    const complexeId = this.getComplexeId();
    
    const response = await api.get<ChambreResponse>('/chambres', { 
      params: { ...params, complexe_id: complexeId }
    });
    return response.data;
  }

  // Récupération d'une chambre
  async getChambre(id: string): Promise<Chambre> {
    this.checkAuth();
    const response = await api.get<Chambre>(`/chambres/${id}`);
    return response.data;
  }

  // Récupération des statistiques d'une chambre
  async getStatistiquesChambre(id: string): Promise<ChambreStatistiques> {
    this.checkAuth();
    const response = await api.get<{ success: boolean; data: ChambreStatistiques }>(`/chambres/${id}/statistiques`);
    return response.data.data;
  }

  // Création d'une chambre
  async createChambre(data: Omit<Chambre, 'chambre_id'>): Promise<Chambre> {
    this.checkAuth();
    const complexeId = this.getComplexeId();
    
    const response = await api.post<Chambre>('/chambres', {
      ...data,
      complexe_id: complexeId
    });
    return response.data;
  }

  // Mise à jour d'une chambre
  async updateChambre(id: string, data: Partial<Chambre>): Promise<Chambre> {
    this.checkAuth();
    const response = await api.put<Chambre>(`/chambres/${id}`, data);
    return response.data;
  }

  // Suppression d'une chambre
  async deleteChambre(id: string): Promise<void> {
    this.checkAuth();
    await api.delete(`/chambres/${id}`);
  }

  // Méthodes commentées car les routes correspondantes n'existent pas dans le backend actuel
  /*
  // Récupération des disponibilités
  async getDisponibilites(params: DisponibiliteParams): Promise<DisponibilitesResponse> {
    this.checkAuth();
    const complexeId = this.getComplexeId();

    const response = await api.get<DisponibilitesResponse>('/chambres/disponibilites', {
      params: { ...params, complexe_id: complexeId }
    });
    return response.data;
  }

  // Verrouillage d'une chambre
  async verrouillerChambre(chambreId: string, params: VerrouChambreParams): Promise<any> {
    this.checkAuth();
    const response = await api.post(`/chambres/${chambreId}/verrouiller`, params);
    return response.data;
  }

  // Libération d'une chambre
  async libererChambre(chambreId: string): Promise<{ message: string; chambre_id: string }> {
    this.checkAuth();
    const response = await api.post(`/chambres/${chambreId}/liberer`);
    return response.data;
  }
  */

  // Mise à jour du statut d'une chambre
  async updateStatutChambre(chambreId: string, params: UpdateStatutParams): Promise<Chambre> {
    this.checkAuth();
    const response = await api.put(`/chambres/${chambreId}/statut`, params);
    return response.data;
  }

  // Méthode commentée car la route correspondante n'existe pas dans le backend actuel
  /*
  // Nettoyage des verrous expirés
  async nettoyerVerrousExpires(): Promise<{ message: string; chambres_liberees: number }> {
    this.checkAuth();
    const response = await api.post('/chambres/nettoyer-verrous');
    return response.data;
  }
  */

  // Récupération du calendrier d'une chambre
  async getCalendrierChambre(chambreId: string, params: { date_debut: string; date_fin: string }): Promise<any> {
    this.checkAuth();
    const response = await api.get(`/chambres/${chambreId}/calendrier`, { params });
    return response.data;
  }

  // Récupération de l'historique d'une chambre
  async getHistoriqueChambre(chambreId: string): Promise<any> {
    this.checkAuth();
    const response = await api.get(`/chambres/${chambreId}/historique`);
    return response.data;
  }
}

export const chambreService = new ChambreService(); 