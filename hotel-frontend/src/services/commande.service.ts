import api from './api.config';
import { authService } from './auth.service';
import type { PaiementRequest } from './paiement.service';

// Types pour les commandes
export interface CommandeItem {
  commande_item_id?: number;
  produit_id: number;
  nom: string;
  quantite: number;
  prix_unitaire: number;
  montant_ligne: number;
  commentaires?: string;
  statut: 'En attente' | 'En préparation' | 'Prêt' | 'Servi';
  temps_preparation?: number;
}

export interface Commande {
  commande_id: number;
  service_id: number;
  table_id?: number;
  employe_id: number;
  session_id?: number;
  date_commande: string;
  statut: 'En cours' | 'Servie' | 'Payée' | 'Annulée';
  montant_total: number;
  montant_tva?: number;
  items: CommandeItem[];
  notes?: string;
  type_commande: 'Sur place' | 'À emporter' | 'Livraison';
  numero_commande?: string;
  // Champs joints
  table_numero?: string;
  employe_nom?: string;
  employe_prenom?: string;
  service_nom?: string;
}

export interface CreateCommandeParams {
  service_id: number;
  table_id?: number;
  items: Omit<CommandeItem, 'commande_item_id' | 'statut'>[];
  notes?: string;
  type_commande: 'Sur place' | 'À emporter' | 'Livraison';
}

export interface UpdateCommandeParams {
  statut?: 'En cours' | 'Servie' | 'Payée' | 'Annulée';
  notes?: string;
}

export interface AddItemParams {
  produit_id: number;
  quantite: number;
  commentaires?: string;
}

export interface UpdateItemParams {
  quantite?: number;
  commentaires?: string;
  statut?: 'En attente' | 'En préparation' | 'Prêt' | 'Servi';
}

export interface PaymentData {
  montant: number;
  mode_paiement: 'CARTE' | 'ESPECES' | 'CHEQUE' | 'VIREMENT';
  montant_recu?: number;
  monnaie_rendue?: number;
  reference?: string;
  notes?: string;
}

export interface CommandeResponse {
  success: boolean;
  data: Commande | Commande[];
  message?: string;
}

export interface CommandeFilters {
  statut?: string;
  table_id?: number;
  date_debut?: string;
  date_fin?: string;
  type_commande?: string;
  employe_id?: number;
}

export interface CommandeStats {
  total_commandes: number;
  montant_total: number;
  commandes_en_cours: number;
  commandes_servies: number;
  commandes_payees: number;
  temps_moyen_preparation: number;
}

class CommandeService {
  private checkAuth() {
    if (!authService.isAuthenticated()) {
      throw new Error('Non authentifié');
    }
  }

  private getCurrentEmployeeId(): number {
    const user = authService.getCurrentUser();
    if (!user || !user.employe_id) {
      throw new Error('ID employé non disponible');
    }
    return user.employe_id;
  }

  private getComplexeId(): number {
    const user = authService.getCurrentUser();
    if (!user) throw new Error('Utilisateur non authentifié');

    if (user.role === 'admin_chaine') {
      const selectedComplexeId = localStorage.getItem('selectedComplexeId');
      if (!selectedComplexeId) {
        throw new Error('Aucun complexe sélectionné');
      }
      return parseInt(selectedComplexeId, 10);
    }

    if (!user.complexe_id) {
      throw new Error('Complexe ID non disponible');
    }
    return user.complexe_id;
  }

  // Création d'une nouvelle commande
  async createCommande(data: CreateCommandeParams): Promise<Commande> {
    this.checkAuth();
    const employeId = this.getCurrentEmployeeId();
    
    const commandeData = {
      ...data,
      employe_id: employeId
    };
    
    const response = await api.post<CommandeResponse>('/commandes', commandeData);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la création de la commande');
  }

  // Récupération d'une commande par ID
  async getCommandeById(commandeId: number): Promise<Commande> {
    this.checkAuth();
    
    const response = await api.get<CommandeResponse>(`/commandes/${commandeId}`);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Commande non trouvée');
  }

  // Récupération des commandes par service
  async getCommandesByService(serviceId: number, filters?: CommandeFilters): Promise<Commande[]> {
    this.checkAuth();
    
    const params: any = { ...filters };
    
    const response = await api.get<CommandeResponse>(`/commandes/service/${serviceId}`, { params });
    
    if (response.data.success && Array.isArray(response.data.data)) {
      return response.data.data;
    }
    return [];
  }

  // Récupération des commandes actives (en cours)
  async getCommandesActives(serviceId: number): Promise<Commande[]> {
    return this.getCommandesByService(serviceId, { statut: 'En cours' });
  }

  // Récupération de la commande active d'une table
  async getCommandeActiveByTable(tableId: number): Promise<Commande | null> {
    this.checkAuth();
    
    const response = await api.get<CommandeResponse>(`/commandes/table/${tableId}/active`);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    return null;
  }

  // Mise à jour du statut d'une commande
  async updateCommandeStatus(commandeId: number, statut: string): Promise<void> {
    this.checkAuth();
    
    const response = await api.put<CommandeResponse>(`/commandes/${commandeId}/statut`, { statut });
    
    if (!response.data.success) {
      throw new Error('Erreur lors de la mise à jour du statut de la commande');
    }
  }

  // Mise à jour d'une commande
  async updateCommande(commandeId: number, data: UpdateCommandeParams): Promise<Commande> {
    this.checkAuth();
    
    const response = await api.put<CommandeResponse>(`/commandes/${commandeId}`, data);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la mise à jour de la commande');
  }

  // Ajout d'un item à une commande
  async addItemToCommande(commandeId: number, item: AddItemParams): Promise<void> {
    this.checkAuth();
    
    const response = await api.post<CommandeResponse>(`/commandes/${commandeId}/items`, item);
    
    if (!response.data.success) {
      throw new Error('Erreur lors de l\'ajout de l\'item à la commande');
    }
  }

  // Mise à jour d'un item de commande
  async updateCommandeItem(commandeId: number, itemId: number, data: UpdateItemParams): Promise<void> {
    this.checkAuth();
    
    const response = await api.put<CommandeResponse>(`/commandes/${commandeId}/items/${itemId}`, data);
    
    if (!response.data.success) {
      throw new Error('Erreur lors de la mise à jour de l\'item');
    }
  }

  // Suppression d'un item de commande
  async removeItemFromCommande(commandeId: number, itemId: number): Promise<void> {
    this.checkAuth();
    
    const response = await api.delete<CommandeResponse>(`/commandes/${commandeId}/items/${itemId}`);
    
    if (!response.data.success) {
      throw new Error('Erreur lors de la suppression de l\'item');
    }
  }

  // Traitement du paiement d'une commande
  async processPayment(commandeId: number, paymentData: PaymentData): Promise<void> {
    this.checkAuth();
    
    const response = await api.post<CommandeResponse>(`/commandes/${commandeId}/payment`, paymentData);
    
    if (!response.data.success) {
      throw new Error('Erreur lors du traitement du paiement');
    }
  }

  // Annulation d'une commande
  async cancelCommande(commandeId: number, raison?: string): Promise<void> {
    this.checkAuth();
    
    const response = await api.put<CommandeResponse>(`/commandes/${commandeId}/cancel`, { raison });
    
    if (!response.data.success) {
      throw new Error('Erreur lors de l\'annulation de la commande');
    }
  }

  // Validation d'une commande (envoi en cuisine)
  async validateCommande(commandeId: number): Promise<void> {
    await this.updateCommandeStatus(commandeId, 'En cours');
  }

  // Marquer une commande comme servie
  async marquerServie(commandeId: number): Promise<void> {
    await this.updateCommandeStatus(commandeId, 'Servie');
  }

  // Marquer une commande comme payée
  async marquerPayee(commandeId: number): Promise<void> {
    await this.updateCommandeStatus(commandeId, 'Payée');
  }

  // Récupération des statistiques de commandes
  async getCommandeStats(serviceId: number, dateDebut?: string, dateFin?: string): Promise<CommandeStats> {
    this.checkAuth();
    
    const params: any = {};
    if (dateDebut) params.dateDebut = dateDebut;
    if (dateFin) params.dateFin = dateFin;
    
    const response = await api.get<{ success: boolean; data: CommandeStats }>(`/commandes/service/${serviceId}/stats`, { params });
    
    if (response.data.success) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la récupération des statistiques');
  }

  // Duplication d'une commande (pour répéter une commande)
  async duplicateCommande(commandeId: number, tableId?: number): Promise<Commande> {
    this.checkAuth();
    
    const response = await api.post<CommandeResponse>(`/commandes/${commandeId}/duplicate`, { table_id: tableId });
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la duplication de la commande');
  }

  // Impression d'une commande (ticket de cuisine)
  async printCommande(commandeId: number): Promise<void> {
    this.checkAuth();
    
    const response = await api.post<CommandeResponse>(`/commandes/${commandeId}/print`);
    
    if (!response.data.success) {
      throw new Error('Erreur lors de l\'impression de la commande');
    }
  }
}

export const commandeService = new CommandeService();
