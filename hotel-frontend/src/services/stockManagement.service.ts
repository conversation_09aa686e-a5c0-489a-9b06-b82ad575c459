import api from './api.config';
import { authService } from './auth.service';

// Types pour la gestion des stocks
export interface MouvementStock {
  mouvement_id: number;
  ingredient_id: number;
  complexe_id: number;
  type_mouvement: 'ENTREE' | 'SORTIE' | 'AJUSTEMENT' | 'CONSOMMATION' | 'PERTE';
  quantite: number;
  quantite_avant: number;
  quantite_apres: number;
  prix_unitaire: number;
  valeur_totale: number;
  reference_id?: number;
  reference_type?: string;
  employe_id?: number;
  notes?: string;
  date_mouvement: string;
  created_at: string;
}

export interface AlerteStock {
  alerte_id: number;
  ingredient_id: number;
  nom_ingredient: string;
  categorie: string;
  complexe_id: number;
  nom_complexe: string;
  type_alerte: 'STOCK_FAIBLE' | 'RUPTURE' | 'PEREMPTION';
  niveau_urgence: 'FAIBLE' | 'MOYEN' | 'ELEVE' | 'CRITIQUE';
  message: string;
  stock_actuel: number;
  seuil_declenche: number;
  date_alerte: string;
  statut: 'ACTIVE' | 'RESOLUE' | 'IGNOREE';
  heures_depuis_alerte: number;
}

export interface StockDetaille {
  ingredient_id: number;
  nom_ingredient: string;
  description: string;
  categorie: string;
  unite_mesure: string;
  prix_unitaire_moyen: number;
  complexe_id: number;
  stock_initial: number;
  stock_actuel: number;
  stock_minimal: number;
  stock_maximal: number;
  conservation: string;
  duree_conservation_jours: number;
  valeur_stock_actuel: number;
  valeur_stock_initial: number;
  statut_stock: 'NORMAL' | 'FAIBLE' | 'CRITIQUE' | 'RUPTURE' | 'EXCESSIF';
  pourcentage_stock: number;
  jours_stock_restant: number;
  derniere_mise_a_jour: string;
}

export interface ConsommationIngredient {
  ingredient_id: number;
  ingredient_nom: string;
  categorie: string;
  unite_mesure: string;
  nombre_utilisations: number;
  quantite_totale_theorique: number;
  quantite_totale_reelle: number;
  quantite_moyenne_theorique: number;
  cout_total_theorique: number;
  cout_total_reel: number;
}

export interface DashboardStock {
  statistiques: {
    total_ingredients: number;
    valeur_totale: number;
    ruptures: number;
    critiques: number;
    faibles: number;
    normaux: number;
    excessifs: number;
  };
  alertes: {
    total_alertes: number;
    critiques: number;
    elevees: number;
    moyennes: number;
  };
  mouvements_recents: MouvementStock[];
  derniere_mise_a_jour: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

/**
 * Service pour la gestion avancée des stocks d'ingrédients
 */
class StockManagementService {
  private baseURL = 'stock-management';

  /**
   * Vérification de l'authentification
   */
  private checkAuth(): void {
    if (!authService.isAuthenticated()) {
      throw new Error('Utilisateur non authentifié');
    }
  }

  /**
   * ==================== GESTION DES MOUVEMENTS ====================
   */

  /**
   * Enregistrer un mouvement de stock manuel
   */
  async enregistrerMouvement(
    ingredientId: number,
    complexeId: number,
    typeMouvement: 'ENTREE' | 'SORTIE' | 'AJUSTEMENT' | 'PERTE',
    quantite: number,
    options?: {
      prixUnitaire?: number;
      notes?: string;
    }
  ): Promise<MouvementStock> {
    this.checkAuth();

    try {
      const response = await api.post<ApiResponse<MouvementStock>>(
        `${this.baseURL}/mouvement`,
        {
          ingredient_id: ingredientId,
          complexe_id: complexeId,
          type_mouvement: typeMouvement,
          quantite,
          prix_unitaire: options?.prixUnitaire,
          notes: options?.notes
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de l\'enregistrement du mouvement');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de l\'enregistrement du mouvement'
      );
    }
  }

  /**
   * Initialiser le stock d'un ingrédient
   */
  async initialiserStock(
    ingredientId: number,
    complexeId: number,
    stockInitial: number
  ): Promise<MouvementStock> {
    this.checkAuth();

    try {
      const response = await api.post<ApiResponse<MouvementStock>>(
        `${this.baseURL}/initialiser`,
        {
          ingredient_id: ingredientId,
          complexe_id: complexeId,
          stock_initial: stockInitial
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de l\'initialisation du stock');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de l\'initialisation du stock'
      );
    }
  }

  /**
   * Consommer les ingrédients d'un produit
   */
  async consommerProduit(
    produitId: number,
    commandeId: number,
    quantitePortions: number = 1
  ): Promise<any> {
    this.checkAuth();

    try {
      const response = await api.post<ApiResponse<any>>(
        `${this.baseURL}/consommer-produit`,
        {
          produit_id: produitId,
          commande_id: commandeId,
          quantite_portions: quantitePortions
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la consommation de produit');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message ||
        error.message ||
        'Erreur lors de la consommation de produit'
      );
    }
  }

  /**
   * ==================== CONSULTATION DES DONNÉES ====================
   */

  /**
   * Récupérer les alertes de stock
   */
  async getAlertes(
    complexeId: number,
    niveauUrgence?: 'FAIBLE' | 'MOYEN' | 'ELEVE' | 'CRITIQUE'
  ): Promise<AlerteStock[]> {
    this.checkAuth();

    try {
      const params = new URLSearchParams();
      if (niveauUrgence) {
        params.append('niveau_urgence', niveauUrgence);
      }

      const response = await api.get<ApiResponse<AlerteStock[]>>(
        `${this.baseURL}/alertes/${complexeId}?${params.toString()}`
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la récupération des alertes');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de la récupération des alertes'
      );
    }
  }

  /**
   * Récupérer le stock détaillé
   */
  async getStockDetaille(
    complexeId: number,
    filters?: {
      categorie?: string;
      statutStock?: 'NORMAL' | 'FAIBLE' | 'CRITIQUE' | 'RUPTURE' | 'EXCESSIF';
    }
  ): Promise<{ ingredients: StockDetaille[]; statistiques: any }> {
    this.checkAuth();

    try {
      const params = new URLSearchParams();
      if (filters?.categorie) {
        params.append('categorie', filters.categorie);
      }
      if (filters?.statutStock) {
        params.append('statut_stock', filters.statutStock);
      }

      const response = await api.get<ApiResponse<{ ingredients: StockDetaille[]; statistiques: any }>>(
        `${this.baseURL}/stock-detaille/${complexeId}?${params.toString()}`
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la récupération du stock détaillé');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de la récupération du stock détaillé'
      );
    }
  }

  /**
   * Récupérer le rapport de consommation
   */
  async getRapportConsommation(
    complexeId: number,
    dateDebut: string,
    dateFin: string
  ): Promise<ConsommationIngredient[]> {
    this.checkAuth();

    try {
      const response = await api.get<ApiResponse<ConsommationIngredient[]>>(
        `${this.baseURL}/rapport-consommation/${complexeId}?date_debut=${dateDebut}&date_fin=${dateFin}`
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la génération du rapport');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de la génération du rapport'
      );
    }
  }

  /**
   * Récupérer le dashboard complet
   */
  async getDashboard(complexeId: number): Promise<DashboardStock> {
    this.checkAuth();

    try {
      const response = await api.get<ApiResponse<DashboardStock>>(
        `${this.baseURL}/dashboard/${complexeId}`
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la récupération du dashboard');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de la récupération du dashboard'
      );
    }
  }

  /**
   * ==================== ADMINISTRATION ====================
   */

  /**
   * Résoudre une alerte
   */
  async resoudreAlerte(alerteId: number, notesResolution?: string): Promise<void> {
    this.checkAuth();

    try {
      const response = await api.put<ApiResponse<void>>(
        `${this.baseURL}/alerte/${alerteId}/resoudre`,
        {
          notes_resolution: notesResolution
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la résolution de l\'alerte');
      }
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de la résolution de l\'alerte'
      );
    }
  }
}

export const stockManagementService = new StockManagementService();
export default stockManagementService;
