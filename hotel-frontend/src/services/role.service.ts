import api from './api.config';
import { authService } from './auth.service';

export interface Role {
  role_id: number;
  nom: string;
  description: string;
  permissions: string[];
  complexe_id: number;
  created_at: string;
  updated_at: string;
}

export interface CreateRoleData {
  nom: string;
  description: string;
  permissions: string[];
  complexe_id?: number;
}

export interface UpdateRoleData {
  nom?: string;
  description?: string;
  permissions?: string[];
}

class RoleService {
  private static instance: RoleService;

  private constructor() {}

  public static getInstance(): RoleService {
    if (!RoleService.instance) {
      RoleService.instance = new RoleService();
    }
    return RoleService.instance;
  }

  private checkAuth() {
    if (!authService.isAuthenticated()) {
      throw new Error('Non authentifié');
    }
  }

  private getComplexeId(): number {
    const user = authService.getCurrentUser();
    if (!user) throw new Error('Utilisateur non authentifié');

    // Si l'utilisateur est un admin de chaîne, on utilise le complexe_id sélectionné
    if (user.role === 'admin_chaine') {
      const selectedComplexeId = localStorage.getItem('selectedComplexeId');
      if (selectedComplexeId) {
        return parseInt(selectedComplexeId, 10);
      }
      throw new Error('Aucun complexe sélectionné');
    }

    // Pour les autres types d'utilisateurs, on utilise leur complexe_id
    if (!user.complexe_id) {
      throw new Error('Complexe ID manquant');
    }
    return user.complexe_id;
  }

  public async getRoles(): Promise<Role[]> {
    try {
      this.checkAuth();

      // Déterminer si on doit inclure le complexe_id en query parameter
      const user = authService.getCurrentUser();
      let url = '/roles';

      // Pour les super_admin et admin_chaine, inclure le complexe_id en query
      if (user?.role === 'super_admin' || user?.role === 'admin_chaine') {
        const complexeId = this.getComplexeId();
        url = `/roles?complexe_id=${complexeId}`;
      }

      const response = await api.get<Role[]>(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching roles:', error);
      throw error;
    }
  }

  public async getRole(id: number): Promise<Role> {
    try {
      const response = await api.get<Role>(`/roles/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching role:', error);
      throw error;
    }
  }

  public async createRole(data: CreateRoleData): Promise<Role> {
    try {
      this.checkAuth();

      // Déterminer si on doit inclure le complexe_id
      const user = authService.getCurrentUser();
      let requestData = { ...data };

      // Pour les super_admin et admin_chaine, inclure le complexe_id
      if (user?.role === 'super_admin' || user?.role === 'admin_chaine') {
        const complexeId = this.getComplexeId();
        requestData = { ...data, complexe_id: complexeId };
      }

      const response = await api.post<{ success: boolean; data: Role }>('/roles', requestData);
      return response.data.data;
    } catch (error) {
      console.error('Error creating role:', error);
      throw error;
    }
  }

  public async updateRole(id: number, data: UpdateRoleData): Promise<Role> {
    try {
      const response = await api.put<Role>(`/roles/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating role:', error);
      throw error;
    }
  }

  public async deleteRole(id: number): Promise<void> {
    try {
      await api.delete(`/roles/${id}`);
    } catch (error) {
      console.error('Error deleting role:', error);
      throw error;
    }
  }

  public async assignRoleToEmployee(employeeId: number, roleId: number): Promise<void> {
    try {
      await api.post('/roles/assign', { employeeId, roleId });
    } catch (error) {
      console.error('Error assigning role to employee:', error);
      throw error;
    }
  }

  public async getEmployeePermissions(employeeId: number): Promise<string[]> {
    try {
      const response = await api.get<{ success: boolean; data: { permissions: string[] } }>(`/roles/employee/${employeeId}/permissions`);
      return response.data.data.permissions;
    } catch (error) {
      console.error('Error fetching employee permissions:', error);
      throw error;
    }
  }

  public async getAvailablePermissions(): Promise<string[]> {
    try {
      // Utiliser les permissions simplifiées du backend
      const response = await api.get<{ success: boolean; data: string[] }>('/permissions/list');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching available permissions:', error);
      throw error;
    }
  }

  public async getAllPermissions(): Promise<Record<string, string>> {
    try {
      // Récupérer toutes les permissions avec leurs descriptions
      const response = await api.get<{ success: boolean; data: Record<string, string> }>('/permissions/all');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching all permissions:', error);
      throw error;
    }
  }

  public async getPermissionsByCategory(): Promise<Record<string, Record<string, string>>> {
    try {
      // Utiliser les catégories simplifiées du système
      const allPermissions = await this.getAllPermissions();

      // Organiser les permissions par catégories selon le système simplifié
      const categories: Record<string, Record<string, string>> = {
        'Gestion des Employés': {},
        'Gestion des Clients': {},
        'Gestion des Réservations': {},
        'Gestion des Services': {},
        'Gestion Financière': {},
        'Rapports et Statistiques': {},
        'Configuration': {}
      };

      // Mapper les permissions aux catégories
      Object.entries(allPermissions).forEach(([permission, description]) => {
        if (permission.includes('employee') || permission.includes('role')) {
          categories['Gestion des Employés'][permission] = description;
        } else if (permission.includes('client')) {
          categories['Gestion des Clients'][permission] = description;
        } else if (permission.includes('reservation')) {
          categories['Gestion des Réservations'][permission] = description;
        } else if (permission.includes('service') || permission.includes('product') || permission.includes('inventory')) {
          categories['Gestion des Services'][permission] = description;
        } else if (permission.includes('payment') || permission.includes('transaction')) {
          categories['Gestion Financière'][permission] = description;
        } else if (permission.includes('report') || permission.includes('statistic')) {
          categories['Rapports et Statistiques'][permission] = description;
        } else {
          categories['Configuration'][permission] = description;
        }
      });

      // Supprimer les catégories vides
      Object.keys(categories).forEach(category => {
        if (Object.keys(categories[category]).length === 0) {
          delete categories[category];
        }
      });

      return categories;
    } catch (error) {
      console.error('Error fetching permissions by category:', error);
      throw error;
    }
  }

  public async getUserPermissions(): Promise<string[]> {
    try {
      const response = await api.get<{ success: boolean; data: { permissions: string[] } }>('/permissions/user');
      return response.data.data.permissions;
    } catch (error) {
      console.error('Error fetching user permissions:', error);
      throw error;
    }
  }

  public async checkUserPermission(permission: string): Promise<boolean> {
    try {
      const response = await api.get<{ success: boolean; data: { hasPermission: boolean } }>(`/permissions/check/${permission}`);
      return response.data.data.hasPermission;
    } catch (error) {
      console.error('Error checking user permission:', error);
      throw error;
    }
  }

  /**
   * Créer les rôles prédéfinis pour un complexe (système simplifié)
   */
  public async createPredefinedRoles(complexeId?: number): Promise<void> {
    try {
      this.checkAuth();

      const user = authService.getCurrentUser();
      let requestComplexeId = complexeId;

      // Pour les super_admin et admin_chaine, utiliser le complexe sélectionné
      if ((user?.role === 'super_admin' || user?.role === 'admin_chaine') && !complexeId) {
        requestComplexeId = this.getComplexeId();
      }

      await api.post('/roles/predefined/create', { complexe_id: requestComplexeId });
    } catch (error) {
      console.error('Error creating predefined roles:', error);
      throw error;
    }
  }

  /**
   * Obtenir le rôle prédéfini pour un type d'employé
   */
  public async getPredefinedRoleForType(employeeType: string): Promise<Role> {
    try {
      this.checkAuth();

      const response = await api.get<{ data: Role }>(`/roles/predefined/type/${employeeType}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching predefined role:', error);
      throw error;
    }
  }

  /**
   * Valider des permissions selon le système simplifié
   */
  public async validatePermissions(permissions: string[]): Promise<{ isValid: boolean; invalidPermissions: string[] }> {
    try {
      const response = await api.post<{ success: boolean; data: { isValid: boolean; invalidPermissions: string[] } }>('/roles/validate-permissions', {
        permissions
      });
      return response.data.data;
    } catch (error) {
      console.error('Error validating permissions:', error);
      throw error;
    }
  }

  /**
   * Obtenir les types d'employés disponibles avec leurs permissions par défaut
   */
  public getEmployeeTypes(): Record<string, { label: string; permissions: string[] }> {
    return {
      reception: {
        label: 'Employé Réception',
        permissions: ['reception_operations']
      },
      gerant_piscine: {
        label: 'Gérant Piscine',
        permissions: ['piscine_operations']
      },
      serveuse: {
        label: 'Serveuse',
        permissions: ['service_operations']
      },
      gerant_services: {
        label: 'Gérant Services',
        permissions: ['service_operations', 'management_operations']
      },
      cuisine: {
        label: 'Employé Cuisine',
        permissions: ['kitchen_operations']
      }
    };
  }
}

export const roleService = RoleService.getInstance(); 