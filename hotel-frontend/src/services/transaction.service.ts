import api from './api.config';

export interface TransactionRequest {
  sessionId: string;
  clientId: string;
  montant: number;
  type: 'debit' | 'credit';
  description?: string;
  reference?: string;
}

export interface TransactionResponse {
  transactionId: string;
  sessionId: string;
  clientId: string;
  montant: number;
  type: 'debit' | 'credit';
  statut: 'en_attente' | 'validee' | 'refusee' | 'annulee';
  description?: string;
  reference?: string; 
  dateCreation: string;
  dateModification?: string;
  solde: number;
}

export interface TransactionDetails extends TransactionResponse {
  session: {
    id: string;
    numero: string;
    dateDebut: string;
    dateFin?: string;
  };
  client: {
    id: string;
    nom: string;
    prenom: string;
    email: string;
  };
  historique: Array<{
    date: string;
    action: string;
    utilisateur: string;
    ancienStatut?: string;
    nouveauStatut?: string;
  }>;
}

export interface SoldeResponse {
  transactionId: string;
  soldeActuel: number;
  derniereMiseAJour: string;
}

class TransactionService {
  async createTransaction(data: TransactionRequest): Promise<TransactionResponse> {
    const response = await api.post<TransactionResponse>('/transactions', data);
    return response.data;
  }

  async getTransactionById(transactionId: string): Promise<TransactionDetails> {
    const response = await api.get<TransactionDetails>(`/transactions/${transactionId}`);
    return response.data;
  }

  async updateTransactionStatus(
    transactionId: string, 
    statut: TransactionResponse['statut']
  ): Promise<TransactionResponse> {
    const response = await api.patch<TransactionResponse>(
      `/transactions/${transactionId}/statut`, 
      { statut }
    );
    return response.data;
  }

  async getTransactionsBySession(sessionId: string): Promise<TransactionResponse[]> {
    const response = await api.get<TransactionResponse[]>(`/transactions/session/${sessionId}`);
    return response.data;
  }

  async getTransactionsByClient(clientId: string): Promise<TransactionResponse[]> {
    const response = await api.get<TransactionResponse[]>(`/transactions/client/${clientId}`);
    return response.data;
  }

  async verifierSoldeTransaction(transactionId: string): Promise<SoldeResponse> {
    const response = await api.get<SoldeResponse>(`/transactions/${transactionId}/solde`);
    return response.data;
  }
}

export const transactionService = new TransactionService();
