import api from './api.config';

export interface HistoriqueAction {
  historique_id: string;
  type_action: string;
  entite_id: string;
  entite_type: string;
  utilisateur_id: string;
  details: string;
  statut_avant?: string;
  statut_apres?: string;
  date_action: string;
}

export interface HistoriquePaiement {
  historique_id: string;
  paiement_id: string;
  montant: number;
  type_paiement: string;
  statut: string;
  details: string;
  date_paiement: string;
  reservation_id: string;
}

export interface HistoriqueTicket {
  historique_id: string;
  ticket_id: string;
  utilisateur_id: string;
  type_utilisation: string;
  details: string;
  date_utilisation: string;
  reservation_id: string;
}

export interface HistoriqueParams {
  date_debut?: string;
  date_fin?: string;
  type?: string;
  page?: number;
  limit?: number;
}

export interface StatistiquesActionsResponse {
  periode: {
    debut: string;
    fin: string;
  };
  totalActions: number;
  actionsParType: Record<string, number>;
  actionsParUtilisateur: Record<string, number>;
  tendances: Array<{
    date: string;
    nombreActions: number;
  }>;
}

export interface RapportParams extends HistoriqueParams {
  type: 'AUDIT' | 'ACTIVITE' | 'PERFORMANCE' | 'SECURITE';
  format?: 'PDF' | 'EXCEL' | 'JSON';
}

class TracabiliteService {
  // Historique client
  async getHistoriqueEntite(entiteId: string, params: HistoriqueParams): Promise<HistoriqueAction[]> {
    const response = await api.get<HistoriqueAction[]>(`/tracabilite/client/historique/${entiteId}`, { params });
    return response.data;
  }

  async getHistoriquePaiements(reservationId: string, params: HistoriqueParams): Promise<HistoriquePaiement[]> {
    const response = await api.get<HistoriquePaiement[]>(`/tracabilite/client/paiements/${reservationId}`, { params });
    return response.data;
  }

  async getHistoriqueTickets(reservationId: string, params: HistoriqueParams): Promise<HistoriqueTicket[]> {
    const response = await api.get<HistoriqueTicket[]>(`/tracabilite/client/tickets/${reservationId}`, { params });
    return response.data;
  }

  // Historique réception
  async getHistoriqueActions(entiteId: string, params: HistoriqueParams): Promise<HistoriqueAction[]> {
    const response = await api.get<HistoriqueAction[]>(`/tracabilite/reception/actions/${entiteId}`, { params });
    return response.data;
  }

  async getHistoriqueModifications(reservationId: string, params: HistoriqueParams): Promise<HistoriqueAction[]> {
    const response = await api.get<HistoriqueAction[]>(`/tracabilite/reception/modifications/${reservationId}`, { params });
    return response.data;
  }

  async getHistoriqueUtilisations(ticketId: string, params: HistoriqueParams): Promise<HistoriqueTicket[]> {
    const response = await api.get<HistoriqueTicket[]>(`/tracabilite/reception/utilisations/${ticketId}`, { params });
    return response.data;
  }

  // Historique admin
  async getAuditLog(params: HistoriqueParams): Promise<HistoriqueAction[]> {
    const response = await api.get<HistoriqueAction[]>('/tracabilite/admin/audit', { params });
    return response.data;
  }

  async getStatistiquesActions(params: HistoriqueParams): Promise<StatistiquesActionsResponse> {
    const response = await api.get<StatistiquesActionsResponse>('/tracabilite/admin/statistiques', { params });
    return response.data;
  }

  async genererRapports(params: RapportParams): Promise<any> {
    const response = await api.get('/tracabilite/admin/rapports', { params });
    return response.data;
  }

  // Méthodes utilitaires
  async getHistoriqueComplet(entiteId: string, entiteType: 'RESERVATION' | 'PAIEMENT' | 'TICKET' | 'CHAMBRE' | 'CLIENT'): Promise<{
    actions: HistoriqueAction[];
    paiements?: HistoriquePaiement[];
    tickets?: HistoriqueTicket[];
  }> {
    const actions = await this.getHistoriqueEntite(entiteId, {});

    const result: any = { actions };

    if (entiteType === 'RESERVATION') {
      try {
        result.paiements = await this.getHistoriquePaiements(entiteId, {});
        result.tickets = await this.getHistoriqueTickets(entiteId, {});
      } catch (error) {
        // Ignorer les erreurs si les données n'existent pas
      }
    }

    return result;
  }

  async getStatistiquesPeriode(debut: string, fin: string): Promise<StatistiquesActionsResponse> {
    return this.getStatistiquesActions({
      date_debut: debut,
      date_fin: fin
    });
  }

  async genererRapportAudit(debut: string, fin: string, format: 'PDF' | 'EXCEL' | 'JSON' = 'JSON'): Promise<any> {
    return this.genererRapports({
      type: 'AUDIT',
      date_debut: debut,
      date_fin: fin,
      format
    });
  }

  async genererRapportActivite(debut: string, fin: string, format: 'PDF' | 'EXCEL' | 'JSON' = 'JSON'): Promise<any> {
    return this.genererRapports({
      type: 'ACTIVITE',
      date_debut: debut,
      date_fin: fin,
      format
    });
  }
}

export const tracabiliteService = new TracabiliteService(); 