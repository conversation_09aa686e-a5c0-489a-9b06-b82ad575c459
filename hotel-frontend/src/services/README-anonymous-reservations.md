# Service de Réservations Anonymes - Frontend

## Vue d'ensemble

Le service `anonymousReservation.service.ts` permet aux clients de c<PERSON><PERSON>, consulter, modifier et annuler des réservations sans fournir d'informations personnelles complètes. Ce service fait partie de la Phase 5 du plan d'implémentation des réservations anonymes.

## Fonctionnalités

### Principales méthodes

#### `createDemandeReservationAnonyme(params)`
Crée une nouvelle demande de réservation anonyme.

**Paramètres :**
```typescript
{
  complexe_id: number;
  date_arrivee: string;
  date_depart: string;
  heure_debut: string;
  heure_fin: string;
  chambres: Array<{
    chambre_id: string;
    type_chambre: string;
    prix_nuit: number;
  }>;
  prix_total: number;
  pseudonyme?: string;
  commentaires?: string;
}
```

**Retour :** `ApiResponse<AnonymousReservationResponse>`

#### `getReservationAnonyme(codeAcces)`
Consulte une réservation anonyme par son code d'accès.

**Paramètres :** `codeAcces: string` (format: ANON-XXXXXXXXXXXX)

**Retour :** `ApiResponse<AnonymousReservationDetails>`

#### `updateReservationAnonyme(codeAcces, params)`
Modifie une réservation anonyme (fonctionnalités limitées).

**Paramètres :**
- `codeAcces: string`
- `params: { commentaires?: string }`

**Retour :** `ApiResponse<AnonymousReservation>`

#### `cancelReservationAnonyme(codeAcces)`
Annule une réservation anonyme.

**Paramètres :** `codeAcces: string`

**Retour :** `ApiResponse<void>`

#### `validateAccessCode(codeAcces)`
Valide un code d'accès (utilitaire pour le frontend).

**Paramètres :** `codeAcces: string`

**Retour :** `AccessCodeValidation`

### Méthodes utilitaires

#### Méthodes statiques
- `formatAccessCodeForDisplay(codeAcces)` - Formate un code pour l'affichage
- `generateDefaultPseudonym()` - Génère un pseudonyme par défaut
- `calculateStayDuration(dateArrivee, dateDepart)` - Calcule la durée du séjour
- `canModifyReservation(reservation)` - Vérifie si une réservation peut être modifiée
- `canCancelReservation(reservation)` - Vérifie si une réservation peut être annulée
- `getStatusLabel(statut)` - Obtient le libellé d'un statut
- `getStatusColor(statut)` - Obtient la couleur CSS d'un statut

## Validation côté client

Le service inclut une validation complète côté client :

- **Dates :** Validation du format et cohérence des dates
- **Heures :** Format HH:MM
- **Code d'accès :** Format ANON-XXXXXXXXXXXX
- **Chambres :** Au moins une chambre sélectionnée
- **Prix :** Montant positif
- **Pseudonyme :** 2-50 caractères (optionnel)
- **Commentaires :** Maximum 500 caractères (optionnel)

## Gestion des erreurs

Le service gère différents types d'erreurs :

- **400 :** Données invalides
- **403 :** Service non disponible ou action non autorisée
- **404 :** Réservation non trouvée
- **410 :** Réservation expirée
- **500 :** Erreur serveur
- **Réseau :** Timeout ou connexion impossible

## Sécurité

- Validation stricte des formats
- Timeout configuré pour chaque requête
- Masquage des informations sensibles
- Rate limiting géré côté serveur
- Logging des accès

## Utilisation

### Import
```typescript
import { anonymousReservationService } from '@/services/anonymousReservation.service';
// ou
import { anonymousReservationService } from '@/services';
```

### Exemple d'utilisation
```typescript
// Créer une réservation anonyme
try {
  const result = await anonymousReservationService.createDemandeReservationAnonyme({
    complexe_id: 1,
    date_arrivee: '2024-01-15',
    date_depart: '2024-01-16',
    heure_debut: '14:00',
    heure_fin: '11:00',
    chambres: [
      {
        chambre_id: '1',
        type_chambre: 'Standard',
        prix_nuit: 100
      }
    ],
    prix_total: 100,
    pseudonyme: 'Client123'
  });
  
  console.log('Code d\'accès:', result.data.code_acces_anonyme);
} catch (error) {
  console.error('Erreur:', error.message);
}

// Consulter une réservation
try {
  const reservation = await anonymousReservationService.getReservationAnonyme('ANON-123456789012');
  console.log('Réservation:', reservation.data);
} catch (error) {
  console.error('Réservation non trouvée');
}
```

## Intégration avec le service de réservation existant

Le service de réservation existant a été étendu pour supporter les réservations anonymes :

- Nouvelles méthodes pour détecter les réservations anonymes
- Gestion de l'affichage des noms (pseudonyme vs nom réel)
- Protection des informations de contact pour les réservations anonymes
- Règles de modification adaptées

## Tests

Des tests unitaires sont disponibles dans `__tests__/anonymousReservation.service.test.ts` couvrant :

- Création de réservations
- Validation des paramètres
- Validation des codes d'accès
- Méthodes utilitaires
- Gestion des erreurs

## Prochaines étapes

Ce service sera utilisé dans la Phase 6 pour créer les composants React d'interface utilisateur pour les réservations anonymes.
