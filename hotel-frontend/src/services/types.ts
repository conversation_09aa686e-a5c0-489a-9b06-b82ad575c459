export type ServiceType = 'Restaurant' | 'Bar' | 'RoomService' | 'Spa' | 'Boutique';

export interface MenuItem {
  produit_id: number;
  categorie_id: number;
  nom: string;
  description?: string;
  prix_vente: number;
  unite_mesure: string;
  stock_disponible: number;
  stock_alerte: number;
  actif: boolean;
  created_at: string;
  updated_at?: string;
}

export interface MenuCategory {
  categorie_id: number;
  service_id: number;
  nom: string;
  description?: string;
  ordre: number;
  actif: boolean;
  created_at: string;
  updated_at?: string;
}

export interface ServiceComplexe {
  service_id: number;
  complexe_id: number;
  type_service: ServiceType;
  nom: string;
  description?: string;
  emplacement?: string;
  horaires_ouverture?: Record<string, any>;
  capacite?: number;
  image_url?: string;
  contact_email?: string;
  contact_telephone?: string;
  actif: boolean;
  configuration?: Record<string, any>;
  tarification?: Record<string, any>;
  created_at: string;
  updated_at?: string;
}

export interface SessionCaisse {
  session_id: number;
  pos_id: number;
  service_id: number;
  employe_id: number;
  complexe_id: number;
  date_ouverture: string;
  date_fermeture?: string;
  fonds_ouverture: number;
  fonds_fermeture?: number;
  statut: 'Ouverte' | 'Fermée' | 'Vérifiée';
  notes?: string;
}

export interface CommandeItem {
  commande_item_id?: number;
  produit_id: number;
  nom: string;
  quantite: number;
  prix_unitaire: number;
  montant_ligne: number;
  commentaires?: string;
  statut: 'En attente' | 'En préparation' | 'Prêt' | 'Servi';
  temps_preparation?: number;
}

export interface Commande {
  commande_id: number;
  service_id: number;
  table_id?: number;
  employe_id: number;
  session_id?: number;
  date_commande: string;
  statut: 'En cours' | 'Servie' | 'Payée' | 'Annulée';
  montant_total: number;
  montant_tva?: number;
  items: CommandeItem[];
  notes?: string;
  type_commande: 'Sur place' | 'À emporter' | 'Livraison';
  numero_commande?: string;
  table_numero?: string;
  employe_nom?: string;
  employe_prenom?: string;
  service_nom?: string;
}

export interface Table {
  table_id: number;
  service_id: number;
  numero: string;
  capacite: number;
  zone: string;
  statut: 'Libre' | 'Occupée' | 'Réservée' | 'Maintenance';
  position_x?: number;
  position_y?: number;
  commande_active?: Commande;
  created_at: string;
  updated_at?: string;
} 