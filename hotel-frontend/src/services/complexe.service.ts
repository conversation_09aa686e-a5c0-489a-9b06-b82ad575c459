import api from './api.config';

export interface Complexe {
  complexe_id: number;
  chaine_id: number;
  nom: string;
  ville: string;
  pays: string;
  adresse?: string;
  type_etablissement?: string;
  nombre_employes?: number;
  created_at: string;
  updated_at?: string;
}

export interface CreateComplexeParams {
  chaine_id: number;
  nom: string;
  ville: string;
  pays: string;
  adresse?: string;
  type_etablissement?: string;
  nombre_employes?: number;
}

export interface UpdateComplexeParams {
  nom?: string;
  ville?: string;
  pays?: string;
  adresse?: string;
  type_etablissement?: string;
  nombre_employes?: number;
}

export interface ComplexeDetails extends Complexe {
  nombre_chambres: number;
  nombre_reservations: number;
  taux_occupation_moyen: number;
  chiffre_affaires_mensuel: number;
}

class ComplexeService {
  // Récupération de tous les complexes
  async getComplexes(): Promise<Complexe[]> {
    try {
      const response = await api.get<Complexe[]>('/complexes');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des complexes:', error);
      throw error;
    }
  }

  // Récupération d'un complexe par ID
  async getComplexeById(id: number): Promise<Complexe> {
    try {
      const response = await api.get<Complexe>(`/complexes/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la récupération du complexe ${id}:`, error);
      throw error;
    }
  }

  // Création d'un nouveau complexe
  async createComplexe(params: CreateComplexeParams): Promise<Complexe> {
    try {
      const response = await api.post<Complexe>('/complexes', params);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la création du complexe:', error);
      throw error;
    }
  }

  // Mise à jour d'un complexe
  async updateComplexe(id: number, params: UpdateComplexeParams): Promise<Complexe> {
    try {
      const response = await api.put<Complexe>(`/complexes/${id}`, params);
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la mise à jour du complexe ${id}:`, error);
      throw error;
    }
  }

  // Suppression d'un complexe
  async deleteComplexe(id: number): Promise<void> {
    try {
      await api.delete(`/complexes/${id}`);
    } catch (error) {
      console.error(`Erreur lors de la suppression du complexe ${id}:`, error);
      throw error;
    }
  }

  // Méthodes utilitaires
  async getComplexesByChaine(chaineId: number): Promise<Complexe[]> {
    try {
      const complexes = await this.getComplexes();
      return complexes.filter(complexe => complexe.chaine_id === chaineId);
    } catch (error) {
      console.error(`Erreur lors de la récupération des complexes de la chaîne ${chaineId}:`, error);
      throw error;
    }
  }

  async getComplexesByVille(ville: string): Promise<Complexe[]> {
    try {
      const complexes = await this.getComplexes();
      return complexes.filter(complexe =>
        complexe.ville.toLowerCase().includes(ville.toLowerCase())
      );
    } catch (error) {
      console.error(`Erreur lors de la recherche de complexes dans ${ville}:`, error);
      throw error;
    }
  }

  async getComplexesByPays(pays: string): Promise<Complexe[]> {
    try {
      const complexes = await this.getComplexes();
      return complexes.filter(complexe =>
        complexe.pays.toLowerCase().includes(pays.toLowerCase())
      );
    } catch (error) {
      console.error(`Erreur lors de la recherche de complexes en ${pays}:`, error);
      throw error;
    }
  }
}

export const complexeService = new ComplexeService();