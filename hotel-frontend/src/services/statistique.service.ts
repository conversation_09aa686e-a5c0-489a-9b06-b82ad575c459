import api from './api.config';

export interface TauxOccupation {
  date: string;
  type_chambre: string;
  total_chambres: number;
  chambres_occupees: number;
  taux_occupation: number;
}

export interface TauxOccupationMoyen {
  type_chambre: string;
  total_jours: number;
  somme_taux: number;
  moyenne: number;
}

export interface TauxOccupationResponse {
  details: TauxOccupation[];
  moyennes: TauxOccupationMoyen[];
}

export interface Revenu {
  date: string;
  type_revenu: 'HEBERGEMENT' | 'RESTAURATION' | 'AUTRE';
  montant: number;
  montant_cumule: number;
}

export interface RevenuTotal {
  type_revenu: string;
  total: number;
}

export interface RevenusResponse {
  details: Revenu[];
  totaux: RevenuTotal[];
}

export interface ChambrePopulaire {
  chambre_id: string;
  numero: string;
  type_chambre: string;
  nombre_reservations: number;
  nombre_nuits: number;
  revenus_totaux: number;
  nombre_clients_uniques: number;
  revenu_moyen_par_nuit: number;
}

export interface ChambresPopulairesResponse {
  chambres: ChambrePopulaire[];
  periode: {
    date_debut: string;
    date_fin: string;
  };
}

export interface RapportParams {
  type_rapport: 'OCCUPATION' | 'REVENUS' | 'CHAMBRES_POPULAIRES';
  date_debut: string;
  date_fin: string;
  format?: 'JSON' | 'CSV';
  type_chambre?: string;
  type_revenu?: 'HEBERGEMENT' | 'RESTAURATION' | 'AUTRE';
  limite?: number;
}

export interface RapportResponse {
  type_rapport: string;
  periode: {
    date_debut: string;
    date_fin: string;
  };
  format: string;
  donnees: TauxOccupationResponse | RevenusResponse | ChambresPopulairesResponse;
}

class StatistiqueService {
  // Récupération du taux d'occupation
  async getTauxOccupation(params: { date_debut: string; date_fin: string; type_chambre?: string }): Promise<TauxOccupationResponse> {
    const response = await api.get<TauxOccupationResponse>('/statistiques/occupation', { params });
    return response.data;
  }

  // Récupération des revenus
  async getRevenus(params: { date_debut: string; date_fin: string; type_revenu?: string }): Promise<RevenusResponse> {
    const response = await api.get<RevenusResponse>('/statistiques/revenus', { params });
    return response.data;
  }

  // Récupération des chambres les plus populaires
  async getChambresPopulaires(params: { date_debut: string; date_fin: string; limite?: number }): Promise<ChambresPopulairesResponse> {
    const response = await api.get<ChambresPopulairesResponse>('/statistiques/chambres-populaires', { params });
    return response.data;
  }

  // Génération de rapports (correspond à la route GET /rapports)
  async genererRapport(params: RapportParams): Promise<RapportResponse> {
    const response = await api.get<RapportResponse>('/statistiques/rapports', { params });
    return response.data;
  }

  // Méthodes utilitaires
  async getStatistiquesPeriode(date_debut: string, date_fin: string): Promise<{
    occupation: TauxOccupationResponse;
    revenus: RevenusResponse;
    chambresPopulaires: ChambresPopulairesResponse;
  }> {
    try {
      const [occupation, revenus, chambresPopulaires] = await Promise.all([
        this.getTauxOccupation({ date_debut, date_fin }),
        this.getRevenus({ date_debut, date_fin }),
        this.getChambresPopulaires({ date_debut, date_fin, limite: 10 })
      ]);

      return {
        occupation,
        revenus,
        chambresPopulaires
      };
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      throw error;
    }
  }

  async getTauxOccupationMoyen(date_debut: string, date_fin: string, type_chambre?: string): Promise<number> {
    try {
      const data = await this.getTauxOccupation({ date_debut, date_fin, type_chambre });

      if (type_chambre) {
        const moyenne = data.moyennes.find(m => m.type_chambre === type_chambre);
        return moyenne ? moyenne.moyenne : 0;
      }

      const totalSomme = data.moyennes.reduce((sum, m) => sum + m.somme_taux, 0);
      const totalJours = data.moyennes.reduce((sum, m) => sum + m.total_jours, 0);

      return totalJours > 0 ? totalSomme / totalJours : 0;
    } catch (error) {
      console.error('Erreur lors du calcul du taux d\'occupation moyen:', error);
      return 0;
    }
  }

  async getRevenuTotal(date_debut: string, date_fin: string, type_revenu?: string): Promise<number> {
    try {
      const data = await this.getRevenus({ date_debut, date_fin, type_revenu });

      if (type_revenu) {
        const total = data.totaux.find(t => t.type_revenu === type_revenu);
        return total ? total.total : 0;
      }

      return data.totaux.reduce((sum, t) => sum + t.total, 0);
    } catch (error) {
      console.error('Erreur lors du calcul du revenu total:', error);
      return 0;
    }
  }

  async genererRapportOccupation(date_debut: string, date_fin: string, format: 'JSON' | 'CSV' = 'JSON'): Promise<RapportResponse> {
    return this.genererRapport({
      type_rapport: 'OCCUPATION',
      date_debut,
      date_fin,
      format
    });
  }

  async genererRapportRevenus(date_debut: string, date_fin: string, format: 'JSON' | 'CSV' = 'JSON'): Promise<RapportResponse> {
    return this.genererRapport({
      type_rapport: 'REVENUS',
      date_debut,
      date_fin,
      format
    });
  }

  async genererRapportChambresPopulaires(date_debut: string, date_fin: string, limite: number = 10, format: 'JSON' | 'CSV' = 'JSON'): Promise<RapportResponse> {
    return this.genererRapport({
      type_rapport: 'CHAMBRES_POPULAIRES',
      date_debut,
      date_fin,
      format,
      limite
    });
  }
}

export const statistiqueService = new StatistiqueService(); 