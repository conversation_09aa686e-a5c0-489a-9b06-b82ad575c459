import api from './api.config';
import type {
  ImportExcel,
  ImportFilters,
  TemplateImport,
  ImportResponse,
  TemplateResponse,
  ServiceType
} from '../types';

/**
 * Service pour la gestion des imports et templates
 */
class ImportService {
  private baseURL = 'imports';

  /**
   * Démarrage du traitement d'un import
   */
  async processImport(importId: number): Promise<ImportExcel> {
    try {
      const response = await api.post<ImportResponse>(`${this.baseURL}/${importId}/process`);

      if (response.data.success && !Array.isArray(response.data.data)) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Erreur lors du traitement de l\'import');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors du traitement de l\'import');
    }
  }

  /**
   * Récupération de l'historique des imports
   */
  async getImportHistory(complexeId: number, filters: ImportFilters = {}): Promise<ImportExcel[]> {
    try {
      const params = new URLSearchParams({
        complexe_id: complexeId.toString(),
        ...this.buildImportFilterParams(filters)
      });

      const response = await api.get<ImportResponse>(`${this.baseURL}/history?${params}`);

      if (response.data.success) {
        return Array.isArray(response.data.data) ? response.data.data : [response.data.data];
      }
      
      throw new Error(response.data.message || 'Erreur lors de la récupération de l\'historique');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération de l\'historique');
    }
  }

  /**
   * Annulation/Rollback d'un import
   */
  async rollbackImport(importId: number): Promise<void> {
    try {
      const response = await api.post(`${this.baseURL}/${importId}/rollback`);

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de l\'annulation de l\'import');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de l\'annulation de l\'import');
    }
  }

  /**
   * Génération d'un rapport d'import
   */
  async getImportReport(
    importId: number, 
    format: 'json' | 'pdf' | 'excel' = 'json'
  ): Promise<any> {
    try {
      const response = await api.get(`${this.baseURL}/${importId}/report?format=${format}`, {
        responseType: format === 'json' ? 'json' : 'blob'
      });

      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la génération du rapport');
    }
  }

  /**
   * Téléchargement d'un rapport d'import
   */
  async downloadImportReport(
    importId: number, 
    format: 'pdf' | 'excel' = 'pdf'
  ): Promise<void> {
    try {
      const blob = await this.getImportReport(importId, format);
      
      // Créer un nom de fichier approprié
      const extension = format === 'pdf' ? 'pdf' : 'xlsx';
      const filename = `rapport_import_${importId}_${new Date().toISOString().split('T')[0]}.${extension}`;
      
      // Créer un lien de téléchargement
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      throw new Error(error.message || 'Erreur lors du téléchargement du rapport');
    }
  }

  /**
   * Récupération des templates disponibles
   */
  async getTemplates(typeService?: ServiceType): Promise<TemplateImport[]> {
    try {
      const params = typeService ? `?type_service=${typeService}` : '';
      const response = await api.get<TemplateResponse>(`${this.baseURL}/templates${params}`);

      if (response.data.success) {
        return Array.isArray(response.data.data) ? response.data.data : [response.data.data];
      }
      
      throw new Error(response.data.message || 'Erreur lors de la récupération des templates');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des templates');
    }
  }

  /**
   * Création d'un nouveau template
   */
  async createTemplate(templateData: Partial<TemplateImport>): Promise<TemplateImport> {
    try {
      const response = await api.post<TemplateResponse>(`${this.baseURL}/templates`, templateData);

      if (response.data.success && !Array.isArray(response.data.data)) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Erreur lors de la création du template');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la création du template');
    }
  }

  /**
   * Mise à jour d'un template
   */
  async updateTemplate(templateId: number, templateData: Partial<TemplateImport>): Promise<TemplateImport> {
    try {
      const response = await api.put<TemplateResponse>(`${this.baseURL}/templates/${templateId}`, templateData);

      if (response.data.success && !Array.isArray(response.data.data)) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Erreur lors de la mise à jour du template');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la mise à jour du template');
    }
  }

  /**
   * Suppression d'un template
   */
  async deleteTemplate(templateId: number): Promise<void> {
    try {
      const response = await api.delete(`${this.baseURL}/templates/${templateId}`);

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la suppression du template');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la suppression du template');
    }
  }

  /**
   * Téléchargement d'un template Excel
   */
  async downloadTemplate(typeImport: string): Promise<Blob> {
    try {
      const response = await api.get(
        `upload/templates/${typeImport}`,
        {
          responseType: 'blob',
          timeout: 15000
        }
      );

      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors du téléchargement du template');
    }
  }

  /**
   * Récupération des détails d'un import
   */
  async getImportDetails(importId: number): Promise<ImportExcel> {
    try {
      const response = await api.get<ImportResponse>(`${this.baseURL}/${importId}`);

      if (response.data.success && !Array.isArray(response.data.data)) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Import non trouvé');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des détails de l\'import');
    }
  }

  /**
   * Récupération des statistiques d'import
   */
  async getImportStatistics(complexeId: number, period?: string): Promise<any> {
    try {
      const params = new URLSearchParams({ complexe_id: complexeId.toString() });
      if (period) params.append('period', period);

      const response = await api.get(`${this.baseURL}/statistics?${params}`);
      
      if (response.data.success) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Erreur lors de la récupération des statistiques');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des statistiques');
    }
  }

  /**
   * Validation d'un template
   */
  async validateTemplate(templateData: Partial<TemplateImport>): Promise<{ valid: boolean; errors?: string[] }> {
    try {
      const response = await api.post(`${this.baseURL}/templates/validate`, templateData);
      
      if (response.data.success) {
        return response.data.data;
      }
      
      return { valid: false, errors: [response.data.message || 'Template invalide'] };
    } catch (error: any) {
      return { 
        valid: false, 
        errors: [error.response?.data?.message || 'Erreur lors de la validation du template'] 
      };
    }
  }

  /**
   * Construction des paramètres de filtre pour l'URL
   */
  private buildImportFilterParams(filters: ImportFilters): Record<string, string> {
    const params: Record<string, string> = {};

    if (filters.type_import) params.type_import = filters.type_import;
    if (filters.statut) params.statut = filters.statut;
    if (filters.date_debut) params.date_debut = filters.date_debut;
    if (filters.date_fin) params.date_fin = filters.date_fin;
    if (filters.page) params.page = filters.page.toString();
    if (filters.limit) params.limit = filters.limit.toString();

    return params;
  }
}

export const importService = new ImportService();
export default importService;
