import api from './api.config';

export interface Ticket {
  ticket_id: string;
  numero_ticket: string;
  reservation_id?: string; // Optionnel pour les tickets de service
  qr_code_data: string;
  date_emission: string;
  statut: 'ACTIF' | 'UTILISE' | 'ANNULE' | 'EXPIRE';
  employe_emission_id?: string;
  updated_at?: string;
  // Nouvelles propriétés pour les tickets de service
  type_ticket?: 'RESERVATION' | 'SERVICE';
  service_id?: number;
  pos_id?: number;
  session_id?: number;
  nom_client?: string;
  nombre_personnes?: number;
  duree_heures?: number;
  prix_total?: number;
  mode_paiement?: string;
  statut_paiement?: string;
  date_expiration?: string;
}

export interface TicketDetails extends Ticket {
  reservation: {
    numero_reservation: string;
    client: string;
    chambre: string;
    type_chambre: string;
    date_arrivee: string;
    date_depart: string;
  };
  employe?: {
    nom: string;
    prenom: string;
  };
}

export interface TicketVerification {
  valide: boolean;
  raison?: string;
  statut?: string;
  date_arrivee?: string;
  date_depart?: string;
  ticket?: {
    numero_ticket: string;
    client: string;
    date_arrivee: string;
    date_depart: string;
    statut: string;
  };
}

export interface TicketGenerationParams {
  reservation_id: string;
  type_ticket?: 'standard' | 'vip' | 'groupe' | 'enfant';
  nombre_tickets?: number;
  options?: {
    impression_automatique?: boolean;
    envoi_email?: boolean;
    envoi_sms?: boolean;
  };
}

export interface TicketStatistiques {
  periode: {
    debut: string;
    fin: string;
  };
  total_tickets: number;
  tickets_par_statut: Record<string, number>;
  tickets_par_type: Record<string, number>;
  taux_utilisation: number;
}

export interface TicketConfiguration {
  template_design?: string;
  informations_affichees?: string[];
  qr_code_options?: {
    taille?: number;
    couleur?: string;
    logo?: string;
  };
  impression_options?: {
    format?: 'A4' | 'ticket' | 'badge';
    orientation?: 'portrait' | 'landscape';
  };
}

// ==================== INTERFACES POUR TICKETS DE SERVICE ====================

export interface ServiceTicket extends Ticket {
  type_ticket: 'SERVICE';
  service_id: number;
  pos_id: number;
  session_id: number;
  nom_client: string;
  nombre_personnes: number;
  duree_heures?: number;
  prix_total: number;
  mode_paiement: 'Espèces' | 'Carte' | 'Virement' | 'Chèque';
  statut_paiement: 'En attente' | 'Payé' | 'Remboursé' | 'Annulé';
  date_expiration?: string;
}

export interface CreateServiceTicketData {
  service_id: number;
  pos_id: number;
  session_id: number;
  nom_client: string;
  nombre_personnes: number;
  duree_heures?: number;
  mode_paiement: 'Espèces' | 'Carte' | 'Virement' | 'Chèque';
  prix_total: number;
}

export interface ServiceTicketResponse {
  ticket: ServiceTicket;
  qr_code: string;
  service: {
    nom: string;
    type: string;
    complexe: string;
  };
  qr_data: {
    type: 'SERVICE';
    service_id: number;
    service_nom: string;
    service_type: string;
    nom_client: string;
    nombre_personnes: number;
    duree_heures?: number;
    date_creation: string;
    date_expiration?: string;
    numero_ticket: string;
  };
}

export interface ServiceTicketVerification {
  valide: boolean;
  raison?: string;
  ticket?: {
    numero_ticket: string;
    nom_client: string;
    nombre_personnes: number;
    duree_heures?: number;
    service_nom: string;
    service_type: string;
    date_emission: string;
    date_expiration?: string;
    statut: string;
  };
}

export interface ServiceTicketFilters {
  statut?: 'ACTIF' | 'UTILISE' | 'ANNULE' | 'EXPIRE';
  date_debut?: string;
  date_fin?: string;
}

class TicketService {
  // Routes client
  async getTicketsReservation(reservationId: string): Promise<Ticket[]> {
    const response = await api.get<Ticket[]>(`/tickets/client/${reservationId}`);
    return response.data;
  }

  async getQRCode(ticketId: string): Promise<{ qrCode: string }> {
    const response = await api.get<{ qrCode: string }>(`/tickets/client/qrcode/${ticketId}`);
    return response.data;
  }

  async getPDFTicket(ticketId: string): Promise<Blob> {
    const response = await api.get(`/tickets/client/pdf/${ticketId}`, {
      responseType: 'blob'
    });
    return response.data;
  }

  // Routes réception
  async genererTickets(reservationId: string, params: TicketGenerationParams): Promise<Ticket[]> {
    const response = await api.post<Ticket[]>(`/tickets/reception/generer/${reservationId}`, params);
    return response.data;
  }

  async validerTicket(ticketId: string): Promise<Ticket> {
    const response = await api.post<Ticket>(`/tickets/reception/valider/${ticketId}`);
    return response.data;
  }

  async imprimerTicket(ticketId: string): Promise<Blob> {
    const response = await api.post(`/tickets/reception/imprimer/${ticketId}`, {}, {
      responseType: 'blob'
    });
    return response.data;
  }

  async getStatutTicket(ticketId: string): Promise<{ statut: Ticket['statut'] }> {
    const response = await api.get<{ statut: Ticket['statut'] }>(`/tickets/reception/statut/${ticketId}`);
    return response.data;
  }

  // Routes admin
  async getStatistiquesTickets(params: { dateDebut: string; dateFin: string }): Promise<TicketStatistiques> {
    const response = await api.get<TicketStatistiques>('/tickets/admin/statistiques', { params });
    return response.data;
  }

  async genererRapportsTickets(params: { dateDebut: string; dateFin: string; format?: 'pdf' | 'excel' | 'json' }): Promise<any> {
    const response = await api.get('/tickets/admin/rapports', { params });
    return response.data;
  }

  async configurerTickets(configuration: TicketConfiguration): Promise<TicketConfiguration> {
    const response = await api.post<TicketConfiguration>('/tickets/admin/config', configuration);
    return response.data;
  }

  // Routes publiques
  async verifierTicket(numero: string): Promise<TicketVerification> {
    const response = await api.get<TicketVerification>(`/tickets/verification/${numero}`);
    return response.data;
  }

  // Route générique - génération de ticket depuis numéro de réservation
  async genererTicketFromReservation(numero: string, params: TicketGenerationParams): Promise<Ticket> {
    const response = await api.post<Ticket>(`/tickets/reservation/${numero}`, params);
    return response.data;
  }

  // Méthode alternative d'impression (route générique)
  async imprimerTicketGeneric(ticketId: string): Promise<Blob> {
    const response = await api.get(`/tickets/${ticketId}/imprimer`, {
      responseType: 'blob'
    });
    return response.data;
  }

  // Méthode commentée car la route correspondante n'existe pas dans le backend actuel
  /*
  async genererTicket(params: TicketGenerationParams): Promise<Ticket> {
    const response = await api.post<Ticket>('/tickets', params);
    return response.data;
  }
  */

  async getTicketById(id: string): Promise<TicketDetails> {
    const response = await api.get<TicketDetails>(`/tickets/${id}`);
    return response.data;
  }

  async updateTicketStatus(id: string, statut: Ticket['statut']): Promise<Ticket> {
    const response = await api.patch<Ticket>(`/tickets/${id}/statut`, { statut });
    return response.data;
  }

  // ==================== MÉTHODES POUR TICKETS DE SERVICE ====================

  /**
   * Créer un ticket de service (piscine, bar, restaurant)
   */
  async createServiceTicket(data: CreateServiceTicketData): Promise<ServiceTicketResponse> {
    try {
      const response = await api.post<ServiceTicketResponse>('/tickets/service', data);
      return response.data;
    } catch (error) {
      console.error('Error creating service ticket:', error);
      throw error;
    }
  }

  /**
   * Récupérer les tickets d'un service spécifique
   */
  async getServiceTickets(serviceId: number, filters?: ServiceTicketFilters): Promise<ServiceTicket[]> {
    try {
      const response = await api.get<ServiceTicket[]>(`/tickets/service/${serviceId}`, {
        params: filters
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching service tickets:', error);
      throw error;
    }
  }

  /**
   * Marquer un ticket de service comme utilisé
   */
  async markServiceTicketUsed(ticketId: number): Promise<ServiceTicket> {
    try {
      const response = await api.put<ServiceTicket>(`/tickets/service/${ticketId}/utiliser`);
      return response.data;
    } catch (error) {
      console.error('Error marking service ticket as used:', error);
      throw error;
    }
  }

  /**
   * Vérifier un ticket de service par son numéro
   */
  async verifyServiceTicket(numero: string): Promise<ServiceTicketVerification> {
    try {
      const response = await api.get<ServiceTicketVerification>(`/tickets/service/verification/${numero}`);
      return response.data;
    } catch (error) {
      console.error('Error verifying service ticket:', error);
      throw error;
    }
  }

  /**
   * Récupérer tous les tickets d'une session de caisse
   */
  async getTicketsBySession(sessionId: number): Promise<Ticket[]> {
    try {
      const response = await api.get<Ticket[]>(`/tickets/session/${sessionId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching tickets by session:', error);
      throw error;
    }
  }

  /**
   * Calculer le prix d'un ticket de service selon les tarifs
   */
  calculateServiceTicketPrice(
    tarification: any,
    nombrePersonnes: number,
    dureeHeures?: number
  ): number {
    if (!tarification) return 0;

    let total = 0;

    // Prix par personne
    if (tarification.prix_par_personne) {
      total += nombrePersonnes * tarification.prix_par_personne;
    }

    // Prix par heure (pour les services temporaires comme la piscine)
    if (dureeHeures && tarification.prix_par_heure) {
      total += dureeHeures * tarification.prix_par_heure;
    }

    // Prix forfaitaire si défini
    if (tarification.prix_forfaitaire) {
      total = Math.max(total, tarification.prix_forfaitaire);
    }

    return Math.round(total * 100) / 100; // Arrondir à 2 décimales
  }

  /**
   * Valider les données avant création d'un ticket de service
   */
  validateServiceTicketData(data: CreateServiceTicketData): string[] {
    const errors: string[] = [];

    if (!data.service_id) {
      errors.push('L\'ID du service est requis');
    }

    if (!data.pos_id) {
      errors.push('L\'ID du point de vente est requis');
    }

    if (!data.session_id) {
      errors.push('L\'ID de la session de caisse est requis');
    }

    if (!data.nom_client || data.nom_client.trim() === '') {
      errors.push('Le nom du client est requis');
    }

    if (!data.nombre_personnes || data.nombre_personnes < 1) {
      errors.push('Le nombre de personnes doit être supérieur à 0');
    }

    if (!data.mode_paiement) {
      errors.push('Le mode de paiement est requis');
    }

    if (!data.prix_total || data.prix_total <= 0) {
      errors.push('Le prix total doit être supérieur à 0');
    }

    if (data.duree_heures !== undefined && data.duree_heures <= 0) {
      errors.push('La durée doit être supérieure à 0');
    }

    return errors;
  }

  /**
   * Formater les données d'un ticket de service pour l'affichage
   */
  formatServiceTicketForDisplay(ticket: ServiceTicket) {
    return {
      numero: ticket.numero_ticket,
      client: ticket.nom_client,
      personnes: ticket.nombre_personnes,
      duree: ticket.duree_heures ? `${ticket.duree_heures}h` : 'N/A',
      prix: `${ticket.prix_total}FCFA`,
      paiement: ticket.mode_paiement,
      statut: ticket.statut,
      date: new Date(ticket.date_emission).toLocaleString('fr-FR'),
      expiration: ticket.date_expiration
        ? new Date(ticket.date_expiration).toLocaleString('fr-FR')
        : 'Aucune'
    };
  }
}

export const ticketService = new TicketService();