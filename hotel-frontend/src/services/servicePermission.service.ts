import api from './api.config';
import { authService } from './auth.service';
import { employeePermissionService } from './employeePermission.service';
import { roleService } from './role.service';

export type ServiceType = 'Restaurant' | 'Bar' | 'Piscine';

export interface ServicePermission {
  serviceType: ServiceType;
  canAccess: boolean;
  canOperate: boolean;
  permissions: string[];
}

export interface UserServicePermissions {
  restaurant: ServicePermission;
  bar: ServicePermission;
  piscine: ServicePermission;
}

// ===== MAPPING SIMPLIFIÉ DES PERMISSIONS =====

const SIMPLIFIED_SERVICE_PERMISSIONS = {
  'Restaurant': {
    access: 'service_operations',
    operate: 'service_operations',
    manage: 'management_operations'
  },
  'Bar': {
    access: 'service_operations',
    operate: 'service_operations',
    manage: 'management_operations'
  },
  'Piscine': {
    access: 'piscine_operations',
    operate: 'piscine_operations',
    manage: 'management_operations'
  }
};

class ServicePermissionService {
  private static instance: ServicePermissionService;

  public static getInstance(): ServicePermissionService {
    if (!ServicePermissionService.instance) {
      ServicePermissionService.instance = new ServicePermissionService();
    }
    return ServicePermissionService.instance;
  }

  private checkAuth() {
    if (!authService.isAuthenticated()) {
      throw new Error('Non authentifié');
    }
  }

  /**
   * Récupère les permissions de services pour l'utilisateur connecté
   * Version simplifiée utilisant le nouveau système
   */
  public async getUserServicePermissions(): Promise<UserServicePermissions> {
    try {
      this.checkAuth();

      // Utiliser le nouveau système de permissions
      const [restaurantPerms, barPerms, piscinePerms] = await Promise.all([
        this.getSimplifiedServicePermissions('Restaurant'),
        this.getSimplifiedServicePermissions('Bar'),
        this.getSimplifiedServicePermissions('Piscine')
      ]);

      return {
        restaurant: restaurantPerms,
        bar: barPerms,
        piscine: piscinePerms
      };
    } catch (error) {
      console.error('Error fetching user service permissions:', error);
      throw error;
    }
  }

  /**
   * Récupère les permissions simplifiées pour un service spécifique
   */
  private async getSimplifiedServicePermissions(serviceType: ServiceType): Promise<ServicePermission> {
    try {
      // Si admin, accès complet
      if (authService.isAdmin()) {
        return {
          serviceType,
          canAccess: true,
          canOperate: true,
          permissions: ['full_access']
        };
      }

      // Pour les employés, vérifier selon le nouveau système
      const serviceMapping = SIMPLIFIED_SERVICE_PERMISSIONS[serviceType];
      const canAccess = await employeePermissionService.hasPermission(serviceMapping.access);
      const canOperate = await employeePermissionService.hasPermission(serviceMapping.operate);

      // Vérifier l'accès au service via services_autorises
      const hasServiceAccess = await employeePermissionService.canAccessService(serviceType.toLowerCase());

      return {
        serviceType,
        canAccess: canAccess && hasServiceAccess,
        canOperate: canOperate && hasServiceAccess,
        permissions: canAccess ? [serviceMapping.access] : []
      };
    } catch (error) {
      console.error(`Error getting simplified permissions for ${serviceType}:`, error);
      return {
        serviceType,
        canAccess: false,
        canOperate: false,
        permissions: []
      };
    }
  }

  /**
   * Vérifie si l'utilisateur peut accéder à une interface de service
   * Version simplifiée utilisant le nouveau système
   */
  public async canAccessService(serviceType: ServiceType): Promise<boolean> {
    try {
      this.checkAuth();

      // Si admin, accès complet
      if (authService.isAdmin()) {
        return true;
      }

      // Pour les employés, vérifier permission + accès service
      const serviceMapping = SIMPLIFIED_SERVICE_PERMISSIONS[serviceType];
      const hasPermission = await employeePermissionService.hasPermission(serviceMapping.access);
      const hasServiceAccess = await employeePermissionService.canAccessService(serviceType.toLowerCase());

      return hasPermission && hasServiceAccess;
    } catch (error) {
      console.error(`Error checking access to ${serviceType}:`, error);
      return false;
    }
  }

  /**
   * Vérifie si l'utilisateur peut opérer sur un service
   * Version simplifiée utilisant le nouveau système
   */
  public async canOperateService(serviceType: ServiceType): Promise<boolean> {
    try {
      this.checkAuth();

      // Si admin, accès complet
      if (authService.isAdmin()) {
        return true;
      }

      // Pour les employés, vérifier permission + accès service
      const serviceMapping = SIMPLIFIED_SERVICE_PERMISSIONS[serviceType];
      const hasPermission = await employeePermissionService.hasPermission(serviceMapping.operate);
      const hasServiceAccess = await employeePermissionService.canAccessService(serviceType.toLowerCase());

      return hasPermission && hasServiceAccess;
    } catch (error) {
      console.error(`Error checking operation access to ${serviceType}:`, error);
      return false;
    }
  }

  /**
   * Récupère la liste des services auxquels l'utilisateur a accès
   */
  public async getAccessibleServices(): Promise<ServiceType[]> {
    try {
      const permissions = await this.getUserServicePermissions();
      const accessibleServices: ServiceType[] = [];

      if (permissions.restaurant.canAccess) {
        accessibleServices.push('Restaurant');
      }
      if (permissions.bar.canAccess) {
        accessibleServices.push('Bar');
      }
      if (permissions.piscine.canAccess) {
        accessibleServices.push('Piscine');
      }

      return accessibleServices;
    } catch (error) {
      console.error('Error fetching accessible services:', error);
      return [];
    }
  }

  /**
   * Récupère la liste des services que l'utilisateur peut opérer
   */
  public async getOperableServices(): Promise<ServiceType[]> {
    try {
      const permissions = await this.getUserServicePermissions();
      const operableServices: ServiceType[] = [];

      if (permissions.restaurant.canOperate) {
        operableServices.push('Restaurant');
      }
      if (permissions.bar.canOperate) {
        operableServices.push('Bar');
      }
      if (permissions.piscine.canOperate) {
        operableServices.push('Piscine');
      }

      return operableServices;
    } catch (error) {
      console.error('Error fetching operable services:', error);
      return [];
    }
  }

  /**
   * Vérifie si l'utilisateur a des permissions spécifiques pour un service
   */
  public async hasServicePermission(serviceType: ServiceType, permission: string): Promise<boolean> {
    try {
      this.checkAuth();
      
      // Construire la permission spécifique au service
      const servicePermission = `${permission}_${serviceType.toLowerCase()}`;
      
      // Vérifier d'abord la permission spécifique au service
      const hasSpecific = await roleService.checkUserPermission(servicePermission);
      if (hasSpecific) return true;

      // Vérifier les permissions générales
      const hasGeneral = await roleService.checkUserPermission(permission);
      if (hasGeneral) return true;

      // Vérifier les permissions de gestion globale
      if (permission.startsWith('manage_') || permission.startsWith('view_')) {
        const globalPermission = permission.replace(/_\w+$/, '_services');
        return await roleService.checkUserPermission(globalPermission);
      }

      return false;
    } catch (error) {
      console.error(`Error checking service permission ${permission} for ${serviceType}:`, error);
      return false;
    }
  }

  /**
   * Récupère les permissions disponibles pour les services
   */
  public getServicePermissionsList(): Record<ServiceType, string[]> {
    return {
      'Restaurant': [
        'access_restaurant_interface',
        'operate_restaurant_pos',
        'view_tables',
        'manage_tables',
        'manage_table_reservations',
        'view_table_reservations',
        'view_menu',
        'manage_menu',
        'manage_menu_prices',
        'view_orders',
        'manage_orders',
        'view_kitchen_orders'
      ],
      'Bar': [
        'access_bar_interface',
        'operate_bar_pos',
        'view_tables',
        'manage_tables',
        'manage_table_reservations',
        'view_table_reservations',
        'view_menu',
        'manage_menu',
        'manage_menu_prices',
        'view_orders',
        'manage_orders'
      ],
      'Piscine': [
        'access_piscine_interface',
        'operate_piscine',
        'manage_piscine_access',
        'view_piscine_reservations'
      ]
    };
  }

  /**
   * Récupère les descriptions des permissions de services
   */
  public getServicePermissionsDescriptions(): Record<string, string> {
    return {
      // Accès aux interfaces
      'access_restaurant_interface': 'Accéder à l\'interface Restaurant',
      'access_bar_interface': 'Accéder à l\'interface Bar',
      'access_piscine_interface': 'Accéder à l\'interface Piscine',

      // Opérations POS
      'operate_restaurant_pos': 'Utiliser le POS Restaurant',
      'operate_bar_pos': 'Utiliser le POS Bar',
      'operate_piscine': 'Utiliser les fonctionnalités Piscine',

      // Gestion des tables
      'view_tables': 'Consulter les tables',
      'manage_tables': 'Gérer les tables',
      'manage_table_reservations': 'Gérer les réservations de tables',
      'view_table_reservations': 'Consulter les réservations de tables',

      // Gestion des menus
      'view_menu': 'Consulter les menus',
      'manage_menu': 'Gérer les menus',
      'manage_menu_prices': 'Gérer les prix des menus',

      // Gestion des commandes
      'view_orders': 'Consulter les commandes',
      'manage_orders': 'Gérer les commandes',
      'view_kitchen_orders': 'Voir les commandes cuisine',

      // Anciennes permissions (compatibilité)
      'manage_restaurant_menu': 'Gérer le menu du restaurant',
      'manage_bar_menu': 'Gérer la carte du bar',
      'manage_piscine_access': 'Gérer l\'accès à la piscine',
      'view_restaurant_orders': 'Voir les commandes restaurant',
      'view_bar_orders': 'Voir les commandes bar',
      'view_piscine_reservations': 'Voir les réservations piscine'
    };
  }

  /**
   * Vérifie les permissions spécifiques aux tables
   */
  public async canManageTables(): Promise<boolean> {
    return await roleService.checkUserPermission('manage_tables');
  }

  public async canViewTables(): Promise<boolean> {
    return await roleService.checkUserPermission('view_tables');
  }

  public async canManageTableReservations(): Promise<boolean> {
    return await roleService.checkUserPermission('manage_table_reservations');
  }

  public async canViewTableReservations(): Promise<boolean> {
    return await roleService.checkUserPermission('view_table_reservations');
  }

  /**
   * Vérifie les permissions spécifiques aux menus
   */
  public async canManageMenu(): Promise<boolean> {
    return await roleService.checkUserPermission('manage_menu');
  }

  public async canViewMenu(): Promise<boolean> {
    return await roleService.checkUserPermission('view_menu');
  }

  public async canManageMenuPrices(): Promise<boolean> {
    return await roleService.checkUserPermission('manage_menu_prices');
  }

  /**
   * Vérifie les permissions spécifiques aux commandes
   */
  public async canManageOrders(): Promise<boolean> {
    return await roleService.checkUserPermission('manage_orders');
  }

  public async canViewOrders(): Promise<boolean> {
    return await roleService.checkUserPermission('view_orders');
  }

  public async canViewKitchenOrders(): Promise<boolean> {
    return await roleService.checkUserPermission('view_kitchen_orders');
  }

  /**
   * Vérifie les permissions POS spécifiques
   */
  public async canOperateRestaurantPOS(): Promise<boolean> {
    return await roleService.checkUserPermission('operate_restaurant_pos');
  }

  public async canOperateBarPOS(): Promise<boolean> {
    return await roleService.checkUserPermission('operate_bar_pos');
  }

  // ===== NOUVELLES MÉTHODES SIMPLIFIÉES =====

  /**
   * Vérifie si l'utilisateur peut gérer les services (admin ou gérant)
   */
  public async canManageServices(): Promise<boolean> {
    if (authService.isAdmin()) {
      return true;
    }
    return await employeePermissionService.hasPermission('management_operations');
  }

  /**
   * Vérifie si l'utilisateur peut accéder aux rapports
   */
  public async canAccessReports(): Promise<boolean> {
    return authService.canAccessReports();
  }

  /**
   * Vérifie si l'utilisateur peut configurer le système
   */
  public async canConfigureSystem(): Promise<boolean> {
    return authService.canAccessConfiguration();
  }

  /**
   * Récupère le type d'utilisateur pour l'affichage
   */
  public getUserTypeInfo(): { type: string; label: string; isAdmin: boolean } {
    const user = authService.getCurrentUser();
    if (!user) {
      return { type: 'unknown', label: 'Inconnu', isAdmin: false };
    }

    return {
      type: user.role,
      label: authService.getUserTypeLabel(),
      isAdmin: authService.isAdmin()
    };
  }

  /**
   * Récupère les services autorisés pour l'utilisateur connecté
   */
  public async getAuthorizedServices(): Promise<string[]> {
    if (authService.isAdmin()) {
      return ['restaurant', 'bar', 'piscine', 'hebergement'];
    }
    return authService.getAuthorizedServices();
  }

  /**
   * Vérifie si l'utilisateur peut accéder à la cuisine
   */
  public async canAccessKitchen(): Promise<boolean> {
    if (authService.isAdmin()) {
      return true;
    }
    return await employeePermissionService.hasPermission('kitchen_operations');
  }

  /**
   * Vérifie si l'utilisateur peut gérer la réception
   */
  public async canManageReception(): Promise<boolean> {
    if (authService.isAdmin()) {
      return true;
    }
    return await employeePermissionService.hasPermission('reception_operations');
  }
}

export const servicePermissionService = ServicePermissionService.getInstance();
