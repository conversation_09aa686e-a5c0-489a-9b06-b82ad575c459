import axios from 'axios';

// Configuration de base de l'API
const API_BASE_URL = (import.meta.env.VITE_API_URL || 'http://localhost:8080') + '/api/';

// Configuration d'axios avec intercepteurs
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Fonction pour nettoyer le token
const cleanToken = (token: string): string => {
  // Supprimer le préfixe "Bearer " s'il existe
  return token.replace(/^Bearer\s+/i, '');
};

// Intercepteur pour ajouter le token d'authentification
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    const cleanTokenValue = cleanToken(token);
    config.headers.Authorization = `Bearer ${cleanTokenValue}`;
  }

  // Si c'est un FormData, ne pas définir Content-Type pour laisser le navigateur
  // gérer automatiquement le multipart/form-data avec la bonne boundary
  if (config.data instanceof FormData) {
    delete config.headers['Content-Type'];
  }

  return config;
});

// Intercepteur pour gérer les erreurs
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Gérer l'expiration du token
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;
