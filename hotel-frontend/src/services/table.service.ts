import api from './api.config';
import { authService } from './auth.service';

// Types pour les tables
export interface Table {
  table_id: number;
  service_id: number;
  numero: string;
  capacite: number;
  zone: string;
  statut: 'Libre' | 'Occupée' | 'Réservée' | 'Maintenance';
  position_x?: number;
  position_y?: number;
  commande_active?: Commande;
  created_at: string;
  updated_at?: string;
}

export interface TableReservation {
  reservation_id: number;
  table_id: number;
  client_id?: number;
  nom_client?: string;
  telephone_client?: string;
  date_debut: string;
  date_fin: string;
  nb_personnes: number;
  statut: 'Confirmée' | 'En attente' | 'Annulée';
  notes?: string;
  created_at: string;
}

export interface Commande {
  commande_id: number;
  service_id: number;
  table_id?: number;
  employe_id: number;
  date_commande: string;
  statut: 'En cours' | 'Servie' | 'Payée' | 'Annulée';
  montant_total: number;
  notes?: string;
}

export interface CreateTableParams {
  service_id: number;
  numero: string;
  capacite: number;
  zone: string;
  position_x?: number;
  position_y?: number;
}

export interface UpdateTableParams {
  numero?: string;
  capacite?: number;
  zone?: string;
  statut?: 'Libre' | 'Occupée' | 'Réservée' | 'Maintenance';
  position_x?: number;
  position_y?: number;
}

export interface CreateTableReservationParams {
  table_id: number;
  client_id?: number;
  nom_client?: string;
  telephone_client?: string;
  date_debut: string;
  date_fin: string;
  nb_personnes: number;
  notes?: string;
}

export interface TableResponse {
  success: boolean;
  data: Table | Table[];
  message?: string;
}

export interface TableReservationResponse {
  success: boolean;
  data: TableReservation | TableReservation[];
  message?: string;
}

export interface TableLayoutData {
  tables: Table[];
  zones: string[];
  dimensions: {
    width: number;
    height: number;
  };
}

class TableService {
  private checkAuth() {
    if (!authService.isAuthenticated()) {
      throw new Error('Non authentifié');
    }
  }

  private getComplexeId(): number {
    const user = authService.getCurrentUser();
    if (!user) throw new Error('Utilisateur non authentifié');

    if (user.role === 'admin_chaine') {
      const selectedComplexeId = localStorage.getItem('selectedComplexeId');
      if (!selectedComplexeId) {
        throw new Error('Aucun complexe sélectionné');
      }
      return parseInt(selectedComplexeId, 10);
    }

    if (!user.complexe_id) {
      throw new Error('Complexe ID non disponible');
    }
    return user.complexe_id;
  }

  // Récupération des tables par service
  async getTablesByService(serviceId: number): Promise<Table[]> {
    this.checkAuth();
    
    const response = await api.get<TableResponse>(`/tables/service/${serviceId}`);
    
    if (response.data.success && Array.isArray(response.data.data)) {
      return response.data.data;
    }
    return [];
  }

  // Récupération d'une table par ID
  async getTableById(tableId: number): Promise<Table> {
    this.checkAuth();
    
    const response = await api.get<TableResponse>(`/tables/${tableId}`);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Table non trouvée');
  }

  // Création d'une nouvelle table
  async createTable(data: CreateTableParams): Promise<Table> {
    this.checkAuth();
    
    const response = await api.post<TableResponse>('/tables', data);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la création de la table');
  }

  // Mise à jour d'une table
  async updateTable(tableId: number, data: UpdateTableParams): Promise<Table> {
    this.checkAuth();
    
    const response = await api.put<TableResponse>(`/tables/${tableId}`, data);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la mise à jour de la table');
  }

  // Mise à jour du statut d'une table
  async updateTableStatus(tableId: number, statut: string): Promise<void> {
    this.checkAuth();
    
    const response = await api.put<TableResponse>(`/tables/${tableId}/statut`, { statut });
    
    if (!response.data.success) {
      throw new Error('Erreur lors de la mise à jour du statut de la table');
    }
  }

  // Suppression d'une table
  async deleteTable(tableId: number): Promise<void> {
    this.checkAuth();
    
    const response = await api.delete<TableResponse>(`/tables/${tableId}`);
    
    if (!response.data.success) {
      throw new Error('Erreur lors de la suppression de la table');
    }
  }

  // Récupération du layout des tables pour un service
  async getTableLayout(serviceId: number): Promise<TableLayoutData> {
    this.checkAuth();
    
    const response = await api.get<{ success: boolean; data: TableLayoutData }>(`/tables/service/${serviceId}/layout`);
    
    if (response.data.success) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la récupération du layout des tables');
  }

  // Création d'une réservation de table
  async createTableReservation(data: CreateTableReservationParams): Promise<TableReservation> {
    this.checkAuth();
    
    const response = await api.post<TableReservationResponse>('/tables/reservations', data);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la création de la réservation de table');
  }

  // Récupération des réservations d'une table
  async getTableReservations(tableId: number, dateDebut?: string, dateFin?: string): Promise<TableReservation[]> {
    this.checkAuth();
    
    const params: any = {};
    if (dateDebut) params.dateDebut = dateDebut;
    if (dateFin) params.dateFin = dateFin;
    
    const response = await api.get<TableReservationResponse>(`/tables/${tableId}/reservations`, { params });
    
    if (response.data.success && Array.isArray(response.data.data)) {
      return response.data.data;
    }
    return [];
  }

  // Annulation d'une réservation de table
  async cancelTableReservation(reservationId: number): Promise<void> {
    this.checkAuth();
    
    const response = await api.put<TableReservationResponse>(`/tables/reservations/${reservationId}/cancel`);
    
    if (!response.data.success) {
      throw new Error('Erreur lors de l\'annulation de la réservation');
    }
  }

  // Vérification de la disponibilité d'une table
  async checkTableAvailability(tableId: number, dateDebut: string, dateFin: string): Promise<boolean> {
    this.checkAuth();
    
    const response = await api.get<{ success: boolean; data: { available: boolean } }>(`/tables/${tableId}/availability`, {
      params: { dateDebut, dateFin }
    });
    
    if (response.data.success) {
      return response.data.data.available;
    }
    return false;
  }

  // Libérer une table (fin de service)
  async libererTable(tableId: number): Promise<void> {
    await this.updateTableStatus(tableId, 'Libre');
  }

  // Occuper une table (début de service)
  async occuperTable(tableId: number): Promise<void> {
    await this.updateTableStatus(tableId, 'Occupée');
  }

  // Mettre une table en maintenance
  async mettreEnMaintenance(tableId: number): Promise<void> {
    await this.updateTableStatus(tableId, 'Maintenance');
  }
}

export const tableService = new TableService();
