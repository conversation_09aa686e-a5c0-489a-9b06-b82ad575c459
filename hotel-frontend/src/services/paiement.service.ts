import api from './api.config';

export interface PaiementRequest {
  reservationId: string;
  montant: number;
  mode: 'CARTE' | 'ESPECES' | 'CHEQUE' | 'VIREMENT';
  reference?: string;
  commentaire?: string;
}

export interface PaiementResponse {
  id: string;
  reservationId: string;
  montant: number;
  mode: PaiementRequest['mode'];
  statut: 'EN_ATTENTE' | 'VALIDE' | 'REFUSE' | 'ANNULE';
  date: string;
  reference?: string;
}

export interface PaiementDetails extends PaiementResponse {
  reservation: {
    id: string;
    numero: string;
    client: {
      nom: string;
      email: string;
    };
  };
  historique: Array<{
    date: string;
    action: string;
    utilisateur: string;
  }>;
}

class PaiementService {
  // Créer un paiement (correspond à la route POST /)
  async creerPaiement(data: PaiementRequest): Promise<PaiementResponse> {
    const response = await api.post<PaiementResponse>('/paiements', data);
    return response.data;
  }

  // Alias pour la compatibilité
  async effectuerPaiement(data: PaiementRequest): Promise<PaiementResponse> {
    return this.creerPaiement(data);
  }

  // Récupérer les paiements d'une réservation (correspond à GET /reservation/:id)
  async getPaiementsByReservation(reservationId: string): Promise<PaiementResponse[]> {
    const response = await api.get<PaiementResponse[]>(`/paiements/reservation/${reservationId}`);
    return response.data;
  }

  async getPaiementById(id: string): Promise<PaiementDetails> {
    const response = await api.get<PaiementDetails>(`/paiements/${id}`);
    return response.data;
  }

  async updatePaiementStatus(id: string, statut: PaiementResponse['statut']): Promise<PaiementResponse> {
    const response = await api.put<PaiementResponse>(`/paiements/${id}/statut`, { statut });
    return response.data;
  }

  async verifierStatutPaiement(id: string): Promise<PaiementResponse> {
    const response = await api.get<PaiementResponse>(`/paiements/verification/${id}`);
    return response.data;
  }

  // Méthodes commentées car les routes correspondantes n'existent pas dans le backend actuel
  /*
  async annulerPaiement(id: string, raison: string): Promise<PaiementResponse> {
    const response = await api.post<PaiementResponse>(`/paiement/${id}/annulation`, { raison });
    return response.data;
  }

  async getPaiementsByDate(debut: string, fin: string): Promise<PaiementResponse[]> {
    const response = await api.get<PaiementResponse[]>('/paiement/date', {
      params: { debut, fin }
    });
    return response.data;
  }

  async getPaiementsByMode(mode: PaiementRequest['mode']): Promise<PaiementResponse[]> {
    const response = await api.get<PaiementResponse[]>(`/paiement/mode/${mode}`);
    return response.data;
  }
  */
}

export const paiementService = new PaiementService(); 