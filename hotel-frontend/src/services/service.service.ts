import api from './api.config';
import { authService } from './auth.service';

// Types pour les services du complexe
export type ServiceType =
  | 'Restaurant'
  | 'Bar'
  | 'Piscine';

export interface ServiceComplexe {
  service_id: number;
  complexe_id: number;
  type_service: ServiceType;
  nom: string;
  description?: string;
  emplacement?: string;
  horaires_ouverture?: Record<string, any>;
  capacite?: number;
  image_url?: string;
  contact_email?: string;
  contact_telephone?: string;
  actif: boolean;
  configuration?: Record<string, any>;
  tarification?: Record<string, any>;
  created_at: string;
  updated_at?: string;
}

export interface CreateServiceParams {
  type_service: ServiceType;
  nom: string;
  description?: string;
  emplacement?: string;
  horaires_ouverture?: Record<string, any>;
  capacite?: number;
  image_url?: string;
  contact_email?: string;
  contact_telephone?: string;
  configuration?: Record<string, any>;
  tarification?: Record<string, any>;
}

export interface UpdateServiceParams extends Partial<CreateServiceParams> {
  actif?: boolean;
}

export interface ServiceParams {
  type_service?: ServiceType;
  actif?: boolean;
}

export interface ServiceResponse {
  success: boolean;
  data: ServiceComplexe | ServiceComplexe[];
  message?: string;
}

// Types spécifiques pour les tarifications
export interface TarificationResponse {
  success: boolean;
  data: {
    service_id: number;
    nom: string;
    type_service: ServiceType;
    tarification: Record<string, any>;
  };
  message?: string;
}

export interface TarificationTemplateResponse {
  success: boolean;
  data: {
    type_service: ServiceType;
    template: Record<string, any>;
  };
  message?: string;
}

// Structures de tarification par type de service
export interface RestaurantTarification {
  menus?: Record<string, number>;
  boissons?: Record<string, number>;
  service_table?: number;
  couvert_par_personne?: number;
}

export interface BarTarification {
  alcools?: Record<string, number>;
  soft_drinks?: Record<string, number>;
  cocktails?: Record<string, number>;
  happy_hour?: {
    reduction_pourcentage: number;
    heures: string[];
  };
}

export interface PiscineTarification {
  prix_par_personne?: number;
  prix_par_heure?: number;
  prix_forfaitaire?: number;
  tarifs_age?: Record<string, number>;
  tarifs_duree?: Record<string, number>;
  services_additionnels?: Record<string, number>;
}

export type TarificationData = RestaurantTarification | BarTarification | PiscineTarification | Record<string, any>;

export class ServiceComplexeService {
  private checkAuth() {
    if (!authService.isAuthenticated()) {
      throw new Error('Non authentifié');
    }
  }

  private getComplexeId(): number {
    const user = authService.getCurrentUser();
    if (!user) throw new Error('Utilisateur non authentifié');

    // Si l'utilisateur est un admin de chaîne, on utilise le complexe_id sélectionné
    if (user.role === 'admin_chaine') {
      const selectedComplexeId = localStorage.getItem('selectedComplexeId');
      if (!selectedComplexeId) {
        throw new Error('Aucun complexe sélectionné');
      }
      return parseInt(selectedComplexeId, 10);
    }

    // Pour les autres rôles, on utilise le complexe_id de l'utilisateur
    if (!user.complexe_id) {
      throw new Error('Complexe ID non disponible');
    }
    return user.complexe_id;
  }

  // Récupération de tous les services d'un complexe
  async getAllServices(params?: ServiceParams): Promise<ServiceComplexe[]> {
    this.checkAuth();
    const complexeId = this.getComplexeId();
    
    const response = await api.get<ServiceResponse>('/services', { 
      params: { ...params, complexeId }
    });
    
    if (response.data.success && Array.isArray(response.data.data)) {
      return response.data.data;
    }
    return [];
  }

  // Récupération d'un service par son ID
  async getServiceById(id: number): Promise<ServiceComplexe> {
    this.checkAuth();
    const response = await api.get<ServiceResponse>(`/services/${id}`);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Service non trouvé');
  }

  // Récupération des services par type
  async getServicesByType(type: ServiceType, params?: ServiceParams): Promise<ServiceComplexe[]> {
    this.checkAuth();
    const complexeId = this.getComplexeId();
    
    const response = await api.get<ServiceResponse>(`/services/type/${type}`, { 
      params: { ...params, complexeId }
    });
    
    if (response.data.success && Array.isArray(response.data.data)) {
      return response.data.data;
    }
    return [];
  }

  // Création d'un nouveau service
  async createService(data: CreateServiceParams): Promise<ServiceComplexe> {
    this.checkAuth();
    const complexeId = this.getComplexeId();
    
    const response = await api.post<ServiceResponse>('/services', {
      ...data,
      complexe_id: complexeId
    });
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la création du service');
  }

  // Mise à jour d'un service
  async updateService(id: number, data: UpdateServiceParams): Promise<ServiceComplexe> {
    this.checkAuth();
    const response = await api.put<ServiceResponse>(`/services/${id}`, data);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la mise à jour du service');
  }

  // Suppression d'un service
  async deleteService(id: number): Promise<void> {
    this.checkAuth();
    const response = await api.delete<ServiceResponse>(`/services/${id}`);
    
    if (!response.data.success) {
      throw new Error('Erreur lors de la suppression du service');
    }
  }

  // Activation/désactivation d'un service
  async toggleServiceStatus(id: number, actif: boolean): Promise<ServiceComplexe> {
    return this.updateService(id, { actif });
  }

  // ==================== GESTION DES TARIFICATIONS ====================

  /**
   * Récupérer la tarification d'un service
   */
  async getTarificationByService(serviceId: number): Promise<TarificationResponse['data']> {
    this.checkAuth();
    const response = await api.get<TarificationResponse>(`/services/${serviceId}/tarification`);

    if (response.data.success) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la récupération de la tarification');
  }

  /**
   * Mettre à jour la tarification d'un service
   */
  async updateTarification(serviceId: number, tarificationData: TarificationData): Promise<ServiceComplexe> {
    this.checkAuth();
    const response = await api.put<ServiceResponse>(`/services/${serviceId}/tarification`, tarificationData);

    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la mise à jour de la tarification');
  }

  /**
   * Obtenir un modèle de tarification par défaut selon le type de service
   */
  async getTarificationTemplate(typeService: ServiceType): Promise<TarificationTemplateResponse['data']> {
    this.checkAuth();
    const response = await api.get<TarificationTemplateResponse>(`/services/tarification/template/${typeService}`);

    if (response.data.success) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la récupération du modèle de tarification');
  }

  /**
   * Valider une structure de tarification côté client
   */
  validateTarificationData(typeService: ServiceType, tarificationData: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!tarificationData || typeof tarificationData !== 'object') {
      errors.push('Les données de tarification doivent être un objet valide');
      return { isValid: false, errors };
    }

    // Validation spécifique selon le type de service
    switch (typeService) {
      case 'Restaurant':
        this.validateRestaurantTarification(tarificationData, errors);
        break;
      case 'Bar':
        this.validateBarTarification(tarificationData, errors);
        break;
      case 'Piscine':
        this.validatePiscineTarification(tarificationData, errors);
        break;
      default:
        this.validateBasicTarification(tarificationData, errors);
    }

    return { isValid: errors.length === 0, errors };
  }

  private validateRestaurantTarification(data: any, errors: string[]): void {
    if (data.menus && typeof data.menus === 'object') {
      for (const [nom, prix] of Object.entries(data.menus)) {
        if (typeof prix !== 'number' || prix < 0) {
          errors.push(`Prix invalide pour le menu "${nom}"`);
        }
      }
    }

    if (data.boissons && typeof data.boissons === 'object') {
      for (const [nom, prix] of Object.entries(data.boissons)) {
        if (typeof prix !== 'number' || prix < 0) {
          errors.push(`Prix invalide pour la boisson "${nom}"`);
        }
      }
    }

    if (data.service_table !== undefined && (typeof data.service_table !== 'number' || data.service_table < 0)) {
      errors.push('Le prix du service de table doit être un nombre positif');
    }

    if (data.couvert_par_personne !== undefined && (typeof data.couvert_par_personne !== 'number' || data.couvert_par_personne < 0)) {
      errors.push('Le prix du couvert par personne doit être un nombre positif');
    }
  }

  private validateBarTarification(data: any, errors: string[]): void {
    const categories = ['alcools', 'soft_drinks', 'cocktails'];

    categories.forEach(category => {
      if (data[category] && typeof data[category] === 'object') {
        for (const [nom, prix] of Object.entries(data[category])) {
          if (typeof prix !== 'number' || prix < 0) {
            errors.push(`Prix invalide pour "${nom}" dans la catégorie ${category}`);
          }
        }
      }
    });

    if (data.happy_hour && typeof data.happy_hour === 'object') {
      if (data.happy_hour.reduction_pourcentage !== undefined) {
        if (typeof data.happy_hour.reduction_pourcentage !== 'number' ||
            data.happy_hour.reduction_pourcentage < 0 ||
            data.happy_hour.reduction_pourcentage > 100) {
          errors.push('Le pourcentage de réduction pour happy hour doit être entre 0 et 100');
        }
      }

      if (data.happy_hour.heures && !Array.isArray(data.happy_hour.heures)) {
        errors.push('Les heures de happy hour doivent être un tableau');
      }
    }
  }

  private validatePiscineTarification(data: any, errors: string[]): void {
    const prixFields = ['prix_par_personne', 'prix_par_heure', 'prix_forfaitaire'];

    prixFields.forEach(field => {
      if (data[field] !== undefined && (typeof data[field] !== 'number' || data[field] < 0)) {
        errors.push(`Le ${field.replace('_', ' ')} doit être un nombre positif`);
      }
    });

    if (data.tarifs_age && typeof data.tarifs_age === 'object') {
      for (const [tranche, prix] of Object.entries(data.tarifs_age)) {
        if (typeof prix !== 'number' || prix < 0) {
          errors.push(`Prix invalide pour la tranche d'âge "${tranche}"`);
        }
      }
    }

    if (data.tarifs_duree && typeof data.tarifs_duree === 'object') {
      for (const [duree, prix] of Object.entries(data.tarifs_duree)) {
        if (typeof prix !== 'number' || prix < 0) {
          errors.push(`Prix invalide pour la durée "${duree}"`);
        }
      }
    }
  }

  private validateBasicTarification(data: any, errors: string[]): void {
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'number' && value < 0) {
        errors.push(`Le prix pour "${key}" ne peut pas être négatif`);
      } else if (typeof value === 'object' && value !== null) {
        for (const [subKey, subValue] of Object.entries(value)) {
          if (typeof subValue === 'number' && subValue < 0) {
            errors.push(`Le prix pour "${key}.${subKey}" ne peut pas être négatif`);
          }
        }
      }
    }
  }

  /**
   * Formater les données de tarification pour l'affichage
   */
  formatTarificationForDisplay(typeService: ServiceType, tarification: any): { label: string; value: string }[] {
    if (!tarification || typeof tarification !== 'object') {
      return [];
    }

    const items: { label: string; value: string }[] = [];

    switch (typeService) {
      case 'Restaurant':
        this.addFormattedItems(items, tarification.menus, 'Menus');
        this.addFormattedItems(items, tarification.boissons, 'Boissons');
        if (tarification.service_table) {
          items.push({ label: 'Service de table', value: `${tarification.service_table}FCFA` });
        }
        if (tarification.couvert_par_personne) {
          items.push({ label: 'Couvert par personne', value: `${tarification.couvert_par_personne}FCFA` });
        }
        break;

      case 'Bar':
        this.addFormattedItems(items, tarification.alcools, 'Alcools');
        this.addFormattedItems(items, tarification.soft_drinks, 'Boissons sans alcool');
        this.addFormattedItems(items, tarification.cocktails, 'Cocktails');
        if (tarification.happy_hour) {
          items.push({
            label: 'Happy Hour',
            value: `${tarification.happy_hour.reduction_pourcentage}% (${tarification.happy_hour.heures?.join(', ') || ''})`
          });
        }
        break;

      case 'Piscine':
        if (tarification.prix_par_personne) {
          items.push({ label: 'Prix par personne', value: `${tarification.prix_par_personne}FCFA` });
        }
        if (tarification.prix_par_heure) {
          items.push({ label: 'Prix par heure', value: `${tarification.prix_par_heure}FCFA` });
        }
        if (tarification.prix_forfaitaire) {
          items.push({ label: 'Prix forfaitaire', value: `${tarification.prix_forfaitaire}FCFA` });
        }
        this.addFormattedItems(items, tarification.tarifs_age, 'Tarifs par âge');
        this.addFormattedItems(items, tarification.tarifs_duree, 'Tarifs par durée');
        break;

      default:
        // Affichage générique
        for (const [key, value] of Object.entries(tarification)) {
          if (typeof value === 'number') {
            items.push({ label: key.replace('_', ' '), value: `${value}FCFA` });
          } else if (typeof value === 'object' && value !== null) {
            this.addFormattedItems(items, value, key.replace('_', ' '));
          }
        }
    }

    return items;
  }

  private addFormattedItems(items: { label: string; value: string }[], data: any, category: string): void {
    if (data && typeof data === 'object') {
      for (const [nom, prix] of Object.entries(data)) {
        if (typeof prix === 'number') {
          items.push({ label: `${category} - ${nom}`, value: `${prix}FCFA` });
        }
      }
    }
  }
}

export const serviceComplexeService = new ServiceComplexeService();
