import api from './api.config';

export interface Disponibilite {
  disponibilite_id: string;
  chambre_id: string;
  date_debut: string;
  date_fin: string;
  statut: 'disponible' | 'bloquee' | 'expiree';
  raison?: string;
  utilisateur_id?: string;
  created_at: string;
  updated_at?: string;
  duree?: number;
}

export interface DisponibiliteDetails extends Disponibilite {
  chambre: {
    type_chambre: string;
    capacite: number;
    prix_nuit: number;
    description?: string;
  };
  statut_actuel: 'disponible' | 'bloquee' | 'expiree';
}

export interface DisponibiliteResponse {
  success: boolean;
  data: {
    disponibilites: Array<{
      type_chambre: string;
      total: string;
      disponibles: string;
      chambres: Array<{
        chambre_id: number;
        numero: string;
        type_chambre: string;
        statut: string;
        capacite: number;
        prix_base: number;
        description: string;
        disponible: boolean;
        statut_disponibilite: string;
        plages_occupation: Array<{
          heure_debut: string;
          heure_fin: string;
          statut: string;
          type_reservation: 'CONFIRMEE' | 'DEMANDE';
          reservation_id?: number;
        }>;
      }>;
    }>;
    date_debut: string;
    date_fin: string;
  };
}

export interface DisponibiliteParams {
  date_debut?: string;
  date_fin?: string;
  type_chambre?: string;
  chambre_id?: string;
  statut?: Disponibilite['statut'];
}

export interface VerifierDisponibiliteParams {
  date_debut: string;
  date_fin: string;
  type_chambre?: string;
  nombre_chambres?: number;
}

export interface CreateDisponibiliteParams {
  chambre_id: string;
  date_debut: string;
  date_fin: string;
  statut: Disponibilite['statut'];
  raison: string;
  utilisateur_id: string;
}

export interface UpdateDisponibiliteParams {
  date_debut?: string;
  date_fin?: string;
  statut?: Disponibilite['statut'];
  raison?: string;
  utilisateur_id?: string;
}

export interface DisponibiliteVerificationResponse {
  disponibilites: {
    type_chambre: string;
    total: number;
    disponibles: number;
  }[];
  date_debut: string;
  date_fin: string;
  nombre_chambres: number;
  message?: string;
  suggestions?: {
    type_chambre: string;
    dates_alternatives: string[];
  }[];
}

class DisponibiliteService {
  // Récupération de la liste des disponibilités
  async getDisponibilites(params: DisponibiliteParams): Promise<DisponibiliteResponse> {
    const response = await api.get<DisponibiliteResponse>('/disponibilites', { params });
    return response.data;
  }

  // Vérification des disponibilités (correspond à la route GET /verification)
  async verifierDisponibilite(params: VerifierDisponibiliteParams): Promise<DisponibiliteVerificationResponse> {
    const response = await api.get<DisponibiliteVerificationResponse>('/disponibilites/verification', { params });
    return response.data;
  }

  // Création d'une disponibilité
  async createDisponibilite(params: CreateDisponibiliteParams): Promise<Disponibilite> {
    const response = await api.post<Disponibilite>('/disponibilites', params);
    return response.data;
  }

  // Mise à jour d'une disponibilité
  async updateDisponibilite(id: string, params: UpdateDisponibiliteParams): Promise<Disponibilite> {
    const response = await api.put<Disponibilite>(`/disponibilites/${id}`, params);
    return response.data;
  }

  // Méthodes utilitaires
  async verifierDisponibiliteRapide(date_debut: string, date_fin: string, type_chambre?: string): Promise<boolean> {
    try {
      const result = await this.verifierDisponibilite({
        date_debut,
        date_fin,
        type_chambre,
        nombre_chambres: 1
      });

      if (type_chambre) {
        const typeDisponible = result.disponibilites.find(d => d.type_chambre === type_chambre);
        return typeDisponible ? typeDisponible.disponibles > 0 : false;
      }

      return result.disponibilites.some(d => d.disponibles > 0);
    } catch (error) {
      console.error('Erreur lors de la vérification de disponibilité:', error);
      return false;
    }
  }

  async getDisponibilitesParType(type_chambre: string, params?: Omit<DisponibiliteParams, 'type_chambre'>): Promise<DisponibiliteResponse> {
    return this.getDisponibilites({
      ...params,
      type_chambre
    });
  }

  // Récupération des disponibilités d'une chambre spécifique
  async getDisponibilitesChambre(chambre_id: string, params?: Omit<DisponibiliteParams, 'chambre_id'>): Promise<DisponibiliteResponse> {
    return this.getDisponibilites({
      ...params,
      chambre_id
    });
  }

  async bloquerChambre(chambre_id: string, date_debut: string, date_fin: string, raison: string, utilisateur_id: string): Promise<Disponibilite> {
    return this.createDisponibilite({
      chambre_id,
      date_debut,
      date_fin,
      statut: 'bloquee',
      raison,
      utilisateur_id
    });
  }

  async libererChambre(disponibilite_id: string, utilisateur_id: string): Promise<Disponibilite> {
    return this.updateDisponibilite(disponibilite_id, {
      statut: 'disponible',
      raison: 'Libération manuelle',
      utilisateur_id
    });
  }
}

export const disponibiliteService = new DisponibiliteService(); 