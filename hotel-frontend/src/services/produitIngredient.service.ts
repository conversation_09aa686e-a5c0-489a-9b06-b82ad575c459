import api from './api.config';
import type {
  BaseApiResponse,
  Ingredient
} from '../types';

/**
 * Interface pour un lien produit-ingrédient
 */
export interface ProduitIngredient {
  produit_ingredient_id: number;
  produit_id: number;
  ingredient_id: number;
  quantite_necessaire: number;
  unite_mesure: string;
  cout_unitaire: number;
  cout_total: number;
  optionnel: boolean;
  notes?: string;
  created_at: string;
  updated_at?: string;
  // Données jointes
  ingredient_nom?: string;
  ingredient_categorie?: string;
  ingredient_stock_actuel?: number;
  ingredient_stock_minimal?: number;
}

/**
 * Interface pour un produit avec ses ingrédients
 */
export interface ProduitAvecIngredients {
  produit_id: number;
  produit_nom: string;
  description?: string;
  prix_vente_defaut: number;
  cout_ingredients: number;
  nombre_ingredients: number;
  cout_total_ingredients: number;
  ingredients?: ProduitIngredient[];
}

/**
 * Interface pour la vérification de disponibilité
 */
export interface DisponibiliteProduit {
  produit_id: number;
  quantite_demandee: number;
  disponible: boolean;
  ingredients: Array<{
    ingredient_id: number;
    ingredient_nom: string;
    quantite_necessaire: number;
    quantite_totale_necessaire: number;
    stock_actuel: number;
    unite_mesure: string;
    disponible: boolean;
  }>;
  ingredients_indisponibles: Array<{
    ingredient_id: number;
    ingredient_nom: string;
    quantite_necessaire: number;
    stock_actuel: number;
  }>;
}

/**
 * Interface pour les options d'ajout d'ingrédient
 */
export interface OptionsIngredient {
  coutUnitaire?: number;
  optionnel?: boolean;
  notes?: string;
}

/**
 * Types de réponses API
 */
interface ProduitIngredientResponse extends BaseApiResponse {
  data: ProduitIngredient | ProduitIngredient[];
}

interface ProduitsAvecIngredientsResponse extends BaseApiResponse {
  data: ProduitAvecIngredients | ProduitAvecIngredients[];
}

interface DisponibiliteResponse extends BaseApiResponse {
  data: DisponibiliteProduit;
}

interface CoutCalculeResponse extends BaseApiResponse {
  data: {
    produit_id: number;
    cout_total: number;
    cout_ingredients: number;
    marge_calculee: number;
  };
}

/**
 * Service pour la gestion des liens produits-ingrédients
 */
class ProduitIngredientService {
  private baseURL = 'produit-ingredient';

  /**
   * Ajouter un ingrédient à un produit
   */
  async ajouterIngredientProduit(
    produitId: number,
    ingredientId: number,
    quantiteNecessaire: number,
    uniteMesure: string,
    options: OptionsIngredient = {}
  ): Promise<ProduitIngredient> {
    try {
      const response = await api.post<ProduitIngredientResponse>(`${this.baseURL}/add`, {
        produitId,
        ingredientId,
        quantiteNecessaire,
        uniteMesure,
        options
      });

      if (response.data.success && !Array.isArray(response.data.data)) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Erreur lors de l\'ajout de l\'ingrédient au produit');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de l\'ajout de l\'ingrédient au produit');
    }
  }

  /**
   * Supprimer un ingrédient d'un produit
   */
  async supprimerIngredientProduit(produitId: number, ingredientId: number): Promise<void> {
    try {
      const response = await api.delete(`${this.baseURL}/${produitId}/${ingredientId}`);

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la suppression de l\'ingrédient du produit');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la suppression de l\'ingrédient du produit');
    }
  }

  /**
   * Mettre à jour un lien produit-ingrédient
   */
  async updateIngredientProduit(
    produitId: number,
    ingredientId: number,
    updateData: Partial<ProduitIngredient>
  ): Promise<ProduitIngredient> {
    try {
      const response = await api.put<ProduitIngredientResponse>(
        `${this.baseURL}/${produitId}/${ingredientId}`,
        updateData
      );

      if (response.data.success && !Array.isArray(response.data.data)) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Erreur lors de la mise à jour du lien produit-ingrédient');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la mise à jour du lien produit-ingrédient');
    }
  }

  /**
   * Récupérer les ingrédients d'un produit
   */
  async getIngredientsProduit(produitId: number): Promise<ProduitIngredient[]> {
    try {
      const response = await api.get<ProduitIngredientResponse>(`${this.baseURL}/produit/${produitId}`);

      if (response.data.success) {
        return Array.isArray(response.data.data) ? response.data.data : [response.data.data];
      }
      
      throw new Error(response.data.message || 'Erreur lors de la récupération des ingrédients du produit');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des ingrédients du produit');
    }
  }

  /**
   * Récupérer tous les produits avec leurs ingrédients pour un complexe
   */
  async getProduitsAvecIngredients(complexeId: number, serviceId?: number): Promise<ProduitAvecIngredients[]> {
    try {
      const params = serviceId ? { serviceId } : {};
      const response = await api.get<ProduitsAvecIngredientsResponse>(
        `${this.baseURL}/complexe/${complexeId}`,
        { params }
      );

      if (response.data.success) {
        return Array.isArray(response.data.data) ? response.data.data : [response.data.data];
      }
      
      throw new Error(response.data.message || 'Erreur lors de la récupération des produits avec ingrédients');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des produits avec ingrédients');
    }
  }

  /**
   * Vérifier la disponibilité des ingrédients pour un produit
   */
  async verifierDisponibiliteProduit(produitId: number, quantite: number = 1): Promise<DisponibiliteProduit> {
    try {
      const response = await api.post<DisponibiliteResponse>(`${this.baseURL}/check-availability`, {
        produitId,
        quantite
      });

      if (response.data.success && !Array.isArray(response.data.data)) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Erreur lors de la vérification de disponibilité');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la vérification de disponibilité');
    }
  }

  /**
   * Calculer le coût d'un produit
   */
  async calculerCoutProduit(produitId: number): Promise<{ cout_total: number; cout_ingredients: number; marge_calculee: number }> {
    try {
      const response = await api.post<CoutCalculeResponse>(`${this.baseURL}/calculate-cost/${produitId}`);

      if (response.data.success && !Array.isArray(response.data.data)) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Erreur lors du calcul du coût du produit');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors du calcul du coût du produit');
    }
  }

  /**
   * Import en masse des liens produits-ingrédients
   */
  async importProduitsIngredients(complexeId: number, excelData: any[]): Promise<any> {
    try {
      const response = await api.post(`${this.baseURL}/import/${complexeId}`, {
        excelData
      });

      if (response.data.success) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Erreur lors de l\'import des liens produits-ingrédients');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de l\'import des liens produits-ingrédients');
    }
  }

  /**
   * Valider des données d'import sans les sauvegarder
   */
  async validateImportData(excelData: any[]): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
    rowsCount: number;
  }> {
    try {
      const response = await api.post(`${this.baseURL}/validate-import`, {
        excelData
      });

      if (response.data.success) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Erreur lors de la validation des données');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la validation des données');
    }
  }
}

export const produitIngredientService = new ProduitIngredientService();
export default produitIngredientService;
