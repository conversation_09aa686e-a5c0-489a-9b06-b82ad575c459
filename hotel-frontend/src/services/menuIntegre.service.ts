import api from './api.config';
import { produitIngredientService } from './produitIngredient.service';
import type {
  BaseApiResponse,
  ProduitAvecIngredients,
  DisponibiliteProduit
} from './produitIngredient.service';

/**
 * Interface pour un produit de menu avec informations de stock
 */
export interface ProduitMenu {
  produit_id: number;
  nom: string;
  description?: string;
  prix_vente_defaut: number;
  cout_ingredients: number;
  marge_calculee: number;
  marge_pourcentage: number;
  temps_preparation?: number;
  allergenes?: string;
  image_url?: string;
  actif: boolean;
  disponible: boolean;
  stock_status: 'disponible' | 'stock_faible' | 'rupture';
  ingredients_manquants: string[];
  created_at: string;
  updated_at?: string;
}

/**
 * Interface pour les statistiques de menu
 */
export interface MenuStats {
  total_produits: number;
  produits_disponibles: number;
  produits_rupture: number;
  cout_moyen: number;
  marge_moyenne: number;
  chiffre_affaires_potentiel: number;
}

/**
 * Interface pour les alertes de menu
 */
export interface MenuAlert {
  type: 'rupture' | 'stock_faible' | 'cout_eleve' | 'marge_faible';
  produit_id: number;
  produit_nom: string;
  message: string;
  severity: 'low' | 'medium' | 'high';
  ingredients_concernes?: string[];
}

/**
 * Interface pour l'optimisation des prix
 */
export interface OptimisationPrix {
  produit_id: number;
  prix_actuel: number;
  prix_suggere: number;
  marge_actuelle: number;
  marge_cible: number;
  justification: string;
}

/**
 * Types de réponses API
 */
interface MenuResponse extends BaseApiResponse {
  data: ProduitMenu | ProduitMenu[];
}

interface MenuStatsResponse extends BaseApiResponse {
  data: MenuStats;
}

interface MenuAlertsResponse extends BaseApiResponse {
  data: MenuAlert[];
}

interface OptimisationResponse extends BaseApiResponse {
  data: OptimisationPrix[];
}

/**
 * Service pour la gestion complète du menu avec stocks intégrés
 */
class MenuIntegreService {
  private baseURL = 'menu';

  /**
   * Récupérer le menu complet d'un service avec statuts de stock
   */
  async getMenuAvecStocks(serviceId: number): Promise<ProduitMenu[]> {
    try {
      const response = await api.get<MenuResponse>(`${this.baseURL}/service/${serviceId}/with-stocks`);

      if (response.data.success) {
        return Array.isArray(response.data.data) ? response.data.data : [response.data.data];
      }
      
      throw new Error(response.data.message || 'Erreur lors de la récupération du menu avec stocks');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération du menu avec stocks');
    }
  }

  /**
   * Vérifier la disponibilité de plusieurs produits
   */
  async verifierDisponibiliteMenu(serviceId: number): Promise<{
    produits_disponibles: ProduitMenu[];
    produits_indisponibles: ProduitMenu[];
    alertes: MenuAlert[];
  }> {
    try {
      // Récupérer tous les produits du service
      const produits = await produitIngredientService.getProduitsAvecIngredients(0, serviceId);
      
      const produitsDisponibles: ProduitMenu[] = [];
      const produitsIndisponibles: ProduitMenu[] = [];
      const alertes: MenuAlert[] = [];

      for (const produit of produits) {
        try {
          const disponibilite = await produitIngredientService.verifierDisponibiliteProduit(produit.produit_id, 1);
          
          const produitMenu: ProduitMenu = {
            produit_id: produit.produit_id,
            nom: produit.produit_nom,
            description: produit.description,
            prix_vente_defaut: produit.prix_vente_defaut,
            cout_ingredients: produit.cout_ingredients,
            marge_calculee: produit.prix_vente_defaut - produit.cout_ingredients,
            marge_pourcentage: produit.prix_vente_defaut > 0 ? 
              ((produit.prix_vente_defaut - produit.cout_ingredients) / produit.prix_vente_defaut) * 100 : 0,
            actif: true,
            disponible: disponibilite.disponible,
            stock_status: this.determinerStatutStock(disponibilite),
            ingredients_manquants: disponibilite.ingredients_indisponibles.map(ing => ing.ingredient_nom),
            created_at: new Date().toISOString(),
          };

          if (disponibilite.disponible) {
            produitsDisponibles.push(produitMenu);
          } else {
            produitsIndisponibles.push(produitMenu);
            
            // Créer une alerte pour les produits indisponibles
            alertes.push({
              type: 'rupture',
              produit_id: produit.produit_id,
              produit_nom: produit.produit_nom,
              message: `Produit indisponible - Ingrédients manquants: ${produitMenu.ingredients_manquants.join(', ')}`,
              severity: 'high',
              ingredients_concernes: produitMenu.ingredients_manquants
            });
          }
        } catch (error) {
          console.error(`Erreur lors de la vérification du produit ${produit.produit_id}:`, error);
        }
      }

      return {
        produits_disponibles: produitsDisponibles,
        produits_indisponibles: produitsIndisponibles,
        alertes
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la vérification de disponibilité du menu');
    }
  }

  /**
   * Calculer les statistiques du menu
   */
  async getMenuStats(serviceId: number): Promise<MenuStats> {
    try {
      const { produits_disponibles, produits_indisponibles } = await this.verifierDisponibiliteMenu(serviceId);
      
      const totalProduits = produits_disponibles.length + produits_indisponibles.length;
      const coutMoyen = totalProduits > 0 ? 
        [...produits_disponibles, ...produits_indisponibles]
          .reduce((sum, p) => sum + p.cout_ingredients, 0) / totalProduits : 0;
      
      const margeMoyenne = totalProduits > 0 ?
        [...produits_disponibles, ...produits_indisponibles]
          .reduce((sum, p) => sum + p.marge_pourcentage, 0) / totalProduits : 0;

      const chiffreAffairesPotentiel = produits_disponibles
        .reduce((sum, p) => sum + p.prix_vente_defaut, 0);

      return {
        total_produits: totalProduits,
        produits_disponibles: produits_disponibles.length,
        produits_rupture: produits_indisponibles.length,
        cout_moyen: coutMoyen,
        marge_moyenne: margeMoyenne,
        chiffre_affaires_potentiel: chiffreAffairesPotentiel
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors du calcul des statistiques du menu');
    }
  }

  /**
   * Récupérer les alertes du menu
   */
  async getMenuAlerts(serviceId: number): Promise<MenuAlert[]> {
    try {
      const { alertes } = await this.verifierDisponibiliteMenu(serviceId);
      return alertes;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des alertes du menu');
    }
  }

  /**
   * Optimiser les prix du menu
   */
  async optimiserPrixMenu(serviceId: number, margeCible: number = 30): Promise<OptimisationPrix[]> {
    try {
      const produits = await produitIngredientService.getProduitsAvecIngredients(0, serviceId);
      const optimisations: OptimisationPrix[] = [];

      for (const produit of produits) {
        const margeActuelle = produit.prix_vente_defaut > 0 ? 
          ((produit.prix_vente_defaut - produit.cout_ingredients) / produit.prix_vente_defaut) * 100 : 0;

        if (Math.abs(margeActuelle - margeCible) > 5) { // Si l'écart est > 5%
          const prixSuggere = produit.cout_ingredients / (1 - margeCible / 100);
          
          optimisations.push({
            produit_id: produit.produit_id,
            prix_actuel: produit.prix_vente_defaut,
            prix_suggere: Math.round(prixSuggere * 100) / 100,
            marge_actuelle: margeActuelle,
            marge_cible: margeCible,
            justification: margeActuelle < margeCible ? 
              'Marge trop faible, augmentation recommandée' : 
              'Marge trop élevée, réduction possible'
          });
        }
      }

      return optimisations;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de l\'optimisation des prix');
    }
  }

  /**
   * Mettre à jour le prix d'un produit
   */
  async updatePrixProduit(produitId: number, serviceId: number, nouveauPrix: number): Promise<void> {
    try {
      const response = await api.put(`${this.baseURL}/service/${serviceId}/product/${produitId}/price`, {
        prix: nouveauPrix
      });

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la mise à jour du prix');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la mise à jour du prix');
    }
  }

  /**
   * Déterminer le statut de stock d'un produit
   */
  private determinerStatutStock(disponibilite: DisponibiliteProduit): 'disponible' | 'stock_faible' | 'rupture' {
    if (!disponibilite.disponible) {
      return 'rupture';
    }

    // Vérifier si certains ingrédients sont en stock faible
    const stockFaible = disponibilite.ingredients.some(ing => {
      // Considérer comme stock faible si moins de 20% du stock nécessaire
      return ing.stock_actuel < (ing.quantite_totale_necessaire * 5);
    });

    return stockFaible ? 'stock_faible' : 'disponible';
  }

  /**
   * Rechercher des produits par nom ou ingrédient
   */
  async rechercherProduits(serviceId: number, query: string): Promise<ProduitMenu[]> {
    try {
      const produits = await this.getMenuAvecStocks(serviceId);
      
      const queryLower = query.toLowerCase();
      return produits.filter(produit => 
        produit.nom.toLowerCase().includes(queryLower) ||
        produit.description?.toLowerCase().includes(queryLower) ||
        produit.ingredients_manquants.some(ing => ing.toLowerCase().includes(queryLower))
      );
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la recherche de produits');
    }
  }
}

export const menuIntegreService = new MenuIntegreService();
export default menuIntegreService;
