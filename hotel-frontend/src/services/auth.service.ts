import api from './api.config';

export interface LoginCredentials {
  email: string;
  password: string;
  userType: 'admin_chaine' | 'admin_complexe' | 'employe';
  complexe_id?: number;
}

export interface User {
  id: number;
  email: string;
  role: 'super_admin' | 'admin_chaine' | 'admin_complexe' | 'employe';
  type_employe?: 'reception' | 'gerant_piscine' | 'serveuse' | 'gerant_services' | 'cuisine';
  services_autorises?: string[]; // ['bar', 'restaurant', 'piscine']
  nom?: string;
  prenom?: string;
  chaine_id?: number;
  complexe_id?: number;
  employe_id?: number; // Pour les employés
}

export interface AuthResponse {
  success: boolean;
  token?: string;
  user?: User;
  message?: string;
}

class AuthService {
  private static instance: AuthService;

  private constructor() {}

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  private cleanToken(token: string): string {
    return token.replace(/^Bearer\s+/i, '');
  }

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      console.log('Sending login request with credentials:', credentials);
      const response = await api.post<AuthResponse>('/auth/login', credentials);
      console.log('Received login response:', response.data);

      const { token, user } = response.data;

      if (token) {
        const cleanTokenValue = this.cleanToken(token);
        localStorage.setItem('token', cleanTokenValue);
        if (user) {
          console.log('Storing user data:', user);
          localStorage.setItem('user', JSON.stringify(user));
        }
      }

      return {
        success: true,
        token,
        user
      };
    } catch (error: any) {
      console.error('Login error:', error.response?.data || error);
      return {
        success: false,
        message: error.response?.data?.message || 'Une erreur est survenue lors de la connexion'
      };
    }
  }

  logout(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  }

  isAuthenticated(): boolean {
    return !!localStorage.getItem('token');
  }

  getToken(): string | null {
    return localStorage.getItem('token');
  }

  getCurrentUser(): User | null {
    const userStr = localStorage.getItem('user');
    if (!userStr) return null;
    
    try {
      const user = JSON.parse(userStr);
      console.log('Retrieved user from localStorage:', user);
      return user;
    } catch (error) {
      console.error('Error parsing user from localStorage:', error);
      return null;
    }
  }

  getChaineId(): number | undefined {
    const user = this.getCurrentUser();
    console.log('Getting chaine_id from user:', user);
    return user?.chaine_id;
  }

  getComplexeId(): number | undefined {
    const user = this.getCurrentUser();
    console.log('Getting complexe_id from user:', user);

    // Si l'utilisateur est un admin de chaîne, on utilise le complexe_id sélectionné
    if (user?.role === 'admin_chaine') {
      const selectedComplexeId = localStorage.getItem('selectedComplexeId');
      if (selectedComplexeId) {
        return parseInt(selectedComplexeId, 10);
      }
      return undefined; // Pas de complexe sélectionné
    }

    // Pour les autres rôles, on utilise le complexe_id du token
    return user?.complexe_id;
  }

  /**
   * Vérifie si un complexe est sélectionné/disponible
   */
  hasComplexeAccess(): boolean {
    const complexeId = this.getComplexeId();
    return complexeId !== undefined && complexeId !== null;
  }

  /**
   * Vérifie si l'utilisateur doit sélectionner un complexe
   */
  needsComplexeSelection(): boolean {
    const user = this.getCurrentUser();
    if (!user) return false;

    // Admin de chaîne doit sélectionner un complexe
    if (user.role === 'admin_chaine') {
      const selectedComplexeId = localStorage.getItem('selectedComplexeId');
      return !selectedComplexeId;
    }

    // Autres rôles n'ont pas besoin de sélectionner
    return false;
  }

  isSuperAdmin(): boolean {
    const user = this.getCurrentUser();
    return user?.role === 'super_admin';
  }

  // ===== NOUVELLES MÉTHODES POUR LE SYSTÈME SIMPLIFIÉ =====

  /**
   * Vérifie si l'utilisateur est un administrateur (tous types)
   */
  isAdmin(): boolean {
    const user = this.getCurrentUser();
    return ['super_admin', 'admin_chaine', 'admin_complexe'].includes(user?.role || '');
  }

  /**
   * Vérifie si l'utilisateur est un employé
   */
  isEmployee(): boolean {
    const user = this.getCurrentUser();
    return user?.role === 'employe';
  }

  /**
   * Récupère le type d'employé
   */
  getEmployeeType(): string | null {
    const user = this.getCurrentUser();
    return user?.type_employe || null;
  }

  /**
   * Récupère les services autorisés pour l'employé
   */
  getAuthorizedServices(): string[] {
    const user = this.getCurrentUser();
    return user?.services_autorises || [];
  }

  /**
   * Vérifie si l'utilisateur peut accéder aux fonctionnalités admin
   */
  canAccessAdminFeatures(): boolean {
    return this.isAdmin();
  }

  /**
   * Vérifie si l'utilisateur peut accéder aux rapports
   */
  canAccessReports(): boolean {
    return this.isAdmin();
  }

  /**
   * Vérifie si l'utilisateur peut accéder à la configuration
   */
  canAccessConfiguration(): boolean {
    return this.isAdmin();
  }

  /**
   * Vérifie si l'employé peut accéder à un service spécifique
   */
  canAccessService(serviceType: string): boolean {
    if (this.isAdmin()) {
      return true; // Les admins ont accès à tout
    }

    if (!this.isEmployee()) {
      return false;
    }

    const authorizedServices = this.getAuthorizedServices();
    return authorizedServices.includes(serviceType.toLowerCase());
  }

  /**
   * Vérifie si l'employé a un type spécifique
   */
  hasEmployeeType(type: string): boolean {
    if (this.isAdmin()) {
      return true; // Les admins peuvent tout faire
    }

    return this.getEmployeeType() === type;
  }

  /**
   * Récupère le nom complet de l'utilisateur
   */
  getFullName(): string {
    const user = this.getCurrentUser();
    if (!user) return '';

    const nom = user.nom || '';
    const prenom = user.prenom || '';
    return `${prenom} ${nom}`.trim() || user.email;
  }

  /**
   * Récupère le type d'utilisateur pour l'affichage
   */
  getUserTypeLabel(): string {
    const user = this.getCurrentUser();
    if (!user) return '';

    switch (user.role) {
      case 'super_admin':
        return 'Super Administrateur';
      case 'admin_chaine':
        return 'Administrateur Chaîne';
      case 'admin_complexe':
        return 'Administrateur Complexe';
      case 'employe':
        return this.getEmployeeTypeLabel();
      default:
        return 'Utilisateur';
    }
  }

  /**
   * Récupère le libellé du type d'employé
   */
  getEmployeeTypeLabel(): string {
    const type = this.getEmployeeType();
    if (!type) return 'Employé';

    switch (type) {
      case 'reception':
        return 'Employé Réception';
      case 'gerant_piscine':
        return 'Gérant Piscine';
      case 'serveuse':
        return 'Serveuse';
      case 'gerant_services':
        return 'Gérant Services';
      case 'cuisine':
        return 'Employé Cuisine';
      default:
        return 'Employé';
    }
  }

  isAdminChaine(): boolean {
    const user = this.getCurrentUser();
    return user?.role === 'admin_chaine';
  }

  isAdminComplexe(): boolean {
    const user = this.getCurrentUser();
    return user?.role === 'admin_complexe';
  }

  isEmploye(): boolean {
    const user = this.getCurrentUser();
    return user?.role === 'employe';
  }
}

export const authService = AuthService.getInstance(); 