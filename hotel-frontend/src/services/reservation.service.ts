import api from './api.config';
import axios from 'axios';

export interface Reservation {
  reservation_id: string;
  numero_reservation: string;
  client_id: string;
  date_arrivee: string;
  date_depart: string;
  heure_debut?: string;
  heure_fin?: string;
  statut: 'en_attente' | 'confirmee' | 'annulee' | 'terminee' | 'payee' | 'expiree';
  type_reservation: 'heure';
  montant_total: number;
  commentaires?: string;
  qr_code?: string;
  acompte_verse?: string;
  code_promo_id?: string | null;
  date_confirmation?: string | null;
  employe_id?: string | null;
  created_at?: string;
  updated_at?: string | null;
  reduction_appliquee?: string;
  client_nom?: string;
  client_prenom?: string;
  client_email?: string;
  client_telephone?: string;
  // Nouveaux champs pour les réservations anonymes
  est_anonyme?: boolean;
  code_acces_anonyme?: string;
  pseudonyme?: string;
  client?: {
    nom: string;
    prenom: string;
    email: string;
    telephone: string;
    // Champs pour les clients anonymes
    est_anonyme?: boolean;
    pseudonyme?: string;
  };
  chambres?: Array<{
    chambre_id: string;
    numero: string;
    type_chambre: string;
    prix_nuit: number;
  }>;
}

export interface ReservationDetails extends Reservation {
  client: {
    nom: string;
    prenom: string;
    email: string;
    telephone: string;
  };
  chambres: Array<{
    chambre_id: string;
    numero: string;
    type_chambre: string;
    prix_nuit: number;
  }>;
  montant_paye: number;
  montant_restant: number;
  duree_sejour: number;
  statut_libelle: string;
  numero_ticket?: string;
}

export interface ReservationParams {
  date_debut?: string;
  date_fin?: string;
  type_chambre?: string;
  tri?: 'created_at' | 'date_arrivee' | 'date_depart';
}

export interface CreateReservationParams {
  client_info: {
    nom: string;
    prenom: string;
    telephone: string;
  };
  complexe_id: number;
  date_arrivee: string;
  date_depart: string;
  heure_debut: string;
  heure_fin: string;
  chambres: Array<{
    chambre_id: string;
    type_chambre: string;
    prix_nuit: number;
  }>;
  prix_total: number;
  commentaires?: string;
}

export interface PaiementParams {
  montant: number;
  mode_paiement: 'especes' | 'carte' | 'virement' | 'cheque' | 'mobile';
  reference_paiement?: string;
}

export interface PaiementReservation {
  paiement_reservation_id: number;
  montant: number;
  mode_paiement: 'especes' | 'carte' | 'virement' | 'cheque' | 'mobile';
  reference_paiement: string;
  date_paiement: string;
  statut: 'Validé' | 'Annulé' | 'Remboursé';
  notes?: string;
  utilisateur_id?: number;
  type_utilisateur: 'employe' | 'admin_complexe' | 'admin_chaine';
  created_at: string;
  updated_at?: string;
}

export interface Client {
  id: string;
  nom: string;
  email: string;
  telephone: string;
}

export interface Chambre {
  id: string;
  numero: string;
  type: string;
}

export interface CalendarEvent {
  id: string;
  title: string;
  start: string;
  end: string;
  client?: Client;
  chambre?: Chambre;
  montant: number;
  statut: 'en_attente' | 'confirmee' | 'terminee' | 'annulee';
}

export interface CalendrierParams {
  date_debut: string;
  date_fin: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}


  // Mise à jour de l'interface pour inclure les informations de paiement
  export interface ConfirmReservationParams {
    chambres_ids: string[];
    paiement?: {
      montant: number;
      mode_paiement: 'especes' | 'carte' | 'virement' | 'cheque' | 'mobile';
      reference_paiement?: string;
    };
  }

class ReservationService {


  // Création d'une réservation (route réception)
  async createReservation(params: CreateReservationParams): Promise<ApiResponse<Reservation>> {
    try {
      console.log('Création de réservation (réception):', params);

      const formattedParams = {
        ...params,
        date_arrivee: params.date_arrivee.split('T')[0],
        date_depart: params.date_depart.split('T')[0]
      };

      const response = await api.post<ApiResponse<Reservation>>('/reservations', formattedParams);
      console.log('Réponse du serveur:', response.data);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la création de la réservation:', error);
      if (axios.isAxiosError(error)) {
        if (error.response) {
          throw new Error(error.response.data.message || 'Erreur lors de la création de la réservation');
        } else if (error.request) {
          throw new Error('Aucune réponse du serveur. Veuillez vérifier votre connexion internet.');
        }
      }
      throw new Error('Erreur lors de la configuration de la requête');
    }
  }

  // Création d'une demande de réservation
  async createDemandeReservation(params: CreateReservationParams): Promise<ApiResponse<Reservation>> {
    try {
      console.log('Envoi de la demande de réservation au serveur:', params);
      
      // S'assurer que les dates sont au format YYYY-MM-DD
      const formattedParams = {
        ...params,
        date_arrivee: params.date_arrivee.split('T')[0],
        date_depart: params.date_depart.split('T')[0]
      };
      
      const response = await api.post<ApiResponse<Reservation>>('/reservations/demande', formattedParams);
      console.log('Réponse du serveur:', response.data);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la création de la demande de réservation:', error);
      if (axios.isAxiosError(error)) {
        if (error.response) {
          console.error('Données de réponse:', error.response.data);
          console.error('Statut de réponse:', error.response.status);
          throw new Error(error.response.data.message || 'Erreur lors de la création de la demande de réservation');
        } else if (error.request) {
          console.error('Aucune réponse reçue:', error.request);
          throw new Error('Aucune réponse du serveur. Veuillez vérifier votre connexion internet.');
        }
      }
      throw new Error('Erreur lors de la configuration de la requête');
    }
  }

  // Vérification d'une réservation
  async verifierReservation(numero: string): Promise<Reservation> {
    const response = await api.get(`/reservations/client/verifier/${numero}`);
    return response.data;
  }

  // Récupération d'une réservation
  async getReservationById(id: string): Promise<ReservationDetails> {
    const response = await api.get(`/reservations/${id}`);
    return response.data;
  }

  // Récupération des chambres d'une réservation
  async getChambresReservation(reservationId: string): Promise<{
    success: boolean;
    data: Array<{
      chambre_reservee_id: number;
      chambre_id: string;
      numero: string;
      type_chambre: string;
      prix_nuit: number;
      date_debut: string;
      date_fin: string;
      heure_debut?: string;
      heure_fin?: string;
      statut: string;
      type_occupation: string;
      capacite: number;
      description?: string;
      etage?: number;
    }>;
    message?: string;
  }> {
    const response = await api.get(`/reservations/${reservationId}/chambres`);
    return response.data;
  }

  // Récupération des paiements d'une réservation
  async getPaiementsReservation(reservationId: string): Promise<{
    success: boolean;
    data: {
      paiements: PaiementReservation[];
      total_paye: number;
      nombre_paiements: number;
    };
    message?: string;
  }> {
    try {
      const response = await api.get(`/reservations/${reservationId}/paiements`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des paiements:', error);
      if (axios.isAxiosError(error)) {
        if (error.response) {
          throw new Error(error.response.data.message || 'Erreur lors de la récupération des paiements');
        } else if (error.request) {
          throw new Error('Aucune réponse du serveur. Veuillez vérifier votre connexion internet.');
        }
      }
      throw new Error('Erreur lors de la configuration de la requête');
    }
  }

  // Mise à jour du statut d'une réservation
  async updateReservationStatus(id: string, statut: Reservation['statut']): Promise<Reservation> {
    const response = await api.patch(`/reservations/${id}/statut`, { statut });
    return response.data;
  }

  // Récupération des réservations en attente
  async getPendingReservations(params: ReservationParams): Promise<ApiResponse<{
    reservations: Reservation[];
    total: number;
    filtres: ReservationParams;
  }>> {
    const response = await api.get('/reservations/en-attente', { params });
    return response.data;
  }


  // Confirmation d'une réservation
  async confirmReservation(id: string, params: ConfirmReservationParams): Promise<Reservation> {
    const response = await api.post(`/reservations/${id}/confirmer`, params);
    return response.data;
  }


  // Récupération de l'historique d'une réservation
  async getHistoriqueReservation(reservationId: string): Promise<any> {
    const response = await api.get(`/reservations/${reservationId}/historique`);
    return response.data;
  }

  async validateReservation(reservationId: string): Promise<void> {
    await api.post(`/reservations/${reservationId}/validate`);
  }

  async rejectReservation(reservationId: string, reason: string): Promise<void> {
    await api.post(`/reservations/${reservationId}/rejeter`, { reason });
  }

   async cleanupOrphanedData(): Promise<ApiResponse<{
    chambres_supprimees: number;
    disponibilites_supprimees: number;
  }>> {
    try {
      const response = await api.post<ApiResponse<{
        chambres_supprimees: number;
        disponibilites_supprimees: number;
      }>>('/reservations/cleanup-orphaned-data');
      return response.data;
    } catch (error) {
      console.error('Erreur lors du nettoyage des données orphelines:', error);
      if (axios.isAxiosError(error)) {
        if (error.response) {
          throw new Error(error.response.data.message || 'Erreur lors du nettoyage des données orphelines');
        } else if (error.request) {
          throw new Error('Aucune réponse du serveur. Veuillez vérifier votre connexion internet.');
        }
      }
      throw new Error('Erreur lors de la configuration de la requête');
    }
  }

  // Récupérer l'historique des réservations pour le calendrier
  async getHistoriqueReservationsCalendrier(params: {
    date_debut: string;
    date_fin: string;
    chambre_id?: string;
  }): Promise<ApiResponse<{
    chambres: Array<{
      chambre_id: number;
      chambre_numero: string;
      type_chambre: string;
      reservations: Array<{
        reservation_id: number;
        numero_reservation: string;
        date_arrivee: string;
        date_depart: string;
        statut: 'en_attente' | 'confirmee' | 'annulee' | 'terminee';
        type_reservation: string;
        montant_total: number;
        commentaires?: string;
        raison_rejet?: string;
        client_nom: string;
        client_prenom: string;
        client_email: string;
        client_telephone: string;
        heure_debut?: string;
        heure_fin?: string;
        prix_nuit?: number;
        created_at: string;
        updated_at: string;
      }>;
    }>;
    periode: {
      date_debut: string;
      date_fin: string;
    };
    total_reservations: number;
  }>> {
    try {
      const response = await api.get('/reservations/historique-calendrier', { params });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique des réservations:', error);
      if (axios.isAxiosError(error)) {
        if (error.response) {
          throw new Error(error.response.data.message || 'Erreur lors de la récupération de l\'historique des réservations');
        } else if (error.request) {
          throw new Error('Aucune réponse du serveur. Veuillez vérifier votre connexion internet.');
        }
      }
      throw new Error('Erreur lors de la configuration de la requête');
    }
  }

  // Générer et télécharger le ticket de caisse avec nouveau QR code
  async genererTicketCaisse(reservationId: string): Promise<Blob> {
    try {
      const response = await api.get(`/reservations/${reservationId}/ticket-caisse`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la génération du ticket de caisse:', error);
      if (axios.isAxiosError(error)) {
        if (error.response) {
          throw new Error('Erreur lors de la génération du ticket de caisse');
        } else if (error.request) {
          throw new Error('Aucune réponse du serveur. Veuillez vérifier votre connexion internet.');
        }
      }
      throw new Error('Erreur lors de la configuration de la requête');
    }
  }

  // ===== MÉTHODES POUR LES RÉSERVATIONS ANONYMES =====

  /**
   * Créer une demande de réservation anonyme (délégation vers le service anonyme)
   * @param params Paramètres de la réservation anonyme
   * @returns Promesse avec la réponse de création
   */
  async createDemandeReservationAnonyme(params: {
    complexe_id: number;
    date_arrivee: string;
    date_depart: string;
    heure_debut: string;
    heure_fin: string;
    chambres: Array<{
      chambre_id: string;
      type_chambre: string;
      prix_nuit: number;
    }>;
    prix_total: number;
    pseudonyme?: string;
    commentaires?: string;
  }): Promise<ApiResponse<{
    reservation: Reservation;
    code_acces_anonyme: string;
    message: string;
  }>> {
    try {
      // Importer le service anonyme dynamiquement pour éviter les dépendances circulaires
      const { anonymousReservationService } = await import('./anonymousReservation.service');

      const response = await anonymousReservationService.createDemandeReservationAnonyme(params);

      // Adapter la réponse au format attendu par le service de réservation
      const anonymousReservation = response.data.reservation;
      const adaptedReservation: Reservation = {
        ...anonymousReservation,
        client_id: 'anonyme',
        est_anonyme: true,
        code_acces_anonyme: response.data.code_acces_anonyme,
        type_reservation: 'heure' as const
      };

      return {
        success: response.success,
        data: {
          reservation: adaptedReservation,
          code_acces_anonyme: response.data.code_acces_anonyme,
          message: response.data.message
        },
        message: response.message
      };
    } catch (error) {
      console.error('Erreur lors de la création de la réservation anonyme:', error);
      throw error;
    }
  }

  /**
   * Vérifier si une réservation est anonyme
   * @param reservation Réservation à vérifier
   * @returns true si la réservation est anonyme
   */
  isAnonymousReservation(reservation: Reservation): boolean {
    return reservation.est_anonyme === true || !!reservation.code_acces_anonyme;
  }

  /**
   * Obtenir le nom d'affichage pour une réservation (gère les cas anonymes)
   * @param reservation Réservation
   * @returns Nom d'affichage
   */
  getDisplayName(reservation: Reservation): string {
    if (this.isAnonymousReservation(reservation)) {
      return reservation.pseudonyme || 'Client Anonyme';
    }

    if (reservation.client) {
      return `${reservation.client.prenom} ${reservation.client.nom}`;
    }

    if (reservation.client_nom && reservation.client_prenom) {
      return `${reservation.client_prenom} ${reservation.client_nom}`;
    }

    return 'Client';
  }

  /**
   * Obtenir les informations de contact pour une réservation (gère les cas anonymes)
   * @param reservation Réservation
   * @returns Informations de contact ou null pour les réservations anonymes
   */
  getContactInfo(reservation: Reservation): { email?: string; telephone?: string } | null {
    if (this.isAnonymousReservation(reservation)) {
      // Les réservations anonymes n'exposent pas les informations de contact
      return null;
    }

    return {
      email: reservation.client?.email || reservation.client_email,
      telephone: reservation.client?.telephone || reservation.client_telephone
    };
  }

  /**
   * Vérifier si une réservation peut être modifiée (gère les cas anonymes)
   * @param reservation Réservation à vérifier
   * @returns true si la réservation peut être modifiée
   */
  canModifyReservation(reservation: Reservation): boolean {
    if (this.isAnonymousReservation(reservation)) {
      // Les réservations anonymes ont des règles de modification limitées
      return reservation.statut === 'en_attente';
    }

    // Logique existante pour les réservations normales
    return reservation.statut === 'en_attente' || reservation.statut === 'confirmee';
  }

  /**
   * Vérifier si une réservation peut être annulée (gère les cas anonymes)
   * @param reservation Réservation à vérifier
   * @returns true si la réservation peut être annulée
   */
  canCancelReservation(reservation: Reservation): boolean {
    if (this.isAnonymousReservation(reservation)) {
      // Les réservations anonymes peuvent être annulées si elles sont en attente ou confirmées
      return reservation.statut === 'en_attente' || reservation.statut === 'confirmee';
    }

    // Logique existante pour les réservations normales
    return reservation.statut === 'en_attente' || reservation.statut === 'confirmee';
  }

  /**
   * Formater le code d'accès pour l'affichage (pour les réservations anonymes)
   * @param reservation Réservation anonyme
   * @returns Code d'accès formaté ou null
   */
  getFormattedAccessCode(reservation: Reservation): string | null {
    if (!this.isAnonymousReservation(reservation) || !reservation.code_acces_anonyme) {
      return null;
    }

    // Importer la méthode statique du service anonyme
    const { AnonymousReservationService } = require('./anonymousReservation.service');
    return AnonymousReservationService.formatAccessCodeForDisplay(reservation.code_acces_anonyme);
  }
}

export const reservationService = new ReservationService();
