import api from './api.config';

export interface Client {
  client_id: string;
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  adresse?: string;
  type_piece: string;
  numero_piece: string;
  date_naissance?: string;
  nationalite?: string;
  utilisateur_id?: string;
  created_at: string;
  updated_at?: string;
}

export interface ClientDetails extends Client {
  nombre_reservations: number;
  derniere_reservation?: string;
  montant_total_paiements: number;
  nombre_paiements: number;
}

export interface ClientReservation {
  reservation_id: string;
  numero_reservation: string;
  date_arrivee: string;
  date_depart: string;
  nombre_personnes: number;
  type_chambre: string;
  nombre_chambres: number;
  montant_total: number;
  statut: string;
  created_at: string;
  nombre_chambres_attribuees: number;
  numeros_chambres: string;
  types_chambres: string;
  montant_total_paiements: number;
  nombre_paiements: number;
  duree_sejour: number;
}

export interface ClientReservationsResponse {
  reservations: ClientReservation[];
  total: number;
  client_id: string;
}

export interface ClientParams {
  statut?: string;
  date_debut?: string;
  date_fin?: string;
  tri?: 'created_at' | 'date_arrivee' | 'date_depart';
}

export interface CreateClientParams {
  nom: string;
  prenom: string;
  email: string;
  telephone: string;
  adresse?: string;
  type_piece: string;
  numero_piece: string;
  date_naissance?: string;
  nationalite?: string;
  utilisateur_id: string;
}

export interface UpdateClientParams {
  nom?: string;
  prenom?: string;
  email?: string;
  telephone?: string;
  adresse?: string;
  type_piece?: string;
  numero_piece?: string;
  date_naissance?: string;
  nationalite?: string;
  utilisateur_id?: string;
}

class ClientService {
  // Création d'un client
  async createClient(params: CreateClientParams): Promise<Client> {
    const response = await api.post<Client>('/clients', params);
    return response.data;
  }

  // Récupération des détails d'un client
  async getClientById(clientId: string): Promise<ClientDetails> {
    const response = await api.get<ClientDetails>(`/clients/${clientId}`);
    return response.data;
  }

  // Mise à jour d'un client
  async updateClient(clientId: string, params: UpdateClientParams): Promise<Client> {
    const response = await api.put<Client>(`/clients/${clientId}`, params);
    return response.data;
  }

  // Récupération de l'historique des réservations d'un client
  async getClientReservations(clientId: string, params?: ClientParams): Promise<ClientReservationsResponse> {
    const response = await api.get<ClientReservationsResponse>(`/clients/${clientId}/reservations`, { params });
    return response.data;
  }

  // Méthodes utilitaires
  async getClientWithStats(clientId: string): Promise<ClientDetails> {
    return this.getClientById(clientId);
  }

  async getClientReservationsActives(clientId: string): Promise<ClientReservation[]> {
    const response = await this.getClientReservations(clientId, {
      statut: 'confirmee'
    });
    return response.reservations.filter(r =>
      new Date(r.date_depart) >= new Date()
    );
  }

  async getClientReservationsHistorique(clientId: string): Promise<ClientReservation[]> {
    const response = await this.getClientReservations(clientId);
    return response.reservations.filter(r =>
      new Date(r.date_depart) < new Date()
    );
  }

  async searchClientByEmail(email: string): Promise<Client | null> {
    try {
      // Cette méthode nécessiterait une route de recherche dans le backend
      // Pour l'instant, on peut seulement retourner null
      console.warn('Recherche par email non implémentée - route backend manquante');
      return null;
    } catch (error) {
      console.error('Erreur lors de la recherche de client:', error);
      return null;
    }
  }

  async validateClientData(data: CreateClientParams | UpdateClientParams): Promise<{
    isValid: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];

    if ('email' in data && data.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.email)) {
        errors.push('Format d\'email invalide');
      }
    }

    if ('telephone' in data && data.telephone) {
      const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
      if (!phoneRegex.test(data.telephone)) {
        errors.push('Format de téléphone invalide');
      }
    }

    if ('numero_piece' in data && data.numero_piece) {
      if (data.numero_piece.length < 3) {
        errors.push('Numéro de pièce d\'identité trop court');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Méthodes commentées car les routes correspondantes n'existent pas dans le backend actuel
  /*
  // Récupération de la liste des clients
  async getClients(params?: { limit?: number; offset?: number }): Promise<{ clients: Client[]; total: number }> {
    const response = await api.get('/clients', { params });
    return response.data;
  }

  // Suppression d'un client
  async deleteClient(clientId: string): Promise<void> {
    await api.delete(`/clients/${clientId}`);
  }
  */
}

export const clientService = new ClientService(); 