import api from './api.config';

export interface Notification {
  notification_id: string;
  titre: string;
  message: string;
  type: string;
  destinataire_id: string;
  emetteur_id: string;
  created_at: string;
  date_lecture?: string;
  lu: boolean;
  emetteur_nom?: string;
  emetteur_prenom?: string;
  destinataire_nom?: string;
  destinataire_prenom?: string;
}

export interface NotificationResponse {
  notifications: Notification[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export interface NotificationParams {
  destinataire_id?: string;
  type?: 'INFO' | 'WARNING' | 'ERROR' | 'SUCCESS' | 'RESERVATION' | 'PAIEMENT' | 'SYSTEM';
  date_debut?: string;
  date_fin?: string;
  page?: number;
  limit?: number;
}

export interface CreateNotificationRequest {
  titre: string;
  message: string;
  type: NotificationParams['type'];
  destinataire_id: string;
}

class NotificationService {
  async getNotifications(params: NotificationParams): Promise<NotificationResponse> {
    const response = await api.get<NotificationResponse>('/notifications', { params });
    return response.data;
  }

  async createNotification(data: CreateNotificationRequest): Promise<Notification> {
    const response = await api.post<Notification>('/notifications', data);
    return response.data;
  }

  async markAsRead(notificationId: string): Promise<Notification> {
    const response = await api.put<Notification>(`/notifications/${notificationId}/lu`);
    return response.data;
  }

  async getUnreadNotifications(params: Omit<NotificationParams, 'date_debut' | 'date_fin'>): Promise<NotificationResponse> {
    const response = await api.get<NotificationResponse>('/notifications/non-lues', { params });
    return response.data;
  }

  // Méthodes utilitaires
  async getUnreadCount(destinataire_id?: string): Promise<number> {
    const response = await this.getUnreadNotifications({
      destinataire_id,
      limit: 1
    });
    return response.pagination.total;
  }

  async markAllAsRead(destinataire_id: string): Promise<void> {
    const unreadNotifications = await this.getUnreadNotifications({
      destinataire_id
    });

    const markPromises = unreadNotifications.notifications.map(notification =>
      this.markAsRead(notification.notification_id)
    );

    await Promise.all(markPromises);
  }

  // Méthodes de création rapide pour différents types
  async createInfoNotification(destinataire_id: string, titre: string, message: string): Promise<Notification> {
    return this.createNotification({
      destinataire_id,
      titre,
      message,
      type: 'INFO'
    });
  }

  async createWarningNotification(destinataire_id: string, titre: string, message: string): Promise<Notification> {
    return this.createNotification({
      destinataire_id,
      titre,
      message,
      type: 'WARNING'
    });
  }

  async createSuccessNotification(destinataire_id: string, titre: string, message: string): Promise<Notification> {
    return this.createNotification({
      destinataire_id,
      titre,
      message,
      type: 'SUCCESS'
    });
  }
}

export const notificationService = new NotificationService(); 