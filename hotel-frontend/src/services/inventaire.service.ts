import api from './api.config';
import type {
  Ingredient,
  IngredientFilters,
  StockIngredient,
  StockAlert,
  InventaireConfig,
  StockThreshold,
  AlertConfig,
  IngredientResponse,
  StockResponse,
  PaginationInfo,
  SearchResponse,
  StockOperationResponse,
  BaseApiResponse
} from '../types';

/**
 * Service pour la gestion de l'inventaire
 */
class InventaireService {
  private baseURL = 'inventaire';

  /**
   * Récupération des ingrédients avec filtres et pagination
   */
  async getIngredients(
    complexeId: number, 
    filters: IngredientFilters = {}
  ): Promise<{ ingredients: Ingredient[]; pagination: PaginationInfo }> {
    try {
      const params = new URLSearchParams({
        complexe_id: complexeId.toString(),
        ...this.buildFilterParams(filters)
      });

      const response = await api.get<IngredientResponse>(`${this.baseURL}/ingredients?${params}`);
      
      if (response.data.success) {
        return {
          ingredients: Array.isArray(response.data.data) ? response.data.data : [response.data.data],
          pagination: response.data.pagination || { page: 1, limit: 50, total: 0, totalPages: 0 }
        };
      }
      
      throw new Error(response.data.message || 'Erreur lors de la récupération des ingrédients');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des ingrédients');
    }
  }

  /**
   * Création d'un nouvel ingrédient
   */
  async createIngredient(ingredientData: Partial<Ingredient>): Promise<Ingredient> {
    try {
      const response = await api.post<IngredientResponse>(`${this.baseURL}/ingredients`, ingredientData);
      
      if (response.data.success && !Array.isArray(response.data.data)) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Erreur lors de la création de l\'ingrédient');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la création de l\'ingrédient');
    }
  }

  /**
   * Mise à jour d'un ingrédient
   */
  async updateIngredient(ingredientId: number, data: Partial<Ingredient>): Promise<Ingredient> {
    try {
      const response = await api.put<IngredientResponse>(`${this.baseURL}/ingredients/${ingredientId}`, data);
      
      if (response.data.success && !Array.isArray(response.data.data)) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Erreur lors de la mise à jour de l\'ingrédient');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la mise à jour de l\'ingrédient');
    }
  }

  /**
   * Suppression d'un ingrédient
   */
  async deleteIngredient(ingredientId: number): Promise<void> {
    try {
      const response = await api.delete(`${this.baseURL}/ingredients/${ingredientId}`);
      
      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la suppression de l\'ingrédient');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la suppression de l\'ingrédient');
    }
  }

  /**
   * Recherche d'ingrédients
   */
  async searchIngredients(query: string, complexeId: number): Promise<Ingredient[]> {
    try {
      const response = await api.get<SearchResponse<Ingredient>>(
        `${this.baseURL}/ingredients/search?q=${encodeURIComponent(query)}&complexe_id=${complexeId}`
      );
      
      if (response.data.success) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Erreur lors de la recherche d\'ingrédients');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la recherche d\'ingrédients');
    }
  }

  /**
   * Récupération du stock par complexe
   */
  async getStock(complexeId: number, serviceId?: number): Promise<StockIngredient[]> {
    try {
      const params = new URLSearchParams({ complexe_id: complexeId.toString() });
      if (serviceId) params.append('service_id', serviceId.toString());

      const response = await api.get<StockResponse>(`${this.baseURL}/stock?${params}`);
      
      if (response.data.success) {
        return Array.isArray(response.data.data) ? response.data.data : [response.data.data];
      }
      
      throw new Error(response.data.message || 'Erreur lors de la récupération du stock');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération du stock');
    }
  }

  /**
   * Mise à jour du stock d'un ingrédient
   */
  async updateStock(
    ingredientId: number, 
    complexeId: number, 
    quantite: number, 
    typeOperation: 'ENTREE' | 'SORTIE' | 'AJUSTEMENT'
  ): Promise<StockIngredient> {
    try {
      const response = await api.put<StockOperationResponse>(`${this.baseURL}/stock/${ingredientId}`, {
        complexe_id: complexeId,
        quantite,
        type_operation: typeOperation
      });
      
      if (response.data.success) {
        // Récupérer le stock mis à jour
        const stockResponse = await api.get<StockResponse>(`${this.baseURL}/stock/${ingredientId}?complexe_id=${complexeId}`);
        if (stockResponse.data.success && !Array.isArray(stockResponse.data.data)) {
          return stockResponse.data.data;
        }
      }
      
      throw new Error(response.data.message || 'Erreur lors de la mise à jour du stock');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la mise à jour du stock');
    }
  }

  /**
   * Récupération des alertes de stock
   */
  async getStockAlerts(complexeId: number): Promise<StockAlert[]> {
    try {
      const response = await api.get<StockResponse>(`${this.baseURL}/alerts/${complexeId}`);
      
      if (response.data.success && response.data.alerts) {
        return response.data.alerts;
      }
      
      return [];
    } catch (error: any) {
      console.error('Erreur lors de la récupération des alertes:', error);
      return [];
    }
  }

  /**
   * Récupération des analyses d'inventaire
   */
  async getInventaireAnalytics(complexeId: number, period?: string): Promise<any> {
    try {
      const params = new URLSearchParams({ complexe_id: complexeId.toString() });
      if (period) params.append('period', period);

      const response = await api.get(`${this.baseURL}/analytics?${params}`);
      
      if (response.data.success) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Erreur lors de la récupération des analyses');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération des analyses');
    }
  }

  /**
   * Récupération d'un ingrédient par ID
   */
  async getIngredientById(ingredientId: number): Promise<Ingredient> {
    try {
      const response = await api.get<IngredientResponse>(`${this.baseURL}/ingredients/${ingredientId}`);
      
      if (response.data.success && !Array.isArray(response.data.data)) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Ingrédient non trouvé');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération de l\'ingrédient');
    }
  }

  /**
   * Construction des paramètres de filtre pour l'URL
   */
  private buildFilterParams(filters: IngredientFilters): Record<string, string> {
    const params: Record<string, string> = {};
    
    if (filters.categorie) params.categorie = filters.categorie;
    if (filters.conservation) params.conservation = filters.conservation;
    if (filters.actif !== undefined) params.actif = filters.actif.toString();
    if (filters.search) params.search = filters.search;
    if (filters.page) params.page = filters.page.toString();
    if (filters.limit) params.limit = filters.limit.toString();
    
    return params;
  }
}

export const inventaireService = new InventaireService();
export default inventaireService;
