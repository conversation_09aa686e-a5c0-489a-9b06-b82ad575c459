import { authService } from './auth.service';
import { api as apiBase } from './api';
import type { MenuItem, ServiceComplexe, SessionCaisse, Commande, MenuCategory } from './types';

// Types pour les points de vente
interface PointDeVente {
  id: number;
  nom: string;
  type: string;
  statut: string;
}

// Types pour les sessions
interface Session {
  id: number;
  point_de_vente_id: number;
  date_ouverture: string;
  date_fermeture?: string;
  fonds_ouverture: number;
  fonds_fermeture?: number;
  statut: 'ouverte' | 'fermée';
}

// Types pour les commandes
interface CommandeItem {
  produit_id: number;
  quantite: number;
  prix_unitaire: number;
}

interface CommandeData {
  point_de_vente_id: number;
  table_id?: number;
  type_commande: 'SUR_PLACE' | 'À_EMPORTER';
  items: CommandeItem[];
}

// Configuration de l'API avec authentification
const api = {
  ...apiBase,
  get: async (url: string) => {
    const token = await authService.getToken();
    return apiBase.get(url, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },
  post: async (url: string, data: any) => {
    const token = await authService.getToken();
    return apiBase.post(url, data, {
      headers: { Authorization: `Bearer ${token}` }
    });
  }
};

export class POSService {
  // Gestion des catégories et produits
  async getMenuCategories(serviceId: number): Promise<MenuCategory[]> {
    const response = await api.get(`/api/services/${serviceId}/categories`);
    return response.data;
  }

  async getMenuItems(categoryId: number): Promise<MenuItem[]> {
    const response = await api.get(`/api/categories/${categoryId}/items`);
    return response.data;
  }

  // Gestion des sessions
  async openSession(serviceId: number, fondsOuverture: number, notes?: string): Promise<SessionCaisse> {
    const response = await api.post('/api/sessions', {
      service_id: serviceId,
      fonds_ouverture: fondsOuverture,
      notes
    });
    return response.data;
  }

  async closeSession(sessionId: number, fondsCloture: number, notes?: string): Promise<void> {
    await api.post(`/api/sessions/${sessionId}/close`, {
      fonds_cloture: fondsCloture,
      notes
    });
  }

  // Gestion des commandes
  async createCommande(data: {
    service_id: number;
    table_id?: number;
    type_commande: 'SUR_PLACE' | 'À_EMPORTER';
    items: Array<{
      produit_id: number;
      quantite: number;
      prix_unitaire: number;
    }>;
  }): Promise<Commande> {
    const response = await api.post('/api/commandes', data);
    return response.data;
  }

  async registerPayment(commandeId: number, paymentData: {
    montant: number;
    mode_paiement: string;
    reference?: string;
  }): Promise<void> {
    await api.post(`/api/commandes/${commandeId}/paiement`, paymentData);
  }

  // Vérification des stocks
  async checkProductAvailability(productId: number): Promise<{
    disponible: boolean;
    quantite_disponible: number;
  }> {
    const response = await api.get(`/api/produits/${productId}/disponibilite`);
    return response.data;
  }

  async validateOrderStock(items: Array<{
    produit_id: number;
    quantite: number;
  }>): Promise<{
    valide: boolean;
    erreurs: Array<{
      produit_id: number;
      message: string;
      alternatives?: Array<{
        produit_id: number;
        nom: string;
        prix: number;
      }>;
    }>;
  }> {
    const response = await api.post('/api/stock/validation', { items });
    return response.data;
  }

  async deductStockForTransaction(transactionId: number): Promise<void> {
    await api.post(`/api/stock/deduction/${transactionId}`);
  }

  async restoreStockForCancelledTransaction(transactionId: number): Promise<void> {
    await api.post(`/api/stock/restoration/${transactionId}`);
  }

  async checkTransactionStockStatus(transactionId: number): Promise<{
    status: 'pending' | 'completed' | 'failed';
    message?: string;
  }> {
    const response = await api.get(`/api/stock/status/${transactionId}`);
    return response.data;
  }
}

export const posService = new POSService();
