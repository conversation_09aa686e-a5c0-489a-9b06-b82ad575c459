import api from './api.config';
import { authService } from './auth.service';

// Types pour les imports d'ingrédients
export interface IngredientImportResponse {
  success: boolean;
  message: string;
  data: {
    import_id: number;
    complexe_id: number;
    total_items: number;
    imported_items: number;
    errors: ImportError[];
    warnings: ImportWarning[];
    categories_created: number;
    processing_time: number;
    type: 'cuisine' | 'boissons';
  };
}

export interface ImportError {
  ligne: number;
  colonne?: string;
  erreur: string;
  valeur_problematique?: any;
  suggestion?: string;
}

export interface ImportWarning {
  ligne: number;
  colonne?: string;
  message: string;
  valeur: any;
}

export interface IngredientValidationResponse {
  success: boolean;
  message: string;
  data: {
    is_valid: boolean;
    total_rows: number;
    valid_rows: number;
    errors: ImportError[];
    warnings: ImportWarning[];
    preview_data: any[];
    suggested_categories: string[];
    suggested_suppliers: string[];
  };
}

export interface IngredientImportStatus {
  complexe_id: number;
  last_import_date?: string;
  total_imports: number;
  total_ingredients_imported: number;
  has_cuisine_ingredients: boolean;
  has_boisson_inventory: boolean;
  cuisine_ingredients_count: number;
  boisson_inventory_count: number;
}

/**
 * Service pour la gestion des imports d'ingrédients et inventaire boissons
 */
class IngredientImportService {
  private baseURL = 'ingredient-import';

  /**
   * Vérification de l'authentification
   */
  private checkAuth(): void {
    if (!authService.isAuthenticated()) {
      throw new Error('Utilisateur non authentifié');
    }
  }

  /**
   * Import d'ingrédients cuisine depuis un fichier Excel
   */
  async importCuisineIngredients(
    complexeId: number, 
    file: File, 
    options?: { overwrite?: boolean; validate_only?: boolean }
  ): Promise<IngredientImportResponse> {
    this.checkAuth();

    const formData = new FormData();
    formData.append('ingredientFile', file);
    
    if (options) {
      formData.append('options', JSON.stringify(options));
    }

    try {
      const response = await api.post<IngredientImportResponse>(
        `${this.baseURL}/cuisine/${complexeId}`,
        formData,
        {
          headers: {
            // Ne pas définir Content-Type, laisser le navigateur le faire automatiquement
          },
          timeout: 60000, // 1 minute pour les gros fichiers
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de l\'import des ingrédients cuisine');
      }

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de l\'import des ingrédients cuisine'
      );
    }
  }

  /**
   * Import d'inventaire boissons depuis un fichier Excel
   */
  async importBoissonInventory(
    complexeId: number, 
    file: File, 
    options?: { overwrite?: boolean; validate_only?: boolean }
  ): Promise<IngredientImportResponse> {
    this.checkAuth();

    const formData = new FormData();
    formData.append('boissonFile', file);
    
    if (options) {
      formData.append('options', JSON.stringify(options));
    }

    try {
      const response = await api.post<IngredientImportResponse>(
        `${this.baseURL}/boissons/${complexeId}`,
        formData,
        {
          headers: {
            // Ne pas définir Content-Type, laisser le navigateur le faire automatiquement
          },
          timeout: 60000,
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de l\'import de l\'inventaire boissons');
      }

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de l\'import de l\'inventaire boissons'
      );
    }
  }

  /**
   * Validation d'un fichier Excel d'ingrédients cuisine sans l'importer
   */
  async validateCuisineIngredients(file: File): Promise<IngredientValidationResponse> {
    this.checkAuth();

    const formData = new FormData();
    formData.append('ingredientFile', file);

    try {
      const response = await api.post<IngredientValidationResponse>(
        `${this.baseURL}/validate/cuisine`,
        formData,
        {
          headers: {
            // Ne pas définir Content-Type, laisser le navigateur le faire automatiquement
            // pour multipart/form-data avec la bonne boundary
          },
          timeout: 30000,
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la validation des ingrédients cuisine');
      }

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message ||
        error.message ||
        'Erreur lors de la validation des ingrédients cuisine'
      );
    }
  }

  /**
   * Validation d'un fichier Excel d'inventaire boissons sans l'importer
   */
  async validateBoissonInventory(file: File): Promise<IngredientValidationResponse> {
    this.checkAuth();

    const formData = new FormData();
    formData.append('boissonFile', file);

    try {
      const response = await api.post<IngredientValidationResponse>(
        `${this.baseURL}/validate/boissons`,
        formData,
        {
          headers: {
            // Ne pas définir Content-Type, laisser le navigateur le faire automatiquement
          },
          timeout: 30000,
        }
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la validation de l\'inventaire boissons');
      }

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de la validation de l\'inventaire boissons'
      );
    }
  }

  /**
   * Récupération du statut d'import pour un complexe
   */
  async getImportStatus(complexeId: number): Promise<IngredientImportStatus> {
    this.checkAuth();

    try {
      const response = await api.get<{
        success: boolean;
        message: string;
        data: IngredientImportStatus;
      }>(`${this.baseURL}/status/${complexeId}`);

      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de la récupération du statut');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de la récupération du statut d\'import'
      );
    }
  }

  /**
   * Récupération des informations sur les imports d'ingrédients
   */
  async getImportInfo(): Promise<{
    supportedFormats: string[];
    maxFileSize: string;
    requiredFields: {
      cuisine: string[];
      boissons: string[];
    };
    optionalFields: {
      cuisine: string[];
      boissons: string[];
    };
    validCategories: {
      cuisine: string[];
      boissons: string[];
    };
    validUnits: string[];
    validConservationTypes: string[];
    validStorageLocations: string[];
  }> {
    try {
      const response = await api.get<{
        success: boolean;
        data: any;
      }>(`${this.baseURL}/info`);

      if (!response.data.success) {
        throw new Error('Erreur lors de la récupération des informations');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Erreur lors de la récupération des informations d\'import'
      );
    }
  }
}

export const ingredientImportService = new IngredientImportService();
export default ingredientImportService;
