import api from './api.config';
import { authService } from './auth.service';
import type { RecetteIngredient } from '../types';

// Types pour les menus et produits
export interface MenuItem {
  produit_id: number;
  service_id: number;
  nom: string;
  description?: string;
  prix_vente: number;
  categorie: string;
  image_url?: string;
  stock_disponible: number;
  ingredients?: RecetteIngredient[];
  allergenes?: string[];
  temps_preparation?: number;
  actif: boolean;
  popularite?: number;
  created_at: string;
  updated_at?: string;
}

export interface MenuCategory {
  categorie_id: number;
  service_id: number;
  nom: string;
  description?: string;
  ordre_affichage: number;
  actif: boolean;
  items: MenuItem[];
}

export interface MenuWithAvailability extends MenuItem {
  disponible: boolean;
  stock_restant: number;
  ingredients_manquants?: string[];
}

export interface CreateMenuItemParams {
  service_id: number;
  nom: string;
  description?: string;
  prix_vente: number;
  categorie: string;
  image_url?: string;
  allergenes?: string[];
  temps_preparation?: number;
}

export interface UpdateMenuItemParams {
  nom?: string;
  description?: string;
  prix_vente?: number;
  categorie?: string;
  image_url?: string;
  allergenes?: string[];
  temps_preparation?: number;
  actif?: boolean;
}

export interface CreateCategoryParams {
  service_id: number;
  nom: string;
  description?: string;
  ordre_affichage?: number;
}

export interface UpdateCategoryParams {
  nom?: string;
  description?: string;
  ordre_affichage?: number;
  actif?: boolean;
}

export interface MenuResponse {
  success: boolean;
  data: MenuItem | MenuItem[] | MenuCategory | MenuCategory[];
  message?: string;
}

export interface AvailabilityResponse {
  success: boolean;
  data: {
    disponible: boolean;
    stock_restant: number;
    ingredients_manquants?: string[];
  };
  message?: string;
}

export interface MenuFilters {
  categorie?: string;
  actif?: boolean;
  disponible?: boolean;
  search?: string;
  prix_min?: number;
  prix_max?: number;
}

class MenuService {
  private checkAuth() {
    if (!authService.isAuthenticated()) {
      throw new Error('Non authentifié');
    }
  }

  private getComplexeId(): number {
    const user = authService.getCurrentUser();
    if (!user) throw new Error('Utilisateur non authentifié');

    if (user.role === 'admin_chaine') {
      const selectedComplexeId = localStorage.getItem('selectedComplexeId');
      if (!selectedComplexeId) {
        throw new Error('Aucun complexe sélectionné');
      }
      return parseInt(selectedComplexeId, 10);
    }

    if (!user.complexe_id) {
      throw new Error('Complexe ID non disponible');
    }
    return user.complexe_id;
  }

  // Récupération du menu complet par service
  async getMenuByService(serviceId: number, filters?: MenuFilters): Promise<MenuCategory[]> {
    this.checkAuth();
    
    const params: any = {};
    if (filters) {
      Object.assign(params, filters);
    }
    
    const response = await api.get<MenuResponse>(`/menu/service/${serviceId}`, { params });
    
    if (response.data.success && Array.isArray(response.data.data)) {
      return response.data.data as MenuCategory[];
    }
    return [];
  }

  // Récupération du menu avec vérification de disponibilité
  async getMenuWithAvailability(serviceId: number): Promise<MenuCategory[]> {
    this.checkAuth();
    
    const response = await api.get<MenuResponse>(`/menu/service/${serviceId}/availability`);
    
    if (response.data.success && Array.isArray(response.data.data)) {
      return response.data.data as MenuCategory[];
    }
    return [];
  }

  // Récupération d'un item de menu par ID
  async getMenuItemById(produitId: number): Promise<MenuItem> {
    this.checkAuth();
    
    const response = await api.get<MenuResponse>(`/menu/items/${produitId}`);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data as MenuItem;
    }
    throw new Error('Produit non trouvé');
  }

  // Vérification de la disponibilité d'un item
  async checkItemAvailability(produitId: number, quantite: number = 1): Promise<boolean> {
    this.checkAuth();
    
    const response = await api.get<AvailabilityResponse>(`/menu/items/${produitId}/availability`, {
      params: { quantite }
    });
    
    if (response.data.success) {
      return response.data.data.disponible;
    }
    return false;
  }

  // Récupération des détails de disponibilité d'un item
  async getItemAvailabilityDetails(produitId: number, quantite: number = 1): Promise<{
    disponible: boolean;
    stock_restant: number;
    ingredients_manquants?: string[];
  }> {
    this.checkAuth();
    
    const response = await api.get<AvailabilityResponse>(`/menu/items/${produitId}/availability`, {
      params: { quantite }
    });
    
    if (response.data.success) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la vérification de disponibilité');
  }

  // Création d'un nouvel item de menu
  async createMenuItem(data: CreateMenuItemParams): Promise<MenuItem> {
    this.checkAuth();
    
    const response = await api.post<MenuResponse>('/menu/items', data);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data as MenuItem;
    }
    throw new Error('Erreur lors de la création du produit');
  }

  // Mise à jour d'un item de menu
  async updateMenuItem(produitId: number, data: UpdateMenuItemParams): Promise<MenuItem> {
    this.checkAuth();
    
    const response = await api.put<MenuResponse>(`/menu/items/${produitId}`, data);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data as MenuItem;
    }
    throw new Error('Erreur lors de la mise à jour du produit');
  }

  // Suppression d'un item de menu
  async deleteMenuItem(produitId: number): Promise<void> {
    this.checkAuth();
    
    const response = await api.delete<MenuResponse>(`/menu/items/${produitId}`);
    
    if (!response.data.success) {
      throw new Error('Erreur lors de la suppression du produit');
    }
  }

  // Gestion des catégories
  async getCategoriesByService(serviceId: number): Promise<MenuCategory[]> {
    this.checkAuth();
    
    const response = await api.get<MenuResponse>(`/menu/service/${serviceId}/categories`);
    
    if (response.data.success && Array.isArray(response.data.data)) {
      return response.data.data as MenuCategory[];
    }
    return [];
  }

  // Création d'une catégorie
  async createCategory(data: CreateCategoryParams): Promise<MenuCategory> {
    this.checkAuth();
    
    const response = await api.post<MenuResponse>('/menu/categories', data);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data as MenuCategory;
    }
    throw new Error('Erreur lors de la création de la catégorie');
  }

  // Mise à jour d'une catégorie
  async updateCategory(categorieId: number, data: UpdateCategoryParams): Promise<MenuCategory> {
    this.checkAuth();
    
    const response = await api.put<MenuResponse>(`/menu/categories/${categorieId}`, data);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data as MenuCategory;
    }
    throw new Error('Erreur lors de la mise à jour de la catégorie');
  }

  // Suppression d'une catégorie
  async deleteCategory(categorieId: number): Promise<void> {
    this.checkAuth();
    
    const response = await api.delete<MenuResponse>(`/menu/categories/${categorieId}`);
    
    if (!response.data.success) {
      throw new Error('Erreur lors de la suppression de la catégorie');
    }
  }

  // Recherche dans le menu
  async searchMenuItems(serviceId: number, searchTerm: string): Promise<MenuItem[]> {
    this.checkAuth();
    
    const response = await api.get<MenuResponse>(`/menu/service/${serviceId}/search`, {
      params: { q: searchTerm }
    });
    
    if (response.data.success && Array.isArray(response.data.data)) {
      return response.data.data as MenuItem[];
    }
    return [];
  }

  // Récupération des items populaires
  async getPopularItems(serviceId: number, limit: number = 10): Promise<MenuItem[]> {
    this.checkAuth();
    
    const response = await api.get<MenuResponse>(`/menu/service/${serviceId}/popular`, {
      params: { limit }
    });
    
    if (response.data.success && Array.isArray(response.data.data)) {
      return response.data.data as MenuItem[];
    }
    return [];
  }

  // Mise à jour du stock d'un item (après vente)
  async updateItemStock(produitId: number, quantiteVendue: number): Promise<void> {
    this.checkAuth();
    
    const response = await api.put<MenuResponse>(`/menu/items/${produitId}/stock`, {
      quantite_vendue: quantiteVendue
    });
    
    if (!response.data.success) {
      throw new Error('Erreur lors de la mise à jour du stock');
    }
  }
}

export const menuService = new MenuService();
