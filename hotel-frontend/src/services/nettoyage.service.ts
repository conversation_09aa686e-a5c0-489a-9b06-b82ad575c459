import api from './api.config';

export interface ChambreEnNettoyage {
  chambre_id: string;
  numero: string;
  type_chambre: string;
  etage?: string;
  statut_nettoyage: 'EN_COURS' | 'NETTOYEE' | string;
  date_debut_nettoyage?: string;
  date_fin_nettoyage?: string;
  nettoye_par?: string;
  nettoyeur_nom?: string;
  nettoyeur_prenom?: string;
  duree_nettoyage_minutes?: number;
}

class NettoyageService {
  // Déclarer une chambre en nettoyage
  async declarerNettoyage(chambreId: string, userId: string): Promise<ChambreEnNettoyage> {
    const response = await api.post<ChambreEnNettoyage>(`/nettoyage/${chambreId}/debut`, { userId });
    return response.data;
  }

  // Marquer une chambre comme nettoyée
  async terminerNettoyage(chambreId: string, userId: string): Promise<ChambreEnNettoyage> {
    const response = await api.post<ChambreEnNettoyage>(`/nettoyage/${chambreId}/fin`, { userId });
    return response.data;
  }

  // Récupérer la liste des chambres en nettoyage
  async getChambresEnNettoyage(): Promise<ChambreEnNettoyage[]> {
    const response = await api.get<ChambreEnNettoyage[]>(`/nettoyage/en-cours`);
    return response.data;
  }
}

export const nettoyageService = new NettoyageService();
