import api from './api.config';
import type {
  TypeImport,
  ImportExcel,
  ImportPreview,
  ImportResponse
} from '../types';

/**
 * Service pour la gestion des uploads de fichiers Excel
 */
class UploadService {
  private baseURL = 'upload';

  /**
   * Upload d'un fichier Excel
   */
  async uploadExcelFile(
    file: File, 
    typeImport: TypeImport, 
    complexeId: number, 
    serviceId?: number
  ): Promise<ImportExcel> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type_import', typeImport);
      formData.append('complexe_id', complexeId.toString());
      
      if (serviceId) {
        formData.append('service_id', serviceId.toString());
      }

      const response = await api.post<ImportResponse>(
        `${this.baseURL}/excel`, 
        formData, 
        {
          headers: { 
            'Content-Type': 'multipart/form-data' 
          },
          timeout: 30000 // 30 secondes pour l'upload
        }
      );

      if (response.data.success && !Array.isArray(response.data.data)) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Erreur lors de l\'upload du fichier');
    } catch (error: any) {
      if (error.response?.status === 413) {
        throw new Error('Fichier trop volumineux');
      }
      if (error.response?.status === 415) {
        throw new Error('Type de fichier non supporté');
      }
      throw new Error(error.response?.data?.message || 'Erreur lors de l\'upload du fichier');
    }
  }

  /**
   * Téléchargement d'un template Excel
   */
  async downloadTemplate(typeImport: TypeImport): Promise<Blob> {
    try {
      const response = await api.get(
        `${this.baseURL}/templates/${typeImport}`, 
        {
          responseType: 'blob',
          timeout: 15000
        }
      );
      
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors du téléchargement du template');
    }
  }

  /**
   * Prévisualisation des données importées
   */
  async getImportPreview(importId: number): Promise<ImportPreview> {
    try {
      const response = await api.get<ImportResponse>(`${this.baseURL}/preview/${importId}`);
      
      if (response.data.success && response.data.preview) {
        return response.data.preview;
      }
      
      throw new Error(response.data.message || 'Erreur lors de la récupération de la prévisualisation');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération de la prévisualisation');
    }
  }

  /**
   * Validation des données d'import
   */
  async validateImport(importId: number): Promise<ImportExcel> {
    try {
      const response = await api.post<ImportResponse>(`${this.baseURL}/validate/${importId}`);
      
      if (response.data.success && !Array.isArray(response.data.data)) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Erreur lors de la validation');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la validation');
    }
  }

  /**
   * Récupération du statut de l'import
   */
  async getImportStatus(importId: number): Promise<ImportExcel> {
    try {
      const response = await api.get<ImportResponse>(`${this.baseURL}/status/${importId}`);
      
      if (response.data.success && !Array.isArray(response.data.data)) {
        return response.data.data;
      }
      
      throw new Error(response.data.message || 'Erreur lors de la récupération du statut');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de la récupération du statut');
    }
  }

  /**
   * Polling pour suivre le statut d'un import
   */
  async pollImportStatus(
    importId: number, 
    onUpdate: (status: ImportExcel) => void,
    intervalMs: number = 2000,
    maxAttempts: number = 150 // 5 minutes max
  ): Promise<ImportExcel> {
    return new Promise((resolve, reject) => {
      let attempts = 0;
      
      const poll = async () => {
        try {
          attempts++;
          
          if (attempts > maxAttempts) {
            reject(new Error('Timeout: L\'import prend trop de temps'));
            return;
          }

          const status = await this.getImportStatus(importId);
          onUpdate(status);

          // Statuts finaux
          if (['IMPORTE', 'ERREUR', 'ANNULE'].includes(status.statut)) {
            resolve(status);
          } else {
            // Continuer le polling
            setTimeout(poll, intervalMs);
          }
        } catch (error) {
          reject(error);
        }
      };
      
      poll();
    });
  }

  /**
   * Annulation d'un import
   */
  async cancelImport(importId: number): Promise<void> {
    try {
      const response = await api.post(`${this.baseURL}/cancel/${importId}`);
      
      if (!response.data.success) {
        throw new Error(response.data.message || 'Erreur lors de l\'annulation');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Erreur lors de l\'annulation');
    }
  }

  /**
   * Téléchargement d'un template avec nom de fichier approprié
   */
  async downloadTemplateWithFilename(typeImport: TypeImport): Promise<void> {
    try {
      const blob = await this.downloadTemplate(typeImport);
      
      // Créer un nom de fichier approprié
      const filename = `template_${typeImport.toLowerCase()}_${new Date().toISOString().split('T')[0]}.xlsx`;
      
      // Créer un lien de téléchargement
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      throw new Error(error.message || 'Erreur lors du téléchargement du template');
    }
  }

  /**
   * Validation côté client d'un fichier avant upload
   */
  validateFileBeforeUpload(file: File): { valid: boolean; error?: string } {
    // Vérifier le type de fichier
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel' // .xls
    ];
    
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Type de fichier non supporté. Seuls les fichiers Excel (.xlsx, .xls) sont acceptés'
      };
    }

    // Vérifier la taille (10MB max)
    const maxSizeBytes = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSizeBytes) {
      return {
        valid: false,
        error: 'Fichier trop volumineux. Taille maximum autorisée : 10MB'
      };
    }

    return { valid: true };
  }
}

export const uploadService = new UploadService();
export default uploadService;
