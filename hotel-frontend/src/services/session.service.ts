import api from './api.config';
import { authService } from './auth.service';

// Types pour les sessions de caisse
export interface SessionCaisse {
  session_id: number;
  pos_id: number;
  employe_id: number;
  complexe_id: number;
  service_id: number;
  date_ouverture: string;
  date_fermeture?: string;
  fonds_ouverture: number;
  fonds_fermeture?: number;
  total_ventes?: number;
  total_especes?: number;
  total_cartes?: number;
  total_autres?: number;
  notes?: string;
  statut: 'Ouverte' | 'Fermée' | 'Vérifiée';
  // Champs joints
  employe_nom?: string;
  employe_prenom?: string;
  pos_nom?: string;
  service_nom?: string;
  type_service?: string;
}

export interface CreateSessionParams {
  pos_id: number;
  employe_id: number;
  complexe_id: number;
  service_id: number;
  fonds_ouverture: number;
  notes?: string;
}

export interface CloseSessionParams {
  fonds_fermeture: number;
  total_ventes?: number;
  total_especes?: number;
  total_cartes?: number;
  total_autres?: number;
  notes?: string;
}

export interface SessionParams {
  dateDebut?: string;
  dateFin?: string;
  statut?: 'Ouverte' | 'Fermée' | 'Vérifiée';
  serviceId?: number;
}

export interface SessionResponse {
  success: boolean;
  data: SessionCaisse | SessionCaisse[];
  message?: string;
}

export class SessionCaisseService {
  private checkAuth() {
    if (!authService.isAuthenticated()) {
      throw new Error('Non authentifié');
    }
  }

  private getComplexeId(): number {
    const user = authService.getCurrentUser();
    if (!user) throw new Error('Utilisateur non authentifié');

    // Si l'utilisateur est un admin de chaîne, on utilise le complexe_id sélectionné
    if (user.role === 'admin_chaine') {
      const selectedComplexeId = localStorage.getItem('selectedComplexeId');
      if (!selectedComplexeId) {
        throw new Error('Aucun complexe sélectionné');
      }
      return parseInt(selectedComplexeId, 10);
    }

    // Pour les autres rôles, on utilise le complexe_id de l'utilisateur
    if (!user.complexe_id) {
      throw new Error('Complexe ID non disponible');
    }
    return user.complexe_id;
  }

  private getCurrentEmployeeId(): number {
    const user = authService.getCurrentUser();
    if (!user) {
      throw new Error('Utilisateur non authentifié');
    }

    // Pour les employés, utiliser employe_id s'il existe, sinon utiliser id
    if (user.role === 'employe') {
      return user.employe_id || user.id;
    }

    // Pour les autres rôles, utiliser l'id général
    return user.id;
  }

  // Ouvrir une nouvelle session de caisse
  async ouvrirSession(data: Omit<CreateSessionParams, 'complexe_id' | 'employe_id'>): Promise<SessionCaisse> {
    this.checkAuth();
    const complexeId = this.getComplexeId();
    const employeId = this.getCurrentEmployeeId();
    
    const sessionData: CreateSessionParams = {
      ...data,
      complexe_id: complexeId,
      employe_id: employeId
    };

    const response = await api.post<SessionResponse>('/sessions/ouvrir', sessionData);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de l\'ouverture de la session de caisse');
  }

  // Fermer une session de caisse
  async fermerSession(sessionId: number, data: CloseSessionParams): Promise<SessionCaisse> {
    this.checkAuth();
    
    const response = await api.put<SessionResponse>(`/sessions/fermer/${sessionId}`, data);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Erreur lors de la fermeture de la session de caisse');
  }

  // Récupérer les sessions d'un point de vente
  async getSessionsByPOS(posId: number): Promise<SessionCaisse[]> {
    this.checkAuth();
    
    const response = await api.get<SessionResponse>(`/sessions/pos/${posId}`);
    
    if (response.data.success && Array.isArray(response.data.data)) {
      return response.data.data;
    }
    return [];
  }

  // Récupérer la session active d'un point de vente
  async getActiveSession(posId: number): Promise<SessionCaisse | null> {
    this.checkAuth();
    
    try {
      const response = await api.get<SessionResponse>(`/sessions/pos/${posId}/active`);
      
      if (response.data.success && !Array.isArray(response.data.data)) {
        return response.data.data;
      }
    } catch (error: any) {
      // Si aucune session active n'est trouvée, retourner null au lieu de lever une erreur
      if (error.response?.status === 404) {
        return null;
      }
      throw error;
    }
    
    return null;
  }

  // Récupérer une session par ID
  async getSessionById(sessionId: number): Promise<SessionCaisse> {
    this.checkAuth();
    
    const response = await api.get<SessionResponse>(`/sessions/${sessionId}`);
    
    if (response.data.success && !Array.isArray(response.data.data)) {
      return response.data.data;
    }
    throw new Error('Session non trouvée');
  }

  // Récupérer les sessions d'un complexe
  async getSessionsByComplexe(params?: SessionParams): Promise<SessionCaisse[]> {
    this.checkAuth();
    const complexeId = this.getComplexeId();
    
    const response = await api.get<SessionResponse>('/sessions', { 
      params: { ...params, complexeId }
    });
    
    if (response.data.success && Array.isArray(response.data.data)) {
      return response.data.data;
    }
    return [];
  }

  // Vérifier si un point de vente a une session ouverte
  async hasActiveSession(posId: number): Promise<boolean> {
    try {
      const session = await this.getActiveSession(posId);
      return session !== null;
    } catch (error) {
      return false;
    }
  }

  // Ouvrir une session pour un service (utilise le premier POS du service)
  async ouvrirSessionPourService(_serviceId: number, _fondsOuverture: number, _notes?: string): Promise<SessionCaisse> {
    // Cette méthode nécessite d'importer le service POS
    // Pour l'instant, on lance une erreur indiquant qu'il faut d'abord créer un POS
    throw new Error('Veuillez d\'abord créer un point de vente pour ce service');
  }
}

export const sessionCaisseService = new SessionCaisseService();
