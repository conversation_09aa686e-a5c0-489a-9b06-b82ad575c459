import { ServiceType } from '../types';

// Fonction utilitaire pour combiner les classes CSS (version simplifiée)
export function cn(...inputs: (string | undefined | null | boolean)[]): string {
  return inputs
    .filter(Boolean)
    .join(' ')
    .trim();
}

// Fonction pour formater les horaires d'ouverture
export const formatHoraires = (horaires?: Record<string, any>): string => {
  if (!horaires) return 'Non défini';
  
  // Exemple simple - on peut améliorer selon le format exact
  const today = new Date().toLocaleDateString('fr-FR', { weekday: 'long' }).toLowerCase();
  const todayHoraires = horaires[today];
  
  if (todayHoraires && todayHoraires.ouverture && todayHoraires.fermeture) {
    return `${todayHoraires.ouverture} - ${todayHoraires.fermeture}`;
  }
  
  return 'Fermé';
};

// Fonction pour obtenir la couleur du type de service
export const getServiceTypeColor = (type: ServiceType): string => {
  const colors: Record<ServiceType, string> = {
    'Restaurant': 'bg-green-100 text-green-800',
    'Bar': 'bg-purple-100 text-purple-800',
    'Piscine': 'bg-cyan-100 text-cyan-800',
  };

  return colors[type] || 'bg-gray-100 text-gray-800';
};

// Fonction pour obtenir l'icône du type de service
export const getServiceTypeIcon = (type: ServiceType): string => {
  const icons: Record<ServiceType, string> = {
    'Restaurant': 'Utensils',
    'Bar': 'Wine',
    'Piscine': 'Droplets',
  };

  return icons[type] || 'MoreHorizontal';
};

// Fonction pour obtenir le label du type de service
export const getServiceTypeLabel = (type: ServiceType): string => {
  return type;
};

// Fonction pour formater une date
export const formatDate = (date: string | Date | null | undefined): string => {
  if (!date) {
    return 'Non défini';
  }

  const dateObj = typeof date === 'string' ? new Date(date) : date;

  if (!dateObj || isNaN(dateObj.getTime())) {
    return 'Date invalide';
  }

  return dateObj.toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};
