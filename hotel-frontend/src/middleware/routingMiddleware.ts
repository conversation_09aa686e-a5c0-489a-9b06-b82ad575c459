// Middleware pour la gestion du routing et des permissions
interface UserContext {
  isAdmin: boolean;
  isEmployee: boolean;
  employeeType: string | null;
  userId?: string;
  complexeId?: string;
}

interface AccessLog {
  timestamp: Date;
  path: string;
  userContext: UserContext;
  allowed: boolean;
  reason?: string;
}

interface PermissionCache {
  [key: string]: {
    permissions: any;
    timestamp: number;
    ttl: number;
  };
}

class RoutingMiddleware {
  private accessLogs: AccessLog[] = [];
  private permissionCache: PermissionCache = {};
  private isInitialized = false;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_LOGS = 1000;

  // Initialisation du middleware
  initialize(): void {
    if (this.isInitialized) return;

    console.log('[RoutingMiddleware] Initialisation du middleware de routing');
    
    // Nettoyer le cache périodiquement
    setInterval(() => {
      this.cleanCache();
    }, 60000); // Toutes les minutes

    // Nettoyer les logs périodiquement
    setInterval(() => {
      this.cleanLogs();
    }, 300000); // Toutes les 5 minutes

    this.isInitialized = true;
  }

  // Logger un accès à une route
  logAccess(path: string, userContext: UserContext, allowed: boolean = true, reason?: string): void {
    const log: AccessLog = {
      timestamp: new Date(),
      path,
      userContext: { ...userContext },
      allowed,
      reason
    };

    this.accessLogs.push(log);

    // Log en console pour le développement
    if (process.env.NODE_ENV === 'development') {
      console.log(`[RoutingMiddleware] ${allowed ? '✅' : '❌'} ${path}`, {
        user: userContext.employeeType || (userContext.isAdmin ? 'admin' : 'unknown'),
        reason
      });
    }

    // Alerter en cas d'accès refusé
    if (!allowed) {
      this.handleAccessDenied(path, userContext, reason);
    }
  }

  // Vérifier les permissions pour une route
  checkRoutePermissions(path: string, userContext: UserContext): { allowed: boolean; reason?: string } {
    // Routes publiques
    const publicRoutes = ['/login', '/reservation-anonyme', '/errors/403', '/errors/404', '/errors/500'];
    if (publicRoutes.includes(path)) {
      return { allowed: true };
    }

    // Vérifier l'authentification
    if (!userContext.isAdmin && !userContext.isEmployee) {
      return { allowed: false, reason: 'Utilisateur non authentifié' };
    }

    // Routes admin
    const adminRoutes = [
      '/reports',
      '/employee-management',
      '/inventory',
      '/pos-management',
      '/inventaire',
      '/import-excel',
      '/import-history',
      '/import-details',
      '/templates'
    ];

    if (adminRoutes.some(route => path.startsWith(route))) {
      if (!userContext.isAdmin) {
        return { allowed: false, reason: 'Accès administrateur requis' };
      }
    }

    // Routes employé spécifiques
    const employeeRoutes = {
      '/reception': ['reception'],
      '/pool': ['gerant_piscine'],
      '/kitchen': ['cuisine'],
      '/pos': ['serveuse', 'gerant_services', 'cuisine'],
      '/client-reservations': ['reception'],
      '/chambres': ['reception'],
      '/reservation-confirmation': ['reception']
    };

    for (const [route, allowedTypes] of Object.entries(employeeRoutes)) {
      if (path.startsWith(route)) {
        if (userContext.isAdmin) {
          return { allowed: true }; // Les admins ont accès à tout
        }
        
        if (!userContext.isEmployee || !userContext.employeeType) {
          return { allowed: false, reason: 'Type d\'employé non défini' };
        }

        if (!allowedTypes.includes(userContext.employeeType)) {
          return { 
            allowed: false, 
            reason: `Accès réservé aux types: ${allowedTypes.join(', ')}` 
          };
        }
      }
    }

    // Routes mixtes (dashboard, services)
    const mixedRoutes = ['/dashboard', '/services'];
    if (mixedRoutes.includes(path)) {
      return { allowed: true }; // Accessible à tous les utilisateurs authentifiés
    }

    // Par défaut, autoriser l'accès
    return { allowed: true };
  }

  // Obtenir les permissions depuis le cache ou les calculer
  getPermissions(userId: string, userContext: UserContext): any {
    const cacheKey = `${userId}_${userContext.employeeType}_${userContext.isAdmin}`;
    const cached = this.permissionCache[cacheKey];

    if (cached && (Date.now() - cached.timestamp) < cached.ttl) {
      return cached.permissions;
    }

    // Calculer les permissions
    const permissions = this.calculatePermissions(userContext);

    // Mettre en cache
    this.permissionCache[cacheKey] = {
      permissions,
      timestamp: Date.now(),
      ttl: this.CACHE_TTL
    };

    return permissions;
  }

  // Calculer les permissions basées sur le contexte utilisateur
  private calculatePermissions(userContext: UserContext): any {
    const permissions = {
      canAccessDashboard: true,
      canAccessReports: userContext.isAdmin,
      canAccessEmployeeManagement: userContext.isAdmin,
      canAccessInventory: userContext.isAdmin,
      canAccessPOSManagement: userContext.isAdmin,
      canAccessServices: true, // Tous peuvent voir, mais avec des vues différentes
      canAccessReception: userContext.isAdmin || userContext.employeeType === 'reception',
      canAccessPool: userContext.isAdmin || userContext.employeeType === 'gerant_piscine',
      canAccessPOS: userContext.isAdmin || ['serveuse', 'gerant_services', 'cuisine'].includes(userContext.employeeType || ''),
      canAccessKitchen: userContext.isAdmin || userContext.employeeType === 'cuisine',
      canModifySettings: userContext.isAdmin,
      canViewAllData: userContext.isAdmin,
      canManageEmployees: userContext.isAdmin,
      canGenerateReports: userContext.isAdmin
    };

    return permissions;
  }

  // Gérer les accès refusés
  private handleAccessDenied(path: string, userContext: UserContext, reason?: string): void {
    // Log d'alerte
    console.warn(`[RoutingMiddleware] Accès refusé à ${path}`, {
      user: userContext.employeeType || (userContext.isAdmin ? 'admin' : 'unknown'),
      reason
    });

    // En production, on pourrait envoyer une alerte au système de monitoring
    if (process.env.NODE_ENV === 'production') {
      // Exemple: sendSecurityAlert(path, userContext, reason);
    }
  }

  // Nettoyer le cache expiré
  private cleanCache(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, cached] of Object.entries(this.permissionCache)) {
      if ((now - cached.timestamp) > cached.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => {
      delete this.permissionCache[key];
    });

    if (keysToDelete.length > 0) {
      console.log(`[RoutingMiddleware] Cache nettoyé: ${keysToDelete.length} entrées supprimées`);
    }
  }

  // Nettoyer les anciens logs
  private cleanLogs(): void {
    if (this.accessLogs.length > this.MAX_LOGS) {
      const toRemove = this.accessLogs.length - this.MAX_LOGS;
      this.accessLogs.splice(0, toRemove);
      console.log(`[RoutingMiddleware] Logs nettoyés: ${toRemove} entrées supprimées`);
    }
  }

  // Obtenir les statistiques d'accès
  getAccessStats(): {
    totalAccess: number;
    deniedAccess: number;
    topPaths: Array<{ path: string; count: number }>;
    recentDenied: AccessLog[];
  } {
    const totalAccess = this.accessLogs.length;
    const deniedAccess = this.accessLogs.filter(log => !log.allowed).length;

    // Compter les accès par path
    const pathCounts: { [path: string]: number } = {};
    this.accessLogs.forEach(log => {
      pathCounts[log.path] = (pathCounts[log.path] || 0) + 1;
    });

    // Trier par nombre d'accès
    const topPaths = Object.entries(pathCounts)
      .map(([path, count]) => ({ path, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Accès refusés récents
    const recentDenied = this.accessLogs
      .filter(log => !log.allowed)
      .slice(-10)
      .reverse();

    return {
      totalAccess,
      deniedAccess,
      topPaths,
      recentDenied
    };
  }

  // Invalider le cache pour un utilisateur
  invalidateUserCache(userId: string): void {
    const keysToDelete = Object.keys(this.permissionCache).filter(key => 
      key.startsWith(`${userId}_`)
    );

    keysToDelete.forEach(key => {
      delete this.permissionCache[key];
    });

    console.log(`[RoutingMiddleware] Cache invalidé pour l'utilisateur ${userId}`);
  }

  // Obtenir les logs d'accès récents
  getRecentLogs(limit: number = 50): AccessLog[] {
    return this.accessLogs.slice(-limit).reverse();
  }

  // Vérifier si le middleware est initialisé
  isReady(): boolean {
    return this.isInitialized;
  }

  // Réinitialiser le middleware (pour les tests)
  reset(): void {
    this.accessLogs = [];
    this.permissionCache = {};
    this.isInitialized = false;
  }
}

// Instance singleton
export const routingMiddleware = new RoutingMiddleware();

// Types exportés
export type { UserContext, AccessLog, PermissionCache };

// Helpers pour les composants
export const useRoutingMiddleware = () => {
  return {
    logAccess: routingMiddleware.logAccess.bind(routingMiddleware),
    checkPermissions: routingMiddleware.checkRoutePermissions.bind(routingMiddleware),
    getStats: routingMiddleware.getAccessStats.bind(routingMiddleware),
    getRecentLogs: routingMiddleware.getRecentLogs.bind(routingMiddleware)
  };
};
