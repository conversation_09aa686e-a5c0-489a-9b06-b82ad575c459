// Types pour le système d'import Excel

export type TypeImport = 'MENU_RESTAURANT' | 'CARTE_BAR' | 'INVENTAIRE_INGREDIENTS';

export type StatutImport = 'EN_COURS' | 'VALIDE' | 'ERREUR' | 'IMPORTE' | 'ANNULE';

/**
 * Interface pour les imports Excel
 */
export interface ImportExcel {
  import_id: number;
  complexe_id: number;
  service_id?: number;
  employe_id: number;
  type_import: TypeImport;
  nom_fichier: string;
  chemin_fichier: string;
  taille_fichier: number;
  statut: StatutImport;
  nombre_lignes_total: number;
  nombre_lignes_valides: number;
  nombre_erreurs: number;
  donnees_parsees?: any;
  erreurs_detectees?: ImportError[];
  mapping_colonnes?: ColumnMapping;
  parametres_import?: any;
  date_import: string;
  date_traitement?: string;
  date_finalisation?: string;
  notes?: string;
}

/**
 * Interface pour les erreurs d'import
 */
export interface ImportError {
  ligne: number;
  colonne?: string;
  erreur: string;
  valeur_problematique?: any;
  suggestion?: string;
}

/**
 * Interface pour le mapping des colonnes
 */
export interface ColumnMapping {
  [excelColumn: string]: string; // Mapping colonne Excel -> champ DB
}

/**
 * Interface pour les templates d'import
 */
export interface TemplateImport {
  template_id: number;
  chaine_id: number;
  type_service: string; // ServiceType from existing types
  type_import: TypeImport;
  nom_template: string;
  description?: string;
  colonnes_requises: TemplateColumn[];
  exemple_donnees: any[];
  regles_validation: ValidationRule[];
  mapping_defaut: ColumnMapping;
  actif: boolean;
  version: string;
  created_at: string;
  updated_at?: string;
}

/**
 * Interface pour les colonnes de template
 */
export interface TemplateColumn {
  nom: string;
  type: 'string' | 'number' | 'date' | 'boolean';
  obligatoire: boolean;
  description: string;
  exemple?: any;
}

/**
 * Interface pour les règles de validation
 */
export interface ValidationRule {
  champ: string;
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom';
  valeur?: any;
  message: string;
}

/**
 * Interface pour la prévisualisation d'import
 */
export interface ImportPreview {
  donnees_valides: any[];
  erreurs: ImportError[];
  statistiques: {
    total_lignes: number;
    lignes_valides: number;
    lignes_erreur: number;
    taux_succes: number;
  };
  suggestions: string[];
}

/**
 * Interface pour les filtres d'import
 */
export interface ImportFilters {
  type_import?: TypeImport;
  statut?: StatutImport;
  date_debut?: string;
  date_fin?: string;
  page?: number;
  limit?: number;
}

/**
 * Interface pour les informations de pagination
 */
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}
