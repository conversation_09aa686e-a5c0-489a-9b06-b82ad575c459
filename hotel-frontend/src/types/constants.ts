// Constantes pour le système d'inventaire et import Excel

import type {
  UniteMesure,
  CategorieIngredient,
  TypeConservation
} from './inventaire.types';
import type { TypeImport } from './import.types';

/**
 * Options pour les unités de mesure
 */
export const UNITE_MESURE_OPTIONS: { value: UniteMesure; label: string }[] = [
  { value: 'kg', label: 'Kilogramme (kg)' },
  { value: 'g', label: 'Gramme (g)' },
  { value: 'L', label: 'Litre (L)' },
  { value: 'mL', label: 'Millilitre (mL)' },
  { value: 'unité', label: 'Unité' },
  { value: 'pièce', label: 'Pièce' },
  { value: 'portion', label: 'Portion' }
];

/**
 * Options pour les catégories d'ingrédients
 */
export const CATEGORIE_INGREDIENT_OPTIONS: { value: CategorieIngredient; label: string }[] = [
  { value: 'Légumes', label: 'Légumes' },
  { value: 'Viandes', label: 'Viandes' },
  { value: 'Poissons', label: 'Poissons' },
  { value: 'Boissons', label: 'Boissons' },
  { value: 'Épices', label: 'Épices' },
  { value: 'Produits laitiers', label: 'Produits laitiers' },
  { value: 'Céréales', label: 'Céréales' },
  { value: 'Fruits', label: 'Fruits' }
];

/**
 * Options pour les types de conservation
 */
export const TYPE_CONSERVATION_OPTIONS: { value: TypeConservation; label: string }[] = [
  { value: 'Frais', label: 'Frais' },
  { value: 'Congelé', label: 'Congelé' },
  { value: 'Sec', label: 'Sec' },
  { value: 'Ambiant', label: 'Ambiant' }
];

/**
 * Options pour les types d'import
 */
export const TYPE_IMPORT_OPTIONS: { value: TypeImport; label: string }[] = [
  { value: 'MENU_RESTAURANT', label: 'Menu Restaurant' },
  { value: 'CARTE_BAR', label: 'Carte Bar' },
  { value: 'INVENTAIRE_INGREDIENTS', label: 'Inventaire Ingrédients' }
];

/**
 * Configuration pour l'upload de fichiers
 */
export const FILE_UPLOAD_CONFIG = {
  MAX_SIZE_MB: 10,
  ACCEPTED_TYPES: [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.ms-excel' // .xls
  ],
  ACCEPTED_EXTENSIONS: ['.xlsx', '.xls']
};

/**
 * Messages d'erreur standardisés
 */
export const ERROR_MESSAGES = {
  FILE_TOO_LARGE: 'Le fichier est trop volumineux. Taille maximum autorisée : 10MB',
  INVALID_FILE_TYPE: 'Type de fichier non supporté. Seuls les fichiers Excel (.xlsx, .xls) sont acceptés',
  UPLOAD_FAILED: 'Erreur lors de l\'upload du fichier',
  VALIDATION_FAILED: 'Erreur lors de la validation des données',
  IMPORT_FAILED: 'Erreur lors de l\'import des données',
  NETWORK_ERROR: 'Erreur de connexion. Veuillez réessayer',
  UNAUTHORIZED: 'Vous n\'avez pas les permissions nécessaires pour cette action',
  NOT_FOUND: 'Ressource non trouvée',
  SERVER_ERROR: 'Erreur serveur. Veuillez contacter l\'administrateur'
};

/**
 * Messages de succès standardisés
 */
export const SUCCESS_MESSAGES = {
  FILE_UPLOADED: 'Fichier uploadé avec succès',
  IMPORT_COMPLETED: 'Import terminé avec succès',
  INGREDIENT_CREATED: 'Ingrédient créé avec succès',
  INGREDIENT_UPDATED: 'Ingrédient mis à jour avec succès',
  INGREDIENT_DELETED: 'Ingrédient supprimé avec succès',
  RECETTE_CREATED: 'Recette créée avec succès',
  RECETTE_UPDATED: 'Recette mise à jour avec succès',
  RECETTE_DELETED: 'Recette supprimée avec succès',
  STOCK_UPDATED: 'Stock mis à jour avec succès',
  COST_CALCULATED: 'Coût recalculé avec succès'
};

/**
 * Configuration des couleurs pour les statuts
 */
export const STATUS_COLORS = {
  EN_COURS: 'bg-blue-100 text-blue-800',
  VALIDE: 'bg-green-100 text-green-800',
  ERREUR: 'bg-red-100 text-red-800',
  IMPORTE: 'bg-green-100 text-green-800',
  ANNULE: 'bg-gray-100 text-gray-800'
};

/**
 * Configuration des couleurs pour les alertes de stock
 */
export const ALERT_COLORS = {
  Faible: 'bg-blue-100 text-blue-800',
  Moyenne: 'bg-yellow-100 text-yellow-800',
  Haute: 'bg-orange-100 text-orange-800',
  Critique: 'bg-red-100 text-red-800'
};

/**
 * Configuration des couleurs pour la rentabilité
 */
export const RENTABILITE_COLORS = {
  Faible: 'bg-red-100 text-red-800',
  Moyenne: 'bg-yellow-100 text-yellow-800',
  Bonne: 'bg-green-100 text-green-800',
  Excellente: 'bg-emerald-100 text-emerald-800'
};

/**
 * Configuration de pagination par défaut
 */
export const DEFAULT_PAGINATION = {
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 0
};

/**
 * Intervalles de polling pour le suivi des imports
 */
export const POLLING_INTERVALS = {
  IMPORT_STATUS: 2000, // 2 secondes
  STOCK_ALERTS: 30000, // 30 secondes
  COST_UPDATES: 60000  // 1 minute
};
