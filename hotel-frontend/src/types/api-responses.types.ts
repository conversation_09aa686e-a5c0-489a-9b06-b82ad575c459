// Types pour les réponses API du système d'inventaire et import

import {
  Ingredient,
  StockIngredient,
  StockAlert
} from './inventaire.types';
import { 
  ImportExcel, 
  ImportPreview, 
  TemplateImport, 
  PaginationInfo 
} from './import.types';

/**
 * Interface de base pour les réponses API
 */
export interface BaseApiResponse {
  success: boolean;
  message?: string;
}

/**
 * Réponses API pour les ingrédients
 */
export interface IngredientResponse extends BaseApiResponse {
  data: Ingredient | Ingredient[];
  pagination?: PaginationInfo;
}



/**
 * Réponses API pour les imports
 */
export interface ImportResponse extends BaseApiResponse {
  data: ImportExcel | ImportExcel[];
  preview?: ImportPreview;
}

/**
 * Réponses API pour le stock
 */
export interface StockResponse extends BaseApiResponse {
  data: StockIngredient | StockIngredient[];
  alerts?: StockAlert[];
}

/**
 * Réponses API pour les templates
 */
export interface TemplateResponse extends BaseApiResponse {
  data: TemplateImport | TemplateImport[];
}

/**
 * Réponses API pour les analyses d'inventaire
 */
export interface InventaireAnalyticsResponse extends BaseApiResponse {
  data: {
    valeur_totale_stock: number;
    nombre_ingredients: number;
    alertes_actives: number;
    evolution_couts: {
      periode: string;
      valeur: number;
    }[];
    top_ingredients_couteux: {
      ingredient: Ingredient;
      valeur_stock: number;
    }[];
    categories_repartition: {
      categorie: string;
      valeur: number;
      pourcentage: number;
    }[];
  };
}

/**
 * Réponses API pour les calculs de coûts
 */
export interface CostCalculationResponse extends BaseApiResponse {
  data: {
    cout_total: number;
    cout_par_portion: number;
    ingredients_detail: {
      ingredient_id: number;
      nom: string;
      quantite: number;
      unite: string;
      prix_unitaire: number;
      cout_total: number;
    }[];
  };
}

/**
 * Réponses API pour l'optimisation de prix
 */
export interface PriceOptimizationResponse extends BaseApiResponse {
  data: {
    prix_actuel: number;
    prix_suggere: number;
    marge_actuelle: number;
    marge_cible: number;
    impact_benefice: number;
    recommandations: string[];
  };
}

/**
 * Réponses API pour les rapports d'import
 */
export interface ImportReportResponse extends BaseApiResponse {
  data: {
    import_id: number;
    resume: {
      total_lignes: number;
      lignes_importees: number;
      lignes_erreur: number;
      taux_succes: number;
    };
    details_erreurs: {
      ligne: number;
      erreur: string;
      donnee_problematique: any;
    }[];
    impact: {
      produits_crees: number;

      ingredients_crees: number;
      prix_mis_a_jour: number;
    };
    duree_traitement: number;
  };
}

/**
 * Réponses API pour la recherche
 */
export interface SearchResponse<T> extends BaseApiResponse {
  data: T[];
  total: number;
  query: string;
  suggestions?: string[];
}

/**
 * Type générique pour les réponses avec pagination
 */
export interface PaginatedResponse<T> extends BaseApiResponse {
  data: T[];
  pagination: PaginationInfo;
}

/**
 * Réponses API pour les opérations de stock
 */
export interface StockOperationResponse extends BaseApiResponse {
  data: {
    stock_avant: number;
    stock_apres: number;
    mouvement_id: number;
    valeur_mouvement: number;
    type_operation: 'ENTREE' | 'SORTIE' | 'AJUSTEMENT';
  };
}
