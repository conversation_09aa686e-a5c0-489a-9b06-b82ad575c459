// Types pour le système d'inventaire et gestion des ingrédients

export type UniteMesure = 'kg' | 'g' | 'L' | 'mL' | 'unité' | 'pièce' | 'portion';

export type CategorieIngredient = 
  | 'Légumes' 
  | 'Viandes' 
  | 'Poissons' 
  | 'Boissons' 
  | 'Épices' 
  | 'Produits laitiers' 
  | 'Céréales' 
  | 'Fruits';

export type TypeConservation = 'Frais' | 'Congelé' | 'Sec' | 'Ambiant';

/**
 * Interface pour les ingrédients
 */
export interface Ingredient {
  ingredient_id: number;
  chaine_id: number;
  complexe_id?: number;
  nom: string;
  description?: string;
  unite_mesure: UniteMesure;
  categorie: CategorieIngredient;
  code_barre?: string;
  prix_unitaire_moyen: number;
  fournisseur_principal_id?: number;
  allergenes?: string[];
  conservation: TypeConservation;
  duree_conservation_jours?: number;
  actif: boolean;
  created_at: string;
  updated_at?: string;
}



/**
 * Interface pour le stock des ingrédients
 */
export interface StockIngredient {
  stock_id: number;
  ingredient_id: number;
  ingredient?: Ingredient;
  complexe_id: number;
  service_id?: number;
  quantite_actuelle: number;
  quantite_minimale: number;
  quantite_maximale?: number;
  emplacement_stockage?: string;
  date_derniere_maj: string;
  valeur_stock: number;
  prix_unitaire_actuel: number;
}

/**
 * Interface pour les filtres d'ingrédients
 */
export interface IngredientFilters {
  categorie?: CategorieIngredient;
  conservation?: TypeConservation;
  actif?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}



/**
 * Interface pour les alertes de stock
 */
export interface StockAlert {
  type: 'STOCK_FAIBLE' | 'EXPIRATION_PROCHE' | 'RUPTURE';
  ingredient: Ingredient;
  stock_actuel: number;
  stock_minimal: number;
  jours_restants?: number;
  urgence: 'Faible' | 'Moyenne' | 'Haute' | 'Critique';
}


