export interface Service {
  service_id: number;
  complexe_id: number;
  type_service: ServiceType;
  nom: string;
  description?: string;
  emplacement?: string;
  horaires_ouverture?: Record<string, any>;
  capacite?: number;
  image_url?: string;
  contact_email?: string;
  contact_telephone?: string;
  actif: boolean;
  configuration?: Record<string, any>;
  tarification?: Record<string, any>;
  created_at: string;
  updated_at?: string;
}

export type ServiceType =
  | 'Restaurant'
  | 'Bar'
  | 'Piscine';

export interface ServiceTypeOption {
  value: ServiceType;
  label: string;
  icon: string;
}

export const SERVICE_TYPES: ServiceTypeOption[] = [
  { value: 'Restaurant', label: 'Restaurant', icon: 'utensils' },
  { value: 'Bar', label: 'Bar', icon: 'wine' },
  { value: 'Piscine', label: 'Piscine', icon: 'droplets' },
];

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// ===== TYPES POUR LE SYSTÈME D'INVENTAIRE ET IMPORT EXCEL =====

// Types d'inventaire
export type {
  UniteMesure,
  CategorieIngredient,
  TypeConservation,
  Ingredient,
  StockIngredient,
  IngredientFilters,
  StockAlert,
} from './inventaire.types';

// Types d'import
export type {
  TypeImport,
  StatutImport,
  ImportExcel,
  ImportError,
  ColumnMapping,
  TemplateImport,
  TemplateColumn,
  ValidationRule,
  ImportPreview,
  ImportFilters,
  PaginationInfo
} from './import.types';

// Types de réponses API
export type {
  BaseApiResponse,
  IngredientResponse,

  ImportResponse,
  StockResponse,
  TemplateResponse,
  InventaireAnalyticsResponse,
  CostCalculationResponse,
  PriceOptimizationResponse,
  ImportReportResponse,
  SearchResponse,
  StockOperationResponse
} from './api-responses.types';

// ===== TYPES UTILITAIRES POUR LES COMPOSANTS =====

export interface FileUploadProps {
  onFileSelect: (file: File) => void;
  acceptedTypes: string[];
  maxSize: number; // en MB
  disabled?: boolean;
  loading?: boolean;
  error?: string;
}

export interface ImportStepperProps {
  currentStep: 'upload' | 'preview' | 'processing' | 'complete';
}

export interface DataTableColumn<T = any> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  render?: (value: any, row: T) => React.ReactNode;
  width?: string;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
}

// ===== TYPES POUR LES HOOKS PERSONNALISÉS =====

import type {
  ImportExcel,
  TypeImport,
  PaginationInfo
} from './import.types';
import type {
  Ingredient,
  IngredientFilters
} from './inventaire.types';

export interface UseImportState {
  currentImport: ImportExcel | null;
  loading: boolean;
  error: string | null;
  uploadFile: (file: File, type: TypeImport, serviceId?: number) => Promise<void>;
  validateImport: (importId: number) => Promise<void>;
  processImport: (importId: number) => Promise<void>;
  reset: () => void;
}

export interface UseInventaireState {
  ingredients: Ingredient[];
  loading: boolean;
  error: string | null;
  filters: IngredientFilters;
  pagination: PaginationInfo;
  loadIngredients: () => Promise<void>;
  createIngredient: (data: Partial<Ingredient>) => Promise<void>;
  updateIngredient: (id: number, data: Partial<Ingredient>) => Promise<void>;
  deleteIngredient: (id: number) => Promise<void>;
  setFilters: (filters: IngredientFilters) => void;
}



// ===== CONSTANTES =====

export {
  UNITE_MESURE_OPTIONS,
  CATEGORIE_INGREDIENT_OPTIONS,
  TYPE_CONSERVATION_OPTIONS,
  TYPE_IMPORT_OPTIONS,
  FILE_UPLOAD_CONFIG,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  STATUS_COLORS,
  ALERT_COLORS,
  RENTABILITE_COLORS,
  DEFAULT_PAGINATION,
  POLLING_INTERVALS
} from './constants';