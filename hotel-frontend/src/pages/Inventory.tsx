import React from 'react';
import { Inventory as InventoryComponent } from '../components/Inventory';
import { useNavigate } from 'react-router-dom';

function Inventory() {
  const navigate = useNavigate();

  const handleClose = () => {
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-6">
        <InventoryComponent onClose={handleClose} />
      </div>
    </div>
  );
}

export default Inventory; 