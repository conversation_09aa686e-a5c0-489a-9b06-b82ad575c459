import React, { useState, useEffect } from 'react';
import { Search, Plus, Pencil, Trash2, BedDouble, X } from 'lucide-react';
import { Chambre, chambreService } from '../services/chambre.service';
import ChambreForm from '../components/ChambreForm';
import { authService } from '../services/auth.service';
import { useNavigate } from 'react-router-dom';

const Chambres: React.FC = () => {
  const navigate = useNavigate();
  const [chambres, setChambres] = useState<Chambre[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [selectedChambre, setSelectedChambre] = useState<Chambre | undefined>();
  const [filterType, setFilterType] = useState<string>('');
  const [filterStatut, setFilterStatut] = useState<string>('');

  const handleClose = () => {
    navigate('/dashboard');
  };

  useEffect(() => {
    // Vérifier l'authentification
    if (!authService.isAuthenticated()) {
      navigate('/login');
      return;
    }

    // Vérifier si un complexe est sélectionné pour les admins de chaîne
    if (authService.isAdminChaine()) {
      const selectedComplexeId = localStorage.getItem('selectedComplexeId');
      if (!selectedComplexeId) {
        navigate('/patron/complexes');
        return;
      }
    }

    loadChambres();
  }, [navigate]);

  const loadChambres = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await chambreService.getChambres();
      if (response.success && response.data) {
        setChambres(response.data.chambres || []);
      } else {
        throw new Error(response.message || 'Erreur lors du chargement des chambres');
      }
    } catch (err: any) {
      console.error('Erreur lors du chargement des chambres:', err);
      setError(err.message || 'Erreur lors du chargement des chambres');
      setChambres([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateChambre = async (data: any) => {
    try {
      const response = await chambreService.createChambre(data);
      if (response.success) {
        setShowForm(false);
        loadChambres();
      } else {
        throw new Error(response.message || 'Erreur lors de la création de la chambre');
      }
    } catch (err: any) {
      throw new Error(err.message || 'Erreur lors de la création de la chambre');
    }
  };

  const handleUpdateChambre = async (data: any) => {
    if (!selectedChambre) return;
    try {
      const response = await chambreService.updateChambre(selectedChambre.chambre_id, data);
      if (response.success) {
        setShowForm(false);
        setSelectedChambre(undefined);
        loadChambres();
      } else {
        throw new Error(response.message || 'Erreur lors de la mise à jour de la chambre');
      }
    } catch (err: any) {
      throw new Error(err.message || 'Erreur lors de la mise à jour de la chambre');
    }
  };

  const handleDeleteChambre = async (chambreId: string) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer cette chambre ?')) return;
    try {
      const response = await chambreService.deleteChambre(chambreId);
      if (response.success) {
        loadChambres();
      } else {
        throw new Error(response.message || 'Erreur lors de la suppression de la chambre');
      }
    } catch (err: any) {
      setError(err.message || 'Erreur lors de la suppression de la chambre');
    }
  };

  const filteredChambres = chambres.filter(chambre => {
    const matchesSearch = chambre.numero.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         chambre.type_chambre.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = !filterType || chambre.type_chambre === filterType;
    const matchesStatut = !filterStatut || chambre.statut === filterStatut;
    return matchesSearch && matchesType && matchesStatut;
  });

  const getStatutColor = (statut: string) => {
    switch (statut) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800';
      case 'verrouillee': return 'bg-red-100 text-red-800';
      case 'occupee': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF'
    }).format(price);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement des chambres...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <p className="text-red-600">{error}</p>
          <button
            onClick={loadChambres}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Réessayer
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Gestion des Chambres</h1>
        <div className="flex items-center gap-4">
          <button
            onClick={() => {
              setSelectedChambre(undefined);
              setShowForm(true);
            }}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Plus className="h-5 w-5 mr-2" />
            Nouvelle Chambre
          </button>
          <button
            onClick={handleClose}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <X className="h-5 w-5 mr-2" />
            Fermer
          </button>
        </div>
      </div>

      {showForm && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-semibold mb-4">
              {selectedChambre ? 'Modifier la Chambre' : 'Nouvelle Chambre'}
            </h2>
            <ChambreForm
              chambre={selectedChambre}
              onSubmit={selectedChambre ? handleUpdateChambre : handleCreateChambre}
              onCancel={() => {
                setShowForm(false);
                setSelectedChambre(undefined);
              }}
            />
          </div>
        </div>
      )}

      <div className="bg-white shadow rounded-lg p-6">
        <div className="mb-6 space-y-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Rechercher une chambre..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
            </div>
            <div className="flex gap-4">
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="">Tous les types</option>
                <option value="standard">Standard</option>
                <option value="VIP">VIP</option>
                <option value="suite">Suite</option>
              </select>
              <select
                value={filterStatut}
                onChange={(e) => setFilterStatut(e.target.value)}
                className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="">Tous les statuts</option>
                <option value="active">Active</option>
                <option value="maintenance">Maintenance</option>
                <option value="verrouillee">Verrouillée</option>
                <option value="occupee">Occupée</option>
              </select>
            </div>
          </div>
        </div>

        {filteredChambres.length === 0 ? (
          <div className="text-center py-4 text-gray-500">
            Aucune chambre trouvée
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Numéro
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Statut
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Capacité
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Prix/Heure
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Étage
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredChambres.map((chambre) => (
                  <tr key={chambre.chambre_id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {chambre.numero}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {chambre.type_chambre}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatutColor(chambre.statut)}`}>
                        {chambre.statut}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {chambre.capacite} personnes
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatPrice(chambre.prix_base)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {chambre.etage || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-3">
                        <button
                          onClick={() => {
                            setSelectedChambre(chambre);
                            setShowForm(true);
                          }}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Pencil className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => handleDeleteChambre(chambre.chambre_id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-5 w-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default Chambres; 