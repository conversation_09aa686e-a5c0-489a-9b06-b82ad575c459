import React from 'react';
import { useNavigate } from 'react-router-dom';
import { KitchenInterface } from '../components/employee';
import { EmployeeAccessGuard } from '../components/guards/EmployeeAccessGuard';

function Kitchen() {
  const navigate = useNavigate();

  const handleClose = () => {
    navigate('/dashboard');
  };

  return (
    <EmployeeAccessGuard 
      requiredType="cuisine" 
      allowAdmin={true}
    >
      <KitchenInterface onClose={handleClose} />
    </EmployeeAccessGuard>
  );
}

export default Kitchen;
