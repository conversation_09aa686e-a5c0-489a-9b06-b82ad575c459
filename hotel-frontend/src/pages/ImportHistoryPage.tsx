import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Download, Eye, RotateCcw, Filter, Calendar, FileText, AlertCircle, CheckCircle, ArrowLeft } from 'lucide-react';
import { authService } from '../services/auth.service';
import { importService } from '../services';
import type { ImportExcel, ImportFilters, TypeImport, StatutImport } from '../types';

const STATUT_COLORS = {
  'EN_COURS': 'bg-blue-100 text-blue-800',
  'VALIDE': 'bg-green-100 text-green-800',
  'ERREUR': 'bg-red-100 text-red-800',
  'IMPORTE': 'bg-green-100 text-green-800',
  'ANNULE': 'bg-gray-100 text-gray-800'
};

const STATUT_ICONS = {
  'EN_COURS': <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>,
  'VALIDE': <CheckCircle className="h-4 w-4" />,
  'ERREUR': <AlertCircle className="h-4 w-4" />,
  'IMPORTE': <CheckCircle className="h-4 w-4" />,
  'ANNULE': <AlertCircle className="h-4 w-4" />
};

const TYPE_IMPORT_LABELS = {
  'MENU_RESTAURANT': 'Menu Restaurant',
  'CARTE_BAR': 'Carte Bar',
  'INVENTAIRE_INGREDIENTS': 'Inventaire Ingrédients'
};

export const ImportHistoryPage: React.FC = () => {
  const navigate = useNavigate();
  const complexeId = authService.getComplexeId();

  const [imports, setImports] = useState<ImportExcel[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState<ImportFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [selectedImport, setSelectedImport] = useState<ImportExcel | null>(null);

  // Vérification de l'authentification
  useEffect(() => {
    if (!authService.isAuthenticated()) {
      navigate('/login');
      return;
    }

    if (authService.isAdminChaine()) {
      const selectedComplexeId = localStorage.getItem('selectedComplexeId');
      if (!selectedComplexeId) {
        navigate('/complexes');
        return;
      }
    }
  }, [navigate]);

  useEffect(() => {
    if (complexeId) {
      loadImports();
    }
  }, [complexeId, filters]);

  const loadImports = async () => {
    if (!complexeId) return;

    try {
      setLoading(true);
      const data = await importService.getImportHistory(complexeId, filters);
      setImports(data);
    } catch (error) {
      console.error('Erreur lors du chargement de l\'historique:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadReport = async (importId: number, format: 'pdf' | 'excel' = 'pdf') => {
    try {
      const report = await importService.getImportReport(importId, format);
      
      // Créer un lien de téléchargement
      const blob = new Blob([report], { 
        type: format === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `rapport-import-${importId}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Erreur lors du téléchargement du rapport:', error);
    }
  };

  const handleRollback = async (importId: number) => {
    if (!confirm('Êtes-vous sûr de vouloir annuler cet import ? Cette action est irréversible.')) {
      return;
    }

    try {
      await importService.rollbackImport(importId);
      await loadImports(); // Recharger la liste
    } catch (error) {
      console.error('Erreur lors de l\'annulation de l\'import:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('fr-FR');
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getSuccessRate = (importData: ImportExcel) => {
    if (importData.nombre_lignes_total === 0) return 0;
    return (importData.nombre_lignes_valides / importData.nombre_lignes_total) * 100;
  };

  if (!complexeId) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Accès non autorisé
          </h2>
          <p className="text-gray-600">
            Vous devez être connecté à un complexe pour accéder à cette page.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/dashboard')}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  Historique des Imports
                </h1>
                <p className="text-gray-600">
                  Consultez l'historique de tous vos imports Excel
                </p>
              </div>
            </div>

            <button
              onClick={() => setShowFilters(!showFilters)}
              className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filtres
            </button>
          </div>
        </div>

      {/* Filtres */}
      {showFilters && (
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Filtres</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Type d'import
              </label>
              <select
                value={filters.type_import || ''}
                onChange={(e) => setFilters({ ...filters, type_import: e.target.value as TypeImport || undefined })}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Tous les types</option>
                <option value="MENU_RESTAURANT">Menu Restaurant</option>
                <option value="CARTE_BAR">Carte Bar</option>
                <option value="INVENTAIRE_INGREDIENTS">Inventaire Ingrédients</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Statut
              </label>
              <select
                value={filters.statut || ''}
                onChange={(e) => setFilters({ ...filters, statut: e.target.value as StatutImport || undefined })}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Tous les statuts</option>
                <option value="EN_COURS">En cours</option>
                <option value="VALIDE">Validé</option>
                <option value="IMPORTE">Importé</option>
                <option value="ERREUR">Erreur</option>
                <option value="ANNULE">Annulé</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date début
              </label>
              <input
                type="date"
                value={filters.date_debut || ''}
                onChange={(e) => setFilters({ ...filters, date_debut: e.target.value || undefined })}
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date fin
              </label>
              <input
                type="date"
                value={filters.date_fin || ''}
                onChange={(e) => setFilters({ ...filters, date_fin: e.target.value || undefined })}
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div className="flex justify-end mt-4 space-x-3">
            <button
              onClick={() => setFilters({})}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              Réinitialiser
            </button>
          </div>
        </div>
      )}

      {/* Liste des imports */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : imports.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p>Aucun import trouvé</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Fichier
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Statut
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Résultats
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {imports.map((importData) => (
                  <tr key={importData.import_id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <FileText className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {importData.nom_fichier}
                          </div>
                          <div className="text-sm text-gray-500">
                            {formatFileSize(importData.taille_fichier)}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                        {TYPE_IMPORT_LABELS[importData.type_import]}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className={`inline-flex items-center px-2 py-1 text-xs rounded-full ${STATUT_COLORS[importData.statut]}`}>
                          <span className="mr-1">{STATUT_ICONS[importData.statut]}</span>
                          {importData.statut}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>
                        <div className="flex items-center">
                          <span className="text-green-600 font-medium">
                            {importData.nombre_lignes_valides}
                          </span>
                          <span className="text-gray-500 mx-1">/</span>
                          <span>{importData.nombre_lignes_total}</span>
                        </div>
                        <div className="text-xs text-gray-500">
                          {getSuccessRate(importData).toFixed(1)}% réussite
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(importData.date_import)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => setSelectedImport(importData)}
                          className="text-blue-600 hover:text-blue-900"
                          title="Voir détails"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDownloadReport(importData.import_id)}
                          className="text-green-600 hover:text-green-900"
                          title="Télécharger rapport"
                        >
                          <Download className="h-4 w-4" />
                        </button>
                        {importData.statut === 'IMPORTE' && (
                          <button
                            onClick={() => handleRollback(importData.import_id)}
                            className="text-red-600 hover:text-red-900"
                            title="Annuler import"
                          >
                            <RotateCcw className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      </div>
    </div>
  );
};
