import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Reservation } from '../services/reservation.service';

export function ReservationConfirmation() {
  const navigate = useNavigate();
  const location = useLocation();
  const [reservation, setReservation] = useState<Reservation | null>(null);

  useEffect(() => {
    // Récupérer les données de la réservation depuis l'état de navigation
    if (location.state?.reservation) {
      setReservation(location.state.reservation);
    } else {
      // Si pas de données, rediriger vers la page d'accueil
      navigate('/');
    }
  }, [location.state, navigate]);

  if (!reservation) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"
    >
      <div className="max-w-3xl mx-auto">
        <div className="bg-white shadow-xl rounded-lg overflow-hidden">
          {/* En-tête */}
          <div className="bg-blue-600 px-6 py-8 text-center">
            <h1 className="text-3xl font-bold text-white mb-2">
              Réservation Confirmée !
            </h1>
            <p className="text-blue-100">
              Votre demande de réservation a été enregistrée avec succès
            </p>
          </div>

          {/* Corps */}
          <div className="px-6 py-8">
            {/* Numéro de réservation */}
            <div className="text-center mb-8">
              <p className="text-sm text-gray-500 mb-1">Numéro de réservation</p>
              <p className="text-2xl font-bold text-gray-900">{reservation.numero_reservation}</p>
            </div>

            {/* QR Code */}
            {reservation.qr_code && (
              <div className="flex flex-col items-center mb-8">
                <img
                  src={reservation.qr_code}
                  alt="QR Code"
                  className="w-48 h-48 border-2 border-gray-200 rounded-lg p-2"
                />
                <p className="text-sm text-gray-600 mt-2">
                  Présentez ce QR code à la réception pour confirmer votre réservation
                </p>
              </div>
            )}

            {/* Détails de la réservation */}
            <div className="border-t border-gray-200 pt-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Détails de la réservation</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Date et heure */}
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Date et heure</h3>
                  <p className="text-gray-900">
                    {new Date(reservation.date_arrivee).toLocaleDateString('fr-FR')}
                  </p>
                  <p className="text-gray-900">
                    De {reservation.heure_debut} à {reservation.heure_fin}
                  </p>
                </div>

                {/* Chambre */}
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Chambre</h3>
                  {reservation.chambres?.map((chambre) => (
                    <div key={chambre.chambre_id}>
                      <p className="text-gray-900">Chambre {chambre.numero}</p>
                      <p className="text-gray-900">{chambre.type_chambre}</p>
                    </div>
                  ))}
                </div>

                {/* Client */}
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Client</h3>
                  {reservation.client && (
                    <>
                      <p className="text-gray-900">
                        {reservation.client.prenom} {reservation.client.nom}
                      </p>
                      <p className="text-gray-900">{reservation.client.telephone}</p>
                      <p className="text-gray-900">{reservation.client.email}</p>
                    </>
                  )}
                </div>

                {/* Montant */}
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Montant</h3>
                  <p className="text-gray-900 font-semibold">
                    {reservation.montant_total.toLocaleString('fr-FR')} FCFA
                  </p>
                </div>
              </div>
            </div>

            {/* Commentaires */}
            {reservation.commentaires && (
              <div className="mt-6 border-t border-gray-200 pt-6">
                <h3 className="text-sm font-medium text-gray-500 mb-2">Commentaires</h3>
                <p className="text-gray-900">{reservation.commentaires}</p>
              </div>
            )}

            {/* Boutons d'action */}
            <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => navigate('/')}
                className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Retour à l'accueil
              </button>
              <button
                onClick={() => window.print()}
                className="px-6 py-3 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
              >
                Imprimer la confirmation
              </button>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
} 