import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { authService } from '../services/auth.service';
import {
  ArrowLeft,
  FileText,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RotateCcw,
  Download,
  Clock,
  Calendar,
  Database
} from 'lucide-react';
import { importService } from '../services';
import type { ImportExcel, TypeImport, StatutImport } from '../types';

export const ImportDetailsPage: React.FC = () => {
  const navigate = useNavigate();
  const { importId } = useParams<{ importId: string }>();
  const complexeId = authService.getComplexeId();

  // État des données
  const [importData, setImportData] = useState<ImportExcel | null>(null);
  const [loading, setLoading] = useState(false);
  const [processing, setProcessing] = useState(false);

  // Vérification de l'authentification
  useEffect(() => {
    if (!authService.isAuthenticated()) {
      navigate('/login');
      return;
    }

    if (authService.needsComplexeSelection()) {
      navigate('/complexes');
      return;
    }
  }, [navigate]);

  // Charger les détails de l'import
  useEffect(() => {
    if (importId && complexeId) {
      loadImportDetails();
    }
  }, [importId, complexeId]);

  const loadImportDetails = async () => {
    if (!importId) return;

    try {
      setLoading(true);
      const details = await importService.getImportDetails(parseInt(importId));
      setImportData(details);
    } catch (error) {
      console.error('Erreur lors du chargement des détails:', error);
      toast.error('Erreur lors du chargement des détails de l\'import');
      navigate('/import-history');
    } finally {
      setLoading(false);
    }
  };

  const handleRollback = async () => {
    if (!importData || !confirm('Êtes-vous sûr de vouloir annuler cet import ? Cette action est irréversible.')) {
      return;
    }

    try {
      setProcessing(true);
      await importService.rollbackImport(importData.import_id);
      toast.success('Import annulé avec succès');
      await loadImportDetails();
    } catch (error) {
      console.error('Erreur lors de l\'annulation:', error);
      toast.error('Erreur lors de l\'annulation de l\'import');
    } finally {
      setProcessing(false);
    }
  };



  const handleDownloadReport = async (format: 'pdf' | 'excel' = 'pdf') => {
    if (!importData) return;

    try {
      const report = await importService.getImportReport(importData.import_id, format);
      
      // Créer un lien de téléchargement
      const blob = new Blob([report], { 
        type: format === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `rapport-import-${importData.import_id}.${format === 'pdf' ? 'pdf' : 'xlsx'}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast.success('Rapport téléchargé avec succès');
    } catch (error) {
      console.error('Erreur lors du téléchargement:', error);
      toast.error('Erreur lors du téléchargement du rapport');
    }
  };

  const getStatusIcon = (statut: StatutImport) => {
    switch (statut) {
      case 'IMPORTE':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      case 'ERREUR':
        return <XCircle className="h-6 w-6 text-red-500" />;
      case 'EN_COURS':
      case 'VALIDE':
        return <Clock className="h-6 w-6 text-blue-500" />;
      case 'ANNULE':
        return <RotateCcw className="h-6 w-6 text-gray-500" />;
      default:
        return <AlertTriangle className="h-6 w-6 text-yellow-500" />;
    }
  };

  const getStatusColor = (statut: StatutImport) => {
    switch (statut) {
      case 'IMPORTE':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'ERREUR':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'EN_COURS':
      case 'VALIDE':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'ANNULE':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    }
  };

  const getTypeImportLabel = (type: TypeImport) => {
    switch (type) {
      case 'MENU_RESTAURANT':
        return 'Menu Restaurant';
      case 'CARTE_BAR':
        return 'Carte Bar';
      case 'INVENTAIRE_INGREDIENTS':
        return 'Inventaire Ingrédients';
      default:
        return type;
    }
  };

  const getSuccessRate = () => {
    if (!importData || importData.nombre_lignes_total === 0) return 0;
    return Math.round((importData.nombre_lignes_valides / importData.nombre_lignes_total) * 100);
  };

  if (!authService.hasComplexeAccess()) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Accès non autorisé
          </h2>
          <p className="text-gray-600">
            {authService.needsComplexeSelection()
              ? 'Veuillez sélectionner un complexe pour accéder à cette page.'
              : 'Vous devez être connecté à un complexe pour accéder à cette page.'
            }
          </p>
          {authService.needsComplexeSelection() && (
            <button
              onClick={() => navigate('/patron/complexes')}
              className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Sélectionner un complexe
            </button>
          )}
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!importData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Import non trouvé
          </h2>
          <p className="text-gray-600 mb-4">
            L'import demandé n'existe pas ou vous n'avez pas les permissions pour le consulter.
          </p>
          <button
            onClick={() => navigate('/import-history')}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour à l'historique
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* En-tête avec navigation */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <button
              onClick={() => navigate('/import-history')}
              className="flex items-center text-gray-600 hover:text-gray-800 transition-colors mr-4"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Retour
            </button>
            <h1 className="text-3xl font-bold text-gray-900">
              Détails de l'Import #{importData.import_id}
            </h1>
          </div>
          
          <div className="flex items-center space-x-4">
            {getStatusIcon(importData.statut)}
            <span className={`px-3 py-1 text-sm font-medium rounded-full border ${getStatusColor(importData.statut)}`}>
              {importData.statut}
            </span>
            <span className="text-gray-500">•</span>
            <span className="text-gray-600">
              {getTypeImportLabel(importData.type_import)}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Informations principales */}
          <div className="lg:col-span-2 space-y-6">
            {/* Résumé de l'import */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Résumé de l'Import
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {importData.nombre_lignes_total}
                  </div>
                  <div className="text-sm text-gray-600">Lignes totales</div>
                </div>
                
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {importData.nombre_lignes_valides}
                  </div>
                  <div className="text-sm text-gray-600">Lignes valides</div>
                </div>
                
                <div className="text-center p-4 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">
                    {importData.nombre_erreurs}
                  </div>
                  <div className="text-sm text-gray-600">Erreurs</div>
                </div>
              </div>

              {/* Barre de progression */}
              <div className="mb-4">
                <div className="flex justify-between text-sm text-gray-600 mb-1">
                  <span>Taux de réussite</span>
                  <span>{getSuccessRate()}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${getSuccessRate()}%` }}
                  ></div>
                </div>
              </div>

              {/* Informations du fichier */}
              <div className="border-t pt-4">
                <h3 className="font-medium text-gray-900 mb-3">Informations du fichier</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center">
                    <FileText className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Nom:</span>
                    <span className="ml-2 font-medium">{importData.nom_fichier}</span>
                  </div>
                  <div className="flex items-center">
                    <Database className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Taille:</span>
                    <span className="ml-2 font-medium">
                      {(importData.taille_fichier / (1024 * 1024)).toFixed(2)} MB
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Date d'import:</span>
                    <span className="ml-2 font-medium">
                      {new Date(importData.date_import).toLocaleDateString('fr-FR', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                  {importData.date_traitement && (
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-gray-600">Date de traitement:</span>
                      <span className="ml-2 font-medium">
                        {new Date(importData.date_traitement).toLocaleDateString('fr-FR', {
                          day: '2-digit',
                          month: '2-digit',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Erreurs détaillées */}
            {importData.erreurs_detectees && importData.erreurs_detectees.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Erreurs Détectées ({importData.erreurs_detectees.length})
                </h2>
                
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {importData.erreurs_detectees.map((error, index) => (
                    <div key={index} className="border border-red-200 rounded-lg p-4 bg-red-50">
                      <div className="flex items-start">
                        <XCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
                        <div className="flex-1">
                          <div className="flex items-center mb-1">
                            <span className="text-sm font-medium text-red-800">
                              Ligne {error.ligne}
                            </span>
                            {error.colonne && (
                              <>
                                <span className="text-red-600 mx-2">•</span>
                                <span className="text-sm text-red-700">
                                  Colonne: {error.colonne}
                                </span>
                              </>
                            )}
                          </div>
                          <p className="text-sm text-red-700 mb-2">
                            {error.erreur}
                          </p>
                          {error.valeur_problematique && (
                            <p className="text-xs text-red-600 mb-2">
                              Valeur problématique: <code className="bg-red-100 px-1 rounded">
                                {JSON.stringify(error.valeur_problematique)}
                              </code>
                            </p>
                          )}
                          {error.suggestion && (
                            <p className="text-xs text-green-700 bg-green-100 p-2 rounded">
                              💡 Suggestion: {error.suggestion}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Notes */}
            {importData.notes && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Notes
                </h2>
                <p className="text-gray-700 whitespace-pre-wrap">
                  {importData.notes}
                </p>
              </div>
            )}
          </div>

          {/* Actions et informations secondaires */}
          <div className="space-y-6">
            {/* Actions */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Actions
              </h2>
              
              <div className="space-y-3">
                <button
                  onClick={() => handleDownloadReport('pdf')}
                  className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Télécharger le rapport (PDF)
                </button>
                
                <button
                  onClick={() => handleDownloadReport('excel')}
                  className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Télécharger le rapport (Excel)
                </button>

                {/* Fonctionnalité de retraitement désactivée */}

                {importData.statut === 'IMPORTE' && (
                  <button
                    onClick={handleRollback}
                    disabled={processing}
                    className="w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {processing ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    ) : (
                      <RotateCcw className="h-4 w-4 mr-2" />
                    )}
                    Annuler l'import
                  </button>
                )}
              </div>
            </div>

            {/* Informations techniques */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Informations Techniques
              </h2>
              
              <div className="space-y-3 text-sm">
                <div>
                  <span className="text-gray-600">ID Import:</span>
                  <span className="ml-2 font-mono">{importData.import_id}</span>
                </div>
                
                <div>
                  <span className="text-gray-600">Complexe ID:</span>
                  <span className="ml-2 font-mono">{importData.complexe_id}</span>
                </div>
                
                {importData.service_id && (
                  <div>
                    <span className="text-gray-600">Service ID:</span>
                    <span className="ml-2 font-mono">{importData.service_id}</span>
                  </div>
                )}
                
                <div>
                  <span className="text-gray-600">Employé ID:</span>
                  <span className="ml-2 font-mono">{importData.employe_id}</span>
                </div>
                
                <div>
                  <span className="text-gray-600">Chemin fichier:</span>
                  <span className="ml-2 font-mono text-xs break-all">
                    {importData.chemin_fichier}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
