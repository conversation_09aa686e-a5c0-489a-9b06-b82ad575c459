import React from 'react';
import { useNavigate } from 'react-router-dom';
import ReceptionReservationView from '../components/reception/ReceptionReservationView';
import { EmployeeAccessGuard } from '../components/guards/EmployeeAccessGuard';

function Reception() {
  const navigate = useNavigate();

  const handleClose = () => {
    navigate('/dashboard');
  };

  return (
    <EmployeeAccessGuard
      requiredType="reception"
      allowAdmin={true}
    >
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto p-6">
          <ReceptionReservationView onClose={handleClose} />
        </div>
      </div>
    </EmployeeAccessGuard>
  );
}

export default Reception; 