import React from 'react';
import { POS as POSComponent } from '../components/POS';
import { useNavigate } from 'react-router-dom';
import { EmployeeAccessGuard } from '../components/guards/EmployeeAccessGuard';

function POS() {
  const navigate = useNavigate();

  const handleClose = () => {
    navigate('/dashboard');
  };

  return (
    <EmployeeAccessGuard
      requiredType={["serveuse", "gerant_services", "cuisine"]}
      allowAdmin={true}
    >
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto p-6">
          <POSComponent onClose={handleClose} />
        </div>
      </div>
    </EmployeeAccessGuard>
  );
}

export default POS; 