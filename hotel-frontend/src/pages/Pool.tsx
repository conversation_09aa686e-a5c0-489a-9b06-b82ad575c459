import React from 'react';
import { Pool as PoolComponent } from '../components/Pool';
import { useNavigate } from 'react-router-dom';
import { EmployeeAccessGuard } from '../components/guards/EmployeeAccessGuard';

function Pool() {
  const navigate = useNavigate();

  const handleClose = () => {
    navigate('/dashboard');
  };

  return (
    <EmployeeAccessGuard
      requiredType="gerant_piscine"
      allowAdmin={true}
    >
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto p-6">
          <PoolComponent onClose={handleClose} />
        </div>
      </div>
    </EmployeeAccessGuard>
  );
}

export default Pool; 