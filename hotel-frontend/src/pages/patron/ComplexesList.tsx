import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Building2, MapPin, Users, Star } from 'lucide-react';
import { complexeService, Complexe } from '../../services/complexe.service';
import { authService } from '../../services/auth.service';
import { ComplexeSelector } from '../../components/ComplexeSelector';

// Images par défaut pour les complexes
const defaultImages = [
  'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1080&q=80',
  'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1080&q=80',
  'https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1080&q=80',
];

function ComplexesList() {
  const navigate = useNavigate();
  const [complexes, setComplexes] = useState<Complexe[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchComplexes = async () => {
      try {
        const data = await complexeService.getComplexes();
        setComplexes(data);
        setError('');
      } catch (err) {
        console.error('Error fetching complexes:', err);
        setError('Impossible de charger la liste des complexes. Veuillez réessayer plus tard.');
      } finally {
        setLoading(false);
      }
    };

    fetchComplexes();
  }, []);

  const handleComplexeSelect = (complexeId: number) => {
    localStorage.setItem('selectedComplexeId', complexeId.toString());
    navigate('/dashboard');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement des complexes...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <p className="text-red-600">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Réessayer
          </button>
        </div>
      </div>
    );
  }

  // Si l'utilisateur est un admin de chaîne, afficher le sélecteur de complexe
  if (authService.isAdminChaine()) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900">Sélection du Complexe</h1>
            <p className="mt-2 text-sm text-gray-600">
              Sélectionnez un complexe pour accéder à son tableau de bord
            </p>
          </div>

          <div className="mt-8 max-w-md mx-auto">
            <ComplexeSelector onComplexeSelect={handleComplexeSelect} />
          </div>
        </div>
      </div>
    );
  }

  // Pour les autres rôles, afficher la liste des complexes
  if (complexes.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-400 mb-4">
            <Building2 className="h-12 w-12 mx-auto" />
          </div>
          <p className="text-gray-600">Aucun complexe hôtelier trouvé</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">Mes Complexes Hôteliers</h1>
          <p className="mt-2 text-sm text-gray-600">
            Sélectionnez un complexe pour accéder à son tableau de bord
          </p>
        </div>

        <div className="mt-8 grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
          {complexes.map((complex, index) => (
            <div
              key={complex.complexe_id}
              onClick={() => handleComplexeSelect(complex.complexe_id)}
              className="bg-white overflow-hidden shadow-lg rounded-lg cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-xl"
            >
              <div className="relative h-48">
                <img
                  src={defaultImages[index % defaultImages.length]}
                  alt={complex.nom}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                <div className="absolute bottom-0 left-0 right-0 p-4">
                  <h3 className="text-xl font-bold text-white">{complex.nom}</h3>
                  <div className="flex items-center mt-1">
                    <Star className="h-4 w-4 text-yellow-400" />
                    <span className="text-white text-sm ml-1">4.5</span>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <div className="flex items-center text-sm text-gray-500 mb-2">
                  <MapPin className="h-4 w-4 mr-2" />
                  <span>{complex.ville}, {complex.pays}</span>
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Users className="h-4 w-4 mr-2" />
                  <span>{complex.nombre_employes || 0} employés</span>
                </div>
                <div className="mt-4">
                  <button
                    className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
                  >
                    Accéder au complexe
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default ComplexesList; 