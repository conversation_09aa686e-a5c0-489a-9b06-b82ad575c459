import React from 'react';
import { ClientReservation as ClientReservationComponent } from '../components/ClientReservation';
import { useNavigate } from 'react-router-dom';

function ClientReservation() {
  const navigate = useNavigate();

  const handleClose = () => {
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-6">
        <ClientReservationComponent onClose={handleClose} />
      </div>
    </div>
  );
}

export default ClientReservation; 