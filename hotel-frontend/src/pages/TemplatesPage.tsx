import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { authService } from '../services/auth.service';
import {
  FileText,
  Download,
  Plus,
  Edit,
  Trash2,
  Copy,
  Search,
  Filter,
  ArrowLeft
} from 'lucide-react';
import { importService } from '../services';
import type { TemplateImport, TypeImport } from '../types';

export const TemplatesPage: React.FC = () => {
  const navigate = useNavigate();
  const complexeId = authService.getComplexeId();

  // État des données
  const [templates, setTemplates] = useState<TemplateImport[]>([]);
  const [loading, setLoading] = useState(false);

  // État de l'interface
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<TypeImport | 'ALL'>('ALL');

  // Vérification de l'authentification
  useEffect(() => {
    if (!authService.isAuthenticated()) {
      navigate('/login');
      return;
    }

    if (authService.needsComplexeSelection()) {
      navigate('/complexes');
      return;
    }
  }, [navigate]);

  // Charger les templates
  useEffect(() => {
    if (complexeId) {
      loadTemplates();
    }
  }, [complexeId, filterType]);

  const loadTemplates = async () => {
    if (!complexeId) return;

    try {
      setLoading(true);
      const result = await importService.getTemplates();
      setTemplates(result);
    } catch (error) {
      console.error('Erreur lors du chargement des templates:', error);
      toast.error('Erreur lors du chargement des templates');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadTemplate = async (template: TemplateImport) => {
    try {
      const blob = await importService.downloadTemplate(template.type_import);
      
      // Créer un lien de téléchargement
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `template-${template.nom_template.toLowerCase().replace(/\s+/g, '-')}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast.success('Template téléchargé avec succès');
    } catch (error) {
      console.error('Erreur lors du téléchargement:', error);
      toast.error('Erreur lors du téléchargement du template');
    }
  };

  // Fonctions de gestion des templates désactivées pour le moment

  const handleDeleteTemplate = async (templateId: number) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce template ?')) {
      return;
    }

    try {
      await importService.deleteTemplate(templateId);
      setTemplates(prev => prev.filter(t => t.template_id !== templateId));
      toast.success('Template supprimé avec succès');
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      toast.error('Erreur lors de la suppression du template');
    }
  };

  const handleDuplicateTemplate = async (template: TemplateImport) => {
    try {
      const duplicatedTemplate = await importService.createTemplate({
        ...template,
        nom_template: `${template.nom_template} (Copie)`,
        template_id: undefined,
        created_at: undefined,
        updated_at: undefined
      });
      setTemplates(prev => [...prev, duplicatedTemplate]);
      toast.success('Template dupliqué avec succès');
    } catch (error) {
      console.error('Erreur lors de la duplication:', error);
      toast.error('Erreur lors de la duplication du template');
    }
  };

  const getTypeImportLabel = (type: TypeImport) => {
    switch (type) {
      case 'MENU_RESTAURANT':
        return 'Menu Restaurant';
      case 'CARTE_BAR':
        return 'Carte Bar';
      case 'INVENTAIRE_INGREDIENTS':
        return 'Inventaire Ingrédients';
      default:
        return type;
    }
  };

  const getServiceTypeLabel = (type: string) => {
    switch (type) {
      case 'Restaurant':
        return 'Restaurant';
      case 'Bar':
        return 'Bar';
      case 'Piscine':
        return 'Piscine';
      default:
        return type;
    }
  };

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.nom_template.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterType === 'ALL' || template.type_import === filterType;
    
    return matchesSearch && matchesFilter;
  });

  if (!authService.hasComplexeAccess()) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Accès non autorisé
          </h2>
          <p className="text-gray-600">
            {authService.needsComplexeSelection()
              ? 'Veuillez sélectionner un complexe pour accéder à cette page.'
              : 'Vous devez être connecté à un complexe pour accéder à cette page.'
            }
          </p>
          {authService.needsComplexeSelection() && (
            <button
              onClick={() => navigate('/patron/complexes')}
              className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Sélectionner un complexe
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* En-tête avec bouton retour */}
        <div className="mb-8">
          <div className="flex justify-between items-start">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/dashboard')}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  Templates d'Import
                </h1>
                <p className="text-lg text-gray-600">
                  Gérez vos templates Excel personnalisés pour faciliter les imports
                </p>
              </div>
            </div>

            {/* Bouton de création désactivé pour le moment */}
            <button
              disabled
              className="flex items-center px-4 py-2 bg-gray-400 text-white rounded-md cursor-not-allowed"
            >
              <Plus className="h-4 w-4 mr-2" />
              Nouveau Template
            </button>
          </div>
        </div>

        {/* Filtres et recherche */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Recherche */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Rechercher un template..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Filtre par type */}
            <div className="md:w-64">
              <div className="relative">
                <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value as TypeImport | 'ALL')}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="ALL">Tous les types</option>
                  <option value="MENU_RESTAURANT">Menu Restaurant</option>
                  <option value="CARTE_BAR">Carte Bar</option>
                  <option value="INVENTAIRE_INGREDIENTS">Inventaire Ingrédients</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Liste des templates */}
        <div className="bg-white rounded-lg shadow-sm">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : filteredTemplates.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Aucun template trouvé
              </h3>
              <p className="text-gray-600 mb-4">
                {searchQuery || filterType !== 'ALL' 
                  ? 'Aucun template ne correspond aux critères de recherche.'
                  : 'Commencez par créer votre premier template d\'import.'
                }
              </p>
              {!searchQuery && filterType === 'ALL' && (
                <button
                  disabled
                  className="inline-flex items-center px-4 py-2 bg-gray-400 text-white rounded-md cursor-not-allowed"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Créer un template
                </button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Template
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Service
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Version
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Statut
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredTemplates.map((template) => (
                    <tr key={template.template_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <FileText className="h-5 w-5 text-gray-400 mr-3" />
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {template.nom_template}
                            </div>
                            {template.description && (
                              <div className="text-sm text-gray-500">
                                {template.description}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm text-gray-900">
                          {getTypeImportLabel(template.type_import)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm text-gray-900">
                          {getServiceTypeLabel(template.type_service)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm text-gray-900">
                          v{template.version}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          template.actif 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {template.actif ? 'Actif' : 'Inactif'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleDownloadTemplate(template)}
                            className="text-blue-600 hover:text-blue-900"
                            title="Télécharger"
                          >
                            <Download className="h-4 w-4" />
                          </button>
                          <button
                            disabled
                            className="text-gray-400 cursor-not-allowed"
                            title="Modification désactivée"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDuplicateTemplate(template)}
                            className="text-purple-600 hover:text-purple-900"
                            title="Dupliquer"
                          >
                            <Copy className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteTemplate(template.template_id)}
                            className="text-red-600 hover:text-red-900"
                            title="Supprimer"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
