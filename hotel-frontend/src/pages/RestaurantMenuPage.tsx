import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Settings, BarChart3, Package, AlertTriangle } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { authService } from '../services/auth.service';
import { serviceComplexeService } from '../services/service.service';
import { MenuAvailabilityDashboard } from '../components/restaurant/MenuAvailabilityDashboard';
import { PlatComposition } from '../components/restaurant/PlatComposition';
import type { ServiceComplexe } from '../services/service.service';

type TabType = 'dashboard' | 'composition' | 'analytics';

export const RestaurantMenuPage: React.FC = () => {
  const { serviceId } = useParams<{ serviceId: string }>();
  const navigate = useNavigate();
  const complexeId = authService.getComplexeId();
  
  const [service, setService] = useState<ServiceComplexe | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<TabType>('dashboard');
  const [selectedProduitId, setSelectedProduitId] = useState<number | null>(null);

  useEffect(() => {
    if (!serviceId || !complexeId) {
      navigate('/services');
      return;
    }
    
    loadService();
  }, [serviceId, complexeId]);

  const loadService = async () => {
    try {
      setLoading(true);
      const services = await serviceComplexeService.getAllServices();
      const foundService = services.find(s => s.service_id === parseInt(serviceId!));
      
      if (!foundService) {
        toast.error('Service non trouvé');
        navigate('/services');
        return;
      }

      if (foundService.type_service !== 'Restaurant') {
        toast.error('Ce service n\'est pas un restaurant');
        navigate('/services');
        return;
      }

      setService(foundService);
    } catch (error) {
      console.error('Erreur lors du chargement du service:', error);
      toast.error('Erreur lors du chargement du service');
      navigate('/services');
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    {
      id: 'dashboard' as TabType,
      name: 'Disponibilité',
      icon: BarChart3,
      description: 'Vue d\'ensemble de la disponibilité du menu'
    },
    {
      id: 'composition' as TabType,
      name: 'Composition',
      icon: Package,
      description: 'Gérer la composition des plats'
    },
    {
      id: 'analytics' as TabType,
      name: 'Analyses',
      icon: AlertTriangle,
      description: 'Analyses des coûts et marges'
    }
  ];

  if (!complexeId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Accès non autorisé
          </h2>
          <p className="text-gray-600">
            Vous devez être connecté à un complexe pour accéder à cette page.
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Service non trouvé
          </h2>
          <p className="text-gray-600">
            Le service demandé n'existe pas ou n'est pas accessible.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/services')}
                className="mr-4 p-2 text-gray-400 hover:text-gray-600"
              >
                <ArrowLeft className="h-6 w-6" />
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  Menu Restaurant - {service.nom}
                </h1>
                <p className="text-lg text-gray-600">
                  Gestion complète du menu avec stocks intégrés
                </p>
                <div className="flex items-center mt-2 text-sm text-gray-500">
                  <span>{service.emplacement}</span>
                  {service.description && (
                    <>
                      <span className="mx-2">•</span>
                      <span>{service.description}</span>
                    </>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={() => navigate(`/menu-setup?serviceId=${serviceId}`)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <Settings className="h-4 w-4 mr-2" />
                Configuration
              </button>
            </div>
          </div>
        </div>

        {/* Navigation par onglets */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className={`mr-2 h-5 w-5 ${
                      activeTab === tab.id ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                    }`} />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </div>
          
          {/* Description de l'onglet actif */}
          <div className="mt-4">
            <p className="text-sm text-gray-600">
              {tabs.find(tab => tab.id === activeTab)?.description}
            </p>
          </div>
        </div>

        {/* Contenu des onglets */}
        <div className="space-y-6">
          {activeTab === 'dashboard' && (
            <MenuAvailabilityDashboard
              serviceId={service.service_id}
              serviceName={service.nom}
            />
          )}

          {activeTab === 'composition' && (
            <div className="space-y-6">
              {selectedProduitId ? (
                <div>
                  <div className="mb-4">
                    <button
                      onClick={() => setSelectedProduitId(null)}
                      className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
                    >
                      <ArrowLeft className="h-4 w-4 mr-1" />
                      Retour à la liste des plats
                    </button>
                  </div>
                  <PlatComposition
                    produitId={selectedProduitId}
                    produitNom="Plat sélectionné"
                    onCompositionChange={() => {
                      // Rafraîchir les données si nécessaire
                    }}
                  />
                </div>
              ) : (
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Sélectionner un plat pour gérer sa composition
                  </h3>
                  <p className="text-gray-600">
                    Utilisez l'onglet "Disponibilité" pour voir la liste des plats et cliquez sur un plat pour gérer sa composition.
                  </p>
                  <button
                    onClick={() => setActiveTab('dashboard')}
                    className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    Voir la liste des plats
                  </button>
                </div>
              )}
            </div>
          )}

          {activeTab === 'analytics' && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Analyses des Coûts et Marges
              </h3>
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-4">
                  Les analyses détaillées seront disponibles dans une prochaine version.
                </p>
                <div className="space-y-2 text-sm text-gray-500">
                  <p>• Analyse des marges par plat</p>
                  <p>• Optimisation des prix</p>
                  <p>• Tendances des coûts</p>
                  <p>• Recommandations d'achat</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
