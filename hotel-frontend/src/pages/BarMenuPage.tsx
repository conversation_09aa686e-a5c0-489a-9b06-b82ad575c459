import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { <PERSON>L<PERSON><PERSON>, Settings, BarChart3, Wine, AlertTriangle } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { authService } from '../services/auth.service';
import { serviceComplexeService } from '../services/service.service';
import { BoissonsAvailabilityDashboard } from '../components/bar/BoissonsAvailabilityDashboard';
import type { ServiceComplexe } from '../services/service.service';

type TabType = 'dashboard' | 'analytics';

export const BarMenuPage: React.FC = () => {
  const { serviceId } = useParams<{ serviceId: string }>();
  const navigate = useNavigate();
  const complexeId = authService.getComplexeId();
  
  const [service, setService] = useState<ServiceComplexe | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<TabType>('dashboard');

  useEffect(() => {
    if (!serviceId || !complexeId) {
      navigate('/services');
      return;
    }
    
    loadService();
  }, [serviceId, complexeId]);

  const loadService = async () => {
    try {
      setLoading(true);
      const services = await serviceComplexeService.getAllServices();
      const foundService = services.find(s => s.service_id === parseInt(serviceId!));
      
      if (!foundService) {
        toast.error('Service non trouvé');
        navigate('/services');
        return;
      }

      if (foundService.type_service !== 'Bar') {
        toast.error('Ce service n\'est pas un bar');
        navigate('/services');
        return;
      }

      setService(foundService);
    } catch (error) {
      console.error('Erreur lors du chargement du service:', error);
      toast.error('Erreur lors du chargement du service');
      navigate('/services');
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    {
      id: 'dashboard' as TabType,
      name: 'Carte & Stock',
      icon: Wine,
      description: 'Vue d\'ensemble de la carte et des stocks de boissons'
    },
    {
      id: 'analytics' as TabType,
      name: 'Analyses',
      icon: BarChart3,
      description: 'Analyses des ventes et marges'
    }
  ];

  if (!complexeId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Accès non autorisé
          </h2>
          <p className="text-gray-600">
            Vous devez être connecté à un complexe pour accéder à cette page.
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Service non trouvé
          </h2>
          <p className="text-gray-600">
            Le service demandé n'existe pas ou n'est pas accessible.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/services')}
                className="mr-4 p-2 text-gray-400 hover:text-gray-600"
              >
                <ArrowLeft className="h-6 w-6" />
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  Carte Bar - {service.nom}
                </h1>
                <p className="text-lg text-gray-600">
                  Gestion de la carte et des stocks de boissons
                </p>
                <div className="flex items-center mt-2 text-sm text-gray-500">
                  <span>{service.emplacement}</span>
                  {service.description && (
                    <>
                      <span className="mx-2">•</span>
                      <span>{service.description}</span>
                    </>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={() => navigate(`/menu-setup?serviceId=${serviceId}`)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <Settings className="h-4 w-4 mr-2" />
                Configuration
              </button>
            </div>
          </div>
        </div>

        {/* Navigation par onglets */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-purple-500 text-purple-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className={`mr-2 h-5 w-5 ${
                      activeTab === tab.id ? 'text-purple-500' : 'text-gray-400 group-hover:text-gray-500'
                    }`} />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </div>
          
          {/* Description de l'onglet actif */}
          <div className="mt-4">
            <p className="text-sm text-gray-600">
              {tabs.find(tab => tab.id === activeTab)?.description}
            </p>
          </div>
        </div>

        {/* Contenu des onglets */}
        <div className="space-y-6">
          {activeTab === 'dashboard' && (
            <BoissonsAvailabilityDashboard
              serviceId={service.service_id}
              serviceName={service.nom}
            />
          )}

          {activeTab === 'analytics' && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Analyses des Ventes et Marges
              </h3>
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-4">
                  Les analyses détaillées seront disponibles dans une prochaine version.
                </p>
                <div className="space-y-2 text-sm text-gray-500">
                  <p>• Top des boissons vendues</p>
                  <p>• Analyse des marges par catégorie</p>
                  <p>• Tendances de consommation</p>
                  <p>• Optimisation des prix</p>
                  <p>• Gestion des happy hours</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
