import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { ArrowLeft, AlertTriangle, ExternalLink } from 'lucide-react';
import { authService } from '../services/auth.service';
import {
  ImportStepper,
  UploadStep,
  ImportPreview,
  ProcessingStep,
  CompleteStep,
  QuickLinks
} from '../components/ui';
import { uploadService, importService } from '../services';
import type { TypeImport, ImportExcel, ImportPreview as ImportPreviewType } from '../types';

type StepType = 'upload' | 'preview' | 'processing' | 'complete';

export const ImportExcelPage: React.FC = () => {
  const navigate = useNavigate();
  const complexeId = authService.getComplexeId();

  // État de l'interface
  const [currentStep, setCurrentStep] = useState<StepType>('upload');
  const [loading, setLoading] = useState(false);

  // Vérification de l'authentification
  useEffect(() => {
    if (!authService.isAuthenticated()) {
      navigate('/login');
      return;
    }

    if (authService.isAdminChaine()) {
      const selectedComplexeId = localStorage.getItem('selectedComplexeId');
      if (!selectedComplexeId) {
        navigate('/patron/complexes');
        return;
      }
    }
  }, [navigate]);

  // Configuration de l'import
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [typeImport, setTypeImport] = useState<TypeImport>('MENU_RESTAURANT');
  const [serviceId, setServiceId] = useState<number | undefined>();

  // Données de l'import
  const [currentImport, setCurrentImport] = useState<ImportExcel | null>(null);
  const [preview, setPreview] = useState<ImportPreviewType | null>(null);

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
  };

  const handleUpload = async () => {
    if (!selectedFile || !complexeId) {
      toast.error('Fichier ou complexe manquant');
      return;
    }

    try {
      setLoading(true);
      
      // Upload du fichier
      const importData = await uploadService.uploadExcelFile(
        selectedFile,
        typeImport,
        complexeId,
        serviceId
      );
      
      setCurrentImport(importData);
      toast.success('Fichier uploadé avec succès');

      // Attendre le parsing automatique (simulé)
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Récupérer la prévisualisation
      const previewData = await uploadService.getImportPreview(importData.import_id);
      setPreview(previewData);
      setCurrentStep('preview');
      
    } catch (error) {
      console.error('Erreur lors de l\'upload:', error);
      toast.error('Erreur lors de l\'upload du fichier');
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmImport = async () => {
    if (!currentImport) {
      toast.error('Aucun import en cours');
      return;
    }

    try {
      setLoading(true);
      setCurrentStep('processing');

      // Démarrer le traitement de l'import
      await importService.processImport(currentImport.import_id);

      // Polling du statut avec mise à jour en temps réel
      await uploadService.pollImportStatus(
        currentImport.import_id,
        (status) => {
          setCurrentImport(status);
          
          if (status.statut === 'IMPORTE') {
            setCurrentStep('complete');
            toast.success('Import terminé avec succès !');
          } else if (status.statut === 'ERREUR') {
            setCurrentStep('complete');
            toast.error('Erreur lors de l\'import');
          }
        }
      );
      
    } catch (error) {
      console.error('Erreur lors du traitement:', error);
      toast.error('Erreur lors du traitement de l\'import');
      setCurrentStep('upload');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelImport = () => {
    setCurrentStep('upload');
    setSelectedFile(null);
    setCurrentImport(null);
    setPreview(null);
  };

  const handleReset = () => {
    setCurrentStep('upload');
    setSelectedFile(null);
    setCurrentImport(null);
    setPreview(null);
    setTypeImport('MENU_RESTAURANT');
    setServiceId(undefined);
  };

  if (!complexeId) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Accès non autorisé
          </h2>
          <p className="text-gray-600">
            Vous devez être connecté à un complexe pour accéder à cette page.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Bandeau de dépréciation */}
        <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start">
            <AlertTriangle className="h-6 w-6 text-yellow-600 mr-3 mt-0.5" />
            <div className="flex-1">
              <h3 className="text-lg font-medium text-yellow-800 mb-2">
                Interface dépréciée - Nouvelle interface disponible
              </h3>
              <p className="text-sm text-yellow-700 mb-3">
                Cette page d'import sera bientôt supprimée. Utilisez la nouvelle interface d'import intégrée
                dans la gestion des services pour une expérience simplifiée.
              </p>
              <button
                onClick={() => navigate('/services')}
                className="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 text-sm font-medium"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Utiliser la nouvelle interface
              </button>
            </div>
          </div>
        </div>

        {/* En-tête avec bouton retour */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Import de Fichiers Excel
              </h1>
              <p className="text-lg text-gray-600">
                Importez vos menus, cartes de bar ou inventaires depuis des fichiers Excel
              </p>
            </div>
            <button
              onClick={() => navigate('/dashboard')}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Retour au Dashboard
            </button>
          </div>
        </div>

        {/* Stepper de progression */}
        <div className="mb-8">
          <ImportStepper currentStep={currentStep} />
        </div>

        {/* Contenu principal selon l'étape */}
        <div className="bg-white rounded-lg shadow-sm">
          {currentStep === 'upload' && (
            <div className="p-6">
              <UploadStep
                typeImport={typeImport}
                onTypeChange={setTypeImport}
                serviceId={serviceId}
                onServiceChange={setServiceId}
                selectedFile={selectedFile}
                onFileSelect={handleFileSelect}
                onUpload={handleUpload}
                loading={loading}
              />
            </div>
          )}

          {currentStep === 'preview' && preview && (
            <div className="p-6">
              <ImportPreview
                preview={preview}
                onConfirm={handleConfirmImport}
                onCancel={handleCancelImport}
                loading={loading}
              />
            </div>
          )}

          {currentStep === 'processing' && currentImport && (
            <div className="p-6">
              <ProcessingStep import={currentImport} />
            </div>
          )}

          {currentStep === 'complete' && currentImport && (
            <div className="p-6">
              <CompleteStep
                import={currentImport}
                onNewImport={handleReset}
              />
            </div>
          )}
        </div>

        {/* Actions globales */}
        {currentStep !== 'complete' && currentStep !== 'processing' && (
          <div className="mt-6 flex justify-between">
            <button
              onClick={handleReset}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              disabled={loading}
            >
              Recommencer
            </button>

            {currentStep === 'preview' && (
              <div className="text-sm text-gray-500">
                Vérifiez attentivement les données avant de confirmer l'import
              </div>
            )}
          </div>
        )}

        {/* Actions rapides pour les imports terminés */}
        {currentStep === 'complete' && (
          <div className="mt-6">
            <QuickLinks context="import" />
          </div>
        )}
      </div>
    </div>
  );
};
