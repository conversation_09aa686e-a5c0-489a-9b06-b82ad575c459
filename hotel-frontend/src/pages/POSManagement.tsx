import React from 'react';
import { POSManagement as POSManagementComponent } from '../components/POSManagement';
import { useNavigate } from 'react-router-dom';

function POSManagement() {
  const navigate = useNavigate();

  const handleClose = () => {
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-6">
        <POSManagementComponent onClose={handleClose} />
      </div>
    </div>
  );
}

export default POSManagement;
