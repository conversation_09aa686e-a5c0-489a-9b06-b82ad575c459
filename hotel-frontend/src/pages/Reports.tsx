import React from 'react';
import { Reports as ReportsComponent } from '../components/Reports';
import { useNavigate } from 'react-router-dom';
import { AdminAccessGuard } from '../components/guards/AdminAccessGuard';

function Reports() {
  const navigate = useNavigate();

  const handleClose = () => {
    navigate('/dashboard');
  };

  return (
    <AdminAccessGuard requiredPage="reports">
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto p-6">
          <ReportsComponent onClose={handleClose} />
        </div>
      </div>
    </AdminAccessGuard>
  );
}

export default Reports; 