import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { X, Shield, AlertTriangle, Settings, Eye } from 'lucide-react';
import { authService } from '../services/auth.service';
import ServiceList from '../components/servicesComplexes/ServiceList';
import { QuickLinks } from '../components/ui';
import { useEmployeePermissions } from '../hooks/useEmployeePermissions';
import { useAdminPermissions } from '../hooks/useAdminPermissions';
import { AdminAccessGuard } from '../components/guards/AdminAccessGuard';

// Composant pour la vue Admin (Configuration complète)
const AdminServicesView: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { canAccessConfiguration } = useAdminPermissions();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Admin */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Settings className="w-6 h-6 mr-3 text-blue-600" />
                Configuration des Services
              </h1>
              <p className="text-gray-600 mt-1 flex items-center">
                <Shield className="w-4 h-4 mr-2 text-green-600" />
                Gestion complète des services et équipements du complexe
              </p>
            </div>
            <button
              onClick={onClose}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <X className="h-5 w-5 mr-2" />
              Retour au Dashboard
            </button>
          </div>
        </div>
      </div>

      {/* Contenu Admin */}
      <div className="py-6">
        <div className="container mx-auto px-4 space-y-6">
          {canAccessConfiguration ? (
            <>
              <ServiceList />
              <QuickLinks context="services" />
            </>
          ) : (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
              <div className="flex items-center">
                <AlertTriangle className="w-6 h-6 text-yellow-600 mr-3" />
                <div>
                  <h3 className="text-lg font-medium text-yellow-800">Accès Limité</h3>
                  <p className="text-yellow-600 mt-1">
                    Vous n'avez pas les permissions pour configurer les services.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Composant pour la vue Employé (Consultation uniquement)
const EmployeeServicesView: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { employeeType, authorizedServices } = useEmployeePermissions();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Employé */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Eye className="w-6 h-6 mr-3 text-green-600" />
                Consultation des Services
              </h1>
              <p className="text-gray-600 mt-1">
                Informations sur les services autorisés pour votre poste ({employeeType})
              </p>
            </div>
            <button
              onClick={onClose}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <X className="h-5 w-5 mr-2" />
              Retour au Dashboard
            </button>
          </div>
        </div>
      </div>

      {/* Contenu Employé */}
      <div className="py-6">
        <div className="container mx-auto px-4 space-y-6">
          {/* Services autorisés */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4">Mes Services Autorisés</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {authorizedServices.map((service) => (
                <div key={service} className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-medium text-blue-800 capitalize">{service}</h3>
                  <p className="text-sm text-blue-600 mt-1">
                    Accès autorisé pour votre poste
                  </p>
                </div>
              ))}
              {authorizedServices.length === 0 && (
                <div className="col-span-full bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
                  <p className="text-gray-600">Aucun service spécifique autorisé</p>
                </div>
              )}
            </div>
          </div>

          {/* Vue limitée des services */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4">Informations Générales</h2>
            <p className="text-gray-600">
              En tant qu'employé, vous avez accès aux informations de consultation uniquement.
              Pour modifier les services, contactez votre administrateur.
            </p>
          </div>

          {/* Actions rapides limitées */}
          <QuickLinks context="services-employee" />
        </div>
      </div>
    </div>
  );
};

const Services: React.FC = () => {
  const navigate = useNavigate();
  const { isAdmin, isEmployee, loading } = useEmployeePermissions();

  useEffect(() => {
    const checkAuth = async () => {
      const isAuthenticated = await authService.isAuthenticated();
      if (!isAuthenticated) {
        navigate('/login');
        return;
      }
    };

    checkAuth();
  }, [navigate]);

  const handleClose = () => {
    navigate('/dashboard');
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement des permissions...</p>
        </div>
      </div>
    );
  }

  // Rendu conditionnel selon le type d'utilisateur
  if (isAdmin) {
    return (
      <AdminAccessGuard>
        <AdminServicesView onClose={handleClose} />
      </AdminAccessGuard>
    );
  }

  if (isEmployee) {
    return <EmployeeServicesView onClose={handleClose} />;
  }

  // Fallback - ne devrait pas arriver
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
        <div className="flex items-center">
          <AlertTriangle className="w-6 h-6 text-red-600 mr-3" />
          <div>
            <h3 className="text-lg font-medium text-red-800">Accès Non Autorisé</h3>
            <p className="text-red-600 mt-1">
              Impossible de déterminer vos permissions. Contactez l'administrateur.
            </p>
          </div>
        </div>
        <button
          onClick={handleClose}
          className="mt-4 w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
        >
          Retour au Dashboard
        </button>
      </div>
    </div>
  );
};

export default Services;
