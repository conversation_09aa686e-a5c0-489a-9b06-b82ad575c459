import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { authService } from '../services/auth.service';
import {
  Package,
  Warehouse,
  ChefHat,
  BarChart3,
  AlertTriangle,
  Plus,
  FileText,
  ArrowLeft,
  Info,
  ExternalLink
} from 'lucide-react';
import {
  IngredientManager,
  StockManager,
  InventaireAnalytics,
  QuickLinks,
  ProduitsIngredientsManager
} from '../components/ui';
import { inventaireService } from '../services';
import type { StockAlert } from '../types';

type TabType = 'ingredients' | 'stock' | 'produits' | 'analytics';

export const InventairePage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const complexeId = authService.getComplexeId();

  // État de l'interface - utilise les paramètres d'URL
  const tabFromUrl = searchParams.get('tab') as TabType || 'ingredients';
  const [activeTab, setActiveTab] = useState<TabType>(tabFromUrl);
  const [stockAlerts, setStockAlerts] = useState<StockAlert[]>([]);

  // Vérification de l'authentification
  useEffect(() => {
    if (!authService.isAuthenticated()) {
      navigate('/login');
      return;
    }

    if (authService.isAdminChaine()) {
      const selectedComplexeId = localStorage.getItem('selectedComplexeId');
      if (!selectedComplexeId) {
        navigate('/complexes');
        return;
      }
    }
  }, [navigate]);

  // Synchroniser l'onglet avec l'URL
  useEffect(() => {
    const tabFromUrl = searchParams.get('tab') as TabType;
    if (tabFromUrl && ['ingredients', 'stock', 'produits', 'analytics'].includes(tabFromUrl)) {
      setActiveTab(tabFromUrl);
    }
  }, [searchParams]);

  // Charger les alertes de stock au montage
  useEffect(() => {
    if (complexeId) {
      loadStockAlerts();
    }
  }, [complexeId]);

  // Fonction pour changer d'onglet avec mise à jour de l'URL
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    setSearchParams({ tab });
  };

  const loadStockAlerts = async () => {
    if (!complexeId) return;

    try {
      const alerts = await inventaireService.getStockAlerts(complexeId);
      setStockAlerts(alerts);
    } catch (error) {
      console.error('Erreur lors du chargement des alertes:', error);
      toast.error('Erreur lors du chargement des alertes de stock');
    }
  };

  const tabs = [
    {
      id: 'ingredients' as TabType,
      label: 'Ingrédients',
      icon: Package,
      description: 'Gérer les ingrédients et leurs propriétés'
    },
    {
      id: 'stock' as TabType,
      label: 'Stock',
      icon: Warehouse,
      description: 'Suivre les quantités et mouvements de stock'
    },
    {
      id: 'produits' as TabType,
      label: 'Produits & Composition',
      icon: ChefHat,
      description: 'Gérer les produits et leurs compositions d\'ingrédients'
    },
    {
      id: 'analytics' as TabType,
      label: 'Analyses',
      icon: BarChart3,
      description: 'Analyses et rapports d\'inventaire'
    }
  ];

  const getAlertSummary = () => {
    if (stockAlerts.length === 0) return null;

    const criticalAlerts = stockAlerts.filter(alert => alert.urgence === 'Critique').length;
    const highAlerts = stockAlerts.filter(alert => alert.urgence === 'Haute').length;
    const totalAlerts = stockAlerts.length;

    return { criticalAlerts, highAlerts, totalAlerts };
  };

  const alertSummary = getAlertSummary();

  if (!complexeId) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Accès non autorisé
          </h2>
          <p className="text-gray-600">
            Vous devez être connecté à un complexe pour accéder à cette page.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Bandeau informatif sur la migration */}
        <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <Info className="h-6 w-6 text-blue-600 mr-3 mt-0.5" />
            <div className="flex-1">
              <h3 className="text-lg font-medium text-blue-800 mb-2">
                Nouveau système d'import de menus disponible
              </h3>
              <p className="text-sm text-blue-700 mb-3">
                Nous avons simplifié l'import de menus ! Utilisez maintenant la nouvelle interface
                intégrée dans la gestion des services pour importer vos menus et ingrédients en 3 clics.
              </p>
              <button
                onClick={() => navigate('/menu-setup')}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 text-sm font-medium mr-3"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Configuration rapide
              </button>
              <button
                onClick={() => navigate('/services')}
                className="inline-flex items-center px-4 py-2 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 text-sm font-medium"
              >
                Gestion des services
              </button>
            </div>
          </div>
        </div>

        {/* En-tête avec alertes et bouton retour */}
        <div className="mb-8">
          <div className="flex justify-between items-start">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/dashboard')}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  Gestion d'Inventaire
                </h1>
                <p className="text-lg text-gray-600">
                  Gérez vos ingrédients et stocks en temps réel
                </p>
              </div>
            </div>

            {/* Résumé des alertes */}
            {alertSummary && (
              <div className="bg-white rounded-lg shadow-sm border p-4 min-w-64">
                <div className="flex items-center mb-2">
                  <AlertTriangle className="h-5 w-5 text-orange-500 mr-2" />
                  <h3 className="font-medium text-gray-900">Alertes de Stock</h3>
                </div>
                <div className="space-y-1 text-sm">
                  {alertSummary.criticalAlerts > 0 && (
                    <div className="flex justify-between">
                      <span className="text-red-600">Critiques:</span>
                      <span className="font-medium text-red-600">{alertSummary.criticalAlerts}</span>
                    </div>
                  )}
                  {alertSummary.highAlerts > 0 && (
                    <div className="flex justify-between">
                      <span className="text-orange-600">Importantes:</span>
                      <span className="font-medium text-orange-600">{alertSummary.highAlerts}</span>
                    </div>
                  )}
                  <div className="flex justify-between border-t pt-1">
                    <span className="text-gray-600">Total:</span>
                    <span className="font-medium">{alertSummary.totalAlerts}</span>
                  </div>
                </div>
                <button
                  onClick={() => handleTabChange('stock')}
                  className="mt-2 w-full text-xs bg-orange-50 text-orange-700 py-1 px-2 rounded hover:bg-orange-100 transition-colors"
                >
                  Voir les détails
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Navigation par onglets */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                const isActive = activeTab === tab.id;
                
                return (
                  <button
                    key={tab.id}
                    onClick={() => handleTabChange(tab.id)}
                    className={`
                      flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors
                      ${isActive
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }
                    `}
                  >
                    <Icon className="h-5 w-5 mr-2" />
                    <span>{tab.label}</span>
                    {tab.id === 'stock' && alertSummary && alertSummary.totalAlerts > 0 && (
                      <span className="ml-2 bg-red-100 text-red-600 text-xs px-2 py-0.5 rounded-full">
                        {alertSummary.totalAlerts}
                      </span>
                    )}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Description de l'onglet actif */}
          <div className="px-6 py-3 bg-gray-50 border-b">
            <p className="text-sm text-gray-600">
              {tabs.find(tab => tab.id === activeTab)?.description}
            </p>
          </div>
        </div>

        {/* Contenu des onglets */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6">
            {activeTab === 'ingredients' && (
              <IngredientManager
                complexeId={complexeId}
                mode="management"
              />
            )}

            {activeTab === 'stock' && (
              <StockManager
                complexeId={complexeId}
                alerts={stockAlerts}
                onAlertsUpdate={loadStockAlerts}
              />
            )}

            {activeTab === 'produits' && (
              <ProduitsIngredientsManager
                complexeId={complexeId}
              />
            )}

            {activeTab === 'analytics' && (
              <InventaireAnalytics complexeId={complexeId} />
            )}
          </div>
        </div>

        {/* Actions rapides */}
        <div className="mt-6">
          <QuickLinks context="inventaire" />
        </div>
      </div>
    </div>
  );
};
