import React from 'react';
import { useNavigate } from 'react-router-dom';
import { EmployeeManagement as EmployeeManagementComponent } from '../components/EmployeeManagement';
import { AdminAccessGuard } from '../components/guards/AdminAccessGuard';

function EmployeeManagement() {
  const navigate = useNavigate();

  const handleClose = () => {
    navigate('/dashboard');
  };

  return (
    <AdminAccessGuard>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto p-6">
          <EmployeeManagementComponent onClose={handleClose} />
        </div>
      </div>
    </AdminAccessGuard>
  );
}

export default EmployeeManagement;
