import { useState, useEffect } from 'react'
import {
  Plus,
  Settings,
  DollarSign,
  ArrowRightLeft,
  Minus,
  Play,
  Square,
  Users,
  Monitor,
  AlertCircle,
  Edit,
  Trash2
} from 'lucide-react'
import { posService, PointDeVente } from '../services/pos.service'
import { sessionCaisseService, SessionCaisse } from '../services/session.service'
import { serviceComplexeService, ServiceComplexe } from '../services/service.service'
import { employeeService, Employee } from '../services/employee.service'

interface POSManagementProps {
  onClose: () => void;
}

export function POSManagement({ onClose }: POSManagementProps) {
  // États principaux
  const [pointsDeVente, setPointsDeVente] = useState<PointDeVente[]>([])
  const [services, setServices] = useState<ServiceComplexe[]>([])
  const [employees, setEmployees] = useState<Employee[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // États pour les modals
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showSessionModal, setShowSessionModal] = useState(false)
  const [showCloseSessionModal, setShowCloseSessionModal] = useState(false)
  const [showTransferModal, setShowTransferModal] = useState(false)
  const [showWithdrawModal, setShowWithdrawModal] = useState(false)
  const [showAssignEmployeeModal, setShowAssignEmployeeModal] = useState(false)
  const [showSettingsModal, setShowSettingsModal] = useState(false)
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false)
  const [selectedPOS, setSelectedPOS] = useState<PointDeVente | null>(null)
  const [activeSession, setActiveSession] = useState<SessionCaisse | null>(null)
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<string>('')

  // États pour les formulaires
  const [newPOS, setNewPOS] = useState({
    nom: '',
    emplacement: '',
    service_id: '',
    fonds_caisse: 0
  })

  const [sessionData, setSessionData] = useState({
    fonds_ouverture: 0,
    notes: ''
  })

  const [transferData, setTransferData] = useState({
    from_pos_id: '',
    to_pos_id: '',
    montant: 0,
    notes: ''
  })

  const [withdrawData, setWithdrawData] = useState({
    montant: 0,
    notes: ''
  })

  const [closeSessionData, setCloseSessionData] = useState({
    fonds_fermeture: 0,
    total_ventes: 0,
    total_especes: 0,
    total_cartes: 0,
    total_autres: 0,
    notes: ''
  })

  const [editPOS, setEditPOS] = useState({
    nom: '',
    emplacement: '',
    service_id: '',
    fonds_caisse: 0
  })

  // Charger les données au montage
  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      setError(null)

      const [posData, servicesData, employeesData] = await Promise.all([
        posService.getAllPOS(),
        serviceComplexeService.getAllServices(),
        employeeService.getEmployees()
      ])

      setPointsDeVente(posData)
      setServices(servicesData)
      setEmployees(employeesData)
    } catch (err) {
      console.error('Erreur lors du chargement des données:', err)
      setError('Erreur lors du chargement des données')
    } finally {
      setLoading(false)
    }
  }

  // Créer un nouveau POS
  const handleCreatePOS = async () => {
    try {
      if (!newPOS.nom || !newPOS.emplacement || !newPOS.service_id) {
        alert('Veuillez remplir tous les champs obligatoires')
        return
      }

      await posService.createPOS({
        complexe_id: 1, // Sera géré automatiquement par le service
        service_id: parseInt(newPOS.service_id),
        nom: newPOS.nom,
        emplacement: newPOS.emplacement,
        fonds_caisse: newPOS.fonds_caisse
      })

      setShowCreateModal(false)
      setNewPOS({ nom: '', emplacement: '', service_id: '', fonds_caisse: 0 })
      await loadData()
    } catch (error) {
      console.error('Erreur création POS:', error)
      alert('Erreur lors de la création du point de vente')
    }
  }

  // Ouvrir une session de caisse
  const handleOpenSession = async () => {
    try {
      if (!selectedPOS) return

      await sessionCaisseService.ouvrirSession({
        pos_id: selectedPOS.pos_id,
        service_id: selectedPOS.service_id,
        fonds_ouverture: sessionData.fonds_ouverture,
        notes: sessionData.notes
      })

      setShowSessionModal(false)
      setSessionData({ fonds_ouverture: 0, notes: '' })
      setSelectedPOS(null)
      await loadData()
    } catch (error) {
      console.error('Erreur ouverture session:', error)
      alert('Erreur lors de l\'ouverture de la session')
    }
  }

  // Transférer des fonds
  const handleTransferFunds = async () => {
    try {
      if (!selectedPOS || !transferData.to_pos_id || !transferData.montant) {
        alert('Veuillez remplir tous les champs obligatoires')
        return
      }

      if (transferData.montant > selectedPOS.fonds_caisse) {
        alert('Montant supérieur aux fonds disponibles')
        return
      }

      await posService.transferFunds(
        selectedPOS.pos_id,
        parseInt(transferData.to_pos_id),
        transferData.montant,
        transferData.notes
      )

      setShowTransferModal(false)
      setTransferData({ from_pos_id: '', to_pos_id: '', montant: 0, notes: '' })
      setSelectedPOS(null)
      await loadData()
      alert('Transfert effectué avec succès')
    } catch (error) {
      console.error('Erreur transfert:', error)
      alert('Erreur lors du transfert')
    }
  }

  // Retirer des fonds
  const handleWithdrawFunds = async () => {
    try {
      if (!selectedPOS || !withdrawData.montant) {
        alert('Veuillez saisir un montant')
        return
      }

      if (withdrawData.montant > selectedPOS.fonds_caisse) {
        alert('Montant supérieur aux fonds disponibles')
        return
      }

      await posService.withdrawFunds(
        selectedPOS.pos_id,
        withdrawData.montant,
        withdrawData.notes
      )

      setShowWithdrawModal(false)
      setWithdrawData({ montant: 0, notes: '' })
      setSelectedPOS(null)
      await loadData()
      alert('Retrait effectué avec succès')
    } catch (error) {
      console.error('Erreur retrait:', error)
      alert('Erreur lors du retrait')
    }
  }

  // Ouvrir la modal de fermeture de session
  const handleOpenCloseSessionModal = async (pos: PointDeVente) => {
    try {
      const session = await sessionCaisseService.getActiveSession(pos.pos_id)
      if (session) {
        setActiveSession(session)
        setSelectedPOS(pos)
        setCloseSessionData({
          fonds_fermeture: pos.fonds_caisse,
          total_ventes: 0,
          total_especes: 0,
          total_cartes: 0,
          total_autres: 0,
          notes: ''
        })
        setShowCloseSessionModal(true)
      } else {
        alert('Aucune session active trouvée pour ce POS')
      }
    } catch (error) {
      console.error('Erreur récupération session active:', error)
      alert('Erreur lors de la récupération de la session active')
    }
  }

  // Fermer une session de caisse
  const handleCloseSession = async () => {
    try {
      if (!activeSession) {
        alert('Aucune session active')
        return
      }

      await sessionCaisseService.fermerSession(activeSession.session_id, closeSessionData)

      setShowCloseSessionModal(false)
      setCloseSessionData({
        fonds_fermeture: 0,
        total_ventes: 0,
        total_especes: 0,
        total_cartes: 0,
        total_autres: 0,
        notes: ''
      })
      setActiveSession(null)
      setSelectedPOS(null)
      await loadData()
      alert('Session fermée avec succès')
    } catch (error) {
      console.error('Erreur fermeture session:', error)
      alert('Erreur lors de la fermeture de la session')
    }
  }

  // Attribuer un employé à un POS
  const handleAssignEmployee = async () => {
    try {
      if (!selectedPOS || !selectedEmployeeId) {
        alert('Veuillez sélectionner un employé')
        return
      }

      await posService.updatePOS(selectedPOS.pos_id, {
        employe_actuel_id: parseInt(selectedEmployeeId)
      })

      setShowAssignEmployeeModal(false)
      setSelectedEmployeeId('')
      setSelectedPOS(null)
      await loadData()
      alert('Employé attribué avec succès')
    } catch (error) {
      console.error('Erreur attribution employé:', error)
      alert('Erreur lors de l\'attribution de l\'employé')
    }
  }

  // Ouvrir le modal de paramètres (modifier/supprimer)
  const handleOpenSettings = (pos: PointDeVente) => {
    setSelectedPOS(pos)
    setEditPOS({
      nom: pos.nom,
      emplacement: pos.emplacement,
      service_id: pos.service_id.toString(),
      fonds_caisse: Number(pos.fonds_caisse) || 0
    })
    setShowSettingsModal(true)
  }

  // Modifier un POS
  const handleUpdatePOS = async () => {
    try {
      if (!selectedPOS || !editPOS.nom || !editPOS.emplacement || !editPOS.service_id) {
        alert('Veuillez remplir tous les champs obligatoires')
        return
      }

      await posService.updatePOS(selectedPOS.pos_id, {
        nom: editPOS.nom,
        emplacement: editPOS.emplacement,
        fonds_caisse: editPOS.fonds_caisse
      })

      setShowSettingsModal(false)
      setSelectedPOS(null)
      setEditPOS({ nom: '', emplacement: '', service_id: '', fonds_caisse: 0 })
      await loadData()
      alert('Point de vente modifié avec succès')
    } catch (error) {
      console.error('Erreur modification POS:', error)
      alert('Erreur lors de la modification du point de vente')
    }
  }

  // Confirmer la suppression
  const handleConfirmDelete = () => {
    if (!selectedPOS) return
    setShowSettingsModal(false)
    setShowDeleteConfirmModal(true)
  }

  // Supprimer un POS
  const handleDeletePOS = async () => {
    try {
      if (!selectedPOS) return

      await posService.deletePOS(selectedPOS.pos_id)

      setShowDeleteConfirmModal(false)
      setSelectedPOS(null)
      await loadData()
      alert('Point de vente supprimé avec succès')
    } catch (error) {
      console.error('Erreur suppression POS:', error)
      alert('Erreur lors de la suppression du point de vente')
    }
  }

  // Obtenir le statut d'un POS
  const getPOSStatus = (pos: PointDeVente) => {
    if (pos.caisse_ouverte) {
      return { status: 'Ouverte', color: 'text-green-600 bg-green-100' }
    }
    return { status: 'Fermée', color: 'text-gray-600 bg-gray-100' }
  }

  // Obtenir le service associé à un POS
  const getServiceForPOS = (serviceId: number) => {
    return services.find(s => s.service_id === serviceId)
  }

  // Affichage du loading
  if (loading) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Chargement des points de vente...</div>
        </div>
      </div>
    )
  }

  // Affichage de l'erreur
  if (error) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg text-red-600">{error}</div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Gestion des Points de Vente</h1>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            <Plus className="w-4 h-4 mr-2" />
            Nouveau POS
          </button>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            Fermer
          </button>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <Monitor className="w-8 h-8 text-blue-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600">Total POS</p>
              <p className="text-2xl font-bold">{pointsDeVente.length}</p>
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <Play className="w-8 h-8 text-green-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600">Sessions Ouvertes</p>
              <p className="text-2xl font-bold">
                {pointsDeVente.filter(pos => pos.caisse_ouverte).length}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <DollarSign className="w-8 h-8 text-yellow-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600">Fonds Total</p>
              <p className="text-2xl font-bold">
                {pointsDeVente.reduce((sum, pos) => sum + (Number(pos.fonds_caisse) || 0), 0).toFixed(2)}FCFA
              </p>
            </div>
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <Users className="w-8 h-8 text-purple-500 mr-3" />
            <div>
              <p className="text-sm text-gray-600">Services Actifs</p>
              <p className="text-2xl font-bold">
                {new Set(pointsDeVente.map(pos => pos.service_id)).size}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Liste des POS */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b">
          <h2 className="text-xl font-semibold">Points de Vente</h2>
        </div>
        <div className="p-6">
          {pointsDeVente.length === 0 ? (
            <div className="text-center py-8">
              <Monitor className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">Aucun point de vente configuré</p>
              <button
                onClick={() => setShowCreateModal(true)}
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              >
                Créer le premier POS
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
              {pointsDeVente.map((pos) => {
                const service = getServiceForPOS(pos.service_id)
                const status = getPOSStatus(pos)
                
                return (
                  <div key={pos.pos_id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-semibold text-lg">{pos.nom}</h3>
                        <p className="text-sm text-gray-600">{pos.emplacement}</p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${status.color}`}>
                        {status.status}
                      </span>
                    </div>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Service:</span>
                        <span className="text-sm font-medium">{service?.nom || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Type:</span>
                        <span className="text-sm">{service?.type_service || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Fonds:</span>
                        <span className="text-sm font-medium">{(Number(pos.fonds_caisse) || 0).toFixed(2)}FCFA</span>
                      </div>
                      {pos.employe_nom && (
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">Employé:</span>
                          <span className="text-sm">{pos.employe_nom} {pos.employe_prenom}</span>
                        </div>
                      )}
                    </div>

                    <div className="flex space-x-2">
                      {!pos.caisse_ouverte ? (
                        <button
                          onClick={() => {
                            setSelectedPOS(pos)
                            setShowSessionModal(true)
                          }}
                          className="flex-1 flex items-center justify-center px-3 py-2 bg-green-500 text-white rounded text-sm hover:bg-green-600"
                        >
                          <Play className="w-4 h-4 mr-1" />
                          Ouvrir
                        </button>
                      ) : (
                        <button
                          onClick={() => handleOpenCloseSessionModal(pos)}
                          className="flex-1 flex items-center justify-center px-3 py-2 bg-red-500 text-white rounded text-sm hover:bg-red-600"
                        >
                          <Square className="w-4 h-4 mr-1" />
                          Fermer
                        </button>
                      )}
                      
                      <button
                        onClick={() => {
                          setSelectedPOS(pos)
                          setShowTransferModal(true)
                        }}
                        className="px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
                      >
                        <ArrowRightLeft className="w-4 h-4" />
                      </button>
                      
                      <button
                        onClick={() => {
                          setSelectedPOS(pos)
                          setShowWithdrawModal(true)
                        }}
                        className="px-3 py-2 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600"
                      >
                        <Minus className="w-4 h-4" />
                      </button>
                      
                      <button
                        onClick={() => {
                          setSelectedPOS(pos)
                          setShowAssignEmployeeModal(true)
                        }}
                        className="px-3 py-2 bg-purple-500 text-white rounded text-sm hover:bg-purple-600"
                        title="Attribuer employé"
                      >
                        <Users className="w-4 h-4" />
                      </button>

                      <button
                        onClick={() => handleOpenSettings(pos)}
                        className="px-3 py-2 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
                        title="Paramètres"
                      >
                        <Settings className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </div>

      {/* Modal Création POS */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Créer un nouveau POS</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nom du POS *
                </label>
                <input
                  type="text"
                  value={newPOS.nom}
                  onChange={(e) => setNewPOS({ ...newPOS, nom: e.target.value })}
                  className="w-full p-2 border rounded-lg"
                  placeholder="Ex: Caisse Piscine 1"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Emplacement *
                </label>
                <input
                  type="text"
                  value={newPOS.emplacement}
                  onChange={(e) => setNewPOS({ ...newPOS, emplacement: e.target.value })}
                  className="w-full p-2 border rounded-lg"
                  placeholder="Ex: Accueil piscine"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Service *
                </label>
                <select
                  value={newPOS.service_id}
                  onChange={(e) => setNewPOS({ ...newPOS, service_id: e.target.value })}
                  className="w-full p-2 border rounded-lg"
                >
                  <option value="">Sélectionner un service</option>
                  {services.map((service) => (
                    <option key={service.service_id} value={service.service_id}>
                      {service.nom} ({service.type_service})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Fonds de caisse initial
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={newPOS.fonds_caisse}
                  onChange={(e) => setNewPOS({ ...newPOS, fonds_caisse: parseFloat(e.target.value) || 0 })}
                  className="w-full p-2 border rounded-lg"
                  placeholder="0.00"
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowCreateModal(false)}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={handleCreatePOS}
                className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              >
                Créer
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Ouverture Session */}
      {showSessionModal && selectedPOS && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">
              Ouvrir une session - {selectedPOS.nom}
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Fonds d'ouverture *
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={sessionData.fonds_ouverture}
                  onChange={(e) => setSessionData({
                    ...sessionData,
                    fonds_ouverture: parseFloat(e.target.value) || 0
                  })}
                  className="w-full p-2 border rounded-lg"
                  placeholder="0.00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes (optionnel)
                </label>
                <textarea
                  value={sessionData.notes}
                  onChange={(e) => setSessionData({ ...sessionData, notes: e.target.value })}
                  className="w-full p-2 border rounded-lg"
                  rows={3}
                  placeholder="Notes sur l'ouverture..."
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowSessionModal(false)
                  setSelectedPOS(null)
                  setSessionData({ fonds_ouverture: 0, notes: '' })
                }}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={handleOpenSession}
                className="flex-1 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600"
              >
                Ouvrir Session
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Fermeture Session */}
      {showCloseSessionModal && selectedPOS && activeSession && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">
              Fermer la session - {selectedPOS.nom}
            </h3>

            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">Session ouverte le:</p>
              <p className="font-medium">{new Date(activeSession.date_ouverture).toLocaleString()}</p>
              <p className="text-sm text-gray-600 mt-1">Fonds d'ouverture:</p>
              <p className="font-medium">{(Number(activeSession.fonds_ouverture) || 0).toFixed(2)}FCFA</p>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Fonds de fermeture *
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={closeSessionData.fonds_fermeture}
                  onChange={(e) => setCloseSessionData({
                    ...closeSessionData,
                    fonds_fermeture: parseFloat(e.target.value) || 0
                  })}
                  className="w-full p-2 border rounded-lg"
                  placeholder="0.00"
                />
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Total ventes
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={closeSessionData.total_ventes}
                    onChange={(e) => setCloseSessionData({
                      ...closeSessionData,
                      total_ventes: parseFloat(e.target.value) || 0
                    })}
                    className="w-full p-2 border rounded-lg"
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Espèces
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={closeSessionData.total_especes}
                    onChange={(e) => setCloseSessionData({
                      ...closeSessionData,
                      total_especes: parseFloat(e.target.value) || 0
                    })}
                    className="w-full p-2 border rounded-lg"
                    placeholder="0.00"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Cartes
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={closeSessionData.total_cartes}
                    onChange={(e) => setCloseSessionData({
                      ...closeSessionData,
                      total_cartes: parseFloat(e.target.value) || 0
                    })}
                    className="w-full p-2 border rounded-lg"
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Autres
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={closeSessionData.total_autres}
                    onChange={(e) => setCloseSessionData({
                      ...closeSessionData,
                      total_autres: parseFloat(e.target.value) || 0
                    })}
                    className="w-full p-2 border rounded-lg"
                    placeholder="0.00"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes de fermeture
                </label>
                <textarea
                  value={closeSessionData.notes}
                  onChange={(e) => setCloseSessionData({ ...closeSessionData, notes: e.target.value })}
                  className="w-full p-2 border rounded-lg"
                  rows={3}
                  placeholder="Notes sur la fermeture..."
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowCloseSessionModal(false)
                  setActiveSession(null)
                  setSelectedPOS(null)
                  setCloseSessionData({
                    fonds_fermeture: 0,
                    total_ventes: 0,
                    total_especes: 0,
                    total_cartes: 0,
                    total_autres: 0,
                    notes: ''
                  })
                }}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={handleCloseSession}
                className="flex-1 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
              >
                Fermer Session
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Attribution Employé */}
      {showAssignEmployeeModal && selectedPOS && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">
              Attribuer un employé - {selectedPOS.nom}
            </h3>

            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">Employé actuel:</p>
              <p className="font-medium">
                {selectedPOS.employe_nom ?
                  `${selectedPOS.employe_nom} ${selectedPOS.employe_prenom}` :
                  'Aucun employé attribué'
                }
              </p>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nouvel employé *
                </label>
                <select
                  value={selectedEmployeeId}
                  onChange={(e) => setSelectedEmployeeId(e.target.value)}
                  className="w-full p-2 border rounded-lg"
                >
                  <option value="">Sélectionner un employé</option>
                  {employees.map((employee) => (
                    <option key={employee.employe_id} value={employee.employe_id}>
                      {employee.nom} {employee.prenom} - {employee.role_nom}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowAssignEmployeeModal(false)
                  setSelectedPOS(null)
                  setSelectedEmployeeId('')
                }}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={handleAssignEmployee}
                className="flex-1 px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600"
              >
                Attribuer
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Transfert */}
      {showTransferModal && selectedPOS && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">
              Transfert depuis {selectedPOS.nom}
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Vers le POS *
                </label>
                <select
                  value={transferData.to_pos_id}
                  onChange={(e) => setTransferData({ ...transferData, to_pos_id: e.target.value })}
                  className="w-full p-2 border rounded-lg"
                >
                  <option value="">Sélectionner un POS de destination</option>
                  {pointsDeVente
                    .filter(pos => pos.pos_id !== selectedPOS.pos_id)
                    .map((pos) => (
                      <option key={pos.pos_id} value={pos.pos_id}>
                        {pos.nom} - {(Number(pos.fonds_caisse) || 0).toFixed(2)}FCFA
                      </option>
                    ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Montant *
                </label>
                <input
                  type="number"
                  step="0.01"
                  max={selectedPOS.fonds_caisse}
                  value={transferData.montant}
                  onChange={(e) => setTransferData({
                    ...transferData,
                    montant: parseFloat(e.target.value) || 0
                  })}
                  className="w-full p-2 border rounded-lg"
                  placeholder="0.00"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Disponible: {(Number(selectedPOS.fonds_caisse) || 0).toFixed(2)}FCFA
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes (optionnel)
                </label>
                <textarea
                  value={transferData.notes}
                  onChange={(e) => setTransferData({ ...transferData, notes: e.target.value })}
                  className="w-full p-2 border rounded-lg"
                  rows={2}
                  placeholder="Raison du transfert..."
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowTransferModal(false)
                  setSelectedPOS(null)
                  setTransferData({ from_pos_id: '', to_pos_id: '', montant: 0, notes: '' })
                }}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={handleTransferFunds}
                className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              >
                Transférer
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Retrait */}
      {showWithdrawModal && selectedPOS && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">
              Retrait depuis {selectedPOS.nom}
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Montant à retirer *
                </label>
                <input
                  type="number"
                  step="0.01"
                  max={selectedPOS.fonds_caisse}
                  value={withdrawData.montant}
                  onChange={(e) => setWithdrawData({
                    ...withdrawData,
                    montant: parseFloat(e.target.value) || 0
                  })}
                  className="w-full p-2 border rounded-lg"
                  placeholder="0.00"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Disponible: {(Number(selectedPOS.fonds_caisse) || 0).toFixed(2)}FCFA
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes (optionnel)
                </label>
                <textarea
                  value={withdrawData.notes}
                  onChange={(e) => setWithdrawData({ ...withdrawData, notes: e.target.value })}
                  className="w-full p-2 border rounded-lg"
                  rows={3}
                  placeholder="Raison du retrait..."
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowWithdrawModal(false)
                  setSelectedPOS(null)
                  setWithdrawData({ montant: 0, notes: '' })
                }}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={handleWithdrawFunds}
                className="flex-1 px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600"
              >
                Retirer
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Paramètres (Modifier/Supprimer) */}
      {showSettingsModal && selectedPOS && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">
              Paramètres - {selectedPOS.nom}
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nom du POS *
                </label>
                <input
                  type="text"
                  value={editPOS.nom}
                  onChange={(e) => setEditPOS({ ...editPOS, nom: e.target.value })}
                  className="w-full p-2 border rounded-lg"
                  placeholder="Ex: Caisse Piscine 1"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Emplacement *
                </label>
                <input
                  type="text"
                  value={editPOS.emplacement}
                  onChange={(e) => setEditPOS({ ...editPOS, emplacement: e.target.value })}
                  className="w-full p-2 border rounded-lg"
                  placeholder="Ex: Accueil piscine"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Fonds de caisse
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={editPOS.fonds_caisse}
                  onChange={(e) => setEditPOS({ ...editPOS, fonds_caisse: parseFloat(e.target.value) || 0 })}
                  className="w-full p-2 border rounded-lg"
                  placeholder="0.00"
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowSettingsModal(false)
                  setSelectedPOS(null)
                  setEditPOS({ nom: '', emplacement: '', service_id: '', fonds_caisse: 0 })
                }}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={handleConfirmDelete}
                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 flex items-center"
              >
                <Trash2 className="w-4 h-4 mr-1" />
                Supprimer
              </button>
              <button
                onClick={handleUpdatePOS}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center"
              >
                <Edit className="w-4 h-4 mr-1" />
                Modifier
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Confirmation Suppression */}
      {showDeleteConfirmModal && selectedPOS && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center mb-4">
              <AlertCircle className="w-8 h-8 text-red-500 mr-3" />
              <h3 className="text-lg font-semibold">Confirmer la suppression</h3>
            </div>

            <p className="text-gray-600 mb-6">
              Êtes-vous sûr de vouloir supprimer le point de vente <strong>"{selectedPOS.nom}"</strong> ?
              Cette action est irréversible.
            </p>

            <div className="flex space-x-3">
              <button
                onClick={() => {
                  setShowDeleteConfirmModal(false)
                  setSelectedPOS(null)
                }}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={handleDeletePOS}
                className="flex-1 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
              >
                Supprimer définitivement
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
