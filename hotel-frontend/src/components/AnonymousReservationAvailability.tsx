import { useState, useEffect } from 'react';
import { Check<PERSON>ircle, XCircle, AlertCircle, Settings } from 'lucide-react';
import { anonymousReservationService, ServiceAvailability } from '../services/anonymousReservation.service';

interface AnonymousReservationAvailabilityProps {
  complexeId: number;
  onAvailabilityChange?: (available: boolean) => void;
  className?: string;
}

export function AnonymousReservationAvailability({ 
  complexeId, 
  onAvailabilityChange,
  className = '' 
}: AnonymousReservationAvailabilityProps) {
  const [availability, setAvailability] = useState<ServiceAvailability | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkAvailability();
  }, [complexeId]);

  useEffect(() => {
    if (availability && onAvailabilityChange) {
      onAvailabilityChange(availability.available);
    }
  }, [availability, onAvailabilityChange]);

  const checkAvailability = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await anonymousReservationService.checkServiceAvailability(complexeId);
      if (response.success) {
        setAvailability(response.data);
      }
    } catch (error) {
      console.error('Erreur lors de la vérification de disponibilité:', error);
      setError(error instanceof Error ? error.message : 'Erreur lors de la vérification');
      setAvailability({ available: false, message: 'Service indisponible' });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className={`bg-gray-50 rounded-lg p-4 border border-gray-200 ${className}`}>
        <div className="flex items-center space-x-3">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-400"></div>
          <span className="text-sm text-gray-600">Vérification de la disponibilité...</span>
        </div>
      </div>
    );
  }

  if (error && !availability) {
    return (
      <div className={`bg-red-50 rounded-lg p-4 border border-red-200 ${className}`}>
        <div className="flex items-start space-x-3">
          <XCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-medium text-red-800">
              Erreur de vérification
            </h4>
            <p className="text-sm text-red-700 mt-1">
              {error}
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!availability) {
    return null;
  }

  if (availability.available) {
    return (
      <div className={`bg-green-50 rounded-lg p-4 border border-green-200 ${className}`}>
        <div className="flex items-start space-x-3">
          <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
          <div className="flex-1">
            <h4 className="text-sm font-medium text-green-800">
              Réservations anonymes disponibles
            </h4>
            <p className="text-sm text-green-700 mt-1">
              {availability.message || 'Vous pouvez effectuer des réservations anonymes pour ce complexe.'}
            </p>
            
            {availability.configuration && (
              <div className="mt-3 space-y-1 text-xs text-green-600">
                <div className="flex justify-between">
                  <span>Durée de validité du code:</span>
                  <span className="font-medium">{availability.configuration.duree_validite_code_heures}h</span>
                </div>
                <div className="flex justify-between">
                  <span>Limite par IP:</span>
                  <span className="font-medium">{availability.configuration.max_reservations_par_ip} réservations</span>
                </div>
                <div className="flex justify-between">
                  <span>Préfixe des codes:</span>
                  <span className="font-medium font-mono">{availability.configuration.prefixe_code}-</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-yellow-50 rounded-lg p-4 border border-yellow-200 ${className}`}>
      <div className="flex items-start space-x-3">
        <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
        <div>
          <h4 className="text-sm font-medium text-yellow-800">
            Réservations anonymes non disponibles
          </h4>
          <p className="text-sm text-yellow-700 mt-1">
            {availability.message || 'Les réservations anonymes ne sont pas activées pour ce complexe.'}
          </p>
          <p className="text-xs text-yellow-600 mt-2">
            Contactez l'administration pour plus d'informations.
          </p>
        </div>
      </div>
    </div>
  );
}

// Composant simple pour afficher juste le statut
export function AnonymousReservationStatusBadge({ 
  complexeId, 
  className = '' 
}: { 
  complexeId: number; 
  className?: string; 
}) {
  const [available, setAvailable] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkStatus = async () => {
      try {
        const response = await anonymousReservationService.checkServiceAvailability(complexeId);
        setAvailable(response.success ? response.data.available : false);
      } catch (error) {
        setAvailable(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkStatus();
  }, [complexeId]);

  if (isLoading) {
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 ${className}`}>
        <div className="animate-spin rounded-full h-3 w-3 border-b border-gray-400 mr-1"></div>
        Vérification...
      </span>
    );
  }

  if (available === null) {
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 ${className}`}>
        <XCircle className="h-3 w-3 mr-1" />
        Erreur
      </span>
    );
  }

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
      available 
        ? 'bg-green-100 text-green-800' 
        : 'bg-red-100 text-red-800'
    } ${className}`}>
      {available ? (
        <>
          <CheckCircle className="h-3 w-3 mr-1" />
          Réservations anonymes actives
        </>
      ) : (
        <>
          <XCircle className="h-3 w-3 mr-1" />
          Réservations anonymes inactives
        </>
      )}
    </span>
  );
}

// Hook personnalisé pour vérifier la disponibilité
export function useAnonymousReservationAvailability(complexeId: number) {
  const [availability, setAvailability] = useState<ServiceAvailability | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkAvailability = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const response = await anonymousReservationService.checkServiceAvailability(complexeId);
        if (response.success) {
          setAvailability(response.data);
        }
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Erreur lors de la vérification');
        setAvailability({ available: false });
      } finally {
        setIsLoading(false);
      }
    };

    if (complexeId) {
      checkAvailability();
    }
  }, [complexeId]);

  return {
    availability,
    isLoading,
    error,
    isAvailable: availability?.available || false
  };
}
