import { useState } from 'react'
import { DollarSign, Calendar, Package, FileText, Download, Share2, FileSpreadsheet, File as FilePdf } from 'lucide-react'

const reportTypes = [
  { id: 'sales', name: 'Sales Report', icon: DollarSign },
  { id: 'reservations', name: 'Reservations Report', icon: Calendar },
  { id: 'inventory', name: 'Stock Usage Report', icon: Package }
]

const departments = [
  'All Departments',
  'Front Desk',
  'Restaurant',
  'Housekeeping',
  'Maintenance'
]

const outputFormats = [
  { id: 'pdf', name: 'PDF', icon: FilePdf },
  { id: 'csv', name: 'CSV', icon: FileSpreadsheet }
]

interface ReportsProps {
  onClose: () => void;
}

export function Reports({ onClose }: ReportsProps) {
  const [selectedReportType, setSelectedReportType] = useState(reportTypes[0])
  const [selectedDepartment, setSelectedDepartment] = useState(departments[0])
  const [selectedFormat, setSelectedFormat] = useState(outputFormats[0])
  const [dateRange, setDateRange] = useState({
    start: new Date().toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  })
  const [generatedReports, setGeneratedReports] = useState<Array<{
    id: string;
    name: string;
    type: string;
    format: string;
    date: string;
    url: string;
  }>>([
    {
      id: '1',
      name: 'March Sales Report',
      type: 'sales',
      format: 'pdf',
      date: '2024-03-15',
      url: '#'
    }
  ])

  const handleGenerateReport = () => {
    const newReport = {
      id: Math.random().toString(36).substr(2, 9),
      name: `${selectedReportType.name} - ${dateRange.start} to ${dateRange.end}`,
      type: selectedReportType.id,
      format: selectedFormat.id,
      date: new Date().toISOString().split('T')[0],
      url: '#'
    }
    setGeneratedReports([newReport, ...generatedReports])
  }

  const handleShareReport = (reportId: string) => {
    // Here you would typically implement sharing functionality
    console.log(`Sharing report ${reportId}`)
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Reports</h1>
        <button
          onClick={onClose}
          className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
        >
          Close
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Report Generator */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Generate Report</h2>
          <div className="space-y-4">
            {/* Report Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Report Type
              </label>
              <div className="grid grid-cols-3 gap-2">
                {reportTypes.map((type) => (
                  <button
                    key={type.id}
                    onClick={() => setSelectedReportType(type)}
                    className={`p-3 rounded-lg flex flex-col items-center ${
                      selectedReportType.id === type.id
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 hover:bg-gray-200'
                    }`}
                  >
                    <type.icon className="w-6 h-6 mb-1" />
                    <span className="text-sm">{type.name}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Department */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Department
              </label>
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="w-full p-2 border rounded-lg"
              >
                {departments.map((dept) => (
                  <option key={dept} value={dept}>
                    {dept}
                  </option>
                ))}
              </select>
            </div>

            {/* Date Range */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Start Date
                </label>
                <input
                  type="date"
                  value={dateRange.start}
                  onChange={(e) => setDateRange({ ...dateRange, start: e.target.value })}
                  className="w-full p-2 border rounded-lg"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  End Date
                </label>
                <input
                  type="date"
                  value={dateRange.end}
                  onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}
                  className="w-full p-2 border rounded-lg"
                />
              </div>
            </div>

            {/* Output Format */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Output Format
              </label>
              <div className="flex space-x-2">
                {outputFormats.map((format) => (
                  <button
                    key={format.id}
                    onClick={() => setSelectedFormat(format)}
                    className={`flex-1 p-3 rounded-lg flex items-center justify-center ${
                      selectedFormat.id === format.id
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 hover:bg-gray-200'
                    }`}
                  >
                    <format.icon className="w-5 h-5 mr-2" />
                    <span>{format.name}</span>
                  </button>
                ))}
              </div>
            </div>

            <button
              onClick={handleGenerateReport}
              className="w-full py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              Generate Report
            </button>
          </div>
        </div>

        {/* Generated Reports */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Generated Reports</h2>
          <div className="space-y-4">
            {generatedReports.map((report) => (
              <div
                key={report.id}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
              >
                <div>
                  <p className="font-medium">{report.name}</p>
                  <p className="text-sm text-gray-500">
                    {report.type} • {report.format.toUpperCase()} • {report.date}
                  </p>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => window.open(report.url, '_blank')}
                    className="p-2 text-blue-500 hover:bg-blue-50 rounded-lg"
                  >
                    <Download className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => handleShareReport(report.id)}
                    className="p-2 text-green-500 hover:bg-green-50 rounded-lg"
                  >
                    <Share2 className="w-5 h-5" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
} 