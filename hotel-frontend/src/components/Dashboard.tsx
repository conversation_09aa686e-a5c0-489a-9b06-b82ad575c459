import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import { AreaChart as AreaChartIcon, BarChart3, BedDouble, Calendar, Clock, DollarSign, Home, Package, Percent, ShoppingCart, Users, AlertTriangle, LogOut, Plus, Filter, Search, Waves, FileText, Ticket, Settings, Upload, ChefHat, Warehouse, Crown, UserCheck, Briefcase, Utensils } from 'lucide-react'
import { StockAlertsWidget, QuickLinks } from './ui'
import { useEmployeePermissions } from '../hooks/useEmployeePermissions'
import { useAdminPermissions } from '../hooks/useAdminPermissions'

interface RevenueData {
  date: string;
  value: number;
}

const revenueData: RevenueData[] = [
  { date: 'Mar 1', value: 4000 },
  { date: 'Mar 2', value: 3000 },
  { date: 'Mar 3', value: 5000 },
  { date: 'Mar 4', value: 2780 },
  { date: 'Mar 5', value: 1890 },
  { date: 'Mar 6', value: 2390 },
  { date: 'Mar 7', value: 3490 }
]

// Supprimé - remplacé par QuickLinks

interface StockAlert {
  item: string;
  quantity: number;
  threshold: number;
}

const stockAlerts: StockAlert[] = [
  { item: 'Bath Towels', quantity: 15, threshold: 20 },
  { item: 'Toilet Paper', quantity: 8, threshold: 25 },
  { item: 'Shampoo Bottles', quantity: 12, threshold: 15 }
]

interface RecentSale {
  id: number;
  item: string;
  amount: number;
  time: string;
}

const recentSales: RecentSale[] = [
  { id: 1, item: 'Room 304 - Suite', amount: 450, time: '2 hours ago' },
  { id: 2, item: 'Restaurant - Dinner', amount: 125, time: '3 hours ago' },
  { id: 3, item: 'Spa Service', amount: 89, time: '5 hours ago' }
]

interface DashboardProps {
  // Props supprimées - navigation gérée par QuickLinks
}

// ===== COMPOSANTS SPÉCIALISÉS =====

// Header utilisateur avec informations contextuelles
function UserHeader() {
  const { isAdmin, isEmployee, employeeType, userTypeLabel, fullName, loading } = useEmployeePermissions();

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="animate-pulse flex space-x-4">
          <div className="rounded-full bg-gray-300 h-12 w-12"></div>
          <div className="flex-1 space-y-2 py-1">
            <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            <div className="h-4 bg-gray-300 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6 mb-6">
      <div className="flex items-center space-x-4">
        <div className="flex-shrink-0">
          {isAdmin ? (
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-600 rounded-full flex items-center justify-center">
              <Crown className="w-6 h-6 text-white" />
            </div>
          ) : (
            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center">
              <UserCheck className="w-6 h-6 text-white" />
            </div>
          )}
        </div>
        <div className="flex-1">
          <h1 className="text-xl font-semibold text-gray-900">
            Bienvenue, {fullName}
          </h1>
          <p className="text-sm text-gray-600 flex items-center">
            <Briefcase className="w-4 h-4 mr-1" />
            {userTypeLabel}
            {employeeType && ` - ${employeeType}`}
          </p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-500">
            {new Date().toLocaleDateString('fr-FR', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </p>
        </div>
      </div>
    </div>
  );
}

// Dashboard Admin - Version complète
function AdminDashboard() {
  const { canAccessReports, canAccessInventory } = useAdminPermissions();

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {/* Revenue Chart - Admin complet */}
      <div className="bg-white rounded-lg shadow p-6 col-span-2">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <BarChart3 className="w-5 h-5 mr-2 text-blue-600" />
          Aperçu des Revenus
        </h2>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={revenueData}>
              <defs>
                <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="#3B82F6" stopOpacity={0}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Area
                type="monotone"
                dataKey="value"
                stroke="#3B82F6"
                fillOpacity={1}
                fill="url(#colorValue)"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Actions rapides Admin */}
      <div className="col-span-1">
        <QuickLinks context="dashboard" />
      </div>

      {/* Stock Alerts - Si accès inventaire */}
      {canAccessInventory && <StockAlertsWidget />}

      {/* Recent Sales - Admin */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <DollarSign className="w-5 h-5 mr-2 text-green-600" />
          Ventes Récentes
        </h2>
        <div className="space-y-4">
          {recentSales.map((sale) => (
            <div key={sale.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="font-medium">{sale.item}</p>
                <p className="text-sm text-gray-500">{sale.time}</p>
              </div>
              <p className="font-semibold text-green-600">${sale.amount}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Statistiques Admin */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <BarChart3 className="w-5 h-5 mr-2 text-purple-600" />
          Statistiques Générales
        </h2>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Réservations Aujourd'hui</span>
            <span className="font-semibold text-blue-600">12</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Taux d'Occupation</span>
            <span className="font-semibold text-green-600">85%</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Employés Actifs</span>
            <span className="font-semibold text-purple-600">24</span>
          </div>
        </div>
      </div>
    </div>
  );
}

// Dashboard Employé Réception
function ReceptionDashboard() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {/* Réservations du jour */}
      <div className="bg-white rounded-lg shadow p-6 col-span-2">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <Calendar className="w-5 h-5 mr-2 text-blue-600" />
          Réservations du Jour
        </h2>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
            <div>
              <p className="font-medium">Check-in: Famille Martin</p>
              <p className="text-sm text-gray-600">Chambre 204 - 14h00</p>
            </div>
            <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">En attente</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
            <div>
              <p className="font-medium">Check-out: M. Dubois</p>
              <p className="text-sm text-gray-600">Chambre 301 - 11h00</p>
            </div>
            <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Terminé</span>
          </div>
        </div>
      </div>

      {/* Actions rapides Réception */}
      <div className="col-span-1">
        <QuickLinks context="reception" />
      </div>

      {/* Statut des chambres */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <BedDouble className="w-5 h-5 mr-2 text-purple-600" />
          Statut Chambres
        </h2>
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Occupées</span>
            <span className="font-semibold text-red-600">18/25</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Disponibles</span>
            <span className="font-semibold text-green-600">7/25</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Maintenance</span>
            <span className="font-semibold text-orange-600">2/25</span>
          </div>
        </div>
      </div>
    </div>
  );
}

// Dashboard Employé Piscine
function PiscineDashboard() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {/* Activité piscine */}
      <div className="bg-white rounded-lg shadow p-6 col-span-2">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <Waves className="w-5 h-5 mr-2 text-blue-600" />
          Activité Piscine Aujourd'hui
        </h2>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
            <div>
              <p className="font-medium">Famille Dupont - 4 personnes</p>
              <p className="text-sm text-gray-600">Entrée: 10h30 - Durée: 2h</p>
            </div>
            <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Actif</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <p className="font-medium">M. et Mme Leroy - 2 personnes</p>
              <p className="text-sm text-gray-600">Sortie: 12h00 - Durée: 1h30</p>
            </div>
            <span className="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm">Terminé</span>
          </div>
        </div>
      </div>

      {/* Actions rapides Piscine */}
      <div className="col-span-1">
        <QuickLinks context="piscine" />
      </div>

      {/* Statistiques piscine */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <BarChart3 className="w-5 h-5 mr-2 text-blue-600" />
          Statistiques
        </h2>
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Visiteurs Aujourd'hui</span>
            <span className="font-semibold text-blue-600">12</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Revenus du Jour</span>
            <span className="font-semibold text-green-600">FCFA180</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Capacité Actuelle</span>
            <span className="font-semibold text-orange-600">6/20</span>
          </div>
        </div>
      </div>
    </div>
  );
}

// Dashboard Employé Service (Serveuse/Gérant)
function ServiceDashboard() {
  const { authorizedServices } = useEmployeePermissions();

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {/* Commandes en cours */}
      <div className="bg-white rounded-lg shadow p-6 col-span-2">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <ShoppingCart className="w-5 h-5 mr-2 text-green-600" />
          Commandes en Cours
        </h2>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
            <div>
              <p className="font-medium">Table 5 - Restaurant</p>
              <p className="text-sm text-gray-600">Menu du jour x2, Vin rouge</p>
            </div>
            <span className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">En préparation</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
            <div>
              <p className="font-medium">Comptoir Bar</p>
              <p className="text-sm text-gray-600">2 Cocktails, 1 Bière</p>
            </div>
            <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Prêt</span>
          </div>
        </div>
      </div>

      {/* Actions rapides Service */}
      <div className="col-span-1">
        <QuickLinks context="service" />
      </div>

      {/* Services autorisés */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <Settings className="w-5 h-5 mr-2 text-purple-600" />
          Mes Services
        </h2>
        <div className="space-y-3">
          {authorizedServices.includes('restaurant') && (
            <div className="flex items-center p-3 bg-green-50 rounded-lg">
              <ChefHat className="w-5 h-5 mr-3 text-green-600" />
              <span className="font-medium text-green-800">Restaurant</span>
            </div>
          )}
          {authorizedServices.includes('bar') && (
            <div className="flex items-center p-3 bg-blue-50 rounded-lg">
              <Package className="w-5 h-5 mr-3 text-blue-600" />
              <span className="font-medium text-blue-800">Bar</span>
            </div>
          )}
        </div>
      </div>

      {/* Statistiques service */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <BarChart3 className="w-5 h-5 mr-2 text-green-600" />
          Performance
        </h2>
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Commandes Servies</span>
            <span className="font-semibold text-green-600">23</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Chiffre d'Affaires</span>
            <span className="font-semibold text-blue-600">FCFA450</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Tables Actives</span>
            <span className="font-semibold text-orange-600">8/15</span>
          </div>
        </div>
      </div>
    </div>
  );
}

// Dashboard Employé Cuisine
function CuisineDashboard() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {/* Commandes cuisine */}
      <div className="bg-white rounded-lg shadow p-6 col-span-2">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <ChefHat className="w-5 h-5 mr-2 text-orange-600" />
          Commandes à Préparer
        </h2>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
            <div>
              <p className="font-medium">Table 3 - URGENT</p>
              <p className="text-sm text-gray-600">Steak frites, Salade César</p>
              <p className="text-xs text-red-600">Commandé il y a 15 min</p>
            </div>
            <span className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">Urgent</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
            <div>
              <p className="font-medium">Table 7</p>
              <p className="text-sm text-gray-600">Poisson du jour x2</p>
              <p className="text-xs text-yellow-600">Commandé il y a 8 min</p>
            </div>
            <span className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">En cours</span>
          </div>
        </div>
      </div>

      {/* Actions rapides Cuisine */}
      <div className="col-span-1">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            <ChefHat className="w-5 h-5 mr-2 text-orange-600" />
            Actions Rapides
          </h2>
          <div className="space-y-3">
            <button
              onClick={() => window.location.href = '/kitchen'}
              className="w-full flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              <ChefHat className="w-4 h-4 mr-2" />
              Interface Cuisine Complète
            </button>
            <button
              onClick={() => window.location.href = '/pos?view=kitchen'}
              className="w-full flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              <Utensils className="w-4 h-4 mr-2" />
              Vue POS Cuisine
            </button>
          </div>
        </div>
      </div>

      {/* Statistiques cuisine */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <Clock className="w-5 h-5 mr-2 text-orange-600" />
          Performance Cuisine
        </h2>
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Plats Préparés</span>
            <span className="font-semibold text-green-600">34</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Temps Moyen</span>
            <span className="font-semibold text-blue-600">12 min</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">En Attente</span>
            <span className="font-semibold text-orange-600">5</span>
          </div>
        </div>
      </div>
    </div>
  );
}

// ===== COMPOSANT PRINCIPAL =====

export function Dashboard({}: DashboardProps) {
  const { isAdmin, isEmployee, employeeType, loading, error } = useEmployeePermissions();

  // Loading state
  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <div className="flex space-x-4">
              <div className="rounded-full bg-gray-300 h-12 w-12"></div>
              <div className="flex-1 space-y-2 py-1">
                <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                <div className="h-4 bg-gray-300 rounded w-1/2"></div>
              </div>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow p-6">
                <div className="h-4 bg-gray-300 rounded w-1/2 mb-4"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-300 rounded"></div>
                  <div className="h-4 bg-gray-300 rounded w-5/6"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center">
            <AlertTriangle className="w-6 h-6 text-red-600 mr-3" />
            <div>
              <h3 className="text-lg font-medium text-red-800">Erreur de Chargement</h3>
              <p className="text-red-600 mt-1">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Fonction pour rendre le dashboard approprié
  const renderDashboard = () => {
    if (isAdmin) {
      return <AdminDashboard />;
    }

    if (isEmployee && employeeType) {
      switch (employeeType) {
        case 'reception':
          return <ReceptionDashboard />;
        case 'gerant_piscine':
          return <PiscineDashboard />;
        case 'serveuse':
        case 'gerant_services':
          return <ServiceDashboard />;
        case 'cuisine':
          return <CuisineDashboard />;
        default:
          return (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
              <div className="flex items-center">
                <AlertTriangle className="w-6 h-6 text-yellow-600 mr-3" />
                <div>
                  <h3 className="text-lg font-medium text-yellow-800">Type d'Employé Non Reconnu</h3>
                  <p className="text-yellow-600 mt-1">
                    Type d'employé "{employeeType}" non supporté. Contactez l'administrateur.
                  </p>
                </div>
              </div>
            </div>
          );
      }
    }

    // Fallback - ne devrait pas arriver
    return (
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
        <div className="flex items-center">
          <AlertTriangle className="w-6 h-6 text-gray-600 mr-3" />
          <div>
            <h3 className="text-lg font-medium text-gray-800">Accès Non Défini</h3>
            <p className="text-gray-600 mt-1">
              Impossible de déterminer le type d'interface à afficher. Contactez l'administrateur.
            </p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6">
      <UserHeader />
      {renderDashboard()}
    </div>
  );
}