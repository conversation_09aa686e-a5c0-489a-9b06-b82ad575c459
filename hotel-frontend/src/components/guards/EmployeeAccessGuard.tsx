import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Users, AlertTriangle, ArrowLeft, Briefcase } from 'lucide-react';
import { useEmployeePermissions } from '../../hooks/useEmployeePermissions';
import { useComplexeAccess, useComplexeStatus } from '../../hooks/useComplexeAccess';

// ===== INTERFACES =====

interface EmployeeAccessGuardProps {
  children: React.ReactNode;
  requiredType?: string | string[];
  requiredService?: string | string[];
  requiredPermission?: string;
  fallbackRoute?: string;
  showAccessDenied?: boolean;
  allowAdmin?: boolean;
}

// ===== COMPOSANT PRINCIPAL =====

const EmployeeAccessGuard: React.FC<EmployeeAccessGuardProps> = ({
  children,
  requiredType,
  requiredService,
  requiredPermission,
  fallbackRoute = '/dashboard',
  showAccessDenied = true,
  allowAdmin = true
}) => {
  const location = useLocation();
  const [showError, setShowError] = useState(false);
  
  // Hooks de permissions
  const { 
    loading: employeeLoading, 
    error: employeeError,
    canAccess,
    canAccessService,
    isAdmin,
    isEmployee,
    employeeType,
    employeeTypeInfo,
    userTypeLabel,
    fullName,
    authorizedServices
  } = useEmployeePermissions();
  
  const {
    hasAccess: hasComplexeAccess,
    needsSelection
  } = useComplexeAccess();

  const {
    isAuthenticated,
    getDefaultRoute
  } = useComplexeStatus();

  const loading = employeeLoading;
  const error = employeeError;

  useEffect(() => {
    if (!loading && error) {
      setShowError(true);
    }
  }, [loading, error]);

  // Affichage du loading
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Vérification des permissions employé...</p>
        </div>
      </div>
    );
  }

  // Vérification de l'authentification
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Vérification de la sélection de complexe
  if (needsSelection) {
    return <Navigate to="/patron/complexes" state={{ from: location }} replace />;
  }

  // Vérification de l'accès au complexe
  if (!hasComplexeAccess) {
    return <Navigate to="/login" replace />;
  }

  // Affichage d'erreur
  if (showError && showAccessDenied) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center mb-4">
            <AlertTriangle className="h-8 w-8 text-red-500 mr-3" />
            <h2 className="text-xl font-semibold text-gray-900">Erreur de permission</h2>
          </div>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => window.location.href = fallbackRoute}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
          >
            Retour au tableau de bord
          </button>
        </div>
      </div>
    );
  }

  // Si admin et allowAdmin=true, accès automatique
  if (isAdmin && allowAdmin) {
    return <>{children}</>;
  }

  // Vérification que l'utilisateur est un employé (si admin non autorisé)
  if (!isEmployee && !allowAdmin) {
    if (!showAccessDenied) {
      return <Navigate to={fallbackRoute} replace />;
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center mb-4">
            <Users className="h-8 w-8 text-blue-500 mr-3" />
            <h2 className="text-xl font-semibold text-gray-900">
              Accès Employé Requis
            </h2>
          </div>
          <p className="text-gray-600 mb-4">
            Cette page est réservée aux employés uniquement.
          </p>
          <p className="text-sm text-gray-500 mb-6">
            Utilisateur actuel : <span className="font-medium">{fullName}</span> ({userTypeLabel})
          </p>
          <button
            onClick={() => window.location.href = fallbackRoute}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour au tableau de bord
          </button>
        </div>
      </div>
    );
  }

  // Vérification du type d'employé requis
  if (requiredType && !isAdmin) {
    const allowedTypes = Array.isArray(requiredType) ? requiredType : [requiredType];
    
    if (!employeeType || !allowedTypes.includes(employeeType)) {
      if (!showAccessDenied) {
        const defaultRoute = getDefaultRoute();
        return <Navigate to={defaultRoute} replace />;
      }

      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center mb-4">
              <Briefcase className="h-8 w-8 text-orange-500 mr-3" />
              <h2 className="text-xl font-semibold text-gray-900">
                Type d'employé requis
              </h2>
            </div>
            <p className="text-gray-600 mb-4">
              Cette page nécessite un type d'employé spécifique pour y accéder.
            </p>
            <div className="text-sm text-gray-500 mb-6">
              <p>Votre type : <span className="font-medium">{employeeTypeInfo?.label || 'Non défini'}</span></p>
              <p>Types requis : <span className="font-medium">{allowedTypes.join(', ')}</span></p>
            </div>
            <div className="space-y-3">
              <button
                onClick={() => window.location.href = getDefaultRoute()}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
              >
                Aller vers mon espace de travail
              </button>
              <button
                onClick={() => window.location.href = fallbackRoute}
                className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors flex items-center justify-center"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour au tableau de bord
              </button>
            </div>
          </div>
        </div>
      );
    }
  }

  // Vérification du service requis
  if (requiredService && !isAdmin) {
    const allowedServices = Array.isArray(requiredService) ? requiredService : [requiredService];
    const hasServiceAccess = allowedServices.some(service => canAccessService(service));
    
    if (!hasServiceAccess) {
      if (!showAccessDenied) {
        const defaultRoute = getDefaultRoute();
        return <Navigate to={defaultRoute} replace />;
      }

      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center mb-4">
              <Briefcase className="h-8 w-8 text-purple-500 mr-3" />
              <h2 className="text-xl font-semibold text-gray-900">
                Accès au service requis
              </h2>
            </div>
            <p className="text-gray-600 mb-4">
              Vous n'avez pas accès aux services nécessaires pour cette page.
            </p>
            <div className="text-sm text-gray-500 mb-6">
              <p>Vos services : <span className="font-medium">{authorizedServices.join(', ') || 'Aucun'}</span></p>
              <p>Services requis : <span className="font-medium">{allowedServices.join(', ')}</span></p>
            </div>
            <div className="space-y-3">
              <button
                onClick={() => window.location.href = getDefaultRoute()}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
              >
                Aller vers mon espace de travail
              </button>
              <button
                onClick={() => window.location.href = fallbackRoute}
                className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors flex items-center justify-center"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour au tableau de bord
              </button>
            </div>
          </div>
        </div>
      );
    }
  }

  // Vérification de la permission spécifique
  if (requiredPermission && !isAdmin) {
    if (!canAccess(requiredPermission)) {
      if (!showAccessDenied) {
        const defaultRoute = getDefaultRoute();
        return <Navigate to={defaultRoute} replace />;
      }

      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center mb-4">
              <Users className="h-8 w-8 text-red-500 mr-3" />
              <h2 className="text-xl font-semibold text-gray-900">
                Permission spécifique requise
              </h2>
            </div>
            <p className="text-gray-600 mb-4">
              Vous n'avez pas la permission spécifique nécessaire pour accéder à cette fonctionnalité.
            </p>
            <div className="text-sm text-gray-500 mb-6">
              <p>Permission requise : <span className="font-medium">{requiredPermission}</span></p>
              <p>Votre type : <span className="font-medium">{employeeTypeInfo?.label || 'Non défini'}</span></p>
            </div>
            <button
              onClick={() => window.location.href = getDefaultRoute()}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              Aller vers mon espace de travail
            </button>
          </div>
        </div>
      );
    }
  }

  // L'utilisateur a les permissions nécessaires, afficher le contenu
  return <>{children}</>;
};

export default EmployeeAccessGuard;
export { EmployeeAccessGuard };
