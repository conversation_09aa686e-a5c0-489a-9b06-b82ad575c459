// ===== GUARDS PRINCIPAUX =====
export { default as AdminAccessGuard, RequireAdmin, RequireSuperAdmin } from './AdminAccessGuard';
export { default as EmployeeAccessGuard } from './EmployeeAccessGuard';
export { default as ServiceAccessGuard } from './ServiceAccessGuard';

// ===== GUARDS SPÉCIALISÉS =====
export {
  // Guards par rôle
  ReceptionGuard,
  PiscineGuard,
  ServiceGuard,
  CuisineGuard,
  
  // Guards par fonctionnalité
  EmployeeManagementGuard,
  ReportsGuard,
  InventoryGuard,
  POSManagementGuard,
  
  // Guards combinés
  ManagementGuard,
  OperationalGuard,
  
  // Guards conditionnels
  ConditionalGuard,
  
  // Guards utilitaires
  SilentRedirectGuard,
  DevelopmentGuard
} from './SpecializedGuards';

// ===== TYPES =====
export type { ServiceType } from '../../services/servicePermission.service';

// ===== CONSTANTES UTILES =====
export const EMPLOYEE_TYPES = {
  RECEPTION: 'reception',
  GERANT_PISCINE: 'gerant_piscine',
  SERVEUSE: 'serveuse',
  GERANT_SERVICES: 'gerant_services',
  CUISINE: 'cuisine'
} as const;

export const PERMISSIONS = {
  RECEPTION_OPERATIONS: 'reception_operations',
  PISCINE_OPERATIONS: 'piscine_operations',
  SERVICE_OPERATIONS: 'service_operations',
  MANAGEMENT_OPERATIONS: 'management_operations',
  KITCHEN_OPERATIONS: 'kitchen_operations'
} as const;

export const SERVICES = {
  BAR: 'bar',
  RESTAURANT: 'restaurant',
  PISCINE: 'piscine',
  HEBERGEMENT: 'hebergement'
} as const;

// ===== HELPERS POUR LES GUARDS =====

/**
 * Helper pour créer des guards personnalisés
 */
export const createCustomGuard = (config: {
  adminOnly?: boolean;
  employeeTypes?: string[];
  services?: string[];
  permissions?: string[];
  fallback?: string;
  showError?: boolean;
}) => {
  return ({ children }: { children: React.ReactNode }) => {
    if (config.adminOnly) {
      return (
        <AdminAccessGuard 
          fallbackRoute={config.fallback || '/dashboard'}
          showAccessDenied={config.showError !== false}
        >
          {children}
        </AdminAccessGuard>
      );
    }

    return (
      <EmployeeAccessGuard
        requiredType={config.employeeTypes}
        requiredService={config.services}
        requiredPermission={config.permissions?.[0]}
        fallbackRoute={config.fallback || '/dashboard'}
        showAccessDenied={config.showError !== false}
        allowAdmin={true}
      >
        {children}
      </EmployeeAccessGuard>
    );
  };
};

/**
 * Helper pour vérifier les permissions sans guard
 */
export const checkAccess = {
  isAdmin: () => {
    // Cette fonction sera utilisée dans les composants qui ont besoin de vérifications conditionnelles
    // sans utiliser de guard complet
    return true; // Placeholder - sera implémenté avec les hooks
  },
  
  hasEmployeeType: (type: string) => {
    return true; // Placeholder
  },
  
  hasService: (service: string) => {
    return true; // Placeholder
  },
  
  hasPermission: (permission: string) => {
    return true; // Placeholder
  }
};

// ===== DOCUMENTATION =====

/**
 * Guide d'utilisation des guards :
 * 
 * 1. Guards principaux :
 *    - AdminAccessGuard : Pour les pages admin uniquement
 *    - EmployeeAccessGuard : Pour les pages employé avec conditions
 *    - ServiceAccessGuard : Pour les pages de service spécifiques
 * 
 * 2. Guards spécialisés :
 *    - ReceptionGuard : Pages de réception
 *    - PiscineGuard : Pages de piscine
 *    - ServiceGuard : Pages de service (bar/restaurant)
 *    - CuisineGuard : Pages de cuisine
 * 
 * 3. Guards par fonctionnalité :
 *    - EmployeeManagementGuard : Gestion des employés
 *    - ReportsGuard : Rapports
 *    - InventoryGuard : Inventaire
 *    - POSManagementGuard : Gestion POS
 * 
 * 4. Utilisation :
 *    ```tsx
 *    import { ReceptionGuard } from '@/components/guards';
 *    
 *    const ReceptionPage = () => (
 *      <ReceptionGuard>
 *        <div>Contenu de la page réception</div>
 *      </ReceptionGuard>
 *    );
 *    ```
 * 
 * 5. Guards personnalisés :
 *    ```tsx
 *    const CustomGuard = createCustomGuard({
 *      employeeTypes: ['serveuse', 'gerant_services'],
 *      services: ['bar'],
 *      fallback: '/dashboard'
 *    });
 *    ```
 */
