import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Shield, AlertTriangle, ArrowLeft, Crown } from 'lucide-react';
import { useAdminPermissions } from '../../hooks/useAdminPermissions';
import { useComplexeAccess, useComplexeStatus } from '../../hooks/useComplexeAccess';

// ===== INTERFACES =====

interface AdminAccessGuardProps {
  children: React.ReactNode;
  fallbackRoute?: string;
  requireSuperAdmin?: boolean;
  requiredPage?: string;
  showAccessDenied?: boolean;
}

// ===== COMPOSANT PRINCIPAL =====

const AdminAccessGuard: React.FC<AdminAccessGuardProps> = ({
  children,
  fallbackRoute = '/dashboard',
  requireSuperAdmin = false,
  requiredPage,
  showAccessDenied = true
}) => {
  const location = useLocation();
  const [showError, setShowError] = useState(false);
  
  // Hooks de permissions
  const { 
    isAdmin, 
    isSuperAdmin, 
    loading: adminLoading, 
    error: adminError,
    canAccessPage,
    userTypeLabel,
    fullName
  } = useAdminPermissions();
  
  const {
    hasAccess: hasComplexeAccess,
    needsSelection
  } = useComplexeAccess();

  const {
    isAuthenticated,
    getDefaultRoute
  } = useComplexeStatus();

  const loading = adminLoading;
  const error = adminError;

  useEffect(() => {
    if (!loading && error) {
      setShowError(true);
    }
  }, [loading, error]);

  // Affichage du loading
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Vérification des permissions administrateur...</p>
        </div>
      </div>
    );
  }

  // Vérification de l'authentification
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Vérification de la sélection de complexe
  if (needsSelection) {
    return <Navigate to="/patron/complexes" state={{ from: location }} replace />;
  }

  // Vérification de l'accès au complexe
  if (!hasComplexeAccess) {
    return <Navigate to="/login" replace />;
  }

  // Affichage d'erreur
  if (showError && showAccessDenied) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center mb-4">
            <AlertTriangle className="h-8 w-8 text-red-500 mr-3" />
            <h2 className="text-xl font-semibold text-gray-900">Erreur de permission</h2>
          </div>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => window.location.href = fallbackRoute}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors"
          >
            Retour au tableau de bord
          </button>
        </div>
      </div>
    );
  }

  // Vérification des permissions admin
  if (requireSuperAdmin && !isSuperAdmin) {
    if (!showAccessDenied) {
      return <Navigate to={fallbackRoute} replace />;
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center mb-4">
            <Crown className="h-8 w-8 text-yellow-500 mr-3" />
            <h2 className="text-xl font-semibold text-gray-900">
              Accès Super Administrateur Requis
            </h2>
          </div>
          <p className="text-gray-600 mb-4">
            Cette fonctionnalité nécessite des privilèges de super administrateur.
          </p>
          <p className="text-sm text-gray-500 mb-6">
            Utilisateur actuel : <span className="font-medium">{fullName}</span> ({userTypeLabel})
          </p>
          <button
            onClick={() => window.location.href = fallbackRoute}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors flex items-center justify-center"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour au tableau de bord
          </button>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    if (!showAccessDenied) {
      const defaultRoute = getDefaultRoute();
      return <Navigate to={defaultRoute} replace />;
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center mb-4">
            <Shield className="h-8 w-8 text-red-500 mr-3" />
            <h2 className="text-xl font-semibold text-gray-900">
              Accès Administrateur Requis
            </h2>
          </div>
          <p className="text-gray-600 mb-4">
            Cette page est réservée aux administrateurs. Vous n'avez pas les permissions nécessaires pour y accéder.
          </p>
          <p className="text-sm text-gray-500 mb-6">
            Utilisateur actuel : <span className="font-medium">{fullName}</span> ({userTypeLabel})
          </p>
          <div className="space-y-3">
            <button
              onClick={() => window.location.href = getDefaultRoute()}
              className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors"
            >
              Aller vers mon espace de travail
            </button>
            <button
              onClick={() => window.location.href = fallbackRoute}
              className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors flex items-center justify-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Retour au tableau de bord
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Vérification d'accès à une page spécifique
  if (requiredPage && !canAccessPage(requiredPage)) {
    if (!showAccessDenied) {
      return <Navigate to={fallbackRoute} replace />;
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center mb-4">
            <Shield className="h-8 w-8 text-orange-500 mr-3" />
            <h2 className="text-xl font-semibold text-gray-900">
              Accès à la page restreint
            </h2>
          </div>
          <p className="text-gray-600 mb-4">
            Vous n'avez pas les permissions nécessaires pour accéder à cette page spécifique.
          </p>
          <p className="text-sm text-gray-500 mb-6">
            Page demandée : <span className="font-medium">{requiredPage}</span>
          </p>
          <button
            onClick={() => window.location.href = fallbackRoute}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors flex items-center justify-center"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour au tableau de bord
          </button>
        </div>
      </div>
    );
  }

  // L'utilisateur a les permissions nécessaires, afficher le contenu
  return <>{children}</>;
};

export default AdminAccessGuard;
export { AdminAccessGuard };

// ===== COMPOSANTS UTILITAIRES =====

/**
 * Guard simplifié pour les pages qui nécessitent uniquement d'être admin
 */
export const RequireAdmin: React.FC<{ children: React.ReactNode; fallback?: string }> = ({ 
  children, 
  fallback = '/dashboard' 
}) => {
  return (
    <AdminAccessGuard fallbackRoute={fallback} showAccessDenied={false}>
      {children}
    </AdminAccessGuard>
  );
};

/**
 * Guard pour les pages qui nécessitent d'être super admin
 */
export const RequireSuperAdmin: React.FC<{ children: React.ReactNode; fallback?: string }> = ({ 
  children, 
  fallback = '/dashboard' 
}) => {
  return (
    <AdminAccessGuard 
      requireSuperAdmin={true} 
      fallbackRoute={fallback} 
      showAccessDenied={false}
    >
      {children}
    </AdminAccessGuard>
  );
};
