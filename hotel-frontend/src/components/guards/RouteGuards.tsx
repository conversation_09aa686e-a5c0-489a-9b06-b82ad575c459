import React from 'react';
import { Navigate } from 'react-router-dom';
import { useEmployeePermissions } from '../../hooks/useEmployeePermissions';
import { useAdminPermissions } from '../../hooks/useAdminPermissions';
import { ContextualErrorPages } from '../errors/ContextualErrorPages';

// Types pour les guards
interface PermissionContext {
  isAdmin: boolean;
  isEmployee: boolean;
  employeeType: string | null;
  authorizedServices: string[];
  loading: boolean;
  error: string | null;
}

interface GuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
}

// Guard pour plusieurs types d'employés
interface MultiTypeGuardProps extends GuardProps {
  allowedTypes: ('admin' | 'employee' | string)[];
  requireAllTypes?: boolean;
}

export const MultiTypeGuard: React.FC<MultiTypeGuardProps> = ({
  children,
  allowedTypes,
  requireAllTypes = false,
  fallback,
  redirectTo = '/errors/403'
}) => {
  const { isAdmin, isEmployee, employeeType, loading, error } = useEmployeePermissions();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Vérification des permissions...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return fallback || <Navigate to="/errors/500" replace />;
  }

  const hasAccess = () => {
    const userTypes: string[] = [];
    
    if (isAdmin) userTypes.push('admin');
    if (isEmployee) userTypes.push('employee');
    if (employeeType) userTypes.push(employeeType);

    if (requireAllTypes) {
      return allowedTypes.every(type => userTypes.includes(type));
    } else {
      return allowedTypes.some(type => userTypes.includes(type));
    }
  };

  if (!hasAccess()) {
    return fallback || <Navigate to={redirectTo} replace />;
  }

  return <>{children}</>;
};

// Guard basé sur les services autorisés
interface ServiceGuardProps extends GuardProps {
  requiredServices: string[];
  requireAllServices?: boolean;
}

export const ServiceGuard: React.FC<ServiceGuardProps> = ({
  children,
  requiredServices,
  requireAllServices = false,
  fallback,
  redirectTo = '/errors/403'
}) => {
  const { authorizedServices, loading, error } = useEmployeePermissions();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Vérification des services autorisés...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return fallback || <Navigate to="/errors/500" replace />;
  }

  const hasServiceAccess = () => {
    if (requireAllServices) {
      return requiredServices.every(service => authorizedServices.includes(service));
    } else {
      return requiredServices.some(service => authorizedServices.includes(service));
    }
  };

  if (!hasServiceAccess()) {
    return fallback || <Navigate to={redirectTo} replace />;
  }

  return <>{children}</>;
};

// Guard avec condition personnalisée
interface ConditionalGuardProps extends GuardProps {
  condition: (context: PermissionContext) => boolean;
  errorMessage?: string;
}

export const ConditionalGuard: React.FC<ConditionalGuardProps> = ({
  children,
  condition,
  fallback,
  redirectTo = '/errors/403',
  errorMessage = 'Accès non autorisé'
}) => {
  const permissions = useEmployeePermissions();
  const { loading, error } = permissions;

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Vérification des conditions d'accès...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return fallback || <Navigate to="/errors/500" replace />;
  }

  const hasAccess = condition(permissions);

  if (!hasAccess) {
    return fallback || (
      <ContextualErrorPages 
        type="403" 
        customMessage={errorMessage}
      />
    );
  }

  return <>{children}</>;
};

// Guard avec redirection intelligente
interface RedirectGuardProps {
  children: React.ReactNode;
  condition: (context: PermissionContext) => boolean;
  getRedirectPath: (context: PermissionContext) => string;
}

export const RedirectGuard: React.FC<RedirectGuardProps> = ({
  children,
  condition,
  getRedirectPath
}) => {
  const permissions = useEmployeePermissions();
  const { loading, error } = permissions;

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Redirection en cours...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return <Navigate to="/errors/500" replace />;
  }

  const hasAccess = condition(permissions);

  if (!hasAccess) {
    const redirectPath = getRedirectPath(permissions);
    return <Navigate to={redirectPath} replace />;
  }

  return <>{children}</>;
};

// Guard composé pour des vérifications complexes
interface CompositeGuardProps extends GuardProps {
  guards: Array<{
    type: 'admin' | 'employee' | 'service' | 'custom';
    config: any;
  }>;
  operator?: 'AND' | 'OR';
}

export const CompositeGuard: React.FC<CompositeGuardProps> = ({
  children,
  guards,
  operator = 'AND',
  fallback,
  redirectTo = '/errors/403'
}) => {
  const permissions = useEmployeePermissions();
  const adminPermissions = useAdminPermissions();
  const { loading, error } = permissions;

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Vérification des permissions composées...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return fallback || <Navigate to="/errors/500" replace />;
  }

  const checkGuard = (guard: any) => {
    switch (guard.type) {
      case 'admin':
        return permissions.isAdmin && (
          !guard.config.requiredPage || 
          adminPermissions[`canAccess${guard.config.requiredPage.charAt(0).toUpperCase() + guard.config.requiredPage.slice(1)}`]
        );
      
      case 'employee':
        return permissions.isEmployee && (
          !guard.config.requiredType || 
          (Array.isArray(guard.config.requiredType) 
            ? guard.config.requiredType.includes(permissions.employeeType)
            : guard.config.requiredType === permissions.employeeType)
        );
      
      case 'service':
        return guard.config.requiredServices.some((service: string) => 
          permissions.authorizedServices.includes(service)
        );
      
      case 'custom':
        return guard.config.condition(permissions);
      
      default:
        return false;
    }
  };

  const results = guards.map(checkGuard);
  const hasAccess = operator === 'AND' 
    ? results.every(result => result)
    : results.some(result => result);

  if (!hasAccess) {
    return fallback || <Navigate to={redirectTo} replace />;
  }

  return <>{children}</>;
};

// Guard pour les heures d'ouverture (exemple d'usage avancé)
interface TimeBasedGuardProps extends GuardProps {
  allowedHours: { start: number; end: number };
  timezone?: string;
  bypassForAdmin?: boolean;
}

export const TimeBasedGuard: React.FC<TimeBasedGuardProps> = ({
  children,
  allowedHours,
  timezone = 'Europe/Paris',
  bypassForAdmin = true,
  fallback,
  redirectTo = '/errors/403'
}) => {
  const { isAdmin, loading, error } = useEmployeePermissions();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Vérification des heures d'accès...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return fallback || <Navigate to="/errors/500" replace />;
  }

  // Bypass pour les admins si configuré
  if (bypassForAdmin && isAdmin) {
    return <>{children}</>;
  }

  const now = new Date();
  const currentHour = now.getHours();
  
  const isWithinAllowedHours = currentHour >= allowedHours.start && currentHour <= allowedHours.end;

  if (!isWithinAllowedHours) {
    return fallback || (
      <ContextualErrorPages 
        type="403" 
        customMessage={`Accès autorisé uniquement entre ${allowedHours.start}h et ${allowedHours.end}h`}
      />
    );
  }

  return <>{children}</>;
};

// Export des types pour utilisation externe
export type { PermissionContext, GuardProps, MultiTypeGuardProps, ServiceGuardProps, ConditionalGuardProps };

// Helpers pour créer des guards personnalisés
export const createCustomGuard = (
  condition: (context: PermissionContext) => boolean,
  errorMessage?: string
) => {
  return ({ children, fallback, redirectTo = '/errors/403' }: GuardProps) => (
    <ConditionalGuard
      condition={condition}
      errorMessage={errorMessage}
      fallback={fallback}
      redirectTo={redirectTo}
    >
      {children}
    </ConditionalGuard>
  );
};

export const createServiceBasedGuard = (
  requiredServices: string[],
  requireAll = false
) => {
  return ({ children, fallback, redirectTo = '/errors/403' }: GuardProps) => (
    <ServiceGuard
      requiredServices={requiredServices}
      requireAllServices={requireAll}
      fallback={fallback}
      redirectTo={redirectTo}
    >
      {children}
    </ServiceGuard>
  );
};
