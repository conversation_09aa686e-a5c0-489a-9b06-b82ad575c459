import React from 'react';
import AdminAccessGuard from './AdminAccessGuard';
import EmployeeAccessGuard from './EmployeeAccessGuard';
import ServiceAccessGuard from './ServiceAccessGuard';
import { ServiceType } from '../../services/servicePermission.service';

// ===== GUARDS SPÉCIALISÉS PAR RÔLE =====

/**
 * Guard pour les pages de réception
 * Accessible aux admins et employés de réception
 */
export const ReceptionGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <EmployeeAccessGuard 
      requiredType="reception" 
      fallbackRoute="/dashboard"
      allowAdmin={true}
    >
      {children}
    </EmployeeAccessGuard>
  );
};

/**
 * Guard pour les pages de piscine
 * Accessible aux admins et gérants de piscine
 */
export const PiscineGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <EmployeeAccessGuard 
      requiredType="gerant_piscine" 
      requiredService="piscine"
      fallbackRoute="/dashboard"
      allowAdmin={true}
    >
      {children}
    </EmployeeAccessGuard>
  );
};

/**
 * Guard pour les pages de service (bar/restaurant)
 * Accessible aux admins, serveuses et gérants de services
 */
export const ServiceGuard: React.FC<{ 
  children: React.ReactNode; 
  serviceType?: ServiceType;
  requireManagement?: boolean;
}> = ({ children, serviceType, requireManagement = false }) => {
  if (serviceType) {
    return (
      <ServiceAccessGuard 
        serviceType={serviceType}
        requireOperation={requireManagement}
        fallbackRoute="/dashboard"
      >
        {children}
      </ServiceAccessGuard>
    );
  }

  return (
    <EmployeeAccessGuard 
      requiredType={requireManagement ? "gerant_services" : ["serveuse", "gerant_services"]}
      requiredService={["bar", "restaurant"]}
      fallbackRoute="/dashboard"
      allowAdmin={true}
    >
      {children}
    </EmployeeAccessGuard>
  );
};

/**
 * Guard pour les pages de cuisine
 * Accessible aux admins et employés de cuisine
 */
export const CuisineGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <EmployeeAccessGuard 
      requiredType="cuisine" 
      requiredService="restaurant"
      fallbackRoute="/dashboard"
      allowAdmin={true}
    >
      {children}
    </EmployeeAccessGuard>
  );
};

/**
 * Guard pour les pages de gestion des employés
 * Accessible uniquement aux admins
 */
export const EmployeeManagementGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <AdminAccessGuard 
      requiredPage="employeeManagement"
      fallbackRoute="/dashboard"
    >
      {children}
    </AdminAccessGuard>
  );
};

/**
 * Guard pour les pages de rapports
 * Accessible uniquement aux admins
 */
export const ReportsGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <AdminAccessGuard 
      requiredPage="reports"
      fallbackRoute="/dashboard"
    >
      {children}
    </AdminAccessGuard>
  );
};

/**
 * Guard pour les pages d'inventaire
 * Accessible aux admins et gérants de services
 */
export const InventoryGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <EmployeeAccessGuard 
      requiredPermission="management_operations"
      fallbackRoute="/dashboard"
      allowAdmin={true}
    >
      {children}
    </EmployeeAccessGuard>
  );
};

/**
 * Guard pour les pages de gestion POS
 * Accessible uniquement aux admins
 */
export const POSManagementGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <AdminAccessGuard 
      requiredPage="posManagement"
      fallbackRoute="/dashboard"
    >
      {children}
    </AdminAccessGuard>
  );
};

// ===== GUARDS COMBINÉS =====

/**
 * Guard pour les pages qui nécessitent des permissions de gestion
 * Accessible aux admins et gérants de services
 */
export const ManagementGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <EmployeeAccessGuard 
      requiredPermission="management_operations"
      fallbackRoute="/dashboard"
      allowAdmin={true}
    >
      {children}
    </EmployeeAccessGuard>
  );
};

/**
 * Guard pour les pages opérationnelles générales
 * Accessible à tous les employés authentifiés
 */
export const OperationalGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <EmployeeAccessGuard 
      fallbackRoute="/login"
      allowAdmin={true}
      showAccessDenied={false}
    >
      {children}
    </EmployeeAccessGuard>
  );
};

// ===== GUARDS CONDITIONNELS =====

/**
 * Guard conditionnel basé sur le type d'utilisateur
 */
export const ConditionalGuard: React.FC<{
  children: React.ReactNode;
  adminOnly?: boolean;
  employeeTypes?: string[];
  services?: string[];
  permissions?: string[];
  fallback?: string;
}> = ({ 
  children, 
  adminOnly = false, 
  employeeTypes, 
  services, 
  permissions,
  fallback = '/dashboard' 
}) => {
  if (adminOnly) {
    return (
      <AdminAccessGuard fallbackRoute={fallback}>
        {children}
      </AdminAccessGuard>
    );
  }

  return (
    <EmployeeAccessGuard
      requiredType={employeeTypes}
      requiredService={services}
      requiredPermission={permissions?.[0]}
      fallbackRoute={fallback}
      allowAdmin={true}
    >
      {children}
    </EmployeeAccessGuard>
  );
};

// ===== GUARDS UTILITAIRES =====

/**
 * Guard qui redirige automatiquement selon le type d'utilisateur
 * Sans affichage d'erreur
 */
export const SilentRedirectGuard: React.FC<{
  children: React.ReactNode;
  condition: boolean;
  redirectTo: string;
}> = ({ children, condition, redirectTo }) => {
  if (!condition) {
    window.location.href = redirectTo;
    return null;
  }
  return <>{children}</>;
};

/**
 * Guard pour les fonctionnalités en développement
 * Accessible uniquement aux super admins
 */
export const DevelopmentGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <AdminAccessGuard 
      requireSuperAdmin={true}
      fallbackRoute="/dashboard"
    >
      <div className="relative">
        <div className="absolute top-0 right-0 bg-yellow-500 text-white px-2 py-1 text-xs rounded-bl-md z-10">
          DÉVELOPPEMENT
        </div>
        {children}
      </div>
    </AdminAccessGuard>
  );
};

// ===== EXPORTS =====

export {
  AdminAccessGuard,
  EmployeeAccessGuard,
  ServiceAccessGuard
};
