import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { AlertTriangle, Lock, ArrowLeft, Settings } from 'lucide-react';
import { ServiceType } from '../../services/servicePermission.service';
import { useServiceAccess, useServiceRedirect } from '../../hooks/useServicePermissions';
import { useEmployeePermissions } from '../../hooks/useEmployeePermissions';
import { useComplexeAccess } from '../../hooks/useComplexeAccess';

interface ServiceAccessGuardProps {
  serviceType: ServiceType;
  requireOperation?: boolean;
  children: React.ReactNode;
  fallbackRoute?: string;
}

const ServiceAccessGuard: React.FC<ServiceAccessGuardProps> = ({
  serviceType,
  requireOperation = false,
  children,
  fallbackRoute = '/dashboard'
}) => {
  const { canAccess, canOperate, loading, error } = useServiceAccess(serviceType);
  const { getDefaultServiceRoute, canAccessAnyService } = useServiceRedirect();
  const {
    isAdmin,
    employeeType,
    employeeTypeInfo,
    userTypeLabel,
    fullName,
    authorizedServices
  } = useEmployeePermissions();
  const {
    hasAccess: hasComplexeAccess,
    needsSelection,
    isAuthenticated,
    getDefaultRoute
  } = useComplexeAccess();

  const location = useLocation();
  const [showError, setShowError] = useState(false);

  useEffect(() => {
    if (!loading && error) {
      setShowError(true);
    }
  }, [loading, error]);

  // Vérification de l'authentification
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Vérification de la sélection de complexe
  if (needsSelection) {
    return <Navigate to="/patron/complexes" state={{ from: location }} replace />;
  }

  // Vérification de l'accès au complexe
  if (!hasComplexeAccess) {
    return <Navigate to="/login" replace />;
  }

  // Affichage du loading
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Vérification des permissions de service...</p>
        </div>
      </div>
    );
  }

  // Affichage d'erreur
  if (showError) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center mb-4">
            <AlertTriangle className="h-8 w-8 text-red-500 mr-3" />
            <h2 className="text-xl font-semibold text-gray-900">Erreur de permission</h2>
          </div>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => window.location.href = fallbackRoute}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors"
          >
            Retour au tableau de bord
          </button>
        </div>
      </div>
    );
  }

  // Vérification des permissions
  const hasRequiredPermission = requireOperation ? canOperate : canAccess;

  if (!hasRequiredPermission) {
    // Si admin, ne devrait pas arriver (debug)
    if (isAdmin) {
      console.warn(`Admin user blocked from ${serviceType} - this should not happen`);
    }

    // Si l'utilisateur n'a pas accès à ce service, essayer de le rediriger vers un service autorisé
    const defaultRoute = getDefaultServiceRoute();

    if (defaultRoute && location.pathname !== defaultRoute) {
      return <Navigate to={defaultRoute} replace />;
    }

    // Si aucun service n'est accessible, afficher une page d'accès refusé
    if (!canAccessAnyService()) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center mb-4">
              <Lock className="h-8 w-8 text-red-500 mr-3" />
              <h2 className="text-xl font-semibold text-gray-900">Accès refusé</h2>
            </div>
            <p className="text-gray-600 mb-4">
              Vous n'avez pas les permissions nécessaires pour accéder aux services du complexe.
            </p>
            <div className="text-sm text-gray-500 mb-6">
              <p>Utilisateur : <span className="font-medium">{fullName}</span> ({userTypeLabel})</p>
              {employeeType && (
                <p>Type d'employé : <span className="font-medium">{employeeTypeInfo?.label}</span></p>
              )}
              <p>Services autorisés : <span className="font-medium">{authorizedServices.join(', ') || 'Aucun'}</span></p>
            </div>
            <div className="space-y-3">
              <button
                onClick={() => window.location.href = getDefaultRoute()}
                className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors"
              >
                Aller vers mon espace de travail
              </button>
              <button
                onClick={() => window.location.href = fallbackRoute}
                className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors flex items-center justify-center"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour au tableau de bord
              </button>
            </div>
          </div>
        </div>
      );
    }

    // Afficher une page d'accès refusé spécifique au service
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center mb-4">
            <Settings className="h-8 w-8 text-orange-500 mr-3" />
            <h2 className="text-xl font-semibold text-gray-900">
              Accès refusé - {serviceType}
            </h2>
          </div>
          <p className="text-gray-600 mb-4">
            Vous n'avez pas les permissions nécessaires pour accéder à l'interface {serviceType}.
            {requireOperation
              ? ' Vous avez besoin des permissions d\'opération pour cette fonctionnalité.'
              : ' Vous avez besoin des permissions d\'accès pour ce service.'
            }
          </p>
          <div className="text-sm text-gray-500 mb-6">
            <p>Utilisateur : <span className="font-medium">{fullName}</span> ({userTypeLabel})</p>
            {employeeType && (
              <p>Type d'employé : <span className="font-medium">{employeeTypeInfo?.label}</span></p>
            )}
            <p>Services autorisés : <span className="font-medium">{authorizedServices.join(', ') || 'Aucun'}</span></p>
            <p>Service demandé : <span className="font-medium">{serviceType}</span></p>
          </div>
          <div className="space-y-3">
            {defaultRoute && (
              <button
                onClick={() => window.location.href = defaultRoute}
                className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 transition-colors"
              >
                Aller vers un service autorisé
              </button>
            )}
            <button
              onClick={() => window.location.href = getDefaultRoute()}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              Aller vers mon espace de travail
            </button>
            <button
              onClick={() => window.location.href = fallbackRoute}
              className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors flex items-center justify-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Retour au tableau de bord
            </button>
          </div>
        </div>
      </div>
    );
  }

  // L'utilisateur a les permissions nécessaires, afficher le contenu
  return <>{children}</>;
};

export default ServiceAccessGuard;
