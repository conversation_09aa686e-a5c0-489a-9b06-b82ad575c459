import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  AlertTriangle, 
  Lock, 
  FileX, 
  Server, 
  Home, 
  ArrowLeft, 
  RefreshCw,
  Shield,
  User,
  Settings,
  HelpCircle
} from 'lucide-react';
import { useEmployeePermissions } from '../../hooks/useEmployeePermissions';

interface ContextualErrorPagesProps {
  type: '403' | '404' | '401' | '500';
  customMessage?: string;
  showSuggestions?: boolean;
  onRetry?: () => void;
}

export const ContextualErrorPages: React.FC<ContextualErrorPagesProps> = ({
  type,
  customMessage,
  showSuggestions = true,
  onRetry
}) => {
  const navigate = useNavigate();
  const { isAdmin, isEmployee, employeeType, userTypeLabel } = useEmployeePermissions();

  const getErrorConfig = () => {
    switch (type) {
      case '403':
        return {
          icon: <Lock className="w-16 h-16 text-red-500" />,
          title: 'Accès Refusé',
          message: customMessage || 'Vous n\'avez pas les permissions nécessaires pour accéder à cette page.',
          color: 'red',
          suggestions: getSuggestions403()
        };
      case '404':
        return {
          icon: <FileX className="w-16 h-16 text-gray-500" />,
          title: 'Page Non Trouvée',
          message: customMessage || 'La page que vous recherchez n\'existe pas ou a été déplacée.',
          color: 'gray',
          suggestions: getSuggestions404()
        };
      case '401':
        return {
          icon: <User className="w-16 h-16 text-orange-500" />,
          title: 'Non Authentifié',
          message: customMessage || 'Vous devez vous connecter pour accéder à cette page.',
          color: 'orange',
          suggestions: getSuggestions401()
        };
      case '500':
        return {
          icon: <Server className="w-16 h-16 text-purple-500" />,
          title: 'Erreur Serveur',
          message: customMessage || 'Une erreur interne s\'est produite. Veuillez réessayer plus tard.',
          color: 'purple',
          suggestions: getSuggestions500()
        };
      default:
        return {
          icon: <AlertTriangle className="w-16 h-16 text-gray-500" />,
          title: 'Erreur',
          message: 'Une erreur inattendue s\'est produite.',
          color: 'gray',
          suggestions: []
        };
    }
  };

  const getSuggestions403 = () => {
    const suggestions = [];

    if (isAdmin) {
      suggestions.push({
        icon: <Settings className="w-5 h-5" />,
        title: 'Vérifier les permissions',
        description: 'Vérifiez vos permissions d\'administrateur',
        action: () => navigate('/employee-management')
      });
    }

    if (isEmployee) {
      switch (employeeType) {
        case 'reception':
          suggestions.push({
            icon: <User className="w-5 h-5" />,
            title: 'Interface Réception',
            description: 'Accéder à votre interface de réception',
            action: () => navigate('/reception')
          });
          break;
        case 'gerant_piscine':
          suggestions.push({
            icon: <User className="w-5 h-5" />,
            title: 'Interface Piscine',
            description: 'Accéder à votre interface piscine',
            action: () => navigate('/pool')
          });
          break;
        case 'serveuse':
        case 'gerant_services':
          suggestions.push({
            icon: <User className="w-5 h-5" />,
            title: 'Interface POS',
            description: 'Accéder à votre interface de service',
            action: () => navigate('/pos')
          });
          break;
        case 'cuisine':
          suggestions.push({
            icon: <User className="w-5 h-5" />,
            title: 'Interface Cuisine',
            description: 'Accéder à votre interface cuisine',
            action: () => navigate('/kitchen')
          });
          break;
      }
    }

    suggestions.push({
      icon: <Home className="w-5 h-5" />,
      title: 'Retour au Dashboard',
      description: 'Retourner à la page d\'accueil',
      action: () => navigate('/dashboard')
    });

    return suggestions;
  };

  const getSuggestions404 = () => {
    const suggestions = [
      {
        icon: <Home className="w-5 h-5" />,
        title: 'Page d\'accueil',
        description: 'Retourner au dashboard principal',
        action: () => navigate('/dashboard')
      }
    ];

    if (isAdmin) {
      suggestions.push(
        {
          icon: <Settings className="w-5 h-5" />,
          title: 'Gestion des employés',
          description: 'Gérer les employés et leurs permissions',
          action: () => navigate('/employee-management')
        },
        {
          icon: <Shield className="w-5 h-5" />,
          title: 'Rapports',
          description: 'Consulter les rapports et statistiques',
          action: () => navigate('/reports')
        }
      );
    }

    if (isEmployee) {
      switch (employeeType) {
        case 'reception':
          suggestions.push({
            icon: <User className="w-5 h-5" />,
            title: 'Réservations',
            description: 'Gérer les réservations et clients',
            action: () => navigate('/reception')
          });
          break;
        case 'gerant_piscine':
          suggestions.push({
            icon: <User className="w-5 h-5" />,
            title: 'Piscine',
            description: 'Gérer la piscine et la billetterie',
            action: () => navigate('/pool')
          });
          break;
        case 'serveuse':
        case 'gerant_services':
          suggestions.push({
            icon: <User className="w-5 h-5" />,
            title: 'Point de Vente',
            description: 'Gérer les commandes et le service',
            action: () => navigate('/pos')
          });
          break;
        case 'cuisine':
          suggestions.push({
            icon: <User className="w-5 h-5" />,
            title: 'Cuisine',
            description: 'Gérer les commandes à préparer',
            action: () => navigate('/kitchen')
          });
          break;
      }
    }

    return suggestions;
  };

  const getSuggestions401 = () => [
    {
      icon: <User className="w-5 h-5" />,
      title: 'Se connecter',
      description: 'Accéder à la page de connexion',
      action: () => navigate('/login')
    }
  ];

  const getSuggestions500 = () => {
    const suggestions = [
      {
        icon: <RefreshCw className="w-5 h-5" />,
        title: 'Réessayer',
        description: 'Recharger la page et réessayer',
        action: () => window.location.reload()
      },
      {
        icon: <Home className="w-5 h-5" />,
        title: 'Page d\'accueil',
        description: 'Retourner au dashboard',
        action: () => navigate('/dashboard')
      }
    ];

    if (onRetry) {
      suggestions.unshift({
        icon: <RefreshCw className="w-5 h-5" />,
        title: 'Réessayer l\'action',
        description: 'Tenter à nouveau l\'opération',
        action: onRetry
      });
    }

    return suggestions;
  };

  const config = getErrorConfig();

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full">
        {/* Icône et titre principal */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            {config.icon}
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {config.title}
          </h1>
          <p className="text-gray-600 text-lg">
            {config.message}
          </p>
        </div>

        {/* Informations utilisateur */}
        {(isAdmin || isEmployee) && (
          <div className="bg-white rounded-lg shadow p-4 mb-6">
            <div className="flex items-center space-x-3">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                isAdmin ? 'bg-purple-100' : 'bg-blue-100'
              }`}>
                {isAdmin ? (
                  <Shield className={`w-5 h-5 ${isAdmin ? 'text-purple-600' : 'text-blue-600'}`} />
                ) : (
                  <User className="w-5 h-5 text-blue-600" />
                )}
              </div>
              <div>
                <p className="font-medium text-gray-900">
                  Connecté en tant que {userTypeLabel}
                </p>
                {employeeType && (
                  <p className="text-sm text-gray-600 capitalize">
                    {employeeType.replace('_', ' ')}
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Suggestions d'actions */}
        {showSuggestions && config.suggestions.length > 0 && (
          <div className="space-y-3 mb-6">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <HelpCircle className="w-5 h-5 mr-2" />
              Que souhaitez-vous faire ?
            </h3>
            {config.suggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={suggestion.action}
                className="w-full flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-left"
              >
                <div className={`flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center mr-4 bg-${config.color}-100`}>
                  <div className={`text-${config.color}-600`}>
                    {suggestion.icon}
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">
                    {suggestion.title}
                  </h4>
                  <p className="text-sm text-gray-600">
                    {suggestion.description}
                  </p>
                </div>
              </button>
            ))}
          </div>
        )}

        {/* Actions par défaut */}
        <div className="flex space-x-3">
          <button
            onClick={() => navigate(-1)}
            className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Retour
          </button>
          <button
            onClick={() => navigate('/dashboard')}
            className={`flex-1 inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-${config.color}-600 hover:bg-${config.color}-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-${config.color}-500`}
          >
            <Home className="w-4 h-4 mr-2" />
            Accueil
          </button>
        </div>

        {/* Code d'erreur */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-500">
            Code d'erreur: {type}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ContextualErrorPages;
