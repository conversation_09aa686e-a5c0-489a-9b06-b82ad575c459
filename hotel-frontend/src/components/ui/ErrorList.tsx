import React, { useState } from 'react';
import { AlertCircle, ChevronDown, ChevronRight, Info } from 'lucide-react';
import type { ImportError } from '../../types';

interface ErrorListProps {
  errors: ImportError[];
}

interface GroupedErrors {
  [key: string]: ImportError[];
}

export const ErrorList: React.FC<ErrorListProps> = ({ errors }) => {
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());

  if (!errors || errors.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <Info className="h-8 w-8 text-green-500 mx-auto mb-2" />
        <p>Aucune erreur détectée</p>
      </div>
    );
  }

  // Grouper les erreurs par type
  const groupedErrors: GroupedErrors = errors.reduce((acc, error) => {
    const errorType = error.erreur.split(':')[0] || 'Erreur générale';
    if (!acc[errorType]) {
      acc[errorType] = [];
    }
    acc[errorType].push(error);
    return acc;
  }, {} as GroupedErrors);

  const toggleGroup = (groupName: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupName)) {
      newExpanded.delete(groupName);
    } else {
      newExpanded.add(groupName);
    }
    setExpandedGroups(newExpanded);
  };

  const getErrorSeverity = (error: ImportError): 'error' | 'warning' => {
    // Déterminer la sévérité basée sur le type d'erreur
    const errorText = error.erreur.toLowerCase();
    if (errorText.includes('obligatoire') || errorText.includes('requis') || errorText.includes('manquant')) {
      return 'error';
    }
    return 'warning';
  };

  const getSeverityColor = (severity: 'error' | 'warning') => {
    return severity === 'error' 
      ? 'text-red-600 bg-red-50 border-red-200'
      : 'text-orange-600 bg-orange-50 border-orange-200';
  };

  const getSeverityIcon = (severity: 'error' | 'warning') => {
    return severity === 'error' 
      ? <AlertCircle className="h-4 w-4" />
      : <Info className="h-4 w-4" />;
  };

  return (
    <div className="space-y-4">
      {/* Résumé des erreurs */}
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center">
          <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
          <div>
            <h4 className="text-sm font-medium text-red-800">
              {errors.length} erreur(s) détectée(s)
            </h4>
            <p className="text-sm text-red-700">
              {Object.keys(groupedErrors).length} type(s) d'erreur différent(s)
            </p>
          </div>
        </div>
      </div>

      {/* Liste groupée des erreurs */}
      <div className="space-y-3">
        {Object.entries(groupedErrors).map(([groupName, groupErrors]) => {
          const isExpanded = expandedGroups.has(groupName);
          const firstError = groupErrors[0];
          const severity = getErrorSeverity(firstError);

          return (
            <div key={groupName} className="border border-gray-200 rounded-lg overflow-hidden">
              {/* Header du groupe */}
              <button
                onClick={() => toggleGroup(groupName)}
                className={`w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50 transition-colors ${getSeverityColor(severity)}`}
              >
                <div className="flex items-center">
                  {getSeverityIcon(severity)}
                  <div className="ml-3">
                    <h5 className="font-medium">{groupName}</h5>
                    <p className="text-sm opacity-75">
                      {groupErrors.length} occurrence(s)
                    </p>
                  </div>
                </div>
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </button>

              {/* Détails du groupe */}
              {isExpanded && (
                <div className="border-t border-gray-200 bg-white">
                  <div className="max-h-64 overflow-y-auto">
                    {groupErrors.map((error, index) => (
                      <div
                        key={index}
                        className="px-4 py-3 border-b border-gray-100 last:border-b-0"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center text-sm">
                              <span className="font-medium text-gray-700">
                                Ligne {error.ligne}
                              </span>
                              {error.colonne && (
                                <span className="ml-2 text-gray-500">
                                  • Colonne: {error.colonne}
                                </span>
                              )}
                            </div>
                            <p className="text-sm text-gray-900 mt-1">
                              {error.erreur}
                            </p>
                            {error.valeur_problematique && (
                              <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
                                <span className="font-medium text-gray-700">Valeur problématique:</span>
                                <span className="ml-1 text-gray-900 font-mono">
                                  {typeof error.valeur_problematique === 'object' 
                                    ? JSON.stringify(error.valeur_problematique)
                                    : String(error.valeur_problematique)
                                  }
                                </span>
                              </div>
                            )}
                            {error.suggestion && (
                              <div className="mt-2 p-2 bg-blue-50 rounded text-xs">
                                <span className="font-medium text-blue-700">Suggestion:</span>
                                <span className="ml-1 text-blue-900">
                                  {error.suggestion}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Conseils pour résoudre les erreurs */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-800 mb-2">
          💡 Conseils pour résoudre les erreurs
        </h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Vérifiez que toutes les colonnes obligatoires sont remplies</li>
          <li>• Assurez-vous que les formats de données correspondent aux attentes</li>
          <li>• Utilisez le template Excel fourni pour éviter les erreurs de structure</li>
          <li>• Corrigez les erreurs dans votre fichier Excel et relancez l'import</li>
        </ul>
      </div>
    </div>
  );
};
