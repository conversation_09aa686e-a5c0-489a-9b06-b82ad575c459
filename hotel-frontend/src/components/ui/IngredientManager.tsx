import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, Edit, Trash2, Eye } from 'lucide-react';
import { IngredientModal } from './IngredientModal';
import { inventaireService } from '../../services';
import type { 
  Ingredient, 
  IngredientFilters, 
  CategorieIngredient, 
  TypeConservation 
} from '../../types';

interface IngredientManagerProps {
  complexeId: number;
  serviceId?: number;
  mode: 'selection' | 'management';
  onIngredientSelect?: (ingredient: Ingredient) => void;
  selectedIngredients?: number[];
}

interface IngredientFiltersComponentProps {
  filters: IngredientFilters;
  onFiltersChange: (filters: IngredientFilters) => void;
  onReset: () => void;
}

const CATEGORIES: CategorieIngredient[] = [
  'Légumes', 'Viandes', 'Poissons', 'Boissons', 
  'Épices', 'Produits laitiers', 'Céréales', 'Fruits'
];

const TYPES_CONSERVATION: TypeConservation[] = ['Frais', 'Congelé', 'Sec', 'Ambiant'];

const IngredientFiltersComponent: React.FC<IngredientFiltersComponentProps> = ({
  filters,
  onFiltersChange,
  onReset
}) => {
  const [showFilters, setShowFilters] = useState(false);

  return (
    <div className="space-y-4">
      {/* Barre de recherche */}
      <div className="flex space-x-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <input
            type="text"
            value={filters.search || ''}
            onChange={(e) => onFiltersChange({ ...filters, search: e.target.value })}
            placeholder="Rechercher un ingrédient..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center"
        >
          <Filter className="h-4 w-4 mr-2" />
          Filtres
        </button>
        <button
          onClick={onReset}
          className="px-4 py-2 text-gray-600 hover:text-gray-800"
        >
          Réinitialiser
        </button>
      </div>

      {/* Filtres avancés */}
      {showFilters && (
        <div className="grid grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Catégorie
            </label>
            <select
              value={filters.categorie || ''}
              onChange={(e) => onFiltersChange({ 
                ...filters, 
                categorie: e.target.value as CategorieIngredient || undefined 
              })}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">Toutes les catégories</option>
              {CATEGORIES.map(cat => (
                <option key={cat} value={cat}>{cat}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Conservation
            </label>
            <select
              value={filters.conservation || ''}
              onChange={(e) => onFiltersChange({ 
                ...filters, 
                conservation: e.target.value as TypeConservation || undefined 
              })}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">Tous les types</option>
              {TYPES_CONSERVATION.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Statut
            </label>
            <select
              value={filters.actif !== undefined ? filters.actif.toString() : ''}
              onChange={(e) => onFiltersChange({ 
                ...filters, 
                actif: e.target.value === '' ? undefined : e.target.value === 'true'
              })}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">Tous</option>
              <option value="true">Actifs</option>
              <option value="false">Inactifs</option>
            </select>
          </div>
        </div>
      )}
    </div>
  );
};

interface IngredientListProps {
  ingredients: Ingredient[];
  mode: 'selection' | 'management';
  selectedIngredients: number[];
  onSelect?: (ingredient: Ingredient) => void;
  onEdit: (ingredient: Ingredient) => void;
  onDelete: (id: number) => void;
}

const IngredientList: React.FC<IngredientListProps> = ({
  ingredients,
  mode,
  selectedIngredients,
  onSelect,
  onEdit,
  onDelete
}) => {
  if (ingredients.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        Aucun ingrédient trouvé
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            {mode === 'selection' && (
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Sélection
              </th>
            )}
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Nom
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Catégorie
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Unité
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Prix moyen
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Conservation
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Statut
            </th>
            {mode === 'management' && (
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            )}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {ingredients.map((ingredient) => (
            <tr 
              key={ingredient.ingredient_id}
              className={mode === 'selection' ? 'hover:bg-gray-50 cursor-pointer' : ''}
              onClick={mode === 'selection' && onSelect ? () => onSelect(ingredient) : undefined}
            >
              {mode === 'selection' && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    checked={selectedIngredients.includes(ingredient.ingredient_id)}
                    onChange={() => onSelect?.(ingredient)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </td>
              )}
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm font-medium text-gray-900">{ingredient.nom}</div>
                {ingredient.description && (
                  <div className="text-sm text-gray-500">{ingredient.description}</div>
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                  {ingredient.categorie}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {ingredient.unite_mesure}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {ingredient.prix_unitaire_moyen.toFixed(2)} FCFA
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`px-2 py-1 text-xs rounded-full ${
                  ingredient.conservation === 'Frais' ? 'bg-green-100 text-green-800' :
                  ingredient.conservation === 'Congelé' ? 'bg-blue-100 text-blue-800' :
                  ingredient.conservation === 'Sec' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {ingredient.conservation}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`px-2 py-1 text-xs rounded-full ${
                  ingredient.actif ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {ingredient.actif ? 'Actif' : 'Inactif'}
                </span>
              </td>
              {mode === 'management' && (
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end space-x-2">
                    <button
                      onClick={() => onEdit(ingredient)}
                      className="text-blue-600 hover:text-blue-900"
                      title="Modifier"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => onDelete(ingredient.ingredient_id)}
                      className="text-red-600 hover:text-red-900"
                      title="Supprimer"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export const IngredientManager: React.FC<IngredientManagerProps> = ({
  complexeId,
  serviceId,
  mode,
  onIngredientSelect,
  selectedIngredients = []
}) => {
  const [ingredients, setIngredients] = useState<Ingredient[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState<IngredientFilters>({});
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingIngredient, setEditingIngredient] = useState<Ingredient | null>(null);

  useEffect(() => {
    loadIngredients();
  }, [complexeId, filters]);

  const loadIngredients = async () => {
    try {
      setLoading(true);
      const result = await inventaireService.getIngredients(complexeId, filters);
      setIngredients(result.ingredients);
    } catch (error) {
      console.error('Erreur lors du chargement des ingrédients:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateIngredient = async (data: Partial<Ingredient>) => {
    try {
      const newIngredient = await inventaireService.createIngredient({
        ...data,
        complexe_id: complexeId
      });
      setIngredients(prev => [newIngredient, ...prev]);
      setShowCreateModal(false);
    } catch (error) {
      console.error('Erreur lors de la création de l\'ingrédient:', error);
      throw error;
    }
  };

  const handleUpdateIngredient = async (id: number, data: Partial<Ingredient>) => {
    try {
      const updatedIngredient = await inventaireService.updateIngredient(id, data);
      setIngredients(prev => prev.map(ing =>
        ing.ingredient_id === id ? updatedIngredient : ing
      ));
      setEditingIngredient(null);
    } catch (error) {
      console.error('Erreur lors de la mise à jour de l\'ingrédient:', error);
      throw error;
    }
  };

  const handleDeleteIngredient = async (id: number) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet ingrédient ?')) return;

    try {
      await inventaireService.deleteIngredient(id);
      setIngredients(prev => prev.filter(ing => ing.ingredient_id !== id));
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'ingrédient:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header avec filtres */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">
          Gestion des Ingrédients
        </h2>
        {mode === 'management' && (
          <button
            onClick={() => setShowCreateModal(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
          >
            <Plus className="h-4 w-4 mr-2" />
            Nouvel ingrédient
          </button>
        )}
      </div>

      {/* Filtres */}
      <IngredientFiltersComponent
        filters={filters}
        onFiltersChange={setFilters}
        onReset={() => setFilters({})}
      />

      {/* Liste des ingrédients */}
      {loading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <IngredientList
          ingredients={ingredients}
          mode={mode}
          selectedIngredients={selectedIngredients}
          onSelect={onIngredientSelect}
          onEdit={setEditingIngredient}
          onDelete={handleDeleteIngredient}
        />
      )}

      {/* Modals */}
      {showCreateModal && (
        <IngredientModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSave={handleCreateIngredient}
          title="Créer un ingrédient"
        />
      )}

      {editingIngredient && (
        <IngredientModal
          isOpen={!!editingIngredient}
          onClose={() => setEditingIngredient(null)}
          onSave={(data) => handleUpdateIngredient(editingIngredient.ingredient_id, data)}
          ingredient={editingIngredient}
          title="Modifier l'ingrédient"
        />
      )}
    </div>
  );
};
