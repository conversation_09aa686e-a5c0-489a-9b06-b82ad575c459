import React from 'react';
import { Lightbulb, CheckCircle, AlertTriangle, Info } from 'lucide-react';

interface SuggestionListProps {
  suggestions: string[];
}

interface CategorizedSuggestion {
  type: 'optimization' | 'warning' | 'info';
  text: string;
  icon: React.ReactNode;
  color: string;
}

export const SuggestionList: React.FC<SuggestionListProps> = ({ suggestions }) => {
  if (!suggestions || suggestions.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
        <p>Aucune suggestion d'amélioration</p>
        <p className="text-sm">Vos données semblent optimales !</p>
      </div>
    );
  }

  // Catégoriser les suggestions basées sur leur contenu
  const categorizeSuggestion = (suggestion: string): CategorizedSuggestion => {
    const lowerSuggestion = suggestion.toLowerCase();
    
    if (lowerSuggestion.includes('optimis') || lowerSuggestion.includes('améliorer') || lowerSuggestion.includes('recommand')) {
      return {
        type: 'optimization',
        text: suggestion,
        icon: <Lightbulb className="h-4 w-4" />,
        color: 'text-blue-600 bg-blue-50 border-blue-200'
      };
    }
    
    if (lowerSuggestion.includes('attention') || lowerSuggestion.includes('vérifi') || lowerSuggestion.includes('prudence')) {
      return {
        type: 'warning',
        text: suggestion,
        icon: <AlertTriangle className="h-4 w-4" />,
        color: 'text-orange-600 bg-orange-50 border-orange-200'
      };
    }
    
    return {
      type: 'info',
      text: suggestion,
      icon: <Info className="h-4 w-4" />,
      color: 'text-gray-600 bg-gray-50 border-gray-200'
    };
  };

  const categorizedSuggestions = suggestions.map(categorizeSuggestion);

  // Grouper par type
  const groupedSuggestions = categorizedSuggestions.reduce((acc, suggestion) => {
    if (!acc[suggestion.type]) {
      acc[suggestion.type] = [];
    }
    acc[suggestion.type].push(suggestion);
    return acc;
  }, {} as Record<string, CategorizedSuggestion[]>);

  const getSectionTitle = (type: string): string => {
    switch (type) {
      case 'optimization': return 'Optimisations recommandées';
      case 'warning': return 'Points d\'attention';
      case 'info': return 'Informations utiles';
      default: return 'Suggestions';
    }
  };

  const getSectionIcon = (type: string): React.ReactNode => {
    switch (type) {
      case 'optimization': return <Lightbulb className="h-5 w-5 text-blue-600" />;
      case 'warning': return <AlertTriangle className="h-5 w-5 text-orange-600" />;
      case 'info': return <Info className="h-5 w-5 text-gray-600" />;
      default: return <Info className="h-5 w-5 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center">
        <Lightbulb className="h-5 w-5 text-blue-600 mr-2" />
        <h4 className="text-lg font-medium text-gray-900">
          Suggestions d'amélioration
        </h4>
        <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
          {suggestions.length}
        </span>
      </div>

      {/* Suggestions groupées */}
      <div className="space-y-4">
        {Object.entries(groupedSuggestions).map(([type, typeSuggestions]) => (
          <div key={type} className="space-y-3">
            {/* Titre de section */}
            <div className="flex items-center">
              {getSectionIcon(type)}
              <h5 className="ml-2 font-medium text-gray-900">
                {getSectionTitle(type)}
              </h5>
              <span className="ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                {typeSuggestions.length}
              </span>
            </div>

            {/* Liste des suggestions */}
            <div className="space-y-2">
              {typeSuggestions.map((suggestion, index) => (
                <div
                  key={index}
                  className={`p-3 border rounded-lg ${suggestion.color}`}
                >
                  <div className="flex items-start">
                    <span className="flex-shrink-0 mt-0.5">
                      {suggestion.icon}
                    </span>
                    <p className="ml-3 text-sm leading-relaxed">
                      {suggestion.text}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Résumé et actions recommandées */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h5 className="font-medium text-blue-900 mb-2">
          📋 Actions recommandées
        </h5>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Prenez note des suggestions d'optimisation pour améliorer vos données</li>
          <li>• Vérifiez les points d'attention avant de finaliser l'import</li>
          <li>• Consultez la documentation pour plus de détails sur les bonnes pratiques</li>
          {groupedSuggestions.optimization && groupedSuggestions.optimization.length > 0 && (
            <li>• Considérez implémenter les optimisations pour de meilleurs résultats</li>
          )}
        </ul>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-3 gap-4 text-center">
        <div className="p-3 bg-blue-50 rounded-lg">
          <div className="text-lg font-bold text-blue-600">
            {groupedSuggestions.optimization?.length || 0}
          </div>
          <div className="text-xs text-blue-700">Optimisations</div>
        </div>
        <div className="p-3 bg-orange-50 rounded-lg">
          <div className="text-lg font-bold text-orange-600">
            {groupedSuggestions.warning?.length || 0}
          </div>
          <div className="text-xs text-orange-700">Avertissements</div>
        </div>
        <div className="p-3 bg-gray-50 rounded-lg">
          <div className="text-lg font-bold text-gray-600">
            {groupedSuggestions.info?.length || 0}
          </div>
          <div className="text-xs text-gray-700">Informations</div>
        </div>
      </div>
    </div>
  );
};
