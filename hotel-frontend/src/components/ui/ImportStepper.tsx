import React from 'react';
import { Check, Upload, Eye, Cog, CheckCircle } from 'lucide-react';

type StepStatus = 'upload' | 'preview' | 'processing' | 'complete';

interface ImportStepperProps {
  currentStep: StepStatus;
}

interface Step {
  id: StepStatus;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
}

const steps: Step[] = [
  {
    id: 'upload',
    title: 'Upload',
    description: 'Sélection du fichier Excel',
    icon: Upload
  },
  {
    id: 'preview',
    title: 'Prévisualisation',
    description: 'Vérification des données',
    icon: Eye
  },
  {
    id: 'processing',
    title: 'Traitement',
    description: 'Import en cours',
    icon: Cog
  },
  {
    id: 'complete',
    title: 'Terminé',
    description: 'Import réussi',
    icon: CheckCircle
  }
];

export const ImportStepper: React.FC<ImportStepperProps> = ({ currentStep }) => {
  const getCurrentStepIndex = () => {
    return steps.findIndex(step => step.id === currentStep);
  };

  const getStepStatus = (stepIndex: number) => {
    const currentIndex = getCurrentStepIndex();
    
    if (stepIndex < currentIndex) return 'completed';
    if (stepIndex === currentIndex) return 'current';
    return 'upcoming';
  };

  const getStepClasses = (status: string) => {
    switch (status) {
      case 'completed':
        return {
          container: 'text-green-600',
          circle: 'bg-green-600 border-green-600',
          icon: 'text-white',
          connector: 'bg-green-600'
        };
      case 'current':
        return {
          container: 'text-blue-600',
          circle: 'bg-blue-600 border-blue-600',
          icon: 'text-white',
          connector: 'bg-gray-200'
        };
      default:
        return {
          container: 'text-gray-400',
          circle: 'bg-white border-gray-300',
          icon: 'text-gray-400',
          connector: 'bg-gray-200'
        };
    }
  };

  return (
    <div className="w-full py-6">
      <nav aria-label="Progress">
        <ol className="flex items-center justify-between">
          {steps.map((step, stepIndex) => {
            const status = getStepStatus(stepIndex);
            const classes = getStepClasses(status);
            const isLast = stepIndex === steps.length - 1;

            return (
              <li key={step.id} className="relative flex-1">
                <div className="flex items-center">
                  {/* Step Circle */}
                  <div className="relative flex items-center justify-center">
                    <div
                      className={`
                        flex h-10 w-10 items-center justify-center rounded-full border-2 transition-colors
                        ${classes.circle}
                      `}
                    >
                      {status === 'completed' ? (
                        <Check className={`h-5 w-5 ${classes.icon}`} />
                      ) : status === 'current' && currentStep === 'processing' ? (
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      ) : (
                        <step.icon className={`h-5 w-5 ${classes.icon}`} />
                      )}
                    </div>
                  </div>

                  {/* Step Content */}
                  <div className="ml-4 min-w-0 flex-1">
                    <div className={`text-sm font-medium ${classes.container}`}>
                      {step.title}
                    </div>
                    <div className="text-xs text-gray-500">
                      {step.description}
                    </div>
                  </div>

                  {/* Connector Line */}
                  {!isLast && (
                    <div className="flex-1 ml-6 mr-6">
                      <div
                        className={`h-0.5 transition-colors ${classes.connector}`}
                      ></div>
                    </div>
                  )}
                </div>
              </li>
            );
          })}
        </ol>
      </nav>

      {/* Step Details */}
      <div className="mt-6 text-center">
        {currentStep === 'upload' && (
          <div className="text-sm text-gray-600">
            Sélectionnez un fichier Excel contenant vos données à importer
          </div>
        )}
        {currentStep === 'preview' && (
          <div className="text-sm text-gray-600">
            Vérifiez les données détectées avant de confirmer l'import
          </div>
        )}
        {currentStep === 'processing' && (
          <div className="text-sm text-gray-600">
            Traitement en cours... Veuillez patienter
          </div>
        )}
        {currentStep === 'complete' && (
          <div className="text-sm text-green-600">
            Import terminé avec succès !
          </div>
        )}
      </div>
    </div>
  );
};
