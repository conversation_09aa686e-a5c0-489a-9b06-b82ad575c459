import React, { useState, useEffect } from 'react';
import { AlertTriangle, Package, TrendingUp, TrendingDown, RefreshCw, Plus, Minus, Edit, X } from 'lucide-react';
import { inventaireService } from '../../services';
import type { 
  StockIngredient, 
  StockAlert, 
  Ingredient 
} from '../../types';

interface StockManagerProps {
  complexeId: number;
  alerts: StockAlert[];
  onAlertsUpdate: () => void;
}

interface StockAlertsProps {
  alerts: StockAlert[];
}

const StockAlerts: React.FC<StockAlertsProps> = ({ alerts }) => {
  if (alerts.length === 0) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center">
          <Package className="h-5 w-5 text-green-600 mr-2" />
          <span className="text-sm font-medium text-green-800">
            Aucune alerte de stock active
          </span>
        </div>
      </div>
    );
  }

  const getAlertColor = (urgence: string) => {
    switch (urgence) {
      case 'Critique': return 'bg-red-50 border-red-200 text-red-800';
      case 'Haute': return 'bg-orange-50 border-orange-200 text-orange-800';
      case 'Moyenne': return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      default: return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'RUPTURE': return <AlertTriangle className="h-5 w-5" />;
      case 'STOCK_FAIBLE': return <TrendingDown className="h-5 w-5" />;
      case 'EXPIRATION_PROCHE': return <Package className="h-5 w-5" />;
      default: return <AlertTriangle className="h-5 w-5" />;
    }
  };

  return (
    <div className="space-y-3">
      <h3 className="text-lg font-medium text-gray-900">Alertes de stock</h3>
      {alerts.map((alert, index) => (
        <div key={index} className={`border rounded-lg p-4 ${getAlertColor(alert.urgence)}`}>
          <div className="flex items-start">
            <div className="mr-3 mt-0.5">
              {getAlertIcon(alert.type)}
            </div>
            <div className="flex-1">
              <div className="flex justify-between items-start">
                <div>
                  <h4 className="font-medium">{alert.ingredient.nom}</h4>
                  <p className="text-sm mt-1">
                    {alert.type === 'RUPTURE' && 'Stock épuisé'}
                    {alert.type === 'STOCK_FAIBLE' && `Stock faible: ${alert.stock_actuel} ${alert.ingredient.unite_mesure} (seuil: ${alert.stock_minimal})`}
                    {alert.type === 'EXPIRATION_PROCHE' && `Expire dans ${alert.jours_restants} jour(s)`}
                  </p>
                </div>
                <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                  alert.urgence === 'Critique' ? 'bg-red-100 text-red-800' :
                  alert.urgence === 'Haute' ? 'bg-orange-100 text-orange-800' :
                  alert.urgence === 'Moyenne' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-blue-100 text-blue-800'
                }`}>
                  {alert.urgence}
                </span>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

interface StockListProps {
  stock: StockIngredient[];
  onUpdateStock: (ingredientId: number, operation: 'ENTREE' | 'SORTIE' | 'AJUSTEMENT', quantite: number) => void;
}

const StockList: React.FC<StockListProps> = ({ stock, onUpdateStock }) => {
  const [editingStock, setEditingStock] = useState<number | null>(null);
  const [newQuantity, setNewQuantity] = useState<string>('');
  const [operationType, setOperationType] = useState<'ENTREE' | 'SORTIE' | 'AJUSTEMENT'>('ENTREE');

  const handleStockUpdate = (ingredientId: number) => {
    const quantity = parseFloat(newQuantity);
    if (isNaN(quantity) || quantity <= 0) return;

    onUpdateStock(ingredientId, operationType, quantity);
    setEditingStock(null);
    setNewQuantity('');
  };

  const getStockStatus = (stock: StockIngredient) => {
    if (stock.quantite_actuelle === 0) return { color: 'text-red-600', label: 'Rupture' };
    if (stock.quantite_actuelle <= stock.quantite_minimale) return { color: 'text-orange-600', label: 'Faible' };
    if (stock.quantite_maximale && stock.quantite_actuelle >= stock.quantite_maximale) return { color: 'text-blue-600', label: 'Plein' };
    return { color: 'text-green-600', label: 'Normal' };
  };

  if (stock.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        Aucun stock d'ingrédient trouvé
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Ingrédient
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Stock actuel
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Seuils
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Valeur
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Statut
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Emplacement
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {stock.map((item) => {
            const status = getStockStatus(item);
            const isEditing = editingStock === item.ingredient_id;

            return (
              <tr key={item.stock_id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {item.ingredient?.nom}
                  </div>
                  <div className="text-sm text-gray-500">
                    {item.ingredient?.categorie}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {item.quantite_actuelle} {item.ingredient?.unite_mesure}
                  </div>
                  <div className="text-sm text-gray-500">
                    Prix: {item.prix_unitaire_actuel.toFixed(2)} FCFA/{item.ingredient?.unite_mesure}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>Min: {item.quantite_minimale}</div>
                  {item.quantite_maximale && (
                    <div>Max: {item.quantite_maximale}</div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {item.valeur_stock.toFixed(2)} FCFA
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`text-sm font-medium ${status.color}`}>
                    {status.label}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {item.emplacement_stockage || 'Non défini'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  {isEditing ? (
                    <div className="flex items-center space-x-2">
                      <select
                        value={operationType}
                        onChange={(e) => setOperationType(e.target.value as any)}
                        className="text-xs border border-gray-300 rounded px-2 py-1"
                      >
                        <option value="ENTREE">Entrée</option>
                        <option value="SORTIE">Sortie</option>
                        <option value="AJUSTEMENT">Ajustement</option>
                      </select>
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        value={newQuantity}
                        onChange={(e) => setNewQuantity(e.target.value)}
                        className="w-20 text-xs border border-gray-300 rounded px-2 py-1"
                        placeholder="Qté"
                      />
                      <button
                        onClick={() => handleStockUpdate(item.ingredient_id)}
                        className="text-green-600 hover:text-green-900"
                        disabled={!newQuantity}
                      >
                        <RefreshCw className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          setEditingStock(null);
                          setNewQuantity('');
                        }}
                        className="text-gray-600 hover:text-gray-900"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ) : (
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setEditingStock(item.ingredient_id);
                          setOperationType('ENTREE');
                        }}
                        className="text-green-600 hover:text-green-900"
                        title="Ajouter du stock"
                      >
                        <Plus className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          setEditingStock(item.ingredient_id);
                          setOperationType('SORTIE');
                        }}
                        className="text-orange-600 hover:text-orange-900"
                        title="Retirer du stock"
                      >
                        <Minus className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          setEditingStock(item.ingredient_id);
                          setOperationType('AJUSTEMENT');
                        }}
                        className="text-blue-600 hover:text-blue-900"
                        title="Ajuster le stock"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                    </div>
                  )}
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export const StockManager: React.FC<StockManagerProps> = ({
  complexeId,
  alerts,
  onAlertsUpdate
}) => {
  const [stock, setStock] = useState<StockIngredient[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedService, setSelectedService] = useState<number | undefined>();

  useEffect(() => {
    loadStock();
  }, [complexeId, selectedService]);

  const loadStock = async () => {
    try {
      setLoading(true);
      const result = await inventaireService.getStock(complexeId, selectedService);
      setStock(result);
    } catch (error) {
      console.error('Erreur lors du chargement du stock:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateStock = async (
    ingredientId: number, 
    operation: 'ENTREE' | 'SORTIE' | 'AJUSTEMENT', 
    quantite: number
  ) => {
    try {
      await inventaireService.updateStock(ingredientId, complexeId, quantite, operation);
      await loadStock(); // Recharger le stock
      onAlertsUpdate(); // Mettre à jour les alertes
    } catch (error) {
      console.error('Erreur lors de la mise à jour du stock:', error);
    }
  };

  const calculateTotalValue = () => {
    return stock.reduce((total, item) => total + item.valeur_stock, 0);
  };

  const getStockStatistics = () => {
    const total = stock.length;
    const rupture = stock.filter(item => item.quantite_actuelle === 0).length;
    const faible = stock.filter(item => 
      item.quantite_actuelle > 0 && item.quantite_actuelle <= item.quantite_minimale
    ).length;
    const normal = total - rupture - faible;

    return { total, rupture, faible, normal };
  };

  const stats = getStockStatistics();

  return (
    <div className="space-y-6">
      {/* Header avec statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Package className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total ingrédients</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <TrendingUp className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Stock normal</p>
              <p className="text-2xl font-semibold text-green-600">{stats.normal}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <TrendingDown className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Stock faible</p>
              <p className="text-2xl font-semibold text-orange-600">{stats.faible}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <AlertTriangle className="h-8 w-8 text-red-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Ruptures</p>
              <p className="text-2xl font-semibold text-red-600">{stats.rupture}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Valeur totale du stock */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">Valeur totale du stock</h3>
          <div className="text-2xl font-bold text-blue-600">
            {calculateTotalValue().toFixed(2)} FCFA
          </div>
        </div>
      </div>

      {/* Alertes */}
      <StockAlerts alerts={alerts} />

      {/* Header de la liste */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Gestion du stock</h3>
        <div className="flex space-x-4">
          <button
            onClick={loadStock}
            className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 flex items-center"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualiser
          </button>
        </div>
      </div>

      {/* Liste du stock */}
      {loading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <StockList
          stock={stock}
          onUpdateStock={handleUpdateStock}
        />
      )}
    </div>
  );
};
