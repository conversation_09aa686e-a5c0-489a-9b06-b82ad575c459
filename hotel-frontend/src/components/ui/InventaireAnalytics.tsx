import React, { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, TrendingDown, Package, AlertTriangle, DollarSign } from 'lucide-react';
import { inventaireService } from '../../services';

interface InventaireAnalyticsProps {
  complexeId: number;
}

interface AnalyticsData {
  valeur_stock_total: number;
  nombre_ingredients: number;
  ingredients_stock_faible: number;
  evolution_valeur: {
    periode: string;
    valeur: number;
    variation: number;
  }[];
  top_ingredients_couteux: {
    ingredient_id: number;
    nom: string;
    valeur_stock: number;
    pourcentage_total: number;
  }[];
  categories_repartition: {
    categorie: string;
    valeur: number;
    pourcentage: number;
  }[];
  alertes_expiration: {
    ingredient_id: number;
    nom: string;
    jours_restants: number;
    quantite: number;
  }[];
}

export const InventaireAnalytics: React.FC<InventaireAnalyticsProps> = ({ complexeId }) => {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [period, setPeriod] = useState<'7d' | '30d' | '90d'>('30d');

  useEffect(() => {
    loadAnalytics();
  }, [complexeId, period]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      const data = await inventaireService.getInventaireAnalytics(complexeId, period);
      setAnalytics(data);
    } catch (error) {
      console.error('Erreur lors du chargement des analyses:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const getVariationColor = (variation: number) => {
    if (variation > 0) return 'text-green-600';
    if (variation < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getVariationIcon = (variation: number) => {
    if (variation > 0) return <TrendingUp className="h-4 w-4" />;
    if (variation < 0) return <TrendingDown className="h-4 w-4" />;
    return null;
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-8 text-gray-500">
        Aucune donnée d'analyse disponible
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header avec sélecteur de période */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">
          Analyses d'Inventaire
        </h2>
        <div className="flex space-x-2">
          {[
            { value: '7d', label: '7 jours' },
            { value: '30d', label: '30 jours' },
            { value: '90d', label: '90 jours' }
          ].map((option) => (
            <button
              key={option.value}
              onClick={() => setPeriod(option.value as any)}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                period === option.value
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Métriques principales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <DollarSign className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Valeur Stock Total</p>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(analytics.valeur_stock_total)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Package className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Ingrédients</p>
              <p className="text-2xl font-bold text-gray-900">
                {analytics.nombre_ingredients}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <AlertTriangle className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Stock Faible</p>
              <p className="text-2xl font-bold text-gray-900">
                {analytics.ingredients_stock_faible}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <BarChart3 className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Évolution</p>
              <div className="flex items-center">
                <p className="text-2xl font-bold text-gray-900">
                  {analytics.evolution_valeur.length > 0 
                    ? analytics.evolution_valeur[analytics.evolution_valeur.length - 1]?.variation.toFixed(1) 
                    : '0'}%
                </p>
                {analytics.evolution_valeur.length > 0 && (
                  <span className={`ml-2 ${getVariationColor(analytics.evolution_valeur[analytics.evolution_valeur.length - 1]?.variation || 0)}`}>
                    {getVariationIcon(analytics.evolution_valeur[analytics.evolution_valeur.length - 1]?.variation || 0)}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Graphiques et analyses détaillées */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top ingrédients coûteux */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Ingrédients les plus coûteux
          </h3>
          <div className="space-y-3">
            {analytics.top_ingredients_couteux.slice(0, 5).map((ingredient) => (
              <div key={ingredient.ingredient_id} className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{ingredient.nom}</p>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${ingredient.pourcentage_total}%` }}
                    ></div>
                  </div>
                </div>
                <div className="ml-4 text-right">
                  <p className="text-sm font-medium text-gray-900">
                    {formatCurrency(ingredient.valeur_stock)}
                  </p>
                  <p className="text-xs text-gray-500">
                    {ingredient.pourcentage_total.toFixed(1)}%
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Répartition par catégories */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Répartition par catégories
          </h3>
          <div className="space-y-3">
            {analytics.categories_repartition.map((categorie, index) => {
              const colors = [
                'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500',
                'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-gray-500'
              ];
              return (
                <div key={categorie.categorie} className="flex items-center justify-between">
                  <div className="flex items-center flex-1">
                    <div className={`w-3 h-3 rounded-full ${colors[index % colors.length]} mr-3`}></div>
                    <span className="text-sm font-medium text-gray-900">{categorie.categorie}</span>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {formatCurrency(categorie.valeur)}
                    </p>
                    <p className="text-xs text-gray-500">
                      {categorie.pourcentage.toFixed(1)}%
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Alertes d'expiration */}
      {analytics.alertes_expiration.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <AlertTriangle className="h-5 w-5 text-orange-600 mr-2" />
            Alertes d'expiration
          </h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ingrédient
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Quantité
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Jours restants
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Urgence
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {analytics.alertes_expiration.map((alerte) => (
                  <tr key={alerte.ingredient_id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {alerte.nom}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {alerte.quantite}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {alerte.jours_restants} jours
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        alerte.jours_restants <= 3 ? 'bg-red-100 text-red-800' :
                        alerte.jours_restants <= 7 ? 'bg-orange-100 text-orange-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {alerte.jours_restants <= 3 ? 'Critique' :
                         alerte.jours_restants <= 7 ? 'Haute' : 'Moyenne'}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};
