import React, { useState } from 'react';
import { Download, FileText, AlertCircle, CheckCircle, Loader } from 'lucide-react';
import { FileUploadZone } from './FileUploadZone';
import { uploadService } from '../../services';
import type { TypeImport, ImportExcel } from '../../types';
import { FILE_UPLOAD_CONFIG, TYPE_IMPORT_OPTIONS } from '../../types/constants';

interface UploadStepProps {
  typeImport: TypeImport;
  onTypeChange: (type: TypeImport) => void;
  serviceId?: number;
  onServiceChange: (serviceId: number | undefined) => void;
  selectedFile: File | null;
  onFileSelect: (file: File) => void;
  onUpload: () => void;
  loading: boolean;
}

export const UploadStep: React.FC<UploadStepProps> = ({
  typeImport,
  onTypeChange,
  serviceId,
  onServiceChange,
  selectedFile,
  onFileSelect,
  onUpload,
  loading
}) => {
  const [downloadingTemplate, setDownloadingTemplate] = useState(false);

  const handleDownloadTemplate = async () => {
    try {
      setDownloadingTemplate(true);
      await uploadService.downloadTemplateWithFilename(typeImport);
    } catch (error) {
      console.error('Erreur lors du téléchargement du template:', error);
    } finally {
      setDownloadingTemplate(false);
    }
  };

  const handleFileSelect = (file: File) => {
    const validation = uploadService.validateFileBeforeUpload(file);
    if (!validation.valid) {
      console.error(validation.error);
      return;
    }
    onFileSelect(file);
  };

  const handleFileRemove = () => {
    onFileSelect(null as any);
  };

  return (
    <div className="space-y-6">
      {/* Configuration de l'import */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Configuration de l'import</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Type d'import */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Type d'import *
            </label>
            <select
              value={typeImport}
              onChange={(e) => onTypeChange(e.target.value as TypeImport)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={loading}
            >
              {TYPE_IMPORT_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Service (optionnel pour certains types) */}
          {(typeImport === 'MENU_RESTAURANT' || typeImport === 'CARTE_BAR') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Service spécifique (optionnel)
              </label>
              <input
                type="number"
                value={serviceId || ''}
                onChange={(e) => onServiceChange(e.target.value ? parseInt(e.target.value) : undefined)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="ID du service"
                disabled={loading}
              />
              <p className="text-xs text-gray-500 mt-1">
                Laissez vide pour appliquer à tous les services du type
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Téléchargement du template */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-start">
          <FileText className="h-6 w-6 text-blue-600 mt-1 mr-3 flex-shrink-0" />
          <div className="flex-1">
            <h4 className="text-sm font-medium text-blue-900 mb-2">
              Template Excel recommandé
            </h4>
            <p className="text-sm text-blue-700 mb-4">
              Pour garantir un import réussi, nous recommandons d'utiliser notre template Excel 
              qui contient la structure et les exemples appropriés pour le type d'import sélectionné.
            </p>
            <button
              onClick={handleDownloadTemplate}
              disabled={downloadingTemplate || loading}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              {downloadingTemplate ? (
                <>
                  <Loader className="animate-spin h-4 w-4 mr-2" />
                  Téléchargement...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Télécharger le template
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Zone d'upload */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Sélection du fichier</h3>
        
        <FileUploadZone
          onFileSelect={handleFileSelect}
          acceptedTypes={FILE_UPLOAD_CONFIG.ACCEPTED_TYPES}
          maxSize={FILE_UPLOAD_CONFIG.MAX_SIZE_MB}
          loading={loading}
          selectedFile={selectedFile}
          onFileRemove={handleFileRemove}
        />

        {/* Informations sur le fichier sélectionné */}
        {selectedFile && (
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              <div className="flex-1">
                <p className="text-sm font-medium text-green-900">
                  Fichier prêt pour l'upload
                </p>
                <p className="text-sm text-green-700">
                  {selectedFile.name} - {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h4 className="text-sm font-medium text-gray-900 mb-3">Instructions importantes</h4>
        <ul className="text-sm text-gray-600 space-y-2">
          <li className="flex items-start">
            <span className="inline-block w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
            Utilisez le template Excel fourni pour garantir la compatibilité
          </li>
          <li className="flex items-start">
            <span className="inline-block w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
            Vérifiez que toutes les colonnes obligatoires sont remplies
          </li>
          <li className="flex items-start">
            <span className="inline-block w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
            Les données seront validées avant l'import final
          </li>
          <li className="flex items-start">
            <span className="inline-block w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
            Taille maximum : {FILE_UPLOAD_CONFIG.MAX_SIZE_MB}MB
          </li>
        </ul>
      </div>

      {/* Bouton d'upload */}
      <div className="flex justify-end">
        <button
          onClick={onUpload}
          disabled={!selectedFile || loading}
          className="px-6 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {loading ? (
            <div className="flex items-center">
              <Loader className="animate-spin h-4 w-4 mr-2" />
              Upload en cours...
            </div>
          ) : (
            'Commencer l\'upload'
          )}
        </button>
      </div>
    </div>
  );
};

interface ProcessingStepProps {
  import: ImportExcel;
}

export const ProcessingStep: React.FC<ProcessingStepProps> = ({ import: importData }) => {
  const getProgressPercentage = () => {
    if (importData.statut === 'EN_COURS') return 25;
    if (importData.statut === 'VALIDE') return 75;
    if (importData.statut === 'IMPORTE') return 100;
    if (importData.statut === 'ERREUR') return 100;
    return 0;
  };

  const getStatusColor = () => {
    switch (importData.statut) {
      case 'EN_COURS': return 'text-blue-600';
      case 'VALIDE': return 'text-green-600';
      case 'IMPORTE': return 'text-green-600';
      case 'ERREUR': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusMessage = () => {
    switch (importData.statut) {
      case 'EN_COURS': return 'Analyse du fichier en cours...';
      case 'VALIDE': return 'Validation terminée, import en cours...';
      case 'IMPORTE': return 'Import terminé avec succès !';
      case 'ERREUR': return 'Erreur lors du traitement';
      default: return 'Traitement en cours...';
    }
  };

  return (
    <div className="space-y-6">
      {/* Statut principal */}
      <div className="bg-white rounded-lg shadow p-6 text-center">
        <div className="mb-4">
          {importData.statut === 'ERREUR' ? (
            <AlertCircle className="h-16 w-16 text-red-600 mx-auto" />
          ) : importData.statut === 'IMPORTE' ? (
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto" />
          ) : (
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-blue-600 mx-auto"></div>
            </div>
          )}
        </div>
        
        <h3 className={`text-xl font-semibold mb-2 ${getStatusColor()}`}>
          {getStatusMessage()}
        </h3>
        
        <p className="text-gray-600 mb-4">
          Fichier: {importData.nom_fichier}
        </p>

        {/* Barre de progression */}
        <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-500"
            style={{ width: `${getProgressPercentage()}%` }}
          ></div>
        </div>

        <div className="text-sm text-gray-500">
          {getProgressPercentage()}% terminé
        </div>
      </div>

      {/* Détails du traitement */}
      <div className="bg-white rounded-lg shadow p-6">
        <h4 className="text-lg font-medium text-gray-900 mb-4">Détails du traitement</h4>
        
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">
              {importData.nombre_lignes_total}
            </div>
            <div className="text-sm text-gray-600">Lignes totales</div>
          </div>
          
          <div className="p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {importData.nombre_lignes_valides}
            </div>
            <div className="text-sm text-gray-600">Lignes valides</div>
          </div>
          
          <div className="p-4 bg-red-50 rounded-lg">
            <div className="text-2xl font-bold text-red-600">
              {importData.nombre_erreurs}
            </div>
            <div className="text-sm text-gray-600">Erreurs</div>
          </div>
        </div>

        {/* Informations supplémentaires */}
        <div className="mt-6 text-sm text-gray-600">
          <div className="flex justify-between py-2">
            <span>Date de début:</span>
            <span>{new Date(importData.date_import).toLocaleString()}</span>
          </div>
          {importData.date_traitement && (
            <div className="flex justify-between py-2">
              <span>Date de traitement:</span>
              <span>{new Date(importData.date_traitement).toLocaleString()}</span>
            </div>
          )}
          {importData.date_finalisation && (
            <div className="flex justify-between py-2">
              <span>Date de finalisation:</span>
              <span>{new Date(importData.date_finalisation).toLocaleString()}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

interface CompleteStepProps {
  import: ImportExcel;
  onNewImport: () => void;
}

export const CompleteStep: React.FC<CompleteStepProps> = ({ import: importData, onNewImport }) => {
  const isSuccess = importData.statut === 'IMPORTE';
  const successRate = importData.nombre_lignes_total > 0 
    ? (importData.nombre_lignes_valides / importData.nombre_lignes_total) * 100 
    : 0;

  return (
    <div className="space-y-6">
      {/* Résultat principal */}
      <div className="bg-white rounded-lg shadow p-6 text-center">
        <div className="mb-4">
          {isSuccess ? (
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto" />
          ) : (
            <AlertCircle className="h-16 w-16 text-red-600 mx-auto" />
          )}
        </div>
        
        <h3 className={`text-xl font-semibold mb-2 ${isSuccess ? 'text-green-600' : 'text-red-600'}`}>
          {isSuccess ? 'Import terminé avec succès !' : 'Import terminé avec des erreurs'}
        </h3>
        
        <p className="text-gray-600 mb-4">
          {importData.nombre_lignes_valides} ligne(s) sur {importData.nombre_lignes_total} ont été importées
        </p>

        <div className={`text-2xl font-bold ${isSuccess ? 'text-green-600' : 'text-orange-600'}`}>
          {successRate.toFixed(1)}% de réussite
        </div>
      </div>

      {/* Résumé détaillé */}
      <div className="bg-white rounded-lg shadow p-6">
        <h4 className="text-lg font-medium text-gray-900 mb-4">Résumé de l'import</h4>
        
        <div className="grid grid-cols-2 gap-6">
          <div>
            <h5 className="font-medium text-gray-900 mb-3">Statistiques</h5>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Fichier:</span>
                <span className="font-medium">{importData.nom_fichier}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Type d'import:</span>
                <span className="font-medium">{importData.type_import}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Taille du fichier:</span>
                <span className="font-medium">
                  {(importData.taille_fichier / (1024 * 1024)).toFixed(2)} MB
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Durée totale:</span>
                <span className="font-medium">
                  {importData.date_finalisation && importData.date_import
                    ? Math.round((new Date(importData.date_finalisation).getTime() - new Date(importData.date_import).getTime()) / 1000)
                    : 0} secondes
                </span>
              </div>
            </div>
          </div>

          <div>
            <h5 className="font-medium text-gray-900 mb-3">Résultats</h5>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Lignes traitées:</span>
                <span className="font-medium text-blue-600">{importData.nombre_lignes_total}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Lignes importées:</span>
                <span className="font-medium text-green-600">{importData.nombre_lignes_valides}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Erreurs:</span>
                <span className="font-medium text-red-600">{importData.nombre_erreurs}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Taux de réussite:</span>
                <span className={`font-medium ${successRate >= 90 ? 'text-green-600' : successRate >= 70 ? 'text-orange-600' : 'text-red-600'}`}>
                  {successRate.toFixed(1)}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-center space-x-4">
        <button
          onClick={onNewImport}
          className="px-6 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors"
        >
          Nouvel import
        </button>
        
        {/* TODO: Ajouter boutons pour télécharger rapport, voir détails, etc. */}
      </div>
    </div>
  );
};
