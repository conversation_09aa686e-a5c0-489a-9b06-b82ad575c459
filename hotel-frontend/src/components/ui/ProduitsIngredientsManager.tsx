import React, { useState, useEffect } from 'react';
import {
  ChefHat,
  Package,
  Search,
  Filter,
  Edit,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { produitIngredientService } from '../../services/produitIngredient.service';
import { serviceComplexeService } from '../../services/service.service';
import { PlatComposition } from '../restaurant/PlatComposition';
import type { ProduitAvecIngredients } from '../../services/produitIngredient.service';
import type { ServiceComplexe } from '../../services/service.service';

interface ProduitsIngredientsManagerProps {
  complexeId: number;
}

export const ProduitsIngredientsManager: React.FC<ProduitsIngredientsManagerProps> = ({
  complexeId
}) => {
  const [produits, setProduits] = useState<ProduitAvecIngredients[]>([]);
  const [services, setServices] = useState<ServiceComplexe[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedServiceId, setSelectedServiceId] = useState<number | null>(null);
  const [selectedProduitId, setSelectedProduitId] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showComposition, setShowComposition] = useState(false);

  useEffect(() => {
    loadServices();
  }, [complexeId]);

  useEffect(() => {
    if (selectedServiceId) {
      loadProduits();
    }
  }, [selectedServiceId]);

  const loadServices = async () => {
    try {
      const allServices = await serviceComplexeService.getAllServices();
      const restaurantBarServices = allServices.filter(s => 
        s.type_service === 'Restaurant' || s.type_service === 'Bar'
      );
      setServices(restaurantBarServices);
      
      // Auto-sélectionner le premier service s'il n'y en a qu'un
      if (restaurantBarServices.length === 1) {
        setSelectedServiceId(restaurantBarServices[0].service_id);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des services:', error);
      toast.error('Erreur lors du chargement des services');
    }
  };

  const loadProduits = async () => {
    if (!selectedServiceId) return;
    
    try {
      setLoading(true);
      const data = await produitIngredientService.getProduitsAvecIngredients(complexeId, selectedServiceId);
      setProduits(data);
    } catch (error) {
      console.error('Erreur lors du chargement des produits:', error);
      toast.error('Erreur lors du chargement des produits');
    } finally {
      setLoading(false);
    }
  };

  const handleCompositionChange = () => {
    // Recharger les produits après modification de composition
    loadProduits();
  };

  const getFilteredProduits = () => {
    if (!searchTerm) return produits;
    
    return produits.filter(produit =>
      produit.produit_nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
      produit.description?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const getStatusIcon = (produit: ProduitAvecIngredients) => {
    if (produit.nombre_ingredients === 0) {
      return (
        <div title="Aucun ingrédient défini">
          <AlertTriangle className="h-5 w-5 text-yellow-500" />
        </div>
      );
    }
    return (
      <div title="Composition définie">
        <CheckCircle className="h-5 w-5 text-green-500" />
      </div>
    );
  };

  const getMargeColor = (produit: ProduitAvecIngredients) => {
    if (produit.prix_vente_defaut === 0) return 'text-gray-500';
    
    const marge = ((produit.prix_vente_defaut - produit.cout_ingredients) / produit.prix_vente_defaut) * 100;
    
    if (marge > 30) return 'text-green-600';
    if (marge > 15) return 'text-yellow-600';
    return 'text-red-600';
  };

  const calculateMarge = (produit: ProduitAvecIngredients) => {
    if (produit.prix_vente_defaut === 0) return 0;
    return ((produit.prix_vente_defaut - produit.cout_ingredients) / produit.prix_vente_defaut) * 100;
  };

  const selectedService = services.find(s => s.service_id === selectedServiceId);
  const filteredProduits = getFilteredProduits();

  if (showComposition && selectedProduitId) {
    const selectedProduit = produits.find(p => p.produit_id === selectedProduitId);
    
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <button
            onClick={() => {
              setShowComposition(false);
              setSelectedProduitId(null);
            }}
            className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
          >
            ← Retour à la liste des produits
          </button>
          <button
            onClick={loadProduits}
            className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Actualiser
          </button>
        </div>
        
        <PlatComposition
          produitId={selectedProduitId}
          produitNom={selectedProduit?.produit_nom || 'Produit'}
          onCompositionChange={handleCompositionChange}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header et sélection de service */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            Gestion Produits & Compositions
          </h2>
          <p className="text-sm text-gray-600">
            Gérez les produits et leurs compositions d'ingrédients
          </p>
        </div>
        
        <button
          onClick={loadProduits}
          disabled={loading}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Actualiser
        </button>
      </div>

      {/* Sélection du service */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center space-x-4">
          <Filter className="h-5 w-5 text-gray-400" />
          <span className="text-sm font-medium text-gray-700">Service:</span>
          <select
            value={selectedServiceId || ''}
            onChange={(e) => setSelectedServiceId(e.target.value ? parseInt(e.target.value) : null)}
            className="border border-gray-300 rounded-md px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Sélectionner un service</option>
            {services.map(service => (
              <option key={service.service_id} value={service.service_id}>
                {service.nom} ({service.type_service})
              </option>
            ))}
          </select>
          
          {selectedService && (
            <span className="text-sm text-gray-600">
              {selectedService.emplacement}
            </span>
          )}
        </div>
      </div>

      {/* Barre de recherche */}
      {selectedServiceId && (
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center space-x-4">
            <Search className="h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher un produit..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 border border-gray-300 rounded-md px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      )}

      {/* Liste des produits */}
      {selectedServiceId && (
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              Produits {selectedService?.type_service === 'Restaurant' ? 'Restaurant' : 'Bar'} ({filteredProduits.length})
            </h3>
          </div>

          {loading ? (
            <div className="p-6">
              <div className="animate-pulse space-y-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="h-16 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          ) : filteredProduits.length === 0 ? (
            <div className="p-6 text-center">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">
                {searchTerm ? 'Aucun produit trouvé pour cette recherche' : 'Aucun produit trouvé pour ce service'}
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredProduits.map((produit) => (
                <div key={produit.produit_id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center flex-1">
                      {getStatusIcon(produit)}
                      <div className="ml-4 flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="text-lg font-medium text-gray-900">
                            {produit.produit_nom}
                          </h4>
                          <div className="flex items-center space-x-4">
                            <span className="text-sm text-gray-600">
                              {produit.nombre_ingredients} ingrédient(s)
                            </span>
                            <button
                              onClick={() => {
                                setSelectedProduitId(produit.produit_id);
                                setShowComposition(true);
                              }}
                              className="inline-flex items-center px-3 py-1.5 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100"
                            >
                              <Edit className="h-4 w-4 mr-1" />
                              Gérer composition
                            </button>
                          </div>
                        </div>
                        
                        {produit.description && (
                          <p className="text-sm text-gray-600 mt-1">{produit.description}</p>
                        )}
                        
                        <div className="flex items-center space-x-6 mt-2 text-sm">
                          <span className="flex items-center text-gray-600">
                            <DollarSign className="h-4 w-4 mr-1" />
                            Prix: {produit.prix_vente_defaut.toFixed(0)} FCFA
                          </span>
                          <span className="text-gray-600">
                            Coût ingrédients: {produit.cout_ingredients.toFixed(0)} FCFA
                          </span>
                          <span className={`font-medium ${getMargeColor(produit)}`}>
                            Marge: {calculateMarge(produit).toFixed(1)}%
                          </span>
                        </div>
                        
                        {produit.nombre_ingredients === 0 && (
                          <div className="mt-2">
                            <p className="text-sm text-yellow-600 font-medium">
                              ⚠️ Aucun ingrédient défini - Cliquez sur "Gérer composition" pour ajouter des ingrédients
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {!selectedServiceId && (
        <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
          <ChefHat className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 mb-4">
            Sélectionnez un service pour voir ses produits
          </p>
          <p className="text-sm text-gray-400">
            Seuls les services Restaurant et Bar sont affichés
          </p>
        </div>
      )}
    </div>
  );
};
