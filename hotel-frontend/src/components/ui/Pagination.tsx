import React from 'react';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import Button from './Button';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  maxVisiblePages?: number;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  maxVisiblePages = 5,
}) => {
  if (totalPages <= 1) return null;

  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return;
    onPageChange(page);
  };

  const renderPageNumbers = () => {
    const pageNumbers: React.ReactNode[] = [];
    
    if (totalPages <= maxVisiblePages) {
      // If we have fewer pages than the max, show all
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(
          <Button
            key={i}
            variant={i === currentPage ? 'primary' : 'outline'}
            size="sm"
            onClick={() => handlePageChange(i)}
            className="min-w-[40px]"
            aria-current={i === currentPage ? 'page' : undefined}
          >
            {i}
          </Button>
        );
      }
    } else {
      // Always show first page
      pageNumbers.push(
        <Button
          key={1}
          variant={1 === currentPage ? 'primary' : 'outline'}
          size="sm"
          onClick={() => handlePageChange(1)}
          className="min-w-[40px]"
          aria-current={1 === currentPage ? 'page' : undefined}
        >
          1
        </Button>
      );

      // Calculate start and end page numbers to display
      let startPage = Math.max(2, currentPage - Math.floor(maxVisiblePages / 2));
      let endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 3);
      
      if (endPage - startPage < maxVisiblePages - 3) {
        startPage = Math.max(2, endPage - (maxVisiblePages - 3) + 1);
      }

      // Add ellipsis if necessary
      if (startPage > 2) {
        pageNumbers.push(
          <span key="ellipsis-start\" className="px-2 py-2 text-gray-500">
            ...
          </span>
        );
      }

      // Add page numbers
      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(
          <Button
            key={i}
            variant={i === currentPage ? 'primary' : 'outline'}
            size="sm"
            onClick={() => handlePageChange(i)}
            className="min-w-[40px]"
            aria-current={i === currentPage ? 'page' : undefined}
          >
            {i}
          </Button>
        );
      }

      // Add ellipsis if necessary
      if (endPage < totalPages - 1) {
        pageNumbers.push(
          <span key="ellipsis-end\" className="px-2 py-2 text-gray-500">
            ...
          </span>
        );
      }

      // Always show last page
      pageNumbers.push(
        <Button
          key={totalPages}
          variant={totalPages === currentPage ? 'primary' : 'outline'}
          size="sm"
          onClick={() => handlePageChange(totalPages)}
          className="min-w-[40px]"
          aria-current={totalPages === currentPage ? 'page' : undefined}
        >
          {totalPages}
        </Button>
      );
    }

    return pageNumbers;
  };

  return (
    <div className="flex items-center justify-between">
      <div className="text-sm text-gray-700">
        Page <span className="font-medium">{currentPage}</span> sur{' '}
        <span className="font-medium">{totalPages}</span>
      </div>
      
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(1)}
          disabled={currentPage === 1}
          icon={<ChevronsLeft size={16} />}
          aria-label="Première page"
        />
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          icon={<ChevronLeft size={16} />}
          aria-label="Page précédente"
        />
        
        <div className="flex items-center space-x-1 mx-2">
          {renderPageNumbers()}
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          icon={<ChevronRight size={16} />}
          aria-label="Page suivante"
        />
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(totalPages)}
          disabled={currentPage === totalPages}
          icon={<ChevronsRight size={16} />}
          aria-label="Dernière page"
        />
      </div>
    </div>
  );
};

export default Pagination;