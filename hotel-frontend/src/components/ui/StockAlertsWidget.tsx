import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { AlertTriangle, Package, TrendingDown, Clock } from 'lucide-react';
import { inventaireService } from '../../services';
import { authService } from '../../services/auth.service';
import type { StockAlert } from '../../types';

interface StockAlertsWidgetProps {
  className?: string;
  maxAlerts?: number;
}

const StockAlertsWidget: React.FC<StockAlertsWidgetProps> = ({ 
  className = '', 
  maxAlerts = 5 
}) => {
  const navigate = useNavigate();
  const [alerts, setAlerts] = useState<StockAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const complexeId = authService.getComplexeId();

  useEffect(() => {
    loadStockAlerts();
  }, [complexeId]);

  const loadStockAlerts = async () => {
    if (!complexeId) return;

    try {
      setLoading(true);
      const stockAlerts = await inventaireService.getStockAlerts(complexeId);
      setAlerts(stockAlerts.slice(0, maxAlerts));
    } catch (error) {
      console.error('Erreur lors du chargement des alertes:', error);
    } finally {
      setLoading(false);
    }
  };

  const getAlertIcon = (type: StockAlert['type']) => {
    switch (type) {
      case 'RUPTURE':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'STOCK_FAIBLE':
        return <TrendingDown className="h-4 w-4 text-orange-500" />;
      case 'EXPIRATION_PROCHE':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <Package className="h-4 w-4 text-gray-500" />;
    }
  };

  const getAlertColor = (urgence: StockAlert['urgence']) => {
    switch (urgence) {
      case 'Critique':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'Haute':
        return 'bg-orange-50 border-orange-200 text-orange-800';
      case 'Moyenne':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'Faible':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  const getAlertMessage = (alert: StockAlert) => {
    switch (alert.type) {
      case 'RUPTURE':
        return `Stock épuisé`;
      case 'STOCK_FAIBLE':
        return `${alert.stock_actuel} restant (min: ${alert.stock_minimal})`;
      case 'EXPIRATION_PROCHE':
        return `Expire dans ${alert.jours_restants} jour(s)`;
      default:
        return 'Alerte de stock';
    }
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (alerts.length === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Alertes de Stock</h3>
          <Package className="h-5 w-5 text-gray-400" />
        </div>
        
        <div className="text-center py-4">
          <div className="text-green-600 mb-2">
            <svg className="mx-auto h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <p className="text-sm text-gray-600">Aucune alerte de stock</p>
          <p className="text-xs text-gray-500 mt-1">Tous les stocks sont à niveau</p>
        </div>
        
        <button
          onClick={() => navigate('/inventaire?tab=stock')}
          className="w-full mt-4 text-sm text-blue-600 hover:text-blue-800 transition-colors"
        >
          Voir l'inventaire complet
        </button>
      </div>
    );
  }

  const criticalCount = alerts.filter(alert => alert.urgence === 'Critique').length;
  const highCount = alerts.filter(alert => alert.urgence === 'Haute').length;

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Alertes de Stock</h3>
        <div className="flex items-center space-x-2">
          {criticalCount > 0 && (
            <span className="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">
              {criticalCount} critique{criticalCount > 1 ? 's' : ''}
            </span>
          )}
          {highCount > 0 && (
            <span className="bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-full">
              {highCount} importante{highCount > 1 ? 's' : ''}
            </span>
          )}
        </div>
      </div>

      <div className="space-y-3">
        {alerts.map((alert, index) => (
          <div
            key={index}
            className={`p-3 rounded-lg border ${getAlertColor(alert.urgence)} transition-colors hover:shadow-sm cursor-pointer`}
            onClick={() => navigate('/inventaire?tab=stock')}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {getAlertIcon(alert.type)}
                <div>
                  <p className="font-medium text-sm">
                    {alert.ingredient.nom}
                  </p>
                  <p className="text-xs opacity-75">
                    {getAlertMessage(alert)}
                  </p>
                </div>
              </div>
              
              <div className="text-right">
                <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                  alert.urgence === 'Critique' ? 'bg-red-200 text-red-800' :
                  alert.urgence === 'Haute' ? 'bg-orange-200 text-orange-800' :
                  alert.urgence === 'Moyenne' ? 'bg-yellow-200 text-yellow-800' :
                  'bg-blue-200 text-blue-800'
                }`}>
                  {alert.urgence}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="flex justify-between items-center">
          <button
            onClick={() => navigate('/inventaire?tab=stock')}
            className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
          >
            Voir tous les stocks
          </button>
          
          <button
            onClick={() => navigate('/inventaire?tab=ingredients&action=create')}
            className="text-sm text-green-600 hover:text-green-800 transition-colors"
          >
            Ajouter ingrédient
          </button>
        </div>
      </div>
    </div>
  );
};

export default StockAlertsWidget;
