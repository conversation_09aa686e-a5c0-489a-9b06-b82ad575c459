import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Upload,
  Package,
  ChefHat,
  BarChart3,
  Plus,
  Settings,
  Warehouse,
  BedDouble,
  ShoppingCart,
  Users,
  Waves,
  Calendar
} from 'lucide-react';

interface QuickLink {
  title: string;
  description: string;
  icon: React.ReactNode;
  action: () => void;
  color: string;
  badge?: string;
}

interface QuickLinksProps {
  context?: 'dashboard' | 'inventaire' | 'services' | 'import';
  className?: string;
}

const QuickLinks: React.FC<QuickLinksProps> = ({ 
  context = 'dashboard', 
  className = '' 
}) => {
  const navigate = useNavigate();

  const getLinksForContext = (): QuickLink[] => {
    switch (context) {
      case 'dashboard':
        return [
          {
            title: 'Réception',
            description: 'Gestion des réservations',
            icon: <BedDouble className="h-5 w-5" />,
            action: () => navigate('/reception'),
            color: 'bg-blue-500 hover:bg-blue-600'
          },
          {
            title: 'POS',
            description: 'Point de vente',
            icon: <ShoppingCart className="h-5 w-5" />,
            action: () => navigate('/pos'),
            color: 'bg-green-500 hover:bg-green-600'
          },
          {
            title: 'Gestion POS',
            description: 'Gestion des caisses',
            icon: <Settings className="h-5 w-5" />,
            action: () => navigate('/pos-management'),
            color: 'bg-gray-600 hover:bg-gray-700'
          },
          {
            title: 'Piscine',
            description: 'Gestion de la piscine',
            icon: <Waves className="h-5 w-5" />,
            action: () => navigate('/pool'),
            color: 'bg-cyan-500 hover:bg-cyan-600'
          },
          {
            title: 'Chambres',
            description: 'Gestion des chambres',
            icon: <BedDouble className="h-5 w-5" />,
            action: () => navigate('/chambres'),
            color: 'bg-teal-500 hover:bg-teal-600'
          },
          {
            title: 'Services',
            description: 'Gestion des services',
            icon: <Settings className="h-5 w-5" />,
            action: () => navigate('/services'),
            color: 'bg-amber-500 hover:bg-amber-600'
          },
          {
            title: 'Inventaire',
            description: 'Gérer ingrédients et stocks',
            icon: <Warehouse className="h-5 w-5" />,
            action: () => navigate('/inventaire'),
            color: 'bg-emerald-500 hover:bg-emerald-600'
          },
          {
            title: 'Import Excel',
            description: 'Importer des données',
            icon: <Upload className="h-5 w-5" />,
            action: () => navigate('/import-excel'),
            color: 'bg-blue-600 hover:bg-blue-700'
          },
          {
            title: 'Personnel',
            description: 'Gestion des employés',
            icon: <Users className="h-5 w-5" />,
            action: () => navigate('/employee-management'),
            color: 'bg-purple-500 hover:bg-purple-600'
          },
          {
            title: 'Rapports',
            description: 'Analyses et rapports',
            icon: <BarChart3 className="h-5 w-5" />,
            action: () => navigate('/reports'),
            color: 'bg-indigo-500 hover:bg-indigo-600'
          },
          {
            title: 'Réservations Clients',
            description: 'Interface client réservations',
            icon: <Calendar className="h-5 w-5" />,
            action: () => navigate('/client-reservations'),
            color: 'bg-rose-500 hover:bg-rose-600'
          }
        ];

      case 'inventaire':
        return [
          {
            title: 'Configuration Rapide',
            description: 'Setup menu en 3 étapes',
            icon: <ChefHat className="h-5 w-5" />,
            action: () => navigate('/menu-setup'),
            color: 'bg-green-500 hover:bg-green-600'
          },
          {
            title: 'Gestion Services',
            description: 'Import menus et ingrédients',
            icon: <Upload className="h-5 w-5" />,
            action: () => navigate('/services'),
            color: 'bg-blue-500 hover:bg-blue-600'
          }
        ];

      case 'services':
        return [
          {
            title: 'Configuration Rapide',
            description: 'Setup menu en 3 étapes',
            icon: <ChefHat className="h-5 w-5" />,
            action: () => navigate('/menu-setup'),
            color: 'bg-green-500 hover:bg-green-600'
          },
          {
            title: 'Inventaire',
            description: 'Gérer les stocks',
            icon: <Package className="h-5 w-5" />,
            action: () => navigate('/inventaire'),
            color: 'bg-emerald-500 hover:bg-emerald-600'
          }
        ];

      case 'import':
        return [
          {
            title: 'Configuration Rapide',
            description: 'Setup menu en 3 étapes',
            icon: <ChefHat className="h-5 w-5" />,
            action: () => navigate('/menu-setup'),
            color: 'bg-green-500 hover:bg-green-600'
          },
          {
            title: 'Gestion Services',
            description: 'Import menus et ingrédients',
            icon: <Upload className="h-5 w-5" />,
            action: () => navigate('/services'),
            color: 'bg-blue-500 hover:bg-blue-600'
          }
        ];

      default:
        return [];
    }
  };

  const links = getLinksForContext();

  if (links.length === 0) {
    return null;
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Actions Rapides
      </h3>
      
      <div className={`grid gap-4 ${
        context === 'dashboard'
          ? 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5'
          : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4'
      }`}>
        {links.map((link, index) => (
          <button
            key={index}
            onClick={link.action}
            className={`
              ${link.color} text-white p-4 rounded-lg 
              transition-all duration-200 hover:shadow-md 
              flex flex-col items-center text-center space-y-2
              group relative
            `}
          >
            <div className="flex items-center justify-center">
              {link.icon}
              {link.badge && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {link.badge}
                </span>
              )}
            </div>
            
            <div>
              <div className="font-medium text-sm">
                {link.title}
              </div>
              <div className="text-xs opacity-90 mt-1">
                {link.description}
              </div>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default QuickLinks;
