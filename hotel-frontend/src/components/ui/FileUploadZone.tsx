import React, { useState, useRef } from 'react';
import { Upload, AlertCircle, FileText, X } from 'lucide-react';

interface FileUploadZoneProps {
  onFileSelect: (file: File) => void;
  acceptedTypes: string[];
  maxSize: number; // en MB
  disabled?: boolean;
  loading?: boolean;
  error?: string;
  selectedFile?: File | null;
  onFileRemove?: () => void;
}

export const FileUploadZone: React.FC<FileUploadZoneProps> = ({
  onFileSelect,
  acceptedTypes,
  maxSize,
  disabled = false,
  loading = false,
  error,
  selectedFile,
  onFileRemove
}) => {
  const [dragActive, setDragActive] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (disabled || loading) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      validateAndSelectFile(files[0]);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      validateAndSelectFile(e.target.files[0]);
    }
  };

  const validateAndSelectFile = (file: File) => {
    // Validation du type
    if (!acceptedTypes.includes(file.type)) {
      console.error(`Type de fichier non supporté. Types acceptés: ${acceptedTypes.join(', ')}`);
      return;
    }

    // Validation de la taille
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > maxSize) {
      console.error(`Fichier trop volumineux. Taille maximum: ${maxSize}MB`);
      return;
    }

    onFileSelect(file);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileTypeLabel = () => {
    const types = acceptedTypes.map(type => {
      if (type.includes('spreadsheetml')) return '.xlsx';
      if (type.includes('ms-excel')) return '.xls';
      return type;
    });
    return types.join(', ');
  };

  if (selectedFile) {
    return (
      <div className="w-full">
        <div className="border-2 border-dashed border-green-300 bg-green-50 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <FileText className="h-8 w-8 text-green-600" />
              <div>
                <p className="text-sm font-medium text-green-900">{selectedFile.name}</p>
                <p className="text-sm text-green-700">{formatFileSize(selectedFile.size)}</p>
              </div>
            </div>
            {onFileRemove && !loading && (
              <button
                onClick={onFileRemove}
                className="p-1 hover:bg-green-100 rounded-full transition-colors"
                disabled={disabled}
              >
                <X className="h-5 w-5 text-green-600" />
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center transition-colors
          ${dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}
          ${disabled || loading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:border-gray-400'}
          ${error ? 'border-red-300 bg-red-50' : ''}
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => !disabled && !loading && inputRef.current?.click()}
      >
        <input
          ref={inputRef}
          type="file"
          className="hidden"
          accept={acceptedTypes.join(',')}
          onChange={handleFileInput}
          disabled={disabled || loading}
        />

        {loading ? (
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
            <p className="text-gray-600">Traitement en cours...</p>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <Upload className="h-12 w-12 text-gray-400 mb-4" />
            <p className="text-lg font-medium text-gray-700 mb-2">
              Glissez votre fichier Excel ici ou cliquez pour sélectionner
            </p>
            <p className="text-sm text-gray-500 mb-4">
              Formats acceptés: {getFileTypeLabel()} - Taille max: {maxSize}MB
            </p>
            <button
              type="button"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
              disabled={disabled || loading}
            >
              Sélectionner un fichier
            </button>
          </div>
        )}
      </div>

      {error && (
        <div className="mt-2 text-sm text-red-600 flex items-center">
          <AlertCircle className="h-4 w-4 mr-1" />
          {error}
        </div>
      )}
    </div>
  );
};
