import React from 'react';

interface DataTableProps {
  data: any[];
  maxRows?: number;
}

export const DataTable: React.FC<DataTableProps> = ({ data, maxRows = 10 }) => {
  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        Aucune donnée à afficher
      </div>
    );
  }

  // Prendre les premières lignes selon maxRows
  const displayData = data.slice(0, maxRows);
  
  // Extraire les colonnes du premier élément
  const columns = Object.keys(displayData[0] || {});

  const formatCellValue = (value: any): string => {
    if (value === null || value === undefined) return '';
    if (typeof value === 'boolean') return value ? 'Oui' : 'Non';
    if (typeof value === 'number') return value.toString();
    if (typeof value === 'object') return JSON.stringify(value);
    return String(value);
  };

  const formatColumnName = (columnName: string): string => {
    // Convertir snake_case en format lisible
    return columnName
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <div className="space-y-4">
      {/* Informations sur l'affichage */}
      <div className="flex justify-between items-center text-sm text-gray-600">
        <span>
          Affichage de {displayData.length} ligne(s) sur {data.length} total
        </span>
        {data.length > maxRows && (
          <span className="text-blue-600">
            {data.length - maxRows} ligne(s) supplémentaire(s) non affichée(s)
          </span>
        )}
      </div>

      {/* Tableau */}
      <div className="overflow-x-auto border border-gray-200 rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {columns.map((column, index) => (
                <th
                  key={column}
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {formatColumnName(column)}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {displayData.map((row, rowIndex) => (
              <tr key={rowIndex} className="hover:bg-gray-50">
                {columns.map((column, colIndex) => (
                  <td
                    key={`${rowIndex}-${column}`}
                    className="px-4 py-3 text-sm text-gray-900 max-w-xs truncate"
                    title={formatCellValue(row[column])}
                  >
                    {formatCellValue(row[column])}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Légende pour les valeurs tronquées */}
      <div className="text-xs text-gray-500">
        💡 Survolez les cellules pour voir le contenu complet
      </div>
    </div>
  );
};
