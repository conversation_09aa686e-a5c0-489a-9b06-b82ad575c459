import React from 'react';
import { useNavigate } from 'react-router-dom';
import { AlertTriangle, Building2, LogIn } from 'lucide-react';
import { useComplexeStatus } from '../../hooks/useComplexeAccess';

interface ComplexeAccessGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showActions?: boolean;
  className?: string;
}

/**
 * Composant guard qui vérifie l'accès au complexe et affiche un message approprié
 * si l'utilisateur n'a pas accès
 */
export const ComplexeAccessGuard: React.FC<ComplexeAccessGuardProps> = ({
  children,
  fallback,
  showActions = true,
  className = ''
}) => {
  const navigate = useNavigate();
  const { hasAccess, accessMessage, accessAction } = useComplexeStatus();

  // Si l'utilisateur a accès, afficher le contenu
  if (hasAccess) {
    return <>{children}</>;
  }

  // Si un fallback personnalisé est fourni, l'utiliser
  if (fallback) {
    return <>{fallback}</>;
  }

  // Affichage par défaut pour accès refusé
  return (
    <div className={`flex items-center justify-center min-h-screen ${className}`}>
      <div className="text-center max-w-md mx-auto p-6">
        <div className="mb-6">
          {accessAction?.path === '/login' ? (
            <LogIn className="mx-auto h-16 w-16 text-gray-400" />
          ) : accessAction?.path === '/complexes' ? (
            <Building2 className="mx-auto h-16 w-16 text-blue-400" />
          ) : (
            <AlertTriangle className="mx-auto h-16 w-16 text-orange-400" />
          )}
        </div>

        <h2 className="text-2xl font-semibold text-gray-900 mb-4">
          Accès requis
        </h2>

        <p className="text-gray-600 mb-6">
          {accessMessage}
        </p>

        {showActions && accessAction && (
          <div className="space-y-3">
            <button
              onClick={() => navigate(accessAction.path)}
              className="w-full inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              {accessAction.path === '/login' && <LogIn className="mr-2 h-5 w-5" />}
              {accessAction.path === '/patron/complexes' && <Building2 className="mr-2 h-5 w-5" />}
              {accessAction.text}
            </button>

            <button
              onClick={() => navigate('/dashboard')}
              className="w-full px-6 py-3 text-gray-600 font-medium rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
            >
              Retour au tableau de bord
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Version simplifiée pour les composants inline
 */
export const ComplexeAccessMessage: React.FC<{
  className?: string;
  showIcon?: boolean;
}> = ({ className = '', showIcon = true }) => {
  const navigate = useNavigate();
  const { hasAccess, accessMessage, accessAction } = useComplexeStatus();

  if (hasAccess) {
    return null;
  }

  return (
    <div className={`bg-yellow-50 border border-yellow-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start">
        {showIcon && (
          <AlertTriangle className="h-5 w-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" />
        )}
        <div className="flex-1">
          <p className="text-sm text-yellow-800 mb-2">
            {accessMessage}
          </p>
          {accessAction && (
            <button
              onClick={() => navigate(accessAction.path)}
              className="text-sm font-medium text-yellow-800 hover:text-yellow-900 underline"
            >
              {accessAction.text}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ComplexeAccessGuard;
