import React, { useState } from 'react';
import { AlertCircle, CheckCircle, AlertTriangle } from 'lucide-react';
import { DataTable } from './DataTable';
import { ErrorList } from './ErrorList';
import { SuggestionList } from './SuggestionList';
import type { ImportPreview as ImportPreviewType } from '../../types';

interface ImportPreviewProps {
  preview: ImportPreviewType;
  onConfirm: () => void;
  onCancel: () => void;
  loading?: boolean;
}



export const ImportPreview: React.FC<ImportPreviewProps> = ({
  preview,
  onConfirm,
  onCancel,
  loading = false
}) => {
  const [activeTab, setActiveTab] = useState<'donnees' | 'erreurs' | 'suggestions'>('donnees');

  const getStatutColor = (taux: number) => {
    if (taux >= 90) return 'text-green-600 bg-green-100';
    if (taux >= 70) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getStatutIcon = (taux: number) => {
    if (taux >= 90) return <CheckCircle className="h-5 w-5" />;
    if (taux >= 70) return <AlertTriangle className="h-5 w-5" />;
    return <AlertCircle className="h-5 w-5" />;
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          Prévisualisation de l'import
        </h3>
        <div className={`px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-2 ${getStatutColor(preview.statistiques.taux_succes)}`}>
          {getStatutIcon(preview.statistiques.taux_succes)}
          <span>{preview.statistiques.taux_succes.toFixed(1)}% de réussite</span>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-4 gap-4 mb-6">
        <div className="text-center p-4 bg-gray-50 rounded-lg">
          <div className="text-2xl font-bold text-gray-900">{preview.statistiques.total_lignes}</div>
          <div className="text-sm text-gray-600">Total lignes</div>
        </div>
        <div className="text-center p-4 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">{preview.statistiques.lignes_valides}</div>
          <div className="text-sm text-gray-600">Lignes valides</div>
        </div>
        <div className="text-center p-4 bg-red-50 rounded-lg">
          <div className="text-2xl font-bold text-red-600">{preview.statistiques.lignes_erreur}</div>
          <div className="text-sm text-gray-600">Lignes avec erreurs</div>
        </div>
        <div className="text-center p-4 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">{preview.suggestions.length}</div>
          <div className="text-sm text-gray-600">Suggestions</div>
        </div>
      </div>

      {/* Onglets */}
      <div className="border-b border-gray-200 mb-4">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'donnees', label: 'Données valides', count: preview.donnees_valides.length },
            { id: 'erreurs', label: 'Erreurs', count: preview.erreurs.length },
            { id: 'suggestions', label: 'Suggestions', count: preview.suggestions.length }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </nav>
      </div>

      {/* Contenu des onglets */}
      <div className="min-h-64 max-h-96 overflow-auto">
        {activeTab === 'donnees' && (
          <DataTable data={preview.donnees_valides} />
        )}

        {activeTab === 'erreurs' && (
          <ErrorList errors={preview.erreurs} />
        )}

        {activeTab === 'suggestions' && (
          <SuggestionList suggestions={preview.suggestions} />
        )}
      </div>

      {/* Actions */}
      <div className="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
        <div className="text-sm text-gray-600">
          {preview.statistiques.lignes_erreur > 0 && (
            <span className="text-yellow-600 flex items-center">
              <AlertTriangle className="h-4 w-4 mr-1" />
              Des erreurs ont été détectées. L'import ne traitera que les lignes valides.
            </span>
          )}
        </div>

        <div className="flex space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
            disabled={loading}
          >
            Annuler
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
            disabled={loading || preview.statistiques.lignes_valides === 0}
          >
            {loading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Import en cours...
              </div>
            ) : (
              `Confirmer l'import (${preview.statistiques.lignes_valides} lignes)`
            )}
          </button>
        </div>
      </div>
    </div>
  );
};
