import React, { useState, useEffect } from 'react';
import { X, Save, AlertCircle } from 'lucide-react';
import type { 
  Ingredient, 
  UniteMesure, 
  CategorieIngredient, 
  TypeConservation 
} from '../../types';

interface IngredientModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: Partial<Ingredient>) => Promise<void>;
  ingredient?: Ingredient | null;
  title: string;
}

const UNITES_MESURE: UniteMesure[] = ['kg', 'g', 'L', 'mL', 'unité', 'pièce', 'portion'];

const CATEGORIES: CategorieIngredient[] = [
  'Légumes',
  'Viandes', 
  'Poissons',
  'Boissons',
  'Épices',
  'Produits laitiers',
  'Céréales',
  'Fruits'
];

const TYPES_CONSERVATION: TypeConservation[] = ['Frais', 'Congelé', 'Sec', 'Ambiant'];

export const IngredientModal: React.FC<IngredientModalProps> = ({
  isOpen,
  onClose,
  onSave,
  ingredient,
  title
}) => {
  const [formData, setFormData] = useState<Partial<Ingredient>>({
    nom: '',
    description: '',
    unite_mesure: 'kg',
    categorie: 'Légumes',
    code_barre: '',
    prix_unitaire_moyen: 0,
    conservation: 'Frais',
    duree_conservation_jours: undefined,
    actif: true,
    allergenes: []
  });

  const [allergenesInput, setAllergenesInput] = useState('');
  const [loading, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (ingredient) {
      setFormData({
        nom: ingredient.nom || '',
        description: ingredient.description || '',
        unite_mesure: ingredient.unite_mesure || 'kg',
        categorie: ingredient.categorie || 'Légumes',
        code_barre: ingredient.code_barre || '',
        prix_unitaire_moyen: ingredient.prix_unitaire_moyen || 0,
        conservation: ingredient.conservation || 'Frais',
        duree_conservation_jours: ingredient.duree_conservation_jours,
        actif: ingredient.actif !== undefined ? ingredient.actif : true,
        allergenes: ingredient.allergenes || []
      });
      setAllergenesInput((ingredient.allergenes || []).join(', '));
    } else {
      // Reset pour création
      setFormData({
        nom: '',
        description: '',
        unite_mesure: 'kg',
        categorie: 'Légumes',
        code_barre: '',
        prix_unitaire_moyen: 0,
        conservation: 'Frais',
        duree_conservation_jours: undefined,
        actif: true,
        allergenes: []
      });
      setAllergenesInput('');
    }
    setErrors({});
  }, [ingredient, isOpen]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.nom?.trim()) {
      newErrors.nom = 'Le nom est requis';
    }

    if (formData.prix_unitaire_moyen !== undefined && formData.prix_unitaire_moyen < 0) {
      newErrors.prix_unitaire_moyen = 'Le prix doit être positif';
    }

    if (formData.duree_conservation_jours !== undefined && formData.duree_conservation_jours <= 0) {
      newErrors.duree_conservation_jours = 'La durée doit être positive';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setSaving(true);
      
      // Traiter les allergènes
      const allergenes = allergenesInput
        .split(',')
        .map(a => a.trim())
        .filter(a => a.length > 0);

      const dataToSave = {
        ...formData,
        allergenes: allergenes.length > 0 ? allergenes : undefined
      };

      await onSave(dataToSave);
      onClose();
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof Ingredient, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Effacer l'erreur si elle existe
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            disabled={loading}
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Nom */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nom de l'ingrédient *
            </label>
            <input
              type="text"
              value={formData.nom || ''}
              onChange={(e) => handleInputChange('nom', e.target.value)}
              className={`w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.nom ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Ex: Tomates cerises"
              disabled={loading}
            />
            {errors.nom && (
              <div className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.nom}
              </div>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={formData.description || ''}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Description optionnelle de l'ingrédient"
              disabled={loading}
            />
          </div>

          {/* Catégorie et Unité de mesure */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Catégorie *
              </label>
              <select
                value={formData.categorie || ''}
                onChange={(e) => handleInputChange('categorie', e.target.value as CategorieIngredient)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={loading}
              >
                {CATEGORIES.map(cat => (
                  <option key={cat} value={cat}>{cat}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Unité de mesure *
              </label>
              <select
                value={formData.unite_mesure || ''}
                onChange={(e) => handleInputChange('unite_mesure', e.target.value as UniteMesure)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={loading}
              >
                {UNITES_MESURE.map(unite => (
                  <option key={unite} value={unite}>{unite}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Prix et Code-barres */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Prix unitaire moyen (FCFA)
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.prix_unitaire_moyen || ''}
                onChange={(e) => handleInputChange('prix_unitaire_moyen', parseFloat(e.target.value) || 0)}
                className={`w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.prix_unitaire_moyen ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="0.00"
                disabled={loading}
              />
              {errors.prix_unitaire_moyen && (
                <div className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.prix_unitaire_moyen}
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Code-barres
              </label>
              <input
                type="text"
                value={formData.code_barre || ''}
                onChange={(e) => handleInputChange('code_barre', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Code-barres optionnel"
                disabled={loading}
              />
            </div>
          </div>

          {/* Conservation et Durée */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Type de conservation *
              </label>
              <select
                value={formData.conservation || ''}
                onChange={(e) => handleInputChange('conservation', e.target.value as TypeConservation)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={loading}
              >
                {TYPES_CONSERVATION.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Durée de conservation (jours)
              </label>
              <input
                type="number"
                min="1"
                value={formData.duree_conservation_jours || ''}
                onChange={(e) => handleInputChange('duree_conservation_jours', parseInt(e.target.value) || undefined)}
                className={`w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.duree_conservation_jours ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Ex: 7"
                disabled={loading}
              />
              {errors.duree_conservation_jours && (
                <div className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.duree_conservation_jours}
                </div>
              )}
            </div>
          </div>

          {/* Allergènes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Allergènes
            </label>
            <input
              type="text"
              value={allergenesInput}
              onChange={(e) => setAllergenesInput(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Ex: gluten, lactose, noix (séparés par des virgules)"
              disabled={loading}
            />
            <div className="mt-1 text-xs text-gray-500">
              Séparez les allergènes par des virgules
            </div>
          </div>

          {/* Statut actif */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="actif"
              checked={formData.actif || false}
              onChange={(e) => handleInputChange('actif', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              disabled={loading}
            />
            <label htmlFor="actif" className="ml-2 block text-sm text-gray-700">
              Ingrédient actif
            </label>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              disabled={loading}
            >
              Annuler
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center"
              disabled={loading}
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Sauvegarde...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Sauvegarder
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
