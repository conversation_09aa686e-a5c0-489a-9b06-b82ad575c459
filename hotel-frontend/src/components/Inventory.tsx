import { useState } from 'react'
import { ArrowDown, ArrowUp, Archive, RefreshCw, Search, Filter } from 'lucide-react'

const inventoryCategories = [
  'All',
  'Housekeeping',
  'Food & Beverage',
  'Maintenance',
  'Office Supplies'
]

const inventoryItems = [
  {
    id: 1,
    name: 'Bath Towels',
    category: 'Housekeeping',
    quantity: 150,
    threshold: 50,
    unit: 'pieces',
    lastMovement: '2024-03-15T10:30:00',
    status: 'normal'
  },
  {
    id: 2,
    name: 'Toilet Paper',
    category: 'Housekeeping',
    quantity: 80,
    threshold: 100,
    unit: 'rolls',
    lastMovement: '2024-03-15T09:15:00',
    status: 'low'
  },
  {
    id: 3,
    name: 'Coffee Beans',
    category: 'Food & Beverage',
    quantity: 25,
    threshold: 20,
    unit: 'kg',
    lastMovement: '2024-03-14T16:45:00',
    status: 'warning'
  },
  {
    id: 4,
    name: 'Light Bulbs',
    category: 'Maintenance',
    quantity: 45,
    threshold: 30,
    unit: 'pieces',
    lastMovement: '2024-03-13T11:20:00',
    status: 'normal'
  }
]

interface InventoryProps {
  onClose: () => void;
}

export function Inventory({ onClose }: InventoryProps) {
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [searchQuery, setSearchQuery] = useState('')
  const [showRestockModal, setShowRestockModal] = useState(false)
  const [selectedItem, setSelectedItem] = useState<typeof inventoryItems[0] | null>(null)
  const [restockAmount, setRestockAmount] = useState('')

  const handleRestock = () => {
    if (selectedItem && restockAmount) {
      // Here you would typically make an API call to update the inventory
      console.log(`Restocking ${selectedItem.name} with ${restockAmount} ${selectedItem.unit}`)
      setShowRestockModal(false)
      setSelectedItem(null)
      setRestockAmount('')
    }
  }

  const handleConsumption = (item: typeof inventoryItems[0], amount: number) => {
    // Here you would typically make an API call to update the inventory
    console.log(`Consuming ${amount} ${item.unit} of ${item.name}`)
  }

  const handleRetire = (item: typeof inventoryItems[0]) => {
    // Here you would typically make an API call to update the inventory
    console.log(`Retiring ${item.name} from inventory`)
  }

  const filteredItems = inventoryItems.filter(item => {
    const matchesCategory = selectedCategory === 'All' || item.category === selectedCategory
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesCategory && matchesSearch
  })

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Inventory Management</h1>
        <button
          onClick={onClose}
          className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
        >
          Close
        </button>
      </div>

      {/* Filters and Search */}
      <div className="mb-6 flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search inventory..."
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
        <div className="flex space-x-2">
          {inventoryCategories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-lg ${
                selectedCategory === category
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 hover:bg-gray-200'
              }`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* Inventory Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Item
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Category
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Quantity
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Movement
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredItems.map((item) => (
              <tr key={item.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{item.name}</div>
                  <div className="text-sm text-gray-500">{item.unit}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{item.category}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{item.quantity}</div>
                  <div className="text-sm text-gray-500">Threshold: {item.threshold}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${
                      item.status === 'normal'
                        ? 'bg-green-100 text-green-800'
                        : item.status === 'warning'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {item.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(item.lastMovement).toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end space-x-2">
                    <button
                      onClick={() => {
                        setSelectedItem(item)
                        setShowRestockModal(true)
                      }}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      <ArrowUp className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => handleConsumption(item, 1)}
                      className="text-yellow-600 hover:text-yellow-900"
                    >
                      <ArrowDown className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => handleRetire(item)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <Archive className="w-5 h-5" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Restock Modal */}
      {showRestockModal && selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Restock {selectedItem.name}</h3>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Amount to Add
              </label>
              <input
                type="number"
                value={restockAmount}
                onChange={(e) => setRestockAmount(e.target.value)}
                className="w-full p-2 border rounded-lg"
                placeholder={`Enter amount in ${selectedItem.unit}`}
              />
            </div>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => {
                  setShowRestockModal(false)
                  setSelectedItem(null)
                  setRestockAmount('')
                }}
                className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg"
              >
                Cancel
              </button>
              <button
                onClick={handleRestock}
                disabled={!restockAmount}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 