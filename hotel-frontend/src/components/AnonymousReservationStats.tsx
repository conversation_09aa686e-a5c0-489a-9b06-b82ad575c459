import { useState, useEffect } from 'react';
import { BarChart3, Users, CheckCircle, Clock } from 'lucide-react';
import { anonymousReservationService, PublicStats } from '../services/anonymousReservation.service';

interface AnonymousReservationStatsProps {
  className?: string;
  showTitle?: boolean;
}

export function AnonymousReservationStats({ 
  className = '', 
  showTitle = true 
}: AnonymousReservationStatsProps) {
  const [stats, setStats] = useState<PublicStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await anonymousReservationService.getPublicStats();
      if (response.success) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error);
      setError('Impossible de charger les statistiques');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-8 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error || !stats) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <div className="text-center text-gray-500">
          <BarChart3 className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">{error || 'Aucune statistique disponible'}</p>
        </div>
      </div>
    );
  }

  const statItems = [
    {
      label: 'Réservations anonymes',
      value: stats.total_reservations_anonymes,
      icon: Users,
      color: 'text-blue-600 bg-blue-100'
    },
    {
      label: 'Réservations actives',
      value: stats.reservations_actives,
      icon: CheckCircle,
      color: 'text-green-600 bg-green-100'
    },
    {
      label: 'Taux de confirmation',
      value: `${Math.round(stats.taux_confirmation)}%`,
      icon: BarChart3,
      color: 'text-purple-600 bg-purple-100'
    },
    {
      label: 'Délai moyen',
      value: `${Math.round(stats.delai_moyen_confirmation)}h`,
      icon: Clock,
      color: 'text-orange-600 bg-orange-100'
    }
  ];

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      {showTitle && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Statistiques des réservations anonymes
          </h3>
          <p className="text-sm text-gray-600">
            Données publiques et anonymisées
          </p>
        </div>
      )}

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {statItems.map((item, index) => (
          <div key={index} className="text-center">
            <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${item.color} mb-3`}>
              <item.icon className="h-6 w-6" />
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-1">
              {item.value}
            </div>
            <div className="text-sm text-gray-600">
              {item.label}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 pt-4 border-t border-gray-200">
        <p className="text-xs text-gray-500 text-center">
          Statistiques mises à jour en temps réel • Données anonymisées
        </p>
      </div>
    </div>
  );
}

// Composant compact pour affichage dans une sidebar ou widget
export function AnonymousReservationStatsCompact({ className = '' }: { className?: string }) {
  const [stats, setStats] = useState<PublicStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadStats = async () => {
      try {
        const response = await anonymousReservationService.getPublicStats();
        if (response.success) {
          setStats(response.data);
        }
      } catch (error) {
        console.error('Erreur lors du chargement des statistiques:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadStats();
  }, []);

  if (isLoading) {
    return (
      <div className={`bg-gray-50 rounded-lg p-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-6 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  return (
    <div className={`bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200 ${className}`}>
      <div className="flex items-center justify-between mb-2">
        <h4 className="text-sm font-medium text-blue-900">
          Réservations anonymes
        </h4>
        <Users className="h-4 w-4 text-blue-600" />
      </div>
      <div className="space-y-1">
        <div className="flex justify-between text-sm">
          <span className="text-blue-700">Total:</span>
          <span className="font-medium text-blue-900">{stats.total_reservations_anonymes}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-blue-700">Actives:</span>
          <span className="font-medium text-blue-900">{stats.reservations_actives}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-blue-700">Taux:</span>
          <span className="font-medium text-blue-900">{Math.round(stats.taux_confirmation)}%</span>
        </div>
      </div>
    </div>
  );
}
