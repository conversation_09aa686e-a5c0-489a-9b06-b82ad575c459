// Export des composants spécialisés pour les employés
export { KitchenInterface } from './KitchenInterface';
export { EmployeeHeader } from './EmployeeHeader';

// Types et interfaces pour les composants employé
export interface EmployeeComponentProps {
  onClose?: () => void;
}

export interface QuickAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  onClick: () => void;
  color: string;
}

export interface EmployeeHeaderProps {
  onMenuToggle?: () => void;
  showMenuButton?: boolean;
  notifications?: number;
  onNotificationsClick?: () => void;
  className?: string;
}

// Types pour les commandes cuisine
export interface CommandeItem {
  item_id: number;
  nom: string;
  quantite: number;
  instructions?: string;
  statut: 'en_attente' | 'en_cours' | 'pret';
  temps_preparation?: number;
}

export interface CommandeCuisine {
  commande_id: number;
  table_numero: string;
  heure_commande: string;
  statut: 'en_attente' | 'en_cours' | 'pret' | 'servie';
  priorite: 'normale' | 'urgente';
  items: CommandeItem[];
  temps_ecoule: number; // en minutes
  client_nom?: string;
}

// Constantes utiles
export const EMPLOYEE_TYPES = {
  RECEPTION: 'reception',
  GERANT_PISCINE: 'gerant_piscine',
  SERVEUSE: 'serveuse',
  GERANT_SERVICES: 'gerant_services',
  CUISINE: 'cuisine'
} as const;

export const COMMANDE_STATUTS = {
  EN_ATTENTE: 'en_attente',
  EN_COURS: 'en_cours',
  PRET: 'pret',
  SERVIE: 'servie'
} as const;

export const PRIORITES = {
  NORMALE: 'normale',
  URGENTE: 'urgente'
} as const;

// Helpers pour les composants employé
export const getEmployeeTypeLabel = (type: string): string => {
  switch (type) {
    case 'reception': return 'Employé Réception';
    case 'gerant_piscine': return 'Gérant Piscine';
    case 'serveuse': return 'Serveuse';
    case 'gerant_services': return 'Gérant Services';
    case 'cuisine': return 'Employé Cuisine';
    default: return 'Employé';
  }
};

export const getEmployeeTypeColor = (type: string): string => {
  switch (type) {
    case 'reception': return 'bg-blue-100 text-blue-800';
    case 'gerant_piscine': return 'bg-cyan-100 text-cyan-800';
    case 'serveuse': return 'bg-green-100 text-green-800';
    case 'gerant_services': return 'bg-purple-100 text-purple-800';
    case 'cuisine': return 'bg-orange-100 text-orange-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

export const getCommandeStatutColor = (statut: string): string => {
  switch (statut) {
    case 'en_attente': return 'bg-gray-100 text-gray-800';
    case 'en_cours': return 'bg-blue-100 text-blue-800';
    case 'pret': return 'bg-green-100 text-green-800';
    case 'servie': return 'bg-purple-100 text-purple-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

export const getPrioriteColor = (priorite: string, tempsEcoule: number): string => {
  if (priorite === 'urgente' || tempsEcoule > 20) {
    return 'border-red-500 bg-red-50';
  }
  if (tempsEcoule > 15) {
    return 'border-orange-500 bg-orange-50';
  }
  return 'border-green-500 bg-green-50';
};

// Documentation des composants
/**
 * # Composants Employé
 * 
 * Ce module contient tous les composants spécialisés pour les interfaces employé.
 * 
 * ## Composants disponibles
 * 
 * ### KitchenInterface
 * Interface spécialisée pour les employés cuisine permettant de :
 * - Voir les commandes restaurant en temps réel
 * - Gérer les statuts de préparation
 * - Prioriser les commandes urgentes
 * - Marquer les plats comme prêts
 * 
 * ```tsx
 * import { KitchenInterface } from '../components/employee';
 * 
 * <KitchenInterface onClose={() => navigate('/dashboard')} />
 * ```
 * 
 * ### EmployeeHeader
 * Header simplifié pour tous les employés avec :
 * - Informations utilisateur (nom, type, services autorisés)
 * - Actions rapides contextuelles selon le type d'employé
 * - Notifications avec badge
 * - Menu utilisateur (profil, paramètres, déconnexion)
 * 
 * ```tsx
 * import { EmployeeHeader } from '../components/employee';
 * 
 * <EmployeeHeader 
 *   notifications={5}
 *   onNotificationsClick={() => setShowNotifications(true)}
 *   showMenuButton={true}
 *   onMenuToggle={() => setMobileMenuOpen(!mobileMenuOpen)}
 * />
 * ```
 * 
 * ## Types d'employés supportés
 * 
 * - **reception** : Gestion réservations, clients, chambres
 * - **gerant_piscine** : Gestion piscine et billetterie
 * - **serveuse** : Service en salle, prise de commandes
 * - **gerant_services** : Gestion services + équipe
 * - **cuisine** : Préparation commandes restaurant
 * 
 * ## Actions rapides par type
 * 
 * ### Réception
 * - Nouvelle réservation
 * - Check-in
 * 
 * ### Piscine
 * - Nouveau ticket
 * 
 * ### Service (Serveuse/Gérant)
 * - Nouvelle commande restaurant (si autorisé)
 * - Nouvelle commande bar (si autorisé)
 * 
 * ### Cuisine
 * - Voir les commandes
 * 
 * ## Intégration avec les hooks
 * 
 * Les composants utilisent automatiquement :
 * - `useEmployeePermissions` : Pour les permissions et informations utilisateur
 * - `useNotifications` : Pour la gestion des notifications
 * - Navigation React Router pour les redirections
 * 
 * ## Responsive Design
 * 
 * Tous les composants sont optimisés pour :
 * - Desktop (interface complète)
 * - Tablette (interface adaptée)
 * - Mobile (interface simplifiée avec menu hamburger)
 */
