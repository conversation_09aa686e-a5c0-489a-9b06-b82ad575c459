import React, { useState } from 'react';
import { 
  User, 
  Bell, 
  Settings, 
  LogOut, 
  ChevronDown,
  UserCheck,
  Briefcase,
  Shield,
  Clock,
  Menu,
  X
} from 'lucide-react';
import { useEmployeePermissions } from '../../hooks/useEmployeePermissions';
import { authService } from '../../services/auth.service';
import { useNavigate } from 'react-router-dom';

interface EmployeeHeaderProps {
  onMenuToggle?: () => void;
  showMenuButton?: boolean;
  notifications?: number;
  onNotificationsClick?: () => void;
  className?: string;
}

interface QuickAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  onClick: () => void;
  color: string;
}

export const EmployeeHeader: React.FC<EmployeeHeaderProps> = ({
  onMenuToggle,
  showMenuButton = false,
  notifications = 0,
  onNotificationsClick,
  className = ''
}) => {
  const navigate = useNavigate();
  const { 
    employeeType, 
    fullName, 
    userTypeLabel, 
    authorizedServices, 
    loading 
  } = useEmployeePermissions();
  
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showQuickActions, setShowQuickActions] = useState(false);

  // Actions rapides selon le type d'employé
  const getQuickActions = (): QuickAction[] => {
    const actions: QuickAction[] = [];

    switch (employeeType) {
      case 'reception':
        actions.push(
          {
            id: 'new-reservation',
            label: 'Nouvelle réservation',
            icon: <User className="w-4 h-4" />,
            onClick: () => navigate('/reception?action=new'),
            color: 'bg-blue-500 hover:bg-blue-600'
          },
          {
            id: 'check-in',
            label: 'Check-in',
            icon: <UserCheck className="w-4 h-4" />,
            onClick: () => navigate('/reception?action=checkin'),
            color: 'bg-green-500 hover:bg-green-600'
          }
        );
        break;

      case 'gerant_piscine':
        actions.push(
          {
            id: 'new-ticket',
            label: 'Nouveau ticket',
            icon: <User className="w-4 h-4" />,
            onClick: () => navigate('/pool?action=new'),
            color: 'bg-blue-500 hover:bg-blue-600'
          }
        );
        break;

      case 'serveuse':
      case 'gerant_services':
        if (authorizedServices.includes('restaurant')) {
          actions.push({
            id: 'new-order-restaurant',
            label: 'Nouvelle commande restaurant',
            icon: <User className="w-4 h-4" />,
            onClick: () => navigate('/pos?service=restaurant'),
            color: 'bg-orange-500 hover:bg-orange-600'
          });
        }
        if (authorizedServices.includes('bar')) {
          actions.push({
            id: 'new-order-bar',
            label: 'Nouvelle commande bar',
            icon: <User className="w-4 h-4" />,
            onClick: () => navigate('/pos?service=bar'),
            color: 'bg-purple-500 hover:bg-purple-600'
          });
        }
        break;

      case 'cuisine':
        actions.push(
          {
            id: 'view-orders',
            label: 'Voir les commandes',
            icon: <User className="w-4 h-4" />,
            onClick: () => navigate('/pos?view=kitchen'),
            color: 'bg-red-500 hover:bg-red-600'
          }
        );
        break;
    }

    return actions;
  };

  const handleLogout = async () => {
    try {
      await authService.logout();
      navigate('/login');
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    }
  };

  const getEmployeeTypeColor = (type: string) => {
    switch (type) {
      case 'reception': return 'bg-blue-100 text-blue-800';
      case 'gerant_piscine': return 'bg-cyan-100 text-cyan-800';
      case 'serveuse': return 'bg-green-100 text-green-800';
      case 'gerant_services': return 'bg-purple-100 text-purple-800';
      case 'cuisine': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getEmployeeTypeIcon = (type: string) => {
    switch (type) {
      case 'reception': return <User className="w-4 h-4" />;
      case 'gerant_piscine': return <User className="w-4 h-4" />;
      case 'serveuse': return <User className="w-4 h-4" />;
      case 'gerant_services': return <Briefcase className="w-4 h-4" />;
      case 'cuisine': return <User className="w-4 h-4" />;
      default: return <User className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className={`bg-white shadow-sm border-b border-gray-200 ${className}`}>
        <div className="container mx-auto px-4 py-3">
          <div className="animate-pulse flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="rounded-full bg-gray-300 h-10 w-10"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-300 rounded w-32"></div>
                <div className="h-3 bg-gray-300 rounded w-24"></div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="h-8 bg-gray-300 rounded w-20"></div>
              <div className="h-8 bg-gray-300 rounded w-8"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const quickActions = getQuickActions();

  return (
    <div className={`bg-white shadow-sm border-b border-gray-200 ${className}`}>
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Left side - User info */}
          <div className="flex items-center space-x-4">
            {/* Menu button for mobile */}
            {showMenuButton && (
              <button
                onClick={onMenuToggle}
                className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 lg:hidden"
              >
                <Menu className="h-5 w-5" />
              </button>
            )}

            {/* User avatar */}
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <UserCheck className="w-5 h-5 text-white" />
              </div>
            </div>

            {/* User info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {fullName}
                </p>
                {employeeType && (
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getEmployeeTypeColor(employeeType)}`}>
                    {getEmployeeTypeIcon(employeeType)}
                    <span className="ml-1 capitalize">{employeeType.replace('_', ' ')}</span>
                  </span>
                )}
              </div>
              <div className="flex items-center space-x-2 mt-1">
                <p className="text-xs text-gray-500">{userTypeLabel}</p>
                {authorizedServices.length > 0 && (
                  <>
                    <span className="text-xs text-gray-300">•</span>
                    <div className="flex items-center space-x-1">
                      <Shield className="w-3 h-3 text-gray-400" />
                      <span className="text-xs text-gray-500">
                        {authorizedServices.join(', ')}
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Right side - Actions */}
          <div className="flex items-center space-x-2">
            {/* Quick actions */}
            {quickActions.length > 0 && (
              <div className="relative">
                <button
                  onClick={() => setShowQuickActions(!showQuickActions)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Actions rapides
                  <ChevronDown className="ml-2 -mr-0.5 h-4 w-4" />
                </button>

                {showQuickActions && (
                  <div className="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                    <div className="py-1">
                      {quickActions.map((action) => (
                        <button
                          key={action.id}
                          onClick={() => {
                            action.onClick();
                            setShowQuickActions(false);
                          }}
                          className="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                        >
                          <div className={`flex-shrink-0 w-6 h-6 rounded flex items-center justify-center mr-3 ${action.color} text-white`}>
                            {action.icon}
                          </div>
                          {action.label}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Notifications */}
            <button
              onClick={onNotificationsClick}
              className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Bell className="h-5 w-5" />
              {notifications > 0 && (
                <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
                  {notifications > 99 ? '99+' : notifications}
                </span>
              )}
            </button>

            {/* User menu */}
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-gray-600" />
                </div>
              </button>

              {showUserMenu && (
                <div className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                  <div className="py-1">
                    <div className="px-4 py-2 text-sm text-gray-700 border-b border-gray-100">
                      <p className="font-medium">{fullName}</p>
                      <p className="text-xs text-gray-500">{userTypeLabel}</p>
                    </div>
                    <button
                      onClick={() => {
                        navigate('/profile');
                        setShowUserMenu(false);
                      }}
                      className="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                    >
                      <User className="mr-3 h-4 w-4 text-gray-400 group-hover:text-gray-500" />
                      Mon profil
                    </button>
                    <button
                      onClick={() => {
                        navigate('/settings');
                        setShowUserMenu(false);
                      }}
                      className="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                    >
                      <Settings className="mr-3 h-4 w-4 text-gray-400 group-hover:text-gray-500" />
                      Paramètres
                    </button>
                    <button
                      onClick={() => {
                        handleLogout();
                        setShowUserMenu(false);
                      }}
                      className="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                    >
                      <LogOut className="mr-3 h-4 w-4 text-gray-400 group-hover:text-gray-500" />
                      Se déconnecter
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Time indicator */}
        <div className="mt-2 flex items-center text-xs text-gray-500">
          <Clock className="w-3 h-3 mr-1" />
          <span>
            {new Date().toLocaleDateString('fr-FR', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </span>
        </div>
      </div>

      {/* Click outside handlers */}
      {(showUserMenu || showQuickActions) && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => {
            setShowUserMenu(false);
            setShowQuickActions(false);
          }}
        />
      )}
    </div>
  );
};

export default EmployeeHeader;
