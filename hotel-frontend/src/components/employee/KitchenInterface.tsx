import React, { useState, useEffect } from 'react';
import { 
  ChefHat, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  Play, 
  Pause,
  RefreshCw,
  Bell,
  Users,
  Utensils
} from 'lucide-react';
import { useEmployeePermissions } from '../../hooks/useEmployeePermissions';

// Types pour les commandes cuisine
interface CommandeItem {
  item_id: number;
  nom: string;
  quantite: number;
  instructions?: string;
  statut: 'en_attente' | 'en_cours' | 'pret';
  temps_preparation?: number;
}

interface CommandeCuisine {
  commande_id: number;
  table_numero: string;
  heure_commande: string;
  statut: 'en_attente' | 'en_cours' | 'pret' | 'servie';
  priorite: 'normale' | 'urgente';
  items: CommandeItem[];
  temps_ecoule: number; // en minutes
  client_nom?: string;
}

interface KitchenInterfaceProps {
  onClose?: () => void;
}

export const KitchenInterface: React.FC<KitchenInterfaceProps> = ({ onClose }) => {
  const { employeeType, fullName, loading } = useEmployeePermissions();
  const [commandes, setCommandes] = useState<CommandeCuisine[]>([]);
  const [loadingCommandes, setLoadingCommandes] = useState(true);
  const [selectedCommande, setSelectedCommande] = useState<CommandeCuisine | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Données de démonstration (à remplacer par des appels API)
  const commandesDemo: CommandeCuisine[] = [
    {
      commande_id: 1,
      table_numero: "Table 3",
      heure_commande: "14:30",
      statut: "en_attente",
      priorite: "urgente",
      temps_ecoule: 15,
      items: [
        { item_id: 1, nom: "Steak frites", quantite: 1, statut: "en_attente", temps_preparation: 20 },
        { item_id: 2, nom: "Salade César", quantite: 1, statut: "en_attente", temps_preparation: 10 }
      ]
    },
    {
      commande_id: 2,
      table_numero: "Table 7",
      heure_commande: "14:45",
      statut: "en_cours",
      priorite: "normale",
      temps_ecoule: 8,
      items: [
        { item_id: 3, nom: "Poisson du jour", quantite: 2, statut: "en_cours", temps_preparation: 25 },
        { item_id: 4, nom: "Légumes grillés", quantite: 2, statut: "pret", temps_preparation: 15 }
      ]
    },
    {
      commande_id: 3,
      table_numero: "Table 12",
      heure_commande: "15:00",
      statut: "en_attente",
      priorite: "normale",
      temps_ecoule: 3,
      items: [
        { item_id: 5, nom: "Pasta Carbonara", quantite: 1, statut: "en_attente", temps_preparation: 18 }
      ]
    }
  ];

  useEffect(() => {
    loadCommandes();
    // Actualisation automatique toutes les 30 secondes
    const interval = setInterval(loadCommandes, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadCommandes = async () => {
    try {
      setLoadingCommandes(true);
      // TODO: Remplacer par un appel API réel
      // const response = await commandeService.getCommandesCuisine();
      // setCommandes(response.data);
      
      // Simulation d'un délai d'API
      await new Promise(resolve => setTimeout(resolve, 500));
      setCommandes(commandesDemo);
    } catch (error) {
      console.error('Erreur lors du chargement des commandes:', error);
    } finally {
      setLoadingCommandes(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadCommandes();
    setRefreshing(false);
  };

  const updateCommandeStatut = async (commandeId: number, nouveauStatut: CommandeCuisine['statut']) => {
    try {
      // TODO: Appel API pour mettre à jour le statut
      // await commandeService.updateStatut(commandeId, nouveauStatut);
      
      setCommandes(prev => 
        prev.map(cmd => 
          cmd.commande_id === commandeId 
            ? { ...cmd, statut: nouveauStatut }
            : cmd
        )
      );
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut:', error);
    }
  };

  const updateItemStatut = async (commandeId: number, itemId: number, nouveauStatut: CommandeItem['statut']) => {
    try {
      // TODO: Appel API pour mettre à jour le statut de l'item
      // await commandeService.updateItemStatut(commandeId, itemId, nouveauStatut);
      
      setCommandes(prev => 
        prev.map(cmd => 
          cmd.commande_id === commandeId 
            ? {
                ...cmd,
                items: cmd.items.map(item => 
                  item.item_id === itemId 
                    ? { ...item, statut: nouveauStatut }
                    : item
                )
              }
            : cmd
        )
      );
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut de l\'item:', error);
    }
  };

  const getPrioriteColor = (priorite: CommandeCuisine['priorite'], tempsEcoule: number) => {
    if (priorite === 'urgente' || tempsEcoule > 20) {
      return 'border-red-500 bg-red-50';
    }
    if (tempsEcoule > 15) {
      return 'border-orange-500 bg-orange-50';
    }
    return 'border-green-500 bg-green-50';
  };

  const getStatutColor = (statut: CommandeItem['statut']) => {
    switch (statut) {
      case 'en_attente': return 'bg-gray-100 text-gray-800';
      case 'en_cours': return 'bg-blue-100 text-blue-800';
      case 'pret': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatutIcon = (statut: CommandeItem['statut']) => {
    switch (statut) {
      case 'en_attente': return <Clock className="w-4 h-4" />;
      case 'en_cours': return <Play className="w-4 h-4" />;
      case 'pret': return <CheckCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Chargement de l'interface cuisine...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Cuisine */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center">
                <ChefHat className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Interface Cuisine</h1>
                <p className="text-gray-600">Bienvenue, {fullName} - Employé Cuisine</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Actualiser
              </button>
              {onClose && (
                <button
                  onClick={onClose}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                >
                  Retour au Dashboard
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Contenu Principal */}
      <div className="container mx-auto px-4 py-6">
        {/* Statistiques rapides */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Utensils className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Commandes en attente</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {commandes.filter(c => c.statut === 'en_attente').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Play className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">En préparation</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {commandes.filter(c => c.statut === 'en_cours').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Urgentes</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {commandes.filter(c => c.priorite === 'urgente' || c.temps_ecoule > 20).length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Prêtes</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {commandes.filter(c => c.statut === 'pret').length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Liste des commandes */}
        <div className="space-y-4">
          {loadingCommandes ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Chargement des commandes...</p>
            </div>
          ) : commandes.length === 0 ? (
            <div className="bg-white rounded-lg shadow p-8 text-center">
              <ChefHat className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune commande</h3>
              <p className="text-gray-600">Il n'y a actuellement aucune commande à préparer.</p>
            </div>
          ) : (
            commandes.map((commande) => (
              <div
                key={commande.commande_id}
                className={`bg-white rounded-lg shadow border-l-4 p-6 ${getPrioriteColor(commande.priorite, commande.temps_ecoule)}`}
              >
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-center space-x-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{commande.table_numero}</h3>
                      <p className="text-sm text-gray-600">Commandé à {commande.heure_commande}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4 text-gray-500" />
                      <span className={`text-sm font-medium ${commande.temps_ecoule > 20 ? 'text-red-600' : commande.temps_ecoule > 15 ? 'text-orange-600' : 'text-green-600'}`}>
                        {commande.temps_ecoule} min
                      </span>
                    </div>
                    {(commande.priorite === 'urgente' || commande.temps_ecoule > 20) && (
                      <div className="flex items-center space-x-1 bg-red-100 text-red-800 px-2 py-1 rounded-full">
                        <Bell className="w-3 h-3" />
                        <span className="text-xs font-medium">URGENT</span>
                      </div>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    {commande.statut === 'en_attente' && (
                      <button
                        onClick={() => updateCommandeStatut(commande.commande_id, 'en_cours')}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                      >
                        <Play className="w-4 h-4 mr-1" />
                        Commencer
                      </button>
                    )}
                    {commande.statut === 'en_cours' && (
                      <button
                        onClick={() => updateCommandeStatut(commande.commande_id, 'pret')}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircle className="w-4 h-4 mr-1" />
                        Marquer prêt
                      </button>
                    )}
                  </div>
                </div>

                {/* Items de la commande */}
                <div className="space-y-2">
                  {commande.items.map((item) => (
                    <div key={item.item_id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <span className="font-medium text-gray-900">{item.quantite}x</span>
                        <span className="text-gray-900">{item.nom}</span>
                        {item.instructions && (
                          <span className="text-sm text-gray-600 italic">({item.instructions})</span>
                        )}
                        {item.temps_preparation && (
                          <span className="text-xs text-gray-500">~{item.temps_preparation}min</span>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatutColor(item.statut)}`}>
                          {getStatutIcon(item.statut)}
                          <span className="ml-1 capitalize">{item.statut.replace('_', ' ')}</span>
                        </span>
                        {item.statut === 'en_attente' && (
                          <button
                            onClick={() => updateItemStatut(commande.commande_id, item.item_id, 'en_cours')}
                            className="text-blue-600 hover:text-blue-800 text-sm"
                          >
                            Commencer
                          </button>
                        )}
                        {item.statut === 'en_cours' && (
                          <button
                            onClick={() => updateItemStatut(commande.commande_id, item.item_id, 'pret')}
                            className="text-green-600 hover:text-green-800 text-sm"
                          >
                            Prêt
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default KitchenInterface;
