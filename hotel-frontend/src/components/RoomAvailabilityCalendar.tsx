import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import { disponibiliteService, DisponibiliteResponse } from '../services/disponibilite.service';
import { reservationService } from '../services/reservation.service';
import { motion } from 'framer-motion';
import { ReservationTimeDialog } from './ReservationTimeDialog';
import { Chambre } from '../services/chambre.service';

interface RoomAvailabilityCalendarProps {
  room: Chambre;
  onClose: () => void;
}

export function RoomAvailabilityCalendar({ room, onClose }: RoomAvailabilityCalendarProps) {
  const navigate = useNavigate();
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [availability, setAvailability] = useState<DisponibiliteResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [startHour, setStartHour] = useState<number | null>(null);
  const [endHour, setEndHour] = useState<number | null>(null);
  const [isReservationDialogOpen, setIsReservationDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchAvailability();
  }, [selectedDate]);

  const fetchAvailability = async () => {
    try {
      setLoading(true);
      const dateStr = selectedDate.toISOString().split('T')[0];
      const response = await disponibiliteService.getDisponibilitesChambre(room.chambre_id.toString(), {
        date_debut: dateStr,
        date_fin: dateStr
      });
      setAvailability(response);
    } catch (error) {
      console.error('Erreur lors du chargement des disponibilités:', error);
      toast.error('Erreur lors du chargement des disponibilités');
      setAvailability(null);
    } finally {
      setLoading(false);
    }
  };

  const handleHourClick = (hour: number) => {
    const disponibilite = getAvailabilityForHour(hour);
    
    if (!disponibilite || disponibilite.statut !== 'disponible') {
      toast.warning('Cette plage horaire n\'est pas disponible');
      return;
    }

    if (startHour === null) {
      setStartHour(hour);
      setEndHour(null);
      return;
    }

    if (endHour === null) {
      if (hour <= startHour) {
        toast.warning('L\'heure de fin doit être après l\'heure de début');
        return;
      }

      let allHoursAvailable = true;
      for (let h = startHour + 1; h < hour; h++) {
        const disponibilite = getAvailabilityForHour(h);
        if (!disponibilite || disponibilite.statut !== 'disponible') {
          allHoursAvailable = false;
          break;
        }
      }

      if (!allHoursAvailable) {
        toast.warning('Toutes les heures de la plage doivent être disponibles');
        return;
      }

      setEndHour(hour);
    } else {
      setStartHour(hour);
      setEndHour(null);
    }
  };

  const getAvailabilityForHour = (hour: number) => {
    if (!availability?.data?.disponibilites?.[0]?.chambres) {
      return null;
    }
    
    const chambre = availability.data.disponibilites[0].chambres.find(
      (c) => c.chambre_id.toString() === room.chambre_id.toString()
    );
    
    if (!chambre) {
      return null;
    }

    // Si la chambre n'est pas disponible globalement
    if (!chambre.disponible) {
      // Vérifier si l'heure actuelle est dans une plage d'occupation
      const plage = chambre.plages_occupation.find(p => {
        const debut = parseInt(p.heure_debut.split(':')[0]);
        const fin = parseInt(p.heure_fin.split(':')[0]);
        return hour >= debut && hour < fin;
      });

      if (plage) {
        return {
          statut: plage.statut,
          plages_occupation: [plage]
        };
      }

      // Si l'heure n'est pas dans une plage d'occupation, la chambre est disponible
      return {
        statut: 'disponible',
        plages_occupation: []
      };
    }

    // Si la chambre est disponible globalement
    const plage = chambre.plages_occupation.find(p => {
      const debut = parseInt(p.heure_debut.split(':')[0]);
      const fin = parseInt(p.heure_fin.split(':')[0]);
      return hour >= debut && hour < fin;
    });

    if (plage) {
      return {
        statut: plage.statut,
        plages_occupation: [plage]
      };
    }

    return {
      statut: 'disponible',
      plages_occupation: []
    };
  };

  const getAvailabilityColor = (hour: number) => {
    if (startHour !== null && endHour !== null && hour >= startHour && hour < endHour) {
      return 'bg-blue-200';
    }

    if (hour === startHour) {
      return 'bg-blue-300';
    }

    const disponibilite = getAvailabilityForHour(hour);
    if (!disponibilite) return 'bg-gray-100';
    
    switch (disponibilite.statut) {
      case 'disponible':
        return 'bg-green-100';
      case 'bloquee':
        return 'bg-red-100';
      case 'en_attente':
        return 'bg-yellow-100';
      case 'expiree':
        return 'bg-orange-100';
      default:
        return 'bg-gray-100';
    }
  };

  const getAvailabilityStatus = (hour: number) => {
    const disponibilite = getAvailabilityForHour(hour);
    if (!disponibilite) return 'Non disponible';
    
    const plage = disponibilite.plages_occupation.find(p => {
      const debut = parseInt(p.heure_debut.split(':')[0]);
      const fin = parseInt(p.heure_fin.split(':')[0]);
      return hour >= debut && hour < fin;
    });

    if (plage) {
      if (plage.statut === 'en_attente') {
        return `En attente (${plage.statut === 'en_attente' ? 'Demande' : 'Confirmée'})`;
      }
      return plage.statut.charAt(0).toUpperCase() + plage.statut.slice(1);
    }
    
    return disponibilite.statut.charAt(0).toUpperCase() + disponibilite.statut.slice(1);
  };

  const handleReservation = () => {
    if (startHour === null || endHour === null) {
      toast.warning('Veuillez sélectionner une plage horaire');
      return;
    }
    console.log('Ouverture du dialogue de réservation avec:', {
      startHour,
      endHour,
      room
    });
    setIsReservationDialogOpen(true);
  };

  const handleCreateReservation = async (clientInfo: { nom: string; prenom: string; telephone: string }) => {
    console.log('handleCreateReservation appelé avec:', clientInfo);
    console.log('Heures sélectionnées:', { startHour, endHour });
    
    if (startHour === null || endHour === null) {
      console.error('startHour ou endHour est null:', { startHour, endHour });
      toast.error('Veuillez sélectionner une plage horaire valide');
      return;
    }

    try {
      setIsSubmitting(true);
      console.log('Début de la création de la demande de réservation', {
        clientInfo,
        startHour,
        endHour,
        room
      });

      const heureDebut = `${startHour.toString().padStart(2, '0')}:00`;
      const heureFin = `${endHour.toString().padStart(2, '0')}:00`;
      const dateStr = selectedDate.toISOString().split('T')[0];
      const nombreHeures = endHour - startHour;
      const prixTotal = room.prix_base * nombreHeures;

      const reservationParams = {
        client_info: clientInfo,
        complexe_id: room.complexe_id,
        date_arrivee: dateStr,
        date_depart: dateStr,
        heure_debut: heureDebut,
        heure_fin: heureFin,
        chambres: [{
          chambre_id: room.chambre_id,
          type_chambre: room.type_chambre,
          prix_nuit: room.prix_base
        }],
        prix_total: prixTotal,
        commentaires: `Demande de réservation pour la chambre ${room.numero} - Type: ${room.type_chambre}`
      };

      const response = await reservationService.createDemandeReservation(reservationParams);
      console.log('Réponse du serveur:', response);

      if (!response.success) {
        throw new Error(response.message || 'Erreur lors de la création de la demande de réservation');
      }

      // Enrichir la réponse du serveur avec les informations de la chambre et du client
      const enrichedReservation = {
        ...response.data,
        chambres: [{
          chambre_id: room.chambre_id,
          numero: room.numero,
          type_chambre: room.type_chambre,
          prix_nuit: room.prix_base
        }],
        client: {
          nom: clientInfo.nom,
          prenom: clientInfo.prenom,
          telephone: clientInfo.telephone,
          email: '' // L'email n'est pas demandé dans le formulaire
        }
      };

      // Rediriger vers la page de confirmation avec les données enrichies
      navigate('/reservation-confirmation', {
        state: { reservation: enrichedReservation }
      });
      
      setIsReservationDialogOpen(false);
      setStartHour(null);
      setEndHour(null);
      onClose();
    } catch (error) {
      console.error('Erreur détaillée lors de la création de la demande de réservation:', error);
      if (error instanceof Error) {
        toast.error(`Erreur: ${error.message}`);
      } else {
        toast.error('Une erreur est survenue lors de la création de la demande de réservation');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const hours = Array.from({ length: 24 }, (_, i) => i);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto"
      >
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-semibold text-gray-900">
              Disponibilité - Chambre {room.numero}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="mb-6">
            <div className="flex items-center justify-between gap-4">
              <input
                type="date"
                value={selectedDate.toISOString().split('T')[0]}
                onChange={(e) => setSelectedDate(new Date(e.target.value))}
                className="px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                onClick={handleReservation}
                disabled={startHour === null || endHour === null}
                className={`px-4 py-2 rounded-md ${
                  startHour === null || endHour === null
                    ? 'bg-gray-300 cursor-not-allowed'
                    : 'bg-blue-500 hover:bg-blue-600 text-white'
                }`}
              >
                Demander une réservation
              </button>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-24 gap-1 mb-6">
                {hours.map((hour) => (
                  <div
                    key={hour}
                    onClick={() => handleHourClick(hour)}
                    className={`p-2 rounded ${getAvailabilityColor(hour)} cursor-pointer hover:opacity-80 transition-opacity`}
                    title={`${hour}:00 - ${getAvailabilityStatus(hour)}`}
                  >
                    {hour}:00
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </motion.div>

      <ReservationTimeDialog
        isOpen={isReservationDialogOpen}
        onClose={() => {
          console.log('Fermeture du dialogue, heures actuelles:', { startHour, endHour });
          setIsReservationDialogOpen(false);
          // Ne pas réinitialiser les heures ici pour éviter de les perdre
        }}
        room={room}
        date={selectedDate}
        startHour={startHour!}
        endHour={endHour!}
        onSubmit={handleCreateReservation}
        isSubmitting={isSubmitting}
      />
    </motion.div>
  );
} 