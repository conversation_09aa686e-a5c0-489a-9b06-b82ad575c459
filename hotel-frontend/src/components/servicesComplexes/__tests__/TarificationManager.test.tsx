import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { TarificationManager } from '../TarificationManager';
import { serviceComplexeService } from '../../../services/service.service';

// Mock du service
jest.mock('../../../services/service.service', () => ({
  serviceComplexeService: {
    getTarificationTemplate: jest.fn(),
  },
}));

// Mock de react-hot-toast
jest.mock('react-hot-toast', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

const mockServiceComplexeService = serviceComplexeService as jest.Mocked<typeof serviceComplexeService>;

describe('TarificationManager', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Restaurant Tarification', () => {
    beforeEach(() => {
      mockServiceComplexeService.getTarificationTemplate.mockResolvedValue({
        type_service: 'Restaurant',
        template: {
          menus: {
            'Menu du jour': 25.00,
            'Menu enfant': 15.00,
          },
          boissons: {
            'Eau plate': 3.00,
            'Soda': 4.00,
          },
          service_table: 2.00,
          couvert_par_personne: 1.50,
        },
      });
    });

    test('should render restaurant tarification interface', async () => {
      render(
        <TarificationManager
          typeService="Restaurant"
          onChange={mockOnChange}
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Tarification - Restaurant')).toBeInTheDocument();
      });

      // Vérifier que les onglets sont présents
      expect(screen.getByText('Menus')).toBeInTheDocument();
      expect(screen.getByText('Boissons')).toBeInTheDocument();
    });

    test('should load template when "Utiliser le modèle" is clicked', async () => {
      render(
        <TarificationManager
          typeService="Restaurant"
          onChange={mockOnChange}
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Utiliser le modèle')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('Utiliser le modèle'));

      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith({
          menus: {
            'Menu du jour': 25.00,
            'Menu enfant': 15.00,
          },
          boissons: {
            'Eau plate': 3.00,
            'Soda': 4.00,
          },
          service_table: 2.00,
          couvert_par_personne: 1.50,
        });
      });
    });

    test('should update service table price', async () => {
      render(
        <TarificationManager
          typeService="Restaurant"
          onChange={mockOnChange}
        />
      );

      await waitFor(() => {
        expect(screen.getByLabelText('Service de table (FCFA)')).toBeInTheDocument();
      });

      const serviceTableInput = screen.getByLabelText('Service de table (FCFA)');
      fireEvent.change(serviceTableInput, { target: { value: '3.50' } });

      expect(mockOnChange).toHaveBeenCalledWith({
        service_table: 3.50,
      });
    });
  });

  describe('Bar Tarification', () => {
    beforeEach(() => {
      mockServiceComplexeService.getTarificationTemplate.mockResolvedValue({
        type_service: 'Bar',
        template: {
          alcools: {
            'Bière': 5.00,
            'Vin rouge': 7.00,
          },
          soft_drinks: {
            'Coca-Cola': 4.00,
          },
          cocktails: {
            'Mojito': 12.00,
          },
          happy_hour: {
            reduction_pourcentage: 20,
            heures: ['17:00-19:00'],
          },
        },
      });
    });

    test('should render bar tarification interface', async () => {
      render(
        <TarificationManager
          typeService="Bar"
          onChange={mockOnChange}
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Tarification - Bar')).toBeInTheDocument();
      });

      // Vérifier que les onglets sont présents
      expect(screen.getByText('Alcools')).toBeInTheDocument();
      expect(screen.getByText('Boissons sans alcool')).toBeInTheDocument();
      expect(screen.getByText('Cocktails')).toBeInTheDocument();
    });

    test('should update happy hour settings', async () => {
      render(
        <TarificationManager
          typeService="Bar"
          onChange={mockOnChange}
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Happy Hour')).toBeInTheDocument();
      });

      const reductionInput = screen.getByLabelText('Réduction (%)');
      fireEvent.change(reductionInput, { target: { value: '25' } });

      expect(mockOnChange).toHaveBeenCalledWith({
        happy_hour: {
          reduction_pourcentage: 25,
          heures: [],
        },
      });
    });
  });

  describe('Piscine Tarification', () => {
    beforeEach(() => {
      mockServiceComplexeService.getTarificationTemplate.mockResolvedValue({
        type_service: 'Piscine',
        template: {
          prix_par_personne: 10.00,
          prix_par_heure: 5.00,
          prix_forfaitaire: 25.00,
          tarifs_age: {
            'Enfant': 5.00,
            'Adulte': 10.00,
          },
          tarifs_duree: {
            '1 heure': 10.00,
            'Journée': 40.00,
          },
        },
      });
    });

    test('should render piscine tarification interface', async () => {
      render(
        <TarificationManager
          typeService="Piscine"
          onChange={mockOnChange}
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Tarification - Piscine')).toBeInTheDocument();
      });

      // Vérifier que les onglets sont présents
      expect(screen.getByText('Tarifs de base')).toBeInTheDocument();
      expect(screen.getByText('Tarifs par âge')).toBeInTheDocument();
      expect(screen.getByText('Tarifs par durée')).toBeInTheDocument();
    });

    test('should update base prices', async () => {
      render(
        <TarificationManager
          typeService="Piscine"
          onChange={mockOnChange}
        />
      );

      await waitFor(() => {
        expect(screen.getByLabelText('Prix par personne (FCFA)')).toBeInTheDocument();
      });

      const prixPersonneInput = screen.getByLabelText('Prix par personne (FCFA)');
      fireEvent.change(prixPersonneInput, { target: { value: '12.00' } });

      expect(mockOnChange).toHaveBeenCalledWith({
        prix_par_personne: 12.00,
      });
    });
  });

  describe('TarificationSection Component', () => {
    test('should add new item when "Ajouter un élément" is clicked', async () => {
      render(
        <TarificationManager
          typeService="Restaurant"
          onChange={mockOnChange}
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Menus')).toBeInTheDocument();
      });

      // Cliquer sur l'onglet Menus
      fireEvent.click(screen.getByText('Menus'));

      await waitFor(() => {
        expect(screen.getByText('+ Ajouter un élément')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('+ Ajouter un élément'));

      // Vérifier qu'un nouveau champ d'entrée est ajouté
      const nameInputs = screen.getAllByPlaceholderText('Nom de l\'élément');
      expect(nameInputs.length).toBeGreaterThan(0);
    });

    test('should remove item when delete button is clicked', async () => {
      const initialTarification = {
        menus: {
          'Menu test': 20.00,
        },
      };

      render(
        <TarificationManager
          typeService="Restaurant"
          initialTarification={initialTarification}
          onChange={mockOnChange}
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Menus')).toBeInTheDocument();
      });

      // Cliquer sur l'onglet Menus
      fireEvent.click(screen.getByText('Menus'));

      await waitFor(() => {
        const deleteButtons = screen.getAllByText('×');
        expect(deleteButtons.length).toBeGreaterThan(0);
      });

      const deleteButton = screen.getAllByText('×')[0];
      fireEvent.click(deleteButton);

      expect(mockOnChange).toHaveBeenCalledWith({
        menus: {},
      });
    });
  });

  describe('Error Handling', () => {
    test('should handle template loading error', async () => {
      mockServiceComplexeService.getTarificationTemplate.mockRejectedValue(
        new Error('Network error')
      );

      render(
        <TarificationManager
          typeService="Restaurant"
          onChange={mockOnChange}
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Tarification - Restaurant')).toBeInTheDocument();
      });

      // Le bouton "Utiliser le modèle" ne devrait pas être présent en cas d'erreur
      expect(screen.queryByText('Utiliser le modèle')).not.toBeInTheDocument();
    });
  });

  describe('Disabled State', () => {
    test('should disable all inputs when disabled prop is true', async () => {
      render(
        <TarificationManager
          typeService="Restaurant"
          onChange={mockOnChange}
          disabled={true}
        />
      );

      await waitFor(() => {
        expect(screen.getByLabelText('Service de table (FCFA)')).toBeDisabled();
      });
    });
  });
});
