import React from 'react';
import Modal from '../ui/Modal';
import Button from '../ui/Button';
import { Service } from '../../types';
import { formatDate, getServiceTypeLabel } from '../../lib/utils';
import { serviceComplexeService } from '../../services';
import * as LucideIcons from 'lucide-react';

interface ServiceDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  service: Service;
}

const ServiceDetailsModal: React.FC<ServiceDetailsModalProps> = ({
  isOpen,
  onClose,
  service
}) => {
  // Format horaires_ouverture for display
  const formatHoraires = (horaires: Record<string, any>) => {
    if (!horaires || typeof horaires !== 'object') {
      return <p className="text-gray-500">Horaires non disponibles</p>;
    }

    const days = [
      { key: 'lundi', label: '<PERSON><PERSON>' },
      { key: 'mardi', label: '<PERSON><PERSON>' },
      { key: 'mercredi', label: 'Mercredi' },
      { key: 'jeudi', label: '<PERSON><PERSON>' },
      { key: 'vendredi', label: 'Vendredi' },
      { key: 'samedi', label: 'Samedi' },
      { key: 'dimanche', label: 'Dimanche' },
    ];

    return (
      <div className="grid grid-cols-2 gap-2">
        {days.map((day) => {
          const dayHours = horaires[day.key];
          return (
            <div key={day.key} className="flex justify-between text-sm">
              <span className="font-medium">{day.label}:</span>
              <span className="text-gray-600">
                {dayHours ? `${dayHours.ouverture} - ${dayHours.fermeture}` : 'Fermé'}
              </span>
            </div>
          );
        })}
      </div>
    );
  };

  // Format configuration and tarification as a list of key-value pairs
  const formatJsonData = (data: Record<string, any>) => {
    if (!data || typeof data !== 'object' || Object.keys(data).length === 0) {
      return <p className="text-gray-500">Aucune donnée disponible</p>;
    }

    const formatValue = (value: any): React.ReactNode => {
      if (typeof value === 'boolean') {
        return value ? 'Oui' : 'Non';
      } else if (Array.isArray(value)) {
        return value.join(', ');
      } else if (typeof value === 'object' && value !== null) {
        return (
          <div className="pl-4 border-l-2 border-gray-200 mt-1">
            {Object.entries(value).map(([k, v]) => (
              <div key={k} className="flex justify-between text-sm my-1">
                <span className="font-medium capitalize">{k.replace(/_/g, ' ')}:</span>
                <span className="text-gray-600">{formatValue(v)}</span>
              </div>
            ))}
          </div>
        );
      } else {
        return String(value);
      }
    };

    return (
      <div className="space-y-2">
        {Object.entries(data).map(([key, value]) => (
          <div key={key}>
            <div className="flex justify-between text-sm">
              <span className="font-medium capitalize">{key.replace(/_/g, ' ')}:</span>
              <span className="text-gray-600">{formatValue(value)}</span>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Format tarification data using the service method
  const formatTarificationData = (data: Record<string, any>, typeService: string) => {
    if (!data || typeof data !== 'object' || Object.keys(data).length === 0) {
      return <p className="text-gray-500">Aucune tarification définie</p>;
    }

    try {
      const formattedItems = serviceComplexeService.formatTarificationForDisplay(
        typeService as any,
        data
      );

      if (formattedItems.length === 0) {
        return <p className="text-gray-500">Aucune tarification valide</p>;
      }

      return (
        <div className="space-y-2">
          {formattedItems.map((item, index) => (
            <div key={index} className="flex justify-between text-sm">
              <span className="font-medium">{item.label}:</span>
              <span className="text-gray-600 font-semibold">{item.value}</span>
            </div>
          ))}
        </div>
      );
    } catch (error) {
      console.error('Erreur lors du formatage de la tarification:', error);
      return <p className="text-red-500">Erreur lors de l'affichage de la tarification</p>;
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={service.nom}
      size="lg"
      footer={
        <Button variant="outline" onClick={onClose}>
          Fermer
        </Button>
      }
    >
      <div className="space-y-6">
        <div className="relative h-48 overflow-hidden rounded-md">
          <img 
            src={service.image_url || 'https://images.pexels.com/photos/271624/pexels-photo-271624.jpeg'}
            alt={service.nom}
            className="w-full h-full object-cover"
          />
          <div className="absolute top-0 right-0 m-2">
            <span className={`inline-block px-2 py-1 rounded text-xs font-bold ${
              service.actif 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {service.actif ? 'ACTIF' : 'INACTIF'}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-semibold mb-2">Informations générales</h3>
            <dl className="space-y-2">
              <div className="flex justify-between">
                <dt className="text-sm font-medium text-gray-500">Type:</dt>
                <dd className="text-sm text-gray-900">{getServiceTypeLabel(service.type_service)}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Description:</dt>
                <dd className="text-sm text-gray-900 mt-1">{service.description || 'Non spécifiée'}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-sm font-medium text-gray-500">Emplacement:</dt>
                <dd className="text-sm text-gray-900">{service.emplacement || 'Non spécifié'}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-sm font-medium text-gray-500">Capacité:</dt>
                <dd className="text-sm text-gray-900">{service.capacite ? `${service.capacite} personnes` : 'Non spécifiée'}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-sm font-medium text-gray-500">Email:</dt>
                <dd className="text-sm text-gray-900">{service.contact_email || 'Non spécifié'}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-sm font-medium text-gray-500">Téléphone:</dt>
                <dd className="text-sm text-gray-900">{service.contact_telephone || 'Non spécifié'}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-sm font-medium text-gray-500">Créé le:</dt>
                <dd className="text-sm text-gray-900">{formatDate(service.created_at)}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-sm font-medium text-gray-500">Mis à jour le:</dt>
                <dd className="text-sm text-gray-900">{formatDate(service.updated_at)}</dd>
              </div>
            </dl>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Horaires d'ouverture</h3>
            <div className="bg-gray-50 p-3 rounded-md">
              {formatHoraires(service.horaires_ouverture || {})}
            </div>

            <h3 className="text-lg font-semibold mb-2 mt-4">Configuration</h3>
            <div className="bg-gray-50 p-3 rounded-md">
              {formatJsonData(service.configuration || {})}
            </div>

            <h3 className="text-lg font-semibold mb-2 mt-4">Tarification</h3>
            <div className="bg-gray-50 p-3 rounded-md">
              {formatTarificationData(service.tarification || {}, service.type_service)}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default ServiceDetailsModal;