import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import Modal from '../ui/Modal';
import Button from '../ui/Button';
import { Service, ServiceType, SERVICE_TYPES } from '../../types';
import { serviceComplexeService, TarificationData } from '../../services';
import { TarificationManager } from './TarificationManager';
import { MenuUploader, IngredientUploader } from '../upload';
import { authService } from '../../services/auth.service';
import toast from 'react-hot-toast';

interface ServiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  service?: Service;
  onServiceCreated: () => void;
}

type FormValues = Omit<Service, 'service_id' | 'created_at' | 'updated_at' | 'complexe_id'>;

const defaultValues: Partial<FormValues> = {
  type_service: 'Restaurant',
  nom: '',
  description: '',
  emplacement: '',
  horaires_ouverture: {
    lundi: { ouverture: '08:00', fermeture: '18:00' },
    mardi: { ouverture: '08:00', fermeture: '18:00' },
    mercredi: { ouverture: '08:00', fermeture: '18:00' },
    jeudi: { ouverture: '08:00', fermeture: '18:00' },
    vendredi: { ouverture: '08:00', fermeture: '18:00' },
    samedi: { ouverture: '09:00', fermeture: '17:00' },
    dimanche: { ouverture: '09:00', fermeture: '17:00' },
  },
  capacite: 50,
  image_url: '',
  contact_email: '',
  contact_telephone: '',
  actif: true,
  configuration: {},
  tarification: {},
};

const ServiceModal: React.FC<ServiceModalProps> = ({
  isOpen,
  onClose,
  service,
  onServiceCreated
}) => {
  const isEditing = !!service;
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'general' | 'horaires' | 'configuration' | 'tarification' | 'import'>('general');
  const [tarificationData, setTarificationData] = useState<TarificationData>(service?.tarification || {});

  const { register, handleSubmit, reset, formState: { errors }, setValue, watch } = useForm<FormValues>({
    defaultValues: service || defaultValues,
  });

  const selectedType = watch('type_service');
  
  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    try {
      setLoading(true);

      // Inclure les données de tarification
      const serviceData = {
        ...data,
        tarification: tarificationData
      };

      if (isEditing && service) {
        await serviceComplexeService.updateService(service.service_id, serviceData);
        toast.success('Service mis à jour avec succès');
      } else {
        await serviceComplexeService.createService(serviceData);
        toast.success('Service créé avec succès');
      }

      onServiceCreated();
      onClose();
      reset();
      setTarificationData({});
    } catch (error) {
      toast.error('Erreur lors de l\'enregistrement du service');
      console.error('Service save error:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Cancel form submission
  const handleCancel = () => {
    reset();
    setTarificationData({});
    onClose();
  };

  // Handle tarification change
  const handleTarificationChange = (newTarification: TarificationData) => {
    setTarificationData(newTarification);
  };

  // Form sections
  const generalSection = (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label htmlFor="nom" className="block text-sm font-medium text-gray-700">
            Nom du service <span className="text-red-500">*</span>
          </label>
          <input
            id="nom"
            type="text"
            {...register('nom', { required: 'Le nom est requis' })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
          />
          {errors.nom && (
            <p className="text-red-500 text-xs">{errors.nom.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <label htmlFor="type_service" className="block text-sm font-medium text-gray-700">
            Type de service <span className="text-red-500">*</span>
          </label>
          <select
            id="type_service"
            {...register('type_service', { required: 'Le type est requis' })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
          >
            {SERVICE_TYPES.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="space-y-2">
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
          Description
        </label>
        <textarea
          id="description"
          rows={3}
          {...register('description')}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
        />
        {errors.description && (
          <p className="text-red-500 text-xs">{errors.description.message}</p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label htmlFor="emplacement" className="block text-sm font-medium text-gray-700">
            Emplacement
          </label>
          <input
            id="emplacement"
            type="text"
            {...register('emplacement')}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
          />
          {errors.emplacement && (
            <p className="text-red-500 text-xs">{errors.emplacement.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <label htmlFor="capacite" className="block text-sm font-medium text-gray-700">
            Capacité
          </label>
          <input
            id="capacite"
            type="number"
            min="1"
            {...register('capacite', {
              min: { value: 1, message: 'La capacité minimum est 1' },
              valueAsNumber: true
            })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
          />
          {errors.capacite && (
            <p className="text-red-500 text-xs">{errors.capacite.message}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <label htmlFor="image_url" className="block text-sm font-medium text-gray-700">
          URL de l'image
        </label>
        <input
          id="image_url"
          type="url"
          {...register('image_url')}
          placeholder="https://example.com/image.jpg"
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
        />
        {watch('image_url') && (
          <div className="mt-2">
            <p className="text-xs text-gray-500 mb-1">Aperçu :</p>
            <img 
              src={watch('image_url')} 
              alt="Aperçu" 
              className="h-20 w-auto object-cover rounded-md"
              onError={(e) => {
                (e.target as HTMLImageElement).src = 'https://images.pexels.com/photos/271624/pexels-photo-271624.jpeg';
              }}
            />
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label htmlFor="contact_email" className="block text-sm font-medium text-gray-700">
            Email de contact
          </label>
          <input
            id="contact_email"
            type="email"
            {...register('contact_email', {
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Adresse email invalide',
              },
            })}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
          />
          {errors.contact_email && (
            <p className="text-red-500 text-xs">{errors.contact_email.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <label htmlFor="contact_telephone" className="block text-sm font-medium text-gray-700">
            Téléphone de contact
          </label>
          <input
            id="contact_telephone"
            type="tel"
            {...register('contact_telephone')}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
          />
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <input
          id="actif"
          type="checkbox"
          {...register('actif')}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label htmlFor="actif" className="text-sm font-medium text-gray-700">
          Service actif
        </label>
      </div>
    </div>
  );

  const horairesSection = (
    <div className="space-y-4">
      <div className="bg-gray-50 p-4 rounded-md mb-4">
        <h3 className="text-sm font-medium text-gray-700 mb-2">Horaires d'ouverture</h3>
        <p className="text-xs text-gray-500 mb-4">
          Définissez les horaires d'ouverture pour chaque jour de la semaine
        </p>

        <div className="space-y-4">
          {['lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi', 'dimanche'].map((day) => (
            <div key={day} className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-xs font-medium text-gray-700 capitalize mb-1">
                  {day} - Ouverture
                </label>
                <input
                  type="time"
                  {...register(`horaires_ouverture.${day}.ouverture` as any)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 capitalize mb-1">
                  {day} - Fermeture
                </label>
                <input
                  type="time"
                  {...register(`horaires_ouverture.${day}.fermeture` as any)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const configurationSection = (
    <div className="space-y-4">
      {/* Configuration dynamique en fonction du type de service */}
      {selectedType === 'Restaurant' && (
        <div className="bg-gray-50 p-4 rounded-md">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Configuration restaurant</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Types de cuisine (séparés par des virgules)
              </label>
              <input
                type="text"
                placeholder="Française, Italienne, etc."
                {...register('configuration.cuisine' as any)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
              />
            </div>
            
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Services proposés (séparés par des virgules)
              </label>
              <input
                type="text"
                placeholder="Petit-déjeuner, Déjeuner, Dîner"
                {...register('configuration.services' as any)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <input
                id="reservation"
                type="checkbox"
                {...register('configuration.reservation' as any)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="reservation" className="text-sm font-medium text-gray-700">
                Réservation possible
              </label>
            </div>
          </div>
        </div>
      )}
      
      {selectedType === 'Bar' && (
        <div className="bg-gray-50 p-4 rounded-md">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Configuration bar</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Spécialités (séparées par des virgules)
              </label>
              <input
                type="text"
                placeholder="Cocktails, Vins, Bières"
                {...register('configuration.specialites' as any)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
              />
            </div>
            
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Ambiance
              </label>
              <select
                {...register('configuration.ambiance' as any)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
              >
                <option value="Lounge">Lounge</option>
                <option value="Sport">Sport</option>
                <option value="Club">Club</option>
                <option value="Classique">Classique</option>
                <option value="Autre">Autre</option>
              </select>
            </div>
            
            <div className="flex items-center space-x-2">
              <input
                id="musique_live"
                type="checkbox"
                {...register('configuration.musique_live' as any)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="musique_live" className="text-sm font-medium text-gray-700">
                Musique live
              </label>
            </div>
          </div>
        </div>
      )}
      
      {/* Autres types de services... */}
    </div>
  );

  const tarificationSection = (
    <div className="space-y-4">
      <TarificationManager
        typeService={selectedType}
        initialTarification={tarificationData}
        onChange={handleTarificationChange}
        disabled={loading}
      />
    </div>
  );



  const importSection = (
    <div className="space-y-6">
      {!service ? (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Service non créé</h3>
          <p className="text-gray-500 text-sm">
            Veuillez d'abord créer le service pour importer le menu et les ingrédients.
          </p>
        </div>
      ) : selectedType === 'Piscine' ? (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Import non disponible</h3>
          <p className="text-gray-500 text-sm">
            L'import de menu n'est pas disponible pour les services de type Piscine.
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Import des ingrédients/inventaire */}
          <div className="border rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              1. Importer {selectedType === 'Restaurant' ? 'les Ingrédients Cuisine' : 'l\'Inventaire Boissons'}
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              {selectedType === 'Restaurant'
                ? 'Uploadez votre liste d\'ingrédients pour la cuisine'
                : 'Uploadez votre inventaire de boissons'
              }
            </p>
            <IngredientUploader
              complexeId={authService.getComplexeId() || 0}
              type={selectedType === 'Restaurant' ? 'cuisine' : 'boissons'}
              onUploadComplete={() => {
                toast.success(`${selectedType === 'Restaurant' ? 'Ingrédients' : 'Inventaire boissons'} importé avec succès`);
              }}
              onError={(error) => {
                toast.error(`Erreur lors de l'import: ${error}`);
              }}
            />
          </div>

          {/* Import du menu/carte */}
          <div className="border rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              2. Importer {selectedType === 'Restaurant' ? 'le Menu' : 'la Carte'}
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              {selectedType === 'Restaurant'
                ? 'Uploadez votre menu avec tous les plats'
                : 'Uploadez votre carte avec toutes les boissons'
              }
            </p>
            <MenuUploader
              serviceId={service.service_id}
              serviceType={selectedType as 'Restaurant' | 'Bar'}
              onUploadComplete={() => {
                toast.success(`${selectedType === 'Restaurant' ? 'Menu' : 'Carte'} importé avec succès`);
                onServiceCreated(); // Refresh la liste des services
              }}
              onError={(error) => {
                toast.error(`Erreur lors de l'import: ${error}`);
              }}
            />
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-800 mb-2">Instructions d'import</h4>
            <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
              <li>Commencez par importer {selectedType === 'Restaurant' ? 'les ingrédients' : 'l\'inventaire boissons'}</li>
              <li>Puis importez {selectedType === 'Restaurant' ? 'le menu' : 'la carte'}</li>
              <li>Téléchargez les templates pour voir le format attendu</li>
              <li>Vérifiez les données avant l'import final</li>
            </ol>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={isEditing ? `Modifier ${service?.nom}` : 'Ajouter un service'}
      size="xl"
      footer={
        <>
          <Button variant="outline" onClick={handleCancel}>
            Annuler
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit(onSubmit)}
            loading={loading}
          >
            {isEditing ? 'Mettre à jour' : 'Créer'}
          </Button>
        </>
      }
    >
      <div className="mb-6">
        <div className="flex border-b border-gray-200">
          <button
            className={`px-4 py-2 text-sm font-medium transition-colors duration-200 ${
              activeTab === 'general'
                ? 'border-b-2 border-blue-500 text-blue-700'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('general')}
          >
            Général
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium transition-colors duration-200 ${
              activeTab === 'horaires'
                ? 'border-b-2 border-blue-500 text-blue-700'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('horaires')}
          >
            Horaires
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium transition-colors duration-200 ${
              activeTab === 'configuration'
                ? 'border-b-2 border-blue-500 text-blue-700'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('configuration')}
          >
            Configuration
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium transition-colors duration-200 ${
              activeTab === 'tarification'
                ? 'border-b-2 border-blue-500 text-blue-700'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('tarification')}
          >
            Tarification
          </button>

          <button
            className={`px-4 py-2 text-sm font-medium transition-colors duration-200 ${
              activeTab === 'import'
                ? 'border-b-2 border-blue-500 text-blue-700'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('import')}
          >
            Import Menu
          </button>
        </div>
      </div>

      {activeTab === 'general' && generalSection}
      {activeTab === 'horaires' && horairesSection}
      {activeTab === 'configuration' && configurationSection}
      {activeTab === 'tarification' && tarificationSection}
      {activeTab === 'import' && importSection}
    </Modal>
  );
};

export default ServiceModal;