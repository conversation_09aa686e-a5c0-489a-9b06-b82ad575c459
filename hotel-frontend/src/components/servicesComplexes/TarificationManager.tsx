import React, { useState, useEffect } from 'react';
import { ServiceType, TarificationData, serviceComplexeService } from '../../services/service.service';
import { toast } from 'react-hot-toast';

interface TarificationManagerProps {
  typeService: ServiceType;
  initialTarification?: TarificationData;
  onChange: (tarification: TarificationData) => void;
  disabled?: boolean;
}

interface TarificationItem {
  key: string;
  label: string;
  value: number;
}

export const TarificationManager: React.FC<TarificationManagerProps> = ({
  typeService,
  initialTarification,
  onChange,
  disabled = false
}) => {
  const [tarification, setTarification] = useState<TarificationData>(initialTarification || {});
  const [template, setTemplate] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('');

  useEffect(() => {
    loadTemplate();
  }, [typeService]);

  useEffect(() => {
    if (initialTarification) {
      setTarification(initialTarification);
    }
  }, [initialTarification]);

  const loadTemplate = async () => {
    try {
      setLoading(true);
      const templateData = await serviceComplexeService.getTarificationTemplate(typeService);
      setTemplate(templateData.template);
      
      // Définir l'onglet actif par défaut selon le type de service
      switch (typeService) {
        case 'Restaurant':
          setActiveTab('menus');
          break;
        case 'Bar':
          setActiveTab('alcools');
          break;
        case 'Piscine':
          setActiveTab('base');
          break;
        default:
          setActiveTab('general');
      }
    } catch (error) {
      console.error('Erreur lors du chargement du template:', error);
      toast.error('Erreur lors du chargement du modèle de tarification');
    } finally {
      setLoading(false);
    }
  };

  const handleUseTemplate = () => {
    if (template) {
      setTarification(template);
      onChange(template);
      toast.success('Modèle de tarification appliqué');
    }
  };

  const updateTarificationSection = (section: string, data: Record<string, number>) => {
    const newTarification = {
      ...tarification,
      [section]: data
    };
    setTarification(newTarification);
    onChange(newTarification);
  };

  const updateTarificationValue = (key: string, value: number) => {
    const newTarification = {
      ...tarification,
      [key]: value
    };
    setTarification(newTarification);
    onChange(newTarification);
  };

  const renderRestaurantTarification = () => {
    const restaurantTarif = tarification as any;
    const tabs = [
      { id: 'menus', label: 'Menus', data: restaurantTarif.menus || {} },
      { id: 'boissons', label: 'Boissons', data: restaurantTarif.boissons || {} }
    ];

    return (
      <div className="space-y-4">
        {/* Onglets */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                disabled={disabled}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Contenu des onglets */}
        {tabs.map((tab) => (
          activeTab === tab.id && (
            <div key={tab.id}>
              <TarificationSection
                title={tab.label}
                data={tab.data}
                onChange={(data) => updateTarificationSection(tab.id, data)}
                disabled={disabled}
              />
            </div>
          )
        ))}

        {/* Frais additionnels */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Service de table (FCFA)
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={restaurantTarif.service_table || ''}
              onChange={(e) => updateTarificationValue('service_table', parseFloat(e.target.value) || 0)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={disabled}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Couvert par personne (FCFA)
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={restaurantTarif.couvert_par_personne || ''}
              onChange={(e) => updateTarificationValue('couvert_par_personne', parseFloat(e.target.value) || 0)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={disabled}
            />
          </div>
        </div>
      </div>
    );
  };

  const renderBarTarification = () => {
    const barTarif = tarification as any;
    const tabs = [
      { id: 'alcools', label: 'Alcools', data: barTarif.alcools || {} },
      { id: 'soft_drinks', label: 'Boissons sans alcool', data: barTarif.soft_drinks || {} },
      { id: 'cocktails', label: 'Cocktails', data: barTarif.cocktails || {} }
    ];

    return (
      <div className="space-y-4">
        {/* Onglets */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                disabled={disabled}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Contenu des onglets */}
        {tabs.map((tab) => (
          activeTab === tab.id && (
            <div key={tab.id}>
              <TarificationSection
                title={tab.label}
                data={tab.data}
                onChange={(data) => updateTarificationSection(tab.id, data)}
                disabled={disabled}
              />
            </div>
          )
        ))}

        {/* Happy Hour */}
        <div className="bg-yellow-50 p-4 rounded-md">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Happy Hour</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Réduction (%)
              </label>
              <input
                type="number"
                min="0"
                max="100"
                value={barTarif.happy_hour?.reduction_pourcentage || ''}
                onChange={(e) => {
                  const newHappyHour = {
                    ...barTarif.happy_hour,
                    reduction_pourcentage: parseInt(e.target.value) || 0,
                    heures: barTarif.happy_hour?.heures || []
                  };
                  updateTarificationValue('happy_hour', newHappyHour);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={disabled}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Heures (ex: 17:00-19:00)
              </label>
              <input
                type="text"
                value={barTarif.happy_hour?.heures?.join(', ') || ''}
                onChange={(e) => {
                  const heures = e.target.value.split(',').map(h => h.trim()).filter(h => h);
                  const newHappyHour = {
                    ...barTarif.happy_hour,
                    reduction_pourcentage: barTarif.happy_hour?.reduction_pourcentage || 0,
                    heures
                  };
                  updateTarificationValue('happy_hour', newHappyHour);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="17:00-19:00, 22:00-24:00"
                disabled={disabled}
              />
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderPiscineTarification = () => {
    const piscineTarif = tarification as any;
    const tabs = [
      { id: 'base', label: 'Tarifs de base' },
      { id: 'age', label: 'Tarifs par âge' },
      { id: 'duree', label: 'Tarifs par durée' }
    ];

    return (
      <div className="space-y-4">
        {/* Onglets */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                disabled={disabled}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Contenu des onglets */}
        {activeTab === 'base' && (
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Prix par personne (FCFA)
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={piscineTarif.prix_par_personne || ''}
                onChange={(e) => updateTarificationValue('prix_par_personne', parseFloat(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={disabled}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Prix par heure (FCFA)
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={piscineTarif.prix_par_heure || ''}
                onChange={(e) => updateTarificationValue('prix_par_heure', parseFloat(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={disabled}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Prix forfaitaire (FCFA)
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={piscineTarif.prix_forfaitaire || ''}
                onChange={(e) => updateTarificationValue('prix_forfaitaire', parseFloat(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={disabled}
              />
            </div>
          </div>
        )}

        {activeTab === 'age' && (
          <TarificationSection
            title="Tarifs par tranche d'âge"
            data={piscineTarif.tarifs_age || {}}
            onChange={(data) => updateTarificationSection('tarifs_age', data)}
            disabled={disabled}
          />
        )}

        {activeTab === 'duree' && (
          <TarificationSection
            title="Tarifs par durée"
            data={piscineTarif.tarifs_duree || {}}
            onChange={(data) => updateTarificationSection('tarifs_duree', data)}
            disabled={disabled}
          />
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header avec bouton template */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">
          Tarification - {typeService}
        </h3>
        {template && !disabled && (
          <button
            onClick={handleUseTemplate}
            className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
          >
            Utiliser le modèle
          </button>
        )}
      </div>

      {/* Contenu selon le type de service */}
      {typeService === 'Restaurant' && renderRestaurantTarification()}
      {typeService === 'Bar' && renderBarTarification()}
      {typeService === 'Piscine' && renderPiscineTarification()}
    </div>
  );
};

// Composant pour gérer une section de tarification (liste d'éléments avec prix)
interface TarificationSectionProps {
  title: string;
  data: Record<string, number>;
  onChange: (data: Record<string, number>) => void;
  disabled?: boolean;
}

const TarificationSection: React.FC<TarificationSectionProps> = ({
  data,
  onChange,
  disabled = false
}) => {
  const [items, setItems] = useState<TarificationItem[]>([]);

  useEffect(() => {
    const itemList = Object.entries(data).map(([key, value]) => ({
      key,
      label: key,
      value: value || 0
    }));
    setItems(itemList);
  }, [data]);

  const addItem = () => {
    const newItems = [...items, { key: '', label: '', value: 0 }];
    setItems(newItems);
  };

  const updateItem = (index: number, field: keyof TarificationItem, value: string | number) => {
    const newItems = [...items];
    newItems[index] = { ...newItems[index], [field]: value };
    setItems(newItems);
    
    // Mettre à jour les données
    const newData: Record<string, number> = {};
    newItems.forEach(item => {
      if (item.label.trim()) {
        newData[item.label] = item.value;
      }
    });
    onChange(newData);
  };

  const removeItem = (index: number) => {
    const newItems = items.filter((_, i) => i !== index);
    setItems(newItems);
    
    // Mettre à jour les données
    const newData: Record<string, number> = {};
    newItems.forEach(item => {
      if (item.label.trim()) {
        newData[item.label] = item.value;
      }
    });
    onChange(newData);
  };

  return (
    <div className="space-y-3">
      {items.map((item, index) => (
        <div key={index} className="flex gap-3 items-center">
          <div className="flex-1">
            <input
              type="text"
              value={item.label}
              onChange={(e) => updateItem(index, 'label', e.target.value)}
              placeholder="Nom de l'élément"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={disabled}
            />
          </div>
          <div className="w-24">
            <input
              type="number"
              step="0.01"
              min="0"
              value={item.value}
              onChange={(e) => updateItem(index, 'value', parseFloat(e.target.value) || 0)}
              placeholder="Prix"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={disabled}
            />
          </div>
          <span className="text-gray-500">FCFA</span>
          {!disabled && (
            <button
              onClick={() => removeItem(index)}
              className="p-2 text-red-600 hover:bg-red-50 rounded-md"
            >
              ×
            </button>
          )}
        </div>
      ))}
      
      {!disabled && (
        <button
          onClick={addItem}
          className="w-full py-2 border-2 border-dashed border-gray-300 rounded-md text-gray-500 hover:border-gray-400 hover:text-gray-600 transition-colors"
        >
          + Ajouter un élément
        </button>
      )}
    </div>
  );
};
