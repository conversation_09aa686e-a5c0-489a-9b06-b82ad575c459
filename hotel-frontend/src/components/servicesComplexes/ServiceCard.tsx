import React, { useState } from 'react';
import { Eye, Edit, Trash2, Power, PowerOff } from 'lucide-react';
import { formatHoraires, getServiceTypeColor, getServiceTypeIcon, getServiceTypeLabel } from '../../lib/utils';
import Button from '../ui/Button';
import { Service } from '../../types';
import ServiceModal from './ServiceModal';
import ConfirmationModal from '../ui/ConfirmationModal';
import ServiceDetailsModal from './ServiceDetailsModal';
import * as LucideIcons from 'lucide-react';

interface ServiceCardProps {
  service: Service;
  onStatusToggle: (id: number, status: boolean) => void;
  onDelete: (id: number) => void;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ 
  service, 
  onStatusToggle,
  onDelete
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Dynamically get the icon component
  const IconComponent = (LucideIcons as any)[getServiceTypeIcon(service.type_service)];
  const Icon = IconComponent || LucideIcons.MoreHorizontal;

  return (
    <>
      <div className={`bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 transition-all duration-200 hover:shadow-lg ${!service.actif ? 'opacity-70' : ''}`}>
        <div className="relative h-48 overflow-hidden">
          <img 
            src={service.image_url || 'https://images.pexels.com/photos/271624/pexels-photo-271624.jpeg'}
            alt={service.nom}
            className="w-full h-full object-cover"
          />
          <div className="absolute top-0 left-0 m-3">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getServiceTypeColor(service.type_service)}`}>
              <Icon size={14} className="mr-1" />
              {getServiceTypeLabel(service.type_service)}
            </span>
          </div>
          {!service.actif && (
            <div className="absolute inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center">
              <span className="bg-red-100 text-red-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                INACTIF
              </span>
            </div>
          )}
        </div>
        
        <div className="p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-semibold text-gray-900 truncate">
              {service.nom}
            </h3>
            <span className="text-sm text-gray-500">
              {formatHoraires(service.horaires_ouverture)}
            </span>
          </div>
          
          {service.description && (
            <p className="text-gray-600 text-sm line-clamp-2 mb-3">
              {service.description}
            </p>
          )}

          {service.emplacement && (
            <div className="flex items-center text-sm text-gray-500 mb-3">
              <LucideIcons.MapPin size={16} className="mr-1" />
              <span className="truncate">
                {service.emplacement}
              </span>
            </div>
          )}

          <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
            {service.capacite && (
              <div className="flex items-center">
                <LucideIcons.Users size={16} className="mr-1" />
                <span>{service.capacite} personnes</span>
              </div>
            )}
            {service.contact_telephone && (
              <div className="flex items-center">
                <LucideIcons.Phone size={16} className="mr-1" />
                <span>{service.contact_telephone}</span>
              </div>
            )}
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              icon={<Eye size={16} />}
              onClick={() => setShowDetails(true)}
              className="flex-1"
            >
              Détails
            </Button>
            <Button
              variant="outline"
              size="sm"
              icon={<Edit size={16} />}
              onClick={() => setShowEditModal(true)}
              className="flex-1"
            >
              Modifier
            </Button>
            <Button
              variant={service.actif ? 'danger' : 'success'}
              size="sm"
              icon={service.actif ? <PowerOff size={16} /> : <Power size={16} />}
              onClick={() => onStatusToggle(service.service_id, !service.actif)}
              className="flex-1"
            >
              {service.actif ? 'Désactiver' : 'Activer'}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              icon={<Trash2 size={16} />}
              onClick={() => setShowDeleteConfirm(true)}
              className="text-red-600 hover:bg-red-50"
            />
          </div>
        </div>
      </div>
      
      {showDetails && (
        <ServiceDetailsModal
          isOpen={showDetails}
          onClose={() => setShowDetails(false)}
          service={service}
        />
      )}
      
      {showEditModal && (
        <ServiceModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          service={service}
          onServiceCreated={() => {}}
        />
      )}
      
      <ConfirmationModal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={() => {
          onDelete(service.service_id);
          setShowDeleteConfirm(false);
        }}
        title="Supprimer le service"
        message={`Êtes-vous sûr de vouloir supprimer le service "${service.nom}" ? Cette action est irréversible.`}
        confirmText="Supprimer"
        cancelText="Annuler"
      />
    </>
  );
};

export default ServiceCard;