import React, { useState } from 'react';
import { Eye, Edit, Trash2, Power, PowerOff } from 'lucide-react';
import Button from '../ui/Button';
import { Service } from '../../types';
import { getServiceTypeLabel, getServiceTypeColor } from '../../lib/utils';
import ServiceModal from './ServiceModal';
import ConfirmationModal from '../ui/ConfirmationModal';
import ServiceDetailsModal from './ServiceDetailsModal';
import * as LucideIcons from 'lucide-react';

interface ServiceListItemProps {
  service: Service;
  onStatusToggle: (id: number, status: boolean) => void;
  onDelete: (id: number) => void;
}

const ServiceListItem: React.FC<ServiceListItemProps> = ({
  service,
  onStatusToggle,
  onDelete
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  return (
    <>
      <tr className={`${!service.actif ? 'bg-gray-50' : ''}`}>
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="flex items-center">
            <img 
              src={service.image_url || 'https://images.pexels.com/photos/271624/pexels-photo-271624.jpeg'} 
              alt={service.nom} 
              className="h-10 w-10 rounded-full object-cover"
            />
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-900">{service.nom}</div>
              {service.description && (
                <div className="text-sm text-gray-500 line-clamp-1">{service.description}</div>
              )}
            </div>
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getServiceTypeColor(service.type_service)}`}>
            {getServiceTypeLabel(service.type_service)}
          </span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          {service.emplacement || '-'}
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
          {service.capacite || '-'}
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span 
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              service.actif 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}
          >
            {service.actif ? 'Actif' : 'Inactif'}
          </span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div className="flex items-center justify-end space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(true)}
              icon={<Eye size={16} />}
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowEditModal(true)}
              icon={<Edit size={16} />}
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onStatusToggle(service.service_id, !service.actif)}
              icon={service.actif ? <PowerOff size={16} /> : <Power size={16} />}
              className={service.actif ? 'text-red-600' : 'text-green-600'}
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDeleteConfirm(true)}
              icon={<Trash2 size={16} />}
              className="text-red-600"
            />
          </div>
        </td>
      </tr>

      {showDetails && (
        <ServiceDetailsModal
          isOpen={showDetails}
          onClose={() => setShowDetails(false)}
          service={service}
        />
      )}
      
      {showEditModal && (
        <ServiceModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          service={service}
          onServiceCreated={() => {}}
        />
      )}
      
      <ConfirmationModal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={() => {
          onDelete(service.service_id);
          setShowDeleteConfirm(false);
        }}
        title="Supprimer le service"
        message={`Êtes-vous sûr de vouloir supprimer le service "${service.nom}" ? Cette action est irréversible.`}
        confirmText="Supprimer"
        cancelText="Annuler"
        danger
      />
    </>
  );
};

export default ServiceListItem;