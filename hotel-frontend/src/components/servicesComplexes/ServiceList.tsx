import React, { useState, useEffect } from 'react';
import { LayoutGrid, ListFilter, List, Search, RefreshCw, Plus } from 'lucide-react';
import { serviceComplexeService } from '../../services';
import { Service, ServiceType, SERVICE_TYPES } from '../../types';
import ServiceCard from './ServiceCard';
import ServiceListItem from './ServiceListItem';
import ServiceTypeFilter from './ServiceTypeFilter';
import Button from '../ui/Button';
import Pagination from '../ui/Pagination';
import LoadingSpinner from '../ui/LoadingSpinner';
import ServiceModal from './ServiceModal';
import toast from 'react-hot-toast';

const ServiceList: React.FC = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedType, setSelectedType] = useState<ServiceType | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [showModal, setShowModal] = useState(false);

  const fetchServices = async () => {
    try {
      setLoading(true);
      let servicesData: Service[];

      if (selectedType) {
        servicesData = await serviceComplexeService.getServicesByType(selectedType);
      } else {
        servicesData = await serviceComplexeService.getAllServices();
      }

      setServices(servicesData);
      // Pour l'instant, on simule la pagination côté client
      setTotalPages(Math.ceil(servicesData.length / 10));
      setError(null);
    } catch (err: any) {
      setError('Erreur lors du chargement des services: ' + err.message);
      toast.error('Erreur lors du chargement des services');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchServices();
  }, [currentPage, selectedType]);

  const handleRefresh = () => {
    fetchServices();
    toast.success('Liste des services actualisée');
  };

  const handleTypeChange = (type: ServiceType | null) => {
    setSelectedType(type);
    setCurrentPage(1);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real application, you would pass this to the API
    // For now, we'll just console log it
    console.log('Searching for:', searchQuery);
    toast.success(`Recherche de: ${searchQuery}`);
  };

  const handleServiceCreated = () => {
    fetchServices();
    toast.success('Service créé avec succès');
  };

  const handleStatusToggle = async (id: number, newStatus: boolean) => {
    try {
      await serviceComplexeService.toggleServiceStatus(id, newStatus);
      setServices(services.map(service =>
        service.service_id === id ? { ...service, actif: newStatus } : service
      ));
      toast.success(`Service ${newStatus ? 'activé' : 'désactivé'} avec succès`);
    } catch (err) {
      toast.error('Erreur lors de la modification du statut');
    }
  };

  const handleServiceDelete = async (id: number) => {
    try {
      await serviceComplexeService.deleteService(id);
      setServices(services.filter(service => service.service_id !== id));
      toast.success('Service supprimé avec succès');
    } catch (err) {
      toast.error('Erreur lors de la suppression du service');
    }
  };

  const filteredServices = searchQuery
    ? services.filter(service =>
        service.nom.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (service.description && service.description.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    : services;

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6">
        <div className="flex-grow">
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Gestion des Services</h1>
          <p className="text-gray-600">
            Gérez tous les services du complexe hôtelier
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          <Button 
            variant="primary" 
            icon={<Plus size={18} />} 
            onClick={() => setShowModal(true)}
          >
            Nouveau Service
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div className="p-4 border-b border-gray-200 flex flex-col md:flex-row gap-4">
          <div className="flex-grow">
            <form onSubmit={handleSearch} className="relative">
              <input
                type="text"
                placeholder="Rechercher un service..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
              />
              <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            </form>
          </div>

          <div className="flex items-center gap-2 justify-end">
            <Button
              variant="outline"
              size="sm"
              icon={<ListFilter size={18} />}
              onClick={() => setIsFilterOpen(!isFilterOpen)}
            >
              Filtrer
            </Button>
            <Button
              variant="outline"
              size="sm"
              icon={<RefreshCw size={18} />}
              onClick={handleRefresh}
              loading={loading}
            >
              Actualiser
            </Button>
            <div className="border-l border-gray-300 h-6 mx-2"></div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setViewMode('grid')}
              className={viewMode === 'grid' ? 'bg-gray-100' : ''}
            >
              <LayoutGrid size={20} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setViewMode('list')}
              className={viewMode === 'list' ? 'bg-gray-100' : ''}
            >
              <List size={20} />
            </Button>
          </div>
        </div>

        {isFilterOpen && (
          <div className="p-4 bg-gray-50 border-b border-gray-200 animate-fade-in">
            <ServiceTypeFilter 
              selectedType={selectedType} 
              onTypeChange={handleTypeChange} 
            />
          </div>
        )}

        <div className="p-4">
          {loading ? (
            <div className="flex justify-center py-10">
              <LoadingSpinner size="lg" />
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-red-500">{error}</p>
              <Button 
                variant="outline" 
                size="sm" 
                className="mt-4" 
                onClick={fetchServices}
              >
                Réessayer
              </Button>
            </div>
          ) : filteredServices.length === 0 ? (
            <div className="text-center py-10">
              <p className="text-gray-500 mb-4">Aucun service trouvé</p>
              <Button 
                variant="primary" 
                size="sm" 
                onClick={() => setShowModal(true)}
                icon={<Plus size={18} />}
              >
                Ajouter un service
              </Button>
            </div>
          ) : (
            viewMode === 'grid' ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredServices.map(service => (
                  <ServiceCard 
                    key={service.service_id} 
                    service={service} 
                    onStatusToggle={handleStatusToggle} 
                    onDelete={handleServiceDelete}
                  />
                ))}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Service
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Emplacement
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Capacité
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredServices.map((service) => (
                      <ServiceListItem 
                        key={service.service_id}
                        service={service}
                        onStatusToggle={handleStatusToggle}
                        onDelete={handleServiceDelete}
                      />
                    ))}
                  </tbody>
                </table>
              </div>
            )
          )}
        </div>

        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <Pagination 
            currentPage={currentPage} 
            totalPages={totalPages} 
            onPageChange={setCurrentPage} 
          />
        </div>
      </div>

      <ServiceModal 
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onServiceCreated={handleServiceCreated}
      />
    </div>
  );
};

export default ServiceList;