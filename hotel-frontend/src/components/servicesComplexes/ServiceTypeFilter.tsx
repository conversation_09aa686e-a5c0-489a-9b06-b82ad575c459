import React from 'react';
import { SERVICE_TYPES, ServiceType } from '../../types';
import Button from '../ui/Button';
import * as LucideIcons from 'lucide-react';

interface ServiceTypeFilterProps {
  selectedType: ServiceType | null;
  onTypeChange: (type: ServiceType | null) => void;
}

const ServiceTypeFilter: React.FC<ServiceTypeFilterProps> = ({ 
  selectedType, 
  onTypeChange 
}) => {
  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium text-gray-700">Filtrer par type</h3>
      
      <div className="flex flex-wrap gap-2">
        <Button
          variant={selectedType === null ? 'primary' : 'outline'}
          size="sm"
          onClick={() => onTypeChange(null)}
        >
          Tous
        </Button>
        
        {SERVICE_TYPES.map((type) => {
          // Dynamically get the icon component
          const IconComponent = (LucideIcons as any)[type.icon];
          const Icon = IconComponent || LucideIcons.MoreHorizontal;
          
          return (
            <Button
              key={type.value}
              variant={selectedType === type.value ? 'primary' : 'outline'}
              size="sm"
              icon={<Icon size={16} />}
              onClick={() => onTypeChange(type.value)}
            >
              {type.label}
            </Button>
          );
        })}
      </div>
    </div>
  );
};

export default ServiceTypeFilter;