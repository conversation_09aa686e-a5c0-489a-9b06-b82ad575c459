import React, { useState, useEffect } from 'react';
import { Chambre, CreateChambreParams, UpdateChambreParams } from '../services/chambre.service';

interface ChambreFormProps {
  chambre?: Chambre;
  onSubmit: (data: CreateChambreParams | UpdateChambreParams) => Promise<void>;
  onCancel: () => void;
}

const ChambreForm: React.FC<ChambreFormProps> = ({ chambre, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState<CreateChambreParams>({
    numero: '',
    type_chambre: '',
    capacite: 1,
    prix_base: 0,
    description: '',
    etage: 1,
    caracteristiques: {}
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (chambre) {
      setFormData({
        numero: chambre.numero,
        type_chambre: chambre.type_chambre,
        capacite: chambre.capacite,
        prix_base: chambre.prix_base,
        description: chambre.description || '',
        etage: chambre.etage || 1,
        caracteristiques: chambre.caracteristiques || {}
      });
    }
  }, [chambre]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'capacite' || name === 'prix_base' || name === 'etage' 
        ? Number(value) 
        : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await onSubmit(formData);
    } catch (err: any) {
      setError(err.message || 'Une erreur est survenue');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="numero" className="block text-sm font-medium text-gray-700">
            Numéro de chambre
          </label>
          <input
            type="text"
            id="numero"
            name="numero"
            value={formData.numero}
            onChange={handleChange}
            required
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
        </div>

        <div>
          <label htmlFor="type_chambre" className="block text-sm font-medium text-gray-700">
            Type de chambre
          </label>
          <select
            id="type_chambre"
            name="type_chambre"
            value={formData.type_chambre}
            onChange={handleChange}
            required
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="">Sélectionner un type</option>
            <option value="standard">Standard</option>
            <option value="VIP">VIP</option>
            <option value="suite">Suite</option>
          </select>
        </div>

        <div>
          <label htmlFor="capacite" className="block text-sm font-medium text-gray-700">
            Capacité
          </label>
          <input
            type="number"
            id="capacite"
            name="capacite"
            value={formData.capacite}
            onChange={handleChange}
            min="1"
            required
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
        </div>

        <div>
          <label htmlFor="prix_base" className="block text-sm font-medium text-gray-700">
            Prix de base
          </label>
          <input
            type="number"
            id="prix_base"
            name="prix_base"
            value={formData.prix_base}
            onChange={handleChange}
            min="0"
            step="0.01"
            required
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
        </div>

        <div>
          <label htmlFor="etage" className="block text-sm font-medium text-gray-700">
            Étage
          </label>
          <input
            type="number"
            id="etage"
            name="etage"
            value={formData.etage}
            onChange={handleChange}
            min="1"
            required
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          />
        </div>
      </div>

      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
          Description
        </label>
        <textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleChange}
          rows={3}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Annuler
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {loading ? 'Enregistrement...' : chambre ? 'Mettre à jour' : 'Créer'}
        </button>
      </div>
    </form>
  );
};

export default ChambreForm; 