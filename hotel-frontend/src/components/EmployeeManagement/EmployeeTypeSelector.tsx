import React from 'react';
import { roleService } from '../../services/role.service';

interface EmployeeTypeSelectorProps {
  selectedType: string;
  onTypeChange: (type: string, permissions: string[]) => void;
  disabled?: boolean;
}

const EmployeeTypeSelector: React.FC<EmployeeTypeSelectorProps> = ({
  selectedType,
  onTypeChange,
  disabled = false
}) => {
  const employeeTypes = roleService.getEmployeeTypes();

  const handleTypeChange = (type: string) => {
    if (disabled) return;
    
    const typeInfo = employeeTypes[type];
    if (typeInfo) {
      onTypeChange(type, typeInfo.permissions);
    }
  };

  return (
    <div className="space-y-4">
      <div className="text-sm text-gray-600 mb-4">
        Sélectionnez un type d'employé prédéfini. Les permissions seront automatiquement assignées.
      </div>

      <div className="grid grid-cols-1 gap-3">
        {Object.entries(employeeTypes).map(([type, info]) => (
          <label
            key={type}
            className={`
              flex items-start space-x-3 p-4 border rounded-lg cursor-pointer transition-colors
              ${selectedType === type 
                ? 'border-purple-500 bg-purple-50' 
                : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }
              ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
            `}
          >
            <input
              type="radio"
              name="employeeType"
              value={type}
              checked={selectedType === type}
              onChange={() => handleTypeChange(type)}
              disabled={disabled}
              className="mt-1 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300"
            />
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-900">
                {info.label}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Permissions : {info.permissions.join(', ')}
              </div>
            </div>
          </label>
        ))}
      </div>

      {selectedType && (
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">
            Type sélectionné : {employeeTypes[selectedType]?.label}
          </h4>
          <div className="text-sm text-blue-700">
            Permissions automatiques : 
            <div className="flex flex-wrap gap-1 mt-1">
              {employeeTypes[selectedType]?.permissions.map(permission => (
                <span
                  key={permission}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {permission}
                </span>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmployeeTypeSelector;
