import React, { useState, useEffect } from 'react';
import { roleService } from '../../services/role.service';
import ServiceAccessSelector from './ServiceAccessSelector';

interface PermissionSelectorProps {
  selectedPermissions: string[];
  onPermissionsChange: (permissions: string[]) => void;
  disabled?: boolean;
}

interface PermissionCategory {
  [key: string]: string;
}

interface PermissionCategories {
  [categoryName: string]: PermissionCategory;
}

const PermissionSelector: React.FC<PermissionSelectorProps> = ({
  selectedPermissions,
  onPermissionsChange,
  disabled = false
}) => {
  const [permissionCategories, setPermissionCategories] = useState<PermissionCategories>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadPermissions();
  }, []);

  const loadPermissions = async () => {
    try {
      setLoading(true);
      const categories = await roleService.getPermissionsByCategory();
      setPermissionCategories(categories);
      
      // Expand categories that have selected permissions
      const categoriesToExpand = new Set<string>();
      Object.entries(categories).forEach(([categoryName, permissions]) => {
        const hasSelectedPermission = Object.keys(permissions).some(permission => 
          selectedPermissions.includes(permission)
        );
        if (hasSelectedPermission) {
          categoriesToExpand.add(categoryName);
        }
      });
      setExpandedCategories(categoriesToExpand);
    } catch (err) {
      setError('Erreur lors du chargement des permissions');
      console.error('Error loading permissions:', err);
    } finally {
      setLoading(false);
    }
  };

  const toggleCategory = (categoryName: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryName)) {
      newExpanded.delete(categoryName);
    } else {
      newExpanded.add(categoryName);
    }
    setExpandedCategories(newExpanded);
  };

  const handlePermissionChange = (permission: string, checked: boolean) => {
    if (disabled) return;

    let newPermissions: string[];
    if (checked) {
      newPermissions = [...selectedPermissions, permission];
    } else {
      newPermissions = selectedPermissions.filter(p => p !== permission);
    }
    onPermissionsChange(newPermissions);
  };

  const handleCategoryToggle = (categoryName: string, checked: boolean) => {
    if (disabled) return;

    const categoryPermissions = Object.keys(permissionCategories[categoryName] || {});
    let newPermissions = [...selectedPermissions];

    if (checked) {
      // Add all permissions from this category
      categoryPermissions.forEach(permission => {
        if (!newPermissions.includes(permission)) {
          newPermissions.push(permission);
        }
      });
    } else {
      // Remove all permissions from this category
      newPermissions = newPermissions.filter(permission => 
        !categoryPermissions.includes(permission)
      );
    }

    onPermissionsChange(newPermissions);
  };

  const getCategoryStats = (categoryName: string) => {
    const categoryPermissions = Object.keys(permissionCategories[categoryName] || {});
    const selectedCount = categoryPermissions.filter(permission => 
      selectedPermissions.includes(permission)
    ).length;
    return { selected: selectedCount, total: categoryPermissions.length };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        <span className="ml-2 text-gray-600">Chargement des permissions...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded">
        {error}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-sm text-gray-600 mb-4">
        Sélectionnez les permissions pour ce rôle. Les permissions sont organisées par catégorie.
      </div>

      {/* Section spéciale pour les services */}
      <div className="border-2 border-purple-200 rounded-lg p-4 bg-purple-50">
        <h4 className="font-semibold text-purple-900 mb-3 flex items-center">
          <span className="w-2 h-2 bg-purple-600 rounded-full mr-2"></span>
          Accès aux Services du Complexe
        </h4>
        <ServiceAccessSelector
          selectedPermissions={selectedPermissions}
          onPermissionsChange={onPermissionsChange}
          disabled={disabled}
        />
      </div>

      {Object.entries(permissionCategories).map(([categoryName, permissions]) => {
        const stats = getCategoryStats(categoryName);
        const isExpanded = expandedCategories.has(categoryName);
        const isAllSelected = stats.selected === stats.total && stats.total > 0;
        const isPartiallySelected = stats.selected > 0 && stats.selected < stats.total;

        return (
          <div key={categoryName} className="border border-gray-200 rounded-lg">
            <div 
              className="flex items-center justify-between p-4 bg-gray-50 cursor-pointer hover:bg-gray-100"
              onClick={() => toggleCategory(categoryName)}
            >
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  ref={input => {
                    if (input) input.indeterminate = isPartiallySelected;
                  }}
                  onChange={(e) => handleCategoryToggle(categoryName, e.target.checked)}
                  onClick={(e) => e.stopPropagation()}
                  disabled={disabled}
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                />
                <h3 className="font-medium text-gray-900">{categoryName}</h3>
                <span className="text-sm text-gray-500">
                  ({stats.selected}/{stats.total})
                </span>
              </div>
              <svg
                className={`w-5 h-5 text-gray-400 transform transition-transform ${
                  isExpanded ? 'rotate-180' : ''
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>

            {isExpanded && (
              <div className="p-4 border-t border-gray-200">
                <div className="grid grid-cols-1 gap-3">
                  {Object.entries(permissions).map(([permission, description]) => (
                    <label key={permission} className="flex items-start space-x-3">
                      <input
                        type="checkbox"
                        checked={selectedPermissions.includes(permission)}
                        onChange={(e) => handlePermissionChange(permission, e.target.checked)}
                        disabled={disabled}
                        className="mt-1 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                      />
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900">
                          {permission}
                        </div>
                        <div className="text-xs text-gray-500">
                          {description}
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            )}
          </div>
        );
      })}

      {selectedPermissions.length > 0 && (
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">
            Permissions sélectionnées ({selectedPermissions.length})
          </h4>
          <div className="flex flex-wrap gap-2">
            {selectedPermissions.map(permission => (
              <span
                key={permission}
                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
              >
                {permission}
                {!disabled && (
                  <button
                    type="button"
                    onClick={() => handlePermissionChange(permission, false)}
                    className="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-400 hover:text-blue-600 hover:bg-blue-200"
                  >
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                )}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default PermissionSelector;
