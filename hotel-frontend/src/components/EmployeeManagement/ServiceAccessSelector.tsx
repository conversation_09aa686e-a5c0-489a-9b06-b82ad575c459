import React, { useState, useEffect } from 'react';
import { HandPlatter, Wine, Waves, Check, X } from 'lucide-react';
import { servicePermissionService, ServiceType } from '../../services/servicePermission.service';

interface ServiceAccessSelectorProps {
  selectedPermissions: string[];
  onPermissionsChange: (permissions: string[]) => void;
  disabled?: boolean;
}

interface ServiceConfig {
  type: ServiceType;
  name: string;
  icon: React.ReactNode;
  color: string;
  permissions: string[];
  descriptions: Record<string, string>;
}

const ServiceAccessSelector: React.FC<ServiceAccessSelectorProps> = ({
  selectedPermissions,
  onPermissionsChange,
  disabled = false
}) => {
  const [serviceConfigs, setServiceConfigs] = useState<ServiceConfig[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadServiceConfigs();
  }, []);

  const loadServiceConfigs = () => {
    try {
      setLoading(true);
      
      const permissionsList = servicePermissionService.getServicePermissionsList();
      const descriptions = servicePermissionService.getServicePermissionsDescriptions();

      const configs: ServiceConfig[] = [
        {
          type: 'Restaurant',
          name: 'Restaurant',
          icon: <HandPlatter size={24} />,
          color: 'bg-orange-500',
          permissions: permissionsList.Restaurant,
          descriptions
        },
        {
          type: 'Bar',
          name: 'Bar',
          icon: <Wine size={24} />,
          color: 'bg-purple-500',
          permissions: permissionsList.Bar,
          descriptions
        },
        {
          type: 'Piscine',
          name: 'Piscine',
          icon: <Waves size={24} />,
          color: 'bg-blue-500',
          permissions: permissionsList.Piscine,
          descriptions
        }
      ];

      setServiceConfigs(configs);
    } catch (error) {
      console.error('Error loading service configs:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePermissionToggle = (permission: string, checked: boolean) => {
    if (disabled) return;

    let newPermissions: string[];
    if (checked) {
      newPermissions = [...selectedPermissions, permission];
    } else {
      newPermissions = selectedPermissions.filter(p => p !== permission);
    }
    onPermissionsChange(newPermissions);
  };

  const handleServiceToggle = (serviceConfig: ServiceConfig, checked: boolean) => {
    if (disabled) return;

    let newPermissions = [...selectedPermissions];

    if (checked) {
      // Ajouter toutes les permissions du service
      serviceConfig.permissions.forEach(permission => {
        if (!newPermissions.includes(permission)) {
          newPermissions.push(permission);
        }
      });
    } else {
      // Retirer toutes les permissions du service
      newPermissions = newPermissions.filter(permission => 
        !serviceConfig.permissions.includes(permission)
      );
    }

    onPermissionsChange(newPermissions);
  };

  const getServiceStats = (serviceConfig: ServiceConfig) => {
    const selectedCount = serviceConfig.permissions.filter(permission => 
      selectedPermissions.includes(permission)
    ).length;
    return { selected: selectedCount, total: serviceConfig.permissions.length };
  };

  const isServiceFullySelected = (serviceConfig: ServiceConfig) => {
    const stats = getServiceStats(serviceConfig);
    return stats.selected === stats.total && stats.total > 0;
  };

  const isServicePartiallySelected = (serviceConfig: ServiceConfig) => {
    const stats = getServiceStats(serviceConfig);
    return stats.selected > 0 && stats.selected < stats.total;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        <span className="ml-2 text-gray-600">Chargement des services...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-sm text-gray-600 mb-4">
        Sélectionnez les services auxquels cet employé aura accès et les permissions associées.
      </div>

      {serviceConfigs.map((serviceConfig) => {
        const stats = getServiceStats(serviceConfig);
        const isFullySelected = isServiceFullySelected(serviceConfig);
        const isPartiallySelected = isServicePartiallySelected(serviceConfig);

        return (
          <div key={serviceConfig.type} className="border border-gray-200 rounded-lg overflow-hidden">
            {/* En-tête du service */}
            <div 
              className={`${serviceConfig.color} text-white p-4 cursor-pointer hover:opacity-90 transition-opacity`}
              onClick={() => handleServiceToggle(serviceConfig, !isFullySelected)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={isFullySelected}
                    ref={input => {
                      if (input) input.indeterminate = isPartiallySelected;
                    }}
                    onChange={(e) => handleServiceToggle(serviceConfig, e.target.checked)}
                    onClick={(e) => e.stopPropagation()}
                    disabled={disabled}
                    className="h-5 w-5 text-white focus:ring-white border-white rounded"
                  />
                  <div className="text-white">
                    {serviceConfig.icon}
                  </div>
                  <h3 className="font-semibold text-lg">{serviceConfig.name}</h3>
                  <span className="bg-white bg-opacity-20 px-2 py-1 rounded-full text-sm">
                    {stats.selected}/{stats.total}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  {isFullySelected && <Check size={20} />}
                  {isPartiallySelected && <span className="text-sm">Partiel</span>}
                </div>
              </div>
            </div>

            {/* Permissions détaillées */}
            <div className="p-4 bg-gray-50">
              <div className="grid grid-cols-1 gap-3">
                {serviceConfig.permissions.map((permission) => (
                  <label key={permission} className="flex items-start space-x-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={selectedPermissions.includes(permission)}
                      onChange={(e) => handlePermissionToggle(permission, e.target.checked)}
                      disabled={disabled}
                      className="mt-1 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                    />
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-900">
                        {permission}
                      </div>
                      <div className="text-xs text-gray-500">
                        {serviceConfig.descriptions[permission] || 'Description non disponible'}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>
          </div>
        );
      })}

      {/* Résumé des permissions sélectionnées */}
      {selectedPermissions.length > 0 && (
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">
            Permissions de services sélectionnées ({selectedPermissions.filter(p => 
              serviceConfigs.some(config => config.permissions.includes(p))
            ).length})
          </h4>
          <div className="flex flex-wrap gap-2">
            {selectedPermissions
              .filter(permission => 
                serviceConfigs.some(config => config.permissions.includes(permission))
              )
              .map(permission => (
                <span
                  key={permission}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {permission}
                  {!disabled && (
                    <button
                      type="button"
                      onClick={() => handlePermissionToggle(permission, false)}
                      className="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-400 hover:text-blue-600 hover:bg-blue-200"
                    >
                      <X size={12} />
                    </button>
                  )}
                </span>
              ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ServiceAccessSelector;
