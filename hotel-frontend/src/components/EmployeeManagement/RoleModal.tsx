import React, { useState, useEffect } from 'react';
import { X, Loader, Info, ShieldCheck, Users } from 'lucide-react';
import { Role, roleService } from '../../services/role.service';
import PermissionSelector from './PermissionSelector';

interface RoleModalProps {
  role: Role | null;
  onClose: () => void;
  onSuccess: (isNew: boolean) => void;
  showToast: (type: 'success' | 'error' | 'info', message: string) => void;
}

interface FormData {
  nom: string;
  description: string;
  permissions: string[];
  employeeType?: string;
  isCustomRole: boolean;
}



const RoleModal: React.FC<RoleModalProps> = ({
  role,
  onClose,
  onSuccess,
  showToast
}) => {
  const isNewRole = !role;
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    nom: '',
    description: '',
    permissions: [],
    employeeType: '',
    isCustomRole: true, // Toujours true pour les nouveaux rôles
  });
  const [errors, setErrors] = useState<Partial<Record<keyof FormData, string>>>({});
  const [isLoading, setIsLoading] = useState(true);

  // Initialize form
  useEffect(() => {
    setIsLoading(true);

    if (role) {
      setFormData({
        nom: role.nom,
        description: role.description,
        permissions: role.permissions,
        employeeType: '',
        isCustomRole: true, // Les rôles existants sont considérés comme personnalisés
      });
    }

    setIsLoading(false);
  }, [role]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error for this field
    if (errors[name as keyof FormData]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name as keyof FormData];
        return newErrors;
      });
    }
  };



  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof FormData, string>> = {};
    
    if (!formData.nom.trim()) newErrors.nom = 'Le nom est requis';
    if (!formData.description.trim()) newErrors.description = 'La description est requise';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    try {
      if (isNewRole) {
        // Create new role
        await roleService.createRole(formData);
      } else if (role) {
        // Update existing role
        await roleService.updateRole(role.role_id, formData);
      }
      
      onSuccess(isNewRole);
    } catch (error) {
      console.error('Error saving role:', error);
      showToast('error', `Erreur lors de ${isNewRole ? 'la création' : 'la modification'} du rôle`);
    } finally {
      setIsSubmitting(false);
    }
  };



  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-purple-50">
          <h2 className="text-xl font-semibold text-purple-900 flex items-center">
            <ShieldCheck size={20} className="mr-2 text-purple-700" />
            {isNewRole ? 'Créer un rôle personnalisé' : 'Modifier un rôle'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
            disabled={isSubmitting || isLoading}
          >
            <X size={24} />
          </button>
        </div>
        
        {isLoading ? (
          <div className="p-6 flex flex-col items-center justify-center">
            <Loader size={40} className="text-purple-600 animate-spin mb-4" />
            <p className="text-gray-500">Chargement des données...</p>
          </div>
        ) : (
          <>
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-8rem)]">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Info sur les rôles personnalisés */}
                {isNewRole && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div className="flex items-start">
                      <Users size={20} className="text-blue-600 mr-3 mt-0.5" />
                      <div>
                        <h3 className="text-sm font-medium text-blue-900 mb-1">
                          Création d'un rôle personnalisé
                        </h3>
                        <p className="text-sm text-blue-700">
                          Les types d'employés prédéfinis (Réception, Gérant Piscine, Serveuse, etc.) sont automatiquement disponibles.
                          Créez ici des rôles personnalisés avec des permissions spécifiques.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Champs de base (nom et description) */}
                <div>
                  <label htmlFor="nom" className="block text-sm font-medium text-gray-700 mb-1">
                    Nom du rôle *
                  </label>
                  <input
                    type="text"
                    id="nom"
                    name="nom"
                    value={formData.nom}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 ${
                      errors.nom 
                        ? 'border-red-300 focus:ring-red-500' 
                        : 'border-gray-300 focus:ring-purple-500'
                    }`}
                    disabled={isSubmitting}
                  />
                  {errors.nom && (
                    <p className="mt-1 text-sm text-red-600">{errors.nom}</p>
                  )}
                </div>
                
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description *
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    rows={3}
                    value={formData.description}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-1 ${
                      errors.description 
                        ? 'border-red-300 focus:ring-red-500' 
                        : 'border-gray-300 focus:ring-purple-500'
                    }`}
                    disabled={isSubmitting}
                  />
                  {errors.description && (
                    <p className="mt-1 text-sm text-red-600">{errors.description}</p>
                  )}
                </div>

                {/* Section des permissions */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-medium text-gray-900">Permissions</h3>
                    <div className="flex items-center text-sm text-gray-500">
                      <Info size={16} className="mr-1" />
                      <span>Sélectionnez les permissions pour ce rôle</span>
                    </div>
                  </div>

                  <PermissionSelector
                    selectedPermissions={formData.permissions}
                    onPermissionsChange={(permissions) => setFormData(prev => ({ ...prev, permissions }))}
                    disabled={isSubmitting}
                  />
                </div>
              </form>
            </div>
            
            <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                disabled={isSubmitting}
              >
                Annuler
              </button>
              <button
                type="button"
                onClick={handleSubmit}
                className="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <div className="flex items-center">
                    <Loader size={16} className="animate-spin mr-2" />
                    <span>En cours...</span>
                  </div>
                ) : (
                  isNewRole ? 'Créer le rôle personnalisé' : 'Enregistrer'
                )}
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default RoleModal;