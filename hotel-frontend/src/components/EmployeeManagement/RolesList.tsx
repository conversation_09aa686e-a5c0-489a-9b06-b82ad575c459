import React, { useState } from 'react';
import { Edit, Trash2, AlertCircle, Users, ShieldCheck } from 'lucide-react';
import { Role, roleService } from '../../services/role.service';
import { Employee } from '../../services/employee.service';
import ConfirmationModal from './ConfirmationModal';

interface RolesListProps {
  roles: Role[];
  employees: Employee[];
  onEditRole: (role: Role) => void;
  onDeleteSuccess: () => void;
  showToast: (type: 'success' | 'error' | 'info', message: string) => void;
}

const RolesList: React.FC<RolesListProps> = ({
  roles,
  employees,
  onEditRole,
  onDeleteSuccess,
  showToast
}) => {
  const [roleToDelete, setRoleToDelete] = useState<Role | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Fonction pour compter le nombre d'employés par rôle
  const getEmployeeCountForRole = (roleId: number): number => {
    return employees.filter(employee => employee.role_id === roleId).length;
  };

  const handleDeleteRole = async () => {
    if (!roleToDelete) return;
    
    setIsDeleting(true);
    try {
      await roleService.deleteRole(roleToDelete.role_id);
      onDeleteSuccess();
    } catch (error) {
      console.error('Error deleting role:', error);
      showToast('error', 'Erreur lors de la suppression du rôle');
    } finally {
      setIsDeleting(false);
      setRoleToDelete(null);
    }
  };

  if (roles.length === 0) {
    return null; // Empty state is handled by parent component
  }

  // Group permissions by category
  const getPermissionGroups = (permissions: string[]) => {
    const groups: Record<string, number> = {
      'Employés': 0,
      'Réservations': 0,
      'Clients': 0,
      'Produits': 0,
      'Paiements': 0,
      'Rapports': 0,
      'Tables': 0,
      'Menus': 0,
      'Commandes': 0,
      'POS': 0
    };

    permissions.forEach(perm => {
      if (perm.includes('employee') || perm.includes('role')) {
        groups['Employés']++;
      } else if (perm.includes('reservation')) {
        groups['Réservations']++;
      } else if (perm.includes('client')) {
        groups['Clients']++;
      } else if (perm.includes('product')) {
        groups['Produits']++;
      } else if (perm.includes('payment')) {
        groups['Paiements']++;
      } else if (perm.includes('report') || perm.includes('statistic')) {
        groups['Rapports']++;
      } else if (perm.includes('table')) {
        groups['Tables']++;
      } else if (perm.includes('menu')) {
        groups['Menus']++;
      } else if (perm.includes('order') || perm.includes('kitchen')) {
        groups['Commandes']++;
      } else if (perm.includes('pos') || perm.includes('operate_')) {
        groups['POS']++;
      }
    });

    return Object.entries(groups)
      .filter(([_, count]) => count > 0)
      .map(([name, count]) => ({ name, count }));
  };

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Nom du rôle
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Description
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Permissions
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Employés assignés
            </th>
            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {roles.map((role) => {
            const permissionGroups = getPermissionGroups(role.permissions);
            
            return (
              <tr key={role.role_id} className="hover:bg-gray-50 transition-colors">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                      <ShieldCheck size={18} className="text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{role.nom}</div>
                      <div className="text-sm text-gray-500">ID: {role.role_id}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-900 max-w-xs">{role.description}</div>
                </td>
                <td className="px-6 py-4">
                  <div className="flex flex-wrap gap-1">
                    {permissionGroups.map((group, index) => (
                      <span key={index} className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                        {group.name} ({group.count})
                      </span>
                    ))}
                    {role.permissions.length === 0 && (
                      <span className="text-sm text-gray-500">Aucune permission</span>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <Users size={16} className="text-gray-400 mr-1" />
                    <span className="text-sm text-gray-900">
                      {getEmployeeCountForRole(role.role_id)} employé{getEmployeeCountForRole(role.role_id) !== 1 ? 's' : ''}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center justify-end space-x-2">
                    <button
                      onClick={() => onEditRole(role)}
                      className="text-blue-600 hover:text-blue-800 transition-colors p-1"
                      title="Modifier"
                    >
                      <Edit size={18} />
                    </button>
                    <button
                      onClick={() => setRoleToDelete(role)}
                      className="text-red-600 hover:text-red-800 transition-colors p-1"
                      title="Supprimer"
                    >
                      <Trash2 size={18} />
                    </button>
                  </div>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>

      {/* Confirmation Modal */}
      {roleToDelete && (
        <ConfirmationModal
          title="Supprimer le rôle"
          message={`Êtes-vous sûr de vouloir supprimer le rôle "${roleToDelete.nom}" ? Cette action supprimera également les autorisations associées à ce rôle.`}
          confirmText="Supprimer"
          confirmButtonClass="bg-red-600 hover:bg-red-700"
          cancelText="Annuler"
          isLoading={isDeleting}
          icon={<AlertCircle className="h-6 w-6 text-red-600" />}
          onConfirm={handleDeleteRole}
          onCancel={() => setRoleToDelete(null)}
        />
      )}
    </div>
  );
};

export default RolesList;