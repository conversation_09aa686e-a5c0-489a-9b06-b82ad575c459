# Composants de Réservations Anonymes - Frontend

## Vue d'ensemble

Cette documentation décrit tous les composants React créés pour la fonctionnalité de réservations anonymes dans la Phase 6 du plan d'implémentation.

## Composants principaux

### 1. AnonymousReservationLookup
**Fichier:** `AnonymousReservationLookup.tsx`

Composant principal pour la recherche et consultation de réservations anonymes.

**Fonctionnalités :**
- Saisie et validation du code d'accès
- Formatage automatique du code (ANON-XXXXXXXXXXXX)
- Masquage/affichage du code
- Copie dans le presse-papiers
- Gestion des erreurs et états de chargement
- Affichage des informations d'aide

**Props :** Aucune

**Utilisation :**
```tsx
import { AnonymousReservationLookup } from '@/components/AnonymousReservationLookup';

<AnonymousReservationLookup />
```

### 2. AnonymousReservationDetails
**Fichier:** `AnonymousReservationDetails.tsx`

Composant pour afficher les détails complets d'une réservation anonyme.

**Fonctionnalités :**
- Affichage des informations de séjour
- Gestion des chambres réservées
- Modification des commentaires
- Annulation de réservation
- Badges de statut colorés
- Gestion des permissions (modification/annulation)

**Props :**
- `reservation: AnonymousReservationDetails` - Données de la réservation
- `onUpdate?: () => void` - Callback après modification

**Utilisation :**
```tsx
import { AnonymousReservationDetails } from '@/components/AnonymousReservationDetails';

<AnonymousReservationDetails 
  reservation={reservationData} 
  onUpdate={() => refetchData()} 
/>
```

### 3. ClientReservation (Modifié)
**Fichier:** `ClientReservation.tsx`

Composant existant étendu pour supporter les réservations anonymes.

**Nouvelles fonctionnalités :**
- Option "Réservation anonyme" avec checkbox
- Champ pseudonyme optionnel
- Formulaire conditionnel (masque email/téléphone si anonyme)
- Récapitulatif adapté selon le type
- Toast spécial avec code d'accès pour les réservations anonymes

## Composants publics

### 4. PublicAnonymousReservationLink
**Fichier:** `PublicAnonymousReservationLink.tsx`

Composants pour créer des liens vers la consultation de réservations anonymes.

**Variantes :**
- `PublicAnonymousReservationLink` - Lien principal (button, card, link)
- `AnonymousReservationInfoBanner` - Bannière d'information
- `AnonymousReservationFooterWidget` - Widget pour footer

**Props :**
- `variant?: 'button' | 'card' | 'link'` - Style du lien
- `className?: string` - Classes CSS additionnelles

**Utilisation :**
```tsx
// Bouton
<PublicAnonymousReservationLink variant="button" />

// Carte
<PublicAnonymousReservationLink variant="card" />

// Bannière d'info
<AnonymousReservationInfoBanner />

// Widget footer
<AnonymousReservationFooterWidget />
```

## Composants de statistiques

### 5. AnonymousReservationStats
**Fichier:** `AnonymousReservationStats.tsx`

Composants pour afficher les statistiques publiques des réservations anonymes.

**Variantes :**
- `AnonymousReservationStats` - Affichage complet
- `AnonymousReservationStatsCompact` - Version compacte

**Statistiques affichées :**
- Total des réservations anonymes
- Réservations actives
- Taux de confirmation
- Délai moyen de confirmation

**Props :**
- `className?: string` - Classes CSS
- `showTitle?: boolean` - Afficher le titre (défaut: true)

**Utilisation :**
```tsx
// Version complète
<AnonymousReservationStats />

// Version compacte
<AnonymousReservationStatsCompact />
```

## Composants de disponibilité

### 6. AnonymousReservationAvailability
**Fichier:** `AnonymousReservationAvailability.tsx`

Composants pour vérifier et afficher la disponibilité du service.

**Variantes :**
- `AnonymousReservationAvailability` - Affichage détaillé
- `AnonymousReservationStatusBadge` - Badge simple
- `useAnonymousReservationAvailability` - Hook personnalisé

**Props :**
- `complexeId: number` - ID du complexe à vérifier
- `onAvailabilityChange?: (available: boolean) => void` - Callback de changement
- `className?: string` - Classes CSS

**Utilisation :**
```tsx
// Composant détaillé
<AnonymousReservationAvailability 
  complexeId={1} 
  onAvailabilityChange={(available) => setCanReserve(available)} 
/>

// Badge simple
<AnonymousReservationStatusBadge complexeId={1} />

// Hook
const { availability, isLoading, error, isAvailable } = useAnonymousReservationAvailability(1);
```

## Page principale

### 7. AnonymousReservationPage
**Fichier:** `pages/AnonymousReservation.tsx`

Page principale accessible publiquement pour la consultation de réservations anonymes.

**Route :** `/reservation-anonyme`

**Fonctionnalités :**
- Layout responsive
- Intégration du composant AnonymousReservationLookup
- Styling cohérent avec l'application

## Intégration et routing

### Routes ajoutées
- `/reservation-anonyme` - Route publique (pas d'authentification requise)

### Navigation
- Ajout dans `MainNavigation.tsx` avec icône `UserCheck`
- Couleur : `text-indigo-600 bg-indigo-50 border-indigo-200`

### Middleware
- Route ajoutée aux routes publiques dans `routingMiddleware.ts`

## Gestion des états

### États de chargement
Tous les composants gèrent les états :
- `isLoading` - Chargement en cours
- `error` - Erreurs avec messages explicites
- `success` - États de succès avec feedback utilisateur

### Validation côté client
- Format des codes d'accès (ANON-XXXXXXXXXXXX)
- Validation des formulaires
- Feedback en temps réel

## Styling et UX

### Design cohérent
- Utilisation de Tailwind CSS
- Icônes Lucide React
- Couleurs cohérentes avec le thème de l'application

### Accessibilité
- Labels appropriés
- Navigation au clavier
- Contrastes respectés
- Messages d'erreur explicites

### Responsive
- Grilles adaptatives
- Composants mobiles-first
- Breakpoints cohérents

## Sécurité

### Côté client
- Validation des formats
- Masquage des codes d'accès
- Pas de stockage sensible en localStorage

### Gestion des erreurs
- Messages d'erreur non révélateurs
- Timeouts appropriés
- Fallbacks gracieux

## Tests

### Composants testables
Tous les composants sont conçus pour être facilement testables :
- Props bien définies
- États isolés
- Callbacks mockables
- Erreurs gérées

### Exemple de test
```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { AnonymousReservationLookup } from './AnonymousReservationLookup';

test('should format access code correctly', () => {
  render(<AnonymousReservationLookup />);
  const input = screen.getByPlaceholderText('ANON-XXXXXXXXXXXX');
  fireEvent.change(input, { target: { value: 'ANON123456789012' } });
  expect(input.value).toBe('ANON-123456789012');
});
```

## Prochaines étapes

Ces composants sont prêts pour la Phase 7 qui consistera à :
1. Intégrer les composants dans les pages existantes
2. Ajouter la navigation et les liens
3. Tester l'ensemble du workflow
4. Optimiser les performances

## Export centralisé

Tous les composants sont exportés via `components/anonymous-reservations/index.ts` pour faciliter les imports :

```tsx
import {
  AnonymousReservationLookup,
  AnonymousReservationDetails,
  PublicAnonymousReservationLink,
  AnonymousReservationStats,
  useAnonymousReservationAvailability
} from '@/components/anonymous-reservations';
```
