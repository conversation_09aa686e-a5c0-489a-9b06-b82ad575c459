import React, { useState, useEffect } from 'react';
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Wine, 
  DollarSign, 
  Package,
  RefreshCw,
  Filter,
  Percent
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { menuIntegreService } from '../../services/menuIntegre.service';
import type { ProduitMenu, MenuStats, MenuAlert } from '../../services/menuIntegre.service';

interface BoissonsAvailabilityDashboardProps {
  serviceId: number;
  serviceName: string;
}

export const BoissonsAvailabilityDashboard: React.FC<BoissonsAvailabilityDashboardProps> = ({
  serviceId,
  serviceName
}) => {
  const [boissons, setBoissons] = useState<ProduitMenu[]>([]);
  const [stats, setStats] = useState<MenuStats | null>(null);
  const [alertes, setAlertes] = useState<MenuAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'disponible' | 'rupture' | 'stock_faible'>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    loadBarData();
  }, [serviceId]);

  const loadBarData = async () => {
    try {
      setLoading(true);
      
      // Charger les données en parallèle
      const [menuData, statsData, alertesData] = await Promise.all([
        menuIntegreService.verifierDisponibiliteMenu(serviceId),
        menuIntegreService.getMenuStats(serviceId),
        menuIntegreService.getMenuAlerts(serviceId)
      ]);

      const allBoissons = [...menuData.produits_disponibles, ...menuData.produits_indisponibles];
      setBoissons(allBoissons);
      setStats(statsData);
      setAlertes(alertesData);
      setLastUpdate(new Date());
      
    } catch (error) {
      console.error('Erreur lors du chargement des données bar:', error);
      toast.error('Erreur lors du chargement de la carte');
    } finally {
      setLoading(false);
    }
  };

  const getFilteredBoissons = () => {
    let filtered = boissons;
    
    // Filtre par statut
    if (filter !== 'all') {
      filtered = filtered.filter(boisson => 
        boisson.stock_status === filter || (filter === 'disponible' && boisson.disponible)
      );
    }
    
    // Filtre par catégorie (basé sur le nom pour l'instant)
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(boisson => {
        const nom = boisson.nom.toLowerCase();
        switch (categoryFilter) {
          case 'cocktails':
            return nom.includes('cocktail') || nom.includes('mojito') || nom.includes('caipirinha');
          case 'bieres':
            return nom.includes('bière') || nom.includes('beer') || nom.includes('heineken');
          case 'vins':
            return nom.includes('vin') || nom.includes('wine') || nom.includes('champagne');
          case 'spiritueux':
            return nom.includes('whisky') || nom.includes('vodka') || nom.includes('rhum') || nom.includes('gin');
          case 'soft':
            return nom.includes('coca') || nom.includes('jus') || nom.includes('eau') || nom.includes('soda');
          default:
            return true;
        }
      });
    }
    
    return filtered;
  };

  const getStatusIcon = (status: string, disponible: boolean) => {
    if (!disponible) return <XCircle className="h-5 w-5 text-red-500" />;
    
    switch (status) {
      case 'disponible':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'stock_faible':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'rupture':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Package className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string, disponible: boolean) => {
    if (!disponible) {
      return <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">Rupture</span>;
    }
    
    switch (status) {
      case 'disponible':
        return <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Disponible</span>;
      case 'stock_faible':
        return <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">Stock faible</span>;
      case 'rupture':
        return <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">Rupture</span>;
      default:
        return <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">Inconnu</span>;
    }
  };

  const getCategoryIcon = (categoryName: string) => {
    switch (categoryName) {
      case 'cocktails':
        return '🍹';
      case 'bieres':
        return '🍺';
      case 'vins':
        return '🍷';
      case 'spiritueux':
        return '🥃';
      case 'soft':
        return '🥤';
      default:
        return '🍸';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="bg-white p-6 rounded-lg border border-gray-200">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="space-y-3">
              {[1, 2, 3, 4, 5].map(i => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  const filteredBoissons = getFilteredBoissons();
  const categories = [
    { key: 'all', label: 'Toutes', icon: '🍸' },
    { key: 'cocktails', label: 'Cocktails', icon: '🍹' },
    { key: 'bieres', label: 'Bières', icon: '🍺' },
    { key: 'vins', label: 'Vins', icon: '🍷' },
    { key: 'spiritueux', label: 'Spiritueux', icon: '🥃' },
    { key: 'soft', label: 'Sans alcool', icon: '🥤' }
  ];

  return (
    <div className="space-y-6">
      {/* Header avec stats */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Carte Bar - {serviceName}
          </h2>
          <p className="text-sm text-gray-600">
            Dernière mise à jour: {lastUpdate.toLocaleTimeString()}
          </p>
        </div>
        <button
          onClick={loadBarData}
          disabled={loading}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Actualiser
        </button>
      </div>

      {/* Statistiques */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <Wine className="h-8 w-8 text-purple-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Boissons</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total_produits}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Disponibles</p>
                <p className="text-2xl font-bold text-green-600">{stats.produits_disponibles}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <XCircle className="h-8 w-8 text-red-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">En Rupture</p>
                <p className="text-2xl font-bold text-red-600">{stats.produits_rupture}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <Percent className="h-8 w-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Marge Moyenne</p>
                <p className="text-2xl font-bold text-blue-600">{stats.marge_moyenne.toFixed(1)}%</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Alertes */}
      {alertes.length > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center mb-3">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
            <h3 className="text-sm font-medium text-yellow-800">
              Alertes Stock ({alertes.length})
            </h3>
          </div>
          <div className="space-y-2">
            {alertes.slice(0, 3).map((alerte, index) => (
              <div key={index} className="text-sm text-yellow-700">
                <strong>{alerte.produit_nom}:</strong> {alerte.message}
              </div>
            ))}
            {alertes.length > 3 && (
              <p className="text-sm text-yellow-600">
                ... et {alertes.length - 3} autre(s) alerte(s)
              </p>
            )}
          </div>
        </div>
      )}

      {/* Filtres */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 space-y-4">
        {/* Filtre par statut */}
        <div className="flex items-center space-x-4">
          <Filter className="h-5 w-5 text-gray-400" />
          <span className="text-sm font-medium text-gray-700">Statut:</span>
          <div className="flex space-x-2">
            {[
              { key: 'all', label: 'Toutes', count: boissons.length },
              { key: 'disponible', label: 'Disponibles', count: boissons.filter(b => b.disponible).length },
              { key: 'stock_faible', label: 'Stock faible', count: boissons.filter(b => b.stock_status === 'stock_faible').length },
              { key: 'rupture', label: 'Rupture', count: boissons.filter(b => !b.disponible).length }
            ].map(({ key, label, count }) => (
              <button
                key={key}
                onClick={() => setFilter(key as any)}
                className={`px-3 py-1 text-sm rounded-full border ${
                  filter === key
                    ? 'bg-blue-100 border-blue-300 text-blue-700'
                    : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {label} ({count})
              </button>
            ))}
          </div>
        </div>

        {/* Filtre par catégorie */}
        <div className="flex items-center space-x-4">
          <Package className="h-5 w-5 text-gray-400" />
          <span className="text-sm font-medium text-gray-700">Catégorie:</span>
          <div className="flex space-x-2 flex-wrap">
            {categories.map(({ key, label, icon }) => (
              <button
                key={key}
                onClick={() => setCategoryFilter(key)}
                className={`px-3 py-1 text-sm rounded-full border ${
                  categoryFilter === key
                    ? 'bg-purple-100 border-purple-300 text-purple-700'
                    : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {icon} {label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Liste des boissons */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Boissons ({filteredBoissons.length})
          </h3>
        </div>
        
        <div className="divide-y divide-gray-200">
          {filteredBoissons.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              Aucune boisson trouvée pour ces filtres
            </div>
          ) : (
            filteredBoissons.map((boisson) => (
              <div key={boisson.produit_id} className="p-6 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center flex-1">
                    {getStatusIcon(boisson.stock_status, boisson.disponible)}
                    <div className="ml-4 flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="text-lg font-medium text-gray-900">
                          {boisson.nom}
                        </h4>
                        {getStatusBadge(boisson.stock_status, boisson.disponible)}
                      </div>
                      
                      {boisson.description && (
                        <p className="text-sm text-gray-600 mt-1">{boisson.description}</p>
                      )}
                      
                      <div className="flex items-center space-x-6 mt-2 text-sm text-gray-600">
                        <span className="flex items-center">
                          <DollarSign className="h-4 w-4 mr-1" />
                          Prix: {boisson.prix_vente_defaut.toFixed(0)} FCFA
                        </span>
                        <span>
                          Coût: {boisson.cout_ingredients.toFixed(0)} FCFA
                        </span>
                        <span className={`font-medium ${
                          boisson.marge_pourcentage > 40 ? 'text-green-600' : 
                          boisson.marge_pourcentage > 25 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          Marge: {boisson.marge_pourcentage.toFixed(1)}%
                        </span>
                      </div>
                      
                      {boisson.ingredients_manquants.length > 0 && (
                        <div className="mt-2">
                          <p className="text-sm text-red-600 font-medium">
                            Ingrédients/composants manquants: {boisson.ingredients_manquants.join(', ')}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};
