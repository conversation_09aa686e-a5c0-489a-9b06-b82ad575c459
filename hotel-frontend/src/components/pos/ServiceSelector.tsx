import React from 'react';
import { UtensilsCrossed, Wine, Power, PowerOff, Clock, Users } from 'lucide-react';
import type { ServiceComplexe } from '../../services';
import type { SessionCaisse } from '../../services';

interface ServiceSelectorProps {
  services: ServiceComplexe[];
  selectedService: ServiceComplexe | null;
  onServiceChange: (service: ServiceComplexe) => void;
  activeSessions?: Record<number, SessionCaisse>;
  loading?: boolean;
}

const getServiceIcon = (type: string) => {
  switch (type) {
    case 'Restaurant':
      return UtensilsCrossed;
    case 'Bar':
      return Wine;
    default:
      return UtensilsCrossed;
  }
};

const getServiceStatusColor = (service: ServiceComplexe, hasActiveSession: boolean) => {
  if (!service.actif) {
    return 'bg-gray-100 border-gray-300 text-gray-500';
  }
  
  if (hasActiveSession) {
    return 'bg-green-100 border-green-300 text-green-700';
  }
  
  return 'bg-yellow-100 border-yellow-300 text-yellow-700';
};

const getStatusText = (service: ServiceComplexe, hasActiveSession: boolean) => {
  if (!service.actif) {
    return 'Fermé';
  }
  
  if (hasActiveSession) {
    return 'Session active';
  }
  
  return 'Prêt à ouvrir';
};

export const ServiceSelector: React.FC<ServiceSelectorProps> = ({
  services,
  selectedService,
  onServiceChange,
  activeSessions = {},
  loading = false
}) => {
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-lg font-semibold mb-4">Sélection du Service</h2>
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-16 bg-gray-200 rounded-lg"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (services.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-lg font-semibold mb-4">Sélection du Service</h2>
        <div className="text-center py-8">
          <UtensilsCrossed className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-500">Aucun service Restaurant/Bar disponible</p>
          <p className="text-sm text-gray-400 mt-1">
            Contactez votre administrateur pour configurer les services
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <h2 className="text-lg font-semibold mb-4">Sélection du Service</h2>
      
      <div className="space-y-3">
        {services.map((service) => {
          const Icon = getServiceIcon(service.type_service);
          const hasActiveSession = !!activeSessions[service.service_id];
          const isSelected = selectedService?.service_id === service.service_id;
          
          return (
            <button
              key={service.service_id}
              onClick={() => onServiceChange(service)}
              disabled={!service.actif}
              className={`
                w-full p-4 rounded-lg border-2 transition-all duration-200
                flex items-center justify-between
                hover:shadow-md disabled:cursor-not-allowed
                ${isSelected 
                  ? 'border-blue-500 bg-blue-50 shadow-md' 
                  : getServiceStatusColor(service, hasActiveSession)
                }
              `}
            >
              <div className="flex items-center space-x-3">
                <div className={`
                  p-2 rounded-lg
                  ${isSelected ? 'bg-blue-100' : 'bg-white'}
                `}>
                  <Icon className={`
                    w-5 h-5
                    ${isSelected ? 'text-blue-600' : 'text-gray-600'}
                  `} />
                </div>
                
                <div className="text-left">
                  <h3 className={`
                    font-medium
                    ${isSelected ? 'text-blue-900' : 'text-gray-900'}
                  `}>
                    {service.nom}
                  </h3>
                  <p className={`
                    text-sm
                    ${isSelected ? 'text-blue-600' : 'text-gray-500'}
                  `}>
                    {service.type_service}
                    {service.emplacement && ` • ${service.emplacement}`}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {/* Indicateur de capacité */}
                {service.capacite && (
                  <div className="flex items-center text-xs text-gray-500">
                    <Users className="w-3 h-3 mr-1" />
                    <span>{service.capacite}</span>
                  </div>
                )}
                
                {/* Indicateur de session */}
                <div className="flex items-center space-x-1">
                  {hasActiveSession ? (
                    <Power className="w-4 h-4 text-green-600" />
                  ) : service.actif ? (
                    <PowerOff className="w-4 h-4 text-yellow-600" />
                  ) : (
                    <PowerOff className="w-4 h-4 text-gray-400" />
                  )}
                  
                  <span className={`
                    text-xs font-medium
                    ${isSelected ? 'text-blue-600' : 'text-gray-600'}
                  `}>
                    {getStatusText(service, hasActiveSession)}
                  </span>
                </div>
              </div>
            </button>
          );
        })}
      </div>
      
      {/* Informations sur le service sélectionné */}
      {selectedService && (
        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-blue-900">
                {selectedService.nom}
              </h4>
              <p className="text-sm text-blue-600">
                Service sélectionné
              </p>
            </div>
            
            {activeSessions[selectedService.service_id] && (
              <div className="flex items-center text-xs text-blue-600">
                <Clock className="w-3 h-3 mr-1" />
                <span>
                  Session ouverte depuis{' '}
                  {new Date(activeSessions[selectedService.service_id].date_ouverture)
                    .toLocaleTimeString('fr-FR', { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                </span>
              </div>
            )}
          </div>
          
          {selectedService.description && (
            <p className="text-sm text-blue-700 mt-2">
              {selectedService.description}
            </p>
          )}
        </div>
      )}
      
      {/* Actions rapides */}
      <div className="mt-4 flex space-x-2">
        <button
          disabled={!selectedService || !selectedService.actif}
          className="
            flex-1 py-2 px-3 text-sm font-medium rounded-lg
            bg-blue-500 text-white hover:bg-blue-600
            disabled:bg-gray-300 disabled:cursor-not-allowed
            transition-colors duration-200
          "
        >
          {activeSessions[selectedService?.service_id || 0] 
            ? 'Continuer la session' 
            : 'Ouvrir une session'
          }
        </button>
        
        <button
          disabled={!selectedService}
          className="
            py-2 px-3 text-sm font-medium rounded-lg
            bg-gray-100 text-gray-700 hover:bg-gray-200
            disabled:bg-gray-50 disabled:text-gray-400 disabled:cursor-not-allowed
            transition-colors duration-200
          "
        >
          Paramètres
        </button>
      </div>
    </div>
  );
};
