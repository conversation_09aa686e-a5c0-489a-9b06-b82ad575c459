// Export des composants POS - Phase 2 & 3
export { ServiceSelector } from './ServiceSelector';
export { TableManager } from './TableManager';
export { MenuDisplay } from './MenuDisplay';
export { OrderCart } from './OrderCart';
export { POSHeader } from './POSHeader';

// Composants avancés - Phase 3
export { SessionManager } from './SessionManager';
export { PaymentModal } from './PaymentModal';
export { NotificationCenter } from './NotificationCenter';

// Types pour les props des composants
export type { ServiceSelectorProps } from './ServiceSelector';
export type { TableManagerProps } from './TableManager';
export type { MenuDisplayProps } from './MenuDisplay';
export type { OrderCartProps } from './OrderCart';
export type { POSHeaderProps } from './POSHeader';

// Types d'interfaces pour les composants
interface ServiceSelectorProps {
  services: import('../../services').ServiceComplexe[];
  selectedService: import('../../services').ServiceComplexe | null;
  onServiceChange: (service: import('../../services').ServiceComplexe) => void;
  activeSessions?: Record<number, import('../../services').SessionCaisse>;
  loading?: boolean;
}

interface TableManagerProps {
  serviceId: number;
  tables: import('../../services').Table[];
  onTableSelect: (table: import('../../services').Table) => void;
  selectedTable?: import('../../services').Table;
  loading?: boolean;
  onCreateTable?: () => void;
  onEditTable?: (table: import('../../services').Table) => void;
  onDeleteTable?: (table: import('../../services').Table) => void;
}

interface MenuDisplayProps {
  serviceId: number;
  categories: import('../../services').MenuCategory[];
  onItemAdd: (item: import('../../services').MenuItem, quantity: number) => void;
  searchTerm?: string;
  loading?: boolean;
}

interface OrderCartProps {
  commande: import('../../services').Commande | null;
  onItemUpdate: (itemId: number, quantity: number) => void;
  onItemRemove: (itemId: number) => void;
  onOrderValidate: () => void;
  onPayment: () => void;
  onAddNote?: (note: string) => void;
  loading?: boolean;
  disabled?: boolean;
}

interface POSHeaderProps {
  selectedService: import('../../services').ServiceComplexe | null;
  sessionActive: import('../../services').SessionCaisse | null;
  onServiceChange: (service: import('../../services').ServiceComplexe) => void;
  onSessionToggle: () => void;
  onClose: () => void;
  onSettings?: () => void;
  isFullscreen?: boolean;
  onToggleFullscreen?: () => void;
  notifications?: number;
  onNotificationsClick?: () => void;
}

export type {
  ServiceSelectorProps,
  TableManagerProps,
  MenuDisplayProps,
  OrderCartProps,
  POSHeaderProps
};
