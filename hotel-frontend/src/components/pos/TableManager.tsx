import React, { useState, useMemo } from 'react';
import { Users, Clock, Plus, Search, Filter, MoreVertical } from 'lucide-react';
import type { Table } from '../../services';

interface TableManagerProps {
  serviceId: number;
  tables: Table[];
  onTableSelect: (table: Table) => void;
  selectedTable?: Table;
  loading?: boolean;
  onCreateTable?: () => void;
  onEditTable?: (table: Table) => void;
  onDeleteTable?: (table: Table) => void;
}

interface TableFilters {
  statut: string;
  zone: string;
  capacite: string;
  search: string;
}

const getTableStatusColor = (statut: string) => {
  switch (statut) {
    case 'Libre':
      return 'bg-green-100 border-green-300 text-green-700 hover:bg-green-200';
    case 'Occupée':
      return 'bg-red-100 border-red-300 text-red-700 hover:bg-red-200';
    case 'Réservée':
      return 'bg-orange-100 border-orange-300 text-orange-700 hover:bg-orange-200';
    case 'Maintenance':
      return 'bg-gray-100 border-gray-300 text-gray-500 hover:bg-gray-200';
    default:
      return 'bg-gray-100 border-gray-300 text-gray-500';
  }
};

const getTableStatusIcon = (statut: string) => {
  switch (statut) {
    case 'Libre':
      return '✓';
    case 'Occupée':
      return '●';
    case 'Réservée':
      return '◐';
    case 'Maintenance':
      return '⚠';
    default:
      return '?';
  }
};

export const TableManager: React.FC<TableManagerProps> = ({
  serviceId,
  tables,
  onTableSelect,
  selectedTable,
  loading = false,
  onCreateTable,
  onEditTable,
  onDeleteTable
}) => {
  const [filters, setFilters] = useState<TableFilters>({
    statut: 'all',
    zone: 'all',
    capacite: 'all',
    search: ''
  });
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Extraction des zones et capacités uniques
  const zones = useMemo(() => {
    const uniqueZones = [...new Set(tables.map(t => t.zone).filter(Boolean))];
    return uniqueZones.sort();
  }, [tables]);

  const capacites = useMemo(() => {
    const uniqueCapacites = [...new Set(tables.map(t => t.capacite))];
    return uniqueCapacites.sort((a, b) => a - b);
  }, [tables]);

  // Filtrage des tables
  const filteredTables = useMemo(() => {
    return tables.filter(table => {
      // Filtre par statut
      if (filters.statut !== 'all' && table.statut !== filters.statut) {
        return false;
      }
      
      // Filtre par zone
      if (filters.zone !== 'all' && table.zone !== filters.zone) {
        return false;
      }
      
      // Filtre par capacité
      if (filters.capacite !== 'all' && table.capacite.toString() !== filters.capacite) {
        return false;
      }
      
      // Filtre par recherche
      if (filters.search && !table.numero.toLowerCase().includes(filters.search.toLowerCase())) {
        return false;
      }
      
      return true;
    });
  }, [tables, filters]);

  // Statistiques des tables
  const tableStats = useMemo(() => {
    const stats = {
      total: tables.length,
      libre: 0,
      occupee: 0,
      reservee: 0,
      maintenance: 0
    };
    
    tables.forEach(table => {
      switch (table.statut) {
        case 'Libre':
          stats.libre++;
          break;
        case 'Occupée':
          stats.occupee++;
          break;
        case 'Réservée':
          stats.reservee++;
          break;
        case 'Maintenance':
          stats.maintenance++;
          break;
      }
    });
    
    return stats;
  }, [tables]);

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="grid grid-cols-3 gap-3">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="h-20 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-lg font-semibold">Gestion des Tables</h2>
          <p className="text-sm text-gray-500">
            {filteredTables.length} table{filteredTables.length > 1 ? 's' : ''} 
            {filteredTables.length !== tables.length && ` sur ${tables.length}`}
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`
              p-2 rounded-lg transition-colors
              ${showFilters ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}
            `}
          >
            <Filter className="w-4 h-4" />
          </button>
          
          {onCreateTable && (
            <button
              onClick={onCreateTable}
              className="p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              <Plus className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-4 gap-2 mb-4">
        <div className="text-center p-2 bg-green-50 rounded-lg">
          <div className="text-lg font-semibold text-green-700">{tableStats.libre}</div>
          <div className="text-xs text-green-600">Libres</div>
        </div>
        <div className="text-center p-2 bg-red-50 rounded-lg">
          <div className="text-lg font-semibold text-red-700">{tableStats.occupee}</div>
          <div className="text-xs text-red-600">Occupées</div>
        </div>
        <div className="text-center p-2 bg-orange-50 rounded-lg">
          <div className="text-lg font-semibold text-orange-700">{tableStats.reservee}</div>
          <div className="text-xs text-orange-600">Réservées</div>
        </div>
        <div className="text-center p-2 bg-gray-50 rounded-lg">
          <div className="text-lg font-semibold text-gray-700">{tableStats.maintenance}</div>
          <div className="text-xs text-gray-600">Maintenance</div>
        </div>
      </div>

      {/* Filtres */}
      {showFilters && (
        <div className="mb-4 p-4 bg-gray-50 rounded-lg space-y-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Rechercher une table..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <div className="grid grid-cols-3 gap-3">
            <select
              value={filters.statut}
              onChange={(e) => setFilters(prev => ({ ...prev, statut: e.target.value }))}
              className="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Tous les statuts</option>
              <option value="Libre">Libre</option>
              <option value="Occupée">Occupée</option>
              <option value="Réservée">Réservée</option>
              <option value="Maintenance">Maintenance</option>
            </select>
            
            <select
              value={filters.zone}
              onChange={(e) => setFilters(prev => ({ ...prev, zone: e.target.value }))}
              className="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Toutes les zones</option>
              {zones.map(zone => (
                <option key={zone} value={zone}>{zone}</option>
              ))}
            </select>
            
            <select
              value={filters.capacite}
              onChange={(e) => setFilters(prev => ({ ...prev, capacite: e.target.value }))}
              className="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">Toutes capacités</option>
              {capacites.map(capacite => (
                <option key={capacite} value={capacite.toString()}>{capacite} pers.</option>
              ))}
            </select>
          </div>
        </div>
      )}

      {/* Liste des tables */}
      {filteredTables.length === 0 ? (
        <div className="text-center py-8">
          <Users className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-500">Aucune table trouvée</p>
          {filters.search || filters.statut !== 'all' || filters.zone !== 'all' || filters.capacite !== 'all' ? (
            <button
              onClick={() => setFilters({ statut: 'all', zone: 'all', capacite: 'all', search: '' })}
              className="text-blue-500 hover:text-blue-600 text-sm mt-2"
            >
              Réinitialiser les filtres
            </button>
          ) : onCreateTable ? (
            <button
              onClick={onCreateTable}
              className="text-blue-500 hover:text-blue-600 text-sm mt-2"
            >
              Créer la première table
            </button>
          ) : null}
        </div>
      ) : (
        <div className="grid grid-cols-3 gap-3 max-h-96 overflow-y-auto">
          {filteredTables.map((table) => {
            const isSelected = selectedTable?.table_id === table.table_id;
            
            return (
              <div
                key={table.table_id}
                className={`
                  relative p-3 rounded-lg border-2 cursor-pointer transition-all duration-200
                  ${isSelected 
                    ? 'border-blue-500 bg-blue-50 shadow-md' 
                    : getTableStatusColor(table.statut)
                  }
                `}
                onClick={() => onTableSelect(table)}
              >
                {/* Menu actions */}
                {(onEditTable || onDeleteTable) && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      // Ici on pourrait ouvrir un menu contextuel
                    }}
                    className="absolute top-2 right-2 p-1 rounded hover:bg-white/50"
                  >
                    <MoreVertical className="w-3 h-3" />
                  </button>
                )}
                
                {/* Contenu de la table */}
                <div className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    <span className="text-lg font-bold">{table.numero}</span>
                    <span className="ml-1 text-sm">{getTableStatusIcon(table.statut)}</span>
                  </div>
                  
                  <div className="flex items-center justify-center text-xs text-gray-600 mb-1">
                    <Users className="w-3 h-3 mr-1" />
                    <span>{table.capacite}</span>
                  </div>
                  
                  {table.zone && (
                    <div className="text-xs text-gray-500 truncate">
                      {table.zone}
                    </div>
                  )}
                  
                  {table.commande_active && (
                    <div className="flex items-center justify-center text-xs text-blue-600 mt-1">
                      <Clock className="w-3 h-3 mr-1" />
                      <span>Commande active</span>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};
