import React, { useState } from 'react';
import { 
  X, 
  <PERSON>tings, 
  Power, 
  PowerOff, 
  Clock, 
  Euro, 
  Users, 
  Bell,
  Minimize2,
  Maximize2
} from 'lucide-react';
import type { ServiceComplexe, SessionCaisse } from '../../services';

interface POSHeaderProps {
  selectedService: ServiceComplexe | null;
  sessionActive: SessionCaisse | null;
  onServiceChange: (service: ServiceComplexe) => void;
  onSessionToggle: () => void;
  onClose: () => void;
  onSettings?: () => void;
  isFullscreen?: boolean;
  onToggleFullscreen?: () => void;
  notifications?: number;
  onNotificationsClick?: () => void;
}

export const POSHeader: React.FC<POSHeaderProps> = ({
  selectedService,
  sessionActive,
  onServiceChange,
  onSessionToggle,
  onClose,
  onSettings,
  isFullscreen = false,
  onToggleFullscreen,
  notifications = 0,
  onNotificationsClick
}) => {
  const [showSessionDetails, setShowSessionDetails] = useState(false);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getSessionDuration = (startTime: string) => {
    const start = new Date(startTime);
    const now = new Date();
    const diffMs = now.getTime() - start.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHours > 0) {
      return `${diffHours}h ${diffMinutes}min`;
    }
    return `${diffMinutes}min`;
  };

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Section gauche - Informations du service */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-semibold text-lg">POS</span>
            </div>
            
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                {selectedService ? selectedService.nom : 'Point de Vente'}
              </h1>
              <p className="text-sm text-gray-500">
                {selectedService ? selectedService.type_service : 'Aucun service sélectionné'}
                {selectedService?.emplacement && ` • ${selectedService.emplacement}`}
              </p>
            </div>
          </div>

          {/* Indicateur de session */}
          {selectedService && (
            <div className="relative">
              <button
                onClick={() => setShowSessionDetails(!showSessionDetails)}
                className={`
                  flex items-center space-x-2 px-3 py-2 rounded-lg border transition-colors
                  ${sessionActive 
                    ? 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100' 
                    : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                  }
                `}
              >
                {sessionActive ? (
                  <Power className="w-4 h-4" />
                ) : (
                  <PowerOff className="w-4 h-4" />
                )}
                <span className="text-sm font-medium">
                  {sessionActive ? 'Session active' : 'Session fermée'}
                </span>
                {sessionActive && (
                  <div className="flex items-center text-xs">
                    <Clock className="w-3 h-3 mr-1" />
                    <span>{getSessionDuration(sessionActive.date_ouverture)}</span>
                  </div>
                )}
              </button>

              {/* Détails de la session */}
              {showSessionDetails && sessionActive && (
                <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-50">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-gray-900">Détails de la session</h3>
                      <button
                        onClick={() => setShowSessionDetails(false)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div>
                        <span className="text-gray-500">Ouverture:</span>
                        <div className="font-medium">{formatTime(sessionActive.date_ouverture)}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Durée:</span>
                        <div className="font-medium">{getSessionDuration(sessionActive.date_ouverture)}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Fonds initial:</span>
                        <div className="font-medium">{formatCurrency(sessionActive.fonds_ouverture)}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Ventes:</span>
                        <div className="font-medium">{formatCurrency(sessionActive.total_ventes || 0)}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Employé:</span>
                        <div className="font-medium">
                          {sessionActive.employe_prenom} {sessionActive.employe_nom}
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-500">POS:</span>
                        <div className="font-medium">{sessionActive.pos_nom}</div>
                      </div>
                    </div>

                    {sessionActive.notes && (
                      <div>
                        <span className="text-gray-500 text-sm">Notes:</span>
                        <p className="text-sm text-gray-700 mt-1">{sessionActive.notes}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Section droite - Actions */}
        <div className="flex items-center space-x-3">
          {/* Notifications */}
          {onNotificationsClick && (
            <button
              onClick={onNotificationsClick}
              className="relative p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <Bell className="w-5 h-5" />
              {notifications > 0 && (
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  {notifications > 9 ? '9+' : notifications}
                </span>
              )}
            </button>
          )}

          {/* Toggle fullscreen */}
          {onToggleFullscreen && (
            <button
              onClick={onToggleFullscreen}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
              title={isFullscreen ? 'Quitter le plein écran' : 'Plein écran'}
            >
              {isFullscreen ? (
                <Minimize2 className="w-5 h-5" />
              ) : (
                <Maximize2 className="w-5 h-5" />
              )}
            </button>
          )}

          {/* Session toggle */}
          {selectedService && (
            <button
              onClick={onSessionToggle}
              className={`
                flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors
                ${sessionActive
                  ? 'bg-red-500 text-white hover:bg-red-600'
                  : 'bg-green-500 text-white hover:bg-green-600'
                }
              `}
            >
              {sessionActive ? (
                <>
                  <PowerOff className="w-4 h-4" />
                  <span>Fermer session</span>
                </>
              ) : (
                <>
                  <Power className="w-4 h-4" />
                  <span>Ouvrir session</span>
                </>
              )}
            </button>
          )}

          {/* Paramètres */}
          {onSettings && (
            <button
              onClick={onSettings}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
              title="Paramètres"
            >
              <Settings className="w-5 h-5" />
            </button>
          )}

          {/* Fermer */}
          <button
            onClick={onClose}
            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
            title="Fermer le POS"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Barre d'informations rapides */}
      {selectedService && sessionActive && (
        <div className="mt-4 flex items-center justify-between text-sm text-gray-600 bg-gray-50 rounded-lg px-4 py-2">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-1">
              <Euro className="w-4 h-4" />
              <span>Ventes: {formatCurrency(sessionActive.total_ventes || 0)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="w-4 h-4" />
              <span>Ouvert depuis: {getSessionDuration(sessionActive.date_ouverture)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Users className="w-4 h-4" />
              <span>Employé: {sessionActive.employe_prenom} {sessionActive.employe_nom}</span>
            </div>
          </div>
          
          <div className="text-xs text-gray-500">
            Session #{sessionActive.session_id} • POS: {sessionActive.pos_nom}
          </div>
        </div>
      )}
    </header>
  );
};
