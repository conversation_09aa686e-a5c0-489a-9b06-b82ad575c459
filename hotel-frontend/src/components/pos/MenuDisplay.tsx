import React, { useState, useMemo, useEffect } from 'react';
import { Search, Plus, Minus, AlertTriangle, Clock, Star } from 'lucide-react';
import type { MenuCategory, MenuItem } from '../../services/types';
import { posService } from '../../services/pos.service';

interface MenuDisplayProps {
  serviceId: number;
  categories: MenuCategory[];
  onItemAdd: (item: MenuItem, quantity: number) => void;
  searchTerm?: string;
  loading?: boolean;
}

interface MenuFilters {
  selectedCategory: string;
  showOnlyAvailable: boolean;
  sortBy: 'name' | 'price' | 'popularity';
  priceRange: { min: number; max: number };
}

interface StockStatus {
  available: boolean;
  quantity: number;
  alert: boolean;
}

const MenuItemCard: React.FC<{
  item: MenuItem;
  onAdd: (quantity: number) => void;
  stockStatus?: StockStatus;
}> = ({ item, onAdd, stockStatus }) => {
  const [quantity, setQuantity] = useState(1);
  const isAvailable = stockStatus?.available ?? (item.stock_disponible > 0);

  return (
    <div className={`
      p-4 rounded-lg border transition-all duration-200 hover:shadow-md
      ${isAvailable 
        ? 'bg-white border-gray-200 hover:border-blue-300' 
        : 'bg-gray-50 border-gray-200 opacity-60'
      }
    `}>
      {/* Image du produit */}
      {item.image_url ? (
        <img
          src={item.image_url}
          alt={item.nom}
          className="w-full h-32 object-cover rounded-lg mb-3"
        />
      ) : (
        <div className="w-full h-32 bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
          <span className="text-gray-400 text-sm">Pas d'image</span>
        </div>
      )}

      {/* Informations du produit */}
      <div className="space-y-2">
        <div className="flex justify-between items-start">
          <h3 className="font-medium text-gray-900">{item.nom}</h3>
          <span className="text-sm font-medium text-gray-900">
            {item.prix_vente.toFixed(2)} FCFA
          </span>
        </div>

        {item.description && (
          <p className="text-sm text-gray-500 line-clamp-2">{item.description}</p>
        )}

        {/* Statut du stock */}
        {stockStatus && (
          <div className="flex items-center space-x-2 text-sm">
            {stockStatus.alert ? (
              <AlertTriangle className="w-4 h-4 text-yellow-500" />
            ) : null}
            <span className={stockStatus.alert ? 'text-yellow-600' : 'text-gray-500'}>
              Stock: {stockStatus.quantity} {item.unite_mesure}
            </span>
          </div>
        )}

        {/* Contrôles de quantité */}
        <div className="flex items-center justify-between pt-2">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setQuantity(Math.max(1, quantity - 1))}
              className="p-1 rounded-full hover:bg-gray-100"
              disabled={!isAvailable}
            >
              <Minus className="w-4 h-4 text-gray-500" />
            </button>
            <span className="w-8 text-center">{quantity}</span>
            <button
              onClick={() => setQuantity(quantity + 1)}
              className="p-1 rounded-full hover:bg-gray-100"
              disabled={!isAvailable}
            >
              <Plus className="w-4 h-4 text-gray-500" />
            </button>
          </div>
          <button
            onClick={() => onAdd(quantity)}
            disabled={!isAvailable}
            className={`
              px-3 py-1 rounded-lg text-sm font-medium
              ${isAvailable
                ? 'bg-blue-500 text-white hover:bg-blue-600'
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }
            `}
          >
            Ajouter
          </button>
        </div>
      </div>
    </div>
  );
};

export const MenuDisplay: React.FC<MenuDisplayProps> = ({
  serviceId,
  categories,
  onItemAdd,
  searchTerm: externalSearchTerm,
  loading = false
}) => {
  const [localSearchTerm, setLocalSearchTerm] = useState(externalSearchTerm || '');
  const [filters, setFilters] = useState<MenuFilters>({
    selectedCategory: 'all',
    showOnlyAvailable: true,
    sortBy: 'name',
    priceRange: { min: 0, max: 1000 }
  });
  const [stockStatuses, setStockStatuses] = useState<Record<number, StockStatus>>({});

  // Mettre à jour le terme de recherche local si le terme externe change
  useEffect(() => {
    if (externalSearchTerm !== undefined) {
      setLocalSearchTerm(externalSearchTerm);
    }
  }, [externalSearchTerm]);

  // Vérifier la disponibilité des produits
  useEffect(() => {
    const checkProductAvailability = async () => {
      const allItems = categories.flatMap(cat => cat.items);
      const statuses: Record<number, StockStatus> = {};

      for (const item of allItems) {
        try {
          const result = await posService.checkProductAvailability(item.produit_id);
          statuses[item.produit_id] = {
            available: result.available,
            quantity: result.quantity,
            alert: result.alert
          };
        } catch (error) {
          console.error(`Erreur lors de la vérification du stock pour ${item.nom}:`, error);
          statuses[item.produit_id] = {
            available: item.stock_disponible > 0,
            quantity: item.stock_disponible,
            alert: false
          };
        }
      }

      setStockStatuses(statuses);
    };

    checkProductAvailability();
    // Vérifier toutes les 30 secondes
    const interval = setInterval(checkProductAvailability, 30000);
    return () => clearInterval(interval);
  }, [categories]);

  const filteredItems = useMemo(() => {
    let items = categories.flatMap(cat => cat.items);

    // Filtrage par recherche
    if (localSearchTerm) {
      items = items.filter(item =>
        item.nom.toLowerCase().includes(localSearchTerm.toLowerCase()) ||
        item.description?.toLowerCase().includes(localSearchTerm.toLowerCase())
      );
    }

    // Filtrage par catégorie
    if (filters.selectedCategory !== 'all') {
      items = items.filter(item => item.categorie === filters.selectedCategory);
    }

    // Filtrage par disponibilité
    if (filters.showOnlyAvailable) {
      items = items.filter(item => {
        const status = stockStatuses[item.produit_id];
        return status ? status.available : item.stock_disponible > 0;
      });
    }

    // Tri
    items.sort((a, b) => {
      switch (filters.sortBy) {
        case 'price': return a.prix_vente - b.prix_vente;
        case 'popularity': return (b.popularite || 0) - (a.popularite || 0);
        default: return a.nom.localeCompare(b.nom);
      }
    });

    return items;
  }, [categories, localSearchTerm, filters, stockStatuses]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">Menu</h2>
        <span className="text-sm text-gray-500">
          {filteredItems.length} produit{filteredItems.length > 1 ? 's' : ''}
        </span>
      </div>

      {/* Barre de recherche */}
      <div className="relative mb-4">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          placeholder="Rechercher un produit..."
          value={localSearchTerm}
          onChange={(e) => setLocalSearchTerm(e.target.value)}
          className="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Filtres */}
      <div className="mb-4 flex flex-wrap gap-4">
        <select
          value={filters.selectedCategory}
          onChange={(e) => setFilters(prev => ({ ...prev, selectedCategory: e.target.value }))}
          className="px-3 py-1 border rounded text-sm"
        >
          <option value="all">Toutes catégories</option>
          {categories.map(cat => (
            <option key={cat.categorie_id} value={cat.nom}>
              {cat.nom}
            </option>
          ))}
        </select>

        <div className="flex items-center space-x-4">
          <label className="flex items-center text-sm">
            <input
              type="checkbox"
              checked={filters.showOnlyAvailable}
              onChange={(e) => setFilters(prev => ({ ...prev, showOnlyAvailable: e.target.checked }))}
              className="mr-2"
            />
            Disponibles uniquement
          </label>

          <select
            value={filters.sortBy}
            onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as any }))}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="name">Nom</option>
            <option value="price">Prix</option>
            <option value="popularity">Popularité</option>
          </select>
        </div>
      </div>

      {/* Liste des produits */}
      {filteredItems.length === 0 ? (
        <div className="text-center py-8">
          <Search className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-500">Aucun produit trouvé</p>
          <button
            onClick={() => {
              setLocalSearchTerm('');
              setFilters({
                selectedCategory: 'all',
                showOnlyAvailable: false,
                sortBy: 'name',
                priceRange: { min: 0, max: 1000 }
              });
            }}
            className="text-blue-500 hover:text-blue-600 text-sm mt-2"
          >
            Réinitialiser les filtres
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-2 gap-4 max-h-96 overflow-y-auto">
          {filteredItems.map((item) => (
            <MenuItemCard
              key={item.produit_id}
              item={item}
              onAdd={(quantity) => onItemAdd(item, quantity)}
              stockStatus={stockStatuses[item.produit_id]}
            />
          ))}
        </div>
      )}
    </div>
  );
};
