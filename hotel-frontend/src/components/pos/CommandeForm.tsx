import React, { useState } from 'react';
import { CheckCircle, XCircle } from 'lucide-react';
import type { Commande, CommandeItem } from '../../services/types';
import { posService } from '../../services/pos.service';

interface CommandeFormProps {
  commande: Commande;
  onValidate: () => void;
  onCancel: () => void;
  loading?: boolean;
}

interface ValidationState {
  isValidating: boolean;
  isValid: boolean;
  errors: Array<{
    productId: number;
    message: string;
    alternative?: any;
  }>;
}

export const CommandeForm: React.FC<CommandeFormProps> = ({
  commande,
  onValidate,
  onCancel,
  loading = false
}) => {
  const [validationState, setValidationState] = useState<ValidationState>({
    isValidating: false,
    isValid: true,
    errors: []
  });

  const validateStock = async () => {
    if (!commande || !commande.items || commande.items.length === 0) {
      return;
    }

    setValidationState(prev => ({ ...prev, isValidating: true }));

    try {
      // Préparer les items pour la validation
      const items = commande.items.map(item => ({
        produit_id: item.produit_id,
        quantite: item.quantite
      }));

      // Appeler le service pour valider le stock
      const result = await posService.validateOrderStock(items);

      setValidationState({
        isValidating: false,
        isValid: result.valide,
        errors: result.erreurs.map(error => ({
          productId: error.produit_id,
          message: error.message,
          alternative: error.alternatives?.[0] // Prendre la première alternative s'il y en a
        }))
      });

      // Si la validation est réussie, on peut procéder
      if (result.valide) {
        onValidate();
      }
    } catch (error) {
      console.error('Erreur lors de la validation du stock:', error);
      setValidationState({
        isValidating: false,
        isValid: false,
        errors: [{
          productId: 0,
          message: 'Erreur lors de la vérification du stock. Veuillez réessayer.'
        }]
      });
    }
  };

  const getErrorMessage = (item: CommandeItem) => {
    const error = validationState.errors.find(e => e.productId === item.produit_id);
    return error?.message;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      {/* En-tête */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">Validation de la commande</h2>
        <span className="text-sm text-gray-500">
          {commande.items.length} article{commande.items.length > 1 ? 's' : ''}
        </span>
      </div>

      {/* Messages de validation */}
      {validationState.errors.length > 0 && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start space-x-3">
            <XCircle className="w-5 h-5 text-red-500 mt-0.5" />
            <div className="flex-1">
              <h3 className="font-medium text-red-800 mb-2">Problèmes de stock détectés</h3>
              <ul className="space-y-2">
                {validationState.errors.map((error, index) => {
                  const item = commande.items.find(i => i.produit_id === error.productId);
                  return (
                    <li key={index} className="text-sm text-red-700">
                      {item?.nom}: {error.message}
                      {error.alternative && (
                        <div className="mt-1 text-red-600">
                          Alternative suggérée: {error.alternative.nom}
                        </div>
                      )}
                    </li>
                  );
                })}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Liste des articles */}
      <div className="space-y-3 mb-6">
        {commande.items.map((item) => {
          const error = getErrorMessage(item);
          return (
            <div
              key={item.produit_id}
              className={`p-3 rounded-lg border ${
                error ? 'bg-red-50 border-red-200' : 'bg-gray-50 border-gray-200'
              }`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">{item.nom}</h4>
                  <div className="text-sm text-gray-500">
                    Quantité: {item.quantite} × {item.prix_unitaire.toFixed(2)} FCFA
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium text-gray-900">
                    {item.montant_ligne.toFixed(2)} FCFA
                  </div>
                  {error && (
                    <div className="text-sm text-red-600 mt-1">{error}</div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Total */}
      <div className="border-t pt-4 mb-6">
        <div className="flex justify-between text-lg font-semibold">
          <span>Total</span>
          <span>{commande.montant_total.toFixed(2)} FCFA</span>
        </div>
      </div>

      {/* Actions */}
      <div className="flex space-x-3">
        <button
          onClick={onCancel}
          disabled={loading || validationState.isValidating}
          className="flex-1 py-3 px-4 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          Annuler
        </button>
        <button
          onClick={validateStock}
          disabled={loading || validationState.isValidating || commande.items.length === 0}
          className="flex-1 py-3 px-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
        >
          {loading || validationState.isValidating ? (
            <div className="flex items-center">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              Vérification...
            </div>
          ) : (
            <>
              <CheckCircle className="w-4 h-4 mr-2" />
              Valider la commande
            </>
          )}
        </button>
      </div>
    </div>
  );
}; 