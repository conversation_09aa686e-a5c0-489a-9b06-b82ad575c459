import React, { useState, useMemo } from 'react';
import { Plus, Minus, Trash2, ShoppingCart, CreditCard, Receipt, MessageSquare } from 'lucide-react';
import type { Commande, CommandeItem } from '../../services';

interface OrderCartProps {
  commande: Commande | null;
  onItemUpdate: (itemId: number, quantity: number) => void;
  onItemRemove: (itemId: number) => void;
  onOrderValidate: () => void;
  onPayment: () => void;
  onAddNote?: (note: string) => void;
  loading?: boolean;
  disabled?: boolean;
}

const OrderItemRow: React.FC<{
  item: CommandeItem;
  onQuantityChange: (quantity: number) => void;
  onRemove: () => void;
  disabled?: boolean;
}> = ({ item, onQuantityChange, onRemove, disabled = false }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [localQuantity, setLocalQuantity] = useState(item.quantite);

  const handleQuantitySubmit = () => {
    if (localQuantity !== item.quantite && localQuantity > 0) {
      onQuantityChange(localQuantity);
    }
    setIsEditing(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleQuantitySubmit();
    } else if (e.key === 'Escape') {
      setLocalQuantity(item.quantite);
      setIsEditing(false);
    }
  };

  return (
    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
      <div className="flex-1 min-w-0">
        <h4 className="font-medium text-gray-900 truncate">{item.nom}</h4>
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <span>{item.prix_unitaire.toFixed(2)} FCFA × {item.quantite}</span>
          {item.commentaires && (
            <div className="flex items-center">
              <MessageSquare className="w-3 h-3 mr-1" />
              <span className="truncate max-w-20">{item.commentaires}</span>
            </div>
          )}
        </div>
        {item.statut !== 'En attente' && (
          <div className="text-xs text-blue-600 mt-1">
            Statut: {item.statut}
          </div>
        )}
      </div>

      <div className="flex items-center space-x-2 ml-4">
        {/* Contrôles de quantité */}
        <div className="flex items-center space-x-1">
          <button
            onClick={() => onQuantityChange(Math.max(1, item.quantite - 1))}
            disabled={disabled || item.quantite <= 1}
            className="p-1 rounded bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Minus className="w-3 h-3" />
          </button>
          
          {isEditing ? (
            <input
              type="number"
              value={localQuantity}
              onChange={(e) => setLocalQuantity(Math.max(1, parseInt(e.target.value) || 1))}
              onBlur={handleQuantitySubmit}
              onKeyDown={handleKeyPress}
              className="w-12 text-center text-sm border rounded px-1 py-0.5"
              min="1"
              autoFocus
            />
          ) : (
            <button
              onClick={() => setIsEditing(true)}
              disabled={disabled}
              className="w-8 text-center text-sm font-medium hover:bg-gray-200 rounded px-1 py-0.5"
            >
              {item.quantite}
            </button>
          )}
          
          <button
            onClick={() => onQuantityChange(item.quantite + 1)}
            disabled={disabled}
            className="p-1 rounded bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Plus className="w-3 h-3" />
          </button>
        </div>

        {/* Prix total */}
        <div className="text-right min-w-16">
          <div className="font-semibold text-gray-900">
            {item.montant_ligne.toFixed(2)} FCFA
          </div>
        </div>

        {/* Bouton supprimer */}
        <button
          onClick={onRemove}
          disabled={disabled}
          className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Trash2 className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export const OrderCart: React.FC<OrderCartProps> = ({
  commande,
  onItemUpdate,
  onItemRemove,
  onOrderValidate,
  onPayment,
  onAddNote,
  loading = false,
  disabled = false
}) => {
  const [notes, setNotes] = useState(commande?.notes || '');
  const [showNotes, setShowNotes] = useState(false);

  // Calculs des totaux
  const totals = useMemo(() => {
    if (!commande || !commande.items) {
      return {
        subtotal: 0,
        tva: 0,
        total: 0,
        itemCount: 0
      };
    }

    const subtotal = commande.items.reduce((sum, item) => sum + item.montant_ligne, 0);
    const tva = commande.montant_tva || subtotal * 0.20; // TVA par défaut 20%
    const total = commande.montant_total || subtotal + tva;
    const itemCount = commande.items.reduce((sum, item) => sum + item.quantite, 0);

    return { subtotal, tva, total, itemCount };
  }, [commande]);

  const handleNotesSubmit = () => {
    if (onAddNote && notes !== commande?.notes) {
      onAddNote(notes);
    }
    setShowNotes(false);
  };

  const canValidate = commande && commande.items.length > 0 && commande.statut === 'En cours';
  const canPay = commande && commande.statut === 'Servie';

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-16 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <ShoppingCart className="w-5 h-5 text-gray-600" />
          <h2 className="text-lg font-semibold">Commande</h2>
          {commande && (
            <span className="text-sm text-gray-500">
              #{commande.numero_commande || commande.commande_id}
            </span>
          )}
        </div>
        
        {totals.itemCount > 0 && (
          <span className="px-2 py-1 bg-blue-100 text-blue-700 text-sm rounded-full">
            {totals.itemCount} article{totals.itemCount > 1 ? 's' : ''}
          </span>
        )}
      </div>

      {/* Informations de la commande */}
      {commande && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="text-gray-500">Table:</span>
              <span className="ml-2 font-medium">
                {commande.table_numero || `Table ${commande.table_id}` || 'À emporter'}
              </span>
            </div>
            <div>
              <span className="text-gray-500">Statut:</span>
              <span className={`ml-2 font-medium ${
                commande.statut === 'En cours' ? 'text-orange-600' :
                commande.statut === 'Servie' ? 'text-green-600' :
                commande.statut === 'Payée' ? 'text-blue-600' :
                'text-gray-600'
              }`}>
                {commande.statut}
              </span>
            </div>
            <div>
              <span className="text-gray-500">Type:</span>
              <span className="ml-2 font-medium">{commande.type_commande}</span>
            </div>
            <div>
              <span className="text-gray-500">Heure:</span>
              <span className="ml-2 font-medium">
                {new Date(commande.date_commande).toLocaleTimeString('fr-FR', {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Liste des items */}
      <div className="space-y-2 mb-4 max-h-64 overflow-y-auto">
        {!commande || commande.items.length === 0 ? (
          <div className="text-center py-8">
            <ShoppingCart className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-500">Aucun article dans la commande</p>
            <p className="text-sm text-gray-400 mt-1">
              Sélectionnez des produits dans le menu
            </p>
          </div>
        ) : (
          commande.items.map((item) => (
            <OrderItemRow
              key={item.commande_item_id || item.produit_id}
              item={item}
              onQuantityChange={(quantity) => 
                onItemUpdate(item.commande_item_id || item.produit_id, quantity)
              }
              onRemove={() => 
                onItemRemove(item.commande_item_id || item.produit_id)
              }
              disabled={disabled}
            />
          ))
        )}
      </div>

      {/* Notes */}
      {onAddNote && (
        <div className="mb-4">
          {showNotes ? (
            <div className="space-y-2">
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Notes pour la cuisine..."
                className="w-full p-2 border rounded-lg text-sm resize-none"
                rows={2}
              />
              <div className="flex space-x-2">
                <button
                  onClick={handleNotesSubmit}
                  className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
                >
                  Sauvegarder
                </button>
                <button
                  onClick={() => {
                    setNotes(commande?.notes || '');
                    setShowNotes(false);
                  }}
                  className="px-3 py-1 bg-gray-200 text-gray-700 text-sm rounded hover:bg-gray-300"
                >
                  Annuler
                </button>
              </div>
            </div>
          ) : (
            <button
              onClick={() => setShowNotes(true)}
              className="flex items-center text-sm text-gray-600 hover:text-gray-800"
            >
              <MessageSquare className="w-4 h-4 mr-1" />
              {commande?.notes ? 'Modifier les notes' : 'Ajouter des notes'}
            </button>
          )}
        </div>
      )}

      {/* Totaux */}
      {commande && commande.items.length > 0 && (
        <div className="border-t pt-4 space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Sous-total:</span>
            <span className="font-medium">{totals.subtotal.toFixed(2)} FCFA</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">TVA (20%):</span>
            <span className="font-medium">{totals.tva.toFixed(2)} FCFA</span>
          </div>
          <div className="flex justify-between text-lg font-semibold border-t pt-2">
            <span>Total:</span>
            <span>{totals.total.toFixed(2)} FCFA</span>
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="mt-6 space-y-2">
        {canValidate && (
          <button
            onClick={onOrderValidate}
            disabled={disabled || loading}
            className="
              w-full py-3 bg-orange-500 text-white rounded-lg font-medium
              hover:bg-orange-600 disabled:bg-gray-300 disabled:cursor-not-allowed
              transition-colors duration-200 flex items-center justify-center
            "
          >
            <Receipt className="w-4 h-4 mr-2" />
            Valider la commande
          </button>
        )}
        
        {canPay && (
          <button
            onClick={onPayment}
            disabled={disabled || loading}
            className="
              w-full py-3 bg-green-500 text-white rounded-lg font-medium
              hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed
              transition-colors duration-200 flex items-center justify-center
            "
          >
            <CreditCard className="w-4 h-4 mr-2" />
            Procéder au paiement
          </button>
        )}
        
        {commande && commande.items.length > 0 && commande.statut === 'En cours' && (
          <button
            disabled={disabled || loading}
            className="
              w-full py-2 bg-gray-100 text-gray-700 rounded-lg
              hover:bg-gray-200 disabled:bg-gray-50 disabled:cursor-not-allowed
              transition-colors duration-200
            "
          >
            Annuler la commande
          </button>
        )}
      </div>
    </div>
  );
};
