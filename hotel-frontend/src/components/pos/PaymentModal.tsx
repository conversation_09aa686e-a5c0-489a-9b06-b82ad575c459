import React, { useState, useEffect } from 'react';
import { CreditCard, Banknote, Calculator, Receipt, X, Printer } from 'lucide-react';
import type { Commande, PaymentData } from '../../services';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  commande: Commande;
  onPaymentComplete: (paymentData: PaymentData) => Promise<void>;
}

type PaymentMode = 'CARTE' | 'ESPECES' | 'CHEQUE' | 'VIREMENT';

interface PaymentFormData {
  mode: PaymentMode;
  montantRecu: number;
  reference: string;
  notes: string;
}

export const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  commande,
  onPaymentComplete
}) => {
  const [formData, setFormData] = useState<PaymentFormData>({
    mode: 'CARTE',
    montantRecu: commande.montant_total,
    reference: '',
    notes: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showReceipt, setShowReceipt] = useState(false);

  // Réinitialiser le formulaire quand la commande change
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      montantRecu: commande.montant_total
    }));
  }, [commande.montant_total]);

  if (!isOpen) return null;

  const montantTotal = commande.montant_total;
  const montantRecu = formData.montantRecu;
  const monnaieARendu = formData.mode === 'ESPECES' ? Math.max(0, montantRecu - montantTotal) : 0;
  const paiementValide = montantRecu >= montantTotal;

  const paymentModes = [
    { value: 'CARTE' as PaymentMode, label: 'Carte bancaire', icon: CreditCard, color: 'blue' },
    { value: 'ESPECES' as PaymentMode, label: 'Espèces', icon: Banknote, color: 'green' },
    { value: 'CHEQUE' as PaymentMode, label: 'Chèque', icon: Receipt, color: 'purple' },
    { value: 'VIREMENT' as PaymentMode, label: 'Virement', icon: Calculator, color: 'orange' }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!paiementValide) return;

    setLoading(true);
    setError(null);

    try {
      const paymentData: PaymentData = {
        montant: montantTotal,
        mode_paiement: formData.mode,
        montant_recu: formData.mode === 'ESPECES' ? montantRecu : undefined,
        monnaie_rendue: formData.mode === 'ESPECES' ? monnaieARendu : undefined,
        reference: formData.reference || undefined,
        notes: formData.notes || undefined
      };

      await onPaymentComplete(paymentData);
      setShowReceipt(true);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const quickAmounts = [
    montantTotal,
    Math.ceil(montantTotal / 5) * 5, // Arrondi au 5FCFA supérieur
    Math.ceil(montantTotal / 10) * 10, // Arrondi au 10FCFA supérieur
    Math.ceil(montantTotal / 20) * 20, // Arrondi au 20FCFA supérieur
    50, 100, 200
  ].filter((amount, index, arr) => arr.indexOf(amount) === index && amount >= montantTotal);

  if (showReceipt) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
          <div className="p-6 text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Receipt className="w-8 h-8 text-green-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Paiement effectué</h2>
            <p className="text-gray-600 mb-6">
              Le paiement de {formatCurrency(montantTotal)} a été traité avec succès
            </p>
            
            <div className="flex space-x-3">
              <button
                onClick={handlePrint}
                className="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center justify-center"
              >
                <Printer className="w-4 h-4 mr-2" />
                Imprimer
              </button>
              <button
                onClick={onClose}
                className="flex-1 py-2 px-4 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
              >
                Fermer
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <CreditCard className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold">Paiement</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Récapitulatif de la commande */}
        <div className="p-6 bg-gray-50 border-b">
          <h3 className="font-medium text-gray-900 mb-3">Récapitulatif de la commande</h3>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">Commande #{commande.numero_commande || commande.commande_id}</span>
              <span className="text-gray-500">
                {commande.table_numero ? `Table ${commande.table_numero}` : commande.type_commande}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">Articles ({commande.items.length})</span>
              <span className="text-gray-500">
                {commande.items.reduce((sum, item) => sum + item.quantite, 0)} unités
              </span>
            </div>
            <div className="flex justify-between text-lg font-semibold pt-2 border-t">
              <span>Total à payer</span>
              <span className="text-blue-600">{formatCurrency(montantTotal)}</span>
            </div>
          </div>
        </div>

        {/* Formulaire de paiement */}
        <form onSubmit={handleSubmit} className="p-6">
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}

          {/* Mode de paiement */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Mode de paiement
            </label>
            <div className="grid grid-cols-2 gap-3">
              {paymentModes.map((mode) => {
                const Icon = mode.icon;
                const isSelected = formData.mode === mode.value;
                
                return (
                  <button
                    key={mode.value}
                    type="button"
                    onClick={() => setFormData(prev => ({ 
                      ...prev, 
                      mode: mode.value,
                      montantRecu: mode.value === 'ESPECES' ? prev.montantRecu : montantTotal
                    }))}
                    className={`
                      p-4 rounded-lg border-2 transition-all duration-200 flex items-center space-x-3
                      ${isSelected 
                        ? `border-${mode.color}-500 bg-${mode.color}-50` 
                        : 'border-gray-200 hover:border-gray-300'
                      }
                    `}
                  >
                    <Icon className={`w-5 h-5 ${isSelected ? `text-${mode.color}-600` : 'text-gray-400'}`} />
                    <span className={`font-medium ${isSelected ? `text-${mode.color}-900` : 'text-gray-700'}`}>
                      {mode.label}
                    </span>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Montant reçu (espèces uniquement) */}
          {formData.mode === 'ESPECES' && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Montant reçu
              </label>
              <input
                type="number"
                step="0.01"
                min={montantTotal}
                value={formData.montantRecu}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  montantRecu: parseFloat(e.target.value) || 0 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
              
              {/* Montants rapides */}
              <div className="mt-3">
                <p className="text-sm text-gray-600 mb-2">Montants rapides:</p>
                <div className="flex flex-wrap gap-2">
                  {quickAmounts.slice(0, 6).map((amount) => (
                    <button
                      key={amount}
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, montantRecu: amount }))}
                      className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                    >
                      {formatCurrency(amount)}
                    </button>
                  ))}
                </div>
              </div>

              {/* Monnaie à rendre */}
              {monnaieARendu > 0 && (
                <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-yellow-800 font-medium">Monnaie à rendre:</span>
                    <span className="text-lg font-semibold text-yellow-900">
                      {formatCurrency(monnaieARendu)}
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Référence (carte, chèque, virement) */}
          {formData.mode !== 'ESPECES' && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Référence {formData.mode === 'CARTE' ? 'transaction' : 
                          formData.mode === 'CHEQUE' ? 'chèque' : 'virement'}
              </label>
              <input
                type="text"
                value={formData.reference}
                onChange={(e) => setFormData(prev => ({ ...prev, reference: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder={
                  formData.mode === 'CARTE' ? 'Numéro d\'autorisation' :
                  formData.mode === 'CHEQUE' ? 'Numéro de chèque' :
                  'Référence de virement'
                }
              />
            </div>
          )}

          {/* Notes */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes (optionnel)
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows={2}
              placeholder="Remarques sur le paiement..."
            />
          </div>

          {/* Actions */}
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 py-3 px-4 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={loading || !paiementValide}
              className="flex-1 py-3 px-4 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Traitement...
                </div>
              ) : (
                `Encaisser ${formatCurrency(montantTotal)}`
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
