import React, { useState } from 'react';
import { Power, PowerOff, Euro, Clock, Calculator, FileText, X } from 'lucide-react';
import type { SessionCaisse, PointDeVente } from '../../services';

interface SessionManagerProps {
  isOpen: boolean;
  onClose: () => void;
  session: SessionCaisse | null;
  pointDeVente: PointDeVente | null;
  onOpenSession: (fondsOuverture: number, notes?: string) => Promise<void>;
  onCloseSession: (fondsClôture: number, notes?: string) => Promise<void>;
}

interface SessionFormData {
  fonds: number;
  notes: string;
}

export const SessionManager: React.FC<SessionManagerProps> = ({
  isOpen,
  onClose,
  session,
  pointDeVente,
  onOpenSession,
  onCloseSession
}) => {
  const [formData, setFormData] = useState<SessionFormData>({
    fonds: session ? session.fonds_ouverture : 100,
    notes: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  if (!isOpen) return null;

  const isOpeningSession = !session;
  const isClosingSession = !!session;

  // Calculs pour la fermeture de session
  const sessionStats = session ? {
    duree: Math.floor((new Date().getTime() - new Date(session.date_ouverture).getTime()) / (1000 * 60)),
    ventesTotal: session.total_ventes || 0,
    ventesEspeces: session.total_especes || 0,
    ventesCartes: session.total_cartes || 0,
    ventesAutres: session.total_autres || 0,
    fondsPrevus: (session.fonds_ouverture || 0) + (session.total_especes || 0),
    ecartCalcule: 0 // Sera calculé avec les fonds de clôture
  } : null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (isOpeningSession) {
        await onOpenSession(formData.fonds, formData.notes);
      } else {
        await onCloseSession(formData.fonds, formData.notes);
      }
      onClose();
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}min`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            {isOpeningSession ? (
              <Power className="w-6 h-6 text-green-600" />
            ) : (
              <PowerOff className="w-6 h-6 text-red-600" />
            )}
            <h2 className="text-xl font-semibold">
              {isOpeningSession ? 'Ouverture de Session' : 'Fermeture de Session'}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Informations du POS */}
        {pointDeVente && (
          <div className="p-6 bg-gray-50 border-b">
            <h3 className="font-medium text-gray-900 mb-2">Point de Vente</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Nom:</span>
                <span className="ml-2 font-medium">{pointDeVente.nom}</span>
              </div>
              <div>
                <span className="text-gray-500">Emplacement:</span>
                <span className="ml-2 font-medium">{pointDeVente.emplacement}</span>
              </div>
              <div>
                <span className="text-gray-500">Service:</span>
                <span className="ml-2 font-medium">{pointDeVente.service_nom}</span>
              </div>
              <div>
                <span className="text-gray-500">Type:</span>
                <span className="ml-2 font-medium">{pointDeVente.type_service}</span>
              </div>
            </div>
          </div>
        )}

        {/* Statistiques de session (fermeture uniquement) */}
        {isClosingSession && sessionStats && (
          <div className="p-6 border-b">
            <h3 className="font-medium text-gray-900 mb-4 flex items-center">
              <Calculator className="w-4 h-4 mr-2" />
              Récapitulatif de la Session
            </h3>
            
            <div className="grid grid-cols-2 gap-6">
              {/* Informations générales */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-gray-700">Informations</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Ouverture:</span>
                    <span className="font-medium">
                      {new Date(session.date_ouverture).toLocaleString('fr-FR')}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Durée:</span>
                    <span className="font-medium">{formatDuration(sessionStats.duree)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Fonds initial:</span>
                    <span className="font-medium">{formatCurrency(session.fonds_ouverture)}</span>
                  </div>
                </div>
              </div>

              {/* Ventes */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-gray-700">Ventes</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Total ventes:</span>
                    <span className="font-medium text-green-600">
                      {formatCurrency(sessionStats.ventesTotal)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Espèces:</span>
                    <span className="font-medium">{formatCurrency(sessionStats.ventesEspeces)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Cartes:</span>
                    <span className="font-medium">{formatCurrency(sessionStats.ventesCartes)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Autres:</span>
                    <span className="font-medium">{formatCurrency(sessionStats.ventesAutres)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Calcul des fonds attendus */}
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="font-medium text-blue-900">Fonds attendus en caisse:</span>
                <span className="text-lg font-semibold text-blue-900">
                  {formatCurrency(sessionStats.fondsPrevus)}
                </span>
              </div>
              <p className="text-sm text-blue-700 mt-1">
                Fonds initial + Ventes en espèces
              </p>
            </div>
          </div>
        )}

        {/* Formulaire */}
        <form onSubmit={handleSubmit} className="p-6">
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}

          <div className="space-y-4">
            {/* Montant */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Euro className="w-4 h-4 inline mr-1" />
                {isOpeningSession ? 'Fonds d\'ouverture' : 'Fonds comptés en caisse'}
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.fonds}
                onChange={(e) => setFormData(prev => ({ ...prev, fonds: parseFloat(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="0.00"
                required
              />
              {isClosingSession && sessionStats && (
                <div className="mt-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Attendu:</span>
                    <span className="font-medium">{formatCurrency(sessionStats.fondsPrevus)}</span>
                  </div>
                  {formData.fonds > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-500">Écart:</span>
                      <span className={`font-medium ${
                        formData.fonds - sessionStats.fondsPrevus >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {formatCurrency(formData.fonds - sessionStats.fondsPrevus)}
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FileText className="w-4 h-4 inline mr-1" />
                Notes (optionnel)
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows={3}
                placeholder={isOpeningSession 
                  ? "Notes d'ouverture..." 
                  : "Observations, incidents, remarques..."
                }
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex space-x-3 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 py-2 px-4 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={loading}
              className={`
                flex-1 py-2 px-4 text-white rounded-lg transition-colors
                ${isOpeningSession 
                  ? 'bg-green-500 hover:bg-green-600' 
                  : 'bg-red-500 hover:bg-red-600'
                }
                disabled:opacity-50 disabled:cursor-not-allowed
              `}
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Traitement...
                </div>
              ) : (
                <>
                  {isOpeningSession ? (
                    <>
                      <Power className="w-4 h-4 inline mr-2" />
                      Ouvrir la Session
                    </>
                  ) : (
                    <>
                      <PowerOff className="w-4 h-4 inline mr-2" />
                      Fermer la Session
                    </>
                  )}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
