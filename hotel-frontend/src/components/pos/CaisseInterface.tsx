import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { serviceService } from '../../services/service.service';
import { sessionService } from '../../services/session.service';
import { commandeService } from '../../services/commande.service';
import { tableService } from '../../services/table.service';
import { authService } from '../../services/auth.service';
import { ServiceComplexe, SessionCaisse, Commande, MenuCategory, MenuItem, Table } from '../../services/types';
import MenuDisplay from './MenuDisplay';
import SessionManager from './SessionManager';
import PaymentModal from './PaymentModal';
import { toast } from 'react-toastify';

interface CaisseInterfaceProps {
  posId: number;
}

const CaisseInterface: React.FC<CaisseInterfaceProps> = ({ posId }) => {
  const { serviceId } = useParams<{ serviceId: string }>();
  const [selectedService, setSelectedService] = useState<ServiceComplexe | null>(null);
  const [sessionActive, setSessionActive] = useState<SessionCaisse | null>(null);
  const [cart, setCart] = useState<Commande | null>(null);
  const [categories, setCategories] = useState<MenuCategory[]>([]);
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);

  useEffect(() => {
    loadService();
    loadCategories();
  }, [serviceId]);

  const loadService = async () => {
    if (!serviceId) return;
    try {
      const service = await serviceService.getServiceById(parseInt(serviceId));
      setSelectedService(service);
    } catch (error) {
      setError('Erreur lors du chargement du service');
    }
  };

  const loadCategories = async () => {
    if (!serviceId) return;
    try {
      const categories = await commandeService.getCategories(parseInt(serviceId));
      setCategories(categories);
    } catch (error) {
      setError('Erreur lors du chargement des catégories');
    }
  };

  const handleSessionStart = async (fonds: number) => {
    if (!serviceId) return;
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        setError('Utilisateur non connecté');
        return;
      }

      const session = await sessionService.createSession({
        pos_id: posId,
        service_id: parseInt(serviceId),
        employe_id: user.employe_id,
        complexe_id: user.complexe_id,
        fonds_ouverture: fonds,
        statut: 'Ouverte'
      });
      setSessionActive(session);
      toast.success('Session démarrée avec succès');
    } catch (error) {
      setError('Erreur lors du démarrage de la session');
    }
  };

  const handleSessionEnd = async (fonds: number) => {
    if (!sessionActive) return;
    try {
      const session = await sessionService.closeSession(sessionActive.session_id, fonds);
      setSessionActive(session);
      toast.success('Session fermée avec succès');
    } catch (error) {
      setError('Erreur lors de la fermeture de la session');
    }
  };

  const handleItemAdd = (item: MenuItem) => {
    if (!sessionActive) {
      setError('Aucune session active');
      return;
    }

    const user = authService.getCurrentUser();
    if (!user) {
      setError('Utilisateur non connecté');
      return;
    }

    if (!cart) {
      const newCart: Commande = {
        commande_id: 0,
        service_id: parseInt(serviceId!),
        table_id: selectedTable?.table_id,
        employe_id: user.employe_id,
        session_id: sessionActive.session_id,
        date_commande: new Date().toISOString(),
        statut: 'En cours',
        montant_total: item.prix_vente,
        items: [{
          produit_id: item.produit_id,
          nom: item.nom,
          quantite: 1,
          prix_unitaire: item.prix_vente,
          montant_ligne: item.prix_vente,
          statut: 'En attente'
        }],
        type_commande: 'Sur place'
      };
      setCart(newCart);
    } else {
      const existingItem = cart.items.find(i => i.produit_id === item.produit_id);
      if (existingItem) {
        existingItem.quantite += 1;
        existingItem.montant_ligne = existingItem.quantite * existingItem.prix_unitaire;
      } else {
        cart.items.push({
          produit_id: item.produit_id,
          nom: item.nom,
          quantite: 1,
          prix_unitaire: item.prix_vente,
          montant_ligne: item.prix_vente,
          statut: 'En attente'
        });
      }
      cart.montant_total = cart.items.reduce((total, item) => total + item.montant_ligne, 0);
      setCart({ ...cart });
    }
  };

  const handleItemRemove = (itemId: number) => {
    if (!cart) return;
    cart.items = cart.items.filter(item => item.produit_id !== itemId);
    cart.montant_total = cart.items.reduce((total, item) => total + item.montant_ligne, 0);
    setCart({ ...cart });
  };

  const handleItemQuantityChange = (itemId: number, quantity: number) => {
    if (!cart) return;
    const item = cart.items.find(item => item.produit_id === itemId);
    if (item) {
      item.quantite = quantity;
      item.montant_ligne = item.quantite * item.prix_unitaire;
      cart.montant_total = cart.items.reduce((total, item) => total + item.montant_ligne, 0);
      setCart({ ...cart });
    }
  };

  const handleTableSelect = async (table: Table) => {
    setSelectedTable(table);
    if (table.commande_active) {
      setCart(table.commande_active);
    } else {
      setCart(null);
    }
  };

  const handleOrderSubmit = async () => {
    if (!cart || !sessionActive) return;
    try {
      const order = await commandeService.createCommande(cart);
      setCart(null);
      toast.success('Commande créée avec succès');
    } catch (error) {
      setError('Erreur lors de la création de la commande');
    }
  };

  const handlePaymentComplete = async (paymentData: { montant: number; mode_paiement: string; reference?: string }) => {
    if (!cart || !sessionActive) return;
    try {
      const order = await commandeService.createCommande({
        ...cart,
        statut: 'Payée'
      });
      setCart(null);
      setIsPaymentModalOpen(false);
      toast.success('Paiement effectué avec succès');
    } catch (error) {
      setError('Erreur lors du paiement');
    }
  };

  if (error) {
    return <div className="text-red-500">{error}</div>;
  }

  return (
    <div className="flex h-screen">
      <div className="w-1/4 p-4 border-r">
        <SessionManager
          posId={posId}
          onSessionStart={handleSessionStart}
          onSessionEnd={handleSessionEnd}
          sessionActive={sessionActive}
        />
      </div>
      <div className="w-1/2 p-4">
        <MenuDisplay
          serviceId={parseInt(serviceId!)}
          categories={categories}
          onItemAdd={handleItemAdd}
        />
      </div>
      <div className="w-1/4 p-4 border-l">
        <div className="mb-4">
          <h2 className="text-lg font-semibold mb-2">Table sélectionnée</h2>
          {selectedTable ? (
            <div className="p-2 bg-gray-100 rounded">
              Table {selectedTable.numero} - {selectedTable.zone}
            </div>
          ) : (
            <div className="text-gray-500">Aucune table sélectionnée</div>
          )}
        </div>
        {cart && (
          <div>
            <h2 className="text-lg font-semibold mb-2">Commande en cours</h2>
            <div className="space-y-2">
              {cart.items.map(item => (
                <div key={item.produit_id} className="flex justify-between items-center">
                  <div>
                    <div>{item.nom}</div>
                    <div className="text-sm text-gray-500">
                      {item.prix_unitaire} x {item.quantite}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleItemQuantityChange(item.produit_id, item.quantite - 1)}
                      className="px-2 py-1 bg-gray-200 rounded"
                    >
                      -
                    </button>
                    <span>{item.quantite}</span>
                    <button
                      onClick={() => handleItemQuantityChange(item.produit_id, item.quantite + 1)}
                      className="px-2 py-1 bg-gray-200 rounded"
                    >
                      +
                    </button>
                    <button
                      onClick={() => handleItemRemove(item.produit_id)}
                      className="px-2 py-1 bg-red-500 text-white rounded"
                    >
                      ×
                    </button>
                  </div>
                </div>
              ))}
              <div className="mt-4 pt-4 border-t">
                <div className="flex justify-between font-semibold">
                  <span>Total</span>
                  <span>{cart.montant_total} €</span>
                </div>
                <button
                  onClick={() => setIsPaymentModalOpen(true)}
                  className="w-full mt-4 px-4 py-2 bg-blue-500 text-white rounded"
                >
                  Payer
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
      {isPaymentModalOpen && cart && (
        <PaymentModal
          montant={cart.montant_total}
          onClose={() => setIsPaymentModalOpen(false)}
          onPaymentComplete={handlePaymentComplete}
        />
      )}
    </div>
  );
};

export default CaisseInterface; 