import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { sessionCaisseService } from '../../services/session.service';
import { commandeService } from '../../services/commande.service';
import { authService } from '../../services/auth.service';
import { posService } from '../../services/pos.service';
import { SessionCaisse, Commande, MenuCategory } from '../../services/types';
import { toast } from 'react-toastify';

interface CaisseInterfaceProps {
  posId: number;
}

const CaisseInterface: React.FC<CaisseInterfaceProps> = ({ posId }) => {
  const { serviceId } = useParams<{ serviceId: string }>();
  const [sessionActive, setSessionActive] = useState<SessionCaisse | null>(null);
  const [cart, setCart] = useState<Commande | null>(null);
  const [categories, setCategories] = useState<MenuCategory[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [isSessionModalOpen, setIsSessionModalOpen] = useState(false);

  useEffect(() => {
    loadCategories();
  }, [serviceId]);

  const loadCategories = async () => {
    if (!serviceId) return;
    try {
      const categories = await posService.getMenuCategories(parseInt(serviceId));
      setCategories(categories);
    } catch (error) {
      setError('Erreur lors du chargement des catégories');
    }
  };

  const handleSessionStart = async (fonds: number) => {
    if (!serviceId) return;
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        setError('Utilisateur non connecté');
        return;
      }

      const session = await sessionCaisseService.ouvrirSession({
        pos_id: posId,
        service_id: parseInt(serviceId),
        fonds_ouverture: fonds
      });
      setSessionActive(session);
      toast.success('Session démarrée avec succès');
    } catch (error) {
      setError('Erreur lors du démarrage de la session');
    }
  };

  const handleSessionEnd = async (fonds: number) => {
    if (!sessionActive) return;
    try {
      const session = await sessionCaisseService.fermerSession(sessionActive.session_id, {
        fonds_fermeture: fonds
      });
      setSessionActive(session);
      toast.success('Session fermée avec succès');
    } catch (error) {
      setError('Erreur lors de la fermeture de la session');
    }
  };

  const handleItemRemove = (itemId: number) => {
    if (!cart) return;
    cart.items = cart.items.filter(item => item.produit_id !== itemId);
    cart.montant_total = cart.items.reduce((total, item) => total + item.montant_ligne, 0);
    setCart({ ...cart });
  };

  const handleItemQuantityChange = (itemId: number, quantity: number) => {
    if (!cart) return;
    const item = cart.items.find(item => item.produit_id === itemId);
    if (item) {
      item.quantite = quantity;
      item.montant_ligne = item.quantite * item.prix_unitaire;
      cart.montant_total = cart.items.reduce((total, item) => total + item.montant_ligne, 0);
      setCart({ ...cart });
    }
  };

  const handlePaymentComplete = async (paymentData: { montant: number; mode_paiement: string; reference?: string }) => {
    if (!cart || !sessionActive) return;
    try {
      // D'abord créer la commande
      const commandeData = {
        service_id: cart.service_id,
        table_id: cart.table_id,
        items: cart.items.map(item => ({
          produit_id: item.produit_id,
          nom: item.nom,
          quantite: item.quantite,
          prix_unitaire: item.prix_unitaire,
          montant_ligne: item.montant_ligne
        })),
        notes: cart.notes,
        type_commande: cart.type_commande
      };
      const order = await commandeService.createCommande(commandeData);

      // Puis traiter le paiement
      await commandeService.processPayment(order.commande_id, {
        montant: paymentData.montant,
        mode_paiement: paymentData.mode_paiement as any,
        reference: paymentData.reference
      });

      setCart(null);
      setIsPaymentModalOpen(false);
      toast.success('Paiement effectué avec succès');
    } catch (error) {
      setError('Erreur lors du paiement');
    }
  };

  if (error) {
    return <div className="text-red-500">{error}</div>;
  }

  return (
    <div className="flex h-screen">
      {/* Session Management */}
      <div className="w-1/4 p-4 border-r">
        <div className="mb-4">
          <h2 className="text-lg font-semibold mb-2">Session de caisse</h2>
          {sessionActive ? (
            <div className="p-3 bg-green-50 border border-green-200 rounded">
              <div className="text-sm text-green-800">
                Session active depuis {new Date(sessionActive.date_ouverture).toLocaleTimeString()}
              </div>
              <div className="text-sm text-green-600">
                Fonds: {sessionActive.fonds_ouverture} FCFA
              </div>
              <button
                onClick={() => setIsSessionModalOpen(true)}
                className="mt-2 w-full px-3 py-1 bg-red-500 text-white rounded text-sm"
              >
                Fermer session
              </button>
            </div>
          ) : (
            <div className="p-3 bg-gray-50 border border-gray-200 rounded">
              <div className="text-sm text-gray-600 mb-2">Aucune session active</div>
              <button
                onClick={() => setIsSessionModalOpen(true)}
                className="w-full px-3 py-1 bg-green-500 text-white rounded text-sm"
              >
                Ouvrir session
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Menu Display */}
      <div className="w-1/2 p-4">
        <h2 className="text-lg font-semibold mb-4">Menu</h2>
        {categories.length > 0 ? (
          <div className="space-y-4">
            {categories.map(category => (
              <div key={category.categorie_id} className="border rounded p-3">
                <h3 className="font-medium mb-2">{category.nom}</h3>
                <div className="text-sm text-gray-600">
                  Catégorie {category.categorie_id}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-gray-500">Aucune catégorie disponible</div>
        )}
      </div>

      {/* Cart */}
      <div className="w-1/4 p-4 border-l">
        <div className="mb-4">
          <h2 className="text-lg font-semibold mb-2">Commande en cours</h2>
        </div>
        {cart && (
          <div>
            <h2 className="text-lg font-semibold mb-2">Commande en cours</h2>
            <div className="space-y-2">
              {cart.items.map(item => (
                <div key={item.produit_id} className="flex justify-between items-center">
                  <div>
                    <div>{item.nom}</div>
                    <div className="text-sm text-gray-500">
                      {item.prix_unitaire} x {item.quantite}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleItemQuantityChange(item.produit_id, item.quantite - 1)}
                      className="px-2 py-1 bg-gray-200 rounded"
                    >
                      -
                    </button>
                    <span>{item.quantite}</span>
                    <button
                      onClick={() => handleItemQuantityChange(item.produit_id, item.quantite + 1)}
                      className="px-2 py-1 bg-gray-200 rounded"
                    >
                      +
                    </button>
                    <button
                      onClick={() => handleItemRemove(item.produit_id)}
                      className="px-2 py-1 bg-red-500 text-white rounded"
                    >
                      ×
                    </button>
                  </div>
                </div>
              ))}
              <div className="mt-4 pt-4 border-t">
                <div className="flex justify-between font-semibold">
                  <span>Total</span>
                  <span>{cart.montant_total} FCFA</span>
                </div>
                <button
                  onClick={() => setIsPaymentModalOpen(true)}
                  className="w-full mt-4 px-4 py-2 bg-blue-500 text-white rounded"
                >
                  Payer
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Session Modal */}
      {isSessionModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">
              {sessionActive ? 'Fermer la session' : 'Ouvrir une session'}
            </h3>
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">
                Fonds {sessionActive ? 'de fermeture' : 'd\'ouverture'}
              </label>
              <input
                type="number"
                className="w-full px-3 py-2 border rounded"
                placeholder="Montant en FCFA"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    const value = parseFloat((e.target as HTMLInputElement).value);
                    if (value > 0) {
                      if (sessionActive) {
                        handleSessionEnd(value);
                      } else {
                        handleSessionStart(value);
                      }
                      setIsSessionModalOpen(false);
                    }
                  }
                }}
              />
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => setIsSessionModalOpen(false)}
                className="flex-1 px-4 py-2 bg-gray-200 text-gray-800 rounded"
              >
                Annuler
              </button>
              <button
                onClick={() => {
                  const input = document.querySelector('input[type="number"]') as HTMLInputElement;
                  const value = parseFloat(input.value);
                  if (value > 0) {
                    if (sessionActive) {
                      handleSessionEnd(value);
                    } else {
                      handleSessionStart(value);
                    }
                    setIsSessionModalOpen(false);
                  }
                }}
                className={`flex-1 px-4 py-2 text-white rounded ${
                  sessionActive ? 'bg-red-500 hover:bg-red-600' : 'bg-green-500 hover:bg-green-600'
                }`}
              >
                {sessionActive ? 'Fermer' : 'Ouvrir'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Payment Modal */}
      {isPaymentModalOpen && cart && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Paiement</h3>
            <div className="mb-4">
              <div className="flex justify-between text-lg font-semibold">
                <span>Total à payer:</span>
                <span>{cart.montant_total} FCFA</span>
              </div>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => setIsPaymentModalOpen(false)}
                className="flex-1 px-4 py-2 bg-gray-200 text-gray-800 rounded"
              >
                Annuler
              </button>
              <button
                onClick={() => {
                  handlePaymentComplete({
                    montant: cart.montant_total,
                    mode_paiement: 'CARTE'
                  });
                }}
                className="flex-1 px-4 py-2 bg-green-500 text-white rounded"
              >
                Confirmer paiement
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CaisseInterface; 