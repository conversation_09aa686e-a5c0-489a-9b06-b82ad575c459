import { useState } from 'react'
import { Dialog } from '@headlessui/react'
import { ArrowLeft } from 'lucide-react'
import { reservationService, CreateReservationParams } from '../services/reservation.service'
import { anonymousReservationService, CreateAnonymousReservationParams } from '../services/anonymousReservation.service'
import { toast } from 'react-hot-toast'
import { RoomList } from './RoomList'
import { Chambre } from '../services/chambre.service'

interface ClientReservationProps {
  onClose?: () => void;
}

interface ReservationFormData {
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  nombre_personnes: number;
  date_arrivee: string;
  date_depart: string;
  commentaires: string;
  // Nouveaux champs pour les réservations anonymes
  isAnonymous: boolean;
  pseudonyme: string;
}

export function ClientReservation({ onClose }: ClientReservationProps) {
  const [selectedRoom, setSelectedRoom] = useState<Chambre | null>(null);
  const [isReservationModalOpen, setIsReservationModalOpen] = useState(false);
  const [showRecap, setShowRecap] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<ReservationFormData>({
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    nombre_personnes: 1,
    date_arrivee: new Date().toISOString().split('T')[0],
    date_depart: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    commentaires: '',
    // Nouveaux champs pour les réservations anonymes
    isAnonymous: false,
    pseudonyme: ''
  });

  const handleRoomSelect = (room: Chambre) => {
    setSelectedRoom(room);
    setIsReservationModalOpen(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const resetForm = () => {
    setIsReservationModalOpen(false);
    setSelectedRoom(null);
    setShowRecap(false);
    setFormData({
      customerName: '',
      customerEmail: '',
      customerPhone: '',
      nombre_personnes: 1,
      date_arrivee: new Date().toISOString().split('T')[0],
      date_depart: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      commentaires: '',
      isAnonymous: false,
      pseudonyme: ''
    });
  };

  const handleReservationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isSubmitting || !selectedRoom) {
      return;
    }

    setIsSubmitting(true);

    try {
      if (formData.isAnonymous) {
        // Créer une réservation anonyme
        const anonymousData: CreateAnonymousReservationParams = {
          complexe_id: 1, // À remplacer par l'ID du complexe actuel
          date_arrivee: formData.date_arrivee,
          date_depart: formData.date_depart,
          heure_debut: '14:00', // Heure d'arrivée par défaut
          heure_fin: '12:00', // Heure de départ par défaut
          chambres: [{
            chambre_id: selectedRoom.chambre_id.toString(),
            type_chambre: selectedRoom.type_chambre,
            prix_nuit: selectedRoom.prix_base
          }],
          prix_total: selectedRoom.prix_base,
          pseudonyme: formData.pseudonyme || anonymousReservationService.constructor.generateDefaultPseudonym(),
          commentaires: formData.commentaires
        };

        const response = await anonymousReservationService.createDemandeReservationAnonyme(anonymousData);

        if (response.success) {
          toast.success(
            <div>
              <p>Réservation anonyme créée avec succès!</p>
              <p className="font-mono text-sm mt-1">Code d'accès: {response.data.code_acces_anonyme}</p>
              <p className="text-xs text-gray-600 mt-1">Conservez ce code pour consulter votre réservation</p>
            </div>,
            { duration: 8000 }
          );
          resetForm();
        } else {
          throw new Error(response.message || 'Erreur lors de la création de la réservation anonyme');
        }
      } else {
        // Créer une réservation normale
        const [nom, prenom] = formData.customerName.split(' ');

        const reservationData: CreateReservationParams = {
          client_info: {
            nom: nom || formData.customerName,
            prenom: prenom || '',
            telephone: formData.customerPhone
          },
          complexe_id: 1, // À remplacer par l'ID du complexe actuel
          date_arrivee: formData.date_arrivee,
          date_depart: formData.date_depart,
          heure_debut: '14:00', // Heure d'arrivée par défaut
          heure_fin: '12:00', // Heure de départ par défaut
          chambres: [{
            chambre_id: selectedRoom.chambre_id.toString(),
            type_chambre: selectedRoom.type_chambre,
            prix_nuit: selectedRoom.prix_base
          }],
          prix_total: selectedRoom.prix_base,
          commentaires: formData.commentaires
        };

        const response = await reservationService.createDemandeReservation(reservationData);

        if (response.success) {
          toast.success(`Réservation créée avec succès! Numéro: ${response.data.numero_reservation}`);
          resetForm();
        } else {
          throw new Error(response.message || 'Erreur lors de la création de la réservation');
        }
      }
    } catch (error) {
      console.error('Erreur lors de la création de la réservation:', error);
      toast.error(error instanceof Error ? error.message : 'Erreur lors de la création de la réservation');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Réserver une Chambre</h1>
          {onClose && (
            <button
              onClick={onClose}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Retour au Dashboard
            </button>
          )}
        </div>

        {/* Liste des chambres */}
        <RoomList onRoomSelect={handleRoomSelect} />

        {/* Modal de réservation */}
        <Dialog
          open={isReservationModalOpen}
          onClose={() => {
            setIsReservationModalOpen(false);
            setSelectedRoom(null);
          }}
          className="relative z-50"
        >
          <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
          
          <div className="fixed inset-0 flex items-center justify-center p-4">
            <Dialog.Panel className="mx-auto max-w-lg rounded bg-white p-6">
              <Dialog.Title className="text-lg font-medium mb-4">
                Réserver {selectedRoom?.type_chambre}
              </Dialog.Title>

              {!showRecap ? (
                <form onSubmit={(e) => { e.preventDefault(); setShowRecap(true); }} className="space-y-4">
                  {/* Option réservation anonyme */}
                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="isAnonymous"
                        name="isAnonymous"
                        checked={formData.isAnonymous}
                        onChange={handleInputChange}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor="isAnonymous" className="text-sm font-medium text-blue-900">
                        Réservation anonyme
                      </label>
                    </div>
                    <p className="text-xs text-blue-700 mt-1">
                      Réservez sans fournir vos informations personnelles. Vous recevrez un code d'accès pour consulter votre réservation.
                    </p>
                  </div>

                  {/* Formulaire de réservation */}
                  <div className="space-y-4">
                    {formData.isAnonymous ? (
                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Pseudonyme (optionnel)
                        </label>
                        <input
                          type="text"
                          name="pseudonyme"
                          value={formData.pseudonyme}
                          onChange={handleInputChange}
                          placeholder="Laissez vide pour un pseudonyme automatique"
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          Si vous ne renseignez pas de pseudonyme, un sera généré automatiquement.
                        </p>
                      </div>
                    ) : (
                      <>
                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            Nom complet
                          </label>
                          <input
                            type="text"
                            name="customerName"
                            value={formData.customerName}
                            onChange={handleInputChange}
                            required
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            Email
                          </label>
                          <input
                            type="email"
                            name="customerEmail"
                            value={formData.customerEmail}
                            onChange={handleInputChange}
                            required
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            Téléphone
                          </label>
                          <input
                            type="tel"
                            name="customerPhone"
                            value={formData.customerPhone}
                            onChange={handleInputChange}
                            required
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          />
                        </div>
                      </>
                    )}

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Date d'arrivée
                      </label>
                      <input
                        type="date"
                        name="date_arrivee"
                        value={formData.date_arrivee}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Date de départ
                      </label>
                      <input
                        type="date"
                        name="date_depart"
                        value={formData.date_depart}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Nombre de personnes
                      </label>
                      <input
                        type="number"
                        name="nombre_personnes"
                        value={formData.nombre_personnes}
                        onChange={handleInputChange}
                        min="1"
                        max={selectedRoom?.capacite}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Commentaires
                      </label>
                      <textarea
                        name="commentaires"
                        value={formData.commentaires}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        rows={3}
                      />
                    </div>
                  </div>

                  <div className="mt-6 flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={() => setIsReservationModalOpen(false)}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                    >
                      Annuler
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 rounded-md"
                    >
                      Suivant
                    </button>
                  </div>
                </form>
              ) : (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Récapitulatif de la réservation</h3>
                  
                  <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                    <p><span className="font-medium">Type de chambre:</span> {selectedRoom?.type_chambre}</p>
                    <p><span className="font-medium">Numéro de chambre:</span> {selectedRoom?.numero}</p>
                    <p><span className="font-medium">Prix par nuit:</span> {selectedRoom?.prix_base} FCFA</p>

                    {formData.isAnonymous ? (
                      <>
                        <p><span className="font-medium">Type de réservation:</span> <span className="text-blue-600 font-semibold">Anonyme</span></p>
                        <p><span className="font-medium">Pseudonyme:</span> {formData.pseudonyme || 'Généré automatiquement'}</p>
                      </>
                    ) : (
                      <>
                        <p><span className="font-medium">Client:</span> {formData.customerName}</p>
                        <p><span className="font-medium">Email:</span> {formData.customerEmail}</p>
                        <p><span className="font-medium">Téléphone:</span> {formData.customerPhone}</p>
                      </>
                    )}

                    <p><span className="font-medium">Date d'arrivée:</span> {formData.date_arrivee}</p>
                    <p><span className="font-medium">Date de départ:</span> {formData.date_depart}</p>
                    <p><span className="font-medium">Nombre de personnes:</span> {formData.nombre_personnes}</p>
                    {formData.commentaires && (
                      <p><span className="font-medium">Commentaires:</span> {formData.commentaires}</p>
                    )}

                    {formData.isAnonymous && (
                      <div className="bg-blue-50 p-3 rounded border border-blue-200 mt-3">
                        <p className="text-sm text-blue-800">
                          <strong>Important:</strong> Après confirmation, vous recevrez un code d'accès unique pour consulter et gérer votre réservation.
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="mt-6 flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={() => setShowRecap(false)}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                    >
                      Retour
                    </button>
                    <button
                      onClick={handleReservationSubmit}
                      disabled={isSubmitting}
                      className="px-4 py-2 text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 rounded-md disabled:opacity-50"
                    >
                      {isSubmitting ? 'Chargement...' : 'Confirmer la réservation'}
                    </button>
                  </div>
                </div>
              )}
            </Dialog.Panel>
          </div>
        </Dialog>
      </div>
    </div>
  );
} 