import React, { useState, useEffect } from 'react';
import { authService } from '../services/auth.service';
import { complexeService, Complexe } from '../services/complexe.service';
import { MapPin, Users, Star } from 'lucide-react';

// Images par défaut pour les complexes
const defaultImages = [
  'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1080&q=80',
  'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1080&q=80',
  'https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1080&q=80',
];

interface ComplexeSelectorProps {
  onComplexeSelect?: (complexeId: number) => void;
}

export function ComplexeSelector({ onComplexeSelect }: ComplexeSelectorProps) {
  const [complexes, setComplexes] = useState<Complexe[]>([]);
  const [selectedComplexeId, setSelectedComplexeId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const user = authService.getCurrentUser();
    if (!user || user.role !== 'admin_chaine') {
      return;
    }
    const storedComplexeId = localStorage.getItem('selectedComplexeId');
    if (storedComplexeId) {
      setSelectedComplexeId(storedComplexeId);
    }
    fetchComplexes();
  }, []);

  const fetchComplexes = async () => {
    try {
      setLoading(true);
      setError(null);
      const user = authService.getCurrentUser();
      if (!user || !user.chaine_id) {
        throw new Error('Chaine ID non disponible');
      }
      const data = await complexeService.getComplexesByChaine(user.chaine_id);
      setComplexes(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Une erreur est survenue');
    } finally {
      setLoading(false);
    }
  };

  const handleComplexeClick = (complexe: Complexe, index: number) => {
    setSelectedComplexeId(complexe.complexe_id.toString());
    localStorage.setItem('selectedComplexeId', complexe.complexe_id.toString());
    if (onComplexeSelect) {
      onComplexeSelect(complexe.complexe_id);
    }
  };

  if (!authService.isAdminChaine()) {
    return null;
  }

  if (loading) {
    return <div>Chargement des complexes...</div>;
  }

  if (error) {
    return <div className="text-red-500">Erreur: {error}</div>;
  }

  return (
    <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
      {complexes.map((complexe, index) => (
        <div
          key={complexe.complexe_id}
          onClick={() => handleComplexeClick(complexe, index)}
          className={`bg-white overflow-hidden shadow-lg rounded-lg cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-xl border-2 ${selectedComplexeId === complexe.complexe_id.toString() ? 'border-blue-600' : 'border-transparent'}`}
        >
          <div className="relative h-48">
            <img
              src={defaultImages[index % defaultImages.length]}
              alt={complexe.nom}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
            <div className="absolute bottom-0 left-0 right-0 p-4">
              <h3 className="text-xl font-bold text-white">{complexe.nom}</h3>
              <div className="flex items-center mt-1">
                <Star className="h-4 w-4 text-yellow-400" />
                <span className="text-white text-sm ml-1">4.5</span>
              </div>
            </div>
          </div>
          <div className="p-6">
            <div className="flex items-center text-sm text-gray-500 mb-2">
              <MapPin className="h-4 w-4 mr-2" />
              <span>{complexe.ville}, {complexe.pays}</span>
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <Users className="h-4 w-4 mr-2" />
              <span>{complexe.nombre_employes || 0} employés</span>
            </div>
            <div className="mt-4">
              <button
                className={`w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 ${selectedComplexeId === complexe.complexe_id.toString() ? 'ring-2 ring-blue-500' : ''}`}
              >
                {selectedComplexeId === complexe.complexe_id.toString() ? 'Sélectionné' : 'Choisir ce complexe'}
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
} 