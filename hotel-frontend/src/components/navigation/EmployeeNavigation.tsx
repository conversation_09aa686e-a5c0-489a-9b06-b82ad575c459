import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { 
  Home, 
  BedDouble, 
  ShoppingCart, 
  Waves, 
  ChefHat,
  Clock,
  Users,
  Bell,
  Settings
} from 'lucide-react';
import { useEmployeePermissions } from '../../hooks/useEmployeePermissions';

// ===== INTERFACES =====

interface EmployeeNavigationProps {
  className?: string;
  compact?: boolean;
}

interface NavigationItem {
  name: string;
  path: string;
  icon: React.ReactNode;
  color: string;
  description: string;
  badge?: string | number;
  isActive?: boolean;
}

// ===== COMPOSANT PRINCIPAL =====

const EmployeeNavigation: React.FC<EmployeeNavigationProps> = ({ 
  className = '', 
  compact = false 
}) => {
  const location = useLocation();
  const { 
    employeeType, 
    employeeTypeInfo, 
    authorizedServices,
    canAccessService,
    loading 
  } = useEmployeePermissions();

  // Fonction pour obtenir les éléments de navigation selon le type d'employé
  const getNavigationItems = (): NavigationItem[] => {
    if (loading || !employeeType) return [];

    const baseItems: NavigationItem[] = [
      {
        name: 'Accueil',
        path: '/dashboard',
        icon: <Home size={20} />,
        color: 'text-blue-600 bg-blue-50 border-blue-200',
        description: 'Tableau de bord personnel'
      }
    ];

    switch (employeeType) {
      case 'reception':
        return [
          ...baseItems,
          {
            name: 'Réservations',
            path: '/reception',
            icon: <BedDouble size={20} />,
            color: 'text-green-600 bg-green-50 border-green-200',
            description: 'Gérer les réservations',
            badge: '3' // Exemple de badge pour les réservations en attente
          },
          {
            name: 'Chambres',
            path: '/chambres',
            icon: <BedDouble size={20} />,
            color: 'text-teal-600 bg-teal-50 border-teal-200',
            description: 'État des chambres'
          },
          {
            name: 'Clients',
            path: '/client-reservations',
            icon: <Users size={20} />,
            color: 'text-purple-600 bg-purple-50 border-purple-200',
            description: 'Gestion des clients'
          }
        ];

      case 'gerant_piscine':
        return [
          ...baseItems,
          {
            name: 'Piscine',
            path: '/pool',
            icon: <Waves size={20} />,
            color: 'text-cyan-600 bg-cyan-50 border-cyan-200',
            description: 'Gestion de la piscine'
          },
          {
            name: 'Billetterie',
            path: '/pool?tab=tickets',
            icon: <Clock size={20} />,
            color: 'text-blue-600 bg-blue-50 border-blue-200',
            description: 'Vente de tickets'
          }
        ];

      case 'serveuse':
        const serveusePaths: NavigationItem[] = [...baseItems];
        
        if (canAccessService('restaurant')) {
          serveusePaths.push({
            name: 'Restaurant',
            path: '/pos?service=restaurant',
            icon: <ChefHat size={20} />,
            color: 'text-orange-600 bg-orange-50 border-orange-200',
            description: 'Service restaurant'
          });
        }
        
        if (canAccessService('bar')) {
          serveusePaths.push({
            name: 'Bar',
            path: '/pos?service=bar',
            icon: <ShoppingCart size={20} />,
            color: 'text-purple-600 bg-purple-50 border-purple-200',
            description: 'Service bar'
          });
        }

        return serveusePaths;

      case 'gerant_services':
        const gerantPaths: NavigationItem[] = [...baseItems];
        
        if (canAccessService('restaurant')) {
          gerantPaths.push({
            name: 'Restaurant',
            path: '/pos?service=restaurant',
            icon: <ChefHat size={20} />,
            color: 'text-orange-600 bg-orange-50 border-orange-200',
            description: 'Gestion restaurant'
          });
        }
        
        if (canAccessService('bar')) {
          gerantPaths.push({
            name: 'Bar',
            path: '/pos?service=bar',
            icon: <ShoppingCart size={20} />,
            color: 'text-purple-600 bg-purple-50 border-purple-200',
            description: 'Gestion bar'
          });
        }

        // Ajout des fonctionnalités de gestion
        gerantPaths.push({
          name: 'Équipe',
          path: '/team-management',
          icon: <Users size={20} />,
          color: 'text-gray-600 bg-gray-50 border-gray-200',
          description: 'Gestion de l\'équipe'
        });

        return gerantPaths;

      case 'cuisine':
        return [
          ...baseItems,
          {
            name: 'Commandes',
            path: '/pos?service=restaurant&view=kitchen',
            icon: <ChefHat size={20} />,
            color: 'text-orange-600 bg-orange-50 border-orange-200',
            description: 'Commandes à préparer',
            badge: '5' // Exemple de badge pour les commandes en attente
          },
          {
            name: 'Menu',
            path: '/menu?view=kitchen',
            icon: <Settings size={20} />,
            color: 'text-gray-600 bg-gray-50 border-gray-200',
            description: 'Consulter le menu'
          }
        ];

      default:
        return baseItems;
    }
  };

  const navigationItems = getNavigationItems();

  const isActiveItem = (item: NavigationItem) => {
    if (item.path === '/dashboard') {
      return location.pathname === '/dashboard';
    }
    return location.pathname.startsWith(item.path.split('?')[0]);
  };

  const renderNavigationItem = (item: NavigationItem) => {
    const isActive = isActiveItem(item);
    
    return (
      <NavLink
        key={item.path}
        to={item.path}
        className={({ isActive: linkActive }) => `
          group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 relative
          ${linkActive || isActive
            ? `${item.color} shadow-sm`
            : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
          }
        `}
      >
        <span className="mr-3">
          {item.icon}
        </span>
        {!compact && (
          <div className="flex-1">
            <div className="text-sm font-medium flex items-center justify-between">
              {item.name}
              {item.badge && (
                <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5 min-w-[20px] text-center">
                  {item.badge}
                </span>
              )}
            </div>
            <div className="text-xs opacity-75 mt-0.5">
              {item.description}
            </div>
          </div>
        )}
        {compact && item.badge && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] text-center">
            {item.badge}
          </span>
        )}
      </NavLink>
    );
  };

  // Affichage du loading
  if (loading) {
    return (
      <nav className={className}>
        <div className="space-y-2">
          <div className="animate-pulse">
            <div className="h-12 bg-gray-200 rounded-lg mb-2"></div>
            <div className="h-12 bg-gray-200 rounded-lg mb-2"></div>
            <div className="h-12 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </nav>
    );
  }

  // Affichage du header avec informations employé
  const renderEmployeeHeader = () => {
    if (compact) return null;

    return (
      <div className="mb-4 p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
        <div className="flex items-center">
          <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
            {employeeTypeInfo?.label.charAt(0) || 'E'}
          </div>
          <div className="ml-3">
            <div className="text-sm font-medium text-gray-900">
              {employeeTypeInfo?.label || 'Employé'}
            </div>
            <div className="text-xs text-gray-500">
              Services : {authorizedServices.join(', ') || 'Aucun'}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <nav className={className}>
      {renderEmployeeHeader()}
      <div className="space-y-1">
        {navigationItems.map(item => renderNavigationItem(item))}
      </div>
      
      {/* Section notifications pour les employés */}
      {!compact && navigationItems.length > 0 && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="flex items-center text-xs text-gray-500 mb-2">
            <Bell size={14} className="mr-1" />
            Notifications
          </div>
          <div className="text-xs text-gray-400">
            Aucune notification
          </div>
        </div>
      )}
    </nav>
  );
};

export default EmployeeNavigation;
