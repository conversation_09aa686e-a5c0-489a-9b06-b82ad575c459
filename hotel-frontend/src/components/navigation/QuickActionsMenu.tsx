import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Plus, 
  Search, 
  Bell, 
  Settings, 
  User,
  LogOut,
  ChevronDown,
  Clock,
  Calendar,
  ShoppingCart,
  Users,
  FileText
} from 'lucide-react';
import { useEmployeePermissions } from '../../hooks/useEmployeePermissions';
import { authService } from '../../services/auth.service';

// ===== INTERFACES =====

interface QuickAction {
  name: string;
  icon: React.ReactNode;
  action: () => void;
  color: string;
  description: string;
  shortcut?: string;
}

interface QuickActionsMenuProps {
  className?: string;
  showUserInfo?: boolean;
}

// ===== COMPOSANT PRINCIPAL =====

const QuickActionsMenu: React.FC<QuickActionsMenuProps> = ({ 
  className = '',
  showUserInfo = true
}) => {
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);
  
  const { 
    isAdmin, 
    employeeType, 
    employeeTypeInfo,
    fullName,
    userTypeLabel,
    canAccessService,
    authorizedServices
  } = useEmployeePermissions();

  // Fonction pour obtenir les actions rapides selon le type d'utilisateur
  const getQuickActions = (): QuickAction[] => {
    const baseActions: QuickAction[] = [];

    if (isAdmin) {
      return [
        {
          name: 'Nouvelle réservation',
          icon: <Plus size={16} />,
          action: () => navigate('/reception?action=new'),
          color: 'text-green-600 bg-green-50 hover:bg-green-100',
          description: 'Créer une nouvelle réservation',
          shortcut: 'Ctrl+N'
        },
        {
          name: 'Rechercher client',
          icon: <Search size={16} />,
          action: () => navigate('/client-reservations?search=true'),
          color: 'text-blue-600 bg-blue-50 hover:bg-blue-100',
          description: 'Rechercher un client',
          shortcut: 'Ctrl+F'
        },
        {
          name: 'Rapports',
          icon: <FileText size={16} />,
          action: () => navigate('/reports'),
          color: 'text-purple-600 bg-purple-50 hover:bg-purple-100',
          description: 'Consulter les rapports'
        },
        {
          name: 'Gestion personnel',
          icon: <Users size={16} />,
          action: () => navigate('/employee-management'),
          color: 'text-orange-600 bg-orange-50 hover:bg-orange-100',
          description: 'Gérer les employés'
        }
      ];
    }

    // Actions selon le type d'employé
    switch (employeeType) {
      case 'reception':
        return [
          {
            name: 'Nouvelle réservation',
            icon: <Plus size={16} />,
            action: () => navigate('/reception?action=new'),
            color: 'text-green-600 bg-green-50 hover:bg-green-100',
            description: 'Créer une nouvelle réservation',
            shortcut: 'Ctrl+N'
          },
          {
            name: 'Check-in',
            icon: <Calendar size={16} />,
            action: () => navigate('/reception?tab=checkin'),
            color: 'text-blue-600 bg-blue-50 hover:bg-blue-100',
            description: 'Enregistrer un client'
          },
          {
            name: 'Rechercher client',
            icon: <Search size={16} />,
            action: () => navigate('/client-reservations?search=true'),
            color: 'text-purple-600 bg-purple-50 hover:bg-purple-100',
            description: 'Rechercher un client',
            shortcut: 'Ctrl+F'
          }
        ];

      case 'gerant_piscine':
        return [
          {
            name: 'Nouveau ticket',
            icon: <Plus size={16} />,
            action: () => navigate('/pool?action=new-ticket'),
            color: 'text-cyan-600 bg-cyan-50 hover:bg-cyan-100',
            description: 'Vendre un ticket piscine',
            shortcut: 'Ctrl+N'
          },
          {
            name: 'Scanner QR',
            icon: <Search size={16} />,
            action: () => navigate('/pool?action=scan'),
            color: 'text-blue-600 bg-blue-50 hover:bg-blue-100',
            description: 'Scanner un ticket'
          }
        ];

      case 'serveuse':
      case 'gerant_services':
        const serviceActions: QuickAction[] = [];
        
        if (canAccessService('restaurant')) {
          serviceActions.push({
            name: 'Nouvelle commande',
            icon: <Plus size={16} />,
            action: () => navigate('/pos?service=restaurant&action=new'),
            color: 'text-orange-600 bg-orange-50 hover:bg-orange-100',
            description: 'Prendre une commande restaurant',
            shortcut: 'Ctrl+N'
          });
        }
        
        if (canAccessService('bar')) {
          serviceActions.push({
            name: 'Commande bar',
            icon: <ShoppingCart size={16} />,
            action: () => navigate('/pos?service=bar&action=new'),
            color: 'text-purple-600 bg-purple-50 hover:bg-purple-100',
            description: 'Prendre une commande bar'
          });
        }

        return serviceActions;

      case 'cuisine':
        return [
          {
            name: 'Voir commandes',
            icon: <Clock size={16} />,
            action: () => navigate('/pos?service=restaurant&view=kitchen'),
            color: 'text-orange-600 bg-orange-50 hover:bg-orange-100',
            description: 'Consulter les commandes en cours'
          }
        ];

      default:
        return baseActions;
    }
  };

  const quickActions = getQuickActions();

  const handleLogout = () => {
    authService.logout();
    navigate('/login');
  };

  const renderUserInfo = () => {
    if (!showUserInfo) return null;

    return (
      <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg mb-4">
        <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
          {fullName.charAt(0).toUpperCase() || 'U'}
        </div>
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-gray-900 truncate">
            {fullName || 'Utilisateur'}
          </div>
          <div className="text-xs text-gray-500 truncate">
            {userTypeLabel}
          </div>
          {employeeType && (
            <div className="text-xs text-blue-600 truncate">
              Services : {authorizedServices.join(', ') || 'Aucun'}
            </div>
          )}
        </div>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="p-1 text-gray-400 hover:text-gray-600"
        >
          <ChevronDown 
            size={16} 
            className={`transform transition-transform ${isOpen ? 'rotate-180' : ''}`}
          />
        </button>
      </div>
    );
  };

  const renderQuickActions = () => {
    if (quickActions.length === 0) return null;

    return (
      <div className="space-y-2">
        <div className="text-xs font-medium text-gray-500 uppercase tracking-wide px-3">
          Actions rapides
        </div>
        <div className="space-y-1">
          {quickActions.map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              className={`
                w-full flex items-center px-3 py-2 text-sm rounded-lg transition-colors
                ${action.color}
              `}
              title={action.description}
            >
              <span className="mr-3">
                {action.icon}
              </span>
              <div className="flex-1 text-left">
                <div className="font-medium">
                  {action.name}
                </div>
                {action.shortcut && (
                  <div className="text-xs opacity-75">
                    {action.shortcut}
                  </div>
                )}
              </div>
            </button>
          ))}
        </div>
      </div>
    );
  };

  const renderUserMenu = () => {
    if (!isOpen) return null;

    return (
      <div className="mt-4 pt-4 border-t border-gray-200 space-y-1">
        <button
          onClick={() => navigate('/profile')}
          className="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg"
        >
          <User size={16} className="mr-3" />
          Mon profil
        </button>
        <button
          onClick={() => navigate('/settings')}
          className="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg"
        >
          <Settings size={16} className="mr-3" />
          Paramètres
        </button>
        <button
          onClick={handleLogout}
          className="w-full flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg"
        >
          <LogOut size={16} className="mr-3" />
          Déconnexion
        </button>
      </div>
    );
  };

  return (
    <div className={className}>
      {renderUserInfo()}
      {renderQuickActions()}
      {renderUserMenu()}
    </div>
  );
};

export default QuickActionsMenu;

// ===== COMPOSANTS UTILITAIRES =====

/**
 * Menu d'actions rapides compact pour les barres d'outils
 */
export const CompactQuickActions: React.FC<{ className?: string }> = ({ className }) => {
  const { employeeType, canAccessService } = useEmployeePermissions();
  const navigate = useNavigate();

  const getCompactActions = () => {
    switch (employeeType) {
      case 'reception':
        return [
          {
            icon: <Plus size={18} />,
            action: () => navigate('/reception?action=new'),
            tooltip: 'Nouvelle réservation'
          },
          {
            icon: <Search size={18} />,
            action: () => navigate('/client-reservations?search=true'),
            tooltip: 'Rechercher client'
          }
        ];
      case 'gerant_piscine':
        return [
          {
            icon: <Plus size={18} />,
            action: () => navigate('/pool?action=new-ticket'),
            tooltip: 'Nouveau ticket'
          }
        ];
      default:
        return [];
    }
  };

  const actions = getCompactActions();

  return (
    <div className={`flex space-x-2 ${className}`}>
      {actions.map((action, index) => (
        <button
          key={index}
          onClick={action.action}
          className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          title={action.tooltip}
        >
          {action.icon}
        </button>
      ))}
    </div>
  );
};

/**
 * Notifications rapides
 */
export const QuickNotifications: React.FC<{ className?: string }> = ({ className }) => {
  const [hasNotifications] = useState(false); // Placeholder

  return (
    <button className={`relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors ${className}`}>
      <Bell size={18} />
      {hasNotifications && (
        <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
      )}
    </button>
  );
};
