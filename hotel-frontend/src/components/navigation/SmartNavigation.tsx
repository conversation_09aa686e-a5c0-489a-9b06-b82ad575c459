import React from 'react';
import { useEmployeePermissions } from '../../hooks/useEmployeePermissions';
import AdminNavigation from './AdminNavigation';
import EmployeeNavigation from './EmployeeNavigation';
import MainNavigation from './MainNavigation';

// ===== INTERFACES =====

interface SmartNavigationProps {
  className?: string;
  compact?: boolean;
  fallbackToMain?: boolean;
}

// ===== COMPOSANT PRINCIPAL =====

const SmartNavigation: React.FC<SmartNavigationProps> = ({ 
  className = '', 
  compact = false,
  fallbackToMain = true
}) => {
  const { 
    isAdmin, 
    isEmployee, 
    employeeType,
    loading 
  } = useEmployeePermissions();

  // Affichage du loading
  if (loading) {
    return (
      <nav className={className}>
        <div className="space-y-2">
          <div className="animate-pulse">
            <div className="h-12 bg-gray-200 rounded-lg mb-2"></div>
            <div className="h-12 bg-gray-200 rounded-lg mb-2"></div>
            <div className="h-12 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </nav>
    );
  }

  // Choix du composant de navigation selon le type d'utilisateur
  if (isAdmin) {
    return (
      <AdminNavigation 
        className={className} 
        compact={compact} 
      />
    );
  }

  if (isEmployee && employeeType) {
    return (
      <EmployeeNavigation 
        className={className} 
        compact={compact} 
      />
    );
  }

  // Fallback vers la navigation principale si demandé
  if (fallbackToMain) {
    return (
      <MainNavigation 
        className={className} 
      />
    );
  }

  // Affichage d'erreur si aucune navigation appropriée
  return (
    <nav className={className}>
      <div className="p-4 text-center text-gray-500">
        <div className="text-sm">
          Type d'utilisateur non reconnu
        </div>
        <div className="text-xs mt-1">
          Impossible de charger la navigation
        </div>
      </div>
    </nav>
  );
};

export default SmartNavigation;

// ===== COMPOSANTS UTILITAIRES =====

/**
 * Navigation compacte pour les sidebars étroites
 */
export const CompactNavigation: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <SmartNavigation 
      className={className} 
      compact={true} 
      fallbackToMain={false}
    />
  );
};

/**
 * Navigation complète avec fallback
 */
export const FullNavigation: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <SmartNavigation 
      className={className} 
      compact={false} 
      fallbackToMain={true}
    />
  );
};

/**
 * Hook pour obtenir le type de navigation recommandé
 */
export const useNavigationType = () => {
  const { isAdmin, isEmployee, employeeType, loading } = useEmployeePermissions();

  const getNavigationType = () => {
    if (loading) return 'loading';
    if (isAdmin) return 'admin';
    if (isEmployee && employeeType) return 'employee';
    return 'main';
  };

  const getRecommendedComponent = () => {
    const type = getNavigationType();
    
    switch (type) {
      case 'admin':
        return AdminNavigation;
      case 'employee':
        return EmployeeNavigation;
      case 'main':
      default:
        return MainNavigation;
    }
  };

  return {
    navigationType: getNavigationType(),
    RecommendedComponent: getRecommendedComponent(),
    isAdmin,
    isEmployee,
    employeeType,
    loading
  };
};

// ===== COMPOSANT DE NAVIGATION CONTEXTUELLE =====

/**
 * Navigation qui s'adapte au contexte de la page
 */
export const ContextualNavigation: React.FC<{
  className?: string;
  pageContext?: 'admin' | 'employee' | 'service' | 'auto';
}> = ({ className, pageContext = 'auto' }) => {
  const { isAdmin, isEmployee, employeeType } = useEmployeePermissions();

  // Déterminer le contexte automatiquement si 'auto'
  const getEffectiveContext = () => {
    if (pageContext !== 'auto') return pageContext;
    
    if (isAdmin) return 'admin';
    if (isEmployee) return 'employee';
    return 'admin'; // Fallback
  };

  const effectiveContext = getEffectiveContext();

  switch (effectiveContext) {
    case 'admin':
      return <AdminNavigation className={className} />;
    case 'employee':
      return <EmployeeNavigation className={className} />;
    case 'service':
      // Navigation spécialisée pour les pages de service
      return <EmployeeNavigation className={className} compact={true} />;
    default:
      return <SmartNavigation className={className} />;
  }
};

// ===== COMPOSANT DE NAVIGATION AVEC BREADCRUMBS =====

/**
 * Navigation avec fil d'Ariane
 */
export const NavigationWithBreadcrumbs: React.FC<{
  className?: string;
  breadcrumbs?: Array<{ name: string; path?: string }>;
}> = ({ className, breadcrumbs = [] }) => {
  return (
    <div className={className}>
      {/* Breadcrumbs */}
      {breadcrumbs.length > 0 && (
        <div className="mb-4 px-3 py-2 bg-gray-50 rounded-lg">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-sm">
              {breadcrumbs.map((crumb, index) => (
                <li key={index} className="flex items-center">
                  {index > 0 && (
                    <span className="mx-2 text-gray-400">/</span>
                  )}
                  {crumb.path ? (
                    <a 
                      href={crumb.path} 
                      className="text-blue-600 hover:text-blue-800"
                    >
                      {crumb.name}
                    </a>
                  ) : (
                    <span className="text-gray-500">{crumb.name}</span>
                  )}
                </li>
              ))}
            </ol>
          </nav>
        </div>
      )}
      
      {/* Navigation principale */}
      <SmartNavigation />
    </div>
  );
};

// ===== EXPORTS SUPPLÉMENTAIRES =====

export { AdminNavigation, EmployeeNavigation, MainNavigation };
