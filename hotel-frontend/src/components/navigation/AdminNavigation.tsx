import React, { useState } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { 
  Home, 
  BedDouble, 
  ShoppingCart, 
  Users, 
  Waves, 
  Package, 
  FileText, 
  Settings,
  Warehouse,
  Upload,
  ChefHat,
  BarChart3,
  History,
  Crown,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { useAdminPermissions } from '../../hooks/useAdminPermissions';

// ===== INTERFACES =====

interface AdminNavigationProps {
  className?: string;
  compact?: boolean;
}

interface NavigationItem {
  name: string;
  path: string;
  icon: React.ReactNode;
  color: string;
  description: string;
  badge?: string | number;
  children?: NavigationItem[];
  isExpanded?: boolean;
}

// ===== COMPOSANT PRINCIPAL =====

const AdminNavigation: React.FC<AdminNavigationProps> = ({ 
  className = '', 
  compact = false 
}) => {
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  
  const { 
    isSuperAdmin,
    canAccessReports, 
    canAccessConfiguration, 
    canManageEmployees,
    userTypeLabel,
    loading 
  } = useAdminPermissions();

  // Fonction pour basculer l'expansion d'un élément
  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev => 
      prev.includes(itemName) 
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  };

  // Fonction pour obtenir les éléments de navigation admin
  const getAdminNavigationItems = (): NavigationItem[] => {
    const items: NavigationItem[] = [
      {
        name: 'Tableau de bord',
        path: '/dashboard',
        icon: <Home size={20} />,
        color: 'text-blue-600 bg-blue-50 border-blue-200',
        description: 'Vue d\'ensemble de l\'hôtel'
      },
      {
        name: 'Hébergement',
        path: '/reception',
        icon: <BedDouble size={20} />,
        color: 'text-green-600 bg-green-50 border-green-200',
        description: 'Gestion de l\'hébergement',
        children: [
          {
            name: 'Réservations',
            path: '/reception',
            icon: <BedDouble size={16} />,
            color: 'text-green-600',
            description: 'Gérer les réservations'
          },
          {
            name: 'Chambres',
            path: '/chambres',
            icon: <BedDouble size={16} />,
            color: 'text-green-600',
            description: 'État des chambres'
          }
        ]
      },
      {
        name: 'Services',
        path: '/services',
        icon: <Settings size={20} />,
        color: 'text-amber-600 bg-amber-50 border-amber-200',
        description: 'Gestion des services',
        children: [
          {
            name: 'Configuration',
            path: '/services',
            icon: <Settings size={16} />,
            color: 'text-amber-600',
            description: 'Configurer les services'
          },
          {
            name: 'POS',
            path: '/pos',
            icon: <ShoppingCart size={16} />,
            color: 'text-amber-600',
            description: 'Point de vente'
          },
          {
            name: 'Piscine',
            path: '/pool',
            icon: <Waves size={16} />,
            color: 'text-amber-600',
            description: 'Gestion piscine'
          }
        ]
      }
    ];

    // Ajouter l'inventaire si l'admin a les permissions
    if (canAccessConfiguration) {
      items.push({
        name: 'Inventaire',
        path: '/inventaire',
        icon: <Warehouse size={20} />,
        color: 'text-emerald-600 bg-emerald-50 border-emerald-200',
        description: 'Gestion des stocks',
        children: [
          {
            name: 'Ingrédients',
            path: '/inventaire?tab=ingredients',
            icon: <Package size={16} />,
            color: 'text-emerald-600',
            description: 'Gérer les ingrédients'
          },
          {
            name: 'Stock',
            path: '/inventaire?tab=stock',
            icon: <Warehouse size={16} />,
            color: 'text-emerald-600',
            description: 'Suivre les stocks'
          },

          {
            name: 'Analyses',
            path: '/inventaire?tab=analytics',
            icon: <BarChart3 size={16} />,
            color: 'text-emerald-600',
            description: 'Analyses et rapports'
          }
        ]
      });

      items.push({
        name: 'Import Excel',
        path: '/import-excel',
        icon: <Upload size={20} />,
        color: 'text-blue-600 bg-blue-50 border-blue-200',
        description: 'Importer des données',
        children: [
          {
            name: 'Nouvel import',
            path: '/import-excel',
            icon: <Upload size={16} />,
            color: 'text-blue-600',
            description: 'Importer un fichier'
          },
          {
            name: 'Historique',
            path: '/import-history',
            icon: <History size={16} />,
            color: 'text-blue-600',
            description: 'Voir l\'historique'
          }
        ]
      });

      items.push({
        name: 'Gestion POS',
        path: '/pos-management',
        icon: <Settings size={20} />,
        color: 'text-gray-600 bg-gray-50 border-gray-200',
        description: 'Gestion des caisses'
      });
    }

    // Ajouter la gestion des employés si l'admin a les permissions
    if (canManageEmployees) {
      items.push({
        name: 'Personnel',
        path: '/employee-management',
        icon: <Users size={20} />,
        color: 'text-purple-600 bg-purple-50 border-purple-200',
        description: 'Gestion des employés'
      });
    }

    // Ajouter les rapports si l'admin a les permissions
    if (canAccessReports) {
      items.push({
        name: 'Rapports',
        path: '/reports',
        icon: <FileText size={20} />,
        color: 'text-indigo-600 bg-indigo-50 border-indigo-200',
        description: 'Rapports et analyses'
      });
    }

    return items;
  };

  const navigationItems = getAdminNavigationItems();

  const isActiveItem = (item: NavigationItem) => {
    if (item.path === '/dashboard') {
      return location.pathname === '/dashboard';
    }
    return location.pathname.startsWith(item.path.split('?')[0]);
  };

  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {
    const isActive = isActiveItem(item);
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.name);
    const paddingLeft = level === 0 ? 'pl-3' : 'pl-8';

    return (
      <div key={item.path}>
        <div
          className={`
            group flex items-center ${paddingLeft} pr-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer
            ${isActive
              ? `${item.color} shadow-sm`
              : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
            }
          `}
          onClick={() => {
            if (hasChildren) {
              toggleExpanded(item.name);
            }
          }}
        >
          <span className="mr-3">
            {item.icon}
          </span>
          {!compact && (
            <>
              <div className="flex-1">
                <NavLink
                  to={item.path}
                  className="block"
                  onClick={(e) => {
                    if (hasChildren) {
                      e.preventDefault();
                    }
                  }}
                >
                  <div className="text-sm font-medium flex items-center justify-between">
                    {item.name}
                    {item.badge && (
                      <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-0.5 min-w-[20px] text-center">
                        {item.badge}
                      </span>
                    )}
                  </div>
                  <div className="text-xs opacity-75 mt-0.5">
                    {item.description}
                  </div>
                </NavLink>
              </div>
              {hasChildren && (
                <span className="ml-2">
                  {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                </span>
              )}
            </>
          )}
          {compact && item.badge && (
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] text-center">
              {item.badge}
            </span>
          )}
        </div>

        {/* Sous-éléments */}
        {hasChildren && isExpanded && !compact && (
          <div className="mt-1 space-y-1">
            {item.children!.map(child => (
              <NavLink
                key={child.path}
                to={child.path}
                className={({ isActive: linkActive }) => `
                  group flex items-center pl-12 pr-3 py-2 text-sm rounded-lg transition-all duration-200
                  ${linkActive
                    ? `${child.color} bg-gray-50 shadow-sm`
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }
                `}
              >
                <span className="mr-3">
                  {child.icon}
                </span>
                <div className="flex-1">
                  <div className="text-sm font-medium">
                    {child.name}
                  </div>
                  <div className="text-xs opacity-75 mt-0.5">
                    {child.description}
                  </div>
                </div>
              </NavLink>
            ))}
          </div>
        )}
      </div>
    );
  };

  // Affichage du loading
  if (loading) {
    return (
      <nav className={className}>
        <div className="space-y-2">
          <div className="animate-pulse">
            <div className="h-12 bg-gray-200 rounded-lg mb-2"></div>
            <div className="h-12 bg-gray-200 rounded-lg mb-2"></div>
            <div className="h-12 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </nav>
    );
  }

  // Affichage du header admin
  const renderAdminHeader = () => {
    if (compact) return null;

    return (
      <div className="mb-4 p-3 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-200">
        <div className="flex items-center">
          <div className="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center text-white">
            <Crown size={20} />
          </div>
          <div className="ml-3">
            <div className="text-sm font-medium text-gray-900">
              {userTypeLabel}
            </div>
            <div className="text-xs text-gray-500">
              {isSuperAdmin ? 'Accès complet' : 'Accès administrateur'}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <nav className={className}>
      {renderAdminHeader()}
      <div className="space-y-1">
        {navigationItems.map(item => renderNavigationItem(item))}
      </div>
    </nav>
  );
};

export default AdminNavigation;
