import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { Restaurant, Wine, Waves, Settings, BarChart3 } from 'lucide-react';
import { ServiceType } from '../../services/servicePermission.service';
import { useServicePermissions } from '../../hooks/useServicePermissions';

interface ServiceNavigationProps {
  className?: string;
}

interface ServiceNavItem {
  type: ServiceType;
  name: string;
  path: string;
  icon: React.ReactNode;
  color: string;
  description: string;
}

const ServiceNavigation: React.FC<ServiceNavigationProps> = ({ className = '' }) => {
  const { permissions, accessibleServices, loading } = useServicePermissions();
  const location = useLocation();

  const serviceNavItems: ServiceNavItem[] = [
    {
      type: 'Restaurant',
      name: 'Restaurant',
      path: '/services/restaurant',
      icon: <Restaurant size={20} />,
      color: 'text-orange-600 bg-orange-50 border-orange-200',
      description: 'Gestion du restaurant'
    },
    {
      type: 'Bar',
      name: 'Bar',
      path: '/services/bar',
      icon: <Wine size={20} />,
      color: 'text-purple-600 bg-purple-50 border-purple-200',
      description: 'Gestion du bar'
    },
    {
      type: 'Piscine',
      name: 'Piscine',
      path: '/services/piscine',
      icon: <Waves size={20} />,
      color: 'text-blue-600 bg-blue-50 border-blue-200',
      description: 'Gestion de la piscine'
    }
  ];

  if (loading) {
    return (
      <div className={`${className} animate-pulse`}>
        <div className="space-y-2">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-12 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  const accessibleNavItems = serviceNavItems.filter(item => 
    accessibleServices.includes(item.type)
  );

  if (accessibleNavItems.length === 0) {
    return (
      <div className={`${className} p-4 text-center`}>
        <div className="text-gray-500 text-sm">
          Aucun service accessible
        </div>
      </div>
    );
  }

  return (
    <nav className={className}>
      <div className="space-y-2">
        {/* Services accessibles */}
        <div className="space-y-1">
          {accessibleNavItems.map((item) => {
            const isActive = location.pathname.startsWith(item.path);
            const servicePermissions = permissions?.[item.type.toLowerCase() as keyof typeof permissions];
            
            return (
              <NavLink
                key={item.type}
                to={item.path}
                className={({ isActive: linkActive }) => `
                  group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200
                  ${linkActive || isActive
                    ? `${item.color} shadow-sm`
                    : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                  }
                `}
              >
                <div className="flex items-center flex-1">
                  <span className="mr-3">
                    {item.icon}
                  </span>
                  <div className="flex-1">
                    <div className="font-medium">{item.name}</div>
                    {servicePermissions && (
                      <div className="text-xs opacity-75">
                        {servicePermissions.canOperate ? 'Opération' : 'Consultation'}
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Indicateur de permissions */}
                <div className="flex items-center space-x-1">
                  {servicePermissions?.canOperate && (
                    <div className="w-2 h-2 bg-green-400 rounded-full" title="Permissions d'opération" />
                  )}
                  {servicePermissions?.canAccess && !servicePermissions?.canOperate && (
                    <div className="w-2 h-2 bg-yellow-400 rounded-full" title="Permissions de consultation" />
                  )}
                </div>
              </NavLink>
            );
          })}
        </div>

        {/* Séparateur si il y a des services */}
        {accessibleNavItems.length > 0 && (
          <div className="border-t border-gray-200 my-3"></div>
        )}

        {/* Liens additionnels pour la gestion des services */}
        <div className="space-y-1">
          <NavLink
            to="/services/management"
            className={({ isActive }) => `
              group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200
              ${isActive
                ? 'text-gray-900 bg-gray-100 border border-gray-200'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }
            `}
          >
            <Settings size={20} className="mr-3" />
            <span>Gestion des services</span>
          </NavLink>

          <NavLink
            to="/services/reports"
            className={({ isActive }) => `
              group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200
              ${isActive
                ? 'text-gray-900 bg-gray-100 border border-gray-200'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }
            `}
          >
            <BarChart3 size={20} className="mr-3" />
            <span>Rapports services</span>
          </NavLink>
        </div>
      </div>

      {/* Résumé des permissions en bas */}
      <div className="mt-6 p-3 bg-gray-50 rounded-lg">
        <div className="text-xs text-gray-600 mb-2">Vos accès :</div>
        <div className="space-y-1">
          {accessibleNavItems.map((item) => {
            const servicePermissions = permissions?.[item.type.toLowerCase() as keyof typeof permissions];
            return (
              <div key={item.type} className="flex items-center justify-between text-xs">
                <span className="text-gray-700">{item.name}</span>
                <span className={`px-2 py-1 rounded-full ${
                  servicePermissions?.canOperate 
                    ? 'bg-green-100 text-green-700' 
                    : 'bg-yellow-100 text-yellow-700'
                }`}>
                  {servicePermissions?.canOperate ? 'Complet' : 'Lecture'}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    </nav>
  );
};

export default ServiceNavigation;
