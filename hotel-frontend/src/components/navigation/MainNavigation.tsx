import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import {
  Home,
  BedDouble,
  ShoppingCart,
  Users,
  Waves,
  Package,
  FileText,
  Settings,
  Warehouse,
  ChefHat,
  BarChart3,
  UserCheck
} from 'lucide-react';
import { useEmployeePermissions } from '../../hooks/useEmployeePermissions';
import { useAdminPermissions } from '../../hooks/useAdminPermissions';

interface NavigationItem {
  name: string;
  path: string;
  icon: React.ReactNode;
  color: string;
  description: string;
  children?: NavigationItem[];
}

interface MainNavigationProps {
  className?: string;
}

const MainNavigation: React.FC<MainNavigationProps> = ({ className = '' }) => {
  const location = useLocation();

  // Hooks de permissions
  const {
    isAdmin,
    employeeType,
    authorizedServices,
    canAccessService,
    loading: employeeLoading
  } = useEmployeePermissions();

  const {
    canAccessReports,
    canAccessConfiguration,
    canManageEmployees,
    loading: adminLoading
  } = useAdminPermissions();

  const loading = employeeLoading || adminLoading;

  // Fonction pour obtenir les éléments de navigation selon le type d'utilisateur
  const getNavigationItems = (): NavigationItem[] => {
    if (loading) return [];

    if (isAdmin) {
      return getAdminNavigationItems();
    }

    return getEmployeeNavigationItems();
  };

  // Navigation pour les administrateurs
  const getAdminNavigationItems = (): NavigationItem[] => [
    {
      name: 'Tableau de bord',
      path: '/dashboard',
      icon: <Home size={20} />,
      color: 'text-blue-600 bg-blue-50 border-blue-200',
      description: 'Vue d\'ensemble de l\'hôtel'
    },
    {
      name: 'Réception',
      path: '/reception',
      icon: <BedDouble size={20} />,
      color: 'text-green-600 bg-green-50 border-green-200',
      description: 'Gestion des réservations'
    },
    {
      name: 'Chambres',
      path: '/chambres',
      icon: <BedDouble size={20} />,
      color: 'text-teal-600 bg-teal-50 border-teal-200',
      description: 'Gestion des chambres'
    },
    {
      name: 'Réservations Anonymes',
      path: '/reservation-anonyme',
      icon: <UserCheck size={20} />,
      color: 'text-indigo-600 bg-indigo-50 border-indigo-200',
      description: 'Consulter une réservation anonyme'
    },
    {
      name: 'Services',
      path: '/services',
      icon: <Settings size={20} />,
      color: 'text-amber-600 bg-amber-50 border-amber-200',
      description: 'Gestion des services du complexe',
      children: [
        {
          name: 'Configuration Rapide',
          path: '/menu-setup',
          icon: <Settings size={16} />,
          color: 'text-green-600',
          description: 'Setup menu en 3 étapes'
        }
      ]
    },
    ...(canAccessConfiguration ? [{
      name: 'Inventaire',
      path: '/inventaire',
      icon: <Warehouse size={20} />,
      color: 'text-emerald-600 bg-emerald-50 border-emerald-200',
      description: 'Gestion des ingrédients et stocks',
      children: [
        {
          name: 'Ingrédients',
          path: '/inventaire?tab=ingredients',
          icon: <Package size={16} />,
          color: 'text-emerald-600',
          description: 'Gérer les ingrédients'
        },
        {
          name: 'Stock',
          path: '/inventaire?tab=stock',
          icon: <Warehouse size={16} />,
          color: 'text-emerald-600',
          description: 'Suivre les stocks'
        },

        {
          name: 'Analyses',
          path: '/inventaire?tab=analytics',
          icon: <BarChart3 size={16} />,
          color: 'text-emerald-600',
          description: 'Analyses et rapports'
        }
      ]
    }] : []),

    {
      name: 'POS',
      path: '/pos',
      icon: <ShoppingCart size={20} />,
      color: 'text-green-600 bg-green-50 border-green-200',
      description: 'Point de vente'
    },
    ...(canAccessConfiguration ? [{
      name: 'Gestion POS',
      path: '/pos-management',
      icon: <Settings size={20} />,
      color: 'text-gray-600 bg-gray-50 border-gray-200',
      description: 'Gestion des caisses'
    }] : []),
    {
      name: 'Piscine',
      path: '/pool',
      icon: <Waves size={20} />,
      color: 'text-cyan-600 bg-cyan-50 border-cyan-200',
      description: 'Gestion de la piscine'
    },
    ...(canManageEmployees ? [{
      name: 'Personnel',
      path: '/employee-management',
      icon: <Users size={20} />,
      color: 'text-purple-600 bg-purple-50 border-purple-200',
      description: 'Gestion des employés'
    }] : []),
    ...(canAccessReports ? [{
      name: 'Rapports',
      path: '/reports',
      icon: <FileText size={20} />,
      color: 'text-indigo-600 bg-indigo-50 border-indigo-200',
      description: 'Rapports et analyses'
    }] : [])
  ];

  // Navigation pour les employés selon leur type
  const getEmployeeNavigationItems = (): NavigationItem[] => {
    const baseItems: NavigationItem[] = [
      {
        name: 'Tableau de bord',
        path: '/dashboard',
        icon: <Home size={20} />,
        color: 'text-blue-600 bg-blue-50 border-blue-200',
        description: 'Vue d\'ensemble'
      }
    ];

    switch (employeeType) {
      case 'reception':
        return [
          ...baseItems,
          {
            name: 'Réception',
            path: '/reception',
            icon: <BedDouble size={20} />,
            color: 'text-green-600 bg-green-50 border-green-200',
            description: 'Gestion des réservations'
          },
          {
            name: 'Chambres',
            path: '/chambres',
            icon: <BedDouble size={20} />,
            color: 'text-teal-600 bg-teal-50 border-teal-200',
            description: 'Gestion des chambres'
          }
        ];

      case 'gerant_piscine':
        return [
          ...baseItems,
          {
            name: 'Piscine',
            path: '/pool',
            icon: <Waves size={20} />,
            color: 'text-cyan-600 bg-cyan-50 border-cyan-200',
            description: 'Gestion de la piscine'
          }
        ];

      case 'serveuse':
      case 'gerant_services':
        const serviceItems: NavigationItem[] = [...baseItems];

        if (canAccessService('restaurant') || canAccessService('bar')) {
          serviceItems.push({
            name: 'POS',
            path: '/pos',
            icon: <ShoppingCart size={20} />,
            color: 'text-green-600 bg-green-50 border-green-200',
            description: 'Point de vente'
          });
        }

        return serviceItems;

      case 'cuisine':
        return [
          ...baseItems,
          {
            name: 'Cuisine',
            path: '/pos?view=kitchen',
            icon: <ChefHat size={20} />,
            color: 'text-orange-600 bg-orange-50 border-orange-200',
            description: 'Commandes cuisine'
          }
        ];

      default:
        return baseItems;
    }
  };

  const navigationItems = getNavigationItems();

  const isActiveItem = (item: NavigationItem) => {
    if (item.path === '/dashboard') {
      return location.pathname === '/dashboard';
    }
    return location.pathname.startsWith(item.path);
  };

  const renderNavigationItem = (item: NavigationItem, isChild = false) => {
    const isActive = isActiveItem(item);
    
    return (
      <div key={item.path}>
        <NavLink
          to={item.path}
          className={({ isActive: linkActive }) => `
            group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200
            ${linkActive || isActive
              ? `${item.color} shadow-sm`
              : isChild 
                ? 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 ml-6'
                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
            }
            ${isChild ? 'text-xs' : ''}
          `}
        >
          <span className={`${isChild ? 'mr-2' : 'mr-3'}`}>
            {item.icon}
          </span>
          <div className="flex-1">
            <div className={`${isChild ? 'text-xs' : 'text-sm'} font-medium`}>
              {item.name}
            </div>
            {!isChild && (
              <div className="text-xs opacity-75 mt-0.5">
                {item.description}
              </div>
            )}
          </div>
        </NavLink>
        
        {/* Sous-éléments */}
        {item.children && isActive && (
          <div className="mt-1 space-y-1">
            {item.children.map(child => renderNavigationItem(child, true))}
          </div>
        )}
      </div>
    );
  };

  // Affichage du loading
  if (loading) {
    return (
      <nav className={className}>
        <div className="space-y-2">
          <div className="animate-pulse">
            <div className="h-12 bg-gray-200 rounded-lg mb-2"></div>
            <div className="h-12 bg-gray-200 rounded-lg mb-2"></div>
            <div className="h-12 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </nav>
    );
  }

  return (
    <nav className={className}>
      <div className="space-y-2">
        {navigationItems.map(item => renderNavigationItem(item))}
      </div>
    </nav>
  );
};

export default MainNavigation;
