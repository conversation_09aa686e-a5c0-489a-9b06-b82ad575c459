// ===== COMPOSANTS DE NAVIGATION PRINCIPAUX =====
export { default as MainNavigation } from './MainNavigation';
export { default as AdminNavigation } from './AdminNavigation';
export { default as EmployeeNavigation } from './EmployeeNavigation';

// ===== NAVIGATION INTELLIGENTE =====
export { 
  default as SmartNavigation,
  CompactNavigation,
  FullNavigation,
  ContextualNavigation,
  NavigationWithBreadcrumbs,
  useNavigationType
} from './SmartNavigation';

// ===== MENUS ET ACTIONS =====
export { 
  default as QuickActionsMenu,
  CompactQuickActions,
  QuickNotifications
} from './QuickActionsMenu';

// ===== TYPES ET INTERFACES =====
export interface NavigationItem {
  name: string;
  path: string;
  icon: React.ReactNode;
  color: string;
  description: string;
  badge?: string | number;
  children?: NavigationItem[];
  isExpanded?: boolean;
}

export interface QuickAction {
  name: string;
  icon: React.ReactNode;
  action: () => void;
  color: string;
  description: string;
  shortcut?: string;
}

// ===== CONSTANTES UTILES =====
export const NAVIGATION_TYPES = {
  ADMIN: 'admin',
  EMPLOYEE: 'employee',
  MAIN: 'main',
  LOADING: 'loading'
} as const;

export const EMPLOYEE_NAVIGATION_CONFIGS = {
  reception: {
    primaryColor: 'green',
    icon: 'BedDouble',
    mainPath: '/reception'
  },
  gerant_piscine: {
    primaryColor: 'cyan',
    icon: 'Waves',
    mainPath: '/pool'
  },
  serveuse: {
    primaryColor: 'purple',
    icon: 'ShoppingCart',
    mainPath: '/pos'
  },
  gerant_services: {
    primaryColor: 'amber',
    icon: 'Settings',
    mainPath: '/pos'
  },
  cuisine: {
    primaryColor: 'orange',
    icon: 'ChefHat',
    mainPath: '/pos?view=kitchen'
  }
} as const;

// ===== HELPERS POUR LA NAVIGATION =====

/**
 * Helper pour créer des éléments de navigation
 */
export const createNavigationItem = (
  name: string,
  path: string,
  icon: React.ReactNode,
  options: {
    color?: string;
    description?: string;
    badge?: string | number;
    children?: NavigationItem[];
  } = {}
): NavigationItem => {
  return {
    name,
    path,
    icon,
    color: options.color || 'text-gray-600 bg-gray-50 border-gray-200',
    description: options.description || '',
    badge: options.badge,
    children: options.children
  };
};

/**
 * Helper pour créer des actions rapides
 */
export const createQuickAction = (
  name: string,
  icon: React.ReactNode,
  action: () => void,
  options: {
    color?: string;
    description?: string;
    shortcut?: string;
  } = {}
): QuickAction => {
  return {
    name,
    icon,
    action,
    color: options.color || 'text-gray-600 bg-gray-50 hover:bg-gray-100',
    description: options.description || '',
    shortcut: options.shortcut
  };
};

/**
 * Helper pour obtenir la configuration de navigation d'un employé
 */
export const getEmployeeNavigationConfig = (employeeType: string) => {
  return EMPLOYEE_NAVIGATION_CONFIGS[employeeType as keyof typeof EMPLOYEE_NAVIGATION_CONFIGS] || {
    primaryColor: 'gray',
    icon: 'User',
    mainPath: '/dashboard'
  };
};

/**
 * Helper pour générer des couleurs de navigation
 */
export const generateNavigationColors = (baseColor: string) => {
  return {
    active: `text-${baseColor}-600 bg-${baseColor}-50 border-${baseColor}-200`,
    hover: `hover:bg-${baseColor}-50 hover:text-${baseColor}-900`,
    icon: `text-${baseColor}-600`,
    badge: `bg-${baseColor}-500 text-white`
  };
};

// ===== HOOKS UTILITAIRES =====

/**
 * Hook pour obtenir les actions rapides recommandées
 */
export const useQuickActions = () => {
  // Cette fonction sera implémentée pour retourner les actions rapides
  // selon le type d'utilisateur et le contexte de la page
  return {
    actions: [],
    loading: false
  };
};

/**
 * Hook pour la navigation contextuelle
 */
export const useContextualNavigation = (pageType?: string) => {
  // Cette fonction sera implémentée pour adapter la navigation
  // selon le contexte de la page
  return {
    navigationComponent: SmartNavigation,
    quickActions: [],
    breadcrumbs: []
  };
};

// ===== DOCUMENTATION =====

/**
 * Guide d'utilisation des composants de navigation :
 * 
 * 1. Navigation intelligente (recommandé) :
 *    ```tsx
 *    import { SmartNavigation } from '@/components/navigation';
 *    
 *    const Layout = () => (
 *      <div className="flex">
 *        <SmartNavigation className="w-64" />
 *        <main className="flex-1">...</main>
 *      </div>
 *    );
 *    ```
 * 
 * 2. Navigation spécialisée :
 *    ```tsx
 *    import { AdminNavigation, EmployeeNavigation } from '@/components/navigation';
 *    
 *    const AdminLayout = () => (
 *      <AdminNavigation className="w-64" />
 *    );
 *    ```
 * 
 * 3. Actions rapides :
 *    ```tsx
 *    import { QuickActionsMenu } from '@/components/navigation';
 *    
 *    const Sidebar = () => (
 *      <div>
 *        <SmartNavigation />
 *        <QuickActionsMenu className="mt-4" />
 *      </div>
 *    );
 *    ```
 * 
 * 4. Navigation contextuelle :
 *    ```tsx
 *    import { ContextualNavigation } from '@/components/navigation';
 *    
 *    const PageLayout = () => (
 *      <ContextualNavigation 
 *        pageContext="employee" 
 *        className="w-64" 
 *      />
 *    );
 *    ```
 * 
 * 5. Navigation avec breadcrumbs :
 *    ```tsx
 *    import { NavigationWithBreadcrumbs } from '@/components/navigation';
 *    
 *    const PageWithBreadcrumbs = () => (
 *      <NavigationWithBreadcrumbs 
 *        breadcrumbs={[
 *          { name: 'Accueil', path: '/dashboard' },
 *          { name: 'Réception', path: '/reception' },
 *          { name: 'Nouvelle réservation' }
 *        ]}
 *      />
 *    );
 *    ```
 */
