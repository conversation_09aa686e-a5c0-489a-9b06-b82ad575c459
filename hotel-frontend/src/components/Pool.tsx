import { useState, useEffect } from 'react'
import { QRCodeSVG } from 'qrcode.react'
import { CheckCircle2, XCircle, History, Users2, CreditCard, DollarSign, Clock, User, Printer, Calculator, Play, Square, AlertCircle } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { serviceComplexeService, ServiceComplexe } from '../services/service.service'
import { posService, PointDeVente } from '../services/pos.service'
import { sessionCaisseService, SessionCaisse } from '../services/session.service'
import { ticketService, CreateServiceTicketData, ServiceTicket } from '../services/ticket.service'

interface BilletterieForm {
  nom_client: string;
  nombre_personnes: number;
  duree_heures: number;
  mode_paiement: 'Espèces' | 'Carte';
}

interface TarifPiscine {
  prix_par_personne?: number;
  prix_par_heure?: number;
  prix_forfaitaire?: number;
  tarifs_age?: Record<string, number>;
  tarifs_duree?: Record<string, number>;
}

interface PoolProps {
  onClose: () => void;
}

export function Pool({ onClose }: PoolProps) {
  // États pour les piscines du backend
  const [piscines, setPiscines] = useState<ServiceComplexe[]>([])
  const [selectedPiscine, setSelectedPiscine] = useState<ServiceComplexe | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // États pour les points de vente et sessions
  const [pointDeVente, setPointDeVente] = useState<PointDeVente | null>(null)
  const [sessionActive, setSessionActive] = useState<SessionCaisse | null>(null)
  const [loadingPOS, setLoadingPOS] = useState(false)
  const [showSessionModal, setShowSessionModal] = useState(false)
  const [fondsOuverture, setFondsOuverture] = useState<number>(0)

  // États pour la billetterie
  const [showBilletterie, setShowBilletterie] = useState(false)
  const [billetterieForm, setBilletterieForm] = useState<BilletterieForm>({
    nom_client: '',
    nombre_personnes: 1,
    duree_heures: 1,
    mode_paiement: 'Espèces'
  })
  const [prixCalcule, setPrixCalcule] = useState(0)
  const [processingPayment, setProcessingPayment] = useState(false)
  const [ticketGenere, setTicketGenere] = useState<ServiceTicket | null>(null)
  const [showTicketModal, setShowTicketModal] = useState(false)

  // États pour l'historique des tickets
  const [ticketsHistorique, setTicketsHistorique] = useState<ServiceTicket[]>([])
  const [loadingHistorique, setLoadingHistorique] = useState(false)

  // Charger les piscines au montage du composant
  useEffect(() => {
    const loadPiscines = async () => {
      try {
        setLoading(true)
        setError(null)
        const services = await serviceComplexeService.getServicesByType('Piscine')
        setPiscines(services)

        // Sélectionner la première piscine par défaut si elle existe
        if (services.length > 0) {
          setSelectedPiscine(services[0])
        }
      } catch (err) {
        console.error('Erreur lors du chargement des piscines:', err)
        setError('Erreur lors du chargement des piscines')
      } finally {
        setLoading(false)
      }
    }

    loadPiscines()
  }, [])

  // Charger le point de vente et la session active quand une piscine est sélectionnée
  useEffect(() => {
    if (selectedPiscine) {
      loadPointDeVenteEtSession()
    }
  }, [selectedPiscine])

  // Calculer le prix automatiquement quand les paramètres changent
  useEffect(() => {
    if (selectedPiscine && billetterieForm.nombre_personnes > 0 && billetterieForm.duree_heures > 0) {
      calculerPrix()
    }
  }, [selectedPiscine, billetterieForm.nombre_personnes, billetterieForm.duree_heures])

  // Gérer le changement de piscine sélectionnée
  const handlePiscineChange = (piscineId: string) => {
    const piscine = piscines.find(p => p.service_id.toString() === piscineId)
    if (piscine) {
      setSelectedPiscine(piscine)
    }
  }

  // Charger le point de vente et la session active
  const loadPointDeVenteEtSession = async () => {
    if (!selectedPiscine) return

    try {
      setLoadingPOS(true)

      // Récupérer ou créer un point de vente pour cette piscine
      const pos = await posService.getOrCreatePOSForService(selectedPiscine.service_id)
      setPointDeVente(pos)

      // Vérifier s'il y a une session active
      const session = await sessionCaisseService.getActiveSession(pos.pos_id)
      setSessionActive(session)

      if (!session) {
        setShowSessionModal(true)
      }
    } catch (error) {
      console.error('Erreur lors du chargement du POS:', error)
      toast.error('Erreur lors du chargement du point de vente')
    } finally {
      setLoadingPOS(false)
    }
  }

  // Ouvrir une session de caisse
  const ouvrirSession = async () => {
    if (!pointDeVente || !selectedPiscine) return

    try {
      const session = await sessionCaisseService.ouvrirSession({
        pos_id: pointDeVente.pos_id,
        service_id: selectedPiscine.service_id,
        fonds_ouverture: fondsOuverture,
        notes: `Session ouverte pour ${selectedPiscine.nom}`
      })

      setSessionActive(session)
      setShowSessionModal(false)
      setFondsOuverture(0)
      toast.success('Session de caisse ouverte avec succès')
    } catch (error) {
      console.error('Erreur ouverture session:', error)
      toast.error('Erreur lors de l\'ouverture de la session')
    }
  }

  // Calculer le prix du ticket
  const calculerPrix = () => {
    if (!selectedPiscine?.tarification) {
      setPrixCalcule(0)
      return
    }

    const tarif: TarifPiscine = selectedPiscine.tarification as TarifPiscine
    let prixTotal = 0

    // Calcul basé sur les tarifs configurés
    if (tarif.prix_par_personne && tarif.prix_par_heure) {
      // Prix par personne ET par heure
      prixTotal = tarif.prix_par_personne * billetterieForm.nombre_personnes +
                  tarif.prix_par_heure * billetterieForm.duree_heures
    } else if (tarif.prix_par_personne) {
      // Prix par personne uniquement
      prixTotal = tarif.prix_par_personne * billetterieForm.nombre_personnes
    } else if (tarif.prix_par_heure) {
      // Prix par heure uniquement
      prixTotal = tarif.prix_par_heure * billetterieForm.duree_heures
    } else {
      // Prix par défaut si aucune tarification configurée
      prixTotal = 5 * billetterieForm.nombre_personnes * billetterieForm.duree_heures
    }

    // Appliquer le prix forfaitaire si configuré et plus avantageux
    if (tarif.prix_forfaitaire && tarif.prix_forfaitaire > prixTotal) {
      prixTotal = tarif.prix_forfaitaire
    }

    setPrixCalcule(Math.round(prixTotal * 100) / 100)
  }

  // Créer un ticket de piscine avec paiement
  const creerTicketPiscine = async () => {
    if (!selectedPiscine || !sessionActive || !pointDeVente) {
      toast.error('Session de caisse requise pour créer un ticket')
      return
    }

    if (!billetterieForm.nom_client.trim()) {
      toast.error('Le nom du client est requis')
      return
    }

    try {
      setProcessingPayment(true)

      const ticketData: CreateServiceTicketData = {
        service_id: selectedPiscine.service_id,
        pos_id: pointDeVente.pos_id,
        session_id: sessionActive.session_id,
        nom_client: billetterieForm.nom_client.trim(),
        nombre_personnes: billetterieForm.nombre_personnes,
        duree_heures: billetterieForm.duree_heures,
        prix_total: prixCalcule,
        mode_paiement: billetterieForm.mode_paiement
      }

      const result = await ticketService.createServiceTicket(ticketData)

      setTicketGenere(result.ticket)
      setShowTicketModal(true)
      setShowBilletterie(false)

      // Réinitialiser le formulaire
      setBilletterieForm({
        nom_client: '',
        nombre_personnes: 1,
        duree_heures: 1,
        mode_paiement: 'Espèces'
      })
      setPrixCalcule(0)

      toast.success('Ticket créé et payé avec succès!')

      // Recharger l'historique
      loadHistoriqueTickets()
    } catch (error: any) {
      console.error('Erreur création ticket:', error)
      toast.error(error.message || 'Erreur lors de la création du ticket')
    } finally {
      setProcessingPayment(false)
    }
  }

  // Charger l'historique des tickets
  const loadHistoriqueTickets = async () => {
    if (!selectedPiscine) return

    try {
      setLoadingHistorique(true)
      const tickets = await ticketService.getServiceTickets(selectedPiscine.service_id)
      setTicketsHistorique(tickets)
    } catch (error) {
      console.error('Erreur chargement historique:', error)
    } finally {
      setLoadingHistorique(false)
    }
  }

  // Imprimer le ticket
  const imprimerTicket = () => {
    if (!ticketGenere) return

    // Créer le contenu d'impression
    const printContent = `
      <div style="font-family: monospace; width: 300px; margin: 0 auto;">
        <div style="text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 10px;">
          <h2>TICKET PISCINE</h2>
          <p>${selectedPiscine?.nom || 'Piscine'}</p>
        </div>

        <div style="margin-bottom: 10px;">
          <p><strong>N° Ticket:</strong> ${ticketGenere.numero_ticket}</p>
          <p><strong>Client:</strong> ${ticketGenere.nom_client}</p>
          <p><strong>Personnes:</strong> ${ticketGenere.nombre_personnes}</p>
          <p><strong>Durée:</strong> ${ticketGenere.duree_heures}h</p>
          <p><strong>Prix:</strong> ${ticketGenere.prix_total}FCFA</p>
          <p><strong>Paiement:</strong> ${ticketGenere.mode_paiement}</p>
          <p><strong>Date:</strong> ${new Date(ticketGenere.date_emission).toLocaleString()}</p>
        </div>

        <div style="text-align: center; margin: 20px 0;">
          <div id="qrcode"></div>
        </div>

        <div style="text-align: center; font-size: 12px; border-top: 1px solid #000; padding-top: 10px;">
          <p>Présentez ce ticket pour accéder à la piscine</p>
          <p>Ticket valable jusqu'au ${new Date(ticketGenere.date_expiration || '').toLocaleString()}</p>
        </div>
      </div>
    `

    // Ouvrir une nouvelle fenêtre pour l'impression
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Ticket Piscine - ${ticketGenere.numero_ticket}</title>
            <style>
              body { margin: 0; padding: 20px; }
              @media print { body { margin: 0; } }
            </style>
          </head>
          <body>
            ${printContent}
            <script>
              // Ajouter le QR code
              const qrDiv = document.getElementById('qrcode');
              if (qrDiv) {
                qrDiv.innerHTML = '<img src="data:image/png;base64,${ticketGenere.qr_code_data}" style="width: 100px; height: 100px;" />';
              }

              // Imprimer automatiquement
              window.onload = function() {
                window.print();
                window.onafterprint = function() {
                  window.close();
                };
              };
            </script>
          </body>
        </html>
      `)
      printWindow.document.close()
    }
  }

  // Affichage du loading
  if (loading) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Chargement des piscines...</div>
        </div>
      </div>
    )
  }

  // Affichage de l'erreur
  if (error) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg text-red-600">{error}</div>
        </div>
      </div>
    )
  }

  // Affichage si aucune piscine trouvée
  if (piscines.length === 0) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg text-gray-600">Aucune piscine configurée pour ce complexe</div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold">
            Billetterie Piscine - {selectedPiscine?.nom || 'Piscine'}
          </h1>
          {piscines.length > 1 && (
            <select
              value={selectedPiscine?.service_id || ''}
              onChange={(e) => handlePiscineChange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg bg-white"
            >
              {piscines.map((piscine) => (
                <option key={piscine.service_id} value={piscine.service_id}>
                  {piscine.nom}
                </option>
              ))}
            </select>
          )}
        </div>
        <button
          onClick={onClose}
          className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
        >
          Fermer
        </button>
      </div>

      {/* Statut de la session de caisse */}
      <div className="mb-6 p-4 bg-white rounded-lg shadow">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${sessionActive ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="font-medium">
              {sessionActive ? 'Session de caisse ouverte' : 'Session de caisse fermée'}
            </span>
            {sessionActive && (
              <span className="text-sm text-gray-600">
                Fonds: {sessionActive.fonds_ouverture}FCFA
              </span>
            )}
          </div>
          {!sessionActive && pointDeVente && (
            <button
              onClick={() => setShowSessionModal(true)}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              <Play className="h-4 w-4 mr-2 inline" />
              Ouvrir Session
            </button>
          )}
        </div>

        {/* Messages informatifs */}
        {!pointDeVente && (
          <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-yellow-600 mr-2" />
              <div>
                <p className="text-sm font-medium text-yellow-800">Point de vente requis</p>
                <p className="text-xs text-yellow-700 mt-1">
                  Veuillez d'abord créer un point de vente pour cette piscine dans
                  <span className="font-medium"> Gestion POS</span> avant de pouvoir vendre des tickets.
                </p>
              </div>
            </div>
          </div>
        )}

        {pointDeVente && !sessionActive && (
          <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-blue-600 mr-2" />
              <div>
                <p className="text-sm font-medium text-blue-800">Session de caisse fermée</p>
                <p className="text-xs text-blue-700 mt-1">
                  Ouvrez une session de caisse pour commencer à vendre des tickets d'accès piscine.
                </p>
              </div>
            </div>
          </div>
        )}

        {!selectedPiscine?.tarification && (
          <div className="mt-3 p-3 bg-orange-50 border border-orange-200 rounded-lg">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-orange-600 mr-2" />
              <div>
                <p className="text-sm font-medium text-orange-800">Tarification non configurée</p>
                <p className="text-xs text-orange-700 mt-1">
                  Configurez les tarifs de cette piscine dans
                  <span className="font-medium"> Services</span> pour un calcul automatique des prix.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Actions rapides */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Actions rapides</h2>
          <div className="space-y-4">
            <button
              onClick={() => setShowBilletterie(true)}
              disabled={!sessionActive || !pointDeVente}
              className="w-full flex items-center justify-center px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <CreditCard className="h-5 w-5 mr-2" />
              Nouvelle Billetterie
            </button>

            {(!sessionActive || !pointDeVente) && (
              <p className="text-xs text-gray-500 text-center mt-1">
                {!pointDeVente ? 'Point de vente requis' : 'Session de caisse requise'}
              </p>
            )}

            <button
              onClick={loadHistoriqueTickets}
              className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              <History className="h-5 w-5 mr-2" />
              Actualiser Historique
            </button>

            {sessionActive && (
              <div className="p-4 bg-green-50 rounded-lg">
                <div className="flex items-center justify-between text-sm">
                  <span>Total ventes session:</span>
                  <span className="font-medium">{sessionActive.total_ventes || 0}FCFA</span>
                </div>
                <div className="flex items-center justify-between text-sm mt-1">
                  <span>Espèces:</span>
                  <span>{sessionActive.total_especes || 0}FCFA</span>
                </div>
                <div className="flex items-center justify-between text-sm mt-1">
                  <span>Cartes:</span>
                  <span>{sessionActive.total_cartes || 0}FCFA</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Historique des tickets */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Tickets récents</h2>
          {loadingHistorique ? (
            <div className="text-center py-4">Chargement...</div>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {ticketsHistorique.slice(0, 10).map((ticket) => (
                <div key={ticket.ticket_id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">{ticket.nom_client}</p>
                    <p className="text-sm text-gray-500">
                      {ticket.nombre_personnes} pers. - {ticket.duree_heures}h - {ticket.prix_total}FCFA
                    </p>
                    <p className="text-xs text-gray-400">
                      {new Date(ticket.date_emission).toLocaleString()}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      ticket.statut === 'ACTIF' ? 'bg-green-100 text-green-700' :
                      ticket.statut === 'UTILISE' ? 'bg-blue-100 text-blue-700' :
                      'bg-gray-100 text-gray-700'
                    }`}>
                      {ticket.statut}
                    </span>
                  </div>
                </div>
              ))}
              {ticketsHistorique.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  Aucun ticket trouvé
                </div>
              )}
            </div>
          )}
        </div>

        {/* Informations tarifaires */}
        <div className="bg-white rounded-lg shadow p-6 lg:col-span-2">
          <h2 className="text-xl font-semibold mb-4">Tarification</h2>
          {selectedPiscine?.tarification ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {(selectedPiscine.tarification as TarifPiscine).prix_par_personne && (
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center">
                    <Users2 className="h-5 w-5 text-blue-500 mr-2" />
                    <span className="font-medium">Prix par personne</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-600 mt-2">
                    {(selectedPiscine.tarification as TarifPiscine).prix_par_personne}FCFA
                  </p>
                </div>
              )}

              {(selectedPiscine.tarification as TarifPiscine).prix_par_heure && (
                <div className="p-4 bg-green-50 rounded-lg">
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 text-green-500 mr-2" />
                    <span className="font-medium">Prix par heure</span>
                  </div>
                  <p className="text-2xl font-bold text-green-600 mt-2">
                    {(selectedPiscine.tarification as TarifPiscine).prix_par_heure}FCFA
                  </p>
                </div>
              )}

              {(selectedPiscine.tarification as TarifPiscine).prix_forfaitaire && (
                <div className="p-4 bg-purple-50 rounded-lg">
                  <div className="flex items-center">
                    <Calculator className="h-5 w-5 text-purple-500 mr-2" />
                    <span className="font-medium">Prix forfaitaire</span>
                  </div>
                  <p className="text-2xl font-bold text-purple-600 mt-2">
                    {(selectedPiscine.tarification as TarifPiscine).prix_forfaitaire}FCFA
                  </p>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              Aucune tarification configurée pour cette piscine
            </div>
          )}
        </div>
      </div>

      {/* Modal d'ouverture de session */}
      {showSessionModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Ouvrir une session de caisse</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Fonds d'ouverture (FCFA)
                </label>
                <input
                  type="number"
                  value={fondsOuverture}
                  onChange={(e) => setFondsOuverture(Number(e.target.value))}
                  className="w-full p-2 border rounded-lg"
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                />
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowSessionModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  Annuler
                </button>
                <button
                  onClick={ouvrirSession}
                  className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
                >
                  Ouvrir
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de billetterie */}
      {showBilletterie && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-lg">
            <h3 className="text-lg font-semibold mb-4">Nouvelle billetterie piscine</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <User className="h-4 w-4 inline mr-1" />
                  Nom du client
                </label>
                <input
                  type="text"
                  value={billetterieForm.nom_client}
                  onChange={(e) => setBilletterieForm({...billetterieForm, nom_client: e.target.value})}
                  className="w-full p-2 border rounded-lg"
                  placeholder="Nom du client"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <Users2 className="h-4 w-4 inline mr-1" />
                    Nombre de personnes
                  </label>
                  <input
                    type="number"
                    value={billetterieForm.nombre_personnes}
                    onChange={(e) => setBilletterieForm({...billetterieForm, nombre_personnes: Number(e.target.value)})}
                    className="w-full p-2 border rounded-lg"
                    min="1"
                    max="20"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <Clock className="h-4 w-4 inline mr-1" />
                    Durée (heures)
                  </label>
                  <input
                    type="number"
                    value={billetterieForm.duree_heures}
                    onChange={(e) => setBilletterieForm({...billetterieForm, duree_heures: Number(e.target.value)})}
                    className="w-full p-2 border rounded-lg"
                    min="0.5"
                    max="12"
                    step="0.5"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <CreditCard className="h-4 w-4 inline mr-1" />
                  Mode de paiement
                </label>
                <select
                  value={billetterieForm.mode_paiement}
                  onChange={(e) => setBilletterieForm({...billetterieForm, mode_paiement: e.target.value as 'Espèces' | 'Carte'})}
                  className="w-full p-2 border rounded-lg"
                >
                  <option value="Espèces">Espèces</option>
                  <option value="Carte">Carte bancaire</option>
                </select>
              </div>

              {/* Calcul du prix */}
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Prix total:</span>
                  <span className="text-2xl font-bold text-blue-600">{prixCalcule.toFixed(2)}FCFA</span>
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {billetterieForm.nombre_personnes} pers. × {billetterieForm.duree_heures}h
                  {selectedPiscine?.tarification && (
                    <div className="text-xs mt-1">
                      {(selectedPiscine.tarification as TarifPiscine).prix_par_personne &&
                        `Prix/pers: ${(selectedPiscine.tarification as TarifPiscine).prix_par_personne}FCFA`}
                      {(selectedPiscine.tarification as TarifPiscine).prix_par_heure &&
                        ` • Prix/h: ${(selectedPiscine.tarification as TarifPiscine).prix_par_heure}FCFA`}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => setShowBilletterie(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  Annuler
                </button>
                <button
                  onClick={creerTicketPiscine}
                  disabled={processingPayment || !billetterieForm.nom_client.trim() || prixCalcule <= 0}
                  className="flex-1 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {processingPayment ? 'Traitement...' : `Payer ${prixCalcule.toFixed(2)}FCFA`}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de ticket généré */}
      {showTicketModal && ticketGenere && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4 text-center">Ticket généré avec succès!</h3>

            <div className="text-center space-y-4">
              <div className="p-4 bg-green-50 rounded-lg">
                <p className="font-medium">N° Ticket: {ticketGenere.numero_ticket}</p>
                <p>Client: {ticketGenere.nom_client}</p>
                <p>{ticketGenere.nombre_personnes} personnes - {ticketGenere.duree_heures}h</p>
                <p className="text-lg font-bold text-green-600">{ticketGenere.prix_total}FCFA</p>
              </div>

              {/* QR Code */}
              <div className="flex justify-center">
                <QRCodeSVG
                  value={ticketGenere.qr_code_data || ''}
                  size={150}
                  className="border rounded"
                />
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => setShowTicketModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  Fermer
                </button>
                <button
                  onClick={imprimerTicket}
                  className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
                >
                  <Printer className="h-4 w-4 mr-2 inline" />
                  Imprimer
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}