import { Link } from 'react-router-dom';
import { UserCheck, Search, Shield } from 'lucide-react';

interface PublicAnonymousReservationLinkProps {
  variant?: 'button' | 'card' | 'link';
  className?: string;
}

export function PublicAnonymousReservationLink({ 
  variant = 'button', 
  className = '' 
}: PublicAnonymousReservationLinkProps) {
  
  if (variant === 'card') {
    return (
      <Link 
        to="/reservation-anonyme" 
        className={`block bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 p-6 border border-gray-200 hover:border-blue-300 ${className}`}
      >
        <div className="flex items-center space-x-4">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <UserCheck className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-1">
              Consulter ma réservation anonyme
            </h3>
            <p className="text-sm text-gray-600">
              Accédez à votre réservation avec votre code d'accès unique
            </p>
          </div>
          <div className="flex-shrink-0">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
        </div>
        
        <div className="mt-4 flex items-center text-xs text-gray-500">
          <Shield className="h-3 w-3 mr-1" />
          <span>Accès sécurisé et confidentiel</span>
        </div>
      </Link>
    );
  }

  if (variant === 'link') {
    return (
      <Link 
        to="/reservation-anonyme" 
        className={`inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors duration-200 ${className}`}
      >
        <UserCheck className="h-4 w-4 mr-2" />
        <span>Consulter ma réservation anonyme</span>
      </Link>
    );
  }

  // Variant 'button' par défaut
  return (
    <Link 
      to="/reservation-anonyme" 
      className={`inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 ${className}`}
    >
      <UserCheck className="h-5 w-5 mr-2" />
      <span>Consulter ma réservation</span>
    </Link>
  );
}

// Composant pour une bannière d'information sur les réservations anonymes
export function AnonymousReservationInfoBanner({ className = '' }: { className?: string }) {
  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <Shield className="h-5 w-5 text-blue-500 mt-0.5" />
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-blue-900">
            Réservations anonymes disponibles
          </h3>
          <p className="text-sm text-blue-700 mt-1">
            Vous pouvez maintenant effectuer des réservations sans fournir vos informations personnelles. 
            Vous recevrez un code d'accès unique pour consulter et gérer votre réservation.
          </p>
          <div className="mt-3">
            <PublicAnonymousReservationLink variant="link" />
          </div>
        </div>
      </div>
    </div>
  );
}

// Composant pour un widget dans le footer
export function AnonymousReservationFooterWidget({ className = '' }: { className?: string }) {
  return (
    <div className={`text-center ${className}`}>
      <h4 className="text-sm font-semibold text-gray-900 mb-2">
        Réservation anonyme
      </h4>
      <p className="text-xs text-gray-600 mb-3">
        Consultez votre réservation avec votre code d'accès
      </p>
      <PublicAnonymousReservationLink 
        variant="button" 
        className="w-full text-sm py-2 px-4"
      />
    </div>
  );
}
