import { useState } from 'react';
import { toast } from 'react-hot-toast';
import { Search, Eye, EyeOff, Copy, Check } from 'lucide-react';
import { anonymousReservationService, AnonymousReservationDetails } from '../services/anonymousReservation.service';
import { AnonymousReservationDetails as AnonymousReservationDetailsComponent } from './AnonymousReservationDetails';

export function AnonymousReservationLookup() {
  const [codeAcces, setCodeAcces] = useState('');
  const [reservation, setReservation] = useState<AnonymousReservationDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showCode, setShowCode] = useState(false);
  const [copied, setCopied] = useState(false);

  const handleLookup = async () => {
    if (!codeAcces.trim()) {
      toast.error('Veuillez entrer votre code d\'accès');
      return;
    }

    setIsLoading(true);
    try {
      const response = await anonymousReservationService.getReservationAnonyme(codeAcces.trim().toUpperCase());
      if (response.success) {
        setReservation(response.data);
        toast.success('Réservation trouvée !');
      }
    } catch (error) {
      console.error('Erreur lors de la recherche:', error);
      toast.error(error instanceof Error ? error.message : 'Réservation non trouvée ou code invalide');
      setReservation(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleLookup();
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(codeAcces);
      setCopied(true);
      toast.success('Code copié dans le presse-papiers');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Erreur lors de la copie');
    }
  };

  const formatCodeInput = (value: string) => {
    // Supprimer tous les caractères non alphanumériques et convertir en majuscules
    const cleaned = value.replace(/[^A-Z0-9]/gi, '').toUpperCase();
    
    // Ajouter le tiret après les 4 premiers caractères si nécessaire
    if (cleaned.length > 4) {
      return cleaned.slice(0, 4) + '-' + cleaned.slice(4, 16);
    }
    return cleaned;
  };

  const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatCodeInput(e.target.value);
    setCodeAcces(formatted);
  };

  const resetSearch = () => {
    setReservation(null);
    setCodeAcces('');
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Consulter ma réservation anonyme
        </h1>
        <p className="text-gray-600">
          Entrez votre code d'accès pour consulter et gérer votre réservation
        </p>
      </div>

      {!reservation ? (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Code d'accès
              </label>
              <div className="relative">
                <input
                  type={showCode ? 'text' : 'password'}
                  value={codeAcces}
                  onChange={handleCodeChange}
                  onKeyPress={handleKeyPress}
                  placeholder="ANON-XXXXXXXXXXXX"
                  className="w-full px-4 py-3 pr-20 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-lg"
                  maxLength={17}
                />
                <div className="absolute inset-y-0 right-0 flex items-center space-x-1 pr-3">
                  <button
                    type="button"
                    onClick={() => setShowCode(!showCode)}
                    className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                    title={showCode ? 'Masquer le code' : 'Afficher le code'}
                  >
                    {showCode ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                  {codeAcces && (
                    <button
                      type="button"
                      onClick={copyToClipboard}
                      className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                      title="Copier le code"
                    >
                      {copied ? <Check className="h-4 w-4 text-green-500" /> : <Copy className="h-4 w-4" />}
                    </button>
                  )}
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Format: ANON-XXXXXXXXXXXX (4 lettres + tiret + 12 caractères)
              </p>
            </div>

            <button
              onClick={handleLookup}
              disabled={isLoading || !codeAcces.trim()}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Recherche en cours...</span>
                </>
              ) : (
                <>
                  <Search className="h-4 w-4" />
                  <span>Consulter ma réservation</span>
                </>
              )}
            </button>
          </div>

          {/* Informations d'aide */}
          <div className="mt-6 bg-gray-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-gray-900 mb-2">
              Comment obtenir mon code d'accès ?
            </h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Le code vous a été fourni lors de la création de votre réservation</li>
              <li>• Il commence toujours par "ANON-" suivi de 12 caractères</li>
              <li>• Conservez-le précieusement, il est unique à votre réservation</li>
              <li>• En cas de perte, contactez la réception de l'hôtel</li>
            </ul>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              Détails de votre réservation
            </h2>
            <button
              onClick={resetSearch}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
            >
              Nouvelle recherche
            </button>
          </div>

          <AnonymousReservationDetailsComponent 
            reservation={reservation} 
            onUpdate={() => handleLookup()} // Recharger après modification
          />
        </div>
      )}

      {/* Section d'aide supplémentaire */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-900 mb-2">
          Besoin d'aide ?
        </h3>
        <p className="text-sm text-blue-700">
          Si vous rencontrez des difficultés pour accéder à votre réservation, 
          n'hésitez pas à contacter notre équipe de réception qui pourra vous assister.
        </p>
      </div>
    </div>
  );
}
