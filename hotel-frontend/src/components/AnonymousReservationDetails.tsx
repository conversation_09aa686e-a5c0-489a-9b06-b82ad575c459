import { useState } from 'react';
import { toast } from 'react-hot-toast';
import { Calendar, Clock, MapPin, Users, CreditCard, Edit3, Trash2, AlertCircle } from 'lucide-react';
import { anonymousReservationService, AnonymousReservationDetails as ReservationDetails } from '../services/anonymousReservation.service';

interface Props {
  reservation: ReservationDetails;
  onUpdate?: () => void;
}

export function AnonymousReservationDetails({ reservation, onUpdate }: Props) {
  const [isModifying, setIsModifying] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [newComments, setNewComments] = useState(reservation.commentaires || '');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const canModify = anonymousReservationService.constructor.canModifyReservation(reservation);
  const canCancel = anonymousReservationService.constructor.canCancelReservation(reservation);

  const handleModify = async () => {
    if (!reservation.code_acces_anonyme) {
      toast.error('Code d\'accès manquant');
      return;
    }

    setIsSubmitting(true);
    try {
      await anonymousReservationService.updateReservationAnonyme(
        reservation.code_acces_anonyme,
        { commentaires: newComments }
      );
      toast.success('Réservation modifiée avec succès');
      setIsModifying(false);
      onUpdate?.();
    } catch (error) {
      console.error('Erreur lors de la modification:', error);
      toast.error(error instanceof Error ? error.message : 'Erreur lors de la modification');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = async () => {
    if (!reservation.code_acces_anonyme) {
      toast.error('Code d\'accès manquant');
      return;
    }

    if (!confirm('Êtes-vous sûr de vouloir annuler cette réservation ? Cette action est irréversible.')) {
      return;
    }

    setIsCancelling(true);
    try {
      await anonymousReservationService.cancelReservationAnonyme(reservation.code_acces_anonyme);
      toast.success('Réservation annulée avec succès');
      onUpdate?.();
    } catch (error) {
      console.error('Erreur lors de l\'annulation:', error);
      toast.error(error instanceof Error ? error.message : 'Erreur lors de l\'annulation');
    } finally {
      setIsCancelling(false);
    }
  };

  const getStatusBadge = () => {
    const statusColor = anonymousReservationService.constructor.getStatusColor(reservation.statut);
    const statusLabel = anonymousReservationService.constructor.getStatusLabel(reservation.statut);
    
    return (
      <span className={`px-3 py-1 rounded-full text-sm font-medium ${statusColor}`}>
        {statusLabel}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* En-tête avec statut */}
      <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Réservation #{reservation.numero_reservation}
            </h3>
            <p className="text-sm text-gray-600">
              Pseudonyme: {reservation.pseudonyme || 'Client Anonyme'}
            </p>
          </div>
          {getStatusBadge()}
        </div>
      </div>

      {/* Détails de la réservation */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Informations de séjour */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900 flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-blue-500" />
              Informations de séjour
            </h4>
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Date d'arrivée:</span>
                <span className="font-medium">{formatDate(reservation.date_arrivee)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Date de départ:</span>
                <span className="font-medium">{formatDate(reservation.date_depart)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Heure d'arrivée:</span>
                <span className="font-medium">{reservation.heure_debut}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Heure de départ:</span>
                <span className="font-medium">{reservation.heure_fin}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Durée du séjour:</span>
                <span className="font-medium">{reservation.duree_sejour} jour(s)</span>
              </div>
            </div>
          </div>

          {/* Informations financières */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900 flex items-center">
              <CreditCard className="h-4 w-4 mr-2 text-green-500" />
              Informations financières
            </h4>
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Montant total:</span>
                <span className="font-medium text-lg">{reservation.montant_total}FCFA</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Type de réservation:</span>
                <span className="font-medium">{reservation.type_reservation}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Chambres réservées */}
        {reservation.chambres && reservation.chambres.length > 0 && (
          <div className="mb-6">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
              <MapPin className="h-4 w-4 mr-2 text-purple-500" />
              Chambres réservées
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {reservation.chambres.map((chambre, index) => (
                <div key={index} className="bg-gray-50 p-4 rounded-lg border">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium text-gray-900">Chambre {chambre.numero}</p>
                      <p className="text-sm text-gray-600">{chambre.type_chambre}</p>
                    </div>
                    <p className="text-sm font-medium text-gray-900">{chambre.prix_nuit}FCFA/nuit</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Commentaires */}
        <div className="mb-6">
          <h4 className="font-medium text-gray-900 mb-3">Commentaires</h4>
          {isModifying ? (
            <div className="space-y-3">
              <textarea
                value={newComments}
                onChange={(e) => setNewComments(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows={3}
                placeholder="Ajoutez vos commentaires..."
                maxLength={500}
              />
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500">
                  {newComments.length}/500 caractères
                </span>
                <div className="space-x-2">
                  <button
                    onClick={() => {
                      setIsModifying(false);
                      setNewComments(reservation.commentaires || '');
                    }}
                    className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
                  >
                    Annuler
                  </button>
                  <button
                    onClick={handleModify}
                    disabled={isSubmitting}
                    className="px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                  >
                    {isSubmitting ? 'Enregistrement...' : 'Enregistrer'}
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-gray-700">
                {reservation.commentaires || 'Aucun commentaire'}
              </p>
            </div>
          )}
        </div>

        {/* Actions */}
        {(canModify || canCancel) && (
          <div className="border-t border-gray-200 pt-6">
            <div className="flex flex-wrap gap-3">
              {canModify && !isModifying && (
                <button
                  onClick={() => setIsModifying(true)}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
                >
                  <Edit3 className="h-4 w-4 mr-2" />
                  Modifier les commentaires
                </button>
              )}

              {canCancel && (
                <button
                  onClick={handleCancel}
                  disabled={isCancelling}
                  className="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 disabled:opacity-50 transition-colors"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  {isCancelling ? 'Annulation...' : 'Annuler la réservation'}
                </button>
              )}
            </div>
          </div>
        )}

        {/* Avertissement pour les réservations expirées */}
        {reservation.statut === 'expiree' && (
          <div className="mt-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-red-800">Réservation expirée</h4>
                <p className="text-sm text-red-700 mt-1">
                  Cette réservation a expiré car elle n'a pas été confirmée dans les délais impartis.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
