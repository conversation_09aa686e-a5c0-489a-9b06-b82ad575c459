import React, { useState } from 'react';
import { Al<PERSON><PERSON>riangle, Loader2 } from 'lucide-react';
import {
  ServiceSelector,
  TableManager,
  MenuDisplay,
  OrderCart,
  POSHeader,
  SessionManager,
  PaymentModal,
  NotificationCenter
} from './pos';
import { usePOS, usePOSKeyboardShortcuts, usePOSPersistence, useNotifications } from '../hooks/usePOS';
import type { ServiceComplexe } from '../services';

interface POSProps {
  onClose: () => void;
}

// Composant d'erreur
const ErrorDisplay: React.FC<{ error: string; onRetry: () => void }> = ({ error, onRetry }) => (
  <div className="flex items-center justify-center h-screen bg-gray-50">
    <div className="text-center p-8 bg-white rounded-lg shadow-lg max-w-md">
      <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
      <h2 className="text-xl font-semibold text-gray-900 mb-2">Erreur POS</h2>
      <p className="text-gray-600 mb-4">{error}</p>
      <button
        onClick={onRetry}
        className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
      >
        Réessayer
      </button>
    </div>
  </div>
);

// Composant de chargement
const LoadingDisplay: React.FC = () => (
  <div className="flex items-center justify-center h-screen bg-gray-50">
    <div className="text-center">
      <Loader2 className="w-12 h-12 text-blue-500 animate-spin mx-auto mb-4" />
      <h2 className="text-xl font-semibold text-gray-900 mb-2">Chargement du POS</h2>
      <p className="text-gray-600">Initialisation en cours...</p>
    </div>
  </div>
);

// Composant de sélection de service initial
const ServiceSelection: React.FC<{
  services: ServiceComplexe[];
  onServiceSelect: (service: ServiceComplexe) => void;
  activeSessions: Record<number, any>;
}> = ({ services, onServiceSelect, activeSessions }) => (
  <div className="flex items-center justify-center h-screen bg-gray-50">
    <div className="w-full max-w-2xl p-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Point de Vente</h1>
        <p className="text-gray-600">Sélectionnez un service pour commencer</p>
      </div>

      <ServiceSelector
        services={services}
        selectedService={null}
        onServiceChange={onServiceSelect}
        activeSessions={activeSessions}
      />
    </div>
  </div>
);

export function POS({ onClose }: POSProps) {
  const {
    workspace,
    selectedTable,
    currentCommande,
    cartItems,
    loading,
    error,
    isFullscreen,
    availableServices,
    activeSessions,
    canValidateOrder,
    selectService,
    selectTable,
    addItemToOrder,
    createOrder,
    validateOrder,
    updateCartItem,
    removeCartItem,
    setError
  } = usePOS();

  // Hooks pour les fonctionnalités avancées
  usePOSKeyboardShortcuts();
  usePOSPersistence();

  const {
    notifications,
    unreadCount,
    addNotification,
    removeNotification,
    markNotificationAsRead,
    markAllAsRead,
    clearNotifications
  } = useNotifications();

  // État local pour les modals
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showSessionModal, setShowSessionModal] = useState(false);
  const [showNotificationCenter, setShowNotificationCenter] = useState(false);

  // Gestion des erreurs
  if (error) {
    return (
      <ErrorDisplay
        error={error}
        onRetry={() => setError(null)}
      />
    );
  }

  // Chargement initial
  if (loading && !workspace) {
    return <LoadingDisplay />;
  }

  // Sélection de service si aucun workspace
  if (!workspace) {
    return (
      <ServiceSelection
        services={availableServices}
        onServiceSelect={selectService}
        activeSessions={activeSessions}
      />
    );
  }

  // Handlers pour les actions
  const handleServiceChange = async (service: ServiceComplexe) => {
    await selectService(service);
  };

  const handleTableSelect = (table: any) => {
    selectTable(table);
  };

  const handleItemAdd = (item: any, quantity: number) => {
    addItemToOrder(item, quantity);
  };

  const handleItemUpdate = (itemId: number, quantity: number) => {
    updateCartItem(itemId, quantity);
  };

  const handleItemRemove = (itemId: number) => {
    removeCartItem(itemId);
  };

  const handleOrderValidate = async () => {
    try {
      if (cartItems.length > 0) {
        await createOrder();
      }
      if (canValidateOrder) {
        await validateOrder();
      }
    } catch (error) {
      console.error('Erreur lors de la validation:', error);
    }
  };

  const handlePayment = () => {
    setShowPaymentModal(true);
  };

  const handleSessionToggle = () => {
    setShowSessionModal(true);
  };

  const handleNotificationsClick = () => {
    setShowNotificationCenter(true);
  };

  const handlePaymentComplete = async (paymentData: any) => {
    try {
      // Traiter le paiement via le hook
      // await processPayment(paymentData);
      setShowPaymentModal(false);
      addNotification({
        type: 'success',
        title: 'Paiement effectué',
        message: `Paiement de ${paymentData.montant}FCFA traité avec succès`
      });
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: 'Erreur de paiement',
        message: error.message
      });
    }
  };

  const handleSessionOpen = async (fondsOuverture: number, _notes?: string) => {
    try {
      if (workspace) {
        // await openSession(workspace.service.service_id, fondsOuverture, notes);
        setShowSessionModal(false);
        addNotification({
          type: 'success',
          title: 'Session ouverte',
          message: `Session ouverte avec ${fondsOuverture}FCFA de fonds`
        });
      }
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: 'Erreur',
        message: error.message
      });
    }
  };

  const handleSessionClose = async (_fondsClôture: number, _notes?: string) => {
    try {
      if (workspace?.session) {
        // await closeSession(workspace.session.session_id, fondsClôture, notes);
        setShowSessionModal(false);
        addNotification({
          type: 'success',
          title: 'Session fermée',
          message: 'La session a été fermée avec succès'
        });
      }
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: 'Erreur',
        message: error.message
      });
    }
  };

  const handleToggleFullscreen = () => {
    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      document.documentElement.requestFullscreen();
    }
  };

  // Interface POS principale
  return (
    <div className={`pos-container h-screen flex flex-col bg-gray-50 ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      {/* Header */}
      <POSHeader
        selectedService={workspace.service}
        sessionActive={workspace.session}
        onServiceChange={handleServiceChange}
        onSessionToggle={handleSessionToggle}
        onClose={onClose}
        isFullscreen={isFullscreen}
        onToggleFullscreen={handleToggleFullscreen}
        notifications={unreadCount}
        onNotificationsClick={handleNotificationsClick}
      />

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Tables - 30% */}
        <div className="w-1/3 border-r border-gray-200 bg-white">
          <TableManager
            serviceId={workspace.service.service_id}
            tables={workspace.tables}
            onTableSelect={handleTableSelect}
            selectedTable={selectedTable || undefined}
            loading={loading}
          />
        </div>

        {/* Menu - 40% */}
        <div className="w-2/5 border-r border-gray-200 bg-white">
          <MenuDisplay
            serviceId={workspace.service.service_id}
            categories={workspace.menu}
            onItemAdd={handleItemAdd}
            loading={loading}
          />
        </div>

        {/* Commande - 30% */}
        <div className="w-1/3 bg-white">
          <OrderCart
            commande={currentCommande}
            onItemUpdate={handleItemUpdate}
            onItemRemove={handleItemRemove}
            onOrderValidate={handleOrderValidate}
            onPayment={handlePayment}
            loading={loading}
          />
        </div>
      </div>

      {/* Modals avancés */}
      {showPaymentModal && currentCommande && (
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          commande={currentCommande}
          onPaymentComplete={handlePaymentComplete}
        />
      )}

      {showSessionModal && (
        <SessionManager
          isOpen={showSessionModal}
          onClose={() => setShowSessionModal(false)}
          session={workspace.session}
          pointDeVente={null} // TODO: Récupérer le POS actuel
          onOpenSession={handleSessionOpen}
          onCloseSession={handleSessionClose}
        />
      )}

      {showNotificationCenter && (
        <NotificationCenter
          isOpen={showNotificationCenter}
          onClose={() => setShowNotificationCenter(false)}
          notifications={notifications}
          onMarkAsRead={markNotificationAsRead}
          onMarkAllAsRead={markAllAsRead}
          onRemove={removeNotification}
          onClearAll={clearNotifications}
        />
      )}
    </div>
  );
}