import React, { useState, useEffect } from 'react';
import { ChefHat, Package, AlertTriangle, CheckCircle, Edit, Trash2, Plus } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { produitIngredientService } from '../../services/produitIngredient.service';
import type { ProduitIngredient } from '../../services/produitIngredient.service';

interface PlatCompositionProps {
  produitId: number;
  produitNom: string;
  onCompositionChange?: () => void;
  readOnly?: boolean;
}

interface IngredientItem extends ProduitIngredient {
  disponible: boolean;
  stock_status: 'ok' | 'faible' | 'rupture';
}

export const PlatComposition: React.FC<PlatCompositionProps> = ({
  produitId,
  produitNom,
  onCompositionChange,
  readOnly = false
}) => {
  const [ingredients, setIngredients] = useState<IngredientItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingIngredient, setEditingIngredient] = useState<number | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);

  useEffect(() => {
    loadIngredients();
  }, [produitId]);

  const loadIngredients = async () => {
    try {
      setLoading(true);
      const data = await produitIngredientService.getIngredientsProduit(produitId);
      
      // Enrichir avec les informations de disponibilité
      const enrichedIngredients = data.map(ingredient => ({
        ...ingredient,
        disponible: (ingredient.ingredient_stock_actuel || 0) >= ingredient.quantite_necessaire,
        stock_status: getStockStatus(
          ingredient.ingredient_stock_actuel || 0,
          ingredient.quantite_necessaire,
          ingredient.ingredient_stock_minimal || 0
        )
      }));

      setIngredients(enrichedIngredients);
    } catch (error) {
      console.error('Erreur lors du chargement des ingrédients:', error);
      toast.error('Erreur lors du chargement de la composition');
    } finally {
      setLoading(false);
    }
  };

  const getStockStatus = (stockActuel: number, quantiteNecessaire: number, stockMinimal: number): 'ok' | 'faible' | 'rupture' => {
    if (stockActuel < quantiteNecessaire) return 'rupture';
    if (stockActuel <= stockMinimal * 2) return 'faible';
    return 'ok';
  };

  const handleUpdateIngredient = async (ingredientId: number, newData: Partial<ProduitIngredient>) => {
    try {
      await produitIngredientService.updateIngredientProduit(produitId, ingredientId, newData);
      await loadIngredients();
      setEditingIngredient(null);
      onCompositionChange?.();
      toast.success('Ingrédient mis à jour');
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      toast.error('Erreur lors de la mise à jour');
    }
  };

  const handleRemoveIngredient = async (ingredientId: number) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet ingrédient ?')) return;

    try {
      await produitIngredientService.supprimerIngredientProduit(produitId, ingredientId);
      await loadIngredients();
      onCompositionChange?.();
      toast.success('Ingrédient supprimé');
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      toast.error('Erreur lors de la suppression');
    }
  };

  const getStatusIcon = (status: 'ok' | 'faible' | 'rupture') => {
    switch (status) {
      case 'ok':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'faible':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'rupture':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusColor = (status: 'ok' | 'faible' | 'rupture') => {
    switch (status) {
      case 'ok':
        return 'bg-green-50 border-green-200';
      case 'faible':
        return 'bg-yellow-50 border-yellow-200';
      case 'rupture':
        return 'bg-red-50 border-red-200';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <ChefHat className="h-5 w-5 text-gray-400 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">
              Composition - {produitNom}
            </h3>
          </div>
          {!readOnly && (
            <button
              onClick={() => setShowAddForm(true)}
              className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
            >
              <Plus className="h-4 w-4 mr-1" />
              Ajouter
            </button>
          )}
        </div>
      </div>

      {/* Liste des ingrédients */}
      <div className="p-6">
        {ingredients.length === 0 ? (
          <div className="text-center py-8">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Aucun ingrédient défini pour ce plat</p>
            {!readOnly && (
              <button
                onClick={() => setShowAddForm(true)}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Ajouter le premier ingrédient
              </button>
            )}
          </div>
        ) : (
          <div className="space-y-3">
            {ingredients.map((ingredient) => (
              <div
                key={ingredient.ingredient_id}
                className={`p-4 rounded-lg border ${getStatusColor(ingredient.stock_status)}`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center flex-1">
                    {getStatusIcon(ingredient.stock_status)}
                    <div className="ml-3 flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-gray-900">
                          {ingredient.ingredient_nom}
                        </h4>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span>
                            {ingredient.quantite_necessaire} {ingredient.unite_mesure}
                          </span>
                          <span>
                            Stock: {ingredient.ingredient_stock_actuel || 0} {ingredient.unite_mesure}
                          </span>
                          <span className="font-medium">
                            {(ingredient.cout_unitaire * ingredient.quantite_necessaire).toFixed(0)} FCFA
                          </span>
                        </div>
                      </div>
                      
                      {ingredient.notes && (
                        <p className="text-xs text-gray-500 mt-1">{ingredient.notes}</p>
                      )}
                      
                      {!ingredient.disponible && (
                        <p className="text-xs text-red-600 mt-1 font-medium">
                          ⚠️ Stock insuffisant pour la préparation
                        </p>
                      )}
                    </div>
                  </div>

                  {!readOnly && (
                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => setEditingIngredient(ingredient.ingredient_id)}
                        className="p-1 text-gray-400 hover:text-blue-600"
                        title="Modifier"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleRemoveIngredient(ingredient.ingredient_id)}
                        className="p-1 text-gray-400 hover:text-red-600"
                        title="Supprimer"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Résumé des coûts */}
        {ingredients.length > 0 && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700">
                Coût total des ingrédients:
              </span>
              <span className="text-lg font-bold text-gray-900">
                {ingredients.reduce((total, ing) => total + (ing.cout_unitaire * ing.quantite_necessaire), 0).toFixed(0)} FCFA
              </span>
            </div>
            
            {/* Statut global */}
            <div className="mt-2 flex items-center">
              {ingredients.every(ing => ing.disponible) ? (
                <div className="flex items-center text-green-600">
                  <CheckCircle className="h-4 w-4 mr-1" />
                  <span className="text-sm">Tous les ingrédients sont disponibles</span>
                </div>
              ) : (
                <div className="flex items-center text-red-600">
                  <AlertTriangle className="h-4 w-4 mr-1" />
                  <span className="text-sm">
                    {ingredients.filter(ing => !ing.disponible).length} ingrédient(s) indisponible(s)
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
