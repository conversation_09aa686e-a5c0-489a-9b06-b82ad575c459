import React, { useState, useEffect } from 'react';
import { 
  Check<PERSON>ircle, 
  AlertTriangle, 
  XCircle, 
  TrendingUp, 
  DollarSign, 
  Package,
  RefreshCw,
  Filter
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { menuIntegreService } from '../../services/menuIntegre.service';
import type { ProduitMenu, MenuStats, MenuAlert } from '../../services/menuIntegre.service';

interface MenuAvailabilityDashboardProps {
  serviceId: number;
  serviceName: string;
}

export const MenuAvailabilityDashboard: React.FC<MenuAvailabilityDashboardProps> = ({
  serviceId,
  serviceName
}) => {
  const [produits, setProduits] = useState<ProduitMenu[]>([]);
  const [stats, setStats] = useState<MenuStats | null>(null);
  const [alertes, setAlertes] = useState<MenuAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'disponible' | 'rupture' | 'stock_faible'>('all');
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    loadMenuData();
  }, [serviceId]);

  const loadMenuData = async () => {
    try {
      setLoading(true);
      
      // Charger les données en parallèle
      const [menuData, statsData, alertesData] = await Promise.all([
        menuIntegreService.verifierDisponibiliteMenu(serviceId),
        menuIntegreService.getMenuStats(serviceId),
        menuIntegreService.getMenuAlerts(serviceId)
      ]);

      const allProduits = [...menuData.produits_disponibles, ...menuData.produits_indisponibles];
      setProduits(allProduits);
      setStats(statsData);
      setAlertes(alertesData);
      setLastUpdate(new Date());
      
    } catch (error) {
      console.error('Erreur lors du chargement des données menu:', error);
      toast.error('Erreur lors du chargement du menu');
    } finally {
      setLoading(false);
    }
  };

  const getFilteredProduits = () => {
    if (filter === 'all') return produits;
    return produits.filter(produit => produit.stock_status === filter || 
      (filter === 'disponible' && produit.disponible));
  };

  const getStatusIcon = (status: string, disponible: boolean) => {
    if (!disponible) return <XCircle className="h-5 w-5 text-red-500" />;
    
    switch (status) {
      case 'disponible':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'stock_faible':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'rupture':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Package className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string, disponible: boolean) => {
    if (!disponible) {
      return <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">Rupture</span>;
    }
    
    switch (status) {
      case 'disponible':
        return <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Disponible</span>;
      case 'stock_faible':
        return <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">Stock faible</span>;
      case 'rupture':
        return <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">Rupture</span>;
      default:
        return <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">Inconnu</span>;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="bg-white p-6 rounded-lg border border-gray-200">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="space-y-3">
              {[1, 2, 3, 4, 5].map(i => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  const filteredProduits = getFilteredProduits();

  return (
    <div className="space-y-6">
      {/* Header avec stats */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Disponibilité Menu - {serviceName}
          </h2>
          <p className="text-sm text-gray-600">
            Dernière mise à jour: {lastUpdate.toLocaleTimeString()}
          </p>
        </div>
        <button
          onClick={loadMenuData}
          disabled={loading}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Actualiser
        </button>
      </div>

      {/* Statistiques */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <Package className="h-8 w-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Produits</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total_produits}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Disponibles</p>
                <p className="text-2xl font-bold text-green-600">{stats.produits_disponibles}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <XCircle className="h-8 w-8 text-red-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">En Rupture</p>
                <p className="text-2xl font-bold text-red-600">{stats.produits_rupture}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-purple-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Marge Moyenne</p>
                <p className="text-2xl font-bold text-purple-600">{stats.marge_moyenne.toFixed(1)}%</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Alertes */}
      {alertes.length > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center mb-3">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
            <h3 className="text-sm font-medium text-yellow-800">
              Alertes ({alertes.length})
            </h3>
          </div>
          <div className="space-y-2">
            {alertes.slice(0, 3).map((alerte, index) => (
              <div key={index} className="text-sm text-yellow-700">
                <strong>{alerte.produit_nom}:</strong> {alerte.message}
              </div>
            ))}
            {alertes.length > 3 && (
              <p className="text-sm text-yellow-600">
                ... et {alertes.length - 3} autre(s) alerte(s)
              </p>
            )}
          </div>
        </div>
      )}

      {/* Filtres */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center space-x-4">
          <Filter className="h-5 w-5 text-gray-400" />
          <span className="text-sm font-medium text-gray-700">Filtrer par:</span>
          <div className="flex space-x-2">
            {[
              { key: 'all', label: 'Tous', count: produits.length },
              { key: 'disponible', label: 'Disponibles', count: produits.filter(p => p.disponible).length },
              { key: 'stock_faible', label: 'Stock faible', count: produits.filter(p => p.stock_status === 'stock_faible').length },
              { key: 'rupture', label: 'Rupture', count: produits.filter(p => !p.disponible).length }
            ].map(({ key, label, count }) => (
              <button
                key={key}
                onClick={() => setFilter(key as any)}
                className={`px-3 py-1 text-sm rounded-full border ${
                  filter === key
                    ? 'bg-blue-100 border-blue-300 text-blue-700'
                    : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {label} ({count})
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Liste des produits */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Produits du Menu ({filteredProduits.length})
          </h3>
        </div>
        
        <div className="divide-y divide-gray-200">
          {filteredProduits.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              Aucun produit trouvé pour ce filtre
            </div>
          ) : (
            filteredProduits.map((produit) => (
              <div key={produit.produit_id} className="p-6 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center flex-1">
                    {getStatusIcon(produit.stock_status, produit.disponible)}
                    <div className="ml-4 flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="text-lg font-medium text-gray-900">
                          {produit.nom}
                        </h4>
                        {getStatusBadge(produit.stock_status, produit.disponible)}
                      </div>
                      
                      {produit.description && (
                        <p className="text-sm text-gray-600 mt-1">{produit.description}</p>
                      )}
                      
                      <div className="flex items-center space-x-6 mt-2 text-sm text-gray-600">
                        <span className="flex items-center">
                          <DollarSign className="h-4 w-4 mr-1" />
                          Prix: {produit.prix_vente_defaut.toFixed(0)} FCFA
                        </span>
                        <span>
                          Coût: {produit.cout_ingredients.toFixed(0)} FCFA
                        </span>
                        <span className={`font-medium ${
                          produit.marge_pourcentage > 30 ? 'text-green-600' : 
                          produit.marge_pourcentage > 15 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          Marge: {produit.marge_pourcentage.toFixed(1)}%
                        </span>
                      </div>
                      
                      {produit.ingredients_manquants.length > 0 && (
                        <div className="mt-2">
                          <p className="text-sm text-red-600 font-medium">
                            Ingrédients manquants: {produit.ingredients_manquants.join(', ')}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};
