import React, { useState, useEffect } from 'react';
import { chambreService, Chambre, ChambreResponse } from '../services/chambre.service';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { RoomAvailabilityCalendar } from './RoomAvailabilityCalendar';

interface RoomListProps {
  onRoomSelect: (room: Chambre) => void;
}

export function RoomList({ onRoomSelect }: RoomListProps) {
  const [rooms, setRooms] = useState<Chambre[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedType, setSelectedType] = useState<string>('all');
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);
  const [selectedRoom, setSelectedRoom] = useState<Chambre | null>(null);

  const roomTypes = [
    { id: 'all', label: 'Tous les types' },
    { id: 'standard', label: 'Standard' },
    { id: 'VIP', label: 'VIP' },
    { id: 'Suite', label: 'Suite' }
  ];

  useEffect(() => {
    fetchRooms();
  }, [selectedType]);

  const fetchRooms = async () => {
    try {
      setLoading(true);
      const params = selectedType !== 'all' ? { type_chambre: selectedType } : {};
      const response = await chambreService.getChambres(params);
      if (response.success && response.data) {
        setRooms(response.data.chambres || []);
      } else {
        setRooms([]);
        toast.error(response.message || 'Erreur lors du chargement des chambres');
      }
    } catch (error) {
      console.error('Erreur lors du chargement des chambres:', error);
      toast.error('Erreur lors du chargement des chambres');
      setRooms([]);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (statut: string) => {
    switch (statut) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800';
      case 'occupee':
        return 'bg-red-100 text-red-800';
      case 'verrouillee':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (statut: string) => {
    switch (statut) {
      case 'active':
        return 'Disponible';
      case 'maintenance':
        return 'En maintenance';
      case 'occupee':
        return 'Occupée';
      case 'verrouillee':
        return 'Verrouillée';
      default:
        return statut;
    }
  };

  return (
    <div className="space-y-6">
      {/* Filtres */}
      <div className="bg-white p-4 rounded-lg shadow-sm">
        <div className="flex flex-wrap gap-4">
          {roomTypes.map((type) => (
            <button
              key={type.id}
              onClick={() => setSelectedType(type.id)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                selectedType === type.id
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {type.label}
            </button>
          ))}
        </div>
      </div>

      {/* Liste des chambres */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {rooms && rooms.length > 0 ? (
            rooms.map((room) => (
              <motion.div
                key={room.chambre_id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow"
              >
                <div className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900">
                        Chambre {room.numero}
                      </h3>
                      <p className="text-sm text-gray-500">{room.type_chambre}</p>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(room.statut)}`}>
                      {getStatusLabel(room.statut)}
                    </span>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center text-sm text-gray-600">
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                      Capacité: {room.capacite} personnes
                    </div>

                    <div className="flex items-center text-sm text-gray-600">
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Prix: {room.prix_base}F / heure
                    </div>

                    {room.etage && (
                      <div className="flex items-center text-sm text-gray-600">
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                        Étage: {room.etage}
                      </div>
                    )}
                  </div>

                  {room.description && (
                    <p className="mt-4 text-sm text-gray-600 line-clamp-2">{room.description}</p>
                  )}

                  <div className="mt-6">
                    <button
                      onClick={() => setSelectedRoom(room)}
                      className="w-full px-4 py-2 rounded-md text-sm font-medium bg-blue-500 text-white hover:bg-blue-600 transition-colors"
                    >
                      Voir disponibilité
                    </button>
                  </div>
                </div>
              </motion.div>
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <p className="text-gray-500">Aucune chambre trouvée pour les critères sélectionnés.</p>
            </div>
          )}
        </div>
      )}

      <AnimatePresence>
        {selectedRoom && (
          <RoomAvailabilityCalendar
            room={selectedRoom}
            onClose={() => setSelectedRoom(null)}
          />
        )}
      </AnimatePresence>
    </div>
  );
} 