import React from 'react';
import { CheckCircle, AlertCircle, Clock, Upload, Database, FileCheck } from 'lucide-react';

interface ImportProgressProps {
  step: 'uploading' | 'validating' | 'processing' | 'complete' | 'error';
  progress?: number;
  message?: string;
  details?: {
    totalItems?: number;
    processedItems?: number;
    errors?: number;
    warnings?: number;
  };
}

export const ImportProgress: React.FC<ImportProgressProps> = ({
  step,
  progress = 0,
  message,
  details
}) => {
  const getStepInfo = (stepName: string) => {
    const steps = {
      uploading: {
        icon: Upload,
        label: 'Upload du fichier',
        color: 'blue',
        description: 'Envoi du fichier vers le serveur...'
      },
      validating: {
        icon: FileCheck,
        label: 'Validation',
        color: 'yellow',
        description: 'Vérification du format et des données...'
      },
      processing: {
        icon: Database,
        label: 'Traitement',
        color: 'blue',
        description: 'Import des données en cours...'
      },
      complete: {
        icon: CheckCircle,
        label: 'Termin<PERSON>',
        color: 'green',
        description: 'Import terminé avec succès'
      },
      error: {
        icon: AlertCircle,
        label: 'Erreur',
        color: 'red',
        description: 'Une erreur est survenue'
      }
    };
    return steps[stepName as keyof typeof steps];
  };

  const currentStep = getStepInfo(step);
  const IconComponent = currentStep.icon;

  const getProgressBarColor = () => {
    switch (step) {
      case 'complete':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-blue-500';
    }
  };

  const getIconColor = () => {
    switch (currentStep.color) {
      case 'green':
        return 'text-green-500';
      case 'red':
        return 'text-red-500';
      case 'yellow':
        return 'text-yellow-500';
      default:
        return 'text-blue-500';
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      {/* Header avec icône et statut */}
      <div className="flex items-center space-x-3 mb-4">
        <div className={`p-2 rounded-full ${
          step === 'uploading' || step === 'validating' || step === 'processing' 
            ? 'animate-pulse' 
            : ''
        }`}>
          <IconComponent className={`h-6 w-6 ${getIconColor()}`} />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">{currentStep.label}</h3>
          <p className="text-sm text-gray-600">
            {message || currentStep.description}
          </p>
        </div>
      </div>

      {/* Barre de progression */}
      {(step === 'uploading' || step === 'processing') && (
        <div className="mb-4">
          <div className="flex justify-between text-sm text-gray-600 mb-1">
            <span>Progression</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor()}`}
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Détails de l'import */}
      {details && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4 p-4 bg-gray-50 rounded-lg">
          {details.totalItems !== undefined && (
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{details.totalItems}</div>
              <div className="text-xs text-gray-600">Total</div>
            </div>
          )}
          {details.processedItems !== undefined && (
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{details.processedItems}</div>
              <div className="text-xs text-gray-600">Traités</div>
            </div>
          )}
          {details.errors !== undefined && (
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{details.errors}</div>
              <div className="text-xs text-gray-600">Erreurs</div>
            </div>
          )}
          {details.warnings !== undefined && (
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{details.warnings}</div>
              <div className="text-xs text-gray-600">Avertissements</div>
            </div>
          )}
        </div>
      )}

      {/* Messages d'état */}
      {step === 'complete' && (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
            <p className="text-sm text-green-700">
              Import terminé avec succès ! Les données sont maintenant disponibles.
            </p>
          </div>
        </div>
      )}

      {step === 'error' && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <p className="text-sm text-red-700">
              {message || 'Une erreur est survenue pendant l\'import. Veuillez réessayer.'}
            </p>
          </div>
        </div>
      )}

      {/* Indicateur de temps pour les étapes en cours */}
      {(step === 'uploading' || step === 'validating' || step === 'processing') && (
        <div className="mt-4 flex items-center justify-center text-sm text-gray-500">
          <Clock className="h-4 w-4 mr-1" />
          <span>Opération en cours, veuillez patienter...</span>
        </div>
      )}
    </div>
  );
};
