import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { FileDropzone } from './FileDropzone';
import { ImportProgress } from './ImportProgress';
import { ImportReport } from './ImportReport';
import { menuImportService } from '../../services/menuImport.service';
import { templateGeneratorService } from '../../services/templateGenerator.service';
import type { MenuImportResponse, MenuValidationResponse } from '../../services/menuImport.service';

interface MenuUploaderProps {
  serviceId: number;
  serviceType: 'Restaurant' | 'Bar';
  onUploadComplete?: (result: MenuImportResponse) => void;
  onError?: (error: string) => void;
}

type UploadStep = 'select' | 'validate' | 'upload' | 'complete' | 'error';

export const MenuUploader: React.FC<MenuUploaderProps> = ({
  serviceId,
  serviceType,
  onUploadComplete,
  onError
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [currentStep, setCurrentStep] = useState<UploadStep>('select');
  const [progress, setProgress] = useState(0);
  const [validationResult, setValidationResult] = useState<MenuValidationResponse | null>(null);
  const [importResult, setImportResult] = useState<MenuImportResponse | null>(null);
  const [error, setError] = useState<string>('');
  const [isValidating, setIsValidating] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  const templateType = serviceType === 'Restaurant' ? 'restaurant' : 'bar';

  const handleFileSelect = async (file: File) => {
    setSelectedFile(file);
    setError('');
    
    // Validation automatique du fichier
    await validateFile(file);
  };

  const handleFileRemove = () => {
    setSelectedFile(null);
    setValidationResult(null);
    setCurrentStep('select');
    setError('');
  };

  const validateFile = async (file: File) => {
    setIsValidating(true);
    setCurrentStep('validate');
    setProgress(0);

    try {
      // Validation du format
      const formatValidation = templateGeneratorService.validateFileFormat(file);
      if (!formatValidation.isValid) {
        throw new Error(formatValidation.error);
      }

      setProgress(30);

      // Validation du contenu
      const validation = serviceType === 'Restaurant' 
        ? await menuImportService.validateRestaurantMenu(file)
        : await menuImportService.validateBarMenu(file);

      setProgress(100);
      setValidationResult(validation);

      if (validation.data.is_valid) {
        setCurrentStep('select'); // Retour à la sélection avec validation OK
        toast.success(`Fichier validé: ${validation.data.valid_rows}/${validation.data.total_rows} lignes valides`);
      } else {
        setCurrentStep('error');
        setError(`Validation échouée: ${validation.data.errors.length} erreurs détectées`);
      }
    } catch (err: any) {
      setCurrentStep('error');
      setError(err.message || 'Erreur lors de la validation du fichier');
      onError?.(err.message);
    } finally {
      setIsValidating(false);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || !validationResult?.data.is_valid) {
      toast.error('Veuillez sélectionner un fichier valide');
      return;
    }

    setIsUploading(true);
    setCurrentStep('upload');
    setProgress(0);

    try {
      // Simulation de progression
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const result = serviceType === 'Restaurant'
        ? await menuImportService.importRestaurantMenu(serviceId, selectedFile)
        : await menuImportService.importBarMenu(serviceId, selectedFile);

      clearInterval(progressInterval);
      setProgress(100);

      setImportResult(result);
      setCurrentStep('complete');
      
      toast.success(`${serviceType === 'Restaurant' ? 'Menu' : 'Carte'} importé avec succès!`);
      onUploadComplete?.(result);

    } catch (err: any) {
      setCurrentStep('error');
      setError(err.message || 'Erreur lors de l\'import');
      onError?.(err.message);
      toast.error('Erreur lors de l\'import');
    } finally {
      setIsUploading(false);
    }
  };

  const handleRetry = () => {
    setCurrentStep('select');
    setError('');
    setImportResult(null);
    setValidationResult(null);
    setProgress(0);
  };

  const handleReset = () => {
    setSelectedFile(null);
    setCurrentStep('select');
    setError('');
    setImportResult(null);
    setValidationResult(null);
    setProgress(0);
  };

  const renderContent = () => {
    switch (currentStep) {
      case 'select':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Importer {serviceType === 'Restaurant' ? 'le Menu Restaurant' : 'la Carte Bar'}
              </h3>
              <p className="text-sm text-gray-600">
                Uploadez votre fichier Excel pour importer automatiquement votre {serviceType === 'Restaurant' ? 'menu' : 'carte'}
              </p>
            </div>

            <FileDropzone
              onFileSelect={handleFileSelect}
              onFileRemove={handleFileRemove}
              selectedFile={selectedFile}
              templateType={templateType}
              error={error}
              disabled={isValidating}
            />

            {validationResult && validationResult.data.is_valid && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-medium text-green-800 mb-2">Aperçu de l'import</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-green-700">Lignes valides:</span>
                    <span className="font-medium ml-2">{validationResult.data.valid_rows}</span>
                  </div>
                  <div>
                    <span className="text-green-700">Total lignes:</span>
                    <span className="font-medium ml-2">{validationResult.data.total_rows}</span>
                  </div>
                </div>
                {validationResult.data.suggested_categories.length > 0 && (
                  <div className="mt-2">
                    <span className="text-green-700 text-sm">Catégories détectées:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {validationResult.data.suggested_categories.map((cat, index) => (
                        <span key={index} className="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                          {cat}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {selectedFile && validationResult?.data.is_valid && (
              <div className="flex justify-center">
                <button
                  onClick={handleUpload}
                  disabled={isUploading}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
                >
                  {isUploading ? 'Import en cours...' : `Importer ${serviceType === 'Restaurant' ? 'le Menu' : 'la Carte'}`}
                </button>
              </div>
            )}
          </div>
        );

      case 'validate':
        return (
          <ImportProgress
            step="validating"
            progress={progress}
            message="Validation du fichier en cours..."
          />
        );

      case 'upload':
        return (
          <ImportProgress
            step="processing"
            progress={progress}
            message={`Import ${serviceType === 'Restaurant' ? 'du menu' : 'de la carte'} en cours...`}
            details={validationResult ? {
              totalItems: validationResult.data.total_rows,
              processedItems: Math.round((progress / 100) * validationResult.data.total_rows)
            } : undefined}
          />
        );

      case 'complete':
        return importResult ? (
          <div className="space-y-6">
            <ImportReport
              success={true}
              totalItems={importResult.data.total_items}
              importedItems={importResult.data.imported_items}
              errors={importResult.data.errors}
              warnings={importResult.data.warnings}
              categoriesCreated={importResult.data.categories_created}
              processingTime={importResult.data.processing_time}
              onClose={handleReset}
            />
          </div>
        ) : null;

      case 'error':
        return (
          <div className="space-y-6">
            <ImportProgress
              step="error"
              message={error}
            />
            {validationResult && !validationResult.data.is_valid && (
              <ImportReport
                success={false}
                totalItems={validationResult.data.total_rows}
                importedItems={validationResult.data.valid_rows}
                errors={validationResult.data.errors}
                warnings={validationResult.data.warnings}
                onRetry={handleRetry}
                onClose={handleReset}
              />
            )}
            {!validationResult && (
              <div className="flex justify-center">
                <button
                  onClick={handleRetry}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Réessayer
                </button>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      {renderContent()}
    </div>
  );
};
