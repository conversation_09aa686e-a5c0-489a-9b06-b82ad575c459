import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, File, X, AlertCircle, CheckCircle } from 'lucide-react';
import { templateGeneratorService } from '../../services/templateGenerator.service';

interface FileDropzoneProps {
  onFileSelect: (file: File) => void;
  onFileRemove: () => void;
  selectedFile?: File;
  accept?: string;
  maxSize?: number;
  templateType?: 'restaurant' | 'bar' | 'cuisine' | 'boissons';
  disabled?: boolean;
  error?: string;
}

export const FileDropzone: React.FC<FileDropzoneProps> = ({
  onFileSelect,
  onFileRemove,
  selectedFile,
  accept = '.xlsx,.xls',
  maxSize = 10 * 1024 * 1024, // 10MB
  templateType,
  disabled = false,
  error
}) => {
  const [isDownloading, setIsDownloading] = useState(false);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      
      // Validation du fichier
      const validation = templateGeneratorService.validateFileFormat(file);
      if (!validation.isValid) {
        // L'erreur sera gérée par le composant parent
        return;
      }
      
      onFileSelect(file);
    }
  }, [onFileSelect]);

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls']
    },
    maxSize,
    multiple: false,
    disabled
  });

  const handleDownloadTemplate = async () => {
    if (!templateType) return;
    
    setIsDownloading(true);
    try {
      await templateGeneratorService.downloadTemplateWithFilename(templateType);
    } catch (error) {
      console.error('Erreur lors du téléchargement du template:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getDropzoneClasses = () => {
    let classes = 'border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200 ';
    
    if (disabled) {
      classes += 'border-gray-200 bg-gray-50 cursor-not-allowed ';
    } else if (isDragReject || error) {
      classes += 'border-red-300 bg-red-50 ';
    } else if (isDragActive) {
      classes += 'border-blue-400 bg-blue-50 ';
    } else if (selectedFile) {
      classes += 'border-green-300 bg-green-50 ';
    } else {
      classes += 'border-gray-300 bg-gray-50 hover:border-gray-400 cursor-pointer ';
    }
    
    return classes;
  };

  return (
    <div className="space-y-4">
      {/* Zone de drop */}
      <div {...getRootProps()} className={getDropzoneClasses()}>
        <input {...getInputProps()} />
        
        {selectedFile ? (
          // Fichier sélectionné
          <div className="space-y-4">
            <div className="flex items-center justify-center">
              <CheckCircle className="h-12 w-12 text-green-500" />
            </div>
            <div>
              <p className="text-lg font-medium text-green-700">Fichier sélectionné</p>
              <div className="mt-2 p-3 bg-white rounded border border-green-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <File className="h-5 w-5 text-green-600" />
                    <div>
                      <p className="font-medium text-gray-900">{selectedFile.name}</p>
                      <p className="text-sm text-gray-500">{formatFileSize(selectedFile.size)}</p>
                    </div>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onFileRemove();
                    }}
                    className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded"
                    disabled={disabled}
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          // Zone de drop vide
          <div className="space-y-4">
            <div className="flex items-center justify-center">
              {isDragReject || error ? (
                <AlertCircle className="h-12 w-12 text-red-500" />
              ) : (
                <Upload className="h-12 w-12 text-gray-400" />
              )}
            </div>
            <div>
              {isDragActive ? (
                <p className="text-lg font-medium text-blue-600">
                  Déposez le fichier ici...
                </p>
              ) : (
                <div>
                  <p className="text-lg font-medium text-gray-700">
                    Glissez-déposez votre fichier Excel ici
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    ou cliquez pour sélectionner un fichier
                  </p>
                </div>
              )}
              <p className="text-xs text-gray-400 mt-2">
                Formats acceptés: .xlsx, .xls (max {formatFileSize(maxSize)})
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Erreur */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* Bouton de téléchargement du template */}
      {templateType && (
        <div className="text-center">
          <button
            onClick={handleDownloadTemplate}
            disabled={isDownloading}
            className="inline-flex items-center px-4 py-2 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isDownloading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                Téléchargement...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Télécharger le template {templateType}
              </>
            )}
          </button>
          <p className="text-xs text-gray-500 mt-1">
            Téléchargez le template pour voir le format attendu
          </p>
        </div>
      )}
    </div>
  );
};
