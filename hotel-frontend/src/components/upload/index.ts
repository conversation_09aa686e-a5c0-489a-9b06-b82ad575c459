// Export de tous les composants d'upload pour l'import de menus et ingrédients

export { FileDropzone } from './FileDropzone';
export { ImportProgress } from './ImportProgress';
export { ImportReport } from './ImportReport';
export { MenuUploader } from './MenuUploader';
export { IngredientUploader } from './IngredientUploader';

// Types réexportés pour faciliter l'utilisation
export type { 
  MenuImportResponse, 
  MenuValidationResponse, 
  ImportStatus 
} from '../../services/menuImport.service';

export type { 
  IngredientImportResponse, 
  IngredientValidationResponse, 
  IngredientImportStatus 
} from '../../services/ingredientImport.service';

export type { 
  TemplateInfo, 
  TemplateColumn 
} from '../../services/templateGenerator.service';
