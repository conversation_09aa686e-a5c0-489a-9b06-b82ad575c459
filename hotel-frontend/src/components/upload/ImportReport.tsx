import React, { useState } from 'react';
import { CheckCircle, AlertCircle, AlertTriangle, ChevronDown, ChevronUp, FileText, Download } from 'lucide-react';

interface ImportError {
  ligne: number;
  colonne?: string;
  erreur: string;
  valeur_problematique?: any;
  suggestion?: string;
}

interface ImportWarning {
  ligne: number;
  colonne?: string;
  message: string;
  valeur: any;
}

interface ImportReportProps {
  success: boolean;
  totalItems: number;
  importedItems: number;
  errors: ImportError[];
  warnings: ImportWarning[];
  categoriesCreated?: number;
  processingTime?: number;
  onClose?: () => void;
  onRetry?: () => void;
}

export const ImportReport: React.FC<ImportReportProps> = ({
  success,
  totalItems,
  importedItems,
  errors = [],
  warnings = [],
  categoriesCreated = 0,
  processingTime = 0,
  onClose,
  onRetry
}) => {
  const [showErrors, setShowErrors] = useState(false);
  const [showWarnings, setShowWarnings] = useState(false);

  const successRate = totalItems > 0 ? Math.round((importedItems / totalItems) * 100) : 0;

  const formatTime = (seconds: number): string => {
    if (seconds < 1) return '< 1 seconde';
    if (seconds < 60) return `${Math.round(seconds)} secondes`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      {/* Header */}
      <div className="flex items-center space-x-3 mb-6">
        {success ? (
          <CheckCircle className="h-8 w-8 text-green-500" />
        ) : (
          <AlertCircle className="h-8 w-8 text-red-500" />
        )}
        <div>
          <h3 className="text-xl font-semibold text-gray-900">
            {success ? 'Import terminé avec succès' : 'Import terminé avec des erreurs'}
          </h3>
          <p className="text-sm text-gray-600">
            Rapport détaillé de l'opération d'import
          </p>
        </div>
      </div>

      {/* Statistiques principales */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">{totalItems}</div>
          <div className="text-sm text-blue-800">Lignes traitées</div>
        </div>
        <div className="bg-green-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-green-600">{importedItems}</div>
          <div className="text-sm text-green-800">Importées</div>
        </div>
        <div className="bg-red-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-red-600">{errors.length}</div>
          <div className="text-sm text-red-800">Erreurs</div>
        </div>
        <div className="bg-yellow-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-yellow-600">{warnings.length}</div>
          <div className="text-sm text-yellow-800">Avertissements</div>
        </div>
      </div>

      {/* Taux de succès */}
      <div className="mb-6">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>Taux de succès</span>
          <span>{successRate}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div
            className={`h-3 rounded-full transition-all duration-500 ${
              successRate >= 90 ? 'bg-green-500' : 
              successRate >= 70 ? 'bg-yellow-500' : 'bg-red-500'
            }`}
            style={{ width: `${successRate}%` }}
          ></div>
        </div>
      </div>

      {/* Informations supplémentaires */}
      {(categoriesCreated > 0 || processingTime > 0) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
          {categoriesCreated > 0 && (
            <div className="flex items-center">
              <FileText className="h-5 w-5 text-blue-500 mr-2" />
              <span className="text-sm text-gray-700">
                {categoriesCreated} catégorie(s) créée(s)
              </span>
            </div>
          )}
          {processingTime > 0 && (
            <div className="flex items-center">
              <Download className="h-5 w-5 text-green-500 mr-2" />
              <span className="text-sm text-gray-700">
                Traité en {formatTime(processingTime)}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Section des erreurs */}
      {errors.length > 0 && (
        <div className="mb-4">
          <button
            onClick={() => setShowErrors(!showErrors)}
            className="w-full flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors"
          >
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
              <span className="font-medium text-red-800">
                {errors.length} erreur(s) détectée(s)
              </span>
            </div>
            {showErrors ? (
              <ChevronUp className="h-5 w-5 text-red-500" />
            ) : (
              <ChevronDown className="h-5 w-5 text-red-500" />
            )}
          </button>
          
          {showErrors && (
            <div className="mt-2 space-y-2">
              {errors.map((error, index) => (
                <div key={index} className="bg-white border border-red-200 rounded p-3">
                  <div className="flex justify-between items-start mb-1">
                    <span className="text-sm font-medium text-red-800">
                      Ligne {error.ligne}
                      {error.colonne && ` - Colonne ${error.colonne}`}
                    </span>
                  </div>
                  <p className="text-sm text-red-700 mb-1">{error.erreur}</p>
                  {error.valeur_problematique && (
                    <p className="text-xs text-gray-600">
                      Valeur: {JSON.stringify(error.valeur_problematique)}
                    </p>
                  )}
                  {error.suggestion && (
                    <p className="text-xs text-blue-600 mt-1">
                      💡 {error.suggestion}
                    </p>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Section des avertissements */}
      {warnings.length > 0 && (
        <div className="mb-6">
          <button
            onClick={() => setShowWarnings(!showWarnings)}
            className="w-full flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg hover:bg-yellow-100 transition-colors"
          >
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2" />
              <span className="font-medium text-yellow-800">
                {warnings.length} avertissement(s)
              </span>
            </div>
            {showWarnings ? (
              <ChevronUp className="h-5 w-5 text-yellow-500" />
            ) : (
              <ChevronDown className="h-5 w-5 text-yellow-500" />
            )}
          </button>
          
          {showWarnings && (
            <div className="mt-2 space-y-2">
              {warnings.map((warning, index) => (
                <div key={index} className="bg-white border border-yellow-200 rounded p-3">
                  <div className="flex justify-between items-start mb-1">
                    <span className="text-sm font-medium text-yellow-800">
                      Ligne {warning.ligne}
                      {warning.colonne && ` - Colonne ${warning.colonne}`}
                    </span>
                  </div>
                  <p className="text-sm text-yellow-700 mb-1">{warning.message}</p>
                  <p className="text-xs text-gray-600">
                    Valeur: {JSON.stringify(warning.valeur)}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Actions */}
      <div className="flex justify-end space-x-3">
        {!success && onRetry && (
          <button
            onClick={onRetry}
            className="px-4 py-2 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Réessayer
          </button>
        )}
        {onClose && (
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Fermer
          </button>
        )}
      </div>
    </div>
  );
};
