import React, { useState, useEffect, useRef } from 'react';
import { toast } from 'react-toastify';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { chambreService, Chambre } from '../../services/chambre.service';
import { reservationService } from '../../services/reservation.service';
import { anonymousReservationService, AnonymousReservationDetails } from '../../services/anonymousReservation.service';
import { AnonymousReservationPaymentInfo, AnonymousPaymentInstructions } from './AnonymousReservationPaymentInfo';

// Interfaces
interface ReceptionReservationViewProps {
  onClose: () => void;
}

const ReceptionReservationView: React.FC<ReceptionReservationViewProps> = ({ onClose }) => {
  const [view, setView] = useState<'list' | 'calendar'>('list');
  const [pendingReservations, setPendingReservations] = useState<any[]>([]);
  const [anonymousReservations, setAnonymousReservations] = useState<AnonymousReservationDetails[]>([]);
  const [showAnonymousReservations, setShowAnonymousReservations] = useState(true);
  const [chambres, setChambres] = useState<Chambre[]>([]);
  const [selectedChambreId, setSelectedChambreId] = useState<number | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [events, setEvents] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [reservationNumber, setReservationNumber] = useState<string>('');
  const [filteredReservation, setFilteredReservation] = useState<any | null>(null);
  const [searchType, setSearchType] = useState<'normal' | 'anonymous'>('normal');
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [selectedReservationId, setSelectedReservationId] = useState<string | null>(null);
  const currentDateRef = useRef<Date>(new Date());

  // Ajouter des états pour le paiement
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState<number>(0);
  const [paymentMethod, setPaymentMethod] = useState<string>('especes');
  const [paymentReference, setPaymentReference] = useState<string>('');
  const [isPartialPayment, setIsPartialPayment] = useState<boolean>(false);
  const [paymentErrors, setPaymentErrors] = useState<{[key: string]: string}>({});
  const [chambresReservation, setChambresReservation] = useState<Array<{
    chambre_id: string;
    numero: string;
    type_chambre: string;
    prix_nuit: number;
  }>>([]);

  // Charger la liste des réservations en attente (normales et anonymes)
  useEffect(() => {
    const fetchAllPendingReservations = async () => {
      try {
        setLoading(true);

        // Récupérer les réservations normales
        const normalResponse = await reservationService.getPendingReservations({});

        // Récupérer les réservations anonymes en attente (via l'API admin)
        let anonymousReservationsData: AnonymousReservationDetails[] = [];
        try {
          // Note: Cette API devra être créée côté backend pour récupérer les réservations anonymes en attente
          // Pour l'instant, on utilise une liste vide
          anonymousReservationsData = [];
        } catch (anonymousError) {
          console.warn('Impossible de récupérer les réservations anonymes:', anonymousError);
        }

        if (normalResponse.success) {
          setPendingReservations(normalResponse.data.reservations || []);
          setAnonymousReservations(anonymousReservationsData);
          setFilteredReservation(null); // Réinitialiser la réservation filtrée
        } else {
          toast.error(normalResponse.message || 'Erreur lors de la récupération des réservations');
          setPendingReservations([]);
          setAnonymousReservations([]);
        }
      } catch (error) {
        console.error('Erreur lors de la récupération des réservations:', error);
        toast.error('Erreur lors de la récupération des réservations');
        setPendingReservations([]);
        setAnonymousReservations([]);
      } finally {
        setLoading(false);
      }
    };

    if (view === 'list') {
      fetchAllPendingReservations();
    }
  }, [view]);

  // Charger la liste des chambres
  useEffect(() => {
    const fetchChambres = async () => {
      try {
        const response = await chambreService.getChambres({});
        if (response.success) {
          setChambres(response.data.chambres);
        }
      } catch (error) {
        console.error('Erreur lors de la récupération des chambres:', error);
        toast.error('Erreur lors de la récupération des chambres');
      }
    };
    fetchChambres();
  }, []);

  // Charger l'historique des réservations pour une chambre
  const loadHistoriqueReservations = async (chambreId: number, date: Date) => {
    try {
      setLoading(true);
      
      // Calculer le début et la fin de la semaine
      const startOfWeek = new Date(date);
      startOfWeek.setDate(date.getDate() - date.getDay()); // Début de la semaine (dimanche)
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6); // Fin de la semaine (samedi)
      
      const dateDebut = startOfWeek.toISOString().split('T')[0];
      const dateFin = endOfWeek.toISOString().split('T')[0];
      
      const response = await reservationService.getHistoriqueReservationsCalendrier({
        date_debut: dateDebut,
        date_fin: dateFin,
        chambre_id: chambreId.toString()
      });

      if (response.success && response.data.chambres) {
        const chambreData = response.data.chambres.find(c => c.chambre_id === chambreId);

        if (chambreData && chambreData.reservations) {
          const calendarEvents = chambreData.reservations.map((reservation) => {
            return {
              id: `reservation-${reservation.reservation_id}`,
              title: `${reservation.client_nom} ${reservation.client_prenom} - ${reservation.statut}`,
              start: reservation.date_arrivee,
              end: reservation.date_depart,
              backgroundColor: getReservationColor(reservation.statut),
              borderColor: getReservationColor(reservation.statut),
              extendedProps: {
                reservation_id: reservation.reservation_id,
                numero_reservation: reservation.numero_reservation,
                statut: reservation.statut,
                client_nom: reservation.client_nom,
                client_prenom: reservation.client_prenom,
                client_email: reservation.client_email,
                client_telephone: reservation.client_telephone,
                montant_total: reservation.montant_total,
                commentaires: reservation.commentaires,
                raison_rejet: reservation.raison_rejet,
                heure_debut: reservation.heure_debut,
                heure_fin: reservation.heure_fin,
                chambre_id: chambreId
              }
            };
          });
          setEvents(calendarEvents);
        } else {
          setEvents([]);
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement de l\'historique des réservations:', error);
      toast.error('Erreur lors du chargement de l\'historique des réservations');
    } finally {
      setLoading(false);
    }
  };

  // Récupérer les chambres d'une réservation
  const fetchChambresReservation = async (reservationId: string) => {
    try {
      console.log('Tentative de récupération des chambres pour la réservation:', reservationId);

      // Utiliser la nouvelle API pour récupérer les chambres de la réservation
      const chambresResponse = await reservationService.getChambresReservation(reservationId);
      console.log('Réponse des chambres:', chambresResponse);

      if (chambresResponse.success && chambresResponse.data && chambresResponse.data.length > 0) {
        console.log('Chambres trouvées:', chambresResponse.data);
        // Transformer les données pour correspondre au format attendu
        const chambresFormatted = chambresResponse.data.map(chambre => ({
          chambre_id: chambre.chambre_id,
          numero: chambre.numero,
          type_chambre: chambre.type_chambre,
          prix_nuit: chambre.prix_nuit
        }));
        setChambresReservation(chambresFormatted);
        return chambresFormatted;
      } else {
        console.log('Aucune chambre trouvée - utilisation des chambres déjà attribuées');
        // Pour les réservations en attente, les chambres sont déjà dans ChambresReservees
        // Le backend les utilisera automatiquement
        setChambresReservation([{ chambre_id: 'auto', numero: 'Attribuées automatiquement', type_chambre: 'Selon demande client', prix_nuit: 0 }]);
        return [{ chambre_id: 'auto', numero: 'Attribuées automatiquement', type_chambre: 'Selon demande client', prix_nuit: 0 }];
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des chambres de la réservation:', error);
      toast.error('Erreur lors de la récupération des chambres de la réservation');
      return [];
    }
  };

  // Valider une réservation (normale ou anonyme)
  const handleValidateReservation = async (reservationId: string) => {
    try {
      let reservation = null;
      let isAnonymous = false;

      // Vérifier d'abord si c'est une réservation filtrée (peut être anonyme)
      if (filteredReservation && filteredReservation.reservation_id.toString() === reservationId) {
        reservation = filteredReservation;
        isAnonymous = filteredReservation.est_anonyme || false;
      } else {
        // Sinon, chercher dans les réservations normales
        reservation = pendingReservations.find(r => r.reservation_id.toString() === reservationId);
        isAnonymous = reservation?.est_anonyme || false;
      }

      if (!reservation) {
        toast.error('Détails de réservation introuvables');
        return;
      }

      // Pour les réservations anonymes, récupérer les chambres différemment
      let chambres = [];
      if (isAnonymous && reservation.chambres) {
        // Les réservations anonymes ont déjà les chambres dans leur structure
        chambres = reservation.chambres;
      } else {
        // Pour les réservations normales, récupérer les chambres via l'API
        chambres = await fetchChambresReservation(reservationId);
      }

      if (chambres.length === 0) {
        toast.error('Aucune chambre trouvée pour cette réservation');
        return;
      }

      // Stocker les informations pour le modal de paiement
      setChambresReservation(chambres);

      // Initialiser le modal de paiement avec les bonnes valeurs
      setPaymentAmount(reservation.montant_total);
      setPaymentMethod('especes');
      setPaymentReference('');
      setIsPartialPayment(false);
      setPaymentErrors({});

      setSelectedReservationId(reservationId);
      setShowPaymentModal(true);
    } catch (error) {
      console.error('Erreur lors de la validation:', error);
      toast.error('Erreur lors de la validation de la réservation');
    }
  };

  // Refuser une réservation
  const handleRejectReservation = async (reservationId: string) => {
    setSelectedReservationId(reservationId);
    setShowRejectModal(true);
  };

  const confirmRejectReservation = async () => {
    if (!selectedReservationId || !rejectReason.trim()) {
      toast.error('Veuillez fournir une raison pour le refus');
      return;
    }

    try {
      // Déterminer si c'est une réservation anonyme
      let isAnonymous = false;
      if (filteredReservation && filteredReservation.reservation_id.toString() === selectedReservationId) {
        isAnonymous = filteredReservation.est_anonyme || false;
      } else {
        const reservation = pendingReservations.find(r => r.reservation_id.toString() === selectedReservationId);
        isAnonymous = reservation?.est_anonyme || false;
      }

      if (isAnonymous) {
        // Pour les réservations anonymes, utiliser l'API spécifique si disponible
        // Note: Cette API devra être créée côté backend pour refuser les réservations anonymes
        // Pour l'instant, on utilise l'API normale
        await reservationService.rejectReservation(
          selectedReservationId,
          rejectReason.trim()
        );
        toast.success('Réservation anonyme refusée avec succès');
      } else {
        // Pour les réservations normales
        await reservationService.rejectReservation(
          selectedReservationId,
          rejectReason.trim()
        );
        toast.success('Réservation refusée avec succès');
      }

      // Recharger la liste des réservations
      const updatedReservations = await reservationService.getPendingReservations({});
      if (updatedReservations.success && updatedReservations.data) {
        setPendingReservations(updatedReservations.data.reservations);
        if (filteredReservation?.reservation_id.toString() === selectedReservationId) {
          setFilteredReservation(null);
          setReservationNumber('');
        }
      }
    } catch (error) {
      console.error('Erreur lors du refus:', error);
      toast.error('Erreur lors du refus de la réservation');
    } finally {
      setShowRejectModal(false);
      setRejectReason('');
      setSelectedReservationId(null);
    }
  };

  // Obtenir la couleur en fonction du statut de réservation
  const getReservationColor = (statut: string) => {
    switch (statut?.toLowerCase()) {
      case 'en_attente':
        return '#FCD34D'; // Jaune - En attente
      case 'confirmee':
        return '#34D399'; // Vert - Confirmée
      case 'terminee':
        return '#9CA3AF'; // Gris - Terminée
      case 'annulee':
        return '#EF4444'; // Rouge - Annulée
      default:
        return '#60A5FA'; // Bleu par défaut
    }
  };

  // Fonction pour rechercher une réservation par numéro ou code d'accès
  const handleSearchReservation = async () => {
    if (!reservationNumber.trim()) {
      setFilteredReservation(null);
      return;
    }

    try {
      setLoading(true);

      if (searchType === 'anonymous') {
        // Recherche de réservation anonyme par code d'accès
        const response = await anonymousReservationService.getReservationAnonyme(reservationNumber.trim().toUpperCase());

        if (response.success && response.data) {
          // Convertir la réservation anonyme au format attendu
          const anonymousReservation = {
            ...response.data,
            client_nom: response.data.pseudonyme || 'Client Anonyme',
            client_prenom: '',
            client_email: 'Réservation anonyme',
            client_telephone: 'Non disponible',
            est_anonyme: true,
            code_acces_anonyme: reservationNumber.trim().toUpperCase()
          };
          setFilteredReservation(anonymousReservation);
          toast.success('Réservation anonyme trouvée');
        } else {
          setFilteredReservation(null);
          toast.error('Réservation anonyme non trouvée ou code invalide');
        }
      } else {
        // Recherche de réservation normale par numéro
        const reservation = pendingReservations.find(
          (r) => r.numero_reservation === reservationNumber.trim()
        );

        if (reservation) {
          setFilteredReservation(reservation);
          toast.success('Réservation trouvée');
        } else {
          setFilteredReservation(null);
          toast.error('Aucune réservation trouvée avec ce numéro');
        }
      }
    } catch (error) {
      console.error('Erreur lors de la recherche:', error);
      setFilteredReservation(null);
      toast.error('Erreur lors de la recherche de la réservation');
    } finally {
      setLoading(false);
    }
  };

  // Validation du paiement (normale ou anonyme)
  const validatePayment = (): boolean => {
    const errors: {[key: string]: string} = {};

    // Validation du montant
    if (!paymentAmount || paymentAmount <= 0) {
      errors.montant = 'Le montant doit être supérieur à 0';
    }

    // Validation de la référence pour certains modes de paiement
    if (['carte', 'virement', 'cheque', 'mobile'].includes(paymentMethod) && !paymentReference.trim()) {
      errors.reference = 'Une référence est requise pour ce mode de paiement';
    }

    // Vérification du montant par rapport au total de la réservation
    let reservation = null;

    // Vérifier d'abord si c'est une réservation filtrée (peut être anonyme)
    if (filteredReservation && filteredReservation.reservation_id.toString() === selectedReservationId) {
      reservation = filteredReservation;
    } else {
      // Sinon, chercher dans les réservations normales
      reservation = pendingReservations.find(r => r.reservation_id.toString() === selectedReservationId);
    }

    if (reservation) {
      if (!isPartialPayment && paymentAmount !== reservation.montant_total) {
        errors.montant = `Le montant doit être égal au total de la réservation (${reservation.montant_total} FCFA)`;
      }
      if (isPartialPayment && paymentAmount > reservation.montant_total) {
        errors.montant = `Le montant ne peut pas dépasser le total de la réservation (${reservation.montant_total} FCFA)`;
      }
    }

    setPaymentErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Confirmer le paiement et la réservation (normale ou anonyme)
  const confirmPaymentAndReservation = async () => {
    if (!selectedReservationId) {
      toast.error('Aucune réservation sélectionnée');
      return;
    }

    // Valider les données de paiement
    if (!validatePayment()) {
      toast.error('Veuillez corriger les erreurs dans le formulaire de paiement');
      return;
    }

    // Déterminer si c'est une réservation anonyme
    let reservation = null;
    let isAnonymous = false;

    // Vérifier d'abord si c'est une réservation filtrée (peut être anonyme)
    if (filteredReservation && filteredReservation.reservation_id.toString() === selectedReservationId) {
      reservation = filteredReservation;
      isAnonymous = filteredReservation.est_anonyme || false;
    } else {
      // Sinon, chercher dans les réservations normales
      reservation = pendingReservations.find(r => r.reservation_id.toString() === selectedReservationId);
      isAnonymous = reservation?.est_anonyme || false;
    }

    if (!reservation) {
      toast.error('Réservation introuvable');
      return;
    }

    try {
      // Utiliser les chambres de la réservation (déjà récupérées lors de l'ouverture du modal)
      let chambresIds = chambresReservation.map(chambre => chambre.chambre_id);

      // Si on a une chambre automatique, on utilise une liste vide pour que le backend utilise les chambres existantes
      if (chambresIds.length === 1 && (chambresIds[0] === 'temp' || chambresIds[0] === 'auto')) {
        chambresIds = [];
      }

      console.log('Chambres IDs à envoyer au backend:', chambresIds);
      console.log('Type de réservation:', isAnonymous ? 'Anonyme' : 'Normale');

      if (isAnonymous && reservation.code_acces_anonyme) {
        // Pour les réservations anonymes, utiliser l'API spécifique
        // Note: Cette API devra être créée côté backend pour confirmer les réservations anonymes
        // Pour l'instant, on utilise l'API normale mais on pourrait l'adapter
        await reservationService.confirmReservation(selectedReservationId, {
          chambres_ids: chambresIds,
          paiement: {
            montant: paymentAmount,
            mode_paiement: paymentMethod as any,
            reference_paiement: paymentReference || undefined
          },
          // Ajouter un flag pour indiquer que c'est une réservation anonyme
          est_anonyme: true,
          code_acces_anonyme: reservation.code_acces_anonyme
        });
      } else {
        // Pour les réservations normales
        await reservationService.confirmReservation(selectedReservationId, {
          chambres_ids: chambresIds,
          paiement: {
            montant: paymentAmount,
            mode_paiement: paymentMethod as any,
            reference_paiement: paymentReference || undefined
          }
        });
      }

      const isFullPayment = paymentAmount >= reservation.montant_total;

      toast.success(
        isAnonymous
          ? (isFullPayment
              ? 'Réservation anonyme confirmée et entièrement payée avec succès'
              : 'Réservation anonyme confirmée avec paiement partiel enregistré')
          : (isFullPayment
              ? 'Réservation confirmée et entièrement payée avec succès'
              : 'Réservation confirmée avec paiement partiel enregistré')
      );

      // Recharger la liste des réservations en attente
      const response = await reservationService.getPendingReservations({});
      if (response.success) {
        setPendingReservations(response.data.reservations);
      }

      // Réinitialiser la réservation filtrée si c'était celle qui a été confirmée
      if (filteredReservation && filteredReservation.reservation_id.toString() === selectedReservationId) {
        setFilteredReservation(null);
        setReservationNumber('');
      }

      // Générer automatiquement le ticket de caisse après confirmation
      if (selectedReservationId) {
        try {
          await handlePrintTicketCaisse(selectedReservationId, isAnonymous);
        } catch (error) {
          console.error('Erreur lors de la génération automatique du ticket:', error);
          // Ne pas bloquer le processus si l'impression échoue
        }
      }

      // Fermer le modal et réinitialiser les états
      resetPaymentModal();
    } catch (error) {
      console.error('Erreur lors de la confirmation:', error);
      toast.error(`Erreur lors de la confirmation de la réservation ${isAnonymous ? 'anonyme' : ''}`);
    }
  };

  // Réinitialiser le modal de paiement
  const resetPaymentModal = () => {
    setShowPaymentModal(false);
    setPaymentAmount(0);
    setPaymentMethod('especes');
    setPaymentReference('');
    setIsPartialPayment(false);
    setPaymentErrors({});
    setChambresReservation([]);
    setSelectedReservationId(null);
  };

  // Fonction pour imprimer le ticket de caisse avec nouveau QR code
  const handlePrintTicketCaisse = async (reservationId: string, isAnonymous: boolean = false) => {
    try {
      const pdfBlob = await reservationService.genererTicketCaisse(reservationId);

      // Créer un URL pour le blob
      const url = window.URL.createObjectURL(pdfBlob);

      // Créer un lien de téléchargement
      const link = document.createElement('a');
      link.href = url;
      link.download = `ticket-${isAnonymous ? 'anonyme-' : ''}reservation-${reservationId}.pdf`;

      // Déclencher le téléchargement
      document.body.appendChild(link);
      link.click();

      // Nettoyer
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success(
        isAnonymous
          ? 'Ticket de caisse anonyme avec QR code généré et téléchargé avec succès'
          : 'Ticket de caisse avec nouveau QR code généré et téléchargé avec succès'
      );
    } catch (error) {
      console.error('Erreur lors de l\'impression du ticket:', error);
      toast.error(`Erreur lors de la génération du ticket de caisse ${isAnonymous ? 'anonyme' : ''}`);
    }
  };

  return (
    <div className="p-6">
      {/* En-tête */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Gestion des Réservations - Réception</h1>
        <div className="flex space-x-4">
          {view === 'list' && (
            <button
              onClick={() => setShowAnonymousReservations(!showAnonymousReservations)}
              className={`px-4 py-2 rounded-lg transition-colors ${
                showAnonymousReservations
                  ? 'bg-indigo-500 text-white hover:bg-indigo-600'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {showAnonymousReservations ? 'Masquer les anonymes' : 'Afficher les anonymes'}
            </button>
          )}
          <button
            onClick={() => setView(view === 'list' ? 'calendar' : 'list')}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            {view === 'list' ? 'Voir le calendrier' : 'Voir les réservations en attente'}
          </button>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            Retour au tableau de bord
          </button>
        </div>
      </div>

      {view === 'list' ? (
        // Vue liste des réservations en attente
        <div className="bg-white rounded-lg shadow p-6">
          {/* Champ de recherche */}
          <div className="mb-6">
            <div className="space-y-3">
              {/* Sélecteur de type de recherche */}
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="searchType"
                    value="normal"
                    checked={searchType === 'normal'}
                    onChange={(e) => setSearchType(e.target.value as 'normal' | 'anonymous')}
                    className="mr-2"
                  />
                  <span className="text-sm font-medium">Réservation normale</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="searchType"
                    value="anonymous"
                    checked={searchType === 'anonymous'}
                    onChange={(e) => setSearchType(e.target.value as 'normal' | 'anonymous')}
                    className="mr-2"
                  />
                  <span className="text-sm font-medium">Réservation anonyme</span>
                </label>
              </div>

              {/* Champ de recherche */}
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={reservationNumber}
                  onChange={(e) => setReservationNumber(e.target.value)}
                  placeholder={searchType === 'anonymous' ? 'Entrez le code d\'accès (ANON-XXXXXXXXXXXX)' : 'Entrez le numéro de réservation'}
                  className="flex-1 px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                  onClick={handleSearchReservation}
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  Rechercher
                </button>
              </div>

              {searchType === 'anonymous' && (
                <p className="text-xs text-gray-500">
                  Format du code d'accès: ANON-XXXXXXXXXXXX (4 lettres + tiret + 12 caractères)
                </p>
              )}
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredReservation ? (
                // Afficher la réservation filtrée
                <div className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex justify-between items-start">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-medium text-lg">
                          {filteredReservation.client_nom} {filteredReservation.client_prenom}
                        </h3>
                        <span className="px-2 py-1 bg-blue-200 text-blue-800 text-xs rounded-full">
                          Recherche
                        </span>
                        {filteredReservation.est_anonyme && (
                          <span className="px-2 py-1 bg-indigo-200 text-indigo-800 text-xs rounded-full">
                            Anonyme
                          </span>
                        )}
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-600">
                            <span className="font-medium">Arrivée:</span> {new Date(filteredReservation.date_arrivee).toLocaleDateString()}
                            {filteredReservation.heure_debut && (
                              <span className="ml-1 text-blue-600">à {filteredReservation.heure_debut}</span>
                            )}
                          </p>
                          <p className="text-gray-600">
                            <span className="font-medium">Départ:</span> {new Date(filteredReservation.date_depart).toLocaleDateString()}
                            {filteredReservation.heure_fin && (
                              <span className="ml-1 text-blue-600">à {filteredReservation.heure_fin}</span>
                            )}
                          </p>
                        </div>
                        <div>
                          <p className="text-gray-600">
                            <span className="font-medium">Montant total:</span> {filteredReservation.montant_total} FCFA
                          </p>
                        </div>
                      </div>
                      <div className="text-sm">
                        {filteredReservation.est_anonyme ? (
                          <>
                            <p className="text-gray-600">
                              <span className="font-medium">Type:</span> Réservation anonyme
                            </p>
                            {filteredReservation.code_acces_anonyme && (
                              <p className="text-gray-600">
                                <span className="font-medium">Code d'accès:</span>
                                <span className="font-mono ml-1">{filteredReservation.code_acces_anonyme}</span>
                              </p>
                            )}
                          </>
                        ) : (
                          <>
                            <p className="text-gray-600">
                              <span className="font-medium">Téléphone:</span> {filteredReservation.client_telephone}
                            </p>
                            {filteredReservation.client_email && (
                              <p className="text-gray-600">
                                <span className="font-medium">Email:</span> {filteredReservation.client_email}
                              </p>
                            )}
                          </>
                        )}
                      </div>
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Commentaires:</span> {filteredReservation.commentaires}
                      </p>
                    </div>
                    <div className="flex flex-col items-end space-y-2">
                      <span className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">
                        En attente
                      </span>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleValidateReservation(filteredReservation.reservation_id.toString())}
                          className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600"
                        >
                          Valider
                        </button>
                        <button
                          onClick={() => handleRejectReservation(filteredReservation.reservation_id.toString())}
                          className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
                        >
                          Refuser
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <>
                  {pendingReservations.length === 0 ? (
                    <p className="text-center text-gray-500">Aucune réservation en attente</p>
                  ) : (
                    pendingReservations.map((reservation) => (
                      <div
                        key={reservation.reservation_id}
                        className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex justify-between items-start">
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <h3 className="font-medium text-lg">
                                {reservation.client_nom} {reservation.client_prenom}
                              </h3>
                              {reservation.est_anonyme && (
                                <span className="px-2 py-1 bg-indigo-200 text-indigo-800 text-xs rounded-full">
                                  Anonyme
                                </span>
                              )}
                            </div>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <p className="text-gray-600">
                                  <span className="font-medium">Arrivée:</span> {new Date(reservation.date_arrivee).toLocaleDateString()}
                                  {reservation.heure_debut && (
                                    <span className="ml-1 text-blue-600">à {reservation.heure_debut}</span>
                                  )}
                                </p>
                                <p className="text-gray-600">
                                  <span className="font-medium">Départ:</span> {new Date(reservation.date_depart).toLocaleDateString()}
                                  {reservation.heure_fin && (
                                    <span className="ml-1 text-blue-600">à {reservation.heure_fin}</span>
                                  )}
                                </p>
                              </div>
                              <div>
                                <p className="text-gray-600">
                                  <span className="font-medium">Montant total:</span> {reservation.montant_total} FCFA
                                </p>
                              </div>
                            </div>
                            <div className="text-sm">
                              {reservation.est_anonyme ? (
                                <>
                                  <p className="text-gray-600">
                                    <span className="font-medium">Type:</span> Réservation anonyme
                                  </p>
                                  {reservation.code_acces_anonyme && (
                                    <p className="text-gray-600">
                                      <span className="font-medium">Code d'accès:</span>
                                      <span className="font-mono ml-1">{reservation.code_acces_anonyme}</span>
                                    </p>
                                  )}
                                </>
                              ) : (
                                <>
                                  <p className="text-gray-600">
                                    <span className="font-medium">Téléphone:</span> {reservation.client_telephone}
                                  </p>
                                  {reservation.client_email && (
                                    <p className="text-gray-600">
                                      <span className="font-medium">Email:</span> {reservation.client_email}
                                    </p>
                                  )}
                                </>
                              )}
                            </div>
                            <p className="text-sm text-gray-600">
                              <span className="font-medium">Commentaires:</span> {reservation.commentaires}
                            </p>
                          </div>
                          <div className="flex flex-col items-end space-y-2">
                            <span className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">
                              En attente
                            </span>
                            <div className="flex space-x-2">
                              <button
                                onClick={() => handleValidateReservation(reservation.reservation_id.toString())}
                                className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600"
                              >
                                Valider
                              </button>
                              <button
                                onClick={() => handleRejectReservation(reservation.reservation_id.toString())}
                                className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
                              >
                                Refuser
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </>
              )}
            </div>
          )}
        </div>
      ) : (
        // Vue calendrier
        <>
          {/* Sélection de la chambre */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Sélectionner une chambre
            </label>
            <select
              value={selectedChambreId || ''}
              onChange={(e) => {
                const chambreId = Number(e.target.value);
                setSelectedChambreId(chambreId);
                if (chambreId) {
                  loadHistoriqueReservations(chambreId, selectedDate);
                }
              }}
              className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Sélectionner une chambre</option>
              {chambres.map((chambre) => (
                <option key={chambre.chambre_id} value={chambre.chambre_id}>
                  Chambre {chambre.numero} - {chambre.type_chambre}
                </option>
              ))}
            </select>
          </div>

          {/* Filtres */}
          <div className="mb-6 flex items-center justify-between">
            <input
              type="date"
              value={selectedDate.toISOString().split('T')[0]}
              onChange={(e) => {
                setSelectedDate(new Date(e.target.value));
                if (selectedChambreId) {
                  loadHistoriqueReservations(selectedChambreId, selectedDate);
                }
              }}
              className="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Calendrier */}
          <div className="bg-white rounded-lg shadow p-6">
            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              <FullCalendar
                plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
                headerToolbar={{
                  left: 'prev,next today',
                  center: 'title',
                  right: ''
                }}
                initialView="timeGridWeek"
                initialDate={selectedDate}
                views={{
                  timeGridWeek: {
                    titleFormat: { year: 'numeric', month: 'long', day: 'numeric' }
                  }
                }}
                events={events}
                height="auto"
                locale="fr"
                slotMinTime="00:00:00"
                slotMaxTime="24:00:00"
                allDaySlot={false}
                eventTimeFormat={{
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: false
                }}
                slotDuration="01:00:00"
                slotLabelInterval="01:00"
                slotLabelFormat={{
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: false
                }}
                expandRows={true}
                stickyHeaderDates={true}
                dayMaxEvents={true}
                datesSet={(dateInfo) => {
                  if (selectedChambreId) {
                    const newDate = dateInfo.start;
                    if (newDate.getTime() !== currentDateRef.current.getTime()) {
                      currentDateRef.current = newDate;
                      setSelectedDate(newDate);
                      loadHistoriqueReservations(selectedChambreId, newDate);
                    }
                  }
                }}
                loading={(isLoading) => setLoading(isLoading)}
              />
            )}
          </div>
        </>
      )}

      {/* Modal de refus */}
      {showRejectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Refuser la réservation</h3>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Raison du refus
              </label>
              <textarea
                value={rejectReason}
                onChange={(e) => setRejectReason(e.target.value)}
                className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={4}
                placeholder="Entrez la raison du refus..."
              />
            </div>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => {
                  setShowRejectModal(false);
                  setRejectReason('');
                  setSelectedReservationId(null);
                }}
                className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                Annuler
              </button>
              <button
                onClick={confirmRejectReservation}
                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              >
                Confirmer le refus
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de paiement */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-lg">
            <h2 className="text-xl font-bold mb-4">Paiement de la réservation</h2>

            {/* Informations de la réservation */}
            {(() => {
              // Chercher d'abord dans les réservations filtrées (peut être anonyme)
              let reservation = null;
              if (filteredReservation && filteredReservation.reservation_id.toString() === selectedReservationId) {
                reservation = filteredReservation;
              } else {
                reservation = pendingReservations.find(r => r.reservation_id.toString() === selectedReservationId);
              }

              if (!reservation) return null;

              return (
                <div className="mb-6 space-y-4">
                  {/* Informations spécifiques aux réservations anonymes */}
                  <AnonymousReservationPaymentInfo reservation={reservation} />

                  {/* Informations générales de la réservation */}
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h3 className="font-medium mb-2">Détails de la réservation</h3>
                    <div className="text-sm space-y-1">
                      <p>
                        <span className="font-medium">Client:</span> {
                          reservation.est_anonyme
                            ? (reservation.pseudonyme || 'Client Anonyme')
                            : `${reservation.client_nom} ${reservation.client_prenom || ''}`.trim()
                        }
                      </p>
                      <p><span className="font-medium">Montant total:</span> {reservation.montant_total} FCFA</p>
                      <p>
                        <span className="font-medium">Période:</span> {new Date(reservation.date_arrivee).toLocaleDateString()}
                        {reservation.heure_debut && <span className="text-blue-600"> à {reservation.heure_debut}</span>}
                        {' - '}
                        {new Date(reservation.date_depart).toLocaleDateString()}
                        {reservation.heure_fin && <span className="text-blue-600"> à {reservation.heure_fin}</span>}
                      </p>
                    </div>
                  </div>

                  {/* Instructions pour les réservations anonymes */}
                  {reservation.est_anonyme && (
                    <AnonymousPaymentInstructions />
                  )}
                </div>
              );
            })()}

            {/* Affichage des chambres de la réservation */}
            {chambresReservation.length > 0 && (
              <div className="mb-6">
                <h3 className="font-medium mb-3">Chambres de la réservation</h3>
                <div className="border rounded-lg p-3 bg-gray-50">
                  <div className="space-y-2">
                    {chambresReservation.map((chambre) => (
                      <div key={chambre.chambre_id} className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm">
                          Chambre {chambre.numero} - {chambre.type_chambre} ({chambre.prix_nuit} FCFA/heure)
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Option paiement partiel */}
            <div className="mb-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={isPartialPayment}
                  onChange={(e) => {
                    setIsPartialPayment(e.target.checked);
                    if (!e.target.checked) {
                      // Chercher la réservation dans les deux sources possibles
                      let reservation = null;
                      if (filteredReservation && filteredReservation.reservation_id.toString() === selectedReservationId) {
                        reservation = filteredReservation;
                      } else {
                        reservation = pendingReservations.find(r => r.reservation_id.toString() === selectedReservationId);
                      }
                      if (reservation) {
                        setPaymentAmount(reservation.montant_total);
                      }
                    }
                  }}
                  className="rounded"
                />
                <span className="text-sm font-medium text-gray-700">Paiement partiel</span>
              </label>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Montant (FCFA) *
              </label>
              <input
                type="number"
                value={paymentAmount}
                onChange={(e) => {
                  setPaymentAmount(Number(e.target.value));
                  // Effacer l'erreur de montant si elle existe
                  if (paymentErrors.montant) {
                    setPaymentErrors(prev => ({ ...prev, montant: '' }));
                  }
                }}
                className={`w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  paymentErrors.montant ? 'border-red-500' : 'border-gray-300'
                }`}
                min="0"
                step="0.01"
              />
              {paymentErrors.montant && (
                <p className="text-red-500 text-xs mt-1">{paymentErrors.montant}</p>
              )}
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mode de paiement *
              </label>
              <select
                value={paymentMethod}
                onChange={(e) => {
                  setPaymentMethod(e.target.value);
                  // Effacer l'erreur de référence si elle existe
                  if (paymentErrors.reference) {
                    setPaymentErrors(prev => ({ ...prev, reference: '' }));
                  }
                }}
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="especes">Espèces</option>
                <option value="carte">Carte bancaire</option>
                <option value="virement">Virement bancaire</option>
                <option value="cheque">Chèque</option>
                <option value="mobile">Mobile Money</option>
              </select>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Référence de paiement {['carte', 'virement', 'cheque', 'mobile'].includes(paymentMethod) && '*'}
              </label>
              <input
                type="text"
                value={paymentReference}
                onChange={(e) => {
                  setPaymentReference(e.target.value);
                  // Effacer l'erreur de référence si elle existe
                  if (paymentErrors.reference) {
                    setPaymentErrors(prev => ({ ...prev, reference: '' }));
                  }
                }}
                className={`w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  paymentErrors.reference ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder={
                  paymentMethod === 'carte' ? 'Numéro de transaction' :
                  paymentMethod === 'virement' ? 'Référence de virement' :
                  paymentMethod === 'cheque' ? 'Numéro de chèque' :
                  paymentMethod === 'mobile' ? 'ID de transaction mobile' :
                  'Référence (optionnel)'
                }
              />
              {paymentErrors.reference && (
                <p className="text-red-500 text-xs mt-1">{paymentErrors.reference}</p>
              )}
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded p-3 mb-4">
              <p className="text-sm text-blue-700">
                <i className="fas fa-info-circle mr-2"></i>
                Un ticket de caisse avec le nouveau QR code sera automatiquement généré après confirmation du paiement.
              </p>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={resetPaymentModal}
                className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300 transition-colors"
              >
                Annuler
              </button>
              <button
                onClick={confirmPaymentAndReservation}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                Confirmer le paiement
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReceptionReservationView; 
