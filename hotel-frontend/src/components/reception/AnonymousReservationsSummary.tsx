import { useState, useEffect } from 'react';
import { User<PERSON><PERSON><PERSON>, Clock, AlertCircle } from 'lucide-react';
import { anonymousReservationService, PublicStats } from '../../services/anonymousReservation.service';

interface AnonymousReservationsSummaryProps {
  className?: string;
}

export function AnonymousReservationsSummary({ className = '' }: AnonymousReservationsSummaryProps) {
  const [stats, setStats] = useState<PublicStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await anonymousReservationService.getPublicStats();
      if (response.success) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error);
      setError('Impossible de charger les statistiques');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !stats) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-4 ${className}`}>
        <div className="flex items-center text-gray-500">
          <AlertCircle className="h-4 w-4 mr-2" />
          <span className="text-sm">{error || 'Aucune donnée disponible'}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-md p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-900 flex items-center">
          <UserCheck className="h-4 w-4 mr-2 text-indigo-500" />
          Réservations Anonymes
        </h3>
        <button
          onClick={loadStats}
          className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
        >
          Actualiser
        </button>
      </div>

      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">En attente:</span>
          <span className="text-sm font-medium text-orange-600">
            {stats.reservations_actives}
          </span>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Total:</span>
          <span className="text-sm font-medium text-gray-900">
            {stats.total_reservations_anonymes}
          </span>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Taux confirmation:</span>
          <span className="text-sm font-medium text-green-600">
            {Math.round(stats.taux_confirmation)}%
          </span>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600 flex items-center">
            <Clock className="h-3 w-3 mr-1" />
            Délai moyen:
          </span>
          <span className="text-sm font-medium text-blue-600">
            {Math.round(stats.delai_moyen_confirmation)}h
          </span>
        </div>
      </div>

      {stats.reservations_actives > 0 && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <div className="flex items-center text-xs text-orange-600">
            <AlertCircle className="h-3 w-3 mr-1" />
            <span>{stats.reservations_actives} réservation(s) en attente de validation</span>
          </div>
        </div>
      )}
    </div>
  );
}

// Composant pour afficher un indicateur rapide dans la barre latérale
export function AnonymousReservationsIndicator({ className = '' }: { className?: string }) {
  const [pendingCount, setPendingCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadPendingCount = async () => {
      try {
        const response = await anonymousReservationService.getPublicStats();
        if (response.success) {
          setPendingCount(response.data.reservations_actives);
        }
      } catch (error) {
        console.error('Erreur lors du chargement du compteur:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadPendingCount();
    
    // Actualiser toutes les 30 secondes
    const interval = setInterval(loadPendingCount, 30000);
    return () => clearInterval(interval);
  }, []);

  if (isLoading) {
    return (
      <div className={`flex items-center ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 w-4 bg-gray-200 rounded mr-2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex items-center ${className}`}>
      <UserCheck className="h-4 w-4 text-indigo-500 mr-2" />
      <span className="text-sm text-gray-700">
        Anonymes: 
        <span className={`ml-1 font-medium ${pendingCount > 0 ? 'text-orange-600' : 'text-gray-500'}`}>
          {pendingCount}
        </span>
      </span>
      {pendingCount > 0 && (
        <span className="ml-2 px-1.5 py-0.5 bg-orange-100 text-orange-800 text-xs rounded-full">
          {pendingCount}
        </span>
      )}
    </div>
  );
}
