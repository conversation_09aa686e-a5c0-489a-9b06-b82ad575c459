import { Shield, User<PERSON><PERSON><PERSON>, Key, AlertCircle } from 'lucide-react';

interface AnonymousReservationPaymentInfoProps {
  reservation: {
    client_nom: string;
    client_prenom?: string;
    pseudonyme?: string;
    code_acces_anonyme?: string;
    est_anonyme?: boolean;
    montant_total: number;
    numero_reservation: string;
  };
  className?: string;
}

export function AnonymousReservationPaymentInfo({ 
  reservation, 
  className = '' 
}: AnonymousReservationPaymentInfoProps) {
  
  if (!reservation.est_anonyme) {
    return null; // Ne rien afficher pour les réservations normales
  }

  return (
    <div className={`bg-indigo-50 border border-indigo-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <Shield className="h-5 w-5 text-indigo-500 mt-0.5" />
        </div>
        <div className="flex-1">
          <h4 className="text-sm font-medium text-indigo-900 mb-2">
            Réservation Anonyme - Informations de Paiement
          </h4>
          
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-indigo-700 flex items-center">
                <UserCheck className="h-3 w-3 mr-1" />
                Pseudonyme:
              </span>
              <span className="font-medium text-indigo-900">
                {reservation.pseudonyme || reservation.client_nom}
              </span>
            </div>
            
            {reservation.code_acces_anonyme && (
              <div className="flex items-center justify-between">
                <span className="text-indigo-700 flex items-center">
                  <Key className="h-3 w-3 mr-1" />
                  Code d'accès:
                </span>
                <span className="font-mono text-xs bg-indigo-100 px-2 py-1 rounded text-indigo-900">
                  {reservation.code_acces_anonyme}
                </span>
              </div>
            )}
            
            <div className="flex items-center justify-between">
              <span className="text-indigo-700">Numéro de réservation:</span>
              <span className="font-medium text-indigo-900">
                {reservation.numero_reservation}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-indigo-700">Montant total:</span>
              <span className="font-medium text-indigo-900">
                {reservation.montant_total} FCFA
              </span>
            </div>
          </div>
          
          <div className="mt-3 pt-3 border-t border-indigo-200">
            <div className="flex items-start space-x-2">
              <AlertCircle className="h-4 w-4 text-indigo-500 mt-0.5 flex-shrink-0" />
              <div className="text-xs text-indigo-700">
                <p className="font-medium mb-1">Confidentialité:</p>
                <ul className="space-y-1">
                  <li>• Aucune information personnelle n'est stockée</li>
                  <li>• Le ticket de caisse sera généré avec le pseudonyme</li>
                  <li>• Le code d'accès permet au client de consulter sa réservation</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Composant pour afficher un résumé rapide dans la liste
export function AnonymousReservationBadge({ 
  reservation,
  size = 'normal',
  className = '' 
}: { 
  reservation: { est_anonyme?: boolean; pseudonyme?: string; client_nom?: string };
  size?: 'small' | 'normal';
  className?: string;
}) {
  
  if (!reservation.est_anonyme) {
    return null;
  }

  const sizeClasses = size === 'small' 
    ? 'px-1.5 py-0.5 text-xs' 
    : 'px-2 py-1 text-xs';

  return (
    <span className={`inline-flex items-center ${sizeClasses} bg-indigo-100 text-indigo-800 rounded-full font-medium ${className}`}>
      <Shield className={`${size === 'small' ? 'h-2.5 w-2.5' : 'h-3 w-3'} mr-1`} />
      Anonyme
    </span>
  );
}

// Composant pour afficher les instructions de paiement anonyme
export function AnonymousPaymentInstructions({ className = '' }: { className?: string }) {
  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-3 ${className}`}>
      <h5 className="text-sm font-medium text-blue-900 mb-2">
        Instructions pour le paiement anonyme
      </h5>
      <ul className="text-xs text-blue-700 space-y-1">
        <li>• Demander au client son code d'accès pour identifier la réservation</li>
        <li>• Utiliser le pseudonyme pour communiquer avec le client</li>
        <li>• Ne pas demander d'informations personnelles supplémentaires</li>
        <li>• Le ticket de caisse sera généré avec les informations anonymisées</li>
        <li>• Conserver le code d'accès pour référence future si nécessaire</li>
      </ul>
    </div>
  );
}

// Hook pour déterminer si une réservation est anonyme
export function useIsAnonymousReservation(reservation: any) {
  return {
    isAnonymous: reservation?.est_anonyme === true,
    pseudonym: reservation?.pseudonyme || reservation?.client_nom,
    accessCode: reservation?.code_acces_anonyme,
    displayName: reservation?.est_anonyme 
      ? (reservation?.pseudonyme || 'Client Anonyme')
      : `${reservation?.client_prenom || ''} ${reservation?.client_nom || ''}`.trim()
  };
}
