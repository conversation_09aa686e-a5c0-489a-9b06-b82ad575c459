import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'react-toastify';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { reservationService, CalendarEvent } from '../../services/reservation.service';
import { DisponibiliteResponse } from '../../services/disponibilite.service';
import { ReservationDetailsModal } from './ReservationDetailsModal';

interface ReservationManagementProps {
  onClose: () => void;
}

export function ReservationManagement({ onClose }: ReservationManagementProps) {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedView, setSelectedView] = useState<'dayGridMonth' | 'timeGridWeek' | 'timeGridDay'>('timeGridWeek');
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedReservation, setSelectedReservation] = useState<CalendarEvent | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  // Charger les réservations
  const loadReservations = async () => {
    try {
      setLoading(true);
      const response = await reservationService.getCalendrier({
        date_debut: selectedDate.toISOString().split('T')[0],
        date_fin: new Date(selectedDate.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      });
      setEvents(response);
    } catch (error) {
      console.error('Erreur lors du chargement des réservations:', error);
      toast.error('Erreur lors du chargement des réservations');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadReservations();
  }, [selectedDate]);

  // Gérer le clic sur un événement
  const handleEventClick = (info: any) => {
    const event = events.find(e => e.title === info.event.title);
    if (event) {
      setSelectedReservation(event);
      setIsDetailsModalOpen(true);
    }
  };

  // Gérer le changement de vue
  const handleViewChange = (view: 'dayGridMonth' | 'timeGridWeek' | 'timeGridDay') => {
    setSelectedView(view);
  };

  // Valider une réservation
  const handleValidateReservation = async (reservationId: string) => {
    try {
      await reservationService.validateReservation(reservationId);
      await loadReservations(); // Recharger les réservations
      toast.success('Réservation validée avec succès');
    } catch (error) {
      console.error('Erreur lors de la validation:', error);
      toast.error('Erreur lors de la validation de la réservation');
      throw error;
    }
  };

  // Refuser une réservation
  const handleRejectReservation = async (reservationId: string, reason: string) => {
    try {
      await reservationService.rejectReservation(reservationId, reason);
      await loadReservations(); // Recharger les réservations
      toast.success('Réservation refusée');
    } catch (error) {
      console.error('Erreur lors du refus:', error);
      toast.error('Erreur lors du refus de la réservation');
      throw error;
    }
  };

  return (
    <div className="p-6">
      {/* En-tête */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Gestion des Réservations</h1>
        <div className="flex space-x-4">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            Fermer
          </button>
        </div>
      </div>

      {/* Filtres */}
      <div className="mb-6 flex space-x-4">
        <select
          className="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          value={selectedView}
          onChange={(e) => handleViewChange(e.target.value as any)}
        >
          <option value="dayGridMonth">Mois</option>
          <option value="timeGridWeek">Semaine</option>
          <option value="timeGridDay">Jour</option>
        </select>

        <input
          type="date"
          value={selectedDate.toISOString().split('T')[0]}
          onChange={(e) => setSelectedDate(new Date(e.target.value))}
          className="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* Calendrier */}
      <div className="bg-white rounded-lg shadow p-6">
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <FullCalendar
            plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
            initialView={selectedView}
            headerToolbar={{
              left: 'prev,next today',
              center: 'title',
              right: 'dayGridMonth,timeGridWeek,timeGridDay'
            }}
            events={events}
            eventClick={handleEventClick}
            height="auto"
            locale="fr"
            slotMinTime="06:00:00"
            slotMaxTime="22:00:00"
            allDaySlot={false}
            eventTimeFormat={{
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            }}
            eventContent={(eventInfo) => {
              const event = events.find(e => e.title === eventInfo.event.title);
              return (
                <div className={`p-2 rounded ${event?.statut === 'EN_ATTENTE' ? 'bg-yellow-100' : 
                  event?.statut === 'CONFIRMEE' ? 'bg-green-100' : 
                  event?.statut === 'TERMINEE' ? 'bg-gray-100' : 'bg-blue-100'}`}>
                  <div className="font-medium">{eventInfo.event.title}</div>
                  <div className="text-sm">{event?.client?.nom}</div>
                </div>
              );
            }}
          />
        )}
      </div>

      {/* Modal de détails */}
      {isDetailsModalOpen && selectedReservation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <ReservationDetailsModal
            reservation={selectedReservation}
            onClose={() => setIsDetailsModalOpen(false)}
            onValidate={handleValidateReservation}
            onReject={handleRejectReservation}
          />
        </div>
      )}
    </div>
  );
} 