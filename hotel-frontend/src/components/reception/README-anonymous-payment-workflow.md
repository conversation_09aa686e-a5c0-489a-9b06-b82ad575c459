# Workflow de Validation et Paiement des Réservations Anonymes

## Vue d'ensemble

Ce document décrit le processus complet de validation et de paiement des réservations anonymes dans l'interface de réception, depuis la recherche jusqu'à la génération du ticket de caisse.

## Processus de validation et paiement

### 1. Recherche de la réservation anonyme

#### **Interface de recherche**
```
○ Réservation normale    ● Réservation anonyme

[ANON-123456789012]  [Rechercher]

Format du code d'accès: ANON-XXXXXXXXXXXX (4 lettres + tiret + 12 caractères)
```

#### **Étapes**
1. **Sélectionner "Réservation anonyme"** dans les boutons radio
2. **Saisir le code d'accès** fourni par le client (format: ANON-XXXXXXXXXXXX)
3. **Cliquer sur "Rechercher"**
4. **Vérification automatique** du format et recherche dans la base

#### **Résultats possibles**
- ✅ **Réservation trouvée** : Affichage avec badge "Anonyme"
- ❌ **Code invalide** : Message d'erreur explicite
- ❌ **Réservation expirée** : Information sur l'expiration
- ❌ **Réservation non trouvée** : Vérification du code

### 2. Affichage de la réservation anonyme

#### **Informations affichées**
```
┌─────────────────────────────────────────────────────────────┐
│ Client Anonyme          [Recherche] [Anonyme]              │
│                                                             │
│ Arrivée: 15/01/2024 à 14:00     Montant: 100FCFA             │
│ Départ: 16/01/2024 à 12:00                                 │
│                                                             │
│ Type: Réservation anonyme                                   │
│ Code d'accès: ANON-123456789012                            │
│                                                             │
│ Commentaires: Réservation pour 2 personnes                 │
│                                   [Valider] [Refuser]      │
└─────────────────────────────────────────────────────────────┘
```

#### **Éléments spécifiques aux réservations anonymes**
- **Badge "Anonyme"** : Identification visuelle claire
- **Pseudonyme** : Affiché au lieu du nom réel
- **Code d'accès** : Visible pour référence
- **Pas d'informations personnelles** : Email/téléphone masqués

### 3. Validation de la réservation

#### **Processus de validation**
1. **Cliquer sur "Valider"** sur la réservation anonyme
2. **Récupération automatique** des informations de réservation
3. **Ouverture du modal de paiement** avec données pré-remplies
4. **Affichage des informations spécifiques** aux réservations anonymes

#### **Différences avec les réservations normales**
- **Récupération des chambres** : Utilise les données déjà présentes dans la réservation anonyme
- **Validation étendue** : Gère les deux types de réservations
- **Interface adaptée** : Composants spécialisés pour l'anonymat

### 4. Modal de paiement pour réservations anonymes

#### **Sections du modal**

##### **A. Informations de confidentialité**
```
┌─────────────────────────────────────────────────────────────┐
│ 🛡️ Réservation Anonyme - Informations de Paiement         │
│                                                             │
│ 👤 Pseudonyme: Client123                                   │
│ 🔑 Code d'accès: ANON-123456789012                         │
│ 📋 Numéro: RES-2024-001                                    │
│ 💰 Montant: 100FCFA                                           │
│                                                             │
│ Confidentialité:                                            │
│ • Aucune information personnelle stockée                   │
│ • Ticket généré avec pseudonyme                            │
│ • Code d'accès pour consultation future                    │
└─────────────────────────────────────────────────────────────┘
```

##### **B. Détails de la réservation**
```
┌─────────────────────────────────────────────────────────────┐
│ Détails de la réservation                                   │
│                                                             │
│ Client: Client Anonyme                                      │
│ Montant total: 100 FCFA                                     │
│ Période: 15/01/2024 à 14:00 - 16/01/2024 à 12:00          │
└─────────────────────────────────────────────────────────────┘
```

##### **C. Instructions spécifiques**
```
┌─────────────────────────────────────────────────────────────┐
│ Instructions pour le paiement anonyme                       │
│                                                             │
│ • Demander au client son code d'accès                      │
│ • Utiliser le pseudonyme pour communiquer                  │
│ • Ne pas demander d'infos personnelles                     │
│ • Ticket généré avec infos anonymisées                     │
│ • Conserver le code pour référence future                  │
└─────────────────────────────────────────────────────────────┘
```

##### **D. Formulaire de paiement**
- **Montant** : Pré-rempli avec le total de la réservation
- **Mode de paiement** : Espèces, carte, virement, chèque, mobile
- **Référence** : Selon le mode de paiement choisi
- **Paiement partiel** : Option disponible

### 5. Confirmation du paiement

#### **Processus de confirmation**
1. **Validation des données** : Montant, mode de paiement, référence
2. **Détection du type** : Identification automatique de la réservation anonyme
3. **API spécialisée** : Utilisation de l'endpoint adapté aux réservations anonymes
4. **Confirmation** : Message de succès spécifique

#### **Messages de confirmation**
- **Paiement complet** : "Réservation anonyme confirmée et entièrement payée avec succès"
- **Paiement partiel** : "Réservation anonyme confirmée avec paiement partiel enregistré"

### 6. Génération du ticket de caisse

#### **Ticket anonyme**
- **Nom de fichier** : `ticket-anonyme-reservation-{ID}.pdf`
- **Contenu anonymisé** : Pseudonyme au lieu du nom réel
- **QR Code** : Code d'accès pour consultation future
- **Informations** : Montant, date, mode de paiement

#### **Différences avec les tickets normaux**
- **Pseudonyme** : Affiché au lieu du nom/prénom
- **Code d'accès** : Inclus dans le QR code
- **Pas d'infos personnelles** : Email/téléphone omis
- **Mention "Anonyme"** : Indication du type de réservation

## Gestion des erreurs

### **Erreurs spécifiques aux réservations anonymes**

#### **Code d'accès invalide**
- **Cause** : Format incorrect (pas ANON-XXXXXXXXXXXX)
- **Message** : "Format de code d'accès invalide"
- **Action** : Vérifier le format et ressaisir

#### **Réservation expirée**
- **Cause** : Code d'accès expiré selon la configuration
- **Message** : "Cette réservation a expiré"
- **Action** : Contacter l'administration

#### **Réservation non trouvée**
- **Cause** : Code inexistant ou déjà traité
- **Message** : "Réservation anonyme non trouvée ou code invalide"
- **Action** : Vérifier le code avec le client

### **Erreurs de paiement**
- **Validation identique** aux réservations normales
- **Messages adaptés** avec mention "anonyme"
- **Gestion des références** selon le mode de paiement

## Sécurité et confidentialité

### **Protection des données**
- **Aucune donnée personnelle** affichée dans l'interface
- **Code d'accès** comme seul identifiant
- **Pseudonyme** pour la communication
- **Logs anonymisés** pour le suivi

### **Bonnes pratiques pour le personnel**
1. **Utiliser le pseudonyme** pour s'adresser au client
2. **Ne pas demander** d'informations personnelles
3. **Conserver le code d'accès** pour référence
4. **Traiter avec le même professionnalisme** que les réservations normales

## Workflow complet

```
1. Client fournit le code d'accès
   ↓
2. Recherche avec type "Réservation anonyme"
   ↓
3. Affichage avec badge "Anonyme"
   ↓
4. Validation → Modal de paiement spécialisé
   ↓
5. Saisie des informations de paiement
   ↓
6. Confirmation avec API anonyme
   ↓
7. Génération du ticket anonymisé
   ↓
8. Remise du ticket au client
```

## Avantages du système

### **Pour le client**
- **Confidentialité préservée** : Aucune donnée personnelle exposée
- **Service équivalent** : Même qualité que les réservations normales
- **Simplicité** : Un seul code d'accès à retenir

### **Pour le personnel**
- **Interface unifiée** : Même workflow que les réservations normales
- **Identification claire** : Badges visuels pour différencier
- **Instructions intégrées** : Guidance contextuelle

### **Pour l'établissement**
- **Conformité** : Respect de la confidentialité des données
- **Efficacité** : Processus optimisé et automatisé
- **Traçabilité** : Logs et tickets pour le suivi
