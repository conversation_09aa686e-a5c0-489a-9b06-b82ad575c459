import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { authService } from '../services/auth.service';

interface UseComplexeAccessOptions {
  redirectToLogin?: boolean;
  redirectToComplexes?: boolean;
  onAccessDenied?: () => void;
}

interface UseComplexeAccessReturn {
  complexeId: number | undefined;
  hasAccess: boolean;
  needsSelection: boolean;
  isLoading: boolean;
}

/**
 * Hook pour gérer l'accès aux pages nécessitant un complexe
 * Gère automatiquement les redirections selon le type d'utilisateur
 */
export const useComplexeAccess = (
  options: UseComplexeAccessOptions = {}
): UseComplexeAccessReturn => {
  const navigate = useNavigate();
  const {
    redirectToLogin = true,
    redirectToComplexes = true,
    onAccessDenied
  } = options;

  const complexeId = authService.getComplexeId();
  const hasAccess = authService.hasComplexeAccess();
  const needsSelection = authService.needsComplexeSelection();
  const isAuthenticated = authService.isAuthenticated();

  useEffect(() => {
    // Vérification de l'authentification
    if (!isAuthenticated && redirectToLogin) {
      navigate('/login');
      return;
    }

    // Vérification de la sélection de complexe pour admin de chaîne
    if (isAuthenticated && needsSelection && redirectToComplexes) {
      navigate('/patron/complexes');
      return;
    }

    // Callback personnalisé en cas d'accès refusé
    if (isAuthenticated && !hasAccess && !needsSelection && onAccessDenied) {
      onAccessDenied();
    }
  }, [
    isAuthenticated,
    hasAccess,
    needsSelection,
    navigate,
    redirectToLogin,
    redirectToComplexes,
    onAccessDenied
  ]);

  return {
    complexeId,
    hasAccess,
    needsSelection,
    isLoading: false // Peut être étendu pour gérer des états de chargement
  };
};

/**
 * Hook simplifié pour les pages qui nécessitent obligatoirement un complexe
 * Gère automatiquement toutes les redirections
 */
export const useRequireComplexe = (): {
  complexeId: number | undefined;
  isReady: boolean;
} => {
  const { complexeId, hasAccess } = useComplexeAccess();

  return {
    complexeId,
    isReady: hasAccess
  };
};

/**
 * Hook pour les composants qui doivent afficher différents contenus selon l'accès
 */
export const useComplexeStatus = () => {
  const complexeId = authService.getComplexeId();
  const hasAccess = authService.hasComplexeAccess();
  const needsSelection = authService.needsComplexeSelection();
  const isAuthenticated = authService.isAuthenticated();
  const user = authService.getCurrentUser();

  // Nouvelles propriétés du système simplifié
  const isAdmin = authService.isAdmin();
  const isEmployee = authService.isEmployee();
  const employeeType = authService.getEmployeeType();
  const userTypeLabel = authService.getUserTypeLabel();
  const authorizedServices = authService.getAuthorizedServices();

  const getAccessMessage = () => {
    if (!isAuthenticated) {
      return 'Vous devez être connecté pour accéder à cette page.';
    }

    if (needsSelection) {
      return 'Veuillez sélectionner un complexe pour accéder à cette page.';
    }

    if (!hasAccess) {
      return 'Vous n\'avez pas accès à cette fonctionnalité.';
    }

    return '';
  };

  const getAccessAction = () => {
    if (!isAuthenticated) {
      return { text: 'Se connecter', path: '/login' };
    }

    if (needsSelection) {
      return { text: 'Sélectionner un complexe', path: '/patron/complexes' };
    }

    return null;
  };

  const getDefaultRoute = () => {
    if (!hasAccess) return '/login';

    // Redirection selon le type d'utilisateur
    if (isAdmin) {
      return '/dashboard';
    }

    // Pour les employés, redirection selon leur type
    switch (employeeType) {
      case 'reception':
        return '/reception';
      case 'gerant_piscine':
        return '/pool';
      case 'serveuse':
      case 'gerant_services':
        return '/pos';
      case 'cuisine':
        return '/pos?view=kitchen';
      default:
        return '/dashboard';
    }
  };

  return {
    complexeId,
    hasAccess,
    needsSelection,
    isAuthenticated,
    user,
    accessMessage: getAccessMessage(),
    accessAction: getAccessAction(),
    // Nouvelles propriétés
    isAdmin,
    isEmployee,
    employeeType,
    userTypeLabel,
    authorizedServices,
    getDefaultRoute
  };
};
