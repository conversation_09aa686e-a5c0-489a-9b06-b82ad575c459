import { useState, useEffect, useCallback } from 'react';
import { servicePermissionService, ServiceType, UserServicePermissions } from '../services/servicePermission.service';
import { employeePermissionService } from '../services/employeePermission.service';
import { authService } from '../services/auth.service';

interface UseServicePermissionsReturn {
  permissions: UserServicePermissions | null;
  loading: boolean;
  error: string | null;
  canAccessService: (serviceType: ServiceType) => boolean;
  canOperateService: (serviceType: ServiceType) => boolean;
  hasServicePermission: (serviceType: ServiceType, permission: string) => Promise<boolean>;
  accessibleServices: ServiceType[];
  operableServices: ServiceType[];
  refreshPermissions: () => Promise<void>;
  // Nouvelles propriétés simplifiées
  isAdmin: boolean;
  employeeType: string | null;
  authorizedServices: string[];
}

export const useServicePermissions = (): UseServicePermissionsReturn => {
  const [permissions, setPermissions] = useState<UserServicePermissions | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [accessibleServices, setAccessibleServices] = useState<ServiceType[]>([]);
  const [operableServices, setOperableServices] = useState<ServiceType[]>([]);
  const [authorizedServices, setAuthorizedServices] = useState<string[]>([]);

  // Informations utilisateur (calculées directement)
  const isAdmin = authService.isAdmin();
  const employeeType = authService.getEmployeeType();

  const loadPermissions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Vérifier l'authentification
      if (!authService.isAuthenticated()) {
        throw new Error('Utilisateur non authentifié');
      }

      // Charger les permissions avec le nouveau système
      const [userPermissions, accessible, operable, authorized] = await Promise.all([
        servicePermissionService.getUserServicePermissions(),
        servicePermissionService.getAccessibleServices(),
        servicePermissionService.getOperableServices(),
        servicePermissionService.getAuthorizedServices()
      ]);

      setPermissions(userPermissions);
      setAccessibleServices(accessible);
      setOperableServices(operable);
      setAuthorizedServices(authorized);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors du chargement des permissions';
      setError(errorMessage);
      console.error('Error loading service permissions:', err);

      // Réinitialiser les états en cas d'erreur
      setPermissions(null);
      setAccessibleServices([]);
      setOperableServices([]);
      setAuthorizedServices([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadPermissions();
  }, [loadPermissions]);

  const canAccessService = useCallback((serviceType: ServiceType): boolean => {
    // Si admin, accès complet
    if (isAdmin) {
      return true;
    }

    // Vérifier les permissions chargées
    if (!permissions) return false;

    switch (serviceType) {
      case 'Restaurant':
        return permissions.restaurant.canAccess;
      case 'Bar':
        return permissions.bar.canAccess;
      case 'Piscine':
        return permissions.piscine.canAccess;
      default:
        return false;
    }
  }, [permissions, isAdmin]);

  const canOperateService = useCallback((serviceType: ServiceType): boolean => {
    // Si admin, accès complet
    if (isAdmin) {
      return true;
    }

    // Vérifier les permissions chargées
    if (!permissions) return false;

    switch (serviceType) {
      case 'Restaurant':
        return permissions.restaurant.canOperate;
      case 'Bar':
        return permissions.bar.canOperate;
      case 'Piscine':
        return permissions.piscine.canOperate;
      default:
        return false;
    }
  }, [permissions, isAdmin]);

  const hasServicePermission = useCallback(async (serviceType: ServiceType, permission: string): Promise<boolean> => {
    try {
      // Si admin, toutes les permissions
      if (isAdmin) {
        return true;
      }

      // Pour les employés, utiliser le nouveau système
      return await servicePermissionService.hasServicePermission(serviceType, permission);
    } catch (err) {
      console.error(`Error checking permission ${permission} for ${serviceType}:`, err);
      return false;
    }
  }, [isAdmin]);

  const refreshPermissions = useCallback(async () => {
    await loadPermissions();
  }, [loadPermissions]);

  return {
    permissions,
    loading,
    error,
    canAccessService,
    canOperateService,
    hasServicePermission,
    accessibleServices,
    operableServices,
    refreshPermissions,
    // Nouvelles propriétés
    isAdmin,
    employeeType,
    authorizedServices
  };
};

// Hook spécialisé pour vérifier l'accès à un service spécifique
// Version simplifiée utilisant le nouveau système
export const useServiceAccess = (serviceType: ServiceType) => {
  const [canAccess, setCanAccess] = useState<boolean>(false);
  const [canOperate, setCanOperate] = useState<boolean>(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkAccess = async () => {
      try {
        setLoading(true);
        setError(null);

        // Si admin, accès complet
        if (authService.isAdmin()) {
          setCanAccess(true);
          setCanOperate(true);
          return;
        }

        // Pour les employés, vérifier avec le nouveau système
        const [accessResult, operateResult] = await Promise.all([
          servicePermissionService.canAccessService(serviceType),
          servicePermissionService.canOperateService(serviceType)
        ]);

        setCanAccess(accessResult);
        setCanOperate(operateResult);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la vérification des permissions';
        setError(errorMessage);
        console.error(`Error checking access to ${serviceType}:`, err);
        setCanAccess(false);
        setCanOperate(false);
      } finally {
        setLoading(false);
      }
    };

    checkAccess();
  }, [serviceType]);

  return {
    canAccess,
    canOperate,
    loading,
    error
  };
};

// Hook pour les composants qui ont besoin de rediriger selon les permissions
// Version simplifiée utilisant le nouveau système
export const useServiceRedirect = () => {
  const { accessibleServices, loading, isAdmin, employeeType, authorizedServices } = useServicePermissions();

  const getDefaultServiceRoute = useCallback((): string | null => {
    if (loading) {
      return null;
    }

    // Pour les admins, redirection vers le dashboard
    if (isAdmin) {
      return '/dashboard';
    }

    // Pour les employés, redirection selon leur type
    switch (employeeType) {
      case 'reception':
        return '/reception';
      case 'gerant_piscine':
        return '/pool';
      case 'serveuse':
      case 'gerant_services':
        // Rediriger vers le premier service autorisé
        if (authorizedServices.includes('restaurant')) {
          return '/pos?service=restaurant';
        }
        if (authorizedServices.includes('bar')) {
          return '/pos?service=bar';
        }
        break;
      case 'cuisine':
        return '/pos?service=restaurant&view=kitchen';
    }

    // Fallback : essayer les services accessibles
    if (accessibleServices.length > 0) {
      const priorityOrder: ServiceType[] = ['Restaurant', 'Bar', 'Piscine'];

      for (const serviceType of priorityOrder) {
        if (accessibleServices.includes(serviceType)) {
          return `/services/${serviceType.toLowerCase()}`;
        }
      }
    }

    return '/dashboard';
  }, [accessibleServices, loading, isAdmin, employeeType, authorizedServices]);

  const canAccessAnyService = useCallback((): boolean => {
    return accessibleServices.length > 0 || authorizedServices.length > 0;
  }, [accessibleServices, authorizedServices]);

  const getAccessibleServiceRoutes = useCallback((): string[] => {
    const routes: string[] = [];

    // Pour les admins, tous les services
    if (isAdmin) {
      return ['/pos?service=restaurant', '/pos?service=bar', '/pool'];
    }

    // Pour les employés, selon leurs services autorisés
    authorizedServices.forEach(service => {
      switch (service) {
        case 'restaurant':
          routes.push('/pos?service=restaurant');
          break;
        case 'bar':
          routes.push('/pos?service=bar');
          break;
        case 'piscine':
          routes.push('/pool');
          break;
      }
    });

    return routes;
  }, [isAdmin, authorizedServices]);

  return {
    getDefaultServiceRoute,
    canAccessAnyService,
    getAccessibleServiceRoutes,
    accessibleServices,
    authorizedServices,
    loading
  };
};
