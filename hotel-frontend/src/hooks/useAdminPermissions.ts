import { useState, useEffect, useCallback } from 'react';
import { authService } from '../services/auth.service';
import { employeePermissionService } from '../services/employeePermission.service';

// ===== INTERFACES =====

interface UseAdminPermissionsReturn {
  isAdmin: boolean;
  isSuperAdmin: boolean;
  canAccessReports: boolean;
  canAccessConfiguration: boolean;
  canManageEmployees: boolean;
  canManageServices: boolean;
  canAccessInventory: boolean;
  canAccessPOS: boolean;
  userTypeLabel: string;
  fullName: string;
  loading: boolean;
  error: string | null;
  // Méthodes de vérification
  canAccessPage: (page: string) => boolean;
  getAccessiblePages: () => string[];
  refreshPermissions: () => Promise<void>;
}

interface AdminPageAccess {
  dashboard: boolean;
  reports: boolean;
  employeeManagement: boolean;
  services: boolean;
  inventory: boolean;
  posManagement: boolean;
  reception: boolean;
  chambres: boolean;
}

// ===== HOOK PRINCIPAL =====

export const useAdminPermissions = (): UseAdminPermissionsReturn => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pageAccess, setPageAccess] = useState<AdminPageAccess | null>(null);

  // Informations utilisateur (calculées directement)
  const isAdmin = authService.isAdmin();
  const isSuperAdmin = authService.isSuperAdmin();
  const userTypeLabel = authService.getUserTypeLabel();
  const fullName = authService.getFullName();

  // Permissions de base
  const canAccessReports = authService.canAccessReports();
  const canAccessConfiguration = authService.canAccessConfiguration();
  const canManageEmployees = employeePermissionService.canManageEmployees();

  /**
   * Charge les permissions d'accès aux pages
   */
  const loadPermissions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Vérifier l'authentification
      if (!authService.isAuthenticated()) {
        throw new Error('Utilisateur non authentifié');
      }

      // Si admin, accès à toutes les pages
      if (isAdmin) {
        setPageAccess({
          dashboard: true,
          reports: true,
          employeeManagement: true,
          services: true,
          inventory: true,
          posManagement: true,
          reception: true,
          chambres: true
        });
      } else {
        // Pour les employés, accès limité
        setPageAccess({
          dashboard: true, // Dashboard basique pour tous
          reports: false,
          employeeManagement: false,
          services: false,
          inventory: false,
          posManagement: false,
          reception: await employeePermissionService.hasPermission('reception_operations'),
          chambres: await employeePermissionService.hasPermission('reception_operations')
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors du chargement des permissions';
      setError(errorMessage);
      console.error('Error loading admin permissions:', err);
      
      // Réinitialiser en cas d'erreur
      setPageAccess(null);
    } finally {
      setLoading(false);
    }
  }, [isAdmin]);

  /**
   * Effet pour charger les permissions au montage
   */
  useEffect(() => {
    loadPermissions();
  }, [loadPermissions]);

  /**
   * Vérifie si l'utilisateur peut accéder à une page spécifique
   */
  const canAccessPage = useCallback((page: string): boolean => {
    // Si admin, accès à tout
    if (isAdmin) {
      return true;
    }

    // Si pas de permissions chargées, refuser
    if (!pageAccess) {
      return false;
    }

    // Vérifier l'accès à la page spécifique
    return (pageAccess as any)[page] === true;
  }, [pageAccess, isAdmin]);

  /**
   * Récupère la liste des pages accessibles
   */
  const getAccessiblePages = useCallback((): string[] => {
    if (!pageAccess) {
      return [];
    }

    const accessiblePages: string[] = [];
    
    Object.entries(pageAccess).forEach(([page, hasAccess]) => {
      if (hasAccess) {
        accessiblePages.push(page);
      }
    });

    return accessiblePages;
  }, [pageAccess]);

  /**
   * Calcul des permissions dérivées
   */
  const canManageServices = useCallback(async (): Promise<boolean> => {
    if (isAdmin) {
      return true;
    }
    return await employeePermissionService.hasPermission('management_operations');
  }, [isAdmin]);

  const canAccessInventory = useCallback((): boolean => {
    if (isAdmin) {
      return true;
    }
    return canAccessPage('inventory');
  }, [isAdmin, canAccessPage]);

  const canAccessPOS = useCallback((): boolean => {
    if (isAdmin) {
      return true;
    }
    // Les employés peuvent accéder au POS selon leur type
    const employeeType = authService.getEmployeeType();
    return ['serveuse', 'gerant_services', 'cuisine'].includes(employeeType || '');
  }, [isAdmin]);

  /**
   * Fonction de rafraîchissement
   */
  const refreshPermissions = useCallback(async () => {
    await loadPermissions();
  }, [loadPermissions]);

  return {
    isAdmin,
    isSuperAdmin,
    canAccessReports,
    canAccessConfiguration,
    canManageEmployees,
    canManageServices: false, // Sera calculé de manière asynchrone si nécessaire
    canAccessInventory: canAccessInventory(),
    canAccessPOS: canAccessPOS(),
    userTypeLabel,
    fullName,
    loading,
    error,
    canAccessPage,
    getAccessiblePages,
    refreshPermissions
  };
};

// ===== HOOKS SPÉCIALISÉS =====

/**
 * Hook pour vérifier l'accès à une page spécifique
 */
export const usePageAccess = (pageName: string) => {
  const { canAccessPage, loading, isAdmin } = useAdminPermissions();
  
  return {
    canAccess: canAccessPage(pageName),
    loading,
    isAdmin
  };
};

/**
 * Hook pour les composants qui nécessitent des permissions admin
 */
export const useRequireAdmin = () => {
  const { isAdmin, loading, error } = useAdminPermissions();
  
  return {
    isAdmin,
    loading,
    error,
    hasAccess: isAdmin
  };
};

/**
 * Hook pour la navigation admin
 */
export const useAdminNavigation = () => {
  const { 
    isAdmin, 
    canAccessReports, 
    canAccessConfiguration, 
    canManageEmployees,
    getAccessiblePages,
    loading 
  } = useAdminPermissions();

  const getNavigationItems = useCallback(() => {
    if (!isAdmin) {
      return [];
    }

    const items = [
      { name: 'Dashboard', path: '/dashboard', icon: 'home' },
      { name: 'Réception', path: '/reception', icon: 'bed' },
      { name: 'Chambres', path: '/chambres', icon: 'bed-double' },
      { name: 'Services', path: '/services', icon: 'settings' }
    ];

    if (canAccessReports) {
      items.push({ name: 'Rapports', path: '/reports', icon: 'file-text' });
    }

    if (canManageEmployees) {
      items.push({ name: 'Personnel', path: '/employee-management', icon: 'users' });
    }

    if (canAccessConfiguration) {
      items.push({ name: 'Inventaire', path: '/inventaire', icon: 'warehouse' });
      items.push({ name: 'Gestion POS', path: '/pos-management', icon: 'settings' });
    }

    return items;
  }, [isAdmin, canAccessReports, canAccessConfiguration, canManageEmployees]);

  return {
    navigationItems: getNavigationItems(),
    accessiblePages: getAccessiblePages(),
    loading
  };
};
