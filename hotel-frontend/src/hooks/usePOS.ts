import { useEffect, useCallback } from 'react';
import { usePOSStore } from '../stores/posStore';
import type { ServiceComplexe, Table, MenuItem, PaymentData, Commande } from '../services';

/**
 * Hook principal pour la gestion du POS
 */
export const usePOS = () => {
  const store = usePOSStore();

  // Chargement initial des services
  useEffect(() => {
    store.loadServices();
  }, []);

  // Actions principales
  const selectService = useCallback(async (service: ServiceComplexe) => {
    await store.loadWorkspace(service.service_id);
  }, [store]);

  const selectTable = useCallback((table: Table | null) => {
    store.selectTable(table);

    // Si on sélectionne une table avec une commande active, la charger
    if (table?.commande_active) {
      // Convertir la commande simple de table vers le type complet
      const commandeComplete: Commande = {
        ...table.commande_active,
        items: [], // Les items seront chargés séparément
        type_commande: 'Sur place' // Valeur par défaut
      };
      store.setCurrentCommande(commandeComplete);
    }
  }, [store]);

  const addItemToOrder = useCallback((item: MenuItem, quantity: number = 1) => {
    store.addItemToCart(item, quantity);
  }, [store]);

  const createOrder = useCallback(async (type: 'Sur place' | 'À emporter' | 'Livraison' = 'Sur place') => {
    const tableId = store.selectedTable?.table_id;
    return await store.createCommande(tableId, type);
  }, [store]);

  const validateOrder = useCallback(async () => {
    await store.validateCommande();
  }, [store]);

  const processPayment = useCallback(async (paymentData: PaymentData) => {
    await store.processPayment(paymentData);
  }, [store]);

  const toggleFullscreen = useCallback(() => {
    store.setFullscreen(!store.isFullscreen);
  }, [store]);

  // Getters calculés
  const cartTotal = store.cartItems.reduce((total, item) => total + item.montant_ligne, 0);
  const cartItemCount = store.cartItems.reduce((total, item) => total + item.quantite, 0);
  const unreadNotifications = store.workspace?.notifications.filter(n => !n.read).length || 0;

  const canCreateOrder = store.cartItems.length > 0;
  const canValidateOrder = store.currentCommande && store.currentCommande.statut === 'En cours';
  const canProcessPayment = store.currentCommande && store.currentCommande.statut === 'Servie';

  return {
    // État
    workspace: store.workspace,
    selectedTable: store.selectedTable,
    currentCommande: store.currentCommande,
    cartItems: store.cartItems,
    loading: store.loading,
    error: store.error,
    isFullscreen: store.isFullscreen,
    availableServices: store.availableServices,
    activeSessions: store.activeSessions,

    // Valeurs calculées
    cartTotal,
    cartItemCount,
    unreadNotifications,
    canCreateOrder,
    canValidateOrder,
    canProcessPayment,

    // Actions
    selectService,
    selectTable,
    addItemToOrder,
    createOrder,
    validateOrder,
    processPayment,
    toggleFullscreen,
    
    // Actions du store
    updateCartItem: store.updateCartItem,
    removeCartItem: store.removeCartItem,
    clearCart: store.clearCart,
    updateTableStatus: store.updateTableStatus,
    refreshTables: store.refreshTables,
    openSession: store.openSession,
    closeSession: store.closeSession,
    addNotification: store.addNotification,
    removeNotification: store.removeNotification,
    markNotificationAsRead: store.markNotificationAsRead,
    clearNotifications: store.clearNotifications,
    setError: store.setError
  };
};

/**
 * Hook pour la gestion des tables
 */
export const useTables = (serviceId?: number) => {
  const { workspace, refreshTables, updateTableStatus } = usePOS();
  
  const tables = workspace?.tables || [];
  
  // Statistiques des tables
  const tableStats = {
    total: tables.length,
    libre: tables.filter(t => t.statut === 'Libre').length,
    occupee: tables.filter(t => t.statut === 'Occupée').length,
    reservee: tables.filter(t => t.statut === 'Réservée').length,
    maintenance: tables.filter(t => t.statut === 'Maintenance').length
  };

  // Rafraîchissement automatique
  useEffect(() => {
    if (serviceId) {
      const interval = setInterval(refreshTables, 30000); // Toutes les 30 secondes
      return () => clearInterval(interval);
    }
  }, [serviceId, refreshTables]);

  return {
    tables,
    tableStats,
    refreshTables,
    updateTableStatus
  };
};

/**
 * Hook pour la gestion des commandes
 */
export const useCommandes = (_serviceId?: number) => {
  const { workspace } = usePOS();
  
  const activeCommandes = workspace?.activeCommandes || [];
  
  // Statistiques des commandes
  const commandeStats = {
    total: activeCommandes.length,
    enCours: activeCommandes.filter(c => c.statut === 'En cours').length,
    servies: activeCommandes.filter(c => c.statut === 'Servie').length,
    payees: activeCommandes.filter(c => c.statut === 'Payée').length
  };

  return {
    activeCommandes,
    commandeStats
  };
};

/**
 * Hook pour la gestion des sessions
 */
export const useSessions = () => {
  const { activeSessions, openSession, closeSession } = usePOS();

  const hasActiveSession = (serviceId: number) => {
    return !!activeSessions[serviceId];
  };

  const getActiveSession = (serviceId: number) => {
    return activeSessions[serviceId] || null;
  };

  return {
    activeSessions,
    hasActiveSession,
    getActiveSession,
    openSession,
    closeSession
  };
};

/**
 * Hook pour la gestion des notifications
 */
export const useNotifications = () => {
  const { workspace, addNotification, removeNotification, markNotificationAsRead, clearNotifications } = usePOS();
  
  const notifications = workspace?.notifications || [];
  const unreadCount = notifications.filter(n => !n.read).length;

  const markAllAsRead = useCallback(() => {
    notifications.forEach(n => {
      if (!n.read) {
        markNotificationAsRead(n.id);
      }
    });
  }, [notifications, markNotificationAsRead]);

  return {
    notifications,
    unreadCount,
    addNotification,
    removeNotification,
    markNotificationAsRead,
    markAllAsRead,
    clearNotifications
  };
};

/**
 * Hook pour les raccourcis clavier
 */
export const usePOSKeyboardShortcuts = () => {
  const { 
    createOrder, 
    validateOrder, 
    processPayment, 
    clearCart,
    toggleFullscreen 
  } = usePOS();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ignorer si on est dans un input
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      // Ctrl/Cmd + raccourcis
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'n': // Nouvelle commande
            event.preventDefault();
            createOrder();
            break;
          case 'v': // Valider commande
            event.preventDefault();
            validateOrder();
            break;
          case 'p': // Paiement
            event.preventDefault();
            // Ouvrir modal de paiement
            break;
          case 'Delete': // Vider panier
            event.preventDefault();
            clearCart();
            break;
          case 'f': // Plein écran
            event.preventDefault();
            toggleFullscreen();
            break;
        }
      }

      // Touches simples
      switch (event.key) {
        case 'Escape':
          // Fermer modals ou réinitialiser sélection
          break;
        case 'F11':
          event.preventDefault();
          toggleFullscreen();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [createOrder, validateOrder, processPayment, clearCart, toggleFullscreen]);
};

/**
 * Hook pour la persistance locale
 */
export const usePOSPersistence = () => {
  const { cartItems, selectedTable } = usePOS();

  // Sauvegarde automatique du panier
  useEffect(() => {
    localStorage.setItem('pos-cart', JSON.stringify(cartItems));
  }, [cartItems]);

  // Sauvegarde de la table sélectionnée
  useEffect(() => {
    if (selectedTable) {
      localStorage.setItem('pos-selected-table', JSON.stringify(selectedTable));
    } else {
      localStorage.removeItem('pos-selected-table');
    }
  }, [selectedTable]);
};
