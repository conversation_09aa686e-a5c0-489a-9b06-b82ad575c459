import { useState, useEffect, useCallback } from 'react';
import { 
  employeePermissionService, 
  EmployeePermissions, 
  ServiceAccess, 
  EmployeeTypeInfo 
} from '../services/employeePermission.service';
import { authService } from '../services/auth.service';

// ===== INTERFACES =====

interface UseEmployeePermissionsReturn {
  permissions: EmployeePermissions | null;
  serviceAccess: ServiceAccess | null;
  loading: boolean;
  error: string | null;
  canAccess: (feature: string) => boolean;
  canAccessService: (service: string) => boolean;
  isAdmin: boolean;
  isEmployee: boolean;
  employeeType: string | null;
  employeeTypeInfo: EmployeeTypeInfo | null;
  userTypeLabel: string;
  fullName: string;
  authorizedServices: string[];
  refreshPermissions: () => Promise<void>;
  // Méthodes de vérification spécifiques
  canManageEmployees: boolean;
  canViewReports: boolean;
  canConfigureSystem: boolean;
  canManageServices: boolean;
}

// ===== HOOK PRINCIPAL =====

export const useEmployeePermissions = (): UseEmployeePermissionsReturn => {
  const [permissions, setPermissions] = useState<EmployeePermissions | null>(null);
  const [serviceAccess, setServiceAccess] = useState<ServiceAccess | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [authorizedServices, setAuthorizedServices] = useState<string[]>([]);

  // Informations utilisateur (calculées directement)
  const isAdmin = authService.isAdmin();
  const isEmployee = authService.isEmployee();
  const employeeType = authService.getEmployeeType();
  const employeeTypeInfo = employeePermissionService.getEmployeeTypeInfo();
  const userTypeLabel = authService.getUserTypeLabel();
  const fullName = authService.getFullName();

  // Permissions calculées
  const canManageEmployees = employeePermissionService.canManageEmployees();
  const canViewReports = employeePermissionService.canViewReports();
  const canConfigureSystem = employeePermissionService.canConfigureSystem();

  /**
   * Charge les permissions et accès aux services
   */
  const loadPermissions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Vérifier l'authentification
      if (!authService.isAuthenticated()) {
        throw new Error('Utilisateur non authentifié');
      }

      // Charger les permissions et accès en parallèle
      const [userPermissions, userServiceAccess, accessibleServices] = await Promise.all([
        employeePermissionService.getEmployeePermissions(),
        employeePermissionService.getServiceAccess(),
        employeePermissionService.getAccessibleServices()
      ]);

      setPermissions(userPermissions);
      setServiceAccess(userServiceAccess);
      setAuthorizedServices(accessibleServices);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors du chargement des permissions';
      setError(errorMessage);
      console.error('Error loading employee permissions:', err);
      
      // Réinitialiser les états en cas d'erreur
      setPermissions(null);
      setServiceAccess(null);
      setAuthorizedServices([]);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Effet pour charger les permissions au montage
   */
  useEffect(() => {
    loadPermissions();
  }, [loadPermissions]);

  /**
   * Vérifie si l'utilisateur peut accéder à une fonctionnalité
   */
  const canAccess = useCallback((feature: string): boolean => {
    // Si admin, accès à tout
    if (isAdmin) {
      return true;
    }

    // Si pas de permissions chargées, refuser
    if (!permissions) {
      return false;
    }

    // Vérifier la permission spécifique
    return (permissions as any)[feature] === true;
  }, [permissions, isAdmin]);

  /**
   * Vérifie si l'utilisateur peut accéder à un service
   */
  const canAccessService = useCallback((service: string): boolean => {
    // Si admin, accès à tout
    if (isAdmin) {
      return true;
    }

    // Si pas d'accès chargé, refuser
    if (!serviceAccess) {
      return false;
    }

    // Normaliser le nom du service
    const normalizedService = service.toLowerCase();
    
    // Vérifier l'accès au service
    return (serviceAccess as any)[normalizedService] === true;
  }, [serviceAccess, isAdmin]);

  /**
   * Calcul de canManageServices basé sur les permissions
   */
  const canManageServices = useCallback((): boolean => {
    if (isAdmin) {
      return true;
    }
    return canAccess('management_operations');
  }, [isAdmin, canAccess]);

  /**
   * Fonction de rafraîchissement
   */
  const refreshPermissions = useCallback(async () => {
    await loadPermissions();
  }, [loadPermissions]);

  return {
    permissions,
    serviceAccess,
    loading,
    error,
    canAccess,
    canAccessService,
    isAdmin,
    isEmployee,
    employeeType,
    employeeTypeInfo,
    userTypeLabel,
    fullName,
    authorizedServices,
    refreshPermissions,
    // Permissions calculées
    canManageEmployees,
    canViewReports,
    canConfigureSystem,
    canManageServices: canManageServices()
  };
};

// ===== HOOKS SPÉCIALISÉS =====

/**
 * Hook spécialisé pour vérifier l'accès à un service spécifique
 */
export const useServiceAccess = (serviceType: string) => {
  const [canAccess, setCanAccess] = useState<boolean>(false);
  const [canOperate, setCanOperate] = useState<boolean>(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkAccess = async () => {
      try {
        setLoading(true);
        setError(null);

        // Si admin, accès complet
        if (authService.isAdmin()) {
          setCanAccess(true);
          setCanOperate(true);
          return;
        }

        // Pour les employés, vérifier les permissions
        const [accessResult, operateResult] = await Promise.all([
          employeePermissionService.canAccessService(serviceType),
          employeePermissionService.canAccessService(serviceType) // Pour l'instant, même logique
        ]);

        setCanAccess(accessResult);
        setCanOperate(operateResult);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Erreur lors de la vérification des permissions';
        setError(errorMessage);
        console.error(`Error checking access to ${serviceType}:`, err);
        setCanAccess(false);
        setCanOperate(false);
      } finally {
        setLoading(false);
      }
    };

    checkAccess();
  }, [serviceType]);

  return {
    canAccess,
    canOperate,
    loading,
    error
  };
};

/**
 * Hook pour les composants qui ont besoin de rediriger selon les permissions
 */
export const useEmployeeRedirect = () => {
  const { authorizedServices, loading, isAdmin, employeeType } = useEmployeePermissions();

  const getDefaultRoute = useCallback((): string | null => {
    if (loading) {
      return null;
    }

    // Pour les admins, rediriger vers le dashboard admin
    if (isAdmin) {
      return '/dashboard';
    }

    // Pour les employés, rediriger selon leur type
    switch (employeeType) {
      case 'reception':
        return '/reception';
      case 'gerant_piscine':
        return '/pool';
      case 'serveuse':
      case 'gerant_services':
        // Rediriger vers le premier service autorisé
        if (authorizedServices.includes('restaurant')) {
          return '/pos?service=restaurant';
        }
        if (authorizedServices.includes('bar')) {
          return '/pos?service=bar';
        }
        return '/dashboard';
      case 'cuisine':
        return '/pos?service=restaurant&view=kitchen';
      default:
        return '/dashboard';
    }
  }, [authorizedServices, loading, isAdmin, employeeType]);

  const canAccessAnyService = useCallback((): boolean => {
    return authorizedServices.length > 0;
  }, [authorizedServices]);

  const getAccessibleRoutes = useCallback((): string[] => {
    const routes: string[] = [];

    if (isAdmin) {
      // Admins ont accès à toutes les routes
      return [
        '/dashboard',
        '/reception',
        '/chambres',
        '/services',
        '/pos',
        '/pool',
        '/employee-management',
        '/reports',
        '/inventaire'
      ];
    }

    // Pour les employés, routes selon leur type
    switch (employeeType) {
      case 'reception':
        routes.push('/reception', '/chambres', '/client-reservations');
        break;
      case 'gerant_piscine':
        routes.push('/pool');
        break;
      case 'serveuse':
      case 'gerant_services':
        routes.push('/pos');
        break;
      case 'cuisine':
        routes.push('/pos?view=kitchen');
        break;
    }

    return routes;
  }, [isAdmin, employeeType]);

  return {
    getDefaultRoute,
    canAccessAnyService,
    getAccessibleRoutes,
    authorizedServices,
    loading
  };
};
