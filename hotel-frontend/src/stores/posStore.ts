import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { 
  ServiceComplexe, 
  Table, 
  MenuCategory, 
  MenuItem,
  Commande, 
  CommandeItem, 
  SessionCaisse,
  PaymentData
} from '../services';
import {
  tableService,
  menuService,
  commandeService,
  sessionCaisseService,
  serviceComplexeService,
  posService
} from '../services';

// Types étendus pour le POS
export interface POSNotification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  read?: boolean;
}

export interface ServiceWithPOSConfig extends ServiceComplexe {
  pos_configuration?: {
    table_management: boolean;
    kitchen_display: boolean;
    auto_print_kitchen: boolean;
    split_bills: boolean;
    table_service: boolean;
    takeaway: boolean;
    delivery: boolean;
  };
  active_session?: SessionCaisse;
  tables_count?: number;
  current_orders?: number;
}

export interface POSWorkspace {
  service: ServiceWithPOSConfig;
  tables: Table[];
  menu: MenuCategory[];
  activeCommandes: Commande[];
  session: SessionCaisse | null;
  notifications: POSNotification[];
}

interface POSStore {
  // État principal
  workspace: POSWorkspace | null;
  selectedTable: Table | null;
  currentCommande: Commande | null;
  cartItems: CommandeItem[];
  
  // États UI
  loading: boolean;
  error: string | null;
  isFullscreen: boolean;
  
  // Services disponibles
  availableServices: ServiceComplexe[];
  activeSessions: Record<number, SessionCaisse>;
  
  // Actions principales
  setWorkspace: (workspace: POSWorkspace) => void;
  selectTable: (table: Table | null) => void;
  setCurrentCommande: (commande: Commande | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setFullscreen: (fullscreen: boolean) => void;
  
  // Gestion du panier
  addItemToCart: (item: MenuItem, quantity: number) => void;
  updateCartItem: (itemId: number, quantity: number) => void;
  removeCartItem: (itemId: number) => void;
  clearCart: () => void;
  
  // Actions métier
  loadServices: () => Promise<void>;
  loadWorkspace: (serviceId: number) => Promise<void>;
  createCommande: (tableId?: number, type?: string) => Promise<Commande>;
  validateCommande: () => Promise<void>;
  processPayment: (paymentData: PaymentData) => Promise<void>;
  
  // Gestion des tables
  updateTableStatus: (tableId: number, statut: string) => Promise<void>;
  refreshTables: () => Promise<void>;
  
  // Gestion des sessions
  openSession: (serviceId: number, fondsOuverture: number, notes?: string) => Promise<void>;
  closeSession: (sessionId: number, fondsClôture: number, notes?: string) => Promise<void>;
  
  // Notifications
  addNotification: (notification: Omit<POSNotification, 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  markNotificationAsRead: (id: string) => void;
  clearNotifications: () => void;
}

export const usePOSStore = create<POSStore>()(
  devtools(
    persist(
      (set, get) => ({
        // État initial
        workspace: null,
        selectedTable: null,
        currentCommande: null,
        cartItems: [],
        loading: false,
        error: null,
        isFullscreen: false,
        availableServices: [],
        activeSessions: {},

        // Actions de base
        setWorkspace: (workspace) => set({ workspace }),
        selectTable: (table) => set({ selectedTable: table }),
        setCurrentCommande: (commande) => set({ currentCommande: commande }),
        setLoading: (loading) => set({ loading }),
        setError: (error) => set({ error }),
        setFullscreen: (fullscreen) => set({ isFullscreen: fullscreen }),

        // Gestion du panier
        addItemToCart: (item, quantity) => {
          const { cartItems } = get();
          const existingItem = cartItems.find(ci => ci.produit_id === item.produit_id);

          if (existingItem) {
            set({
              cartItems: cartItems.map(ci =>
                ci.produit_id === item.produit_id
                  ? { 
                      ...ci, 
                      quantite: ci.quantite + quantity,
                      montant_ligne: (ci.quantite + quantity) * ci.prix_unitaire
                    }
                  : ci
              )
            });
          } else {
            set({
              cartItems: [...cartItems, {
                produit_id: item.produit_id,
                nom: item.nom,
                quantite: quantity,
                prix_unitaire: item.prix_vente,
                montant_ligne: item.prix_vente * quantity,
                statut: 'En attente'
              }]
            });
          }
        },

        updateCartItem: (itemId, quantity) => {
          const { cartItems } = get();
          if (quantity <= 0) {
            set({
              cartItems: cartItems.filter(item => item.produit_id !== itemId)
            });
          } else {
            set({
              cartItems: cartItems.map(item =>
                item.produit_id === itemId
                  ? { 
                      ...item, 
                      quantite: quantity,
                      montant_ligne: quantity * item.prix_unitaire
                    }
                  : item
              )
            });
          }
        },

        removeCartItem: (itemId) => {
          const { cartItems } = get();
          set({
            cartItems: cartItems.filter(item => item.produit_id !== itemId)
          });
        },

        clearCart: () => set({ cartItems: [] }),

        // Actions métier
        loadServices: async () => {
          try {
            set({ loading: true, error: null });
            
            const [restaurants, bars] = await Promise.all([
              serviceComplexeService.getServicesByType('Restaurant'),
              serviceComplexeService.getServicesByType('Bar')
            ]);
            
            const services = [...restaurants, ...bars];
            set({ availableServices: services });
            
          } catch (error: any) {
            set({ error: error.message });
          } finally {
            set({ loading: false });
          }
        },

        loadWorkspace: async (serviceId) => {
          try {
            set({ loading: true, error: null });
            
            const [service, tables, menu, activeCommandes] = await Promise.all([
              serviceComplexeService.getServiceById(serviceId),
              tableService.getTablesByService(serviceId),
              menuService.getMenuByService(serviceId),
              commandeService.getCommandesActives(serviceId)
            ]);

            // Récupérer la session active en utilisant les méthodes existantes
            let session = null;
            try {
              // 1. Récupérer les POS du service
              const posList = await posService.getPOSByService(serviceId);

              // 2. Si il y a des POS, chercher une session active
              if (posList.length > 0) {
                // Prendre le premier POS actif avec une caisse ouverte
                const activePOS = posList.find(pos => pos.statut === 'Actif' && pos.caisse_ouverte);

                if (activePOS) {
                  // 3. Récupérer la session active de ce POS
                  session = await sessionCaisseService.getActiveSession(activePOS.pos_id);
                }
              }
            } catch (error) {
              // Si aucune session active n'est trouvée, session reste null
              console.log('Aucune session active trouvée pour ce service');
            }

            const workspace: POSWorkspace = {
              service: service as ServiceWithPOSConfig,
              tables,
              menu,
              activeCommandes,
              session,
              notifications: []
            };

            set({ workspace });
            
          } catch (error: any) {
            set({ error: error.message });
          } finally {
            set({ loading: false });
          }
        },

        createCommande: async (tableId, type = 'Sur place') => {
          try {
            const { workspace, cartItems } = get();
            if (!workspace || cartItems.length === 0) {
              throw new Error('Aucun article dans le panier');
            }

            const commandeData = {
              service_id: workspace.service.service_id,
              table_id: tableId,
              items: cartItems.map(item => ({
                produit_id: item.produit_id,
                nom: item.nom,
                quantite: item.quantite,
                prix_unitaire: item.prix_unitaire,
                montant_ligne: item.montant_ligne
              })),
              type_commande: type as any
            };

            const commande = await commandeService.createCommande(commandeData);
            
            set({ 
              currentCommande: commande,
              cartItems: []
            });

            get().addNotification({
              type: 'success',
              title: 'Commande créée',
              message: `Commande #${commande.commande_id} créée avec succès`
            });

            return commande;
            
          } catch (error: any) {
            get().addNotification({
              type: 'error',
              title: 'Erreur',
              message: error.message
            });
            throw error;
          }
        },

        validateCommande: async () => {
          try {
            const { currentCommande } = get();
            if (!currentCommande) {
              throw new Error('Aucune commande sélectionnée');
            }

            await commandeService.validateCommande(currentCommande.commande_id);
            
            set({
              currentCommande: {
                ...currentCommande,
                statut: 'En cours'
              }
            });

            get().addNotification({
              type: 'success',
              title: 'Commande validée',
              message: 'La commande a été envoyée en cuisine'
            });
            
          } catch (error: any) {
            get().addNotification({
              type: 'error',
              title: 'Erreur',
              message: error.message
            });
            throw error;
          }
        },

        processPayment: async (paymentData) => {
          try {
            const { currentCommande } = get();
            if (!currentCommande) {
              throw new Error('Aucune commande sélectionnée');
            }

            await commandeService.processPayment(currentCommande.commande_id, paymentData);
            
            set({
              currentCommande: {
                ...currentCommande,
                statut: 'Payée'
              }
            });

            get().addNotification({
              type: 'success',
              title: 'Paiement effectué',
              message: `Paiement de ${paymentData.montant}FCFA traité avec succès`
            });
            
          } catch (error: any) {
            get().addNotification({
              type: 'error',
              title: 'Erreur de paiement',
              message: error.message
            });
            throw error;
          }
        },

        updateTableStatus: async (tableId, statut) => {
          try {
            await tableService.updateTableStatus(tableId, statut);
            
            const { workspace } = get();
            if (workspace) {
              const updatedTables = workspace.tables.map(table =>
                table.table_id === tableId ? { ...table, statut: statut as any } : table
              );
              
              set({
                workspace: {
                  ...workspace,
                  tables: updatedTables
                }
              });
            }
            
          } catch (error: any) {
            get().addNotification({
              type: 'error',
              title: 'Erreur',
              message: error.message
            });
            throw error;
          }
        },

        refreshTables: async () => {
          try {
            const { workspace } = get();
            if (!workspace) return;

            const tables = await tableService.getTablesByService(workspace.service.service_id);
            
            set({
              workspace: {
                ...workspace,
                tables
              }
            });
            
          } catch (error: any) {
            get().addNotification({
              type: 'error',
              title: 'Erreur',
              message: error.message
            });
          }
        },

        openSession: async (serviceId, fondsOuverture, notes) => {
          try {
            const session = await sessionCaisseService.ouvrirSessionPourService(
              serviceId, 
              fondsOuverture, 
              notes
            );
            
            const { activeSessions } = get();
            set({
              activeSessions: {
                ...activeSessions,
                [serviceId]: session
              }
            });

            get().addNotification({
              type: 'success',
              title: 'Session ouverte',
              message: `Session ouverte avec ${fondsOuverture}FCFA de fonds`
            });
            
          } catch (error: any) {
            get().addNotification({
              type: 'error',
              title: 'Erreur',
              message: error.message
            });
            throw error;
          }
        },

        closeSession: async (sessionId, fondsClôture, notes) => {
          try {
            await sessionCaisseService.fermerSession(sessionId, {
              fonds_fermeture: fondsClôture,
              notes
            });
            
            const { activeSessions } = get();
            const updatedSessions = { ...activeSessions };
            
            // Trouver et supprimer la session fermée
            Object.keys(updatedSessions).forEach(serviceId => {
              if (updatedSessions[parseInt(serviceId)].session_id === sessionId) {
                delete updatedSessions[parseInt(serviceId)];
              }
            });
            
            set({ activeSessions: updatedSessions });

            get().addNotification({
              type: 'success',
              title: 'Session fermée',
              message: 'La session a été fermée avec succès'
            });
            
          } catch (error: any) {
            get().addNotification({
              type: 'error',
              title: 'Erreur',
              message: error.message
            });
            throw error;
          }
        },

        // Gestion des notifications
        addNotification: (notification) => {
          const { workspace } = get();
          if (!workspace) return;

          const newNotification: POSNotification = {
            ...notification,
            id: Date.now().toString(),
            timestamp: new Date(),
            read: false
          };

          set({
            workspace: {
              ...workspace,
              notifications: [newNotification, ...workspace.notifications]
            }
          });
        },

        removeNotification: (id) => {
          const { workspace } = get();
          if (!workspace) return;

          set({
            workspace: {
              ...workspace,
              notifications: workspace.notifications.filter(n => n.id !== id)
            }
          });
        },

        markNotificationAsRead: (id) => {
          const { workspace } = get();
          if (!workspace) return;

          set({
            workspace: {
              ...workspace,
              notifications: workspace.notifications.map(n =>
                n.id === id ? { ...n, read: true } : n
              )
            }
          });
        },

        clearNotifications: () => {
          const { workspace } = get();
          if (!workspace) return;

          set({
            workspace: {
              ...workspace,
              notifications: []
            }
          });
        }
      }),
      { 
        name: 'pos-store',
        partialize: (state) => ({
          selectedTable: state.selectedTable,
          cartItems: state.cartItems,
          isFullscreen: state.isFullscreen
        })
      }
    )
  )
);
