# Plan de Simplification du Système de Permissions

## 📋 Analyse de l'Existant

### Structure Actuelle
- **4 types d'utilisateurs** : `super_admin`, `admin_chaine`, `admin_complexe`, `employe`
- **Tables principales** : `UtilisateursSuperAdmin`, `<PERSON><PERSON><PERSON><PERSON><PERSON>`, `Ad<PERSON>Complexe`, `Employes`, `RolesComplexe`
- **Système de permissions complexe** avec plus de 50 permissions granulaires
- **Middlewares existants** : `auth.js`, `permission.js`, `serviceAccess.js`

### Problèmes Identifiés
1. **Complexité excessive** : Trop de permissions granulaires (manage_complexes, view_complexes, create_complex, etc.)
2. **Redondance** : Beaucoup de permissions qui se chevauchent
3. **Maintenance difficile** : Système trop détaillé pour les besoins réels
4. **Confusion des rôles employés** : Pas de distinction claire entre les types d'employés

## 🎯 Objectif de Simplification

### Nouveau Système (4 Types + Sous-types Employés)
1. **Super Admin** : Accès complet à tout
2. **Admin Chaîne** : Accès complet à leur chaîne
3. **Admin Complexe** : Accès complet à leur complexe
4. **Employés** avec 5 sous-types spécialisés :
   - **Réception** : Chambres, clients, réservations
   - **Gérant Piscine** : Tout ce qui concerne la piscine
   - **Serveuse** : Entrée des commandes (bar et/ou restaurant)
   - **Gérant Services** : Validation commandes et paiements (bar et/ou restaurant)
   - **Cuisine** : Voir les commandes restaurant envoyées par le gérant

## 🔧 Plan d'Implémentation Backend

### Phase 1 : Modification du Schéma de Base de Données

#### 1.1 Ajout du champ `type_employe` dans la table `Employes`
```sql
ALTER TABLE "Employes"
ADD COLUMN "type_employe" varchar;

-- Valeurs possibles : 'reception', 'gerant_piscine', 'serveuse', 'gerant_services', 'cuisine'
```

#### 1.2 Ajout du champ `services_autorises` pour la flexibilité
```sql
-- Le champ services_autorises existe déjà mais on va l'utiliser différemment
-- Il contiendra un array JSON des services autorisés : ['bar', 'restaurant', 'piscine']
-- Exemple pour une serveuse bar+restaurant : ["bar", "restaurant"]
-- Exemple pour un gérant restaurant uniquement : ["restaurant"]
```

#### 1.3 Création de rôles prédéfinis par type d'employé
- Créer des rôles standards pour chaque type d'employé
- Ces rôles seront automatiquement assignés lors de la création d'un employé

### Phase 2 : Simplification des Permissions

#### 2.0 Séparation claire Admin vs Employés

**🔧 ADMINS (Super Admin, Admin Chaîne, Admin Complexe) :**
- ✅ Accès complet à leur périmètre
- ✅ Rapports et statistiques
- ✅ Configuration système
- ✅ Gestion des employés et rôles
- ✅ Paramètres des services
- ✅ Inventaire et stock
- ✅ Tarification et prix

**👥 EMPLOYÉS (Tous types) :**
- ✅ Opérations quotidiennes de leur service
- ✅ Interface POS/caisse de leur service
- ✅ Gestion des commandes/réservations
- ❌ **PAS d'accès aux rapports**
- ❌ **PAS d'accès à la configuration**
- ❌ **PAS de gestion d'autres employés**
- ❌ **PAS de modification des tarifs**
- ❌ **PAS d'accès aux statistiques globales**

#### 2.1 Nouvelle liste de permissions simplifiées
```javascript
const SIMPLIFIED_PERMISSIONS = {
  // Permissions administratives (Super Admin, Admin Chaîne, Admin Complexe)
  'full_access': 'Accès complet au système (super admin)',
  'chain_access': 'Accès complet à la chaîne (admin chaîne)',
  'complex_access': 'Accès complet au complexe (admin complexe)',

  // Permissions opérationnelles employés (PAS d'accès config/rapports)
  'reception_operations': 'Opérations réception (chambres, clients, réservations)',
  'piscine_operations': 'Opérations piscine (billetterie, accès)',
  'service_operations': 'Opérations service (commandes bar/restaurant)',
  'management_operations': 'Opérations gestion (validation, paiements)',
  'kitchen_operations': 'Opérations cuisine (voir commandes restaurant)'
};
```

#### 2.2 Mapping des permissions par type d'utilisateur
```javascript
const USER_TYPE_PERMISSIONS = {
  // Admins : Accès complet incluant rapports, configuration, gestion
  'super_admin': ['full_access'],
  'admin_chaine': ['chain_access'],
  'admin_complexe': ['complex_access'],

  // Employés : Accès opérationnel uniquement (PAS de rapports/config)
  'employe': {
    'reception': ['reception_operations'],
    'gerant_piscine': ['piscine_operations'],
    'serveuse': ['service_operations'],
    'gerant_services': ['service_operations', 'management_operations'],
    'cuisine': ['kitchen_operations']
  }
};
```

#### 2.3 Détail des accès par type d'employé

**👩‍💼 RÉCEPTION (`reception_operations`) :**
- ✅ Voir/créer/modifier réservations
- ✅ Gérer les clients (check-in/check-out)
- ✅ Voir disponibilités chambres
- ✅ Encaisser paiements réservations
- ❌ Voir rapports financiers
- ❌ Modifier tarifs chambres

**🏊‍♂️ GÉRANT PISCINE (`piscine_operations`) :**
- ✅ Interface billetterie piscine
- ✅ Vendre tickets d'accès
- ✅ Gérer les entrées/sorties
- ✅ Encaisser paiements piscine
- ❌ Voir rapports de revenus
- ❌ Modifier tarifs piscine

**👩‍🍳 SERVEUSE (`service_operations`) :**
- ✅ Interface POS bar/restaurant
- ✅ Prendre commandes
- ✅ Voir menu/carte
- ❌ Valider paiements
- ❌ Voir rapports ventes
- ❌ Modifier prix menu

**👨‍💼 GÉRANT SERVICES (`service_operations` + `management_operations`) :**
- ✅ Tout ce que fait la serveuse
- ✅ Valider commandes
- ✅ Encaisser paiements
- ✅ Gérer les tables
- ❌ Voir rapports financiers globaux
- ❌ Modifier configuration POS

**👨‍🍳 CUISINE (`kitchen_operations`) :**
- ✅ Voir commandes restaurant
- ✅ Marquer commandes prêtes
- ✅ Gérer file d'attente cuisine
- ❌ Voir autres services
- ❌ Accès aux ventes/paiements

### Phase 3 : Refactoring des Services

#### 3.1 Modification du `PermissionService`
- Simplifier la méthode `getUserPermissions()`
- Remplacer la logique complexe par le mapping simple
- Supprimer les permissions granulaires obsolètes

#### 3.2 Modification du `EmployeeService`
- Ajouter la gestion du `type_employe`
- Créer une méthode pour assigner automatiquement les permissions selon le type
- Modifier les méthodes de création et mise à jour d'employés

#### 3.3 Nouveau service `EmployeeTypeService`
- Gérer les types d'employés et leurs permissions
- Fournir les méthodes pour valider les types
- Gérer les transitions entre types d'employés

### Phase 4 : Refactoring des Middlewares

#### 4.1 Simplification du `permission.middleware.js`
- Remplacer `checkPermission()` par une version simplifiée
- Ajouter `checkEmployeeType()` pour vérifier les types d'employés
- Créer `checkServiceAccess()` simplifié

#### 4.2 Nouveau middleware `employeeType.middleware.js`
```javascript
const checkEmployeeType = (allowedTypes) => (req, res, next) => {
  // Vérifier si l'employé a le bon type pour accéder à la ressource
};

const checkServiceEmployeeAccess = (serviceTypes) => (req, res, next) => {
  // Vérifier l'accès aux services selon le type d'employé
  // serviceTypes peut être ['bar', 'restaurant'] pour un gérant multi-services
};
```

### Phase 5 : Refactoring des Contrôleurs

#### 5.1 Modification du `EmployeeController`
- Ajouter la gestion du `type_employe` dans `createEmployee()`
- Modifier `updateEmployee()` pour gérer les changements de type
- Ajouter des méthodes pour lister les employés par type

#### 5.2 Nouveau `EmployeeTypeController`
- Gérer les opérations spécifiques aux types d'employés
- Fournir les endpoints pour les types disponibles
- Gérer les permissions par type

### Phase 6 : Refactoring des Routes

#### 6.1 Simplification des routes existantes
- Remplacer les middlewares de permissions complexes
- Utiliser les nouveaux middlewares simplifiés
- Grouper les routes par type d'accès

#### 6.2 Nouvelles routes pour les types d'employés
```javascript
// Routes spécifiques aux types d'employés
router.get('/employee-types', EmployeeTypeController.getAvailableTypes);
router.get('/employee-types/:type/permissions', EmployeeTypeController.getTypePermissions);
router.post('/employees/:id/change-type', EmployeeTypeController.changeEmployeeType);
```

### Phase 7 : Migration des Données

#### 7.1 Script de migration
- Analyser les employés existants et leurs rôles actuels
- Mapper vers les nouveaux types d'employés
- Migrer les permissions existantes

#### 7.2 Nettoyage des données obsolètes
- Supprimer les permissions granulaires non utilisées
- Nettoyer les rôles complexes obsolètes
- Archiver les anciennes configurations

## 📁 Structure des Fichiers à Modifier/Créer

### Fichiers à Modifier
1. `backend/schema/schema.sql` - Ajout du champ type_employe
2. `backend/services/permission.service.js` - Simplification
3. `backend/services/employee.service.js` - Gestion des types
4. `backend/middleware/permission.js` - Simplification
5. `backend/controllers/employee.controller.js` - Types d'employés
6. `backend/routes/employee.routes.js` - Nouvelles routes
7. Toutes les routes utilisant les permissions complexes

### Nouveaux Fichiers à Créer
1. `backend/services/employeeType.service.js`
2. `backend/controllers/employeeType.controller.js`
3. `backend/middleware/employeeType.middleware.js`
4. `backend/routes/employeeType.routes.js`
5. `backend/migrations/simplify-permissions.sql`

## 🔄 Ordre d'Exécution

1. **Phase 1** : Modification du schéma BDD
2. **Phase 2** : Création du nouveau système de permissions
3. **Phase 3** : Refactoring des services
4. **Phase 4** : Refactoring des middlewares
5. **Phase 5** : Refactoring des contrôleurs
6. **Phase 6** : Refactoring des routes
7. **Phase 7** : Migration des données existantes

## ⚠️ Points d'Attention

1. **Rétrocompatibilité** : Maintenir l'ancien système pendant la transition
2. **Tests** : Tester chaque phase avant de passer à la suivante
3. **Documentation** : Documenter les nouveaux types et permissions
4. **Formation** : Préparer la documentation pour les utilisateurs

## 🎯 Résultat Attendu

- **Système simplifié** avec seulement 8 permissions au lieu de 50+
- **Séparation claire** : Admins (config/rapports) vs Employés (opérations)
- **Types d'employés clairs** et faciles à comprendre
- **Flexibilité maximale** : un employé peut travailler sur plusieurs services
- **Sécurité renforcée** : employés ne peuvent pas accéder aux données sensibles
- **Maintenance facilitée** du système de permissions
- **Performance améliorée** avec moins de vérifications complexes
- **Code plus lisible** et maintenable

## 🔄 Avantages de la Flexibilité Multi-Services

### Cas d'Usage Réels
1. **Serveuse polyvalente** : Peut travailler au bar le matin et au restaurant le soir
2. **Gérant multi-services** : Supervise bar + restaurant selon les besoins
3. **Employé saisonnier** : Peut être affecté à différents services selon l'affluence
4. **Remplacement** : Un employé peut temporairement couvrir un autre service

### Implémentation Technique
- **Champ `type_employe`** : Définit le niveau de responsabilité (serveuse, gérant, etc.)
- **Champ `services_autorises`** : Array JSON des services accessibles
- **Validation dynamique** : Vérification en temps réel des accès selon le service demandé
- **Interface flexible** : L'employé voit seulement les services auxquels il a accès
